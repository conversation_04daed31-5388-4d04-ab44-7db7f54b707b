using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Jobid.App.AdminConsole.Enums;

namespace Jobid.App.AdminConsole.Models.Calls
{
    public class CallRecord
    {
        [Key]
        public Guid Id { get; set; }
        public Guid PhoneNumberId { get; set; }

        [Required]
        public string FromNumber { get; set; }

        [Required]
        public string ToNumber { get; set; }
        public CallDirection Direction { get; set; }
        public CallStatus Status { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Duration { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Cost { get; set; }        
        public string RecordingUrl { get; set; }
        public string TwilioCallSid { get; set; }
        public string AnsweredBy { get; set; }
        public DateTime? AnsweredAt { get; set; }

        // Navigation property to PhoneNumber
        public virtual Phone.PhoneNumber PhoneNumber { get; set; }

        public CallRecord()
        {
            Id = Guid.NewGuid();
            StartTime = DateTime.UtcNow;
            Status = CallStatus.Initiated; // Default status
        }
    }
}
