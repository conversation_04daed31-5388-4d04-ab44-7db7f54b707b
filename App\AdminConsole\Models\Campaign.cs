using Jobid.App.AdminConsole.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    /// <summary>
    /// Campaign model for marketing campaigns
    /// </summary>
    public class Campaign
    {
        public Campaign()
        {
            Id = Guid.NewGuid();
            CreatedBy = string.Empty;
            UpdatedBy = string.Empty;
        }

        [Key]
        public Guid Id { get; set; }

        /// <summary>
        /// Reference to the contact group
        /// </summary>
        [Required]
        public Guid GroupId { get; set; }

        /// <summary>
        /// Phone number for the campaign
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Expected outcome of the campaign
        /// </summary>
        [Required]
        public CampaignExpectedOutcome ExpectedOutcome { get; set; }

        /// <summary>
        /// Status of the campaign
        /// </summary>
        public CampaignStatus Status { get; set; } = CampaignStatus.Active;

        /// <summary>
        /// External meeting ID associated with this campaign
        /// </summary>
        public Guid? ExternalMeetingId { get; set; }

        /// <summary>
        /// User who owns this campaign
        /// </summary>
        [Required]
        public string UserId { get; set; }

        /// <summary>
        /// Navigation property to the group
        /// </summary>
        [ForeignKey("GroupId")]
        public virtual Group Group { get; set; }

        /// <summary>
        /// Soft delete flag
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        #region Audit Properties

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When this record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created this record
        /// </summary>
        [StringLength(450)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// User who last updated this record
        /// </summary>
        [StringLength(450)]
        public string UpdatedBy { get; set; }

        #endregion
    }
}
