﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class wiki_tables_updates : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public wiki_tables_updates(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BatchId",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WikiFileType",
                schema: _Schema,
                table: "WikiFiles",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BatchId",
                schema: _Schema,
                table: "WikiFiles");

            migrationBuilder.DropColumn(
                name: "WikiFileType",
                schema: _Schema,
                table: "WikiFiles");
        }
    }
}
