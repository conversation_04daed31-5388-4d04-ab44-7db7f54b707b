﻿using Jobid.App.JobProject.Enums;
using System;
using static Jobid.App.JobProject.Enums.Enums;
using System.Collections.Generic;

namespace Jobid.App.ActivityLog.Model
{
    public class ShareActivityParams
    {
        public Guid Id { get; set; } = new Guid();
        public string ShareId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string MemberId { get; set; }
        public string EventCategory { get; set; }
        public bool AnyOneCanView { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
