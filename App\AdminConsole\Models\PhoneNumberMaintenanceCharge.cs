using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Jobid.App.AdminConsole.Enums;

namespace Jobid.App.AdminConsole.Models
{
    public class PhoneNumberMaintenanceCharge
    {
        [Key]
        public Guid Id { get; set; }

        [ForeignKey("Tenant")]
        public Guid TenantId { get; set; }

        public int TotalPhoneNumbers { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal ChargeAmount { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PerNumberFee { get; set; }

        public MaintenanceChargeStatus Status { get; set; }

        public DateTime ChargeDate { get; set; }

        public DateTime? PaidDate { get; set; }

        public DateTime? RetryDate { get; set; }

        public int RetryCount { get; set; }

        public string TransactionReference { get; set; }

        public string WalletTransactionId { get; set; }

        public string FailureReason { get; set; }

        public string NotificationSent { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        public virtual Jobid.App.Tenant.Model.Tenant Tenant { get; set; }

        public PhoneNumberMaintenanceCharge()
        {
            Id = Guid.NewGuid();
            Status = MaintenanceChargeStatus.Pending;
            RetryCount = 0;
            CreatedAt = DateTime.UtcNow;
        }
    }
}
