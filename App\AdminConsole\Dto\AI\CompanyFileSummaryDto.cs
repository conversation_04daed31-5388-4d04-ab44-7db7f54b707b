﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;
using System.Collections.Generic;

namespace Jobid.App.AdminConsole.Dto.AI
{
    public class CompanyFileSummaryDto
    {
        [Required]
        [ForeignKey("Tenant")]
        public Guid CompanyId { get; set; }

        [Required]
        public string SummarizedText { get; set; }

        public List<Guid> fileIds { get; set; } = new List<Guid>();
    }
}
