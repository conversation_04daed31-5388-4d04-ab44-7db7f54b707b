﻿using Jobid.App.Helpers.Utils;
using Jobid.App.Subscription.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.Models
{
    public class AppPermissions
    {
        [Key]
        public Guid Id { get; set; } = new Guid();

        [Required]
        public string UserId { get; set; }
        public string Application { get; set; }
        public AIAgents? Agent { get; set; }
        public string SubscriptionStatus { get; set; }
        public bool IsEnabled { get; set; } = true;
        public string TenantId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public string UpdatedBy { get; set; }
        public bool MakeFavorite { get; set; }
        public bool IsSuspended { get; set; }

        public AppPermissions(bool deleteCache)
        {
            if (deleteCache)
            {
                // Clear the reddis cahce for client-admin key
                var redisKey = $"{GlobalVariables.Subdomain}-client-admin";
                var todoCacheKeys = new List<string>();
                if (GlobalVariables.CacheKeys.ContainsKey(redisKey))
                    todoCacheKeys = GlobalVariables.CacheKeys[redisKey];

                // Remove the key from cache
                if (todoCacheKeys.Count > 0)
                {
                    foreach (var key in todoCacheKeys)
                    {
                        var redisRes = GlobalVariables.RedisCacheService.RemoveDataAsync(key).Result;
                    }
                }
            }
        }

        public AppPermissions() { }
    }
}
