﻿using DinkToPdf;
using DinkToPdf.Contracts;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Serilog;
using System;
using System.IO;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services
{
    public class PdfService : IPdfService
    {
        private IConverter _converter;
        private readonly ILogger _logger;

        public PdfService(IConverter converter)
        {
            _converter = converter;
            _logger = Log.ForContext<PdfService>();
        }
        
        public async Task<byte[]> GeneratePdfAsync(string htmlContent)
        {
            _logger.Information("GeneratePdfAsync called to convert HTML content to PDF");
            try
            {
                // First try the direct DinkToPdf approach
                return await Task.Run(() =>
                {
                    var globalSettings = new GlobalSettings
                    {
                        ColorMode = ColorMode.Color,
                        Orientation = Orientation.Portrait,
                        PaperSize = PaperKind.A4,
                        Margins = new MarginSettings { Top = 18, Bottom = 18 },
                    };

                    var objectSettings = new ObjectSettings
                    {
                        PagesCount = true,
                        HtmlContent = htmlContent,
                        WebSettings = { DefaultEncoding = "utf-8" },
                        HeaderSettings = { FontSize = 10, Right = "Page [page] of [toPage]", Line = true },
                        FooterSettings = { FontSize = 8, Center = "Document generated by JobSuite", Line = true },
                    };

                    var htmlToPdfDocument = new HtmlToPdfDocument
                    {
                        GlobalSettings = globalSettings,
                        Objects = { objectSettings },
                    };

                    _logger.Information("Converting HTML to PDF using DinkToPdf");
                    return _converter.Convert(htmlToPdfDocument);
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error using primary PDF generation method, trying fallback strategy");
                
                try
                {
                    // Use our fallback strategy
                    string tempFileName = Path.GetTempFileName() + ".pdf";
                    using (var pdfStream = await PdfGenerationStrategy.ConvertHtmlToPdfWithFallback(htmlContent, tempFileName))
                    {
                        _logger.Information("Successfully generated PDF using fallback strategy");
                        return pdfStream.ToArray();
                    }
                }
                catch (Exception fallbackEx)
                {
                    _logger.Error(fallbackEx, "Fallback PDF generation strategy also failed");
                    throw new Exception("All PDF generation methods failed", fallbackEx);
                }
            }
        }
    }
}
