﻿using Jobid.App.Helpers.Enums;
using Microsoft.AspNetCore.Http;
using static Jobid.App.JobProject.Enums.Enums;
using System.Collections.Generic;
using System;
using System.Text.Json.Serialization;
using Jobid.App.Helpers.Utils.Attributes;

namespace Jobid.App.JobProject.ViewModel
{
    public class AddProjectDto
    {
        public string ProjectId { get; set; }
        public string Name { get; set; }
        public List<string> Members { get; set; }
        public string Summary { get; set; }

        [DateValidator]
        public DateTime StartDate { get; set; }

        [DateValidator]
        public DateTime EndDate { get; set; }
        public bool IsBillable { get; set; }
        public decimal? AmountPerSelectedFrequency { get; set; }
        public decimal? ExpectedProjectValue { get; set; }
        public Currency? CurrencySymbol { get; set; }
        public AmountFrequency? AmountFrequency { get; set; }
        public List<string> ExternalMembersEmails { get; set; }
        public List<string> Clients { get; set; }
        public ProjectStatus ProjectStatus { get; set; } = ProjectStatus.Active;

        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
