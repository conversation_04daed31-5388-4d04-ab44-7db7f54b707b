﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;


namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TriggerVm
    {
        public string TriggerName { get; set; }
        public TriggerReason TriggerReason { get; set; } = TriggerReason.Personal;
        public List<string> Reasons { get; set; }
        public List<DateTime> TriggerDateAndTime { get; set; }
        public TriggerAction Action { get; set; } = TriggerAction.Email;
        public List<string> ParticipantsIds { get; set; }
        public bool Notification { get; set; } = false;
        public bool SMS { get; set; } = false;
        public bool Email { get; set; } = false;
        public string ProjectId { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }
    }
}
