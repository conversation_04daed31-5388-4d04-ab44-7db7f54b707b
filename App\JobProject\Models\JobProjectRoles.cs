﻿using Jobid.App.JobProjectManagement.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.JobProject.Models
{
    public class JobProjectRoles
    {
        [Key]
        public string Id { get; set; }
        public string Name { get; set; }
        [NotMapped]
        public ICollection<JobProjectPermission> Permissions { get; set; } = new Collection<JobProjectPermission>();
    }
}
