using Npgsql;
using System;

namespace Jobid.App.Helpers.Extensions
{
    /// <summary>
    /// Extensions for PostgreSQL connection configuration to help with serialization issues
    /// </summary>
    public static class PostgreSqlConnectionExtensions
    {
        /// <summary>
        /// Configures a PostgreSQL connection string with optimized settings to reduce serialization conflicts
        /// </summary>
        /// <param name="baseConnectionString">The base connection string</param>
        /// <returns>Optimized connection string</returns>
        public static string WithOptimizedSettings(this string baseConnectionString)
        {
            var builder = new NpgsqlConnectionStringBuilder(baseConnectionString);
            
            // Configure connection settings to help reduce serialization conflicts
            
            // Set a reasonable command timeout (default is 30 seconds)
            builder.CommandTimeout = 60;
            
            // Enable connection pooling optimizations
            builder.Pooling = true;
            builder.MinPoolSize = 1;
            builder.MaxPoolSize = 20;
            
            // Set connection lifetime to help refresh connections
            builder.ConnectionLifetime = 300; // 5 minutes
              // Enable keep alive to detect broken connections faster
            builder.KeepAlive = 30;
            
            // Set timeout for faster failure detection
            builder.Timeout = 60; // Connection timeout in seconds
            
            // Configure application name for easier monitoring
            if (string.IsNullOrEmpty(builder.ApplicationName))
            {
                builder.ApplicationName = "Joble-Backend";
            }
            
            return builder.ConnectionString;
        }

        /// <summary>
        /// Configures a PostgreSQL connection string specifically for Hangfire with settings to reduce lock contention
        /// </summary>
        /// <param name="baseConnectionString">The base connection string</param>
        /// <returns>Hangfire-optimized connection string</returns>
        public static string WithHangfireOptimizedSettings(this string baseConnectionString)
        {
            var builder = new NpgsqlConnectionStringBuilder(baseConnectionString);
            
            // Hangfire-specific optimizations
            
            // Longer command timeout for background jobs
            builder.CommandTimeout = 120;
            
            // Smaller pool size for Hangfire to reduce contention
            builder.Pooling = true;
            builder.MinPoolSize = 1;
            builder.MaxPoolSize = 10;
              // Shorter connection lifetime for Hangfire
            builder.ConnectionLifetime = 180; // 3 minutes
            
            // Set timeout for faster failure detection  
            builder.Timeout = 60; // Connection timeout in seconds
            builder.KeepAlive = 30; // Keep alive interval
            
            // Application name for monitoring
            builder.ApplicationName = "Joble-Hangfire";
            
            return builder.ConnectionString;
        }

        /// <summary>
        /// Configures a PostgreSQL connection string specifically for WatchDog with settings to reduce lock contention
        /// </summary>
        /// <param name="baseConnectionString">The base connection string</param>
        /// <returns>WatchDog-optimized connection string</returns>
        public static string WithWatchDogOptimizedSettings(this string baseConnectionString)
        {
            var builder = new NpgsqlConnectionStringBuilder(baseConnectionString);
            
            // WatchDog-specific optimizations
            
            // Moderate command timeout for logging operations
            builder.CommandTimeout = 90;
            
            // Moderate pool size for WatchDog logging operations
            builder.Pooling = true;
            builder.MinPoolSize = 1;
            builder.MaxPoolSize = 15;
            
            // Connection lifetime for WatchDog
            builder.ConnectionLifetime = 240; // 4 minutes
            
            // Set timeout for faster failure detection  
            builder.Timeout = 60; // Connection timeout in seconds
            builder.KeepAlive = 30; // Keep alive interval
            
            // Application name for monitoring
            builder.ApplicationName = "Joble-WatchDog";
            
            return builder.ConnectionString;
        }
    }
}
