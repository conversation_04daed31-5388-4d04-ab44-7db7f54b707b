﻿using Jobid.App.Helpers.Utils._Helper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Extensions
{
    /// <summary>
    /// Authentication Service Extension
    /// </summary>
    public static class AuthenticationServiceExtension
    {
        public static void AddAuthenticationService(this IServiceCollection services, IConfiguration configuration)
        {
            JwtSecurityTokenHandler.DefaultInboundClaimTypeMap.Clear();
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(cfg =>
            {
                cfg.RequireHttpsMetadata = false;
                cfg.SaveToken = true;
                cfg.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidIssuer = JwtokenOptions.Issuer,
                    ValidAudience = JwtokenOptions.Issuer,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JwtokenOptions.Key)),
                    ClockSkew = TimeSpan.Zero, // remove delay of token when expire
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true
                };
                
                // Configure SignalR to use JWT authentication
                cfg.Events = new Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerEvents
                {
                    OnMessageReceived = context =>
                    {
                        var accessToken = context.Request.Query["access_token"];
                        Console.WriteLine($"SignalR JWT Token: {accessToken}");
                        var path = context.HttpContext.Request.Path;
                        
                        // If the request is for SignalR hubs, get the token from the query string
                        if (!string.IsNullOrEmpty(accessToken) && 
                            (path.StartsWithSegments("/callHub") || 
                             path.StartsWithSegments("/notificationHub") || 
                             path.StartsWithSegments("/smart-login")))
                        {
                            context.Token = accessToken;
                        }
                        
                        return Task.CompletedTask;
                    },
                    OnAuthenticationFailed = context =>
                    {
                        // Log authentication failures for debugging
                        if (context.Request.Path.StartsWithSegments("/callHub"))
                        {
                            Console.WriteLine($"SignalR JWT Authentication failed: {context.Exception?.Message}");
                        }
                        return Task.CompletedTask;
                    }
                };
            }).AddJwtBearer("super-admin", options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidIssuer = configuration["Jwt:SuperAdmin:Issuer"],
                    ValidAudience = configuration["Jwt:SuperAdmin:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["Jwt:SuperAdmin:Key"]!)),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ClockSkew = TimeSpan.Zero
                };
            }).AddJwtBearer("service-to-service-auth", options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidIssuer = configuration["Jwt:Grpc:Issuer"],
                    ValidAudience = configuration["Jwt:Grpc:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["Jwt:Grpc:Key"]!)),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ClockSkew = TimeSpan.Zero
                };
            });
            //services.AddAuthorization();
        }
    }
}
