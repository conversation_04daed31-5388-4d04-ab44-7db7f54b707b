﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class CalenderUpload : BaseModel
    {
        public Guid Id { get; set; }

        public string FileName { get; set; }

        [ForeignKey("CalenderMeeting")]
        public Guid? MeetingId { get; set; }

        [ForeignKey("SubsequentMeeting")]
        public Guid? SubsequentMeetingId { get; set; }

        // Navigational Properties
        public CalenderMeeting? CalenderMeeting { get; set; } = null;

        public SubsequentMeeting? SubsequentMeeting { get; set; } = null;

        public CalenderUpload()
        {
            Id = Guid.NewGuid();
        }
    }
}
