﻿using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.ViewModels
{
    public class UpdateEnterprisePalnDto
    {
        public Applications Application { get; set; }

        public PaymentProviders? Provider { get; set; }

        [Required]
        public double AmountPaid { get; set; }

        [Required]
        public int ProjectLimit { get; set; }

        [Required]
        public int InternalCommunicationHistoryLimit { get; set; }

        public int? DataRetentionPeriodInMonth { get; set; }

        public bool? TimeSheetManagement { get; set; }

        [Required]
        public int ActivityLogHistoryLimit { get; set; }

        [Required]
        public int CalenderLimit { get; set; }

        [Required]
        public int StorageLimit { get; set; }

        [Required]
        public int UsersLimit { get; set; }

        public bool? AiAssistants { get; set; }

        [Required]
        public string TenantId { get; set; }

        [Required]
        public string LoggedInUserId { get; set; }

        [Required]
        public string EnterPriseSubId { get; set; }

        public DateTime? ActivatedOn { get; set; }

        public DateTime? ExpiresOn { get; set; }
    }
}
