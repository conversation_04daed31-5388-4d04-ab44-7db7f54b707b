﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class ClientServices : IClientServices
    {
        private JobProDbContext Db;
        public ClientServices(JobProDbContext _db) => Db = _db;


        public async Task<bool> CreateRole(RoleVm roleVm, string userId = "")
        {
            var user = await Db.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();
            if (user == null)
            {
                return false;
            }
            int result;
            var role = new ClientRole()
            {
                RoleName = roleVm.RoleName,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = string.IsNullOrEmpty(userId) ? "" : user.FirstName
            };

            var roleModuleList = new List<ClientRoleRoleModule>();

            Db.ClientRoles.Add(role);
            result = await Db.SaveChangesAsync();

            if (!roleVm.ModuleId.Any())
            {
                return true;
            }

            foreach (var moduleId in roleVm.ModuleId)
            {
                var isRoleModule = Db.ClientRoleRoleModules.Where(x => x.RoleModuleId.ToString() == moduleId && x.ClientRoleId == role.Id);
                if (!Db.RoleModules.Where(x => x.Id.ToString() == moduleId).Any() || isRoleModule.Any())
                {
                    return false;
                }

                var roleModule = new ClientRoleRoleModule()
                {
                    ClientRoleId = new Guid(role.Id.ToString()),
                    RoleModuleId = new Guid(moduleId)

                };

                if (result > 0)
                {
                    Db.ClientRoleRoleModules.Add(roleModule);
                    result = await Db.SaveChangesAsync();
                    Db.Permissions.Add(new Permission() { RoleId = role.Id.ToString(), Create = false, RoleModuleId = new Guid(moduleId), Delete = false, Update = false, View = false });
                    int resultPermission = await Db.SaveChangesAsync();
                }
                else
                {
                    return false;
                }
            }
            return result > 0 ? true : false;
        }


        public async Task<bool> AddModuleToRole(ModuleDtoVm moduleDtoVm, string roleId)
        {
            var role = await Db.ClientRoles.Include(x => x.user).Where(x => x.Id.ToString() == roleId).FirstOrDefaultAsync();
            var module = await Db.RoleModules.Include(x => x.Permission).Where(x => x.Id.ToString() == moduleDtoVm.ModuleId).FirstOrDefaultAsync();

            if (module == null || role == null)
            {
                return false;
            }

            var newClientModule = new ClientRoleRoleModule()
            {
                ClientRole = role,
                RoleModule = module

            };

            Db.ClientRoleRoleModules.Add(newClientModule);
            int result = await Db.SaveChangesAsync();
            if (result > 0)
            {
                Db.Permissions.Add(new Permission() { RoleId = role.Id.ToString(), Create = false, RoleModuleId = module.Id, Delete = false, Update = false, View = false });
                int resultPermission = await Db.SaveChangesAsync();
                if (resultPermission > 0) { return true; } else { return false; }
            }
            else
            {
                return false;

            }
        }

        public async Task<bool> CreateClientRole(ClientRole clientRole)
        {
            Db.ClientRoles.Add(clientRole);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }

        public async Task<bool> CreateRoleModule(RoleModuleVm roleModuleVm, string roleId)
        {
            var role = await Db.ClientRoles.AsNoTracking().SingleOrDefaultAsync(x => x.Id.ToString() == roleId);

            if (role == null)
                return false;


            var newRoleModule = new RoleModule()
            {
                CreatedAt = DateTime.UtcNow,
                ModuleName = roleModuleVm.ModuleName,
                CreatedBy = roleModuleVm.CreatedBy,
                RegionName = roleModuleVm.RegionName
            };

            var result2 = new List<ClientRoleRoleModule>() { new ClientRoleRoleModule() { RoleModule = newRoleModule, ClientRoleId = role.Id } };
            var ud = new ClientRoleRoleModule() { RoleModule = newRoleModule, ClientRoleId = role.Id, ClientRole = role };


            var drt = new ClientRoleRoleModule()
            {
                ClientRole = role
            };

            Db.ClientRoles.Update(role);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }

        public async Task<List<RoleModule>> GetModules()
        {
            return await Db.RoleModules.ToListAsync();
        }

        public async Task<RoleModule> GetModulesById(string moduleId)
        {
            return await Db.RoleModules.Include(x => x.Permission).Where(x => x.Id.ToString() == moduleId).FirstOrDefaultAsync();
        }


        public async Task<bool> CreateModule(ModuleVm moduleVm, string userId = "")
        {
            var modules = Db.RoleModules.Where(x => x.ModuleName.ToLower().Trim() == moduleVm.Name.ToLower().Trim());

            var user = await Db.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();
            if (user == null)
            {
                return false;
            }

            if (modules.Any())
                return false;

            var newModule = new RoleModule()
            {

                ModuleName = moduleVm.Name.Trim(),
                CreatedAt = DateTime.UtcNow,
                CreatedBy = string.IsNullOrEmpty(userId) ? "" : user.FirstName
            };

            Db.RoleModules.Add(newModule);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }

        public async Task<List<RoleDtoVm>> GetRoles()
        {
            var roles = await Db.ClientRoles.Include(x => x.ClientRoleRoleModules).ThenInclude(x => x.RoleModule).ThenInclude(x => x.Permission).ToListAsync();

            var newRoleList = new List<RoleDtoVm>();
            foreach (var role in roles)
            {
                var newRole = new RoleDtoVm()
                {
                    Id = role.Id,
                    RoleName = role.RoleName,
                    RoleModule = role.ClientRoleRoleModules.Select(x => new ModuleRoleDtoVm()
                    {
                        ModuleName = x.RoleModule.ModuleName,
                        Id = x.RoleModule.Id,
                        Permission = x.RoleModule?.Permission?.Where(x => x?.RoleId?.ToLower() == role.Id.ToString().ToLower()).Select(x => new PermissionDtoVm() { Create = x.Create, Delete = x.Delete, Update = x.Update, View = x.View }).FirstOrDefault()

                    }).ToList()

                };

                newRoleList.Add(newRole);
            }
            return newRoleList;
        }

        public async Task<ClientRole> GetRoleById(string roleId)
        {
            var role = await Db.ClientRoles.Where(x => x.Id.ToString() == roleId).Include(x => x.ClientRoleRoleModules).ThenInclude(x => x.RoleModule).ThenInclude(x => x.Permission).FirstOrDefaultAsync();
            return role;
        }

        public async Task<RoleDtoVm> GetRoleDtoById(string roleId)
        {
            var role = await Db.ClientRoles.Where(x => x.Id.ToString() == roleId).Include(x => x.ClientRoleRoleModules).ThenInclude(x => x.RoleModule).ThenInclude(x => x.Permission).FirstOrDefaultAsync();
            return await GetCLientRole(role);
        }

        public async Task<bool> DeleteRoleById(string Id)
        {

            var role = Db.ClientRoles.Include(x => x.user).Where(x => x.Id.ToString() == Id).FirstOrDefault();

            if (role == null)
                return false;

            Db.ClientRoles.Remove(role);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }

        public async Task<bool> DeleteModuleRoleById(string Id)
        {
            var roleModule = Db.RoleModules.Where(x => x.Id.ToString() == Id).FirstOrDefault();

            if (roleModule == null)
                return false;

            Db.RoleModules.Remove(roleModule);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }

        public async Task Test()
        {
            var module = Db.RoleModules.Where(X => X.Id.ToString() == "26614A70-8931-48D8-9CEC-08DAA26D0A3F").FirstOrDefault();
            var role = Db.ClientRoles.Where(x => x.Id.ToString() == "8AC9C02F-E531-40CC-FB60-08DA9FFDE7E8").FirstOrDefault();

            var drt = new ClientRoleRoleModule()
            {
                ClientRoleId = role.Id,
                RoleModuleId = module.Id
            };

            await Db.SaveChangesAsync();
        }

        public async Task<bool> UpdatePermission(UpdatePermissionDtoVm updatePermissionDtoVm, string permissionId)
        {
            var permission = Db.Permissions.Where(x => x.Id.ToString() == permissionId).FirstOrDefault();

            if (permission == null)
            {
                return false;
            }
            permission.Create = updatePermissionDtoVm.Create;
            permission.View = updatePermissionDtoVm.View;
            permission.Update = updatePermissionDtoVm.Update;
            permission.Delete = updatePermissionDtoVm.Delete;

            Db.Permissions.Update(permission);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }


        public async Task<bool> UpdateModule(ModuleVm moduleVm, string moduleId)
        {
            var module = await Db.RoleModules.Include(x => x.Permission).Where(x => x.Id.ToString() == moduleId).FirstOrDefaultAsync();

            if (module == null)
                return false;


            module.ModuleName = moduleVm.Name;
            Db.RoleModules.Update(module);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }


        public async Task<bool> UpdateUserRole(string roleId, string userId)
        {

            var appUser = await Db.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();
            var role = await Db.ClientRoles.Where(x => x.Id.ToString() == roleId).FirstOrDefaultAsync();

            if (appUser == null || role == null)
            {
                return false;
            }

            appUser.ClientRole = role;
            Db.Users.Update(appUser);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }


        public async Task<RoleDtoVm> GetCLientRole(ClientRole clientRole)
        {
            if (clientRole == null)
            {
                return null;
            }

            var role2 = new RoleDtoVm()
            {

                Id = clientRole.Id,
                CreatedBy = clientRole.CreatedBy,
                RoleName = clientRole.RoleName,
                CreatedAt = clientRole.CreatedAt

            };

            var roleModules = new List<ModuleRoleDtoVm>();

            foreach (var module in clientRole.ClientRoleRoleModules)
            {
                var permission = module.RoleModule.Permission.Where(x => x?.RoleId?.ToLower() == clientRole?.Id.ToString().ToLower()).FirstOrDefault();
                var RoleModule = new ModuleRoleDtoVm
                {
                    Id = module.RoleModuleId,
                    ModuleName = module.RoleModule.ModuleName,
                    Permission = permission == null ? null : new PermissionDtoVm()
                    {
                        Id = permission.Id,
                        Create = permission.Create,
                        Update = permission.Update,
                        View = permission.View,
                        Delete = permission.Delete
                    }
                };
                roleModules.Add(RoleModule);
            }
            role2.RoleModule = roleModules;
            return role2;
        }

        public async Task<bool> CreatePermission(PermissionVm permissionVm, string moduleId)
        {
            var module = Db.RoleModules.Where(x => x.Id.ToString() == moduleId).Any();
            var role = Db.ClientRoles.Where(x => x.Id.ToString() == permissionVm.RoleId).Any();

            if (!module || !role)
            {
                return false;
            }

            var permission = Db.Permissions.Where(x => x.RoleModuleId.ToString() == moduleId && x.RoleId == permissionVm.RoleId).FirstOrDefault();

            if (permission != null)
            {
                return false;
            }


            permission.Create = permissionVm.Create;
            permission.View = permission.View;
            permission.Update = permission.Update;
            permission.Delete = permission.Delete;
            permission.RoleId = permission.RoleId;
            permission.RoleModuleId = new Guid(moduleId);

            Db.Permissions.Add(permission);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }

        }

        public async Task<Permission> GetPermission(string permissionId)
        {
            return await Db.Permissions.Where(x => x.Id.ToString() == permissionId).FirstOrDefaultAsync();
        }
    }
}
