﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class ProjectMgmt_ProjectUpdateVM
    {
        [Required]
        public string ProjectName { get; set; }

        [JsonIgnore]
        public string Duration { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public ProjectStatus ProjectStatus { get; set; }
        public bool IsBillable { get; set; }
        public decimal? AmountPerSelectedFrequency { get; set; }
        public decimal? ExpectedProjectValue { get; set; }
        public Currency? CurrencySymbol { get; set; }
        public AmountFrequency? AmountFrequency { get; set; }

    }
}
