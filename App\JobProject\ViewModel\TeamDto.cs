﻿using Microsoft.Graph.Models;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TeamDto
    {
        [Required]
        public string Name { get; set; }
        public List<string> TeamMemberIds { get; set; }
        [Required]
        public string UserId { get; set; }
        [JsonIgnore]
        public string Subdomain { get; set; }
    }
}
