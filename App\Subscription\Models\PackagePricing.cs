﻿using Jobid.App.Subscription.Enums;
using System;

namespace Jobid.App.Subscription.Models
{
    public class PackagePricing
    {
        public Guid Id { get; set; }
        public Guid PricingPlanId { get; set; }
        public string Application { get; set; }
        public double? PricePerMonth { get; set; }
        public double? PricePerMonthForYearlyOption { get; set; }
        public SubscriptionRegions? Region { get; set; }
        public string Currency { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public PricingPlan PricingPlan { get; set; }
    }
}
