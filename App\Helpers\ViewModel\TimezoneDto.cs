﻿using Newtonsoft.Json;

namespace Jobid.App.Helpers.ViewModel
{
    public class TimezoneDto
    {
        [JsonProperty("text")]
        public string Name { get; set; }
        [JsonProperty("value")]
        public string Value { get; set; }
        [JsonProperty("abbr")]
        public string Abbr { get; set; }
        [JsonProperty("isdst")]
        public string IsDst { get; set; }
        [JsonProperty("offset")]
        public decimal Offset { get; set; }
        [JsonProperty("gmt")]
        public string[] Gmt { get; set; }
    }
}
