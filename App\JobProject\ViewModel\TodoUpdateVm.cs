﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Xunit.Sdk;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TodoUpdateVm
    {
        public string TodoDescription { get; set; }

        [Required(ErrorMessage = "Todo summary cannot be null")]
        public string TodoSummary { get; set; }

        [Required(ErrorMessage = "Invalid Status")]
        public ProjectManagementStatus Status { get; set; }

        [Required(ErrorMessage = "Invalid Priority")]
        public ProjectManagementPriority Priority { get; set; }

        [ValidEmailChecks]
        public string ExternalTeamMember { get; set; } = string.Empty;
        public string MemberId { get; set; } = string.Empty;
        public string Comment { get; set; }
        public string ExistingTodoLink { get; set; }
        public List<string> TagIds { get; set; } = new List<string>();
        public List<IFormFile> UploadFile { get; set; } = new List<IFormFile>();
        public DateTime StartDateAndTime { get; set; }
        public DateTime EndTime { get; set; }
        public DateTime? DueDate { get; set; }
        public Guid UserId { get; set; }
        public string TimeEstimate { get; set; }
        public string ProjectId { get; set; } = string.Empty;
        public string SprintId { get; set; } = string.Empty;
        public bool IsMeasurable { get; set; }
        public TodoCustomFrequencyDto? TodoCustomFrequency { get; set; }
        public bool UpdatedByAdmin { get; set; } = false;

        [JsonIgnore]
        public string SubDomain { get; set; }
        [JsonIgnore]
        public string TenantId { get; set; }
        [JsonIgnore]
        public string LoggedInUserId { get; set; }
    }
}
