using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Newtonsoft.Json;

namespace Jobid.App.Tenant.SchemaTenant
{
    public static class Extensions
    {
        public static IApplicationBuilder CustomEnginerInterceptor(this IApplicationBuilder builder)
        {
            builder.Use(CheckIfSubdomainExists);
            builder.Use(CheckIfUserBelongsToTheTenat);
            return builder;
        }

        #region Private Methods
        private static async Task CheckIfSubdomainExists(HttpContext context, Func<Task> next)
        {
            // Get the url from context
            var isWeebHookCall = false;
            var url = context.Request.Path.Value;
            if (!string.IsNullOrEmpty(url) && url.Contains("webhook"))
                isWeebHookCall = true;

            TenantSchema tenantSchema = new TenantSchema();
            //await tenantSchema.RunMigrations(Constants.PUBLIC_SCHEMA);
            var origin = tenantSchema.ExtractSubdomainFromRequest(context);
            origin = string.IsNullOrEmpty(origin) ? "api" : origin;
            GlobalVariables.Subdomain = origin;

            // Get the timezone from the header
            GlobalVariables.TimeZone = context.Request.Headers["timezone"];

            tenantSchema._schema = origin;
            var schemaExists = await tenantSchema.DoesCurrentSubdomainExist();

            if (!schemaExists && origin != "api" && !isWeebHookCall)
            {
                context.Response.StatusCode = 404;
                context.Response.ContentType = "application/json";
                ApiResponse<dynamic> apiResponse = new ApiResponse<dynamic>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Subdomain not found",
                    user = null,
                };
                await context.Response.WriteAsync(JsonConvert.SerializeObject(apiResponse));
                return;
            }

            // check/generate schema if not exist on every request
            await next.Invoke();
        }

        private static async Task CheckIfUserBelongsToTheTenat(HttpContext context, Func<Task> next)
        {
            // Check user claims from token
            var claims = context.User.Claims.ToList();
            var userId = claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
            var belongsTo = false;

            TenantSchema tenantSchema = new TenantSchema();
            var subdomain = tenantSchema.ExtractSubdomainFromRequest(context);
            subdomain = string.IsNullOrEmpty(subdomain) ? "api" : subdomain;
            var publicContext = new JobProDbContext(new DbContextSchema());

            if (subdomain != "api" && !string.IsNullOrEmpty(subdomain) && !string.IsNullOrEmpty(userId))
            {
                // Get all the companies the user belongs to
                var userCompanies = await publicContext.UserCompanies.Include(x => x.tenant).Where(c => c.UserId == userId)
                    .Select(u => new { subdomain = u.tenant.Subdomain }).ToListAsync();
                if (userCompanies.Any(c => c.subdomain == subdomain)) belongsTo = true;

                if (!belongsTo)
                {
                    context.Response.StatusCode = 404;
                    context.Response.ContentType = "application/json";
                    ApiResponse<dynamic> apiResponse = new ApiResponse<dynamic>
                    {
                        Data = null,
                        ResponseCode = "401",
                        ResponseMessage = $"User does not belong to the company - {subdomain}",
                        user = null,
                    };
                    await context.Response.WriteAsync(JsonConvert.SerializeObject(apiResponse));
                    return;
                }
            }

            await next.Invoke();
        }
        #endregion

        public static object ExecuteScalar(this JobProDbContext context, string sql,
        List<DbParameter> parameters = null,
        CommandType commandType = CommandType.Text,
        int? commandTimeOutInSeconds = null)
        {
            Object value = ExecuteScalar(context.Database, sql, parameters,
                                         commandType, commandTimeOutInSeconds);
            return value;
        }

        public static object ExecuteScalar(this DatabaseFacade database,
        string sql, List<DbParameter> parameters = null,
        CommandType commandType = CommandType.Text,
        int? commandTimeOutInSeconds = null)
        {
            Object value;
            using (var cmd = database.GetDbConnection().CreateCommand())
            {
                if (cmd.Connection.State != ConnectionState.Open)
                {
                    cmd.Connection.Open();
                }
                cmd.CommandText = sql;
                cmd.CommandType = commandType;
                if (commandTimeOutInSeconds != null)
                {
                    cmd.CommandTimeout = (int)commandTimeOutInSeconds;
                }
                if (parameters != null)
                {
                    cmd.Parameters.AddRange(parameters.ToArray());
                }
                value = cmd.ExecuteScalar();
            }
            return value;
        }
    }
}