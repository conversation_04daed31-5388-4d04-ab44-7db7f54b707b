﻿﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Controllers
{
    [Route("api/admin/[controller]")]
    [ApiController]
    [CustomAuthorize]
    public class EndpointAnalyticsController : ControllerBase
    {
        private readonly IUnitofwork _unitOfWork;
        private readonly ILogger _logger;

        public EndpointAnalyticsController(IUnitofwork unitOfWork)
        {
            _unitOfWork = unitOfWork;
            _logger = Log.ForContext<EndpointAnalyticsController>();
        }

        /// <summary>
        /// Gets endpoint call counts grouped by section for a specific application and date range
        /// </summary>
        /// <param name="application">The application</param>
        /// <param name="startDate">Start date (optional, defaults to 30 days ago)</param>
        /// <param name="endDate">End date (optional, defaults to today)</param>
        /// <returns>Dictionary with section as key and count as value</returns>
        [HttpGet("section-usage")]
        public async Task<IActionResult> GetSectionUsage(
            [FromQuery] Applications application,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var start = startDate ?? DateTime.UtcNow.AddDays(-30);
            var end = endDate ?? DateTime.UtcNow;

            var result = await _unitOfWork.EndpointTrackerService.GetEndpointCallsBySectionAsync(
                application,
                start,
                end);

            return Ok(new
            {
                Success = true,
                Data = result,
                StartDate = start,
                EndDate = end
            });
        }

        /// <summary>
        /// Gets detailed endpoint call data for a specific application and time range
        /// </summary>
        /// <param name="application">The application</param>
        /// <param name="timeRange">The time range filter (optional, defaults to ThisMonth)</param>
        /// <returns>Endpoint call tracking response with section and monthly data</returns>
        [HttpGet("detailed-usage")]
        public async Task<IActionResult> GetDetailedUsage(
            [FromQuery] Applications application,
            [FromQuery] TimeRangeFilter timeRange = TimeRangeFilter.ThisMonth)
        {
            var result = await _unitOfWork.EndpointTrackerService.GetEndpointCallsAsync(
                      application,
                      timeRange);

            return StatusCode(int.Parse(result.ResponseCode), result);
        }
    }
}
