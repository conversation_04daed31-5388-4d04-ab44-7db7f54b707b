#region Using Statements
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Amazon.Runtime;
using Amazon.S3;
using Jobid.App.ActivityLog.contract;
using Jobid.App.ActivityLog.Repository;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Services;
using Microsoft.AspNetCore.Http;
using DinkToPdf.Contracts;
using DinkToPdf;
using Jobid.App.Helpers.Utils;
using Jobid.App.SchemaTenant;
using Jobid.App.Tenant.Repository;
using Jobid.App.Tenant;
using System.Text.Json.Serialization;
using Jobid.App.RabbitMQ;
using Jobid.App.ActivityLog.BackGroundJobs;
using Newtonsoft.Json;
using Jobid.App.Tenant.Contract;
using Jobid.App.Calender.Contracts;
using Calendar.Service.RabbitMQ.Consumers;
using Jobid.App.Calender.Services;
using Jobid.App.AdminConsole.Services;
using Jobid.App.RabbitMQ.Consumers;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProject.Services.Implemetations;
using Jobid.App.Helpers.Services.Implementations;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Calender.Services.Implementations;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.Wiki.Services.Implementations;
using Microsoft.AspNetCore.Http.Features;
using Jobid.App.Notification.Contracts;
using Jobid.App.Notification.Repository;
using System;
#endregion

namespace Jobid.App.Helpers.Extensions
{
    /// <summary>
    /// Service extensions
    /// </summary>
    public static class ServiceExtensions
    {
        /// <summary>
        /// Custom services
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddCustomServices(this IServiceCollection services, IConfiguration configuration)
        {
            var awsOption = configuration.GetAWSOptions();
            awsOption.Credentials = new BasicAWSCredentials(
                configuration["AWSAccessKeyId"],
                configuration["AWSSecretAccessKey"]
             );

            // Configure IFormFile size limit
            services.Configure<FormOptions>(options =>
            {
                options.MultipartBodyLengthLimit = 524288000; // 500 MB
            });

            // Add stripe configuration values
            services.AddStripeService(configuration);

            //services.AddScoped<BrowserDetection>();
            services.AddScoped<IPdfGeneratorServices, PdfGeneratorServices>();
            services.AddDefaultAWSOptions(awsOption);
            services.AddAWSService<IAmazonS3>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IActivityService, ActivityService>();
            services.AddScoped<IActivityBackgroundService, ActivityBackgroundService>();
            services.AddScoped<IAdminService, AdminService>();
            services.AddScoped<IAWSS3Sevices, AWSS3Sevices>();
            services.AddScoped<IEndpointTrackerService, EndpointTrackerService>();
            services.AddRouting(options => options.LowercaseUrls = true);
            services.AddScoped<IUnitofwork, Unitofwork>();
            services.AddScoped<IBackGroundServices, BackGroundServices>();
            services.AddScoped<IBackGroundService, BackGroundService>();
            services.AddScoped<IPublisherService, PublisherService>();
            services.AddScoped<IRabbitMQConnectionService, RabbitMQConnectionService>();
            services.AddScoped<IApiCallService, ApiCallService>();
            services.AddScoped<IUserServices,  UserServices>();
            services.AddScoped<ICalenderService,  CalenderService>();
            services.AddScoped<IUserCompaniesServices, UserCompaniesService>();
            services.AddScoped<IOrganogramService, OrganogramService>();
            services.AddScoped<IBackgroundJobService, BackgroundJobService>();
            services.AddScoped<IMeetingSkillsService, MeetingSkillsService>();
            services.AddScoped<IWalletService, WalletService>();
            services.AddScoped<IPaymentService, PaymentService>();
            services.AddScoped<IPhoneNumberService, PhoneNumberService>();
            services.AddScoped<INotificationsService, NotificationsService>();
            services.AddScoped<ITwilioService, TwilioService>();

            // Add ApplicationShutdownService as a hosted service
            services.AddHostedService<ApplicationShutdownService>();
            services.AddHostedService<CreateMeetingConsumer>();
            services.AddHostedService<UpdateMeetingConsumer>();
            services.AddHostedService<CreateTodoConsumer>();

            services.AddScoped<IProductUpdateService, ProductUpdateService>();
            services.AddScoped<IProductUpdateReceiverService, ProductUpdateReceiverService>();
            services.AddScoped<IActivityViewBackgroundService, ActivityViewBackgroundService>();
            services.AddScoped<IBackgroungService, BackgroundService>();
            services.AddScoped<ITodoServices, TodoServices>();
            services.AddScoped<ITenantService, TenantService>();
            services.AddScoped<ICompanyUserInvite, CompanyUserInviteService>();
            services.AddScoped<IWikiAccessService, WikiAccessService>();
            services.AddScoped<IWikiFileService, WikiFileService>();
            services.AddScoped<IWikiFileBackgroundService, WikiFileBackgroundService>();

            // Add Telephony Services (using Twilio only)
            services.AddScoped<IPhoneNumberService, PhoneNumberService>();

            // Add Twilio Conference Service for unified audio conferencing
            services.AddScoped<ITwilioConferenceService, TwilioConferenceService>();

            // Add Telephony Service for unified call management
            services.AddScoped<ITelephonyService, TelephonyService>();

            // Add Phone Number Maintenance Service
            services.AddScoped<IPhoneNumberMaintenanceService, PhoneNumberMaintenanceService>();

            // Register mollie services
            services.AddMollieApi(configuration);

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddHttpClient();
            services.AddGrpc();
            services.AddAuthentication();

            // Add custom cors policy
            services.AddCustomCors();

            // Add automapper
            services.AddAutoMapper(typeof(Utils.AutoMapper));
            //services.AddAutoMapper(Assembly.GetExecutingAssembly());

            services.AddAuthenticationService(configuration);
            services.AddCustomSwagger();

            // Register DinkToPdf converter
            // The native library is loaded at application startup in Program.cs
            services.AddSingleton(typeof(IConverter), new SynchronizedConverter(new DinkToPdf.PdfTools()));
            services.AddScoped<IPdfService, PdfService>();

            services.AddSignalR();
            services.AddScoped<ITenantConfig<JobProDbContext>, TenantConfig>();
            services.AddSingleton<IConfiguration>(configuration);
            services.AddTransient<TenantService>();
            services.AddScoped<ITenantSchema, TenantSchema>();
            services.AddSingleton<IRedisCacheService, RedisCacheService>();
            services.AddScoped<ILogService, LogService>();
        }
    }
}
