﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TimeSheetFilters
    {
        public List<ProjectStatus> ProjectStatus { get; set; } = null;
        public List<string> TeamIds { get; set; } = new List<string>();
        public List<string> TeamMemberIds { get; set; } = new List<string>();
        public bool? IsBillable { get; set; }
        public List<string> SprintIds { get; set; } = new List<string>();
        public bool GetForAllProjects { get; set; } = false;

        [JsonIgnore]
        public string UserId { get; set; }
    }
}
