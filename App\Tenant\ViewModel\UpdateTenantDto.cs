﻿using System.ComponentModel.DataAnnotations.Schema;
using System;
using Microsoft.AspNetCore.Http;

namespace Jobid.App.Tenant.ViewModel
{
    public class UpdateTenantDto
    {
        public Guid Id { get; set; }
        public string CompanyName { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string VerifiedEmailDomain { get; set; }
        public DateTime DateCreated { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime LastUpdate { get; set; }
        public string LogoUrl { get; set; }
        public int CompanySize { get; set; }
        public string Status { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string CompanyType { get; set; }
        public string RegNumber { get; set; }
        public IFormFile? logo { get; set; }
    }
}
