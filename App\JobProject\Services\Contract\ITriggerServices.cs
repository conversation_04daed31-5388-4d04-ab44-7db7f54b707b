﻿using Jobid.App.Helpers.Models;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ITriggerServices
    {
        Task<List<ProjectTriggerDto>> GetTriggersById(string projectId);
        Task<ProjectTrigger> AddTrigger(TriggerVm triggerVm, ProjectMgmt_Project project = null);
        Task<ProjectTriggerDto> GetTriggerById(Guid id);
        Task<List<ProjectTrigger>> GetUpcomingTriggerById(Guid id);
        Task<List<ProjectTriggerDto>> GetTriggersByUserId(string userId);
        Task<bool> DeleteTrigger(Guid id);
    }
}
