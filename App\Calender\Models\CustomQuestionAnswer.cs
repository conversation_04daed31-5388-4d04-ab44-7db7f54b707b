﻿using Microsoft.Graph.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class CustomQuestionAnswer
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public string Question { get; set; }
        public string Answer { get; set; }
        public string Email { get; set; }
        public Guid BookedExternalMeetingId { get; set; }
        public Guid  CustomQuestionId { get; set; }

        public BookedExternalMeeting BookedExternalMeeting { get; set; }
    }
}
