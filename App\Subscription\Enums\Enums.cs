﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Subscription.Enums
{
    public class Enums
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public enum DurationTypes
        {
            [Display(Name = "Day")]
            Day = 1,

            [Display(Name = "Week")]
            Week = 2,

            [Display(Name = "Month")]
            Month = 3,

            [Display(Name = "Year")]
            Year = 4,

            [Display(Name = "Hour")]
            Hour = 5,

            [Display(Name = "Number")]
            Number = 6,

            [Display(Name = "Minutes")]
            Minutes = 6,
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum CategoriesForFeatures
        {

            [Display(Name = "AI")]
            AI,

            [Display(Name = "Team")]
            Team,

            [Display(Name = "Project")]
            Project,

            [Display(Name = "Chat")]
            Chat,

            [Display(Name = "Timesheet")]
            TimeSheet,

            [Display(Name = "Activity Log")]
            ActivityLog,

            [Display(Name = "Calendar")]
            Calender,

            [Display(Name = "Storage")]
            Storage,

            [Display(Name = "User")]
            User
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum PricingPlans
        {
            //[Display(Name = "Free")]
            //Free,
            [Display(Name = "Starter")]
            Starter,
            [Display(Name = "Growth")]
            Growth,
            [Display(Name = "Business Pro")]
            BusinessPro,
            [Display(Name = "Enterprise")]
            Enterprise
        }


        [JsonConverter(typeof(StringEnumConverter))]
        public enum CardType
        {
            Visa,
            Verve,
            MasterCard
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum SubscriptionStatusForRMQ
        {
            New = 1,
            Active,
            Inactive,
            Cancelled
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum ReasonForSubscriptionStatus
        {
            PaymentFialed = 1,
            Upgrade,
            Downgrade,
            Renewal,
            Activation,
            Deactivation,
            CancelledByUser,
            CancelledByAdmin,
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum SubscriptionInterval
        {
            Monthly,
            Yearly
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum PaymentProviders
        {
            Mollie = 1,
            Stripe,
            Others
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum PaymentMethods
        {
            Card,
            PayPal,
            DirectDebit,
            Ideal
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum SubscriptionStatus
        {
            Active = 1,
            Inactive,
            Pending,
            Cancelled
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum PaymentStatus
        {
            Successful,
            Pending,
            Cancelled,
            Failed
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum EchoFeatures
        {
            [Display(Name = "Contact and Lead Management")]
            ContactAndLeadManagement,

            [Display(Name = "Basic Sales Automation Tools")]
            BasicSalesAutomationTools,

            [Display(Name = "Standard Reporting Features")]
            StandardReportingFeatures,

            [Display(Name = "Multi-Channel Campaign Management")]
            MultiChannelCampaignManagement,

            [Display(Name = "Enhanced Automation")]
            EnhancedAutomation,

            [Display(Name = "AI-Powered Scheduling")]
            AIPoweredScheduling,

            [Display(Name = "Advanced Analytics")]
            AdvancedAnalytics,

            [Display(Name = "Priority Customer Support")]
            PriorityCustomerSupport,

            [Display(Name = "Integration Capabilities")]
            IntegrationCapabilities,

            [Display(Name = "Access to New Features/Updates")]
            AccessToNewFeaturesUpdates,

            [Display(Name = "Dedicated Account Manager")]
            DedicatedAccountManager,

            [Display(Name = "Custom Integration")]
            CustomIntegration,

            [Display(Name = "Customizable Features and Reports")]
            CustomizableFeaturesAndReports
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum EchoPricingPlans
        {
            [Display(Name = "Starter")]
            Starter,

            [Display(Name = "Growth")]
            Growth,

            [Display(Name = "Enterprise")]
            Enterprise
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum JobIdPricingPlans
        {
            [Display(Name = "On Demand")]
            OnDemand,

            [Display(Name = "Starter")]
            Starter,

            [Display(Name = "Growth")]
            Growth,

            [Display(Name = "Business Growth")]
            BusinessGrowth,

            [Display(Name = "Enterprise")]
            Enterprise
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum JobIdFeatures
        {
            [Display(Name = "Job Posting")]
            JobPosting,

            [Display(Name = "User")]
            User,

            [Display(Name = "Candidate Pool Access")]
            CandidatePoolAccess,

            [Display(Name = "Security Check")]
            SecurityCheck,
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum JobFyPricingPlans
        {
            [Display(Name = "Starter")]
            Starter,

            [Display(Name = "Growth")]
            Growth,

            [Display(Name = "Business Pro")]
            BusinessPro,

            [Display(Name = "Enterprise")]
            Enterprise
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum JobPaysPricingPlans
        {
            [Display(Name = "Business Pro")]
            BusinessPro
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum JobFyFeatures
        {
            [Display(Name = "User")]
            [Description("User")]
            User,

            [Display(Name = "Monthly Search")]
            [Description("Simplified search tools to streamline your process.")]
            MonthlySearch,

            [Display(Name = "KYC Monthly Checks")]
            [Description("Keep your client data compliant and secure.")]
            KYCMonthlyChecks,

            [Display(Name = "Guarantor Monthly Checks")]
            [Description("Confidence in every deal with monthly checks.")]
            GuarantorMonthlyChecks,

            [Display(Name = "Document Signing")]
            [Description("Seamlessly send documents for digital signing.")]
            DocumentSigning,

            [Display(Name = "Filing And Storage")]
            [Description("Store and manage your documents with ease.")]
            FilingAndStorage,

            [Display(Name = "Data Retention (Archive)")]
            [Description("Safeguard your records with secure data archiving.")]
            DataRetention,

            [Display(Name = "Address Verification")]
            [Description("Ensure accuracy with automated address verification.")]
            AddressVerification,

            [Display(Name = "On-Ground Verification")]
            [Description("Real-world verification for added security.")]
            OnGroundVerification,

            [Display(Name = "GDPR Compliance Tools")]
            [Description("Stay compliant with powerful GDPR management tools.")]
            GDPRComplianceTools,

            [Display(Name = "Integrations")]
            [Description("Sync with Google Drive, Dropbox, and more for seamless data flow.")]
            Integrations,

            [Display(Name = "PEP Sanctions Checks")]
            [Description("Screen clients against politically exposed persons (PEP) and sanctions lists.")]
            PEPSanctionsChecks,

            [Display(Name = "Verification Updates")]
            [Description("Get real-time email updates, including risk labels.")]
            VerificationUpdates,

            [Display(Name = "API Session Generation")]
            [Description("Create secure sessions and manage workflows with API integration.")]
            APISessionGeneration,

            [Display(Name = "Ongoing Monitoring")]
            [Description("Continuous monitoring for risk and compliance, ensuring you stay on top of every change.")]
            OngoingMonitoring,

            [Display(Name = "EmbeddedSigning")]
            [Description("Fully integrated document signing for a seamless experience.")]
            EmbeddedSigning,

            [Display(Name = "Centralized Controls")]
            [Description("Streamline administration with customizable, centralized policies.")]
            CentralizedControls,

            [Display(Name = "DedicatedSupportTeam")]
            [Description("Enjoy a designated account manager and success team for ongoing support.")]
            DedicatedSupportTeam,

            [Display(Name = "AdvancedBranding")]
            [Description("Customize your platform with advanced branding options.")]
            AdvancedBranding,

            [Display(Name = "Custom Usage Limits")]
            [Description("Scale and adapt with flexible usage caps.")]
            CustomUsageLimits,

            [Display(Name = "Industry-Specific Modules")]
            [Description("Get tailored features for your specific sector.")]
            IndustrySpecificModules,
        }
    }
}
