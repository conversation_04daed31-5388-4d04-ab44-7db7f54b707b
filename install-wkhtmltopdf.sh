#!/bin/bash

# Script to install wkhtmltopdf on different platforms
# For development and testing purposes

PLATFORM=$(uname -s)
ARCH=$(uname -m)

echo "Detecting platform: $PLATFORM on $ARCH"

# Install wkhtmltopdf based on platform
if [ "$PLATFORM" == "Linux" ]; then
    # For Linux platforms
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        echo "Detected Debian/Ubuntu-based system"
        sudo apt-get update
        sudo apt-get install -y wget xfonts-75dpi xfonts-base fontconfig libxrender1
        
        if [ "$ARCH" == "x86_64" ]; then
            # 64-bit
            wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.bionic_amd64.deb
            sudo dpkg -i wkhtmltox_0.12.6-1.bionic_amd64.deb
            sudo apt-get -f install -y
            rm wkhtmltox_0.12.6-1.bionic_amd64.deb
        else
            # 32-bit
            wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.bionic_i386.deb
            sudo dpkg -i wkhtmltox_0.12.6-1.bionic_i386.deb
            sudo apt-get -f install -y
            rm wkhtmltox_0.12.6-1.bionic_i386.deb
        fi
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL/Fedora
        echo "Detected CentOS/RHEL/Fedora system"
        sudo yum install -y wget xorg-x11-fonts-75dpi xorg-x11-fonts-Type1 fontconfig
        
        if [ "$ARCH" == "x86_64" ]; then
            # 64-bit
            wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.centos8.x86_64.rpm
            sudo rpm -Uvh wkhtmltox-0.12.6-1.centos8.x86_64.rpm
            rm wkhtmltox-0.12.6-1.centos8.x86_64.rpm
        else
            # 32-bit
            wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.centos8.i686.rpm
            sudo rpm -Uvh wkhtmltox-0.12.6-1.centos8.i686.rpm
            rm wkhtmltox-0.12.6-1.centos8.i686.rpm
        fi
    else
        echo "Unsupported Linux distribution. Please install wkhtmltopdf manually."
        exit 1
    fi
    
    # Create symbolic links
    sudo ln -sf /usr/local/bin/wkhtmltopdf /usr/bin/wkhtmltopdf
    sudo ln -sf /usr/local/bin/wkhtmltoimage /usr/bin/wkhtmltoimage
    
elif [ "$PLATFORM" == "Darwin" ]; then
    # For macOS
    echo "Detected macOS"
    brew install wkhtmltopdf
    
else
    # For Windows (using WSL or Git Bash)
    echo "For Windows, please download and install from: https://wkhtmltopdf.org/downloads.html"
    echo "Make sure to add the installation directory to your PATH environment variable."
fi

# Test installation
echo "Testing wkhtmltopdf installation..."
wkhtmltopdf --version

if [ $? -eq 0 ]; then
    echo "wkhtmltopdf installed successfully!"
else
    echo "wkhtmltopdf installation may have issues. Please check and install manually if needed."
fi

echo ""
echo "Installation complete! You may need to restart your terminal or application."
