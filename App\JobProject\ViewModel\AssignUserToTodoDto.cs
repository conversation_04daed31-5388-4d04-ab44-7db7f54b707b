﻿using Jobid.App.Helpers.Utils.Attributes;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProject.ViewModel
{
    public class AssignUserToTodoDto
    {
        public List<string> UserIds { get; set; } = new List<string>();


        [CanInviteExternalUser(Helpers.Enums.Applications.Joble)]
        public List<string> ExternalMemberEmail { get; set; } = new List<string>();
        public string TeamId { get; set; }

        [JsonIgnore]
        public string Subdomain { get; set; }

        [JsonIgnore]
        public string LoggedInUserId { get; set; }
    }
}
