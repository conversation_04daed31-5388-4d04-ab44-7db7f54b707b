﻿using Jobid.App.Helpers.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace Jobid.App.JobProject.ViewModel
{
    public class TodoCommentDto
    {
        public Guid Id { get; set; }
        public string Comment { get; set; }
        public Guid TodoId { get; set; }
        public string CommentedBy { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        public UserDto Commenter { get; set; }
    }
}
