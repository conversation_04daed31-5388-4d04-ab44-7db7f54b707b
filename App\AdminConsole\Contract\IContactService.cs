using Microsoft.AspNetCore.Http;
using Jobid.App.Helpers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;

namespace Jobid.App.AdminConsole.Contract
{
    /// <summary>
    /// Contract for Contact Service operations
    /// </summary>
    public interface IContactService
    {        /// <summary>
        /// Create a new contact
        /// </summary>
        Task<ApiResponse<UserContactDto>> CreateContactAsync(CreateUserContactDto contactDto);

        /// <summary>
        /// Create multiple contacts
        /// </summary>
        Task<ApiResponse<List<UserContactDto>>> CreateContactsAsync(List<CreateUserContactDto> contacts);

        /// <summary>
        /// Get contacts for a specific user
        /// </summary>
        Task<ApiResponse<List<UserContactDto>>> GetUserContactsAsync(string userId);

        /// <summary>
        /// Check if user has uploaded contacts
        /// </summary>
        Task<ApiResponse<bool>> HasUserUploadedContactsAsync(string userId);        /// <summary>
        /// Update an existing contact
        /// </summary>
        Task<ApiResponse<UserContactDto>> UpdateContactAsync(UpdateUserContactDto contactDto);

        /// <summary>
        /// Delete a contact
        /// </summary>
        Task<ApiResponse<bool>> DeleteContactAsync(Guid contactId, string userId);

        /// <summary>
        /// Get all contacts with pagination
        /// </summary>
        Task<ApiResponse<PaginatedContactsDto>> GetAllContactsAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Get all contacts with pagination and user filter
        /// </summary>
        Task<ApiResponse<PaginatedContactsDto>> GetAllContactsAsync(int pageNumber, int pageSize, List<string> userIds);

        /// <summary>
        /// Get contact by ID
        /// </summary>
        Task<ApiResponse<UserContactDto>> GetContactByIdAsync(Guid contactId);        /// <summary>
        /// Upload contacts from CSV or Excel file
        /// </summary>
        Task<ApiResponse<ContactUploadResponseDto>> UploadContactsAsync(IFormFile file, string userId);

#region Group Operations

        /// <summary>
        /// Create a new contact group
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> CreateContactGroupAsync(CreateContactGroupDto groupDto);

        /// <summary>
        /// Update an existing contact group
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> UpdateContactGroupAsync(UpdateContactGroupDto groupDto);

        /// <summary>
        /// Delete a contact group
        /// </summary>
        Task<ApiResponse<bool>> DeleteContactGroupAsync(Guid groupId, string userId);

        /// <summary>
        /// Get contact group by ID
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> GetContactGroupByIdAsync(Guid groupId);

        /// <summary>
        /// Get contact groups for a specific user with pagination
        /// </summary>
        Task<ApiResponse<PaginatedContactGroupsDto>> GetUserContactGroupsAsync(string userId, int pageNumber, int pageSize);

        /// <summary>
        /// Get all contact groups with pagination
        /// </summary>
        Task<ApiResponse<PaginatedContactGroupsDto>> GetAllContactGroupsAsync(int pageNumber, int pageSize);        /// <summary>
        /// Add contacts to a contact group
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> AddContactsToContactGroupAsync(UpdateContactGroupContactsDto updateDto);

        /// <summary>
        /// Remove contacts from a contact group
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> RemoveContactsFromContactGroupAsync(UpdateContactGroupContactsDto updateDto);

        /// <summary>
        /// Update contact group status
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> UpdateContactGroupStatusAsync(UpdateContactGroupStatusDto statusDto);        
        
        /// <summary>
        /// Get contact group statistics for a user
        /// </summary>
        Task<ApiResponse<ContactGroupStatsDto>> GetContactGroupStatsAsync(string userId);

        /// <summary>
        /// Upload a file to an existing contact group
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> UploadContactGroupFileAsync(UploadContactGroupFileDto uploadDto);

        /// <summary>
        /// Delete a file from an existing contact group
        /// </summary>
        Task<ApiResponse<ContactGroupDto>> DeleteContactGroupFileAsync(DeleteContactGroupFileDto deleteDto);

#endregion
    }
}
