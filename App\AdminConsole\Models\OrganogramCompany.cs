﻿using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    public class OrganogramCompany
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long index { get; set; }
        public string CompanyName { get; set; }
        public string Country { get; set; }
        public string FullAddress { get; set; }
        public string EmailAddress { get; set; }
        public string BranchColor { get; set; }
        public long BelongsTo { get; set; }  //it is 0 if it is a parent company
        public EntityType EntityType { get; set; }
        public Industries Industry { get; set; }
        public CompanyType CompanyType { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string Subdomain { get; set; }
    }
}