using System.Linq;
using System.Threading.Tasks;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.ViewModel;
using Microsoft.EntityFrameworkCore;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class UserCompaniesService : IUserCompaniesServices
    {
        private JobProDbContext Db;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly JobProDbContext _subdomainContext;

        public UserCompaniesService(JobProDbContext _db, IAWSS3Sevices aWSS3Sevices, JobProDbContext subdomainContext)
        {
            Db = _db;
            _aWSS3Sevices = aWSS3Sevices;
            _subdomainContext = subdomainContext;
        }

        #region Get User Company Details
        public UserCompaniesVM GetUserCompany(string userId, string subdomain)
        {
            // Get last accessed company
            var lastAccessedCompany = GetDefaultCompany(userId).Result;

            UserCompanies userCompany = null;
            var userCompanys = Db.UserCompanies
                .Include(x => x.user)
                .Include(x => x.tenant).Where(z => z.UserId == userId && z.Active).ToList();

            if (lastAccessedCompany != null && subdomain == "api")
            {
                userCompany = userCompanys.Where(x => x.tenant.Subdomain == lastAccessedCompany).FirstOrDefault();
            }
            else if (subdomain != "api")
            {
                userCompany = userCompanys.Where(x => x.tenant.Subdomain == subdomain).FirstOrDefault();
            }
            else
            {
                userCompany = userCompanys.FirstOrDefault();
            }

            if (userCompany == null) { return null; }

            var _mapped = userCompany?.Map();
            var presignedUrl = !string.IsNullOrEmpty(_mapped?.tenant.LogoUrl) ? _aWSS3Sevices.GetSignedUrlAsync(_mapped?.tenant.LogoUrl) : null;
            _mapped.tenant.LogoUrl = presignedUrl != null ? presignedUrl.Result : null;

            // Get comapny plans
            _mapped.tenant.Plans = Db.CompanySubscriptions
                .Include(x => x.Subscription)
                .ThenInclude(x => x.PricingPlan)
                .Where(x => x.TenantId == _mapped.tenant.Id)
                .Select(x => new Plan
                {
                    PlanId = x.Subscription.PricingPlanId.ToString(),
                    PlanName = x.Subscription.PricingPlan.Name,
                    Application = x.Application.ToString()
                }).ToList();

            // Get company 2FA options
            var company2FASettings = _subdomainContext.TwoFactorSettings
                .FirstOrDefault();
            if (company2FASettings != null)
            {
                _mapped._2FAOptions = company2FASettings.options;
            }

            return _mapped;
        }
        #endregion

        #region Add Last Accessed company
        public async Task<bool> AddOrUpdateDefaultCompany(string userId, string subdomain)
        {
            if (await Db.DefaultCompanies.AnyAsync(x => x.UserId == userId))
            {
                var defaultCompany = await Db.DefaultCompanies.Where(x => x.UserId == userId).FirstOrDefaultAsync();
                defaultCompany.Subdomain = subdomain;
                Db.DefaultCompanies.Update(defaultCompany);
            }
            else
            {
                var defaultCom = new DefaultCompany
                {
                    Subdomain = subdomain,
                    UserId = userId
                };

                await Db.DefaultCompanies.AddAsync(defaultCom);
            }

            return await Db.SaveChangesAsync() > 0;
        }
        #endregion

        #region Get User's Last Accessed company
        public async Task<string> GetDefaultCompany(string userId)
        {
            var defaultCompany = await Db.DefaultCompanies.Where(x => x.UserId == userId).FirstOrDefaultAsync();
            return defaultCompany?.Subdomain;
        }
        #endregion
    }
}
