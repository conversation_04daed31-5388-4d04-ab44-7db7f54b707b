﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Subscription.Models;
using Jobid.App.Subscription.ViewModels;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Services.Contract
{
    public interface ISubscriptionServices
    {
        /// <summary>
        /// Gets the subscription plans for the application
        /// </summary>
        /// <param name="application"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        Task<GenericResponse> GetSubscriptionPlans(Applications? application, string subdomain);

        /// <summary>
        /// Get AI Subscription Plans
        /// </summary>
        /// <returns></returns>
        Task<GenericResponse> GetAISubscriptionPlans();

        /// <summary>
        /// Gets subscription details for a company
        /// </summary>
        /// <returns></returns>
        Task<GenericResponse> GetSubscriptionPlanDetails(Applications? app, string subdomain);

        /// <summary>
        /// Get plan price details
        /// </summary>
        /// <param name="app"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        Task<GenericResponse> GetPlanPriceDetails(Applications? app, string subdomain);

        /// <summary>
        /// Get subscription details for a company
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="application"></param>
        /// <returns></returns>
        Task<GenericResponse> GetCompanySubscriptionStatus(string subdomain, Applications application);

        /// <summary>
        /// Send email notification to company admin for free trial expiry
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="application"></param>
        /// <returns></returns>
        Task<GenericResponse> SendEmailNotificationToCompanyAdminForFreeTrialExpiry(string subdomain, Applications application);

        /// <summary>
        /// Create Subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> CreateSubscription(SubscribeDto model);

        /// <summary>
        /// Get subscriptions for a user or a company
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        Task<GenericResponse> GetSubscriptions(string userId, string subdomain = null);

        /// <summary>
        /// Upgrade or downgrade subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> UpgradeOrDowngradeSubscriptionPlan(UpdateSubscriptionDto model);

        /// <summary>
        /// Remove users from AI subscription plan
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> RemoveUsersFromAISubscription(RemoveOrAddUsersFromAISubReq model);

        /// <summary>
        /// Add users to an active AI subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> AddUsersToAISubscription(RemoveOrAddUsersFromAISubReq model);

        /// <summary>
        /// Cancel AI subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> CancelAISubscription(CancelAISubscriptionDto model);

        /// <summary>
        /// Create enterprize plan subscription payment link
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> InitaiteEnterprizePlanPaymentLinkCreation(EnterprizeSubscriptionPaymentDto model);
        Task<GenericResponse> ActivateEnterpriseSubscriptionManually(string tenantId, Applications app);
        Task<GenericResponse> InitiateEnterpriseSubPaymentForRenewal(string tenantId, Applications app);
        Task<GenericResponse> HandleEnterpriseStripeEvents(string json, string signature);
        Task<GenericResponse> MakeEnterprizePlanPayment(string paymentDetailsId, PaymentProviders provider);
        Task<GenericResponse> CancelSubscription(string subscriptionId, bool applyAtTheEndOfCurrentSub, string subdomain = null, bool isForEnterprisePlan = false);
        Task<GenericResponse> ResumeSubscription(Applications app, string subdomain = null, string userId = null);
        Task<GenericResponse> VerifyMolliePaymentForFE(string paymentId);
        Task<GenericResponse> VerifyFirstSubscriptionPayment(string Id);
        Task<GenericResponse> VerifySubsequentSubscriptionPayment(string Id);
        Task<GenericResponse> VerifyEnterpriseSubscriptionPayment(string paymentId);
        Task<GenericResponse> RetryFailedSubscriptionPayment(string subscriptionId);
        Task<GenericResponse> AddUsersToCurrentSubscription(BuyMoreLicenceDto model);
        Task<GenericResponse> ActivateSubscriptionManually(string subdomain, Applications app);
        Task<GenericResponse> RemoveUsersFromSubscription(int numberOfUsers, string subdoamin, Applications app, List<string> userIds);
        Task<GenericResponse> GetEnterprisePaymentDetails(string companyEmail, Applications app);
        Task<GenericResponse> ReGenerateEnterprisePaymentLink(string id, string companyEmail, Applications? app);

        /// <summary>
        /// This creates an enterprize plan for a new company (the company will be crerated before the plan is created)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> CreateEnterpriseSubscriptionForNewCompany(EnterpriseSubscriptionDto model);

        /// <summary>
        /// This creates an enterprize plan for a new company and for an existing user (the company will be crerated before the plan is created)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> CreateEnterpriseSubscriptionForNewCompanyWithExistingUser(EnterpriseSubscriptionForExistingUserDto model);

        /// <summary>
        /// This creates an enterprize plan for a new existing company
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> CreateEnterpriseSubForExistingCompany(EnterPriseOptionsDto model);
        Task<GenericResponse> MakeApplicationFavorite(Applications app, string userId, string subdomain);
        Task<bool> AddUserReviewAndRating(AddFeedbackReviewsAndRatingsDto model);
        Task<GenericResponse> HandleStripeEvents(string json, string signature);
        Task<Page<GetEnterpriseSubscriptionDto>> GetEnterpriseSubscriptions(PaginationParameters parameters, string searchKeyword);
        Task<List<TotalSubscriptionCountPerPlanDto>> GetTotalSubscriptionsCount(Applications application, PaymentProviders? paymentProvider, TimePeriodFilter periodFilter, DateTime? fromDate, DateTime? toDate);
        Task<List<PercentageIncrementPerPlanDto>> GetPercentageIncrementPerPlan(Applications application, PaymentProviders? paymentProvider, TimePeriodFilter timePeriodFilter, DateTime? fromDate, DateTime? toDate);
        Task<Page<SubscriptionCompanyDetail>> GetSubscribedCompanyDetail(SubscriptionQueryParameters subscriptionQueryParameters);
        Task<Page<SubscriptionCompanyDetail>> GetSubscribedAndUnsubscribedCompanyDetail(SubscriptionQueryParameters subscriptionQueryParameters);
        Task<List<ProviderRevenueDto>> GetRevenueByProvider(Applications application, TimePeriodFilter periodFilter, DateTime? fromDate, DateTime? toDate);
        Task<Page<TenantSubscription>> GetTenantsSubscriptionHistory(SubscriptionQueryParameters parameters);
        Task<Page<TenantSubscriptionDetail>> GetTenantSubscriptionHistory(TenantSubscriptionQueryParameters parameters);
        Task<SummaryStatisticsDto> GetSummaryStatistics(Applications application, PaymentProviders? paymentProvider);
        Task<ApplicationSummaryDto> GetAllSummaryStatistics(PaymentProviders? paymentProvider);
        Task<Page<TransactionHistoryResponse>> GetTransactionHistory(PaginationParameters parameters, Applications application, PaymentProviders? paymentProvider, string sortBy, string planId, string companyName);
        Task<GetCorperateWalletBalanceResponse> GetCorperateWalletBalance();
        Task<List<GetAllWalletbalancesResponse>> GetAllWalletbalances();
        Task<Page<TransactionHistoryResponse>> GetFailedPayments(PaginationParameters parameters, Applications application);
        Task<GetTotalWalletBalanceResponse> GetTotalWalletbalances();
        Task<GenericResponse> EditEnterpriseSubscription(UpdateEnterprisePalnDto model);
        Task<GenericResponse> DeleteEnterpriseSubscription(string tenentId, string userId, string enterPriseSubId);
        Task<GenericResponse> ActivateDeactivateReactivateEntPlan(bool activationStatus, string tenantId, Applications app, string enterPriseSubId, DateTime? expiresOn);

    }
}
