﻿using Jobid.App.Tenant.Repository;
using Jobid.App.Helpers.Utils;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;

namespace Jobid.App.Helpers.Middlewares
{
    public static class ApplyMigrationMiddleware
    {
        public static void ApplyMigration(this IApplicationBuilder app)
        {
            using IServiceScope scope = app.ApplicationServices.CreateScope();
            var tenantService = scope.ServiceProvider.GetRequiredService<TenantService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<TenantService>>();

            try
            {
                // Retry migration on serialization errors
                RetryHelper.RetryOnSerializationError(
                    async () => await tenantService.MigrateTenants(),
                    maxRetries: 5,
                    delayMs: 2000,
                    logger: logger
                ).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to apply migrations after all retry attempts");
                throw;
            }
        }
    }
}
