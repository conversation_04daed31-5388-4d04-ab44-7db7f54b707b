using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.ViewModel
{
    /// <summary>
    /// View model for HTML to PDF conversion
    /// </summary>
    public class HtmlToPdfViewModel
    {
        /// <summary>
        /// HTML content to convert to PDF
        /// </summary>
        [Required]
        public string HtmlContent { get; set; }

        /// <summary>
        /// Optional filename for the generated PDF (without extension)
        /// </summary>
        public string Filename { get; set; } = "document";
    }
}
