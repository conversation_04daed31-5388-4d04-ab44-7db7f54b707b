using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    /// <summary>
    /// Contact information for users
    /// </summary>
    public class UserContact
    {
        public UserContact()
        {
            Id = Guid.NewGuid();
            Name = string.Empty;
            PhoneNumber = string.Empty;
            Email = null;
            UserId = string.Empty;
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }        [Required]
        [Phone]
        [StringLength(20)]
        public string PhoneNumber { get; set; }

        public bool IsDeleted { get; set; } = false;

        public string Industry { get; set; }

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When this record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created this record
        /// </summary>
        [StringLength(450)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// User who last updated this record
        /// </summary>
        [StringLength(450)]
        public string UpdatedBy { get; set; }
    }
}
