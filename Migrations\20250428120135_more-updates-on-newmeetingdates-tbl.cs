﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class moreupdatesonnewmeetingdatestbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public moreupdatesonnewmeetingdatestbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 1. Drop the old UNIQUE index
            migrationBuilder.DropIndex(
                name: "IX_ProposedDateDetails_MeetingId",
                schema: _Schema, // Replace _Schema with your actual schema name
                table: "ProposedDateDetails");

            // 2. Recreate the index WITHOUT unique constraint
            migrationBuilder.CreateIndex(
                name: "IX_ProposedDateDetails_MeetingId",
                schema: _Schema,
                table: "ProposedDateDetails",
                column: "MeetingId",
                unique: false); // Now NOT unique
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
            name: "IX_ProposedDateDetails_MeetingId",
            schema: _Schema,
            table: "ProposedDateDetails");

            migrationBuilder.CreateIndex(
                name: "IX_ProposedDateDetails_MeetingId",
                schema: _Schema,
                table: "ProposedDateDetails",
                column: "MeetingId",
                unique: true); // Recreate as UNIQUE (original state)
        }
    }
}
