﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Jobid.App.Calender.Models.RTC.Responses
{
    public class Data
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("meetingId")]
        public string MeetingId { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("hostJobAuthId")]
        public string HostJobAuthId { get; set; }

        [JsonProperty("coHostJobAuthIds")]
        public List<object> CoHostJobAuthIds { get; set; }

        [JsonProperty("passcode")]
        [DefaultValue("")]
        public object? Passcode { get; set; }

        [JsonProperty("isPrivate")]
        public bool IsPrivate { get; set; }

        [JsonProperty("guestJobAuthIds")]
        public List<string> GuestJobAuthIds { get; set; }

        [JsonProperty("externalGuestEmails")]
        public List<string> ExternalGuestEmails { get; set; }

        [JsonProperty("tenantId")]
        public string TenantId { get; set; }

        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }
    }

    public class CreateMeetingResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("data")]
        public Data Data { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }
}
