﻿using Jobid.App.JobProjectManagement.ViewModel;
using System.Collections.Generic;
using System;
using Jobid.App.Helpers.Models;

namespace Jobid.App.JobProject.ViewModel
{
    public class SprintReportSummaryDto
    {
        public string Id { get; set; }
        public string SprintName { get; set; }
        public string Status { get; set; }
        public string Duration { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string PercentageCompleted { get; set; }
        public List<UserDto> Members { get; set; } = new List<UserDto>();
        public List<ProjectMgmt_Todo> Todos { get; set; }
    }
}
