using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.Contract;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class UserProfileServices : IUserProfileServices
    {
        private JobProDbContext publicSchemaContext;
        JobProDbContext subdomainSchemaContext;
        private readonly IWebHostEnvironment _environment;
        private readonly IEmailService _emailService;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private static ILogger _logger = Log.ForContext<UserProfileServices>();

        public UserProfileServices(JobProDbContext publicSchemaContext, JobProDbContext subdomainSchemaContext, IWebHostEnvironment environment, IEmailService emailService, IAWSS3Sevices aWSS3Sevices)
        {
            this.publicSchemaContext = publicSchemaContext;
            this.subdomainSchemaContext = subdomainSchemaContext;
            _environment = environment;
            _emailService = emailService;
            _aWSS3Sevices = aWSS3Sevices;
        }

        #region Get User profile details with comapny details
        public async Task<ApiResponse<UserProfile>> GetTenantUserProfileFromPublicUserId(string userId)
        {
            var response = new ApiResponse<UserProfile>();

            var user = publicSchemaContext.Users.FirstOrDefault(x => x.Id == userId);
            if (user == null)
            {
                response.ResponseCode = "500";
                response.ResponseMessage = "User not found";
                return response;
            }

            var userProfile = subdomainSchemaContext.UserProfiles.FirstOrDefault(x => x.UserId == userId);
            var role = subdomainSchemaContext.UserAndRoleIds.Include(i => i.EmployeeRoles).Where(x => x.UserProId == userId)
                        .Select(s => s.EmployeeRoles.RoleName).FirstOrDefaultAsync();
            if (userProfile == null)
            {
                response.ResponseCode = "500";
                response.ResponseMessage = "User Profile not found.";
            }
            else
            {
                // Get company details
                var tenant = await publicSchemaContext.UserCompanies
                    .Include(x => x.tenant)
                    .Where(t => t.UserId == userProfile.UserId)
                    .Select(x => x.tenant).FirstOrDefaultAsync();
                userProfile.CompanyDetails = tenant;

                userProfile.ProfilePictureUrl = userProfile.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(userProfile.ProfilePictureUrl) : null;

                response.ResponseCode = "200";
                response.ResponseMessage = "User Profile found.";
                response.Data = userProfile;
            }

            return response;
        }
        #endregion

        #region Get User profile
        public async Task<GenericResponse> GetAllUserInJobproWithCompanyDetails()
        {
            var response = new GenericResponse();
            var users = await publicSchemaContext.Users.ToListAsync();

            if (users == null || !users.Any())
            {
                response.ResponseCode = "200";
                response.ResponseMessage = "No records found.";
            }
            else
            {
                foreach (var user in users)
                {
                    var userCompany = await publicSchemaContext.UserCompanies
                        .Include(x => x.tenant)
                        .Where(t => t.UserId == user.Id)
                        .FirstOrDefaultAsync();

                    user.UserCompany = userCompany;
                }

                response.ResponseCode = "200";
                response.ResponseMessage = "Records found.";
                response.Data = users;
            }

            return response;
        }
        #endregion

        #region Send doamain verification request mail
        /// <summary>
        /// This is used to send an email notification to the super admin to verify the domain
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> SendVerificationRequest(SendVerificationRequestDto model)
        {
            var company = await publicSchemaContext.Tenants
                .FirstOrDefaultAsync(t => t.Id.ToString() == model.TenanId);
            if (company == null)
                throw new RecordNotFoundException("Company does not exist");

            if (company.Subdomain.ToLower() != model.Subdomain.ToLower())
                throw new Exception("supplied subdomain does not belong to the company");

            // Get super admin details
            var admin = await subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(u => u.UserId == company.AdminId);

            var user = await subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(u => u.UserId == model.LoggedInUserId);

            // Send an email notification to the user
            var url = Utility.Constants.ADMIN_URL;
            var parameters = new Dictionary<string, string>
            {
                { "{admin-user}", admin.FirstName },
                { "{employee-name}", user.FirstName },
                { "{domain}", company.VerifiedEmailDomain },
                { "{company}", company.CompanyName },
                { "{url}", url },
            };

            _logger.Information("Calling UpdateTemplateWithParams method with {parameters}", parameters);

            var template = Utils.Extensions.UpdateTemplateWithParams("verify-domain-notification", _environment, parameters);
            if (string.IsNullOrEmpty(template))
                throw new Exception("Template not found");

            var subject = "Domain Verification Request";
            await _emailService.SendEmail(template, admin.Email, subject);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Verification request sent successfully."
            };
        }
        #endregion
    }
}
