using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.Helpers;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.AdminConsole.Contract
{
    public interface IWalletService
    {
        Task<GenericResponse> GetWalletBalance();
        Task<GenericResponse> FundWallet(FundWalletDto model);
        Task<GenericResponse> GetTransactions(DateTime? startDate = null, DateTime? endDate = null);
        Task<GenericResponse> GetTransaction(Guid transactionId);
    }
} 