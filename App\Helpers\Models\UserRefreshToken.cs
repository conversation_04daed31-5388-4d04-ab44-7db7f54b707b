﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.Models
{
    public class UserRefreshToken
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public string Token { get; set; }
        public DateTime Expires { get; set; }
        public bool IsExpired => DateTime.UtcNow >= Expires;
        public DateTime Created { get; set; }
        public string CreatedByIp { get; set; }
        public DateTime? Revoked { get; set; }
        public string RevokedByIp { get; set; }
        public string ReplacedByToken { get; set; }
        public bool IsActive => Revoked == null && !IsExpired;
        public string UserId { get; set; }

        // Navigation Properties
        public User User { get; set; }
    }
}
