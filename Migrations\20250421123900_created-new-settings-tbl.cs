﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class creatednewsettingstbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public creatednewsettingstbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "UserLoggedOut",
                schema: _Schema,
                table: "UserProfiles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "CompanyDeletionRequests",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RequestId = table.Column<string>(type: "text", nullable: false),
                    RequestedBy = table.Column<string>(type: "text", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    DateRequested = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    ActionPerfomedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyDeletionRequests", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Localizations",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TimeZone = table.Column<string>(type: "text", nullable: true),
                    LanguagePreference = table.Column<string>(type: "text", nullable: true),
                    CalenderWeekStartsOn = table.Column<string>(type: "text", nullable: true),
                    DateFormat = table.Column<string>(type: "text", nullable: true),
                    TimeFormat = table.Column<string>(type: "text", nullable: true),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    AmountFormat = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Localizations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PasswordPolicies",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MinimumPasswordLength = table.Column<int>(type: "integer", nullable: false),
                    RequireAtLeastOneUppercase = table.Column<bool>(type: "boolean", nullable: false),
                    RequireAtLeastOneLowercase = table.Column<bool>(type: "boolean", nullable: false),
                    RequireAtLeastOneNumber = table.Column<bool>(type: "boolean", nullable: false),
                    RequireAtLeastOneSpecialCharacter = table.Column<bool>(type: "boolean", nullable: false),
                    ProhibitUserNameAsPassword = table.Column<bool>(type: "boolean", nullable: false),
                    RequirePasswordExpiration = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordExpirationDays = table.Column<int>(type: "integer", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PasswordPolicies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TwoFactorSettings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    options = table.Column<int>(type: "integer", nullable: false),
                    Require2FA = table.Column<bool>(type: "boolean", nullable: false),
                    ImidiatelyRequire2FA = table.Column<bool>(type: "boolean", nullable: false),
                    Require2FAOnThirdLogin = table.Column<bool>(type: "boolean", nullable: false),
                    Require2FAOnSecondFailedLoginAttempt = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TwoFactorSettings", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CompanyDeletionRequests",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Localizations",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PasswordPolicies",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "TwoFactorSettings",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "UserLoggedOut",
                schema: _Schema,
                table: "UserProfiles");
        }
    }
}
