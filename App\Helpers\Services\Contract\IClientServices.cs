﻿using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Contract
{
    public interface IClientServices
    {
        Task<bool> CreateClientRole(ClientRole clientRole);
        Task<bool> CreateRoleModule(RoleModuleVm roleModuleVm, string roleId);
        Task<List<RoleModule>> GetModules();
        Task<bool> CreateModule(ModuleVm moduleVm, string userId = "");
        Task<List<RoleDtoVm>> GetRoles();
        Task<bool> DeleteRoleById(string Id);
        Task<bool> DeleteModuleRoleById(string Id);
        Task<ClientRole> GetRoleById(string roleId);
        Task<bool> UpdatePermission(UpdatePermissionDtoVm updatePermissionDtoVm, string permissionId);
        Task<bool> UpdateModule(ModuleVm moduleVm, string moduleId);
        Task<bool> CreateRole(RoleVm roleVm, string userId = "");
        Task<bool> AddModuleToRole(ModuleDtoVm moduleDtoVm, string roleId);
        Task<bool> UpdateUserRole(string roleId, string userId);
        Task<RoleModule> GetModulesById(string moduleId);
        Task<RoleDtoVm> GetCLientRole(ClientRole clientRole);
        Task<bool> CreatePermission(PermissionVm permissionVm, string moduleId);
        Task<Permission> GetPermission(string permissionId);
        Task<RoleDtoVm> GetRoleDtoById(string roleId);
        Task Test();
    }
}
