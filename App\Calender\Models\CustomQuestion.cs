﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class CustomQuestion
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public QuestionType QuestionType { get; set; }
        public string Question { get; set; }
        public string? Options { get; set; }
        public Guid ExternalMeetingId { get; set; }
        public ExternalMeeting ExternalMeeting { get; set; }

    }

    public enum QuestionType
    {
        Select,
        Radio,
        Checkbox,
        Paragraph,
        ShortText
    }
}
