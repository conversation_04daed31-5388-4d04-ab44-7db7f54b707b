﻿using Jobid.App.Helpers.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.Models
{
    public class ProjectTag
    {
        [Key]
        public Guid Id { get; set; }
        [Required]
        public string TagName { get; set; }
        public string TenantId { get; set; }
        public Guid? ProjectMgmt_ProjectId { get; set; }
        public ProjectMgmt_Project? projectMgmt_Project { get; set; }

        public Guid? ProjectMgmt_TodoId { get; set; }
        public ProjectMgmt_Todo? projectMgmt_Todo { get; set; }
        public Guid? TimeSheetId { get; set; }
        public TimeSheet TimeSheet { get; set; }

        public string UserId { get; set; }
    }
}
