﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Jobid.App.AdminConsole.Models.AI
{
    public class CompanyFilesSummary
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        [ForeignKey("Tenant")]
        public Guid TenantId { get; set; }
        public string SummarizedText { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }

        // Navigation properties
        public virtual Jobid.App.Tenant.Model.Tenant Tenant { get; set; }

        public CompanyFilesSummary()
        {
            Id = Guid.NewGuid();
            CreatedOn = DateTime.UtcNow;
        }
    }
}
