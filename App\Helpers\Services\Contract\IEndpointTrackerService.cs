﻿﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface IEndpointTrackerService
    {
        /// <summary>
        /// Tracks an endpoint call
        /// </summary>
        /// <param name="application">The application</param>
        /// <param name="section">The section of the application</param>
        /// <param name="controller">The controller name</param>
        /// <param name="action">The action/endpoint name</param>
        /// <param name="httpMethod">The HTTP method</param>
        /// <param name="userId">The user ID who made the call</param>
        /// <param name="tenantId">The tenant ID where the call was made</param>
        /// <returns>True if successful</returns>
        Task<bool> TrackEndpointCallAsync(
            Applications application,
            ApplicationSection section,
            string controller,
            string action,
            string httpMethod);

        /// <summary>
        /// Gets endpoint call counts for a specific application and time range filter
        /// </summary>
        /// <param name="application">The application</param>
        /// <param name="timeRange">The time range filter</param>
        /// <returns>Endpoint call tracking response with section and monthly data</returns>
        Task<GenericResponse> GetEndpointCallsAsync(
            Applications application,
            TimeRangeFilter timeRange);

        /// <summary>
        /// Gets endpoint call counts grouped by section for a specific application and date range
        /// </summary>
        /// <param name="application">The application</param>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <returns>Dictionary with section as key and count as value</returns>
        Task<Dictionary<string, int>> GetEndpointCallsBySectionAsync(
            Applications application,
            DateTime startDate,
            DateTime endDate);
    }
}
