using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Tenant.Model
{
    public class DeletedTenant
    {
        [Key]
        public Guid Id { get; set; }
        
        // Original tenant information
        public Guid OriginalTenantId { get; set; }
        public string CompanyName { get; set; }
        public string CompanyAddress { get; set; }
        public string WorkSpace { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string VerifiedEmailDomain { get; set; }
        public DateTime OriginalDateCreated { get; set; }
        public string LogoUrl { get; set; }
        public int CompanySize { get; set; }
        public string Status { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string Region { get; set; }
        public string CompanyType { get; set; }
        public string RegNumber { get; set; }
        public string AdminId { get; set; }
        
        [Column(TypeName = "varchar(24)")]
        public Industries Industry { get; set; }
        
        // Deletion information
        public DateTime DeletedAt { get; set; }
        public string DeletedBy { get; set; }
        public string DeletionReason { get; set; }
        
        // Additional metadata
        public int TotalUsers { get; set; }
        public int ActiveSubscriptions { get; set; }
        public string LastMigrationDate { get; set; }
        public string DeletionNotes { get; set; }
    }
}
