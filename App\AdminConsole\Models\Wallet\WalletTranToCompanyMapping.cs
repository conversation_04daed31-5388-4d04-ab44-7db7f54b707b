﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models.Wallet
{
    public class WalletTranToCompanyMapping
    {
        public Guid Id { get; set; }

        public string TransactionId { get; set; }

        [ForeignKey(nameof(TenantId))]
        public Guid TenantId { get; set; }
        public DateTime CreatedOn { get; set; }

        // Navigationa Properties
        public virtual Jobid.App.Tenant.Model.Tenant Tenant { get; set; }

        public WalletTranToCompanyMapping()
        {
            Id = Guid.NewGuid();
            CreatedOn = DateTime.UtcNow;
        }
    }
}
