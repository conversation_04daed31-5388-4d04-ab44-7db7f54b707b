﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.ActivityLog.Model
{
    public class LogAttachment
    {
        public string Id { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public DateTime CreatedOn { get; set; }

        // Navigational Properties
        [ForeignKey("Activity")]
        public Guid ActivityId { get; set; }
        public Activity Activity { get; set; }

        public LogAttachment()
        {
            Id = Guid.NewGuid().ToString();
            CreatedOn = DateTime.UtcNow;
        }
    }
}
