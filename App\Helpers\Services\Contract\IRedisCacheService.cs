﻿using System;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface IRedisCacheService
    {
        /// <summary>
        /// Get Data using key
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<T> GetDataAsync<T>(string key);

        /// <summary>
        /// Set Data with Value and Expiration Time of Key
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expirationTime"></param>
        /// <returns>bool</returns>
        Task<bool> SetDataAsync<T>(string key, T value, DateTimeOffset expirationTime);

        /// <summary>
        /// Remove Data
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<bool> RemoveDataAsync(string key);

        /// <summary>
        /// Delete All Keys
        /// </summary>
        /// <returns></returns>
        Task<string[]> GetAllKeysAsync();
    }
}
