﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class updated_phone_no_tbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updated_phone_no_tbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON><PERSON>",
                schema: _Schema,
                table: "PhoneNumbers",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TwilioSid",
                schema: _Schema,
                table: "PhoneNumbers",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>",
                schema: _Schema,
                table: "PhoneNumbers");

            migrationBuilder.DropColumn(
                name: "TwilioSid",
                schema: _Schema,
                table: "PhoneNumbers");
        }
    }
}
