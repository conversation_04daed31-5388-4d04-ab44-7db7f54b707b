﻿using Jobid.App.Subscription.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace Jobid.App.Helpers.Extensions
{
    public static class StripeServiceExtrension
    {
        public static void AddStripeService(this IServiceCollection services, IConfiguration configuration)
        {
            // Add stripe configuration values
            services.Configure<StripeOptions>(options =>
            {
                options.PublishableKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_PUB_KEY_TEST") ?? configuration["StripeOptions:PublishableKey"];
                options.SecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_SECRET_KEY_TEST") ?? configuration["StripeOptions:SecretKey"];
                options.WebhookSecret = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_WEBHOOK_SECRET_TEST") ?? configuration["StripeOptions:WebhookSecret"];
                options.WalletWebHookSecret = Environment.GetEnvironmentVariable("JOBPRO_WALLET_STRIPE_WEBHOOK_SECRET_TEST") ?? configuration["StripeOptions:WebhookSecret"];
            });
        }
    }
}
