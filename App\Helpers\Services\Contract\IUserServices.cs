﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;


namespace Jobid.App.Helpers.Contract
{
    public interface IUserServices
    {
        /// <summary>
        /// Get last login details of a user
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<bool> LogLastLogin(string userId);

        /// <summary>
        /// Get user companies/workspaces
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<IEnumerable<UserCompanies>> GetUserCompanies(string userId);

        /// <summary>
        /// Get free pla id
        /// </summary>
        /// <returns></returns>
        Task<string> GetFreePlanId();

        /// <summary>
        /// Add email to wait list
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<bool> AddEmailToWaitList(WaitListDto model);

        /// <summary>
        /// Black list a token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        Task<bool> BlacklistJwtToken(string token);

        /// <summary>
        /// Get user by email
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<User> GetUserById(string id);

        /// <summary>
        /// Get all users
        /// </summary>
        /// <param name="region"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="searchparam"></param>
        /// <returns></returns>
        public Task<Page<User>> GetAllUsers(Region region = Region.Africa, int pageNumber = 1, int pageSize = 20, string searchparam = null);

        /// <summary>
        /// Generate Ref No
        /// </summary>
        /// <returns></returns>
        public string GenerateRef();

        /// <summary>
        /// Check if user exist by phone No
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        Task<bool> CheckIfUserExistByPhoneNumber(string phoneNumber);

        /// <summary>
        /// Activate/Deactivate or Delete an Individual User Account
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        Task<GenericResponse> ActivateDeactivateOrDeleteIndUserAcct(string userId, IndividualUserAccountStatus status);
        public bool CheckRefExists(string refe);
        public Task<UserRefreshToken> AuthenticateRefreshToken(string token, string ipAddress);
        Task<ApiResponse<bool>> AddOrUpdateUserOnlineStatus(string userId, UserOnlineStatusOptions status);
        Task<ApiResponse<string>> GetUserOnlineStatus(string userId);
        public Task<bool> RevokeToken(string ipAddress);
        Task<User> GetUserByRefreshToken(string refreshToken);
        public Task AddOrUpdateRefreshToken(UserRefreshToken refreshToken);
        public Task<bool> SaveUser(User user);
        Task<GenericResponse> GenerateQRCodeForSmartLogin(string browserId, HttpRequest httpRequest);
        string GetIp(IPAddress remoteIpAddress);
        Task<bool> lockUserIp(string userId, bool ipUpdate = true);
        Task<bool> ValidateUserIp(User user, string ipAdress);
        Task<bool> SaveNewUserProfile(User user);
        Task<User> GetUserByMicrosoftGraphAccessToken(MicrosoftAuthModel model);
        Task<List<UserMDVm>> GetTeamMembers(string? nameParam, string subdoamin);
        Task<GenericResponse> GetTeamMemberDetails(List<string> userIds, string subdomain);
        Task<List<Country>> GetCountries();
        Task<bool> UpdateUserType(string userId, UserTypes userType);
        Task<UserProfile> GetUserByEmail(string email);
        Task<UserProfile> GetUserProfileById(string userId, JobProDbContext context = null);
        Task<bool> AssignRoleAndPermissionsToUser(string userid, string role, string subdomain);
        Task<bool> AddUserToRole(string userId, string role, string appName, string subdomain, string connectionStr);
        Task<string> GetUserRoleName(string roleId);
        Task<bool> DeleteUserRole(string userId, string role, string appName);
        Task<GenericResponse> GetUserRolesAndPermissions(string userId, string appName);
        Task<List<WaitingEmailList>> GetUserFromWaitList();
        Task<ApiResponse<string>> GetUserSubdomain(string email);
        Task<List<UserMDVm>> GetAllUsers(string personalEmail = null);
        Task<GenericResponse> InternalServiceLogin(GrpcAuthenticateRequest request);
        Task<GenericResponse> GetAllServices();
        Task<bool> CheckIfJobProIdExist(string jobPproId);

        // Skills
        Task<GenericResponse> AddOrUpdateUserSkills(AddOrUpdateSkillsDto model);
        Task<GenericResponse> DeleteUserSkills(List<string> skillIds);
        Task<GenericResponse> GetUserSkills(string userId);

        // Account Deletion
        Task<GenericResponse> RequestForAccountDeletion(RequestForAccountDeletionDto model);
        Task<GenericResponse> CancelAccountDeletion(string requestId);
        Task<GenericResponse> GetCompanyDeletionRequests(string userId);
    }
}
