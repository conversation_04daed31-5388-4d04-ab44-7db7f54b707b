﻿using Jobid.App.Subscription.Enums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Subscription.Models
{
    public class AISubscriptionDetail
    {
        public Guid Id { get; set; }

        [ForeignKey("Subscription")]
        public Guid SubscriptionId { get; set; }
        public AIAgents Agent { get; set; }
        public int NoOfUserSubscribedFor { get; set; }
        public double Amount { get; set; }

        // Navigation property
        public Subscription Subscription { get; set; }
    }
}
