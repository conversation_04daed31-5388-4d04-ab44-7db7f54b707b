﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Jobid.App.Helpers.ViewModel
{
    public class Endpoints
    {
        public string CreateCorporate { get; set; }
        public string SendEmailVerification { get; set; }
        public string Createpassword { get; set; }
        public string CreateConsumer { get; set; }
        public string SendEmailVerificationConsumers { get; set; }
        public string Verifyconsumeremail { get; set; }
        public string Verifycorporateemail { get; set; }
        public string GetToken { get; set; }
        public string StartKyb { get; set; }
        public string StartKyc { get; set; }
        public string StartKycMobile { get; set; }
        public string CreateManagedAccount { get; set; }
        public string CreateIban { get; set; }
        public string CreateDebitCard { get; set; }
        public string StepUpToken { get; set; }
        public string VerifyChallenge { get; set; }
        public string Enrol2FA { get; set; }
        public string Verify2FA { get; set; }
        public string CreateAuthUser { get; set; }
        public string SendEmailVerificationAuthUser { get; set; }
        public string GetWeavrAccount { get; set; }
        public string AddBeneficiary { get; set; }
        public string StartBenOtp { get; set; }
        public string VerifyBen { get; set; }
        public string GetAllBen { get; set; }
        public string InternalTransfer { get; set; }
        public string SepaOutwireTransfers { get; set; }
        public string GetIBan { get; set; }
        public string SendOutwireChallenge { get; set; }
        public string Verifyoutwirechallenge { get; set; }
        public string TransferToOwnAccount { get; set; }
        public string SendInternalTransferChallenge { get; set; }
        public string VerifyInternalTransferChallenge { get; set; }
        public string GetManageAccount { get; set; }
        public string RemoveBeneficiary { get; set; }
    }

    public class ProvidHivesEnv
    {
        public string Is_Live { get; set; }
        public string BaseURL { get; set; }
        public string ClientID { get; set; }
        public string ClientSecret { get; set; }
        public string X_AUTH_SIG { get; set; }
        public string createDynamicAccountNumber { get; set; }
        public string updateAccountName { get; set; }
        public string verifyTransaction { get; set; }
        public string blackListAccount { get; set; }
        public string createReservedAccountNumber { get; set; }
        public string verifyTransactionBySessionID { get; set; }
        public string verifyTransactionBySettlemmentID { get; set; }
        public string accountSettlementNotification { get; set; }
        public string USN { get; set; }
        public string PWD { get; set; }
        public string TP_BaseURL { get; set; }
        public string GetNIPAccount { get; set; }
        public string NIPFundTransfer { get; set; }
        public string GetNIPTransactionStatus { get; set; }
        public string GetNIPBanks { get; set; }
        public string ProvidusFundTransfer { get; set; }
        public string GetProvidusTransactionStatus { get; set; }
        public string GetProvidusAccount { get; set; }
        public string NIPFundTransferMultipleDebitAccounts { get; set; }
    }


    public class Grpc
    {
        public string BaseUrl { get; set; }
    }

    public class HangfireSettings
    {
        public string UnsuspendEmployeeTime { get; set; }
    }

    public class WeavrConfigurations
    {
        public string API_KEY { get; set; }
    }

    public class RTCEndpoints
    {
        public string BaseUrl { get; set; }
        public string CreateMeeting { get; set; }
        public string ClientUrl { get; set; }
        public string UserColloborationCount { get; set; }

    }

    public class AIEnpoints
    {
        public string BaseUrl { get; set; }
        public string RescheduleMeeting { get; set; }
    }

    public class SuperAdminEnpoints
    {
        public string BaseUrl { get; set; }
        public string CreateTicketExternal { get; set; }
    }

    public class BridgeCard
    {
        public string BaseUrl { get; set; }
        public string CardDetailsBaseUrl { get; set; }
        public string NairaCardOtpBaseUrl { get; set; }
        public string AuthToken { get; set; }
        public string SecretKey { get; set; }
        public string WebHookKey { get; set; }
        public string IssuingID { get; set; }
        public string RegCardHolder { get; set; }
        public string CardHolderDetails { get; set; }
        public string DeleteCardHolder { get; set; }
        public string CreateCard { get; set; }
        public string ActivatePhysicalCard { get; set; }
        public string CardDetails { get; set; }
        public string FundCard { get; set; }
        public string FundTestWallet { get; set; }
        public string UnloadCard { get; set; }
        public string MockDebit { get; set; }
        public string CardTransactions { get; set; }
        public string CardTransactionById { get; set; }
        public string CardTransactionStatus { get; set; }
        public string FreezeCard { get; set; }
        public string UnFreezeCard { get; set; }
        public string CardHolderCards { get; set; }
        public string DeleteCard { get; set; }
        public string UpdateCardPin { get; set; }
        public string NairaCardBalance { get; set; }
        public string NairaCardOtp { get; set; }
        public string FundNairaCard { get; set; }
        public string NairaCardTransactions { get; set; }
        public string NairaFreezeCard { get; set; }
        public string NairaUnFreezeCard { get; set; }
    }

    public class TwilioAddressSids
    {
        public Dictionary<string, string> CountrySids { get; set; }

        public string GetSid(string countryCode)
        {
            if (CountrySids != null && CountrySids.TryGetValue(countryCode.ToUpper(), out var sid))
            {
                return sid;
            }

            return null;
        }
    }

    public class CustomAppSettings
    {
        public string ApplicationName { get; set; }
        public string AllowedHosts { get; set; }
        public string SendGridAPIKey { get; set; }
        public string RootDomain { get; set; }
        public string CreateAccountUrl { get; set; }
        public string HivSecret { get; set; }
        public string MicroservicekycResponse { get; set; }
        public Endpoints Endpoints { get; set; }
        public ProvidHivesEnv ProvidHivesEnv { get; set; }
        public string WebhookSignature { get; set; }
        public Grpc Grpc { get; set; }
        public HangfireSettings HangfireSettings { get; set; }
        public WeavrConfigurations WeavrConfigurations { get; set; }
        public RTCEndpoints RTCEndpoints { get; set; }
        public string InterfaceEnv { get; set; }
        public string[] AllowedCorsOrigins { get; set; }
        public AIEnpoints AIEnpoints { get; set; }
        public SuperAdminEnpoints SuperAdminEnpoints { get; set; }
        public BridgeCard BridgeCard { get; set; }
        public string UserColloborationCount { get; set; }
        public string FrontendUrl { get; set; }
        public TwilioAddressSids TwilioAddressSids { get; set; }
    }
}
