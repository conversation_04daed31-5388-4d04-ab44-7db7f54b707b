﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Subscription.Enums;
using Jobid.App.Subscription.Models;
using Jobid.App.Subscription.ViewModels;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Mollie.Api.Client;
using Serilog;
using Stripe;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Controllers
{
    public class SubscriptionController : ControllerBase
    {
        private readonly IUnitofwork _unitofwork;
        private readonly ITenantSchema _tenantSchema;
        private readonly ILogger _logger = Log.ForContext<SubscriptionController>();

        public SubscriptionController(IUnitofwork unitofwork, ITenantSchema tenantSchema)
        {
            _unitofwork = unitofwork;
            _tenantSchema = tenantSchema;
        }

        #region Create Subscription
        /// <summary>
        /// This allows a user to create a subscription or update payment method used for subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("api/subscription/createsubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateSubscription([FromBody] SubscribeDto model)
        {
            try
            {
                var usedId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.UserId = usedId;
                model.Subdomain = subdomain == "api" ? null : subdomain;
                if (model.IsAISubscription)
                    model.Application = Applications.All;

                GenericResponse response = await _unitofwork.SubscriptionServices.CreateSubscription(model);

                if (response.ResponseCode == "200")
                    return Ok(response);
                else
                    return BadRequest(response);
            }
            catch (MollieApiException ex)
            {
                _logger.Error(ex.ToString(), "Error creating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = ex.ToString(),
                    Data = false
                });
            }
            catch (StripeException ex)
            {
                _logger.Error(ex.ToString(), "Error creating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = ex.ToString(),
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Error creating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "404",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "Error creating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "404",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Get Subscription Details
        /// <summary>
        /// Get subscription details for a user/company
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("api/subscription/getsubscriptionplandetails")]
        public async Task<IActionResult> GetSubsciptionDetails([FromQuery] Applications? app, string subdomain)
        {
            GenericResponse response = await _unitofwork.SubscriptionServices.GetSubscriptionPlanDetails(app, subdomain);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Get Subscription Plan Price Details
        [HttpGet]
        [Route("api/subscription/getsubscriptionplandetailsprice")]
        public async Task<IActionResult> GetSubscriptionPlanPriceDetails([FromQuery] Applications? app)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            GenericResponse response = await _unitofwork.SubscriptionServices.GetPlanPriceDetails(app, subdomain);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Get Current Subscription
        /// <summary>
        /// Get the current subscription for a user/company
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("api/subscription/getcurrentsubscriptions")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetSubscriptions()
        {
            var usedId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            subdomain = subdomain == "api" ? null : subdomain;

            GenericResponse response = await _unitofwork.SubscriptionServices.GetSubscriptions(usedId, subdomain);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Get Company Subscription Status
        /// <summary>
        /// Gets the subscription status for a company
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("api/subscription/getcompanysubscriptionstatus")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetCompanySubscriptionStatus(Applications app)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            subdomain = subdomain == "api" ? null : subdomain;

            GenericResponse response = await _unitofwork.SubscriptionServices.GetCompanySubscriptionStatus(subdomain, app);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Send Email Notification To Company Admin For Free Trial Expiry
        /// <summary>
        /// Send email notification to company admin for free trial expiry
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("api/subscription/sendfreetrialendnotification")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SendFreeTrialEndNotification(Applications app)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            subdomain = subdomain == "api" ? null : subdomain;

            GenericResponse response = await _unitofwork.SubscriptionServices.SendEmailNotificationToCompanyAdminForFreeTrialExpiry(subdomain, app);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Upgrade/Downgrade Subscription
        /// <summary>
        /// This allows a user/company to upgrade/downgrade their subscription plan
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("api/subscription/upgradeordowngrade")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateSubscription([FromBody] UpdateSubscriptionDto model)
        {
            try
            {
                var usedId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.UserId = usedId;
                model.Subdomain = subdomain;

                GenericResponse response = await _unitofwork.SubscriptionServices.UpgradeOrDowngradeSubscriptionPlan(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (MollieApiException ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Your subscription cannot be updagraded at this time, your card is fully charged. Please contact support",
                    DevResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Cancel Subscription
        /// <summary>
        /// This allows a user/company to cancel their subscription
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="applyAtTheEndOfCurrentSub"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("api/subscription/cancel/{subscriptionId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CancelSubscription(string subscriptionId, bool applyAtTheEndOfCurrentSub = false)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            subdomain = subdomain == "api" ? null : subdomain;
            GenericResponse response = await _unitofwork.SubscriptionServices.CancelSubscription(subscriptionId, applyAtTheEndOfCurrentSub, subdomain);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Cancel AI Subscription
        /// <summary>
        /// This allows a user/company to cancel their subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("api/subscription/ai/cancel/{subscriptionId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CancelAISubscription(CancelAISubscriptionDto model)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                subdomain = subdomain == "api" ? null : subdomain;
                model.Subdomain = subdomain;

                GenericResponse response = await _unitofwork.SubscriptionServices.CancelAISubscription(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, "CancelAISubscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Resume Subscription
        /// <summary>
        /// This allows a user/company to resume their subscription
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("api/subscription/resume")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ResumeSubscription(Applications app)
        {
            var usedId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            subdomain = subdomain == "api" ? null : subdomain;

            GenericResponse response = await _unitofwork.SubscriptionServices.ResumeSubscription(app, subdomain, usedId);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Get Subscription Plans
        /// <summary>
        /// This gets all the subscription plans for a for jobpro. With appliation param supplied, it fetches plans for a specific application, otherwise, it fetches all plans
        /// </summary>
        /// <param name="application"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("api/subscription/getplans")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetSubscriptionPlans(Applications? application)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            GenericResponse response = await _unitofwork.SubscriptionServices.GetSubscriptionPlans(application, subdomain);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Get AI Subscription plans
        /// <summary>
        /// Gets all AI subscription plans
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("api/subscription/ai/getaisubscriptionplans")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAISubscriptionPlans()
        {
            GenericResponse response = await _unitofwork.SubscriptionServices.GetAISubscriptionPlans();
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Retry failed subscription payment
        /// <summary>
        /// This allows a user/company to retry a failed subsequent subscription payment
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("api/subscription/retry")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RetryFailedSubscriptionPayment(string subscriptionId)
        {
            try
            {
                GenericResponse response = await _unitofwork.SubscriptionServices.RetryFailedSubscriptionPayment(subscriptionId);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex.Message, "Error retrying failed subscription payment");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Verify Mollie Payment For FE Comfirmation
        /// <summary>
        /// Verify Mollie Payment For FE Comfirmation
        /// </summary>
        /// <param name="paymentId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/verifymolliepayment/{paymentId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> VerifyMolliePayment(string paymentId)
        {
            GenericResponse response = await _unitofwork.SubscriptionServices.VerifyMolliePaymentForFE(paymentId);
            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Webhook Endpoint - Verify First Subscription Payment - Mollie
        /// <summary>
        /// Webhook Endpoint - Verify First Subscription Payment
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/webhook/verifyfirstpayment")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> VerifyFirstSubscriptionPayment([FromForm] string id)
        {
            try
            {
                //var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                //var subscription = JsonConvert.DeserializeObject<Subscription>(json);

                GenericResponse response = await _unitofwork.SubscriptionServices.VerifyFirstSubscriptionPayment(id);
                if ((bool)response.Data)
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Error verifying first subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying first subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex}");
            }
        }
        #endregion

        #region Webhook Endpoint - Verify Subsequent Subscription Payment - Mollie
        /// <summary>
        /// Webhook Endpoint - Verify Subsequent Subscription Payment
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/webhook/verifysubsequentpayment")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> VerifySubsequentSubscriptionPayment([FromForm] string id)
        {
            try
            {
                GenericResponse response = await _unitofwork.SubscriptionServices.VerifySubsequentSubscriptionPayment(id);
                if ((bool)response.Data)
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error verifying subsequent subscription payment");
                return BadRequest("Something went wrong, please try again later");
            }
        }
        #endregion

        #region Webhook Endpoint - Verify Enterprise Subscription Payment - Mollie
        /// <summary>
        /// Webhook Endpoint - Verify First Subscription Payment
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/webhook/verifyenterprisepayment")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> VerifyEnterpriseSubscriptionPayment([FromForm] string id)
        {
            try
            {
                GenericResponse response = await _unitofwork.SubscriptionServices.VerifyEnterpriseSubscriptionPayment(id);
                if ((bool)response.Data)
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Error verifying enterprise subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying enterprise subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex}");
            }
        }
        #endregion

        #region Webhook Endpoint - Stripe Webhook
        /// <summary>
        /// Webhook Endpoint - Stripe Webhook
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/stripe/webhook")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> StripeWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var signature = Request.Headers["Stripe-Signature"];
                var response = await _unitofwork.SubscriptionServices.HandleStripeEvents(json, signature);
                if (response.ResponseCode == "200")
                {
                    return Ok(response);
                }
                else
                {
                    _logger.Error(response.ResponseMessage, "Error handling stripe webhook");
                    return BadRequest(response);
                }
            }
            catch (StripeException ex)
            {
                _logger.Error(ex.Message, "Error handling stripe webhook");
                return BadRequest($"Something went wrong, please try again later - {ex.ToString()}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error verifying subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex.ToString()}");
            }
        }
        #endregion

        #region Webhook Endpoint - Stripe Webhook:Enterprise
        /// <summary>
        /// Webhook Endpoint - Stripe Webhook
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/stripe/enterprise/webhook")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> StripeEnterpriseWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                var signature = Request.Headers["Stripe-Signature"];
                var response = await _unitofwork.SubscriptionServices.HandleStripeEvents(json, signature);
                if (response.ResponseCode == "200")
                {
                    return Ok(response);
                }
                else
                {
                    _logger.Error(response.ResponseMessage, "Error handling stripe webhook");
                    return BadRequest(response);
                }
            }
            catch (StripeException ex)
            {
                _logger.Error(ex.Message, "Error handling stripe enterprise webhook");
                return BadRequest($"Something went wrong, please try again later - {ex.ToString()}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error verifying stripe enterprise subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex.ToString()}");
            }
        }
        #endregion

        #region Add user to subscription
        /// <summary>
        /// Add more users to an active subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
        [HttpPost]
        [Route("api/subscription/adduserstosubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddUsersToSubscription([FromBody] BuyMoreLicenceDto model)
        {
            try
            {
                var usedId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.Subdomain = subdomain;
                model.LoggedInUserId = usedId;

                if (model.UserIds == null)
                    model.UserIds = new List<string>();

                GenericResponse response = await _unitofwork.SubscriptionServices
                    .AddUsersToCurrentSubscription(model);

                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error adding users to subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Remove user from subscription
        /// <summary>
        /// Removes users from an active subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
        [HttpPut]
        [Route("api/subscription/removeuserfromsubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RemoveUserFromSubscription([FromBody] RemoveUserFromSubDto model)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                GenericResponse response = await _unitofwork.SubscriptionServices.RemoveUsersFromSubscription(model.NumberOfUsers, subdomain, model.Application, model.UserIds);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, "Error removing user from subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex.Message, "Error removing user from AI subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Remove user from AI subscription
        /// <summary>
        /// Removes users from an active AI subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
        [HttpPut]
        [Route("api/subscription/ai/removeusersfromsubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> RemoveUserFromAISubscription([FromBody] RemoveOrAddUsersFromAISubReq model)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                GenericResponse response = await _unitofwork.SubscriptionServices.RemoveUsersFromAISubscription(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, "Error removing user from AI subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex.Message, "Error removing user from AI subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Add users to an active AI subscription
        /// <summary>
        /// Removes users from an active AI subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
        [HttpPut]
        [Route("api/subscription/ai/adduserstosubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddUserFromAISubscription([FromBody] RemoveOrAddUsersFromAISubReq model)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                GenericResponse response = await _unitofwork.SubscriptionServices.AddUsersToAISubscription(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, "Error removing user from AI subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex.Message, "Error removing user from AI subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Activate subscription manually
        /// <summary>
        /// Activate subscription manually
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("api/subscription/activatesubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ActivateSubscriptionManually(Applications app)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                GenericResponse response = await _unitofwork.SubscriptionServices.ActivateSubscriptionManually(subdomain, app);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Activae enterprise subscription manually
        /// <summary>
        /// This allows a user/company to activate an enterprise subscription manually
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/activateenterprisesubscription")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ActivateEnterpriseSubscriptionManually([Required] string tenantId, [Required] Applications app)
        {
            try
            {
                GenericResponse response = await _unitofwork.SubscriptionServices.ActivateEnterpriseSubscriptionManually(tenantId, app);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error activating enterprise subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Initiate enterprize plan payment link geberation
        /// <summary>
        /// Generate enterprise subscription payment link
        /// </summary>
        /// <param name="model"></param>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/createenterprisepaymentlink")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        public async Task<IActionResult> CreateEnterprizePlanPaymentLink([FromBody] EnterprizeSubscriptionPaymentDto model)
        {
            model.LoggedInUser = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var response = await _unitofwork.SubscriptionServices.InitaiteEnterprizePlanPaymentLinkCreation(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Initaite enterprise plan payment for renewal/Renew enterprise subscription
        /// <summary>
        /// Initiate enterprise subscription payment for renewal
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/initaiteenterprisepaymentforrenewal")]
        public async Task<IActionResult> InitiateEnterprisePaymentForRenewal([Required] string tenantId, Applications app)
        {
            var response = await _unitofwork.SubscriptionServices.InitiateEnterpriseSubPaymentForRenewal(tenantId, app);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Make enterprize plan payment
        /// <summary>
        /// Make enterprise subscription payment
        /// </summary>
        /// <param name="paymentDetailsId"></param>
        /// <param name="provider"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("api/subscription/makeenterprisepayment")]
        public async Task<IActionResult> MakeEnterprizePlanPayment([Required] string paymentDetailsId, PaymentProviders provider)
        {
            var response = await _unitofwork.SubscriptionServices.MakeEnterprizePlanPayment(paymentDetailsId, provider);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Get Enterprise Payment Details
        /// <summary>
        /// Get Enterprise Payment Details
        /// </summary>
        /// <param name="companyEmail"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/getenterprisepaymentdetails")]
        public async Task<IActionResult> GetEnterprisePaymentDetails([Required] string companyEmail, [Required] Applications app)
        {
            var response = await _unitofwork.SubscriptionServices.GetEnterprisePaymentDetails(companyEmail, app);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Re-Generate enterprise payment link
        /// <summary>
        /// Re-genetrate enterprise payment link
        /// </summary>
        /// <param name="id"></param>
        /// <param name="companyEmail"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/regenerateenterprisepaymentlink")]
        public async Task<IActionResult> ReGenerateEnterprisePaymentLink(string id, string companyEmail, Applications? app)
        {
            var response = await _unitofwork.SubscriptionServices.ReGenerateEnterprisePaymentLink(id, companyEmail, app);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Create an Enterprise subscription for a new company
        /// <summary>
        /// This creates an enterprise subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/onboardandcreateenterprisesub")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateEnterpriseSubscription([FromBody] EnterpriseSubscriptionDto model)
        {
            try
            {
                var response = await _unitofwork.SubscriptionServices.CreateEnterpriseSubscriptionForNewCompany(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Create an Enterprise subscription for a new company and for an existing user
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/onboardandcreateenterprisesubforexistinguser")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> OnbaordAndCreateEnterpriseSubscriptionForExistingUser([FromBody] EnterpriseSubscriptionForExistingUserDto model)
        {
            try
            {
                var response = await _unitofwork.SubscriptionServices.CreateEnterpriseSubscriptionForNewCompanyWithExistingUser(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Edit an Enterprise subscription
        /// <summary>
        /// This Edit an enterprise subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPut]
        [Route("api/subscription/editenterprisesub")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> EditEnterpriseSubscription([FromBody] UpdateEnterprisePalnDto model)
        {
            try
            {
                var response = await _unitofwork.SubscriptionServices.EditEnterpriseSubscription(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex.ToString(), "Error editing subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error editing subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex.ToString(), "Error editing subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Delete an Enterprise subscription
        /// <summary>
        /// This Delete an enterprise subscription
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="userId"></param>
        /// <param name="enterPriseSubId"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpDelete]
        [Route("api/subscription/deleteenterprisesub/{tenantId}/{userId}/{enterPriseSubId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> DeleteEnterpriseSubscription([FromRoute] string tenantId, string userId, string enterPriseSubId)
        {
            try
            {
                var response = await _unitofwork.SubscriptionServices.DeleteEnterpriseSubscription(tenantId, userId, enterPriseSubId);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error deleting subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Get Enterprise Subscription
        /// <summary>
        /// Get Enterprise Subscription
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="searchKeyword"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/getenterprisesubscriptions")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetEnterPriceSubscription([FromQuery] PaginationParameters parameters, string? searchKeyword)
        {
            try
            {
                var enterprisePlan = await _unitofwork.SubscriptionServices.GetEnterpriseSubscriptions(parameters, searchKeyword);
                return Ok(new ApiResponse<Page<GetEnterpriseSubscriptionDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Transaction history fetched successfully",
                    Data = enterprisePlan
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching enterprise plan");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
          
        }
        #endregion

        #region Create an Enterprise subscription for an existing company
        /// <summary>
        /// Create an enterprise subscription for an existing companincrementy
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/createenterprisesubforexistingcompany")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CreateEnterpriseSubscriptionForExistingCompany([FromBody] EnterPriseOptionsDto model)
        {
            try
            {
                var response = await _unitofwork.SubscriptionServices.CreateEnterpriseSubForExistingCompany(model);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Activate, Deactivate or Reactivate an Enterprise plan
        /// <summary>
        /// Activate, Deactivate or Reactivate an Enterprise plan
        /// </summary>
        /// <param name="activationStatus"></param>
        /// <param name="app"></param>
        /// <param name="enterPriseSubId"></param>
        /// <param name="expiresOn"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpPost]
        [Route("api/subscription/activateordeactivateenterpriseplan/{tenantId}/{enterPriseSubId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ActivateDeactivateReactivateEntPlan([FromQuery] bool activationStatus, [FromRoute] string tenantId, Applications app, [FromRoute] string enterPriseSubId, [FromQuery] DateTime? expiresOn)
        {
            try
            {
                var response = await _unitofwork.SubscriptionServices.ActivateDeactivateReactivateEntPlan(activationStatus, tenantId, app, enterPriseSubId, expiresOn);
                if (response.ResponseCode == "200")
                    return Ok(response);
                else return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex.ToString(), "Error activating subscription");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Mark an application as favorite
        /// <summary>
        /// Mark an application as favorite
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("api/subscription/markasfavorite")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> MarkApplicationAsFavorite(Applications app)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            subdomain = subdomain ?? "api";
            var loggedInUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            GenericResponse response = await _unitofwork.SubscriptionServices.MakeApplicationFavorite(app, loggedInUserId, subdomain);

            if (response.ResponseCode == "200")
                return Ok(response);
            else return BadRequest(response);
        }
        #endregion

        #region Add user review and ratings
        /// <summary>
        /// Adds user review and ratings
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("api/subscription/addreview")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> AddUserReviewAndRatings([FromBody] AddFeedbackReviewsAndRatingsDto model)
        {
            var loggedInUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            model.UserId = loggedInUserId;
            var response = await _unitofwork.SubscriptionServices.AddUserReviewAndRating(model);

            return Ok(new ApiResponse<bool>
            {
                ResponseCode = "200",
                ResponseMessage = "User review and ratings added successfully",
                Data = response
            });
        }
        #endregion

        #region Get subscription percentage increment
        /// <summary>
        /// Get subscription percentage increment
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <param name="periodFilter"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/percentage-increment")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetPercentageIncrementPerPlan([FromQuery][Required] Applications application, [FromQuery] PaymentProviders paymentProvider, [FromQuery][Required] TimePeriodFilter periodFilter, [FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            if (periodFilter == TimePeriodFilter.Custom)
            {
                if (fromDate == null)
                    fromDate = DateTime.UtcNow.AddMonths(-6);

                if (toDate == null)
                    toDate = DateTime.UtcNow;
            }

            var percentageIncrementPerPlanDtos = await _unitofwork.SubscriptionServices.GetPercentageIncrementPerPlan(application, paymentProvider, periodFilter, fromDate, toDate);
            return Ok(new ApiResponse<List<PercentageIncrementPerPlanDto>>
            {
                ResponseCode = "200",
                ResponseMessage = "Subscription percentage per plan fetched successfully",
                Data = percentageIncrementPerPlanDtos
            });
        }
        #endregion

        #region Get subscribed company detail
        /// <summary>
        /// Get subscribed company detail
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/subscribed-company")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetSubscribedCompanyDetail([FromQuery][Required] SubscriptionQueryParameters parameters)
        {
            try
            {
                if (parameters.PeriodFilter == TimePeriodFilter.Custom)
                {
                    if (parameters.FromDate == null)
                        parameters.FromDate = DateTime.UtcNow.AddMonths(-6);

                    if (parameters.ToDate == null)
                        parameters.ToDate = DateTime.UtcNow;
                }

                var pagedSubscriptionCompanyDetail = await _unitofwork.SubscriptionServices.GetSubscribedCompanyDetail(parameters);
                return Ok(new ApiResponse<Page<SubscriptionCompanyDetail>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Subscribed company detail fetched successfully.",
                    Data = pagedSubscriptionCompanyDetail
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching subscribed company detail");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get subscribed and unsubscribed company detail
        /// <summary>
        /// Get subscribed and unsubscribed company detail
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/all-company")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetSubscribedAndUnsubscribedCompanyDetail([FromQuery][Required] SubscriptionQueryParameters parameters)
        {
            try
            {
                if (parameters.PeriodFilter == TimePeriodFilter.Custom)
                {
                    if (parameters.FromDate == null)
                        parameters.FromDate = DateTime.UtcNow.AddMonths(-6);

                    if (parameters.ToDate == null)
                        parameters.ToDate = DateTime.UtcNow;
                }

                var pagedSubscriptionCompanyDetail = await _unitofwork.SubscriptionServices.GetSubscribedAndUnsubscribedCompanyDetail(parameters);
                return Ok(new ApiResponse<Page<SubscriptionCompanyDetail>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Subscribed and unsubscribed company detail fetched successfully.",
                    Data = pagedSubscriptionCompanyDetail
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching subscribed and unsubscribed company detail");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get subsctiption statistics
        /// <summary>
        /// Get subscription statistics
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/statistic")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetSummaryStatistics([FromQuery][Required] Applications application, PaymentProviders? paymentProvider)
        {
            try
            {
                var statisticsDto = await _unitofwork.SubscriptionServices.GetSummaryStatistics(application, paymentProvider);
                return Ok(new ApiResponse<SummaryStatisticsDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Subscription statistic fetched successfully",
                    Data = statisticsDto
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching subscription statistic");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get all subsctiption statistics
        /// <summary>
        /// Get All subscription statistics
        /// </summary>
        /// <param name="paymentProvider"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/allstatistic")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAllSummaryStatistics([FromQuery] PaymentProviders? paymentProvider)
        {
            try
            {
                var statisticsDto = await _unitofwork.SubscriptionServices.GetAllSummaryStatistics(paymentProvider);
                return Ok(new ApiResponse<ApplicationSummaryDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = "All Subscription statistic fetched successfully",
                    Data = statisticsDto
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching all subscription statistic");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Revenue per provider
        /// <summary>
        /// This method is used to get revenue per provider
        /// </summary>
        /// <param name="application"></param>
        /// <param name="periodFilter"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/revenue")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetRevenueByProvider([FromQuery][Required] Applications application, [FromQuery][Required] TimePeriodFilter periodFilter, [FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            try
            {
                var revenueDtos = await _unitofwork.SubscriptionServices.GetRevenueByProvider(application, periodFilter, fromDate, toDate);
                return Ok(new ApiResponse<List<ProviderRevenueDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Subscription revenue fetched successfully",
                    Data = revenueDtos
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching subscription revenue");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Tenants subscription history
        /// <summary>
        /// This method is used to get revenue per provider
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/tenants/history")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTenantsSubscriptionHistory([FromQuery][Required] SubscriptionQueryParameters parameters)
        {
            try
            {
                var revenueDtos = await _unitofwork.SubscriptionServices.GetTenantsSubscriptionHistory(parameters);
                return Ok(new ApiResponse<Page<TenantSubscription>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Subscription history fetched successfully",
                    Data = revenueDtos
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching subscription history");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Tenant subscription history
        /// <summary>
        /// This method a single tenant subscription history
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/tenant/history")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTenantSubscriptionHistory([FromQuery][Required] TenantSubscriptionQueryParameters parameters)
        {
            try
            {
                var revenueDtos = await _unitofwork.SubscriptionServices.GetTenantSubscriptionHistory(parameters);
                return Ok(new ApiResponse<Page<TenantSubscriptionDetail>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Subscription history fetched successfully",
                    Data = revenueDtos
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching subscription history");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get total subscription count
        /// <summary>
        /// Get total subscription count
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <param name="timePeriod"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/total-count")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTotalSubscriptionsCount([FromQuery][Required] Applications application, [FromQuery] PaymentProviders? paymentProvider, [FromQuery][Required] TimePeriodFilter timePeriod, [FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            try
            {
                if (timePeriod == TimePeriodFilter.Custom)
                {
                    if (fromDate == null)
                        fromDate = DateTime.UtcNow.AddMonths(-6);

                    if (toDate == null)
                        toDate = DateTime.UtcNow;
                }

                var totalSubscriptionCount = await _unitofwork.SubscriptionServices.GetTotalSubscriptionsCount(application, paymentProvider, timePeriod, fromDate, toDate);
                return Ok(new ApiResponse<List<TotalSubscriptionCountPerPlanDto>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Total Subscription count fetched successfully",
                    Data = totalSubscriptionCount
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching total subscription count");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Transaction History
        /// <summary>
        /// This method is used to get the transaction history
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="application"></param>
        /// <param name="providers"></param>
        /// <param name="planId"></param>
        /// <param name="sortBy"></param>
        /// <param name="companyName"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/transaction/history")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTransactionHistory([FromQuery] PaginationParameters parameters, [Required] Applications application, PaymentProviders? providers, string? sortBy, string? planId, string? companyName)
        {
            try
            {
                var transactionHistory = await _unitofwork.SubscriptionServices.GetTransactionHistory(parameters, application,providers,sortBy, planId,companyName);
                return Ok(new ApiResponse<Page<TransactionHistoryResponse>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Transaction history fetched successfully",
                    Data = transactionHistory
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching transaction history");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Corpearte wallet balance
        /// <summary>
        /// This method is used to get the corperate wallet balance
        /// </summary>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/corporate-wallet/history")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetCorporateWalletBalance()
        {
            try
            {
                var corperateWalletBalance = await _unitofwork.SubscriptionServices.GetCorperateWalletBalance();
                return Ok(new ApiResponse<GetCorperateWalletBalanceResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Corporate wallet balance fetched successfully",
                    Data = corperateWalletBalance
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching transaction history");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get All Wallet balances 
        /// <summary>
        /// This method is used to get the All Wallet balances
        /// </summary>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/transaction/GetAllWalletbalances")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetAllWalletbalances()
        {
            try
            {
                var allWalletbalances = await _unitofwork.SubscriptionServices.GetAllWalletbalances();
                return Ok(new ApiResponse<List<GetAllWalletbalancesResponse>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Wallet Balance fetched successfully",
                    Data = allWalletbalances
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching Wallet balance");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Failed Payments 
        /// <summary>
        /// This method is used to get the Failed Payments
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="application"></param>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/transaction/GetFailedPayments")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetFailedPayments([FromQuery] PaginationParameters parameters, [Required] Applications application)
        {
            try
            {
                var transactionHistory = await _unitofwork.SubscriptionServices.GetFailedPayments(parameters, application);
                return Ok(new ApiResponse<Page<TransactionHistoryResponse>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Failed Payments fetched successfully",
                    Data = transactionHistory
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching failed payments");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region  Get Total Available Wallet Balance  
        /// <summary>
        /// This method is used to get the Total Wallet Available Balance
        /// </summary>
        /// <returns></returns>
        //[Authorize(AuthenticationSchemes = "super-admin")]
        [HttpGet]
        [Route("api/subscription/transaction/GetTotalWalletBalances")]
        [ProducesResponseType(200)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTotalWalletBalances()
        {
            try
            {
                var totalWalletBalance = await _unitofwork.SubscriptionServices.GetTotalWalletbalances();
                return Ok(new ApiResponse<GetTotalWalletBalanceResponse>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Corporate wallet balance fetched successfully",
                    Data = totalWalletBalance
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error fetching transaction history");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion
    }
}
