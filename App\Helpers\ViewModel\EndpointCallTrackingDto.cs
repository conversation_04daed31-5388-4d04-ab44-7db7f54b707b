﻿﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;

namespace Jobid.App.Helpers.ViewModel
{
    /// <summary>
    /// Response model for endpoint call tracking
    /// </summary>
    public class EndpointCallTrackingResponse
    {
        /// <summary>
        /// The application that the endpoint belongs to
        /// </summary>
        public Applications Application { get; set; }
        
        /// <summary>
        /// The time range filter used for the query
        /// </summary>
        public TimeRangeFilter TimeRange { get; set; }
        
        /// <summary>
        /// The start date of the time range
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// The end date of the time range
        /// </summary>
        public DateTime EndDate { get; set; }
        
        /// <summary>
        /// The sections with their monthly usage data
        /// </summary>
        public List<SectionUsage> Sections { get; set; } = new List<SectionUsage>();
    }
    
    /// <summary>
    /// Usage data for a specific section
    /// </summary>
    public class SectionUsage
    {
        /// <summary>
        /// The section name
        /// </summary>
        public string SectionName { get; set; }
        
        /// <summary>
        /// The total number of calls for this section
        /// </summary>
        public int TotalCalls { get; set; }
        
        /// <summary>
        /// The monthly usage data
        /// </summary>
        public List<MonthlyUsage> MonthlyUsage { get; set; } = new List<MonthlyUsage>();
    }
    
    /// <summary>
    /// Usage data for a specific month
    /// </summary>
    public class MonthlyUsage
    {
        /// <summary>
        /// The month (1-12)
        /// </summary>
        public int Month { get; set; }
        
        /// <summary>
        /// The year
        /// </summary>
        public int Year { get; set; }
        
        /// <summary>
        /// The month name
        /// </summary>
        public string MonthName { get; set; }
        
        /// <summary>
        /// The number of calls for this month
        /// </summary>
        public int Calls { get; set; }
    }
}
