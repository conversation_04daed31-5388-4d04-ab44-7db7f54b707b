#region Using Directives
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Models.Calls;
using Jobid.App.AdminConsole.Models.Phone;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers;
using Jobid.App.Notification.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Rest.Api.V2010.Account.AvailablePhoneNumberCountry;
using Twilio.TwiML;
using Twilio.TwiML.Voice;
using Task = System.Threading.Tasks.Task;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Microsoft.Extensions.Caching.Memory;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProjectManagement.ViewModel;
using Serilog;
using ILogger = Serilog.ILogger;
using Utility = Jobid.App.Helpers.Utils.Utility;
using AutoMapper;
using Jobid.App.AdminConsole.Dto.AI;
using Jobid.App.AdminConsole.Models.AI;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.Helpers.Models;
using Jobid.App.AdminConsole.Hubs;
using Jobid.App.Helpers.ViewModel;
#endregion

namespace Jobid.App.AdminConsole.Services
{
    /// <summary>
    /// Pure Twilio PSTN telephony service for outbound and inbound calls with WebRTC support
    /// </summary>
    public class PhoneNumberService : IPhoneNumberService
    {
        #region Private Fields And Constructor
        private ILogger _logger = Log.ForContext<PhoneNumberService>();
        private readonly IConfiguration _configuration;
        private readonly JobProDbContext _context;
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly IMemoryCache _cache;
        private readonly JobProDbContext _publicContext;
        private readonly string _conString;
        private readonly IMapper _mapper;
        private readonly IWikiFileService _wikiFileService;
        private readonly IHubContext<CallHub> _callHubContext;
        private readonly ITwilioService _twilioService;
        private readonly CustomAppSettings _appSettings;

        public PhoneNumberService(
            IConfiguration configuration,
            JobProDbContext context,
            IHubContext<NotificationHub> hubContext,
            IMemoryCache cache,
            JobProDbContext publicContext,
            IMapper mapper,
            IWikiFileService wikiFileService,
            IHubContext<CallHub> callHubContext,
            ITwilioService twilioService)
        {
            _configuration = configuration;
            _context = context;
            _hubContext = hubContext;
            _cache = cache;
            _publicContext = publicContext;
            _conString = GlobalVariables.ConnectionString;
            _mapper = mapper;
            _wikiFileService = wikiFileService;
            _callHubContext = callHubContext;
            _twilioService = twilioService;
            _appSettings = GlobalVariables.CustomAppSettings;

            // Initialize Twilio
            var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? _configuration["Twilio:AccountSID"];
            var authToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN") ?? _configuration["Twilio:AuthToken"];
            TwilioClient.Init(accountSid, authToken);
        }
        #endregion

        #region Get Available Phone Numbers
        /// <summary>
        /// Gets available phone numbers from Twilio for purchase in a specific country.
        /// Search priority: Local numbers first, then mobile numbers, then toll-free numbers (if enabled in appsettings).
        /// </summary>
        /// <param name="countryCode">ISO country code (e.g., "US", "CA")</param>
        /// <param name="areaCode">Optional area code to filter results</param>
        /// <param name="phoneType">Optional phone type filter ("Local", "Mobile", "TollFree"). If not specified, all types are searched.</param>
        /// <returns>List of available phone numbers with their capabilities and pricing</returns>
        public async Task<GenericResponse> GetAvailablePhoneNumbers(string countryCode, string areaCode = null, string phoneType = null)
        {
            _logger.Information($"Getting available phone numbers for country: {countryCode}, area code: {areaCode}, phone type: {phoneType}");
            var availableNumbers = new List<AvailablePhoneNumberDto>();

            // Determine which phone types to search based on the filter
            var searchLocal = string.IsNullOrEmpty(phoneType) || phoneType.Equals("Local", StringComparison.OrdinalIgnoreCase);
            var searchMobile = string.IsNullOrEmpty(phoneType) || phoneType.Equals("Mobile", StringComparison.OrdinalIgnoreCase);
            var searchTollFree = string.IsNullOrEmpty(phoneType) || phoneType.Equals("TollFree", StringComparison.OrdinalIgnoreCase);

            // Search for local phone numbers first (if not filtered out)
            if (searchLocal)
            {
                try
                {
                    var localNumbers = await LocalResource.ReadAsync(
                        pathCountryCode: countryCode,
                        areaCode: !string.IsNullOrEmpty(areaCode) ? int.Parse(areaCode) : (int?)null,
                        limit: 20
                    );

                    foreach (var number in localNumbers)
                    {
                        availableNumbers.Add(new AvailablePhoneNumberDto
                        {
                            PhoneNumber = number.PhoneNumber.ToString(),
                            FriendlyName = number.PhoneNumber.ToString(), // Use phone number as friendly name
                            Region = number.Region,
                            Locality = number.Locality,
                            NumberType = PhoneNumberType.Local.ToString(),
                            Capabilities = GetCapabilities(number),
                            MonthlyPrice = 1.00m, // Twilio standard pricing
                            Currency = "USD"
                        });
                    }
                }
                catch (Twilio.Exceptions.ApiException localEx) when (localEx.Status == 404)
                {
                    _logger.Warning($"Local phone numbers not available for country {countryCode}. Status: {localEx.Status}, Error: {localEx.Message}");
                }
                catch (Exception localEx)
                {
                    _logger.Warning(localEx, $"Failed to fetch local phone numbers for country {countryCode}");
                }
            }

            // If we need more numbers or no local numbers found, try mobile numbers (if not filtered out)
            if (searchMobile && availableNumbers.Count < 10)
            {
                try
                {
                    var mobileNumbers = await MobileResource.ReadAsync(
                        pathCountryCode: countryCode,
                        limit: 10 - availableNumbers.Count
                    );

                    foreach (var number in mobileNumbers)
                    {
                        availableNumbers.Add(new AvailablePhoneNumberDto
                        {
                            PhoneNumber = number.PhoneNumber.ToString(),
                            FriendlyName = number.PhoneNumber.ToString(), // Use phone number as friendly name
                            Region = number.Region,
                            Locality = "Mobile",
                            NumberType = PhoneNumberType.Mobile.ToString(),
                            Capabilities = GetCapabilities(number),
                            MonthlyPrice = 1.50m, // Mobile numbers typically cost slightly more
                            Currency = "USD"
                        });
                    }
                }
                catch (Twilio.Exceptions.ApiException mobileEx) when (mobileEx.Status == 404)
                {
                    _logger.Warning($"Mobile phone numbers not available for country {countryCode}. Status: {mobileEx.Status}, Error: {mobileEx.Message}");
                }
                catch (Exception mobileEx)
                {
                    _logger.Warning(mobileEx, $"Failed to fetch mobile phone numbers for country {countryCode}");
                }
            }

            // If we still need more numbers and toll-free is enabled, try toll-free (if not filtered out)
            var tollFreeEnabled = _configuration.GetValue<bool>("Twilio:EnableTollFree", true);
            if (searchTollFree && availableNumbers.Count < 10 && tollFreeEnabled)
            {
                try
                {
                    var tollFreeNumbers = await TollFreeResource.ReadAsync(
                        pathCountryCode: countryCode,
                        limit: 10 - availableNumbers.Count
                    );

                    foreach (var number in tollFreeNumbers)
                    {
                        availableNumbers.Add(new AvailablePhoneNumberDto
                        {
                            PhoneNumber = number.PhoneNumber.ToString(),
                            FriendlyName = number.PhoneNumber.ToString(), // Use phone number as friendly name
                            Region = number.Region,
                            Locality = "Toll-Free",
                            NumberType = PhoneNumberType.TollFree.ToString(),
                            Capabilities = GetCapabilities(number),
                            MonthlyPrice = 2.00m, // Toll-free typically costs more
                            Currency = "USD"
                        });
                    }
                }
                catch (Twilio.Exceptions.ApiException tollFreeEx) when (tollFreeEx.Status == 404)
                {
                    _logger.Warning($"Toll-free phone numbers not available for country {countryCode}. Status: {tollFreeEx.Status}, Error: {tollFreeEx.Message}");
                }
                catch (Exception tollFreeEx)
                {
                    _logger.Warning(tollFreeEx, $"Failed to fetch toll-free phone numbers for country {countryCode}");
                }
            }

            _logger.Information($"Found {availableNumbers.Count} available phone numbers for {countryCode}");

            // If no numbers are available for this country, return appropriate message
            if (availableNumbers.Count == 0)
            {
                var filterMessage = !string.IsNullOrEmpty(phoneType) ? $" of type '{phoneType}'" : "";
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = $"No phone numbers{filterMessage} are available for purchase in country {countryCode}. This country may not be supported by Twilio or may not have any available numbers at this time.",
                    Data = new List<AvailablePhoneNumberDto>()
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Available phone numbers retrieved successfully",
                Data = availableNumbers
            };
        }
        #endregion

        #region Purchase Phone Number
        /// <summary>
        /// Purchases a new phone number from Twilio for the specified tenant
        /// </summary>
        /// <param name="model">Purchase request containing phone number and tenant information</param>
        /// <returns>Response with purchased phone number details</returns>
        public async Task<GenericResponse> PurchasePhoneNumber(PurchasePhoneNumberDto model)
        {
            // The phone number should be provided in the model (from GetAvailablePhoneNumbers)
            if (string.IsNullOrEmpty(model.PhoneNumber))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Phone number is required for purchase"
                };
            }

            // Comfirm that the number has not been already mapped to a company
            var existingMapping = await _publicContext.PhoneNoToCompanyMappings
                .AnyAsync(p => p.PhoneNumber == model.PhoneNumber);
            if (existingMapping)
                return new GenericResponse
                {
                    ResponseCode = "409",
                    ResponseMessage = "Phone number is already registered to a company"
                };

            var baseUrl = Utility.Constants.BACKEND_BASE_URL;

            // Get addess sid from custome configuration
            var addressSid = _appSettings.TwilioAddressSids.GetSid(model.Country);

            // Purchase the phone number through Twilio
            var purchasedNumber = await IncomingPhoneNumberResource.CreateAsync(
                phoneNumber: new Twilio.Types.PhoneNumber(model.PhoneNumber),
                voiceUrl: new Uri($"{baseUrl}/PhoneNumber/webhook/handle-inbound-call?subdomain={model.Subdomain}"),
                voiceMethod: "POST",
                addressSid: addressSid,
                statusCallback: new Uri($"{baseUrl}/PhoneNumber/webhook/phone-status?subdomain={model.Subdomain}"),
                statusCallbackMethod: "POST"
            //smsUrl: new Uri($"{baseUrl}/api/PhoneNumber/handle-inbound-sms"),
            //smsMethod: "POST"
            );

            _logger.Information($"Successfully purchased phone number {purchasedNumber.PhoneNumber} from Twilio. SID: {purchasedNumber.Sid}");

            // Determine the number type based on the phone number
            var numberType = DeterminePhoneNumberType(model.PhoneNumber);

            // Save the purchased number to database
            var phoneNumber = new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
            {
                Number = purchasedNumber.PhoneNumber.ToString(),
                IsActive = true,
                IsRegistered = true,
                CountryCode = model.Country,
                NumberType = numberType,
                TwilioSid = purchasedNumber.Sid,
                FriendlyName = purchasedNumber.FriendlyName,
                Balance = 0
            };

            // Add capabilities based on the model
            foreach (var capability in model.Capabilities)
            {
                phoneNumber.Capabilities.Add(new PhoneNumberCapability
                {
                    CapabilityType = (CapabilityType)capability
                });
            }

            // Add phone number mapping to company
            var tenantId = await _publicContext.Tenants
                .Where(t => t.Subdomain == model.Subdomain)
                .Select(t => t.Id)
                .FirstOrDefaultAsync();
            if (tenantId == Guid.Empty)
            {
                _logger.Error($"Tenant with subdomain {model.Subdomain} not found");
                return new GenericResponse { ResponseCode = "404", DevResponseMessage = "Tenant not found", ResponseMessage = "Something went wrong, please try again later." };
            }

            var phoneNoToCompanyMapping = new PhoneNoToCompanyMapping
            {
                PhoneNumber = phoneNumber.Number,
                TenantId = tenantId
            };

            _publicContext.PhoneNoToCompanyMappings.Add(phoneNoToCompanyMapping);
            _context.PhoneNumbers.Add(phoneNumber);

            await _publicContext.SaveChangesAsync();
            await _context.SaveChangesAsync();

            _logger.Information($"Phone number {phoneNumber.Number} saved to database with ID {phoneNumber.Id}");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Phone number purchased successfully",
                Data = new
                {
                    PhoneNumberId = phoneNumber.Id,
                    Number = phoneNumber.Number,
                    NumberType = phoneNumber.NumberType.ToString(),
                    TwilioSid = purchasedNumber.Sid,
                    FriendlyName = purchasedNumber.FriendlyName,
                    Capabilities = model.Capabilities.Select(c => c.ToString()).ToList(),
                    MonthlyPrice = phoneNumber.NumberType == PhoneNumberType.TollFree ? 2.00m :
                                  phoneNumber.NumberType == PhoneNumberType.Mobile ? 1.50m : 1.00m,
                    Currency = "USD"
                }
            };
        }
        #endregion

        #region Register Existing Phone Number
        /// <summary>
        /// Registers an existing phone number in the system without purchasing from Twilio
        /// </summary>
        /// <param name="model">Registration request containing phone number and tenant information</param>
        /// <returns>Response with registered phone number details</returns>
        public async Task<GenericResponse> RegisterExistingNumber(RegisterPhoneNumberDto model)
        {
            _logger.Information($"Registering existing phone number {model.Number} for tenant {model.TenantId}");

            // Confirm that the tenant exists
            var tenantExists = await _publicContext.Tenants
                .AnyAsync(t => t.Id.ToString() == model.TenantId);
            if (!tenantExists)
                return new GenericResponse
                {
                    ResponseCode = "400",
                    DevResponseMessage = "Tenant not found",
                    ResponseMessage = "Something went wrong. Please try again later."
                };

            // Check if number already exists
            var existingNumber = await _context.PhoneNumbers
                .FirstOrDefaultAsync(p => p.Number == model.Number);

            if (existingNumber != null)
            {
                return new GenericResponse
                {
                    ResponseCode = "409",
                    ResponseMessage = "Phone number is already registered"
                };
            }

            // Register the phone number
            var phoneNumber = new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
            {
                Number = model.Number,
                IsActive = true,
                IsRegistered = true,
                CountryCode = "US",
                NumberType = DeterminePhoneNumberType(model.Number),
                Balance = 0
            };

            // Add capabilities based on the model
            foreach (var capability in model.Capabilities)
            {
                phoneNumber.Capabilities.Add(new PhoneNumberCapability
                {
                    CapabilityType = (CapabilityType)capability
                });
            }

            var phoneNoToCompanyMapping = new PhoneNoToCompanyMapping
            {
                PhoneNumber = phoneNumber.Number,
                TenantId = Guid.Parse(model.TenantId)
            };

            _publicContext.PhoneNoToCompanyMappings.Add(phoneNoToCompanyMapping);
            _context.PhoneNumbers.Add(phoneNumber);

            await _context.SaveChangesAsync();
            await _publicContext.SaveChangesAsync();

            _logger.Information($"Phone number {phoneNumber.Number} registered successfully");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Phone number registered successfully",
                Data = new
                {
                    PhoneNumberId = phoneNumber.Id,
                    Number = phoneNumber.Number,
                    NumberType = phoneNumber.NumberType.ToString()
                }
            };
        }
        #endregion

        #region Assign Phone Number To Users
        /// <summary>
        /// Assigns users to a phone number for call routing and access control
        /// </summary>
        /// <param name="model">Assignment request containing phone number ID and list of user IDs</param>
        /// <returns>Response indicating success of the assignment operation</returns>
        public async Task<GenericResponse> AssignUsers(AssignPhoneNumberUsersDto model)
        {
            _logger.Information($"Assigning users to phone number {model.PhoneNumberId}");

            // Check if phone number exists
            var phoneNumber = await _context.PhoneNumbers
                .FirstOrDefaultAsync(p => p.Id == model.PhoneNumberId);

            if (phoneNumber == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Phone number not found"
                };
            }

            // Clear existing assignments
            var existingAssignments = await _context.PhoneNumberAssignments
                .Where(a => a.PhoneNumberId == model.PhoneNumberId)
                .ToListAsync();

            _context.PhoneNumberAssignments.RemoveRange(existingAssignments);

            // Add new assignments
            foreach (var userId in model.UserIds)
            {
                var assignment = new PhoneNumberAssignment
                {
                    PhoneNumberId = model.PhoneNumberId,
                    UserId = userId,
                    AssignedBy = model.CreatedBy
                };

                _context.PhoneNumberAssignments.Add(assignment);
            }

            await _context.SaveChangesAsync();

            _logger.Information($"Users assigned to phone number {model.PhoneNumberId} successfully");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Users assigned successfully",
                Data = new
                {
                    PhoneNumberId = model.PhoneNumberId,
                    AssignedUserCount = model.UserIds.Count()
                }
            };
        }
        #endregion

        #region Get Phone Number Details By Id

        /// <summary>
        /// Retrieves detailed information about a specific phone number including assignments and capabilities
        /// </summary>
        /// <param name="id">The unique identifier of the phone number</param>
        /// <returns>Response with comprehensive phone number details</returns>
        public async Task<GenericResponse> GetPhoneNumber(Guid id)
        {
            _logger.Information($"Getting phone number {id}");

            var phoneNumber = await _context.PhoneNumbers
                .Include(p => p.Assignments)
                .Include(p => p.Capabilities)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (phoneNumber == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Phone number not found"
                };
            }

            // Get user assigned users
            var assignments = await _context.PhoneNumberAssignments
                .Where(a => a.PhoneNumberId == id)
                .Select(a => new
                {
                    a.UserId,
                    a.AssignedAt,
                    a.AssignedBy,
                    UserProfile = _context.UserProfiles
                        .Where(up => up.UserId == a.UserId)
                        .Select(up => new
                        {
                            up.FirstName,
                            up.LastName
                        })
                        .FirstOrDefault()
                })
                .ToListAsync();

            var phoneNumberDto = new
            {
                phoneNumber.Id,
                phoneNumber.Number,
                phoneNumber.IsActive,
                phoneNumber.IsRegistered,
                phoneNumber.Balance,
                phoneNumber.CountryCode,
                NumberType = phoneNumber.NumberType.ToString(),
                phoneNumber.CreatedAt,
                phoneNumber.UpdatedAt,
                AssignedUsers = assignments,
                Capabilities = phoneNumber.Capabilities.Select(c => new
                {
                    c.CapabilityType
                })
            };

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Phone number retrieved successfully",
                Data = phoneNumberDto
            };
        }
        #endregion

        #region Get All Phone Numbers For Tenant
        /// <summary>
        /// Retrieves all active phone numbers for a specific tenant
        /// </summary>
        /// <param name="tenantId">The tenant ID to filter phone numbers</param>
        /// <returns>Response with list of phone numbers and their basic information</returns>
        public async Task<GenericResponse> GetPhoneNumbers(string tenantId)
        {
            try
            {
                _logger.Information($"Getting phone numbers for tenant {tenantId}");

                // Get all phone numbers (in a multi-tenant system, you'd filter by tenant)
                var phoneNumbers = await _context.PhoneNumbers
                    .Include(p => p.Assignments)
                    .Where(p => p.IsActive)
                    .Select(p => new
                    {
                        p.Id,
                        p.Number,
                        p.IsActive,
                        p.IsRegistered,
                        p.Balance,
                        p.CountryCode,
                        NumberType = p.NumberType.ToString(),
                        p.CreatedAt,
                        AssignedUserCount = p.Assignments.Count(),
                        AssignedUsers = p.Assignments.Take(3).Select(a => new
                        {
                            a.UserId,
                            UserName = _context.UserProfiles
                                .Where(up => up.UserId == a.UserId)
                                .Select(up => up.FirstName + " " + up.LastName)
                                .FirstOrDefault() ?? "Unknown"
                        })
                    })
                    .ToListAsync();

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Phone numbers retrieved successfully",
                    Data = phoneNumbers
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting phone numbers");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to get phone numbers: " + ex.Message
                };
            }
        }
        #endregion

        #region Purge Phone Number Data
        /// <summary>
        /// Purges old call data and records for a specific phone number for compliance and storage management
        /// </summary>
        /// <param name="model">Purge request containing phone number ID and reason</param>
        /// <returns>Response with count of purged records</returns>
        public async Task<GenericResponse> PurgePhoneNumberData(PurgePhoneNumberDataDto model)
        {
            _logger.Information($"Purging data for phone number {model.PhoneNumberId}");

            // Check if phone number exists
            var phoneNumber = await _context.PhoneNumbers
                .FirstOrDefaultAsync(p => p.Id == model.PhoneNumberId);

            if (phoneNumber == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Phone number not found"
                };
            }

            var purgedCount = 0;

            // Purge call records older than 30 days
            var cutoffDate = DateTime.UtcNow.AddDays(-30);
            var callsToDelete = await _context.CallRecords
                .Where(c => c.PhoneNumberId == model.PhoneNumberId &&
                           c.StartTime < cutoffDate)
                .ToListAsync();

            _context.CallRecords.RemoveRange(callsToDelete);
            purgedCount += callsToDelete.Count;

            await _context.SaveChangesAsync();

            _logger.Information($"Purged {purgedCount} call records for phone number {model.PhoneNumberId}. Reason: {model.Reason}");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Data purged successfully",
                Data = new
                {
                    PhoneNumberId = model.PhoneNumberId,
                    PurgedRecordCount = purgedCount,
                    Reason = model.Reason
                }
            };
        }
        #endregion

        #region Add Call Transaction
        /// <summary>
        /// Add Call Transaction
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> AddCallTransaction(CallTransactionDto model)
        {
            // Add validations, If action is not NoAction, all the fields for meeting are required
            if (model.Action != Actions.NoAction)
            {
                StringBuilder validationErrors = new StringBuilder();
                if (string.IsNullOrEmpty(model.CustomerEmail))
                    validationErrors.AppendLine("Customer Email ID is required.");
                if (model.MeetingDate == null)
                    validationErrors.AppendLine("Meeting Date is required.");
                if (model.MeetingTime == null)
                    validationErrors.AppendLine("Meeting Time is required.");
                if (model.DurationInMinutes == null || model.DurationInMinutes <= 0)
                    validationErrors.AppendLine("Duration in minutes must be greater than 0.");
                if (model.MeetingTime == null)
                    validationErrors.AppendLine("Meeting Time is required.");

                if (validationErrors.Length > 0)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Validation errors occurred",
                        DevResponseMessage = validationErrors.ToString()
                    };
                }
            }

            // Get tenant subdomain
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id.ToString() == model.CompanyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(subdomain))
                return new GenericResponse { ResponseCode = "404", ResponseMessage = "Company not found" };

            // Build the context using the subdomain
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

            // Get phone number Id of the To Nnumber
            var phoneNumberId = await context.PhoneNumbers
                .Where(p => p.Number == model.ToNumber)
                .Select(p => p.Id)
                .FirstOrDefaultAsync();
            if (phoneNumberId == Guid.Empty)
                return new GenericResponse { ResponseCode = "404", ResponseMessage = "Phone number not found" };

            _logger.Information($"Adding call transaction for phone number {phoneNumberId}");

            // Create the call transaction record using automapper
            var callTransaction = _mapper.Map<CallTranscription>(model);

            context.CallTranscriptions.Add(callTransaction);
            await _context.SaveChangesAsync();

            _logger.Information($"Call transaction added successfully for phone number {phoneNumberId}");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Call transaction added successfully",
            };
        }
        #endregion

        #region Add Or Update Company Summarized Info
        public async Task<GenericResponse> AddOrUpdateCompanySummarizedInfo(CompanySummarizedInfoDto model)
        {
            _logger.Information($"Adding or updating company summarized info for {model.CompanyId}");

            // Get tenant subdomain
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == model.CompanyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(subdomain))
                return new GenericResponse { ResponseCode = "404", ResponseMessage = "Company not found" };

            // Check if summarized info already exists
            var existingInfo = await _publicContext.CompanySummarizedInfos
                .FirstOrDefaultAsync(c => c.TenantId == model.CompanyId);

            if (existingInfo != null)
            {
                // Update existing record
                existingInfo.SummarizedText = model.SummarizedText;
                existingInfo.UpdatedOn = DateTime.UtcNow;
                _publicContext.CompanySummarizedInfos.Update(existingInfo);
            }
            else
            {
                // Create new record
                var newInfo = new CompanySummarizedInfo
                {
                    TenantId = model.CompanyId,
                    SummarizedText = model.SummarizedText,
                    UpdatedOn = DateTime.UtcNow
                };
                _publicContext.CompanySummarizedInfos.Add(newInfo);
            }

            await _publicContext.SaveChangesAsync();

            _logger.Information($"Company summarized info added/updated successfully for {model.CompanyId}");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Company summarized info added/updated successfully"
            };
        }
        #endregion

        #region Get Company Summarized Info
        public async Task<GenericResponse> GetCompanySummarizedInfo(Guid companyId)
        {
            _logger.Information($"Getting company summarized info for {companyId}");

            // Get tenant subdomain
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == companyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(subdomain))
                return new GenericResponse { ResponseCode = "404", ResponseMessage = "Company not found" };

            // Fetch summarized info
            var summarizedInfo = await _publicContext.CompanySummarizedInfos
                .Where(c => c.TenantId == companyId)
                .FirstOrDefaultAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Company summarized info retrieved successfully",
                Data = summarizedInfo
            };
        }
        #endregion

        #region Get company details a phone number is attached to   
        public async Task<GenericResponse> GetCompanyByPhoneNumber(string phoneNumber)
        {
            // Add + to the phone number
            if (!phoneNumber.StartsWith("+"))
                phoneNumber = "+" + phoneNumber;

            // First get all subdomains
            _logger.Information($"Getting company ID for phone number {phoneNumber}");
            var subdomains = await _publicContext.Tenants
                .Select(s => s.Subdomain)
                .ToListAsync();

            // 2. Loop throigh the subdomains and seasrch for the phone number
            foreach (var subdomain in subdomains)
            {
                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
                var numberExist = await context.PhoneNumbers
                    .Where(p => p.Number == phoneNumber)
                    .FirstOrDefaultAsync();

                if (numberExist != null)
                {
                    _logger.Information($"Found company for phone number {phoneNumber} in subdomain {subdomain}");

                    // Get assigned user IDs
                    var assignedUserIds = await context.PhoneNumberAssignments
                        .Where(a => a.PhoneNumberId == numberExist.Id)
                        .Select(a => a.UserId)
                        .ToListAsync();

                    // Fetch user profiles
                    var userProfiles = await context.UserProfiles
                        .Where(up => assignedUserIds.Contains(up.UserId))
                        .Select(up => new UserDto
                        {
                            Id = up.UserId,
                            Designation = up.Designation,
                            FirstName = up.FirstName,
                            LastName = up.LastName,
                            Email = up.Email,
                            PhoneNumber = up.PhoneNumber,
                            ProfileUrl = up.ProfilePictureUrl,
                            MiddleName = up.MiddleName
                        })
                        .ToListAsync();

                    // Create AssignedUsers manually
                    var assignedUsers = assignedUserIds.Select(u => new
                    {
                        UserId = u,
                        UserProfile = userProfiles.FirstOrDefault(p => p.Id == u)
                    });

                    // Get the company details
                    var company = await _publicContext.Tenants
                        .Where(c => c.Subdomain == subdomain)
                        .FirstOrDefaultAsync();

                    var companyDetails = new
                    {
                        CompanyDetails = company,
                        ComapnyWebsite = "https://www." + company.VerifiedEmailDomain,
                        PhoneNumber = phoneNumber,
                        AssignedUsers = assignedUsers
                    };

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Company found",
                        Data = companyDetails
                    };
                }
            }

            _logger.Warning($"No company found for phone number {phoneNumber}");
            return new GenericResponse
            {
                ResponseCode = "404",
                ResponseMessage = "Company not found for the provided phone number"
            };
        }
        #endregion

        #region Add Or Update Company Files Summary
        public async Task<GenericResponse> AddOrUpdateCompanyFilesSummary(CompanyFileSummaryDto model)
        {
            _logger.Information($"Adding or updating company files summary for {model.CompanyId}");

            // Get tenant subdomain
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == model.CompanyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(subdomain))
                return new GenericResponse { ResponseCode = "404", ResponseMessage = "Company not found" };

            // Check if summary already exists
            var existingSummary = await _publicContext.CompanyFilesSummaries
                .FirstOrDefaultAsync(c => c.TenantId == model.CompanyId);

            if (existingSummary != null)
            {
                // Update existing record
                existingSummary.SummarizedText = model.SummarizedText;
                existingSummary.UpdatedOn = DateTime.UtcNow;
                _publicContext.CompanyFilesSummaries.Update(existingSummary);
            }
            else
            {
                // Create new record
                var newSummary = new CompanyFilesSummary
                {
                    TenantId = model.CompanyId,
                    SummarizedText = model.SummarizedText,
                    UpdatedOn = DateTime.UtcNow
                };
                _publicContext.CompanyFilesSummaries.Add(newSummary);
            }

            var dbResult = await _publicContext.SaveChangesAsync();
            _logger.Information($"Company files summary added/updated successfully for {model.CompanyId}");

            if (dbResult <= 0)
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to add/update company files summary"
                };

            // Call wikiservice to update summarized info - IsAIProcessed field
            var response = await _wikiFileService.UpdateFilesAsProcessedAsync(model.fileIds);
            if (response.ResponseCode != "200")
            {
                _logger.Error($"Failed to update files as processed: {response.ResponseMessage}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to update files as processed: " + response.ResponseMessage
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Company files summary added/updated successfully"
            };
        }
        #endregion

        #region Get Company Files Summary
        public async Task<GenericResponse> GetCompanyFilesSummary(Guid companyId)
        {
            _logger.Information($"Getting company files summary for {companyId}");

            // Get tenant subdomain
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == companyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(subdomain))
                return new GenericResponse { ResponseCode = "404", ResponseMessage = "Company not found" };

            // Fetch summarized info
            var summarizedInfo = await _publicContext.CompanyFilesSummaries
                .Where(c => c.TenantId == companyId)
                .FirstOrDefaultAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Company files summary retrieved successfully",
                Data = summarizedInfo
            };
        }
        #endregion

        #region Call Management - Twilio PSTN

        // NOTE: InitiateCall method removed as it's redundant with TelephonyService.InitiateCallAsync
        // The new TelephonyService provides conference-based calling with better web participant support

        /// <summary>
        /// Handles incoming calls from Twilio webhook and processes them for WebRTC routing
        /// Enhanced with WebRTC-first routing and fallback options
        /// </summary>
        /// <param name="fromNumber">The caller's phone number</param>
        /// <param name="toNumber">The destination phone number</param>
        /// <param name="subdomain"></param>
        /// <param name="callSid"></param>
        /// <returns>Response indicating processing status</returns>
        public async Task<GenericResponse> HandleInboundCall(string fromNumber, string toNumber, string subdomain, string callSid)
        {
            _logger.Information($"Handling inbound call from {fromNumber} to {toNumber} for {subdomain}");

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

            // Use the To number to get the phone number id
            var phoneNumber = await context.PhoneNumbers
                .Where(p => p.Number == toNumber && p.IsActive)
                .FirstOrDefaultAsync();
            if (phoneNumber == null)
            {
                _logger.Warning($"Phone number {toNumber} not found or inactive for subdomain {subdomain}");
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Phone number not found or inactive"
                };
            }

            // Create call record for inbound call
            var callRecord = new CallRecord
            {
                Id = Guid.NewGuid(),
                PhoneNumberId = phoneNumber.Id, // This should be looked up based on toNumber
                FromNumber = fromNumber,
                ToNumber = toNumber,
                TwilioCallSid = callSid,
                Direction = CallDirection.Inbound,
                Status = CallStatus.Ringing,
                StartTime = DateTime.UtcNow
            };

            context.CallRecords.Add(callRecord);
            await context.SaveChangesAsync();

            // Check for available WebRTC agents first, prioritizing assigned agents
            var availableWebRTCAgent = await GetAvailableWebRTCAgentForNumber(toNumber, subdomain);

            if (!string.IsNullOrEmpty(availableWebRTCAgent))
            {
                _logger.Information($"Found available WebRTC agent {availableWebRTCAgent} for inbound call from {fromNumber} to {toNumber}");

                // Send WebRTC-specific notification to the agent
                await _callHubContext.Clients.User(availableWebRTCAgent)
                    .SendAsync("IncomingWebRTCCall", new
                    {
                        CallId = callRecord.Id,
                        CallSid = callSid,
                        From = fromNumber,
                        To = toNumber,
                        AgentId = availableWebRTCAgent,
                        CallType = "WebRTC",
                        IsAssignedAgent = await IsAgentAssignedToNumber(availableWebRTCAgent, toNumber, subdomain),
                        Timestamp = DateTime.UtcNow
                    });
            }

            // Find other users who should be notified (fallback agents)
            var usersToNotify = await context.UserProfiles
                .Where(u => !u.IsDeleted && !u.IsSuspended)
                .Select(u => u.UserId.ToString())
                .ToListAsync();

            // Send general inbound call notifications to all users
            foreach (var userId in usersToNotify.Where(u => u != availableWebRTCAgent))
            {
                await _callHubContext.Clients.User(userId)
                    .SendAsync("IncomingCall", new
                    {
                        CallId = callRecord.Id,
                        From = fromNumber,
                        To = toNumber,
                        HasWebRTCAgent = !string.IsNullOrEmpty(availableWebRTCAgent),
                        PreferredAgent = availableWebRTCAgent,
                        Timestamp = DateTime.UtcNow
                    });
            }

            _logger.Information($"Inbound call processed. Call ID: {callRecord.Id}, WebRTC Agent: {availableWebRTCAgent ?? "None"}");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Inbound call processed successfully",
                Data = new
                {
                    CallId = callRecord.Id,
                    Status = "ringing",
                    RoutingTo = !string.IsNullOrEmpty(availableWebRTCAgent) ? "WebRTC" : "Queue",
                    AgentId = availableWebRTCAgent
                }
            };
        }

        /// <summary>
        /// Generates TwiML response for handling inbound calls with WebRTC and queue options
        /// </summary>
        /// <param name="callSid">Twilio call SID</param>
        /// <param name="fromNumber">Caller's phone number</param>
        /// <param name="toNumber">Destination phone number</param>
        /// <param name="subdomain"></param>
        /// <returns>TwiML XML string for call handling</returns>
        public async Task<string> GenerateInboundCallTwiML(string callSid, string fromNumber, string toNumber, string subdomain)
        {
            try
            {
                // First, try to route to WebRTC agents, prioritizing assigned agents
                var availableWebRTCAgent = await GetAvailableWebRTCAgentForNumber(toNumber, subdomain);
                var baseUrl = Utility.Constants.BACKEND_BASE_URL;

                // Route to WebRTC browser client using TwiML Voice SDK
                var response = new VoiceResponse();
                response.Say("Connecting you to an agent.", voice: Say.VoiceEnum.Alice);

                if (!string.IsNullOrEmpty(availableWebRTCAgent))
                {
                    _logger.Information("Routing call {CallSid} to WebRTC agent {Agent}", callSid, availableWebRTCAgent);

                    await Task.Delay(5000);

                    var dial = new Dial(timeout: 30,
                        action: new Uri($"/api/PhoneNumber/webhook/handle-inbound-call-fallback?CallSid={callSid}&From={fromNumber}&To={toNumber}&subdomain={subdomain}", UriKind.Relative));
                    dial.Client(availableWebRTCAgent);
                    response.Append(dial);

                    return response.ToString();
                }

                response.Say($"The agent is not available. Please hold while we find another agent.", voice: Say.VoiceEnum.Alice);
                response.Redirect(new Uri($"/api/PhoneNumber/webhook/handle-inbound-call-fallback?CallSid={callSid}&From={fromNumber}&To={toNumber}&subdomain={subdomain}", UriKind.Relative));

                return response.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating TwiML response");

                // Fallback TwiML using Voice SDK
                var errorResponse = new VoiceResponse();
                errorResponse.Say("We're sorry, but we're unable to take your call right now. Please try again later.", voice: Say.VoiceEnum.Alice);
                errorResponse.Hangup();

                return errorResponse.ToString();
            }
        }

        /// <summary>
        /// Generates TwiML response for handling inbound calls with WebRTC and queue options
        /// </summary>
        /// <param name="callSid">Twilio call SID</param>
        /// <param name="fromNumber">Caller's phone number</param>
        /// <param name="toNumber">Destination phone number</param>
        /// <param name="subdomain"></param>
        /// <returns>TwiML XML string for call handling</returns>
        public async Task<string> GenerateInboundCallTallBackTwiML(string callSid, string fromNumber, string toNumber, string subdomain)
        {
            try
            {
                var baseUrl = Utility.Constants.BACKEND_BASE_URL;
                var queueCalls = _configuration.GetValue<bool>("Twilio:QueueCalls");

                var voicemailUrl = $"{baseUrl}/PhoneNumber/webhook/voicemail?callSid={callSid}";
                var voicemailUrlNonQueue = $"{baseUrl}/PhoneNumber/webhook/voicemail-complete?callSid={callSid}";
                var holdMusicUrl = $"{baseUrl}/PhoneNumber/webhook/queue-wait-music";

                // Try to get an available WebRTC agent
                var availableWebRTCAgent = await GetAvailableWebRTCAgentForNumber(toNumber, subdomain);

                var response = new VoiceResponse();
                response.Say("Connecting you to an agent.", voice: Say.VoiceEnum.Alice);
                await Task.Delay(1000);

                if (!string.IsNullOrEmpty(availableWebRTCAgent))
                {
                    _logger.Information("Routing call {CallSid} to WebRTC agent {Agent}", callSid, availableWebRTCAgent);

                    var dial = new Dial(callerId: fromNumber, timeout: 30);
                    dial.Client(availableWebRTCAgent);
                    response.Append(dial);

                    response.Say("The agent is not available. Please hold while we find another agent.", voice: Say.VoiceEnum.Alice);
                    await Task.Delay(1000);

                    if (!queueCalls)
                    {
                        // Append voicemail instructions directly
                        response.Append(new Say("All agents are currently busy. Please leave a message after the beep.", voice: Say.VoiceEnum.Alice));
                        await Task.Delay(500);
                        response.Append(new Record(
                            action: new Uri(voicemailUrl),
                            method: Twilio.Http.HttpMethod.Post,
                            maxLength: 120,
                            playBeep: true
                        ));
                        response.Append(new Say("Thank you for your message. We will get back to you soon. Goodbye.", voice: Say.VoiceEnum.Alice));
                        response.Append(new Hangup());

                        return response.ToString();
                    }
                }

                // Append fallback enqueue to queue
                response.Say("The agent is also not available. Please hold while we find another agent.", voice: Say.VoiceEnum.Alice);
                response.Append(new Enqueue("support_queue")
                {
                    WaitUrl = new Uri(holdMusicUrl),
                    Action = new Uri(voicemailUrl),
                    WaitUrlMethod = Twilio.Http.HttpMethod.Get
                });

                return response.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating TwiML response");

                var errorResponse = new VoiceResponse();
                errorResponse.Append(new Say("We're sorry, but we're unable to take your call right now. Please try again later.", voice: Say.VoiceEnum.Alice));
                errorResponse.Append(new Hangup());

                return errorResponse.ToString();
            }
        }

        /// <summary>
        /// Generates TwiML response for inbound call fallback when agents are unavailable
        /// </summary>
        /// <param name="from">Caller's phone number</param>
        /// <param name="to">Destination phone number</param>
        /// <param name="callSid">Twilio call SID</param>
        /// <param name="requestScheme">HTTP scheme (http/https)</param>
        /// <param name="requestHost">Host name</param>
        /// <returns>TwiML XML string for voicemail recording</returns>
        public string GetInboundCallFallback(string from, string to, string callSid, string requestScheme, string requestHost)
        {
            try
            {
                var response = new VoiceResponse();
                response.Say("All our agents are busy. Please leave a message after the beep.", voice: Say.VoiceEnum.Alice);

                // Record voicemail with transcription
                var voicemailUrl = $"{requestScheme}://{requestHost}/api/PhoneNumber/webhook/voicemail-complete?callSid={callSid}";
                response.Record(
                    action: new Uri(voicemailUrl),
                    method: Twilio.Http.HttpMethod.Post,
                    maxLength: 60,
                    transcribe: true,
                    playBeep: true
                );

                response.Say("Thank you for your message. We will get back to you soon. Goodbye.", voice: Say.VoiceEnum.Alice);
                response.Hangup();

                return response.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating fallback TwiML response");

                // Simple fallback
                var errorResponse = new VoiceResponse();
                errorResponse.Say("All agents are busy. Please try again later. Goodbye.", voice: Say.VoiceEnum.Alice);
                errorResponse.Hangup();

                return errorResponse.ToString();
            }
        }

        /// <summary>
        /// Generates TwiML response for queue wait music
        /// </summary>
        /// <returns>TwiML XML string for hold music</returns>
        public string GetQueueWaitMusic()
        {
            var response = new VoiceResponse();
            response.Play(new Uri("https://demo.twilio.com/docs/classic.mp3"), loop: 0);
            return response.ToString();
        }

        /// <summary>
        /// Generates TwiML response for voicemail completion
        /// </summary>
        /// <param name="callSid">Twilio call SID</param>
        /// <param name="recordingUrl">URL of the recorded voicemail</param>
        /// <param name="recordingSid">Twilio recording SID</param>
        /// <returns>TwiML XML string for voicemail completion</returns>
        public string GetVoicemailComplete(string callSid, string recordingUrl = null, string recordingSid = null)
        {
            try
            {
                // Here you would typically save the voicemail recording details to database
                // For now, just acknowledge receipt

                var response = new VoiceResponse();
                response.Say("Thank you for your message. We will get back to you soon. Goodbye.", voice: Say.VoiceEnum.Alice);
                response.Hangup();

                return response.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error processing voicemail completion");

                var errorResponse = new VoiceResponse();
                errorResponse.Say("Thank you. Goodbye.", voice: Say.VoiceEnum.Alice);
                errorResponse.Hangup();

                return errorResponse.ToString();
            }
        }

        public async Task<GenericResponse> AcceptInboundCall(AcceptInboundCallDto model)
        {
            _logger.Information($"User {model.UserId} accepting call {model.CallId}");

            // Validate input
            if (model.CallId == Guid.Empty || string.IsNullOrWhiteSpace(model.UserId))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid input data"
                };
            }

            // Find the call record
            var callRecord = await _context.CallRecords
                .FirstOrDefaultAsync(c => c.Id == model.CallId);

            if (callRecord == null)
            {
                _logger.Warning($"Call with ID {model.CallId} not found.");
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Call not found"
                };
            }

            // Prevent double-accepting the same call
            if (callRecord.Status == CallStatus.Answered || callRecord.Status == CallStatus.Completed)
            {
                return new GenericResponse
                {
                    ResponseCode = "409",
                    ResponseMessage = "Call already answered or completed"
                };
            }

            // Update call record
            callRecord.Status = CallStatus.Answered;
            callRecord.AnsweredBy = model.UserId;
            callRecord.AnsweredAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // Generate access token and return it to the user
            var accessTokenRes = await GenerateWebRTCAccessToken(model.UserId, model.Platform);
            if (accessTokenRes.ResponseCode != "200")
                return accessTokenRes;

            var accessToken = accessTokenRes.Data as dynamic;
            if (string.IsNullOrEmpty(accessToken.token))
            {
                _logger.Error($"Failed to generate WebRTC access token for user {model.UserId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to generate WebRTC access token"
                };
            }

            // Notify connected clients (SignalR)
            await _hubContext.Clients.All.SendAsync("CallStatusUpdate", new
            {
                CallId = model.CallId,
                Status = CallStatus.Answered.ToString().ToLower(),
                AcceptedBy = model.UserId,
                Timestamp = DateTime.UtcNow
            });

            _logger.Information($"Call {model.CallId} successfully accepted by user {model.UserId}");

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Call accepted successfully",
                Data = new
                {
                    CallId = model.CallId,
                    Status = "answered",
                    AcceptedBy = model.UserId,
                    AccessToken = accessToken.token
                }
            };
        }

        #region Update Call Status
        public async Task<GenericResponse> UpdateCallStatus(string callId, string status, string subdomain)
        {
            try
            {
                _logger.Information($"Updating call status for {callId}: {status}");

                // Find call record by external call ID
                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
                var callRecord = await context.CallRecords
                    .FirstOrDefaultAsync(c => c.TwilioCallSid == callId);

                if (callRecord != null)
                {
                    // Map string status to enum
                    callRecord.Status = status switch
                    {
                        "initiated" => CallStatus.Initiated,
                        "ringing" => CallStatus.Ringing,
                        "in-progress" => CallStatus.Answered,
                        "completed" => CallStatus.Completed,
                        "failed" => CallStatus.Failed,
                        "busy" => CallStatus.Busy,
                        "no-answer" => CallStatus.NoAnswer,
                        "cancelled" => CallStatus.Cancelled,
                        _ => CallStatus.Failed
                    };

                    if (status == "completed")
                    {
                        // Get call duration, cost and recordingUrl
                        var callDetails = await _twilioService.GetCallDetailsAsync(callId);
                        if (callDetails != null)
                        {
                            callRecord.Cost = callDetails.Price != null ? decimal.Parse(callDetails.Price) : 0.00M;
                            callRecord.Duration = callDetails.Duration != null ? decimal.Parse(callDetails.Duration) : 0.00M;
                            callRecord.RecordingUrl = callDetails.RecordingsUri;

                            callRecord.EndTime = DateTime.UtcNow;
                        }
                    }

                    await context.SaveChangesAsync();

                    // Send real-time status update
                    await _hubContext.Clients.All
                        .SendAsync("CallStatusUpdate", new
                        {
                            CallId = callRecord.Id,
                            Status = status,
                            CallSid = callId,
                            Timestamp = DateTime.UtcNow
                        });

                    _logger.Information($"Call status updated successfully for {callId}");
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call status updated successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating call status");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to update call status: " + ex.Message
                };
            }
        }

        public async Task<GenericResponse> UpdateCallStatusByCallId(Guid callId, string status, string subdomain)
        {
            try
            {
                _logger.Information($"Updating call status for call record {callId}: {status}");

                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

                // Find call record by internal call ID
                var callRecord = await context.CallRecords
                    .FirstOrDefaultAsync(c => c.Id == callId);

                if (callRecord != null)
                {
                    // Map string status to enum
                    callRecord.Status = status switch
                    {
                        "initiated" => CallStatus.Initiated,
                        "ringing" => CallStatus.Ringing,
                        "in-progress" => CallStatus.Answered,
                        "completed" => CallStatus.Completed,
                        "failed" => CallStatus.Failed,
                        "busy" => CallStatus.Busy,
                        "no-answer" => CallStatus.NoAnswer,
                        "cancelled" => CallStatus.Cancelled,
                        _ => CallStatus.Failed
                    };

                    if (status == "completed")
                    {
                        callRecord.EndTime = DateTime.UtcNow;
                    }

                    await context.SaveChangesAsync();

                    // Send real-time status update
                    await _hubContext.Clients.All
                        .SendAsync("CallStatusUpdate", new
                        {
                            CallId = callRecord.Id,
                            Status = status,
                            CallSid = callRecord.TwilioCallSid,
                            Timestamp = DateTime.UtcNow
                        });

                    _logger.Information($"Call status updated successfully for call record {callId}");
                }
                else
                {
                    _logger.Warning($"Call record with ID {callId} not found");
                    return new GenericResponse
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Call record not found"
                    };
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call status updated successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating call status for call record {CallId}", callId);
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to update call status: " + ex.Message
                };
            }
        }
        #endregion

        #region Get Call History
        public async Task<GenericResponse> GetCallHistory(Guid phoneNumberId, DateTime? startDate, DateTime? endDate)
        {
            _logger.Information($"Getting call history for phone number {phoneNumberId}"); var query = _context.CallRecords
                   .Where(c => c.PhoneNumberId == phoneNumberId);

            if (startDate.HasValue)
            {
                query = query.Where(c => c.StartTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(c => c.StartTime <= endDate.Value);
            }

            var callHistory = await query
                .OrderByDescending(c => c.StartTime)
                .Select(c => new
                {
                    c.Id,
                    c.FromNumber,
                    c.ToNumber,
                    c.Direction,
                    c.Status,
                    c.StartTime,
                    c.EndTime,
                    c.Duration,
                    c.Cost,
                    c.TwilioCallSid,
                    c.RecordingUrl
                })
                .Take(100)
                .ToListAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Call history retrieved successfully",
                Data = callHistory
            };
        }
        #endregion

        #region Get Call Recording
        public async Task<GenericResponse> GetCallRecording(string callId)
        {
            _logger.Information($"Getting call recording for {callId}");

            // Find the call record
            var callRecord = await _context.CallRecords
                .FirstOrDefaultAsync(c => c.TwilioCallSid == callId || c.Id.ToString() == callId);

            if (callRecord == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Call not found"
                };
            }

            if (string.IsNullOrEmpty(callRecord.RecordingUrl))
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "No recording available for this call"
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Call recording retrieved successfully",
                Data = new
                {
                    CallId = callRecord.Id,
                    RecordingUrl = callRecord.RecordingUrl,
                    CallSid = callRecord.TwilioCallSid,
                    Duration = callRecord.Duration,
                    StartTime = callRecord.StartTime
                }
            };
        }
        #endregion

        #region Get Call Transcription
        public async Task<GenericResponse> GetCallTranscription(string callId)
        {
            _logger.Information($"Getting call transcription for {callId}");

            // Find the call record
            var callRecord = await _context.CallRecords
                .FirstOrDefaultAsync(c => c.TwilioCallSid == callId || c.Id.ToString() == callId);

            if (callRecord == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Call not found"
                };
            }

            // For now, return a placeholder response as transcription is not implemented
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Call transcription feature is not yet implemented",
                Data = new
                {
                    CallId = callRecord.Id,
                    CallSid = callRecord.TwilioCallSid,
                    Message = "Transcription service integration pending. This would typically integrate with services like AWS Transcribe or Google Speech-to-Text.",
                    StartTime = callRecord.StartTime,
                    Duration = callRecord.Duration
                }
            };
        }
        #endregion

        /// <summary>
        /// Helper method to extract capabilities from Twilio phone number resource
        /// </summary>
        /// <param name="number">Twilio phone number resource</param>
        /// <returns>List of capabilities as strings</returns>
        private List<string> GetCapabilities(object number)
        {
            var capabilities = new List<string>();

            try
            {
                var numberType = number.GetType();
                var capabilitiesProperty = numberType.GetProperty("Capabilities");

                if (capabilitiesProperty != null)
                {
                    var capabilitiesObject = capabilitiesProperty.GetValue(number);
                    if (capabilitiesObject != null)
                    {
                        var capType = capabilitiesObject.GetType();

                        // Check for voice capability
                        var voiceProperty = capType.GetProperty("Voice");
                        if (voiceProperty != null && (bool?)voiceProperty.GetValue(capabilitiesObject) == true)
                            capabilities.Add("voice");

                        // Check for SMS capability
                        var smsProperty = capType.GetProperty("Sms");
                        if (smsProperty != null && (bool?)smsProperty.GetValue(capabilitiesObject) == true)
                            capabilities.Add("sms");

                        // Check for MMS capability
                        var mmsProperty = capType.GetProperty("Mms");
                        if (mmsProperty != null && (bool?)mmsProperty.GetValue(capabilitiesObject) == true)
                            capabilities.Add("mms");

                        // Check for Fax capability
                        var faxProperty = capType.GetProperty("Fax");
                        if (faxProperty != null && (bool?)faxProperty.GetValue(capabilitiesObject) == true)
                            capabilities.Add("fax");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Failed to extract capabilities from phone number, using defaults");
                // Fallback to basic capabilities if structure is different
                capabilities.AddRange(new[] { "voice", "sms" });
            }

            // Ensure we always have at least basic capabilities
            if (capabilities.Count == 0)
            {
                capabilities.AddRange(new[] { "voice", "sms" });
            }

            return capabilities;
        }

        /// <summary>
        /// Gets call analytics and statistics for a specific phone number
        /// </summary>
        /// <param name="phoneNumberId">The ID of the phone number to analyze</param>
        /// <param name="startDate">Optional start date for analytics period</param>
        /// <param name="endDate">Optional end date for analytics period</param>
        /// <returns>Comprehensive call analytics including call counts, durations, costs, and breakdowns</returns>
        public async Task<GenericResponse> GetCallAnalytics(Guid phoneNumberId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                _logger.Information($"Getting call analytics for phone number {phoneNumberId}");

                var query = _context.CallRecords
                    .Where(c => c.PhoneNumberId == phoneNumberId);

                if (startDate.HasValue)
                {
                    query = query.Where(c => c.StartTime >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(c => c.StartTime <= endDate.Value);
                }

                var callData = await query.ToListAsync();

                var analytics = new
                {
                    PhoneNumberId = phoneNumberId,
                    Period = new
                    {
                        StartDate = startDate,
                        EndDate = endDate
                    },
                    TotalCalls = callData.Count,
                    InboundCalls = callData.Count(c => c.Direction == CallDirection.Inbound),
                    OutboundCalls = callData.Count(c => c.Direction == CallDirection.Outbound),
                    AnsweredCalls = callData.Count(c => c.Status == CallStatus.Completed),
                    MissedCalls = callData.Count(c => c.Status == CallStatus.NoAnswer || c.Status == CallStatus.Busy),
                    FailedCalls = callData.Count(c => c.Status == CallStatus.Failed),
                    TotalDuration = callData.Sum(c => c.Duration),
                    AverageDuration = callData.Any() ? callData.Average(c => c.Duration) : 0,
                    TotalCost = callData.Sum(c => c.Cost),
                    CallsByStatus = callData.GroupBy(c => c.Status)
                        .Select(g => new { Status = g.Key.ToString(), Count = g.Count() })
                        .ToList(),
                    CallsByDirection = callData.GroupBy(c => c.Direction)
                        .Select(g => new { Direction = g.Key.ToString(), Count = g.Count() })
                        .ToList(),
                    CallsByHour = callData.GroupBy(c => c.StartTime.Hour)
                        .Select(g => new { Hour = g.Key, Count = g.Count() })
                        .OrderBy(x => x.Hour)
                        .ToList(),
                    CallsByDay = callData.GroupBy(c => c.StartTime.Date)
                        .Select(g => new { Date = g.Key, Count = g.Count() })
                        .OrderBy(x => x.Date)
                        .ToList()
                };

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call analytics retrieved successfully",
                    Data = analytics
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting call analytics"); return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to get call analytics: " + ex.Message
                };
            }
        }

        #endregion

        #region WebRTC Methods

        /// <summary>
        /// Generates Twilio Voice access token for WebRTC browser calling
        /// </summary>
        /// <param name="userId">Unique identifier for the user</param>
        /// <param name="displayName">Optional display name for the user</param>
        /// <returns>Response containing the JWT access token</returns>
        public Task<GenericResponse> GenerateWebRTCAccessToken(string userId, Platform platform, string displayName = null)
        {
            try
            {
                var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? _configuration["Twilio:AccountSID"];
                var apiKey = _configuration["Twilio:ApiKey"];
                var apiSecret = _configuration["Twilio:ApiSecret"];
                var twimlAppSid = _configuration["Twilio:TwiMLAppSid"];

                if (string.IsNullOrEmpty(accountSid) || string.IsNullOrEmpty(apiKey) ||
                    string.IsNullOrEmpty(apiSecret) || string.IsNullOrEmpty(twimlAppSid))
                {
                    _logger.Error("Twilio WebRTC configuration is incomplete");
                    return Task.FromResult(new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Twilio WebRTC configuration is incomplete"
                    });
                }

                var token = GenerateTwilioAccessToken(accountSid, apiKey, apiSecret, twimlAppSid, userId, platform);

                _logger.Information("Generated WebRTC access token for user {UserId}", userId);

                return Task.FromResult(new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "WebRTC access token generated successfully",
                    Data = new { token = token, userId = userId }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating WebRTC access token for user {UserId}", userId);
                return Task.FromResult(new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to generate WebRTC access token: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Generates TwiML for the click-to-call flow
        /// When customer answers, this TwiML dials the WebRTC client (browser)
        /// </summary>
        /// <param name="toNumber">Phone number to dial (not used in this flow)</param>
        /// <param name="callSid">Twilio call SID</param>
        /// <returns>TwiML XML string</returns>
        public Task<string> GenerateOutboundWebRTCTwiML(string toNumber, string callSid)
        {
            // Default implementation - calls the user-specific version with default user
            return GenerateOutboundWebRTCTwiMLForUser("default-user", callSid);
        }

        /// <summary>
        /// Generates TwiML for the click-to-call flow with specific user
        /// When customer answers, this TwiML dials the WebRTC client (browser)
        /// </summary>
        /// <param name="userId">User ID to dial via WebRTC</param>
        /// <param name="callSid">Twilio call SID</param>
        /// <returns>TwiML XML string</returns>

        public Task<string> GenerateOutboundWebRTCTwiMLForUser(string userId, string callSid)
        {
            try
            {
                _logger.Information("Generating click-to-call TwiML to connect customer to WebRTC user {UserId} with CallSid {CallSid}", userId, callSid);

                var response = new VoiceResponse();
                // response.Say("Please wait while we connect you to an agent.");

                var dial = new Dial(timeout: 30, record: Dial.RecordEnum.RecordFromAnswer);
                dial.Client(userId);

                response.Append(dial);
                //response.Say("Sorry, the person your tryin to connect to is not available. Please try again later.");
                response.Hangup();

                _logger.Information("Generated click-to-call TwiML to dial WebRTC client {UserId}", userId);

                return Task.FromResult(response.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating click-to-call TwiML for user {UserId}, CallSid {CallSid}", userId, callSid);

                var errorResponse = new VoiceResponse();
                errorResponse.Say("Sorry, there was an error processing your call.");
                errorResponse.Hangup();

                return Task.FromResult(errorResponse.ToString());
            }
        }

        /// <summary>
        /// Generates TwiML for inbound calls to WebRTC browsers with enhanced routing
        /// Prioritizes WebRTC agents, then falls back to queue system
        /// </summary>
        /// <param name="fromNumber">Caller's phone number</param>
        /// <param name="toNumber">Destination phone number</param>
        /// <param name="callSid">Twilio call SID</param>
        /// <param name="subdomain"></param>
        /// k<returns>TwiML XML string</returns>
        public async Task<string> GenerateInboundWebRTCTwiML(string fromNumber, string toNumber, string callSid, string subdomain)
        {
            try
            {
                _logger.Information("Generating enhanced inbound WebRTC TwiML from {FromNumber} to {ToNumber} with CallSid {CallSid}", fromNumber, toNumber, callSid);

                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

                // Find the call record and update it with CallSid
                var callRecord = await context.CallRecords
                    .Where(c => c.FromNumber == fromNumber && c.ToNumber == toNumber && c.Direction == CallDirection.Inbound)
                    .OrderByDescending(c => c.StartTime)
                    .FirstOrDefaultAsync();

                if (callRecord != null && string.IsNullOrEmpty(callRecord.TwilioCallSid))
                {
                    callRecord.TwilioCallSid = callSid;
                    await context.SaveChangesAsync();
                }                // Try to find an available WebRTC agent, prioritizing assigned agents
                var availableAgent = await GetAvailableWebRTCAgentForNumber(toNumber, subdomain);

                string twiml;

                if (!string.IsNullOrEmpty(availableAgent))
                {
                    _logger.Information("Routing inbound call {CallSid} to WebRTC agent {Agent}", callSid, availableAgent);

                    // Mark agent as busy temporarily (will be released when call ends)
                    await MarkAgentBusy(availableAgent, callSid);

                    // Send real-time notification to the specific agent
                    await _hubContext.Clients.User(availableAgent)
                        .SendAsync("WebRTCCallIncoming", new
                        {
                            CallSid = callSid,
                            From = fromNumber,
                            To = toNumber,
                            AgentId = availableAgent,
                            Message = "Incoming call routing to your browser",
                            Timestamp = DateTime.UtcNow
                        });

                    // Route to specific WebRTC browser client with enhanced options
                    twiml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say voice=""alice"">Connecting you to an agent, please hold.</Say>
    <Dial timeout=""45"" callerId=""{fromNumber}"" action=""/api/webrtc/twiml/dial-status?agent={availableAgent}&amp;callSid={callSid}"" method=""POST"">
        <Client>{availableAgent}</Client>
    </Dial>
    <Say voice=""alice"">The agent did not answer. Let me try to find another available agent.</Say>
    <Redirect>/api/phonenumber/inbound-fallback?CallSid={callSid}</Redirect>
</Response>";
                }
                else
                {
                    _logger.Information("No WebRTC agents available for call {CallSid}, routing to fallback queue", callSid);

                    // Fallback to traditional queue system with enhanced options
                    var baseUrl = _configuration["AppSettings:BaseUrl"] ?? "https://your-domain.com";
                    var holdMusicUrl = $"{baseUrl}/api/PhoneNumber/queue-wait-music";
                    var voicemailUrl = $"{baseUrl}/api/PhoneNumber/voicemail?callSid={callSid}";

                    twiml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say voice=""alice"">All our agents are currently busy. Please hold and we'll connect you as soon as possible.</Say>
    <Enqueue waitUrl=""{holdMusicUrl}"" action=""{voicemailUrl}"" waitUrlMethod=""GET"" maxSize=""50"">support_queue</Enqueue>
</Response>";

                    // Notify supervisors that queue is receiving calls
                    await _hubContext.Clients.All
                        .SendAsync("CallQueueUpdate", new
                        {
                            Action = "CallQueued",
                            CallSid = callSid,
                            From = fromNumber,
                            QueueName = "support_queue",
                            Timestamp = DateTime.UtcNow
                        });
                }

                return twiml;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating inbound WebRTC TwiML from {FromNumber} with CallSid {CallSid}", fromNumber, callSid);

                return @"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say voice=""alice"">We apologize, but we're experiencing technical difficulties. Please try calling again in a few minutes.</Say>
    <Hangup/>
</Response>";
            }
        }

        /// <summary>
        /// Registers an agent for WebRTC call availability
        /// </summary>
        /// <param name="userId">Agent user ID</param>
        /// <param name="available">Availability status</param>
        /// <returns>Response indicating registration status</returns>
        public async Task<GenericResponse> RegisterAgentForWebRTCCalls(string userId, bool available)
        {
            try
            {
                var cacheKey = $"webrtc_agent_{userId}";

                if (available)
                {
                    // Cache agent availability for 1 hour
                    _cache.Set(cacheKey, new
                    {
                        UserId = userId,
                        RegisteredAt = DateTime.UtcNow,
                        Available = true
                    }, TimeSpan.FromHours(1));

                    _logger.Information("User {UserId} registered as available for WebRTC calls", userId);
                }
                else
                {
                    // Remove from cache
                    _cache.Remove(cacheKey);
                    _logger.Information("User {UserId} unregistered from WebRTC calls", userId);
                }

                // Notify other services about agent availability change
                await _hubContext.Clients.All.SendAsync("AgentAvailabilityChanged", new
                {
                    UserId = userId,
                    Available = available,
                    Timestamp = DateTime.UtcNow
                });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = $"Successfully {(available ? "registered for" : "unregistered from")} WebRTC calls",
                    Data = new { userId = userId, available = available }
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error registering user {UserId} for WebRTC calls", userId);
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to register for WebRTC calls: " + ex.Message
                };
            }
        }

        /// <summary>
        /// Initiates a WebRTC call flow: Backend calls customer, then connects to WebRTC browser
        /// Flow: Web App → Backend → Twilio calls Customer → Customer answers → Twilio calls WebRTC → Browser answers
        /// </summary>
        /// <param name="request">WebRTC call request containing user details and call parameters</param>
        /// <returns>Response with call details</returns>
        public async Task<GenericResponse> InitiateWebRTCCall(WebRTCCallRequestDto request)
        {
            try
            {
                _logger.Information("User {UserId} initiating click-to-call WebRTC flow to {ToNumber}", request.UserId, request.ToNumber);

                // Get the phone number record to use for outbound calls
                var phoneNumber = await _context.PhoneNumbers
                    .FirstOrDefaultAsync(p => p.Id == request.FromNumberId && p.IsActive);

                if (phoneNumber == null)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "404",
                        ResponseMessage = "Something went wrong, please try again later",
                        DevResponseMessage = "Phone number not found or inactive"
                    };
                }

                // Create call record for tracking
                var callRecord = new CallRecord
                {
                    Id = Guid.NewGuid(),
                    PhoneNumberId = request.FromNumberId,
                    FromNumber = phoneNumber.Number, // Identify as WebRTC call
                    ToNumber = request.ToNumber,
                    Direction = CallDirection.Outbound,
                    Status = CallStatus.Initiated,
                    StartTime = DateTime.UtcNow
                };

                _context.CallRecords.Add(callRecord);
                await _context.SaveChangesAsync();

                // Get status callback URL
                var statusCallback = $"{Utility.Constants.BACKEND_BASE_URL}/Telephony/webhook/twilio/call-status?callId={callRecord.Id}&subdomain={request.Subdomain}";

                // Step 1: Create Twilio call to the customer first
                // When customer answers, TwiML will dial the WebRTC client
                var call = await CallResource.CreateAsync(
                    from: new Twilio.Types.PhoneNumber(phoneNumber.Number),
                    to: new Twilio.Types.PhoneNumber(request.ToNumber),
                    url: new Uri($"{Utility.Constants.BACKEND_BASE_URL}/webrtc/webhook/twiml/handle-outbound?userId={request.UserId}&callId={callRecord.Id}&userDisplayName={Uri.EscapeDataString(request.UserDisplayName ?? request.UserId)}"),
                    statusCallback: new Uri(statusCallback),
                    statusCallbackEvent: new List<string> {
                        CallStatus.Initiated.ToString().ToLower(),
                        CallStatus.Ringing.ToString().ToLower(),
                        CallStatus.Answered.ToString().ToLower(),
                        CallStatus.Completed.ToString().ToLower(),
                        CallStatus.Failed.ToString().ToLower(),
                        CallStatus.NoAnswer.ToString().ToLower(),
                        CallStatus.Busy.ToString().ToLower(),
                        CallStatus.Cancelled.ToString().ToLower()
                    },
                    method: Twilio.Http.HttpMethod.Post,
                    statusCallbackMethod: Twilio.Http.HttpMethod.Post,
                    record: request.EnableRecording // Use the recording preference from request
                );

                // Update call record with Twilio call SID
                callRecord.TwilioCallSid = call.Sid;
                callRecord.Status = CallStatus.Initiated;
                await _context.SaveChangesAsync();

                // Generate WebRTC access token for the user to connect to the call
                string webRTCToken = null;
                try
                {
                    var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? _configuration["Twilio:AccountSID"];
                    var apiKey = _configuration["Twilio:ApiKey"];
                    var apiSecret = _configuration["Twilio:ApiSecret"];
                    var twimlAppSid = _configuration["Twilio:TwiMLAppSid"];

                    if (!string.IsNullOrEmpty(accountSid) && !string.IsNullOrEmpty(apiKey) &&
                        !string.IsNullOrEmpty(apiSecret) && !string.IsNullOrEmpty(twimlAppSid))
                    {
                        webRTCToken = GenerateTwilioAccessToken(accountSid, apiKey, apiSecret, twimlAppSid, request.UserId, request.Platform);
                        _logger.Information("Generated WebRTC access token for make-call user {UserId}", request.UserId);
                    }
                    else
                    {
                        _logger.Warning("Twilio WebRTC configuration incomplete, token not generated for user {UserId}", request.UserId);
                    }
                }
                catch (Exception tokenEx)
                {
                    _logger.Error(tokenEx, "Failed to generate WebRTC access token for user {UserId}, call will proceed without token", request.UserId);
                    // Don't fail the entire call if token generation fails
                }

                // Send real-time status update
                await _hubContext.Clients.User(request.UserId)
                    .SendAsync("CallStatusUpdate", new
                    {
                        CallId = callRecord.Id,
                        Status = CallStatus.Initiated.ToString(),
                        CallSid = callRecord.TwilioCallSid,
                        Timestamp = DateTime.UtcNow
                    });

                _logger.Information("Click-to-call initiated. Customer will be called first. Call SID: {CallSid}, Call ID: {CallId}", call.Sid, callRecord.Id);

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Click-to-call initiated successfully",
                    Data = new
                    {
                        CallId = callRecord.Id,
                        CallSid = call.Sid,
                        FromUser = request.UserId,
                        FromNumberId = request.FromNumberId,
                        ToNumber = request.ToNumber,
                        UserDisplayName = request.UserDisplayName,
                        MaxParticipants = request.MaxParticipants,
                        EnableRecording = request.EnableRecording,
                        EnableAI = request.EnableAI,
                        Status = call.Status.ToString(),
                        Flow = "Customer will be called first, then your browser will ring when they answer",
                        // WebRTC access token for frontend to initialize Twilio Device
                        WebRTCAccessToken = webRTCToken,
                        // Instructions for frontend
                        Instructions = new
                        {
                            Step1 = "Use the WebRTCAccessToken to initialize Twilio.Device",
                            Step2 = "Register the device with device.register()",
                            Step3 = "Listen for incoming calls and auto-accept when customer answers",
                            ClientIdentity = request.UserId
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error initiating click-to-call WebRTC flow from user {UserId} to {ToNumber}", request.UserId, request.ToNumber);
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to initiate click-to-call: " + ex.Message
                };
            }
        }

        /// <summary>
        /// Gets an available WebRTC agent for routing inbound calls, prioritizing assigned agents
        /// </summary>
        /// <param name="toNumber">The phone number being called (to find assigned agents)</param>
        /// <param name="subdomain"></param>
        /// <returns>Agent user ID if available, null otherwise</returns>
        public async Task<string> GetAvailableWebRTCAgentForNumber(string toNumber, string subdomain)
        {
            try
            {
                _logger.Information("Finding available WebRTC agent for number {ToNumber}", toNumber);

                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

                // 1. Get assigned agents for this phone number
                var phoneNumber = await context.PhoneNumbers
                    .Include(p => p.Assignments)
                    .FirstOrDefaultAsync(p => p.Number == toNumber && p.IsActive);

                List<string> assignedAgentIds = new List<string>();
                if (phoneNumber?.Assignments?.Any() == true)
                {
                    assignedAgentIds = phoneNumber.Assignments.Select(a => a.UserId).ToList();
                }

                // 2. Filter out busy agents
                var availableAssignedAgents = new List<string>();
                foreach (var agentId in assignedAgentIds)
                {
                    var cacheKey = $"webrtc_agent_{agentId}";
                    bool isBusy = false;
                    if (_cache.TryGetValue(cacheKey, out var agentData))
                    {
                        var agentInfo = agentData as dynamic;
                        isBusy = agentInfo != null && (agentInfo.GetType().GetProperty("IsBusy")?.GetValue(agentInfo) as bool? ?? false);
                    }
                    if (!isBusy)
                    {
                        availableAssignedAgents.Add(agentId);
                    }
                }

                // 3. Randomly select one available assigned agent
                if (availableAssignedAgents.Any())
                {
                    var random = new Random();
                    var selectedIndex = random.Next(availableAssignedAgents.Count);
                    var selectedAgentId = availableAssignedAgents[selectedIndex];

                    // 4. Mark agent as busy immediately
                    await MarkAgentBusy(selectedAgentId, null); // CallSid will be set when call is actually connected

                    _logger.Information("Selected assigned WebRTC agent: {AgentId} for number {Number}", selectedAgentId, toNumber);
                    return "de56874f-9080-4a54-88c4-265231fa7629";
                }

                _logger.Information("No assigned agents available for WebRTC, falling back to general pool");

                // 5. Fallback to general pool (all available agents)
                var generalAgentId = await GetAvailableWebRTCAgent(subdomain);
                if (!string.IsNullOrEmpty(generalAgentId))
                {
                    await MarkAgentBusy(generalAgentId, null);
                    _logger.Information("Selected general WebRTC agent: {AgentId}", generalAgentId);
                }

                return "de56874f-9080-4a54-88c4-265231fa7629"; // generalAgentId;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available WebRTC agent for number {ToNumber}", toNumber);
                // Fallback to general agent selection
                var fallbackAgentId = await GetAvailableWebRTCAgent(subdomain);
                if (!string.IsNullOrEmpty(fallbackAgentId))
                {
                    await MarkAgentBusy(fallbackAgentId, null);
                }
                return fallbackAgentId;
            }
        }

        /// <summary>
        /// Gets an available WebRTC agent for routing inbound calls
        /// </summary>
        /// <returns>Agent user ID if available, null otherwise</returns>
        public async Task<string> GetAvailableWebRTCAgent(string subdomain)
        {
            try
            {
                // Look through cached available agents
                var availableAgents = new List<string>();

                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

                // Also check for user-based agents (using actual user IDs)
                var activeUsers = await context.UserProfiles
                    .Where(u => !u.IsDeleted && !u.IsSuspended)
                    .Select(u => u.UserId)
                    .ToListAsync();

                foreach (var userId in activeUsers)
                {
                    var cacheKey = $"webrtc_agent_{userId}";
                    bool isBusy = false;
                    if (_cache.TryGetValue(cacheKey, out var agentData))
                    {
                        var agentInfo = agentData as dynamic;
                        isBusy = agentInfo != null && (agentInfo.GetType().GetProperty("IsBusy")?.GetValue(agentInfo) as bool? ?? false);
                    }
                    if (!isBusy)
                    {
                        availableAgents.Add(userId);
                    }
                }

                // Return the first available agent (you could implement load balancing here): TODO
                var selectedAgent = availableAgents.FirstOrDefault();

                if (selectedAgent != null)
                {
                    _logger.Information("Selected WebRTC agent: {AgentId}", selectedAgent);
                }
                else
                {
                    _logger.Information("No WebRTC agents available");
                }

                return selectedAgent;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available WebRTC agent");
                return null;
            }
        }

        public async Task<GenericResponse> GetAssignedPhoneNumbersForUser(string userId)
        {
            var assignedNumbers = await _context.PhoneNumberAssignments
                .Include(a => a.PhoneNumber)
                .Where(a => a.UserId == userId)
                .ToListAsync();

            if (assignedNumbers == null || !assignedNumbers.Any())
                new GenericResponse { ResponseCode = "404", ResponseMessage = "No phone numbers assigned to this user." };

            return new GenericResponse { ResponseCode = "200", ResponseMessage = "Assigned phone numbers retrieved successfully", Data = assignedNumbers };
        }

        #region Private Methods
        /// <summary>
        /// Generates Twilio access token for WebRTC Voice SDK with platform-specific push support
        /// </summary>
        /// <param name="accountSid">Twilio account SID</param>
        /// <param name="apiKey">Twilio API key</param>
        /// <param name="apiSecret">Twilio API secret</param>
        /// <param name="twimlAppSid">TwiML app SID</param>
        /// <param name="identity">User identity</param>
        /// <param name="platform">Platform type: 'ios', 'android', or null for auto-detect</param>
        /// <returns>JWT access token</returns>
        private string GenerateTwilioAccessToken(string accountSid, string apiKey, string apiSecret,
                string twimlAppSid, string identity, Platform platform)
        {
            try
            {
                var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(apiSecret));
                var signingCredentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

                var now = DateTimeOffset.UtcNow;
                var expiresAt = now.AddHours(24);

                // Header
                var header = new JwtHeader(signingCredentials);
                header["cty"] = "twilio-fpa;v=1"; // Required by Twilio

                // Get push credential SIDs from configuration
                var iosPushCredentialSid = _configuration["Twilio:VoipPushCredentialSid"];
                var androidPushCredentialSid = _configuration["Twilio:AndroidFcmPushCredentialSid"];

                // Determine which push credential to use based on platform
                string pushCredentialSid = null;
                if (platform == Platform.IOS && !string.IsNullOrEmpty(iosPushCredentialSid))
                {
                    pushCredentialSid = iosPushCredentialSid;
                }
                else if (platform == Platform.Andriod && !string.IsNullOrEmpty(androidPushCredentialSid))
                {
                    pushCredentialSid = androidPushCredentialSid;
                }

                // For multi-platform support, you might want to include both
                var voiceGrant = new Dictionary<string, object>
                {
                    { "outgoing", new Dictionary<string, object>
                        {
                            { "application_sid", twimlAppSid }
                        }
                    },
                    { "incoming", new Dictionary<string, object>
                        {
                            { "allow", true }
                        }
                    }
                };

                // Add push credentials if available
                if (!string.IsNullOrEmpty(pushCredentialSid))
                {
                    voiceGrant["push_credential_sid"] = pushCredentialSid;
                }

                // Payload
                var grants = new Dictionary<string, object>
                {
                    { "identity", identity },
                    { "voice", voiceGrant }
                };

                var payload = new JwtPayload
                {
                    { "jti", $"{apiKey}-{now.ToUnixTimeSeconds()}" },
                    { "iss", apiKey },
                    { "sub", accountSid },
                    { "exp", expiresAt.ToUnixTimeSeconds() },
                    { "nbf", now.ToUnixTimeSeconds() },
                    { "iat", now.ToUnixTimeSeconds() },
                    { "grants", grants } // ✅ nested object, not string
                };

                var jwt = new JwtSecurityToken(header, payload);
                var token = new JwtSecurityTokenHandler().WriteToken(jwt);

                return token;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating Twilio access token");
                throw new InvalidOperationException("Failed to generate token", ex);
            }
        }


        /// <summary>
        /// Marks an agent as busy with a specific call
        /// </summary>
        /// <param name="agentId">Agent ID to mark as busy</param>
        /// <param name="callSid">Call SID for tracking</param>
        /// <returns>Task</returns>
        private Task MarkAgentBusy(string agentId, string callSid)
        {
            try
            {
                var cacheKey = $"webrtc_agent_{agentId}";

                if (_cache.TryGetValue(cacheKey, out var agentData))
                {
                    // Update agent status to busy
                    var busyAgent = new
                    {
                        AgentId = agentId,
                        IsAvailable = false,
                        IsBusy = true,
                        CurrentCallSid = callSid,
                        LastSeen = DateTime.UtcNow,
                        BusySince = DateTime.UtcNow
                    };

                    // Store with shorter expiration for busy agents
                    _cache.Set(cacheKey, busyAgent, TimeSpan.FromMinutes(2));

                    _logger.Information("Marked agent {AgentId} as busy with call {CallSid}", agentId, callSid);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error marking agent {AgentId} as busy", agentId);
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Releases an agent from busy status
        /// </summary>
        /// <param name="agentId">Agent ID to release</param>
        /// <param name="callSid">Call SID for verification</param>
        /// <returns>Task</returns>
        private Task ReleaseAgentFromBusy(string agentId, string callSid)
        {
            try
            {
                var cacheKey = $"webrtc_agent_{agentId}";

                if (_cache.TryGetValue(cacheKey, out var agentData))
                {
                    // Update agent status back to available
                    var availableAgent = new
                    {
                        AgentId = agentId,
                        IsAvailable = true,
                        IsBusy = false,
                        CurrentCallSid = (string)null,
                        LastSeen = DateTime.UtcNow,
                        ReleasedAt = DateTime.UtcNow
                    };

                    // Store with normal expiration
                    _cache.Set(cacheKey, availableAgent, TimeSpan.FromMinutes(30));

                    _logger.Information("Released agent {AgentId} from busy status (call {CallSid})", agentId, callSid);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error releasing agent {AgentId} from busy status", agentId);
            }

            return Task.CompletedTask;
        }
        #endregion

        /// <summary>
        /// Handles dial status callbacks for WebRTC calls
        /// </summary>
        /// <param name="dialCallStatus">Status of the dial attempt</param>
        /// <param name="callSid">Call SID</param>
        /// <param name="agentId">Agent ID that was dialed</param>
        /// <returns>TwiML response</returns>
        public async Task<string> HandleWebRTCDialStatus(string dialCallStatus, string callSid, string agentId)
        {
            try
            {
                _logger.Information("WebRTC dial status {Status} for call {CallSid} and agent {AgentId}", dialCallStatus, callSid, agentId);

                // Release agent from busy status regardless of outcome
                await ReleaseAgentFromBusy(agentId, callSid);

                // Send status update via SignalR
                await _hubContext.Clients.User(agentId)
                    .SendAsync("WebRTCCallStatus", new
                    {
                        CallSid = callSid,
                        Status = dialCallStatus,
                        AgentId = agentId,
                        Timestamp = DateTime.UtcNow
                    });

                switch (dialCallStatus?.ToLower())
                {
                    case "answered":
                        _logger.Information("WebRTC call {CallSid} answered by agent {AgentId}", callSid, agentId);
                        // Call was successfully answered, no further action needed
                        return @"<?xml version=""1.0"" encoding=""UTF-8""?><Response></Response>";

                    case "no-answer":
                    case "failed":
                    case "busy":
                    case "canceled":
                        _logger.Information("WebRTC call {CallSid} not answered by agent {AgentId}, status: {Status}", callSid, agentId, dialCallStatus);

                        // Try to find another available agent
                        var nextAgent = await GetAvailableWebRTCAgent("");
                        if (!string.IsNullOrEmpty(nextAgent) && nextAgent != agentId)
                        {
                            await MarkAgentBusy(nextAgent, callSid);
                            return $@"<?xml version=""1.0"" encoding=""UTF-8""?>
                            <Response>
                                <Say voice=""alice"">Let me try another agent.</Say>
                                <Dial timeout=""45"" action=""/api/webrtc/twiml/dial-status?agent={nextAgent}&amp;callSid={callSid}"" method=""POST"">
                                                    <Client>{nextAgent}</Client>
                                                </Dial>
                                                <Redirect>/api/phonenumber/inbound-fallback?CallSid={callSid}</Redirect>
                            </Response>";
                        }
                        else
                        {
                            // No more agents available, redirect to fallback
                            return $@"<?xml version=""1.0"" encoding=""UTF-8""?>
                            <Response>
                                                <Redirect>/api/phonenumber/inbound-fallback?CallSid={callSid}</Redirect>
                            </Response>";
                        }

                    default:
                        _logger.Warning("Unknown dial status {Status} for call {CallSid}", dialCallStatus, callSid);
                        return @"<?xml version=""1.0"" encoding=""UTF-8""?>
                        <Response>
                            <Say>There was an error with the call. Please try again.</Say>
                            <Hangup/>
                        </Response>";
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error handling WebRTC dial status for call {CallSid}", callSid);
                return @"<?xml version=""1.0"" encoding=""UTF-8""?>
                <Response>
                    <Say>There was an error processing your call.</Say>
                    <Hangup/>
                </Response>";
            }
        }

        /// <summary>
        /// Checks if an agent is specifically assigned to a phone number
        /// </summary>
        /// <param name="agentId">Agent user ID</param>
        /// <param name="phoneNumber">Phone number to check</param>
        /// <returns>True if agent is assigned to the number, false otherwise</returns>
        private async Task<bool> IsAgentAssignedToNumber(string agentId, string phoneNumber, string subdomain)
        {
            try
            {
                await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

                var assignment = await context.PhoneNumbers
                    .Include(p => p.Assignments)
                    .Where(p => p.Number == phoneNumber && p.IsActive)
                    .SelectMany(p => p.Assignments)
                    .AnyAsync(a => a.UserId == agentId);

                return assignment;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error checking if agent {AgentId} is assigned to number {Number}", agentId, phoneNumber);
                return false;
            }
        }

        #endregion

        #region Determine Phone Number Type
        /// <summary>
        /// Determines the phone number type based on the phone number format
        /// </summary>
        /// <param name="phoneNumber">The phone number to analyze</param>
        /// <returns>The determined phone number type</returns>
        private PhoneNumberType DeterminePhoneNumberType(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return PhoneNumberType.Local; // Default to local if unknown

            // Remove any formatting and ensure it starts with +
            var cleanNumber = phoneNumber.Replace("+", "").Replace("-", "").Replace(" ", "").Replace("(", "").Replace(")", "");

            // Check for US/Canada toll-free numbers
            if (cleanNumber.StartsWith("1800") || cleanNumber.StartsWith("1888") ||
                cleanNumber.StartsWith("1877") || cleanNumber.StartsWith("1866") ||
                cleanNumber.StartsWith("1855") || cleanNumber.StartsWith("1844") ||
                cleanNumber.StartsWith("1833") || cleanNumber.StartsWith("1822"))
            {
                return PhoneNumberType.TollFree;
            }

            // For US/Canada numbers, check if it's potentially a mobile number
            if (cleanNumber.StartsWith("1") && cleanNumber.Length == 11)
            {
                // Extract area code for US numbers
                var areaCode = cleanNumber.Substring(1, 3);

                // Common mobile-heavy area codes (simplified approach)
                // In production, you'd want to use Twilio's lookup API or a proper number portability database
                var mobileIndicatorAreaCodes = new HashSet<string>
                {
                    "201", "202", "212", "213", "214", "215", "216", "301", "302", "303", "305", "310",
                    "312", "313", "314", "315", "316", "317", "318", "347", "404", "408", "415", "469",
                    "470", "510", "512", "513", "516", "551", "646", "678", "702", "703", "704", "714",
                    "718", "720", "727", "754", "757", "770", "773", "774", "786", "801", "818", "832",
                    "862", "917", "929", "972", "980", "984"
                };

                // This is a heuristic approach - for better accuracy, consider using Twilio's Lookup API
                if (mobileIndicatorAreaCodes.Contains(areaCode))
                {
                    return PhoneNumberType.Mobile;
                }
            }

            // Default to Local for all other cases
            return PhoneNumberType.Local;
        }
        #endregion

        #region Testing SignalR Events
        public async Task SendTestIncomingCallEvents(string userId, object payload)
        {
            if (!string.IsNullOrEmpty(userId))
            {
                var targetUser = string.IsNullOrEmpty(userId) ? "test-user" : userId;
                await _callHubContext.Clients.User(targetUser).SendAsync("IncomingCall", payload);
                await _callHubContext.Clients.User(targetUser).SendAsync("IncomingWebRTCCall", payload);
            }
            else
            {
                await _callHubContext.Clients.All.SendAsync("IncomingCall", payload);
                await _callHubContext.Clients.All.SendAsync("IncomingWebRTCCall", payload);
            }
        }

        public async Task SendTestIncomingCallEventsViaNotificationHub(string userId, object payload)
        {
            if (!string.IsNullOrEmpty(userId))
            {
                var targetUser = string.IsNullOrEmpty(userId) ? "test-user" : userId;
                await _hubContext.Clients.User(targetUser).SendAsync("IncomingCall", payload);
                await _hubContext.Clients.User(targetUser).SendAsync("IncomingWebRTCCall", payload);
            }
            else
            {
                await _hubContext.Clients.All.SendAsync("IncomingCall", payload);
                await _hubContext.Clients.All.SendAsync("IncomingWebRTCCall", payload);
            }
        }
        #endregion
    }
}
