﻿using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProject.Models
{
    public class JobProjectSettings
    {
        public Guid Id { get; set; } = new Guid();
        public string UserId { get; set; }
        [Required]
        public string CannotResceduleWithin { get; set; }
        [Required]
        public string OverDueWaitingPeriod { get; set; }
        public bool OutOfOffice { get; set; }
        public bool WorkingHours { get; set; }
        public bool EmergencyHours { get; set; }
        public DateTime UpdatedOn { get; set; }
    }
}
