using System;
using Jobid.App.AdminConsole.Enums;
using System.ComponentModel.DataAnnotations.Schema;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.Model;
using Jobid.App.Tenant.ViewModel;

public class UserCompaniesViewModel
{
    public Guid Id { get; set; }
    public string UserId { get; set; }
    public User user { get; set; }
    public bool Active { get; set; }
    public Guid TenantId { get; set; }
    public Tenant tenant { get; set; }
    public DateTime DateCreated { get; set; }
    public string UpdatedBy { get; set; }
    public DateTime LastUpdate { get; set; }
}

public class UserCompaniesVM
{
    public Guid Id { get; set; }
    public string UserId { get; set; }
    public UserVm user { get; set; }
    public bool Active { get; set; }
    public Guid TenantId { get; set; }
    public TenantModelVM tenant { get; set; }
    public DateTime DateCreated { get; set; }
    public string UpdatedBy { get; set; }
    public DateTime LastUpdate { get; set; }
    public string Email { get; set; }
    public _2FAOptions _2FAOptions { get; set; }
    public bool IsTwoFactorEnabled
    {
        get => _2FAOptions != _2FAOptions.None;
    }
    public string _2FAOptionsString 
    {
        get => _2FAOptions.ToString();
    }
}