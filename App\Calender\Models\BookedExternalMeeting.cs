﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class BookedExternalMeeting
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        public int DurationInMinutes { get; set; }

        public string TimeZone { get; set; }

        public DateTime SelectedDateAndTime { get; set; }

        [Required]
        public string FullName { get; set; }

        [Required]
        public string Email { get; set; }

        public string Location { get; set; }

        public string GuestEmails { get; set; }

        public string AdditionalInfo { get; set; }

        public bool IsCancelled { get; set; } = false;

        public string ReasonForCancelling { get; set; }

        public string ReasonForReSchedulling { get; set; }

        public string CancelMeetingLink { get; set; }

        public string ReScheduleMeetingLink { get; set; }

        public string PersonalScheduleId { get; set; }

        public DateTime BookedOn { get; set; } = DateTime.UtcNow;

        public DateTime? ReScheduledOn { get; set; }

        // To be separated with a '-'
        public List<CustomQuestionAnswer> CustomQuestionAnswer { get; set; }

        public Guid ExternalMeetingId { get; set; }

        public ExternalMeeting ExternalMeeting { get; set; }

        [NotMapped]
        public UserMDVm MeetingOwner { get; set; } = new UserMDVm();

        [NotMapped]
        public List<UserMDVm> InternalMembers { get; set; } = new List<UserMDVm>();

        [NotMapped]
        public List<string> ExternalMembers { get; set; } = new List<string>();
    }
}
