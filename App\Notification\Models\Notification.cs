﻿using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.Notification.Models
{
    public class Notification
    {
        public Guid Id { get; set; } = new Guid();

        [Required]
        public string Message { get; set; }
        public EventCategory Event { get; set; }
        public string EventId { get; set; }
        public DateTime CreatedAt { get; set; }

        [Required]
        public string CreatedBy { get; set; }

        // Navigation properties
        public ICollection<UserNotification> UserNotification { get; set; }

        // UnMapped properties
        [NotMapped]
        public UserDto CreatedByProfileDetails { get; set; }

        public Notification()
        {
            UserNotification = new List<UserNotification>();
        }
    }
}
