﻿using Dapper;
using Jobid.App.Calender.Controllers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Controllers;
using Jobid.App.Helpers.ViewModel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Utils
{
    public static class Extensions
    {
        private static ILogger _logger = Log.ForContext(typeof(Extensions));

        public static bool TableExists(this JobProDbContext dbContext, string tableName)
        {
            var sqlQ = $"SELECT COUNT(*) as Count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}'";

            var conn = dbContext.Database.GetDbConnection();
            {
                if (conn != null)
                {
                    // Query - method extension provided by Dapper library
                    var count = conn.Query<int>(sqlQ).FirstOrDefault();

                    return (count > 0);
                }
            }
            return false;
        }

        public static bool IsPayPalBillingAgreementFormatValid(this string billingAgreementId)
        {
            if (string.IsNullOrEmpty(billingAgreementId))
                return false;

            // PayPal Billing Agreement IDs typically start with "BA-" followed by alphanumeric characters
            var regex = new Regex(@"^BA-[A-Z0-9]+$", RegexOptions.IgnoreCase);
            return regex.IsMatch(billingAgreementId);
        }

        public static DateTime GetAdjustedDateTimeBasedOnTZNow()
        {
            //var timeZone = GlobalVariables.TimeZone;
            //if (!string.IsNullOrEmpty(timeZone))
            //{
            //    TimeZoneInfo timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            //    DateTime now = DateTime.UtcNow;
            //    return DateTime.UtcNow.AddHours(timeZoneInfo.BaseUtcOffset.Hours);
            //}

            return DateTime.UtcNow;
        }

        public static string ReadTemplateFromFile(string templateName, IWebHostEnvironment environment)
        {
            if (string.IsNullOrEmpty(templateName))
                return null;

            if (environment == null)
                throw new ArgumentNullException(nameof(environment));

            var templatePath = Path.Combine(environment.WebRootPath, $"EmailTemplates/{templateName}.html");
            _logger.Information($"Reading template from path: {templatePath}");

            // Check if the template file exists
            if (!File.Exists(templatePath))
            {
                Log.Error($"File path {templatePath} does not exist");
                return null;
            }

            var templateContent = File.ReadAllText(templatePath);
            _logger.Information($"Template content: {templateContent}");

            return templateContent;
        }

        public static string UpdateTemplateWithParams(string templateName, IWebHostEnvironment environment, Dictionary<string, string> parameters)
        {
            _logger.Information($"Updating template {templateName} with parameters");
            string template = ReadTemplateFromFile(templateName, environment);
            if (template == null)
            {
                return null;
            }

            foreach (var parameter in parameters)
            {
                template = template.Replace($"{parameter.Key}", parameter.Value);
            }

            return template;
        }

        // Get list of strings of enum descriptions
        public static List<string> GetEnumDescriptions<T>()
        {
            var enumDescriptions = new List<string>();
            foreach (var value in Enum.GetValues(typeof(T)))
            {
                var field = typeof(T).GetField(value.ToString());
                var attribute = (DescriptionAttribute)field.GetCustomAttribute(typeof(DescriptionAttribute));
                enumDescriptions.Add(attribute.Description);
            }

            return enumDescriptions;
        }

        public static bool IsPersonalEmail(this string email)
        {
            string[] publicDomains = { "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", 
                "aol.com", "icloud.com", "ymail.com", "sharklasers.com", "grr.la", "yopmail.com" };
            string[] parts = email.Split('@');
            if (parts.Length != 2)
            {
                return false;
            }

            string domain = parts[1].ToLower();
            foreach (string publicDomain in publicDomains)
            {
                if (domain.Equals(publicDomain))
                {
                    return true;
                }
            }

            return false;
        }

        public static bool IsEmailValid(this string email)
        {
            if (string.IsNullOrEmpty(email))
                return false;

            // Regular expression for validating an Email
            string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            Regex regex = new Regex(pattern);
            return regex.IsMatch(email);
        }

        public static bool IsTimeInTheCorrectFormat(this string input)
        {
            var parts = new List<string> { };
            string[] arr = input.Split(':');
            if (arr.Length != 3)
                return false;

            if (input.Contains('.'))
            {
                parts.Add(arr[0].Split('.')[0]);
                parts.Add(arr[0].Split('.')[1]);
                parts.Add(arr[1]);
                parts.Add(arr[2]);

                // Parse each component and validate its format
                if (!int.TryParse(parts[0], out int days) || days < 0 ||
                    !int.TryParse(parts[1], out int hours) || hours < 0 || hours > 23 ||
                    !int.TryParse(parts[2], out int minutes) || minutes < 0 || minutes > 59 ||
                    !int.TryParse(parts[3], out int seconds) || seconds < 0 || seconds > 59)
                {
                    return false;
                }
            }
            else
            {
                parts.Add(arr[0]);
                parts.Add(arr[1]);
                parts.Add(arr[2]);

                // Parse each component and validate its format
                if (!int.TryParse(parts[0], out int hours) || hours < 0 || hours > 23 ||
                    !int.TryParse(parts[1], out int minutes) || minutes < 0 || minutes > 59 ||
                    !int.TryParse(parts[2], out int seconds) || seconds < 0 || seconds > 59)
                {
                    return false;
                }
            }

            return true;
        }

        public static T ToEnum<T>(this string value) where T : struct
        {
            if (Enum.TryParse(value, out T result))
            {
                return result;
            }
            else
            {
                return default(T);
            }
        }

        public static string ToSubString(this string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                int lenghtOfString = value.Length;
                var substring = value.Substring((lenghtOfString / 2));
                return $"XXXXXX{substring}";
            }
            else
            {
                return value;
            }
        }
        public static string GetEmailDomain(this string s)
        {
            if (!string.IsNullOrEmpty(s))
            {
                if (!Regex.IsMatch(s, @"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"))
                    throw new Exception("string is not a valid email address");

                return s.Split('@')[1].ToLower();
            }

            return "";
        }
        public static DateTime ToDatetime(this string value)
        {
            DateTime dt = default(DateTime);
            if (string.IsNullOrEmpty(value))
                return dt;

            DateTime.TryParseExact(value, "dd-MM-yyyy", CultureInfo.InvariantCulture,
            DateTimeStyles.None, out dt);

            return dt;
        }
        public static DateTime ToDateTime(this string value, string dateFormat, int FormatTimeType)
        {
            DateTime dt = default(DateTime);
            if (DateTime.TryParseExact(value, dateFormat, CultureInfo.InvariantCulture,
            DateTimeStyles.None, out dt))
            {
                if (FormatTimeType == 1)
                {
                    dt = new DateTime(dt.Year, dt.Month, dt.Day, 23, 59, 0);
                }
                else
                {
                    dt.AddHours(0);
                    dt.AddMinutes(1);
                }
                return dt;
            }
            return default(DateTime);
        }

        public static string ToSha256(this string rawValue)
        {
            //return rawValue;
            if (string.IsNullOrEmpty(rawValue))
            {
                return null;
            }
            using (var alg = SHA256.Create())
            {
                var bytes = alg.ComputeHash(Encoding.UTF8.GetBytes(rawValue));
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        public static string GenerateToken(this string value, int count, char ch)
        {
            string s = new string($"{Guid.NewGuid().ToString()}{Guid.NewGuid().ToString()}".Where(x => char.IsDigit(x)).ToArray());
            s = $"{value}{s}";
            if (s.Length >= count)
                return s.Substring(0, count);
            return s.PadRight(count, ch);
        }

        public static string GetLast(this string source, int tail_length)
        {
            if (tail_length >= source.Length)
                return source;
            return source.Substring(source.Length - tail_length);
        }

        public static string ToTitleCase(this string title)
        {
            return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(title.ToLower());
        }

        public static string CapitalizeFirstLetterOfEachWord(this string sentence)
        {
            if (string.IsNullOrEmpty(sentence))
            {
                return sentence;
            }

            string[] words = sentence.Split(' ');

            for (int i = 0; i < words.Length; i++)
            {
                if (!string.IsNullOrEmpty(words[i]))
                {
                    words[i] = char.ToUpper(words[i][0]) + words[i].Substring(1);
                }
            }

            return string.Join(" ", words);
        }

        public static string CapitalizeFirstLetterOfFirstWord(this string sentence)
        {
            if (string.IsNullOrEmpty(sentence))
            {
                return sentence;
            }

            return char.ToUpper(sentence[0]) + sentence.Substring(1);
        }

        public static string CapitalizeFirstLetter(this string input)
        {
            if (string.IsNullOrWhiteSpace(input))
            {
                return input;
            }

            return char.ToUpper(input[0]) + input.Substring(1);
        }

        public static string ReadToEnd(this MemoryStream BASE)
        {
            BASE.Position = 0;
            StreamReader R = new StreamReader(BASE);
            return R.ReadToEnd();
        }


        public static Page<T> ToPageList<T>(this IEnumerable<T> query, int pageNumber, int pageSize)
        {
            var count = query.Count();
            int offset = (pageNumber - 1) * pageSize;
            var items = query.Skip(offset).Take(pageSize).ToArray();
            return new Page<T>(items, count, pageNumber, pageSize);
        }


        public static async Task<Page<T>> ToPageListAsync<T>(this IQueryable<T> query, int pageNumber, int pageSize)
        {
            var count = await query.CountAsync();
            int offset = (pageNumber - 1) * pageSize;
            var items = await query.Skip(offset).Take(pageSize).ToArrayAsync();
            return new Page<T>(items, count, pageNumber, pageSize);
        }

        public static Page<T> ToPageList<T>(this IQueryable<T> query, int pageNumber, int pageSize)
        {
            var count = query.Count();
            int offset = (pageNumber - 1) * pageSize;
            var items = query.Skip(offset).Take(pageSize).ToArray();
            return new Page<T>(items, count, pageNumber, pageSize);
        }

        public static string Base64Encode(this string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public static string Base64Decode(this string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }

        public static async Task<byte[]> ToByteArray(this IFormFile file)
        {
            var _stream = new MemoryStream();
            await file.CopyToAsync(_stream);
            return _stream.ToArray();
        }

        public static string prepareSubdomainName(this string schemaName)
        {
            if (schemaName.Contains('-'))
            {
                schemaName = schemaName.Replace('-', '_');
            }
            return schemaName;
        }

        // Generate a 10 digit randon string, a combination of number and alphabet

    }
}
