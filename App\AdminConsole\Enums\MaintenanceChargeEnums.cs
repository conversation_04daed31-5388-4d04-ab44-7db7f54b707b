using System.ComponentModel;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Jobid.App.AdminConsole.Enums
{
    [JsonConverter(typeof(StringEnumConverter))]
    public enum MaintenanceChargeStatus
    {
        [Description("Pending")]
        Pending = 1,

        [Description("Paid")]
        Paid = 2,

        [Description("Failed")]
        Failed = 3,

        [Description("Insufficient Funds")]
        InsufficientFunds = 4,

        [Description("Retry Scheduled")]
        RetryScheduled = 5,

        [Description("Cancelled")]
        Cancelled = 6
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum MaintenanceNotificationType
    {
        [Description("Charge Success")]
        ChargeSuccess = 1,

        [Description("Charge Failed")]
        ChargeFailed = 2,

        [Description("Insufficient Funds")]
        InsufficientFunds = 3,

        [Description("Fund Wallet Reminder")]
        FundWalletReminder = 4,

        [Description("Retry Notification")]
        RetryNotification = 5
    }
}
