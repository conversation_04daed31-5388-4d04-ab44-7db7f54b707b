﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class IsPersonalEmail : ValidationAttribute
    {
        public IsPersonalEmail() { }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var email = value as string;
            if (email != null)
            {
                string[] publicDomains = { "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "aol.com", "icloud.com", "ymail.com", "sharklasers.com", "grr.la", "yopmail.com" };
                string[] parts = email.Split('@');
                if (parts.Length != 2)
                {
                    return new ValidationResult("Email must be a valid email address");
                }

                string domain = parts[1].ToLower();
                foreach (string publicDomain in publicDomains)
                {
                    if (domain.Equals(publicDomain))
                    {
                        return ValidationResult.Success;
                    }
                }

                return new ValidationResult("Please use your private email, work emails are not allowed ( with your private email you can belong to multiple workspaces at the same time and navigate to them from your profile section)");
            }

            return new ValidationResult("Personal email address is required");
        }
    }
}
