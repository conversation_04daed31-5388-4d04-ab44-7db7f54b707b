﻿using Microsoft.Extensions.Configuration;
using RabbitMQ.Client;
using Serilog;
using System;

namespace Jobid.App.RabbitMQ
{
    public class RabbitMQConnectionService : IRabbitMQConnectionService, IDisposable
    {
        #region Properties and Constructors
        private readonly ILogger _logger = Log.ForContext<RabbitMQConnectionService>();
        private IConnection? _connection;
        private readonly IModel _channel;

        IConnection IRabbitMQConnectionService._connection => _connection;
        IModel IRabbitMQConnectionService._channel => _channel;

        public RabbitMQConnectionService(IConfiguration config)
        {
            // Close and dispose the connection if it is not null
            if (_connection is not null)
            {
                _connection.Close();
                _connection.Dispose();
            }

            _connection = GetRabbitMQConnection(config);
            if (_connection is not null)
            {
                _channel = _connection.CreateModel();
            }
        }

        #endregion

        #region Get RabbitMQ Connection
        public IConnection? GetRabbitMQConnection(IConfiguration config)
        {         
            try
            {
                //int num = 0;
                //string url = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_URL") ?? config.GetSection("RabbitMQConfiguration").GetSection("Host").Value;
                //string port = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PORT") ?? config.GetSection("RabbitMQConfiguration").GetSection("Port").Value;
                //string username = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_USERNAME") ?? config.GetSection("RabbitMQConfiguration").GetSection("Username").Value;
                //string password = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PASSWORD") ?? config.GetSection("RabbitMQConfiguration").GetSection("Password").Value;

                //if (!string.IsNullOrEmpty(port))
                //{
                //    num = Convert.ToInt32(port);
                //}

                //ConnectionFactory connectionFactory = new ConnectionFactory
                //{
                //    Uri = new Uri(url),
                //};

                //return connectionFactory.CreateConnection();
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine("RabbitMQ connection failed ------ " + ex.Message);
                _logger.Error("RabbitMQ connection failed >- " + ex.Message);
                return null;
            }
        }

        #endregion

        #region Dispose
        public void Dispose()
        {
            _connection?.Close();
            _connection?.Dispose();
        }

        #endregion
    }
}
