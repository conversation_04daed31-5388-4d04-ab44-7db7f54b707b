﻿using Jobid.App.Helpers.ViewModel;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface I2FAService
    {
        Task<GenericResponse> Setup2FA(TwoFactorSetupRequest model);
        Task<GenericResponse> Enable2FA(TwoFactorVerificationRequest model);
        Task<GenericResponse> Disable2FA(string userId);
        Task<bool> Verify2FA(string userId, string code);
    }
}
