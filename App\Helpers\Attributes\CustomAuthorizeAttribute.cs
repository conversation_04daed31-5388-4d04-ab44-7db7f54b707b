﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.SchemaTenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Linq;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;

namespace Jobid.App.Helpers.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public class CustomAuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        private Permissions[] _allowedPermissions;

        public CustomAuthorizeAttribute(params Permissions[] allowedPermissions)
        {
            var permissionsList = allowedPermissions.ToList();
            permissionsList.Add(Permissions.all);
            _allowedPermissions = permissionsList.ToArray();
        }

        public CustomAuthorizeAttribute() { }

        public async void OnAuthorization(AuthorizationFilterContext context)
        {
            // Skip authorization if action is decorated with [AllowAnonymous] attribute
            if (context.ActionDescriptor.EndpointMetadata
                .Any(item => item.GetType().Equals(typeof(AllowAnonymousAttribute))))
            {
                return;
            }

            // Get 'aaplication' header value from the header and allow if its 'Echo'
            var application = context.HttpContext.Request.Headers["application"].ToString();
            if (application == "Echo" || application == "echo")
            {
                return;
            }

            var claims = context.HttpContext.User.Claims.ToList();
            var userId = claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                context.Result = new JsonResult(new
                {
                    Status = "Failed",
                    Message = "User not authorized"
                })
                { StatusCode = 401 };
                return;
            }

            // Get the required services
            var tenantConfig = context.HttpContext.RequestServices.GetService(typeof(ITenantConfig<JobProDbContext>)) as ITenantConfig<JobProDbContext>;
            var tenantSchema = context.HttpContext.RequestServices.GetService(typeof(ITenantSchema)) as ITenantSchema;
            var httpContextAccessor = context.HttpContext.RequestServices.GetService(typeof(IHttpContextAccessor)) as IHttpContextAccessor;
            var DbCon = tenantConfig.getRequestContext(httpContextAccessor.HttpContext);
            var publicContext = new JobProDbContext(new DbContextSchema());
            var subdomain = tenantConfig.getSubdomainName(httpContextAccessor.HttpContext);

            // Fetch user permissions from database
            if (_allowedPermissions != null &&  _allowedPermissions.Any())
            {
                var userRole = DbCon.UserAndRoleIds.FirstOrDefault(x => x.UserProId == userId);
                if (userRole != null)
                {
                    var userPermissions = DbCon.EmployeeRolesPermissions
                    .Include(x => x.EmployeePermission)
                    .Where(x => x.RoleId == userRole.RoleId).Select(x => x.EmployeePermission.PermissionName).ToList();

                    userPermissions = userPermissions.Select(x => x.Replace("'", "")).ToList();
                    var enumPermissions = userPermissions.Select(x => Enum.Parse<Permissions>(x)).ToList();

                    var permisiionCheck = _allowedPermissions.Any(x => enumPermissions.Contains(x));
                    if (_allowedPermissions.Length > 0 && !permisiionCheck)
                    {
                        context.Result = new JsonResult(new
                        {
                            Status = "Failed",
                            Message = "You do not have the required permission"
                        })
                        { StatusCode = 403 };
                        return;
                    }
                }
                else
                {
                    context.Result = new JsonResult(new
                    {
                        Status = "Failed",
                        Message = "You don't have the required role"
                    })
                    { StatusCode = 403 };
                    return;
                }
            }
            else
            {
                var company = publicContext.Tenants.Where(x => x.Subdomain == subdomain).FirstOrDefaultAsync().Result;
                if (company == null)
                {
                    context.Result = new JsonResult(new
                    {
                        Status = false,
                        Message = "Company not found"
                    })
                    { StatusCode = 401 };
                    return;
                }

                // Check if the company has any subscription
                var subscription = await publicContext.CompanySubscriptions.Where(x => x.TenantId == company.Id).FirstOrDefaultAsync();
                if (subscription == null)
                {
                    // Check that from that date of creation, 14 days have not passed
                    if (company.DateCreated.AddDays(14) < DateTime.UtcNow)
                    {
                        context.Result = new JsonResult(new
                        {
                            Status = false,
                            Message = $"Company does not have an active subscription, your free trial has expired. Please contact admin"
                        })
                        { StatusCode = 401 };
                        return;
                    }
                }
            }
        }
    }
}
