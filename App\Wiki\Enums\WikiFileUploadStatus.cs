using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Jobid.App.Wiki.Enums
{
    [JsonConverter(typeof(StringEnumConverter))]
    public enum WikiFileUploadStatus
    {
        [EnumMember(Value = "Pending")]
        Pending = 0,
        
        [EnumMember(Value = "InProgress")]
        InProgress = 1,
        
        [EnumMember(Value = "Completed")]
        Completed = 2,
        
        [EnumMember(Value = "Failed")]
        Failed = 3
    }
}
