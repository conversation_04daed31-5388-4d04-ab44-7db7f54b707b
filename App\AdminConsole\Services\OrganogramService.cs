﻿using AutoMapper;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto.Organogram;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Services
{
    /// <summary>
    /// Service for managing organizational structure including parent and subsidiary companies
    /// </summary>
    public class OrganogramService : IOrganogramService
    {
        private readonly JobProDbContext _publicSchemaContext;
        private readonly IMapper _mapper;

        public OrganogramService(JobProDbContext publicSchemaContext, IMapper mapper)
        {
            _publicSchemaContext = publicSchemaContext ?? throw new ArgumentNullException(nameof(publicSchemaContext));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        }

        /// <summary>
        /// Creates a new parent company in the organization structure
        /// </summary>
        /// <param name="dto">The data for creating the parent company</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created company details</returns>
        public async Task<OrganogramCompanyResponseDto> CreateParentCompanyAsync(
            CreateParentCompanyDto dto,
            CancellationToken cancellationToken = default)
        {
            var organogramCompany = _mapper.Map<OrganogramCompany>(dto);
            organogramCompany.CompanyType = CompanyType.Parent;
            organogramCompany.EntityType = EntityType.Corporation;
            organogramCompany.BelongsTo = 0; // Parent company has no parent
            organogramCompany.CreatedBy = GlobalVariables.LoggedInUserId;
            organogramCompany.UpdatedBy = GlobalVariables.LoggedInUserId;
            organogramCompany.Subdomain = GlobalVariables.Subdomain;

            await _publicSchemaContext.OrganogramCompanies.AddAsync(organogramCompany, cancellationToken);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<OrganogramCompanyResponseDto>(organogramCompany);
        }

        /// <summary>
        /// Creates a new subsidiary company under a specified parent company
        /// </summary>
        /// <param name="dto">The data for creating the subsidiary company</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created company details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the parent company is not found</exception>
        public async Task<OrganogramCompanyResponseDto> CreateSubsidiaryCompanyAsync(
            CreateSubsidiaryCompanyDto dto,
            CancellationToken cancellationToken = default)
        {
            var parentCompany = await _publicSchemaContext.OrganogramCompanies
                .FirstOrDefaultAsync(c => c.Id == dto.ParentCompanyId && c.CompanyType == CompanyType.Parent,
                    cancellationToken);

            if (parentCompany == null)
            {
                throw new RecordNotFoundException($"Parent company with ID {dto.ParentCompanyId} not found or is not a parent company");
            }

            var organogramCompany = _mapper.Map<OrganogramCompany>(dto);
            organogramCompany.CompanyType = CompanyType.Subsidiary;
            organogramCompany.EntityType = EntityType.Corporation;
            organogramCompany.BelongsTo = parentCompany.index;
            organogramCompany.CreatedBy = GlobalVariables.LoggedInUserId;
            organogramCompany.UpdatedBy = GlobalVariables.LoggedInUserId;
            organogramCompany.UpdatedOn = DateTime.UtcNow;
            organogramCompany.Subdomain = GlobalVariables.Subdomain;

            await _publicSchemaContext.OrganogramCompanies.AddAsync(organogramCompany, cancellationToken);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<OrganogramCompanyResponseDto>(organogramCompany);
        }

        /// <summary>
        /// Updates an existing parent company's information
        /// </summary>
        /// <param name="dto">The updated company information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated company details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the parent company is not found</exception>
        public async Task<OrganogramCompanyResponseDto> UpdateParentCompanyAsync(
            UpdateOrganogramCompanyDto dto,
            CancellationToken cancellationToken = default)
        {
            var company = await _publicSchemaContext.OrganogramCompanies
                .FirstOrDefaultAsync(c => c.Id == dto.Id && c.CompanyType == CompanyType.Parent,
                    cancellationToken);

            if (company == null)
            {
                throw new RecordNotFoundException($"Parent company with ID {dto.Id} not found");
            }

            // Update only the allowed properties
            company.CompanyName = dto.CompanyName;
            company.Country = dto.Country;
            company.FullAddress = dto.FullAddress;
            company.EmailAddress = dto.EmailAddress;
            company.BranchColor = dto.BranchColor;
            company.Industry = dto.Industry;
            company.UpdatedBy = GlobalVariables.LoggedInUserId;
            company.CreatedBy = GlobalVariables.LoggedInUserId;
            company.UpdatedOn = DateTime.UtcNow;

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<OrganogramCompanyResponseDto>(company);
        }

        /// <summary>
        /// Updates an existing subsidiary company's information
        /// </summary>
        /// <param name="dto">The updated company information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated company details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the subsidiary company is not found</exception>
        public async Task<OrganogramCompanyResponseDto> UpdateSubsidiaryCompanyAsync(
            UpdateOrganogramCompanyDto dto,
            CancellationToken cancellationToken = default)
        {
            var company = await _publicSchemaContext.OrganogramCompanies
                .FirstOrDefaultAsync(c => c.Id == dto.Id && c.CompanyType == CompanyType.Subsidiary,
                    cancellationToken);

            if (company == null)
            {
                throw new RecordNotFoundException($"Subsidiary company with ID {dto.Id} not found");
            }

            // Update only the allowed properties
            company.CompanyName = dto.CompanyName;
            company.Country = dto.Country;
            company.FullAddress = dto.FullAddress;
            company.EmailAddress = dto.EmailAddress;
            company.BranchColor = dto.BranchColor;
            company.Industry = dto.Industry;
            company.UpdatedBy = GlobalVariables.LoggedInUserId;
            company.UpdatedOn = DateTime.UtcNow;

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<OrganogramCompanyResponseDto>(company);
        }

        /// <summary>
        /// Deletes a subsidiary company if it has no associated departments or employees
        /// </summary>
        /// <param name="id">The ID of the subsidiary company to delete</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>True if the company was deleted, false if it wasn't found</returns>
        public async Task<bool> DeleteSubsidiaryCompanyAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var company = await _publicSchemaContext.OrganogramCompanies
                .FirstOrDefaultAsync(c => c.Id == id && c.CompanyType == CompanyType.Subsidiary,
                    cancellationToken);

            if (company == null)
            {
                return false;
            }

            // Get all departments in this company
            var departments = await _publicSchemaContext.Departments
                .Where(d => d.CompanyId == id)
                .ToListAsync(cancellationToken);

            if (departments.Any())
            {
                // Get all employees in these departments
                var departmentIds = departments.Select(d => d.Id);
                var employees = await _publicSchemaContext.Individuals
                    .Where(i => departmentIds.Contains(i.DepartmentId))
                    .ToListAsync(cancellationToken);

                // Remove employees first
                if (employees.Any())
                {
                    _publicSchemaContext.Individuals.RemoveRange(employees);
                }

                // Remove departments
                _publicSchemaContext.Departments.RemoveRange(departments);
            }

            // Finally remove the company
            _publicSchemaContext.OrganogramCompanies.Remove(company);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return true;
        }

        /// <summary>
        /// Creates a new department in a company
        /// </summary>
        /// <param name="dto">The data for creating the department</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created department details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the company is not found</exception>
        public async Task<DepartmentResponseDto> CreateDepartmentAsync(
            CreateDepartmentDto dto,
            CancellationToken cancellationToken = default)
        {
            // Validate company exists
            var companyExists = await _publicSchemaContext.OrganogramCompanies
                .FirstOrDefaultAsync(c => c.Id == dto.CompanyId, cancellationToken);

            if (companyExists == null)
            {
                throw new RecordNotFoundException($"Company with ID {dto.CompanyId} not found");
            }

            var department = _mapper.Map<Department>(dto);
            department.CreatedOn = DateTime.UtcNow;
            department.CreatedBy = GlobalVariables.LoggedInUserId;
            department.BelongsTo = companyExists.index;

            await _publicSchemaContext.Departments.AddAsync(department, cancellationToken);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<DepartmentResponseDto>(department);
        }

        /// <summary>
        /// Updates an existing department's information
        /// </summary>
        /// <param name="dto">The updated department information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated department details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the department is not found</exception>
        public async Task<DepartmentResponseDto> UpdateDepartmentAsync(
            UpdateDepartmentDto dto,
            CancellationToken cancellationToken = default)
        {
            var department = await _publicSchemaContext.Departments
                .FirstOrDefaultAsync(d => d.Id == dto.Id, cancellationToken);

            if (department == null)
            {
                throw new RecordNotFoundException($"Department with ID {dto.Id} not found");
            }

            department.DepartmentName = dto.DepartmentName;
            department.BranchColor = dto.BranchColor;
            department.UpdatedBy = GlobalVariables.LoggedInUserId;
            department.UpdatedOn = DateTime.UtcNow;

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<DepartmentResponseDto>(department);
        }

        /// <summary>
        /// Deletes a department and its associated employees. Will not delete if department has sub-departments
        /// </summary>
        /// <param name="id">The ID of the department to delete</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>True if the department was deleted, false if it wasn't found</returns>
        public async Task<bool> DeleteDepartmentAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var department = await _publicSchemaContext.Departments
                .FirstOrDefaultAsync(d => d.Id == id, cancellationToken);

            if (department == null)
            {
                return false;
            }

            // Check for sub-departments - we don't want to delete if there are sub-departments
            var subDepartments = await _publicSchemaContext.Departments
                .Where(d => d.BelongsTo == department.index).ToListAsync(cancellationToken);

            if (subDepartments.Any())
            {
                _publicSchemaContext.Departments.RemoveRange(subDepartments);
            }

            // Get all employees in this department
            var departmentEmployees = await _publicSchemaContext.Individuals
                .Where(i => i.DepartmentId == id)
                .ToListAsync(cancellationToken);

            // Remove all employees first
            if (departmentEmployees.Any())
            {
                _publicSchemaContext.Individuals.RemoveRange(departmentEmployees);
            }

            // Then remove the department
            _publicSchemaContext.Departments.Remove(department);

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);
            return true;
        }

        /// <summary>
        /// Creates a new employee position in a department
        /// </summary>
        /// <param name="dto">The position details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created position details</returns>
        public async Task<EmployeePositionResponseDto> CreateEmployeePositionAsync(
            CreateEmployeePositionDto dto,
            CancellationToken cancellationToken = default)
        {
            // Validate department exists
            var departmentExists = await _publicSchemaContext.Departments
                .AnyAsync(d => d.Id == dto.DepartmentId, cancellationToken);

            if (!departmentExists)
            {
                throw new RecordNotFoundException($"Department with ID {dto.DepartmentId} not found");
            }

            // If BelongsTo is not 0, validate parent position exists in same department
            if (dto.BelongsTo != 0)
            {
                var parentPositionExists = await _publicSchemaContext.EmployeePositions
                    .AnyAsync(p => p.index == dto.BelongsTo && p.DepartmentId == dto.DepartmentId,
                        cancellationToken);

                if (!parentPositionExists)
                {
                    throw new RecordNotFoundException($"Parent position with index {dto.BelongsTo} not found in the specified department");
                }
            }

            var position = _mapper.Map<EmployeePosition>(dto);
            position.CreatedOn = DateTime.UtcNow;
            position.CreatedBy = GlobalVariables.LoggedInUserId;

            await _publicSchemaContext.EmployeePositions.AddAsync(position, cancellationToken);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<EmployeePositionResponseDto>(position);
        }

        /// <summary>
        /// Updates an existing employee position
        /// </summary>
        /// <param name="dto">The updated position details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated position details</returns>
        public async Task<EmployeePositionResponseDto> UpdateEmployeePositionAsync(
            UpdateEmployeePositionDto dto,
            CancellationToken cancellationToken = default)
        {
            var position = await _publicSchemaContext.EmployeePositions
                .FirstOrDefaultAsync(p => p.Id == dto.Id, cancellationToken);

            if (position == null)
            {
                throw new RecordNotFoundException($"Position with ID {dto.Id} not found");
            }

            position.PositionName = dto.PositionName;
            position.UpdatedBy = GlobalVariables.LoggedInUserId;
            position.UpdatedOn = DateTime.UtcNow;

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<EmployeePositionResponseDto>(position);
        }

        /// <summary>
        /// Deletes an employee position and all employees assigned to it
        /// </summary>
        /// <param name="id">The ID of the position to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the position was deleted, false if it wasn't found</returns>
        public async Task<bool> DeleteEmployeePositionAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var position = await _publicSchemaContext.EmployeePositions
                .FirstOrDefaultAsync(p => p.Id == id, cancellationToken);

            if (position == null)
            {
                return false;
            }

            // Get all employees in this position
            var employeesInPosition = await _publicSchemaContext.Individuals
                .Where(i => i.PositionId == id)
                .ToListAsync(cancellationToken);

            // Remove all employees first if any exist
            if (employeesInPosition.Any())
            {
                _publicSchemaContext.Individuals.RemoveRange(employeesInPosition);
            }

            // Then remove the position
            _publicSchemaContext.EmployeePositions.Remove(position);

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);
            return true;
        }


        /// <summary>
        /// Creates a new individual in the organization with specified company, department, and position
        /// </summary>
        /// <param name="dto">The data for creating the individual</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created individual details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when company, department, position, or superior is not found</exception>
        public async Task<IndividualResponseDto> CreateIndividualAsync(CreateIndividualDto dto, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(GlobalVariables.Subdomain));

            var user = await context.UserProfiles.FirstOrDefaultAsync(u => u.UserId == dto.UserId.ToString(), cancellationToken);

            if (user == null)
            {
                throw new RecordNotFoundException($"User with ID {dto.UserId} not found.");
            }

            // Validate department exists and belongs to company
            var department = await _publicSchemaContext.Departments.FirstOrDefaultAsync(d => d.Id == dto.DepartmentId, cancellationToken);

            if (department == null)
            {
                throw new RecordNotFoundException($"Department with ID {dto.DepartmentId} not found in specified company");
            }

            // Validate position exists and belongs to department
            var positionExists = await _publicSchemaContext.EmployeePositions
                .AnyAsync(p => p.Id == dto.PositionId && p.DepartmentId == dto.DepartmentId,
                    cancellationToken);

            if (!positionExists)
            {
                throw new RecordNotFoundException($"Position with ID {dto.PositionId} not found in specified department");
            }

            // Validate superior exists if specified
            if (dto.BelongsTo != 0)
            {
                var superiorExists = await _publicSchemaContext.Individuals
                    .AnyAsync(i => i.index == dto.BelongsTo && i.DepartmentId == dto.DepartmentId,
                        cancellationToken);

                if (!superiorExists)
                {
                    throw new RecordNotFoundException($"Superior with index {dto.BelongsTo} not found in specified company");
                }
            }

            var individual = _mapper.Map<Individual>(dto);
            individual.CreatedOn = DateTime.UtcNow;
            individual.CreatedBy = GlobalVariables.LoggedInUserId;
            individual.UpdatedBy = GlobalVariables.LoggedInUserId;
            individual.CompanyId = department.CompanyId;

            await _publicSchemaContext.Individuals.AddAsync(individual, cancellationToken);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<IndividualResponseDto>(individual);
        }

        /// <summary>
        /// Updates an existing individual's information in the organization
        /// </summary>
        /// <param name="dto">The updated individual information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated individual details</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the individual is not found</exception>
        public async Task<IndividualResponseDto> UpdateIndividualAsync(UpdateIndividualDto dto, CancellationToken cancellationToken = default)
        {
            var individual = await _publicSchemaContext.Individuals.FirstOrDefaultAsync(i => i.Id == dto.Id, cancellationToken);

            if (individual == null)
            {
                throw new RecordNotFoundException($"Individual with ID {dto.Id} not found");
            }

            individual.EmailAddress = dto.EmailAddress;
            individual.Name = dto.Name;
            individual.Title = dto.Title;
            individual.BranchColor = dto.BranchColor;
            individual.IsHeadofDepartment = dto.IsHeadOfDepartment;
            individual.UpdatedBy = GlobalVariables.LoggedInUserId;
            individual.UpdatedOn = DateTime.UtcNow;

            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return _mapper.Map<IndividualResponseDto>(individual);
        }

        /// <summary>
        /// Deletes an individual from the organization if they have no subordinates
        /// </summary>
        /// <param name="id">The ID of the individual to delete</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>True if the individual was deleted, false if they weren't found</returns>
        public async Task<bool> DeleteIndividualAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var individual = await _publicSchemaContext.Individuals
                .FirstOrDefaultAsync(i => i.Id == id, cancellationToken);

            if (individual == null)
            {
                return false;
            }

            // Check if this individual has subordinates
            var hasSubordinates = await _publicSchemaContext.Individuals
                .AnyAsync(i => i.BelongsTo == individual.index, cancellationToken);

            if (hasSubordinates)
            {
                throw new InvalidOperationException("Cannot delete individual with existing subordinates");
            }

            _publicSchemaContext.Individuals.Remove(individual);
            await _publicSchemaContext.SaveChangesAsync(cancellationToken);

            return true;
        }


        /// <summary>
        /// Gets the complete organizational hierarchy for a given subdomain
        /// </summary>
        /// <param name="subdomain">The subdomain to get the hierarchy for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The complete organizational hierarchy</returns>
        public async Task<OrganogramHierarchyDto> GetOrganizationHierarchyAsync(string subdomain, CancellationToken cancellationToken = default)
        {
            // Get the parent company
            var parentCompany = await _publicSchemaContext.OrganogramCompanies
                .Where(c => c.Subdomain == subdomain && c.CompanyType == CompanyType.Parent)
                .FirstOrDefaultAsync(cancellationToken);

            if (parentCompany == null)
            {
                return new OrganogramHierarchyDto
                {
                    ParentCompany = new ParentCompanyDto(),
                    Subsidiaries = new List<SubsidiaryCompanyDto>()
                };
            }

            var result = new OrganogramHierarchyDto
            {
                ParentCompany = _mapper.Map<ParentCompanyDto>(parentCompany)
            };

            // Get all subsidiary companies
            var subsidiaries = await _publicSchemaContext.OrganogramCompanies
                .Where(c => c.Subdomain == subdomain && c.CompanyType == CompanyType.Subsidiary)
                .ToListAsync(cancellationToken);

            foreach (var subsidiary in subsidiaries)
            {
                var subsidiaryDto = _mapper.Map<SubsidiaryCompanyDto>(subsidiary);

                // Get departments for this subsidiary
                var departments = await _publicSchemaContext.Departments
                    .Where(d => d.CompanyId == subsidiary.Id)
                    .ToListAsync(cancellationToken);

                foreach (var department in departments)
                {
                    var departmentDto = _mapper.Map<DepartmentHierarchyDto>(department);

                    // Get positions for this department
                    var positions = await _publicSchemaContext.EmployeePositions
                        .Where(p => p.DepartmentId == department.Id)
                        .ToListAsync(cancellationToken);

                    foreach (var position in positions)
                    {
                        var positionDto = _mapper.Map<PositionHierarchyDto>(position);

                        // Get individuals for this position
                        var individuals = await _publicSchemaContext.Individuals
                            .Where(i => i.PositionId == position.Id)
                            .ToListAsync(cancellationToken);

                        positionDto.Individuals = _mapper.Map<List<IndividualHierarchyDto>>(individuals);
                        departmentDto.Positions.Add(positionDto);
                    }

                    subsidiaryDto.Departments.Add(departmentDto);
                }

                result.Subsidiaries.Add(subsidiaryDto);
            }

            return result;
        }


        /// <summary>
        /// Retrieves a list of user suggestions based on a search term matching first name, middle name, or last name
        /// </summary>
        /// <param name="searchTerm">The search term to match against user names. Can be part of first name, middle name, or last name</param>
        /// <param name="maxResults">Maximum number of suggestions to return (default is 10)</param>
        /// <param name="cancellationToken">A cancellation token that can be used to cancel the operation</param>
        /// <returns>A list of user suggestions containing user details</returns>
        public async Task<List<UserSuggestionDto>> GetUserSuggestionsAsync(
            string searchTerm,
            int maxResults = 10,
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<UserSuggestionDto>();

            // Normalize the search term
            searchTerm = searchTerm.Trim().ToLower();

            // Initialize subdomain context
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            // Query for user profiles
            var query = from user in context.UserProfiles
                        where !user.IsDeleted
                              && !user.IsSuspended
                              && (user.FirstName.ToLower().Contains(searchTerm)
                                  || user.LastName.ToLower().Contains(searchTerm)
                                  || user.MiddleName != null && user.MiddleName.ToLower().Contains(searchTerm))
                        select new
                        {
                            user.UserId,
                            user.FirstName,
                            user.MiddleName,
                            user.LastName,
                            user.Profession,
                            user.Designation,
                            user.Email
                        };

            // Execute query and process results on the client side
            var users = await query.ToListAsync(cancellationToken);

            // Client-side processing for name formatting and ordering
            var suggestions = users.Select(user => new UserSuggestionDto
            {
                UserId = user.UserId,
                Name = string.IsNullOrEmpty(user.MiddleName)
                    ? $"{user.FirstName} {user.LastName}"
                    : $"{user.FirstName} {user.MiddleName} {user.LastName}",
                Title = user.Profession,
                Position = user.Designation,
                EmailAddress = user.Email
            })
            .OrderBy(u => u.Name.ToLower().StartsWith(searchTerm) ? 0 : 1) // Exact matches first
            .ThenBy(u => u.Name)                                           // Then alphabetically
            .Take(maxResults)
            .ToList();

            return suggestions;
        }


        public async Task<BasicInfoResponseDto> GetBasicInfoAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            var basicInfo = await context.BasicInfos.FirstOrDefaultAsync(b => b.UserId == userId, cancellationToken);

            if (basicInfo == null)
                throw new RecordNotFoundException($"Basic information not found for user ID {userId}");

            return _mapper.Map<BasicInfoResponseDto>(basicInfo);
        }

        public async Task<BasicInfoResponseDto> UpdateBasicInfoAsync(UpdateBasicInfoDto dto, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            var basicInfo = await context.BasicInfos.FirstOrDefaultAsync(b => b.UserId == dto.UserId, cancellationToken);

            if (basicInfo == null)
            {
                basicInfo = new BasicInfo
                {
                    UserId = dto.UserId,
                    Subdomain = GlobalVariables.Subdomain,
                    CreatedBy = GlobalVariables.LoggedInUserId
                };
                await context.BasicInfos.AddAsync(basicInfo, cancellationToken);
            }
            else
            {
                basicInfo.UpdatedBy = GlobalVariables.LoggedInUserId;
                basicInfo.UpdatedOn = DateTime.UtcNow;
            }

            _mapper.Map(dto, basicInfo);
            await context.SaveChangesAsync(cancellationToken);

            return _mapper.Map<BasicInfoResponseDto>(basicInfo);
        }

        public async Task<EmergencyContactResponseDto> GetEmergencyContactAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            var emergencyInfo = await context.EmergencyInfos.FirstOrDefaultAsync(e => e.UserId == userId, cancellationToken);

            if (emergencyInfo == null)
                throw new RecordNotFoundException($"Emergency contact information not found for user ID {userId}");

            return _mapper.Map<EmergencyContactResponseDto>(emergencyInfo);
        }

        public async Task<EmergencyContactResponseDto> UpdateEmergencyContactAsync(UpdateEmergencyContactDto dto, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            var emergencyInfo = await context.EmergencyInfos.FirstOrDefaultAsync(e => e.UserId == dto.UserId, cancellationToken);

            if (emergencyInfo == null)
            {
                emergencyInfo = new EmergencyInfo
                {
                    UserId = dto.UserId,
                    Subdomain = GlobalVariables.Subdomain,
                    CreatedBy = GlobalVariables.LoggedInUserId
                };
                await context.EmergencyInfos.AddAsync(emergencyInfo, cancellationToken);
            }
            else
            {
                emergencyInfo.UpdatedBy = GlobalVariables.LoggedInUserId;
                emergencyInfo.UpdatedOn = DateTime.UtcNow;
            }

            _mapper.Map(dto, emergencyInfo);
            await context.SaveChangesAsync(cancellationToken);

            return _mapper.Map<EmergencyContactResponseDto>(emergencyInfo);
        }


        public async Task<BasicInfoResponseDto> AddBasicInfoAsync(CreateBasicInfoDto dto, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            // Check if basic info already exists for the user
            var existingInfo = await context.BasicInfos
                .FirstOrDefaultAsync(b => b.UserId == dto.UserId && b.Subdomain == GlobalVariables.Subdomain,
                    cancellationToken);

            if (existingInfo != null)
            {
                throw new RecordAlreadyExistException($"Basic information already exists for user ID {dto.UserId}");
            }

            var basicInfo = _mapper.Map<BasicInfo>(dto);
            basicInfo.CreatedBy = GlobalVariables.LoggedInUserId;
            basicInfo.Subdomain = GlobalVariables.Subdomain;
            basicInfo.CreatedOn = DateTime.UtcNow;

            await context.BasicInfos.AddAsync(basicInfo, cancellationToken);
            await context.SaveChangesAsync(cancellationToken);

            return _mapper.Map<BasicInfoResponseDto>(basicInfo);
        }

        public async Task<EmergencyContactResponseDto> AddEmergencyContactAsync(CreateEmergencyContactDto dto, CancellationToken cancellationToken = default)
        {
            var context = new JobProDbContext(new DbContextSchema(GlobalVariables.Subdomain));

            // Check if emergency contact already exists for the user
            var existingContact = await context.EmergencyInfos
                .FirstOrDefaultAsync(e => e.UserId == dto.UserId && e.Subdomain == GlobalVariables.Subdomain,
                    cancellationToken);

            if (existingContact != null)
            {
                throw new RecordAlreadyExistException($"Emergency contact information already exists for user ID {dto.UserId}");
            }

            var emergencyInfo = _mapper.Map<EmergencyInfo>(dto);
            emergencyInfo.CreatedBy = GlobalVariables.LoggedInUserId;
            emergencyInfo.Subdomain = GlobalVariables.Subdomain;
            emergencyInfo.CreatedOn = DateTime.UtcNow;

            await context.EmergencyInfos.AddAsync(emergencyInfo, cancellationToken);
            await context.SaveChangesAsync(cancellationToken);

            return _mapper.Map<EmergencyContactResponseDto>(emergencyInfo);
        }


        #region Register Complaint
        public async Task<GenericResponse> RegisterComplaint(RegisterComplaintDto dto, string subDomain, CancellationToken cancellationToken = default)
        {
            var result = false;
            var context = new JobProDbContext(new DbContextSchema(subDomain));
            var user = await context.UserProfiles.FirstOrDefaultAsync(u => u.UserId == dto.UserId, cancellationToken);

            if (string.IsNullOrWhiteSpace(subDomain))
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "subDomain is required" };

            if (user == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "user not found" };

            if (!Enum.IsDefined(typeof(ComplaintCategory), dto.Category))
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "category not found" };

            var complaint = _mapper.Map<EmployeeComplaint>(dto);
            complaint.IssuedBy = GlobalVariables.LoggedInUserId;
            complaint.Subdomain = GlobalVariables.Subdomain;
            complaint.DateIssued = DateTime.UtcNow;
            complaint.ActionWithdrawn = false;

            await context.EmployeeComplaints.AddAsync(complaint, cancellationToken);
            result = await context.SaveChangesAsync(cancellationToken) > 0;

            if (result) return new GenericResponse { ResponseCode = "200", ResponseMessage = "Complaint registered successfully." };
            return new GenericResponse { ResponseCode = "400", ResponseMessage = "Failed to register complaint" }; ;
        }

        #endregion

        #region Withdraw Action
        public async Task<GenericResponse> WithdrawAction(string userId, string id, string subDomain)
        {
            var result = false;
            var context = new JobProDbContext(new DbContextSchema(subDomain));
            var getComplaints = await context.EmployeeComplaints.FirstOrDefaultAsync(x => x.UserId == userId && x.Id.ToString() == id && !x.ActionWithdrawn);
            if (getComplaints == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Complaint no found for this user." };

            getComplaints.ActionWithdrawn = true;

            result = await context.SaveChangesAsync() > 0;
            if (result) return new GenericResponse { ResponseCode = "200", ResponseMessage = "Action withdrawn successfully.." };

            return new GenericResponse { ResponseCode = "400", ResponseMessage = "Unable to withdraw action." };
        }
        #endregion

        #region View User Complaint Details
        public async Task<ApiResponse<GetRegisteredComplaint>> ViewUserComplaints(string userId, string id, string subDomain)
        {
            var context = new JobProDbContext(new DbContextSchema(subDomain));
            var getComplaints = await context.EmployeeComplaints.FirstOrDefaultAsync(x => x.UserId == userId && x.Id.ToString() == id && !x.ActionWithdrawn);
            if (getComplaints == null)
                return new ApiResponse<GetRegisteredComplaint> { ResponseCode = "400", ResponseMessage = "Complaint no found for this user." };

            var complaint = _mapper.Map<GetRegisteredComplaint>(getComplaints);
            return new ApiResponse<GetRegisteredComplaint> { Data = complaint, ResponseCode = "200", ResponseMessage = "Complaint fatched successfully." };
        }
        #endregion

        #region Get all Users Complaints
        public async Task<Page<GetRegisteredComplaint>> GetAllUserComplaints(string userId, PaginationParameters parameters, string subDomain)
        {
            var context = new JobProDbContext(new DbContextSchema(subDomain));
            IQueryable<EmployeeComplaint> employeeComplaints = context.EmployeeComplaints.Where(x => x.UserId == userId && !x.ActionWithdrawn);

            return await employeeComplaints.Select(x => new GetRegisteredComplaint()
            {
                DateIssued = x.DateIssued,
                Issuer = x.IssuedBy
            }).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }
        #endregion
    }
}