﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class ExternalMeeting : BaseModel
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public string MeetingName { get; set; }

        [Required]
        public string Location { get; set; }
        public string Description { get; set; }
        public DateTime? CanBookStartDate { get; set; }
        public DateTime? CanBookEndDate { get; set; }
        public Guid CreatedBy { get; set; }
        public ExternalMeetingTypePlan ExternalMeetingTypePlan { get; set; } = ExternalMeetingTypePlan.Free;
        public ExternalMeetingType ExternalMeetingType { get; set; } = ExternalMeetingType.OneOnOne;
        public decimal? Price { get; set; }
        public string Currency { get; set; }
        public bool EmailComfirmation { get; set; }
        public bool EmailRemindersBefore { get; set; }
        public bool EmailFollowUpAfter { get; set; }
        public bool CancellationPolicy { get; set; }
        public ComfirmationPageOptions ComfirmationPageOptions { get; set; } = ComfirmationPageOptions.Default;
        public string SiteUrl { get; set; }
        public bool PushMeetingToWebsite { get; set; }
        public string InternalNote { get; set; }
        public int MeetingDuration { get; set; }
        public string BookingLink { get; set; }
        public string MeetingLink { get; set; }
        public string MeetingId { get; set; }
        public string Guests { get; set; }
        public Guid PersonalScheduleId { get; set; }
        public Guid ExternalMeetingQuestionId { get; set; }
        public ExternalMeetingFrequency MeetingFrequency { get; set; } = ExternalMeetingFrequency.OneOff;
        public DateTime? MeetingStartDateRange { get; set; }
        public DateTime? MeetingEndDateRange { get; set; }
        public string MeetingOwnerId { get; set; }
        public string OtherMeetingHostsIds { get; set; }  // For Round Robin Meetings
        public bool CanBeChosenBasedOnAvailability{ get; set; }
        public int? MaxInvitePerMeeting { get; set; } // For Group Meetings
        public bool MakeMeetingPrivate { get; set; }
        public bool IsVisible { get; set; }
        public bool IsLocked { get; set; }
        public bool IsCancelled { get; set; }

        /// <summary>
        /// Buffer in minutes between booked meetings. Default is 10 minutes.
        /// </summary>
        public int MeetingBuffer { get; set; } = 10;

        /// <summary>
        /// This property is for avoiding conflicts when booking meetings.
        /// </summary>
        public bool AvoidConflicts { get; set; }

        /// <summary>
        /// This property is for number of bookings allowed per time slot for when avoid conflicts is set to false.
        /// </summary>
        public int? MaxNoOfBookingsPerSlot { get; set; }

        // Navigation Properties
        public PersonalSchedule PersonalSchedule { get; set; }
        public ExternalMeetingQuestion ExternalMeetingQuestion { get; set; }
        public List<CustomQuestion> CustomQuestion { get; set; }

        [NotMapped]
        public List<ExternalMeetingTimeManagement> ExternalMeetingTimeBreakDowns { get; set; } = new List<ExternalMeetingTimeManagement>();
    }
}
