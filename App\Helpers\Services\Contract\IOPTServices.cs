using System.Threading.Tasks;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.Tenant.Contract
{
    public interface IOTPServices
    {
        Task<ApiResponse<bool>> RegisterTenantCreateToken(NewOTPVM model);
        Task<ApiResponse<bool>> VerifyToken(VerifyTokenOTPVM model);
        Task<bool> GenerateToken(NewOTPVM model, int Length, OTPTokenType tokenType, string smsMessage = null);
        Task<bool> RegisterNewUserEmailToken(NewOTPVM model);
        string GenerateRandomNumbers(int min = 0, int max = 9, int count = 4);
        OTPVM GetToken(string identifier, OTPIdentifierType IdentifierType, OTPTokenType tokenType, string status = null);
        Task<bool> MarkTokenAsUsed(string id);
        
    }
}
