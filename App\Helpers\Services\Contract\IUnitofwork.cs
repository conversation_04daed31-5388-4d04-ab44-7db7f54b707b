using Jobid.App.ActivityLog.BackGroundJobs;
using Jobid.App.ActivityLog.contract;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Calender.Contracts;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.Notification.Contracts;
using Jobid.App.Subscription.Services.Contract;
using Jobid.App.Tenant.Contract;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Contract
{
    public interface IUnitofwork
    {
        public Task CommitAsync();
        public IUserServices Userservice { get; }
        public ITeamSheetService TeamSheetService { get; }
        public IBackGroundService BackGroundService { get; }
        public IBackgroundJobService BackgroundJobService { get; }
        public I2FAService I2FAService { get; }
        public IClientServices ClientService { get; }
        public IProjectFileService ProjectFileService { get; }
        public ITenantService TenantService { get; }        public IAdminService AdminService { get; }
        public IAppDashboardService AppDashboardService { get; }
        public IContactService ContactService { get; }
        public ICampaignService CampaignService { get; }
        public ICompanyUserInvite CompanyUserInviteService { get; }
        public IUserCompaniesServices UserCompaniesServices { get; }
        public IProjectServices ProjectService { get; }
        public ITodoServices TodoService { get; }
        public IOTPServices OPTService { get; }
        public ITimeSheetService TimeSheetService { get; }
        public ITriggerServices TriggerService { get; }
        public ISprintProjectServices SprintProjectService { get; }
        public ICalenderService CalenderService { get; }
        public IUserProfileServices UserProfileServices { get; }
        public ITagService TagService { get; }
        public IActivityService ActivityService { get; }
        public IUserAdminServices UserAdminServices { get; }
        public ISubscriptionServices SubscriptionServices { get; }
        public Subscription.Services.Contract.IBackGroundServices BackGroundServices { get; }
        public INotificationsService NotificationsService { get; }
        public ActivityLog.BackGroundJobs.IActivityBackgroundService ActivityBackgroundService { get; }
        public IProductUpdateService ProductUpdateService { get; }        public IEndpointTrackerService EndpointTrackerService { get; }
        public ITenantClientService TenantClientService { get; }        public Jobid.App.Wiki.Services.Contract.IWikiAccessService WikiAccess { get; }        public Jobid.App.Wiki.Services.Contract.IWikiFileService WikiFile { get; }
        public IPhoneNumberService PhoneNumberService { get; }
        public IPaymentService PaymentService { get; }
        public IWalletService WalletService { get; }
        public ITelephonyService TelephonyService { get; }
        public ITwilioConferenceService TwilioConferenceService { get; }
    }
}
