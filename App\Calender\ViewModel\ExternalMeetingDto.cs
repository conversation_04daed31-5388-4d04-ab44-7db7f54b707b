﻿using Jobid.App.Helpers.Enums;
using System.ComponentModel.DataAnnotations;
using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Jobid.App.Calender.ViewModel
{
    public class ExternalMeetingDto
    {
        [Required]
        public string MeetingName { get; set; }

        [Required]
        public string Location { get; set; } = "JobPro Video Conferrencing";
        public string Description { get; set; }
        public PersonalScheduleType PersonalSchedule { get; set; }
        public DateTime CanBookStartDate { get; set; }
        public DateTime CanBookEndDate { get; set; }
        public ExternalMeetingTypePlan ExternalMeetingTypePlan { get; set; } = ExternalMeetingTypePlan.Free;
        public ExternalMeetingType ExternalMeetingType { get; set; } = ExternalMeetingType.OneOnOne;

        [Required]
        public int MeetingDuration { get; set; }
        public decimal? Price { get; set; }
        public string Currency { get; set; }
        public bool EmailComfirmation { get; set; }
        public bool EmailRemindersBefore { get; set; }
        public bool EmailFollowUpAfter { get; set; }
        public bool CancellationPolicy { get; set; }
        public ComfirmationPageOptions ComfirmationPageOptions { get; set; } = ComfirmationPageOptions.Default;
        public string SiteUrl { get; set; }
        public bool PushMeetingToWebsite { get; set; }
        public ExternalMeetingQuestionDto ExternalMeetingQuestionSetting { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }
        public string InternalNote { get; set; }

        /// <summary>
        /// This property is for invited guests
        /// </summary>
        public List<string> InvitedGuestEmails { get; set; } = new List<string>();
        public ExternalMeetingFrequency MeetingFrequency { get; set; } = ExternalMeetingFrequency.OneOff;
        public DateTime? MeetingStartDateRange { get; set; }
        public DateTime? MeetingEndDateRange { get; set; }
        public string MeetingOwnerId { get; set; }

        /// <summary>
        /// Buffer in minutes between booked meetings. Default is 10 minutes.
        /// </summary>
        public int MeetingBuffer { get; set; } = 10;
        public bool AvoidConflicts { get; set; }

        /// <summary>
        /// This property is for number of bookings allowed per time slot for when avoid conflicts is set to false.
        /// </summary>
        public int? MaxNoOfBookingsPerSlot { get; set; }

        // For Round Robin Meetings
        public List<string> OtherMeetingHostsIds { get; set; } = new List<string> { };
        public bool CanBeChosenBasedOnAvailability { get; set; } = true;

        /// <summary>
        /// For Group meetings
        /// </summary>
        public List<string> TeamMembersIds { get; set; } = new List<string> { };
        public bool MakeMeetingPrivate { get; set; }
        public int? MaxInvitePerMeeting { get; set; }
        public List<PersonalScheduleDto> PersonalScheduleDtos { get; set; } = new List<PersonalScheduleDto>();

        [Newtonsoft.Json.JsonIgnore]
        public Guid CreatedBy { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
