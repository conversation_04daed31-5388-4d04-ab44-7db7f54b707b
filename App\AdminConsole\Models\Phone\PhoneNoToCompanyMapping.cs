﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models.Phone
{
    public class PhoneNoToCompanyMapping
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public string PhoneNumber { get; set; }

        [ForeignKey("Tenant")]
        public Guid TenantId { get; set; }

        // Navigation properties
        public virtual Jobid.App.Tenant.Model.Tenant Tenant { get; set; }

        public PhoneNoToCompanyMapping()
        {
            Id = Guid.NewGuid();
        }
    }
}
