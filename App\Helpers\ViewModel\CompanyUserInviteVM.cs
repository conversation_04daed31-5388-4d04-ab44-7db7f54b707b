using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.Models
{
    public class CompanyUserInviteVM
    {
        public string Id { get; set; }

        [Required]
        [ValidEmailCheck]
        public string Email { get; set; }
        public DateTime DateCreated { get; set; }
        public string Status { get; set; }
        public DateTime LastUpdate { get; set; }
        public string InviteCode { get; set; }
        public string TenantId { get; set; }
        public Applications Application { get; set; }
    }
}
