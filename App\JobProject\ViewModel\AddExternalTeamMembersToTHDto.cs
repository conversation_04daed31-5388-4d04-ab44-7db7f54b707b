﻿using Jobid.App.Helpers.Utils.Attributes;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class AddExternalTeamMembersToTHDto
    {
        public string TeamId { get; set; }

        [CanInviteExternalUser(Helpers.Enums.Applications.Joble)]
        public List<string> ExternalMemberEmails { get; set; } = new List<string>();
        public string UserId { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
