using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System;
using System.Security.Claims;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Wiki.Controllers
{
    /// <summary>
    /// BaseController
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class BaseController : ControllerBase
    {
        private string ClaimId => User?.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        private string ClaimName => User?.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;

        /// <summary>
        /// Current user
        /// </summary>
        protected Guid? CurrentUserId => ClaimId is null ? null : Guid.Parse(ClaimId);
        protected string CurrentUser => ClaimName is null ? null : ClaimName;

        public async Task<T> ConvertDateTimeToLocalDateTime<T>(T response)
        {
            if (response == null)
                return response;

            var properties = typeof(T).GetProperties();
            foreach (var property in properties)
            {
                if (property.PropertyType == typeof(DateTime))
                {
                    var dateTimeValue = (DateTime)property.GetValue(response);
                    var localDateTimeValue = dateTimeValue.ToLocalTime();
                    property.SetValue(response, localDateTimeValue);
                }
                else if (property.PropertyType == typeof(DateTime?))
                {
                    var nullableDateTimeValue = property.GetValue(response) as DateTime?;
                    if (nullableDateTimeValue.HasValue)
                    {
                        var localDateTimeValue = nullableDateTimeValue.Value.ToLocalTime();
                        property.SetValue(response, localDateTimeValue);
                    }
                }
                // Process any property that might contain nested objects, not just "Data"
                else if (property.PropertyType.IsClass && property.PropertyType != typeof(string))
                {
                    var nestedObject = property.GetValue(response);
                    if (nestedObject != null)
                    {
                        await ConvertDateTimeInDataAsync(nestedObject);
                    }
                }
            }

            return response;
        }

        private async Task ConvertDateTimeInDataAsync(object data)
        {
            if (data == null)
                return;

            // Handle collections
            if (data is IEnumerable<object> collection && !(data is string))
            {
                foreach (var item in collection)
                {
                    if (item != null)
                    {
                        await ConvertDateTimeInDataAsync(item);
                    }
                }
                return;
            }

            var properties = data.GetType().GetProperties();
            foreach (var property in properties)
            {
                if (property.PropertyType == typeof(DateTime))
                {
                    var dateTimeValue = (DateTime)property.GetValue(data);
                    var localDateTimeValue = dateTimeValue.ToLocalTime();
                    property.SetValue(data, localDateTimeValue);
                }
                else if (property.PropertyType == typeof(DateTime?))
                {
                    var nullableDateTimeValue = property.GetValue(data) as DateTime?;
                    if (nullableDateTimeValue.HasValue)
                    {
                        var localDateTimeValue = nullableDateTimeValue.Value.ToLocalTime();
                        property.SetValue(data, localDateTimeValue);
                    }
                }
                else if (property.PropertyType.IsClass && property.PropertyType != typeof(string))
                {
                    var nestedData = property.GetValue(data);
                    if (nestedData != null)
                    {
                        await ConvertDateTimeInDataAsync(nestedData);
                    }
                }
            }
        }
    }
}
