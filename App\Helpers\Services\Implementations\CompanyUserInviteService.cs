using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.Contract;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class CompanyUserInviteService : ICompanyUserInvite
    {
        private JobProDbContext Db;
        public CompanyUserInviteService(JobProDbContext publicSchemaDbContext) => Db = publicSchemaDbContext;

        #region Create Or Update Invite
        public async Task<bool> CreateOrUpdateInvite(CompanyUserInviteVM model, string tenantId)
        {
            // Check that the email is valid
            if (!model.Email.IsEmailValid())
            {
                return false;
            }

            var invite = Db.CompanyUserInvites
                .FirstOrDefault(x => x.Email == model.Email && x.Application == model.Application && x.TenantId == tenantId);
            if (invite == null)
            {
                invite = new CompanyUserInvite(true)
                {
                    Id = Guid.NewGuid().ToString(),
                    DateCreated = DateTime.UtcNow,
                    LastUpdate = DateTime.UtcNow,
                    Email = model.Email,
                    Status = "pending-finalization",
                    TenantId = tenantId,
                    Application = model.Application
                };
                await Db.AddAsync(invite);
            }
            else
            {
                invite.LastUpdate = DateTime.UtcNow;
                invite.Status = "pending-finalization";
                Db.CompanyUserInvites.Update(invite);
            }
            var result = await Db.SaveChangesAsync();
            if (result > 0) return true;
            return false;
        }
        #endregion

        #region Get Invites
        public async Task<List<CompanyUserInviteVM>> GetInvites(string tenantId, PaginationParameters paginationParameter, CompanyUserInviteStatus status = CompanyUserInviteStatus.PendingFinalization)
        {
            var statusStr = status == CompanyUserInviteStatus.PendingFinalization ? "pending-finalization" : status.ToString();
            var _invites = Db.CompanyUserInvites.Where(x => x.TenantId == tenantId && x.Status == statusStr);

            if (paginationParameter.StartDate != null && paginationParameter.EndDate == null)
            {
                _invites = _invites.Where(x => x.LastUpdate >= paginationParameter.StartDate);
            }

            if (paginationParameter.StartDate == null && paginationParameter.EndDate != null)
            {
                _invites = _invites.Where(x => x.LastUpdate <= paginationParameter.EndDate);
            }

            if (paginationParameter.StartDate != null && paginationParameter.EndDate != null)
            {
                _invites = _invites.Where(x => x.LastUpdate >= paginationParameter.StartDate && x.LastUpdate <= paginationParameter.EndDate);
            }

            if (paginationParameter.AlphabeticalOrder.Value)
            {
                _invites = _invites.OrderBy(x => x.Email);
            }

            if (paginationParameter.Recent.Value)
            {
                _invites = _invites.OrderByDescending(x => x.LastUpdate);
            }

            return await _invites.Select(x => x.Map()).ToListAsync();
        }
        #endregion

        #region Get Invite By Email
        public async Task<CompanyUserInviteVM> GetInvite(string email)
        {
            var _invite = await Db.CompanyUserInvites.FirstOrDefaultAsync(x => x.Email == email);
            return _invite?.Map();
        }
        #endregion

        #region Get Invite By Code/Token
        public CompanyUserInviteVM GetInviteByCode(string code)
        {
            var _invite = Db.CompanyUserInvites.FirstOrDefault(x => x.InviteCode == code);
            return _invite?.Map();
        }
        #endregion

        #region Update Invite
        public async Task<bool> UpdateInvite(CompanyUserInviteVM model)
        {
            var _invite = Db.CompanyUserInvites.FirstOrDefault(x => x.Email.ToLower() == model.Email.ToLower());
            if (_invite == null)
            {
                throw new Exception("Invite not found.");
            }
            else
            {
                _invite.LastUpdate = DateTime.UtcNow;
                _invite.Status = model.Status;
                Db.CompanyUserInvites.Update(_invite);
            }
            var result = await Db.SaveChangesAsync();
            if (result > 0) return true;
            return false;
        }
        #endregion
    }
}