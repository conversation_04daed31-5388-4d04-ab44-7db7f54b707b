{"ElasticConfiguration": {"Uri": "https://jobpro.es.us-central1.gcp.cloud.es.io", "CloudId": "Zarttech:dXMtY2VudHJhbDEuZ2NwLmNsb3VkLmVzLmlvOjQ0MyRlZmNlZWNjMmM5MjQ0ZGJiYWUzMGY3OWVlNTQ0MTZhZCQ2N2IxYzRhZjZjNTY0MjQwOGM1YjRiNjgzOGZhZDZiNA==", "Username": "elastic", "Password": "uGT1QlGNpJQ9GRuVcddQxJnN"}, "ConnectionStrings": {"ConnectionString": "Host=jobpro-dev.clciecgggu9z.eu-central-1.rds.amazonaws.com;Port=5432;Database=jobprodb;Username=postgres;Password=**********************$|SlNR;Include Error Detail=true;MaxPoolSize=100;Timeout=60;Pooling=true;MinPoolSize=1;ConnectionIdleLifetime=300", "LocalConnectionString": "Host=localhost;Port=5432;Database=Jobpro;Username=postgres;Password=*****;Include Error Detail=true;MaxPoolSize=100;Timeout=60;Pooling=true;MinPoolSize=1;ConnectionIdleLifetime=300"}, "HangfireCredentials": {"Username": "super-*****", "Password": "Jobpro@2024"}, "2FAIssuer": "<PERSON><PERSON>", "AccountClosureTicketCategoryId": "287e3c78-d583-453e-8297-1c834c5e2095", "Jwt": {"SuperAdmin": {"Issuer": "app.jobpro.com", "Audience": "app.jobpro.com", "Key": "thdllsldd8kjfkdsslald90kp00okmmsddskaksmssml", "ExpiryInMinutes": 240}, "Grpc": {"Issuer": "pactocoin.com", "Audience": "pactocoin.com", "Key": "staging:qadshfkdkjfkdsljjjsksyh23gdbs8y7777dnsndbnn", "ExpiryInMinutes": 3000}}, "RabbitMQConfiguration": {"Host": "amqps://user:<EMAIL>", "Port": "5672", "Username": "user", "Password": "N91zFLPqoYSASqd9"}, "AWSConfigOptions": {"AccessKey": "********************", "BucketName": "jobpro-public-dev-eu-central-1-************", "SecretKey": "7VoFakO5IYjiKSxkZ0L28RHlzqrxrB3UpRhZIs56", "Region": "eu-central-1", "ElasticCache": "master.redis-cluster.qop9dv.euc1.cache.amazonaws.com:6379", "ElasticCachePassword": "^h1r[R6PT8omXprpmK{U"}, "WikiFileUploadSettings": {"DirectUploadThreshold": ********, "BackgroundUploadThreshold": *********, "MultipartChunkSize": 5242880}, "FreePlanDays": 15, "NumberOfFreeUsers": 3, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "JwtKey": "staging!###!!!!=--=-90-&%^][ghg0gvtftf1!!@@%556872%$#$#$%@$}^^^^secretkey!98765", "Twilio": {"AccountSid": "**********************************", "AuthToken": "134e2aa7e61fdde8d4644a2b17a17213", "PhoneNumber": "+***********", "CallbackUrl": "https://your-api-base-url/api/phonenumber/call-status", "RecordingCallbackUrl": "https://your-api-base-url/api/phonenumber/recording-status", "ApiKey": "**********************************", "ApiSecret": "oiOykxgzCIejyqLsaNZGQJZPLguB1tPj", "TwiMLAppSid": "AP2c510e2bde48467c6a2e42c716e44a1f", "VoipPushCredentialSid": "CRc5802d4ca703df37cc9abcacddbabe3d", "AndroidFcmPushCredentialSid": "CR4b4641d24966a9f7a678c1395dab8cb3", "EnableTollFree": true, "QueueCalls": false, "ApiBaseUrl": "https://api.twilio.com"}, "EchoUrl": {"BaseUrl": "https://api.pactocoin.com/api/"}, "Mollie": {"ApiKey": "test_Tqg35AaWK664zscK9zbgC3Cv5W3PbA"}, "RtcNotification": {"ApiKey": "staging-qvQkMoFad0F6hCzl6PBaBnYpF7TNi1KNt7daVDJm9CCIBZKW"}, "StripeOptions": {"PublishableKey": "pk_test_51JcTyPDdszWIpiLLS8tA7976h5zKdVNgfY8Tq4VaRL1RgaMH66JJoSQfUX93hn9Hd5upNbcoLrrB8Ac3EHx0iMPy00xJfUTEks", "SecretKey": "sk_test_51JcTyPDdszWIpiLLUDn2tcJsAXDHnS5Cn4dXoJc6zaeWBkjquq4j8U6aVka1MyWgxyLpos659bodtewoZ7Ld8yAI00qF3qjwPh", "WebhookSecret": "whsec_KZ2jiPwhYxJTXbsCPtHZMarrPIWVrwXx", "WalletWebHookSecret": "whsec_qpSGHo1kk702dg5JpOD1jGdJy02qI4NT"}, "WatchDogCredentials": {"Username": "*****", "Password": "*****"}, "GCPConfigOptions": {"ProjectId": "C:\\Users\\<USER>\\Desktop\\Zarttech\\vocal-framework-390413-eaadcd7a67a1.json", "BucketName": "jobpro-bucket", "ClientEmail": "", "PrivateKey": ""}, "Redis": "redis://default:bmRrc2hha2hmYWRzdWZnaGlkbHVzZml1Z2RlaWhmbGRobGtmaG9pZHdoZmxpdWdydWZoa2pkaGZramh3ZGl1Z2ZpdQ==@*************:6379/0", "SendGridAPIKey": "*********************************************************************", "MAILGUN_API_KEY": "**************************************************", "MAILGUN_DOMAIN": "mg.jobpro.app", "RTC_API_KEY": "2b26b1b52d677d3f3c786134f7459f781b48a2cfe144e1ce", "Payments": {"Stripe": {"SecretKey": "your-stripe-secret-key", "PublicKey": "your-stripe-public-key", "WebhookSecret": "your-stripe-webhook-secret"}, "Mollie": {"ApiKey": "your-mollie-api-key"}}, "PhoneNumber": {"DefaultCallType": "voice", "MaxConcurrentCalls": 100, "CallTimeoutSeconds": 30, "EnableCallAnalytics": true, "EnableRecording": false}, "LiveKit": {"ServerUrl": "wss://phone-agent-try-out-mo0p7zmb.livekit.cloud", "ApiKey": "APICDfX49ehygzK", "ApiSecret": "tbno6fprES5w1r8mIwzFHa36161Xv3fOdo786kGJMxx", "RestApiUrl": "https://phone-agent-try-out-mo0p7zmb.livekit.cloud", "DefaultRoomTimeout": 3600, "MaxParticipants": 10, "EnableRecording": true, "EnableTranscription": false, "AudioOnlyMode": true, "OptimizedForTelephony": true, "RecordingFolderPrefix": "livekit-recordings"}, "MediaBridge": {"EnablePstnWebRtcBridge": true, "MaxConcurrentSessions": 100, "SessionTimeoutMinutes": 60, "AudioBufferSizeKb": 64, "EnableAudioTranscoding": true, "LogAudioActivity": false, "CleanupIntervalMinutes": 5}, "PhoneNumberMaintenance": {"FeePerNumber": 2.0, "MaxRetryAttempts": 3, "RetryIntervalDays": 7}, "AWS": {"Region": "your-aws-region"}, "BaseUrl": "https://your-api-base-url"}