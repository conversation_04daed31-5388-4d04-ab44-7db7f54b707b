﻿using Jobid.App.ActivityLog.Model;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.contract
{
    public interface IActivityService
    {
        Task<bool> CreateLog(ActivityDto model);
        Task<Page<Activity>> GetActivities(PaginationParameters paginationParameters);
        Task<GenericResponse> GetMonthlyActivityCounts(Applications applications);
        Task<GenericResponse> GetTenantsCounts(ActivityQueryParameters activityQueryParameters);
        Task<GenericResponse> GetActivitiesWithFilters(AcitivityLogFilters filters);
        Task<bool> SetActivitySettings(ActivitySettingsDto activitySettingDto);
        Task<bool> RequestForPermissions(List<ActivityRequestedPermisssionsDto> model);
        Task<List<ActivityRequestedPermisssions>> GetRequestedPermissionPerUser(string userId);
        Task<bool> GrantOrRejectActivityPermissionsRequest(string requestId, ApprovalStatus status);
        Task<List<ActivityRequestedPermisssions>> GetPermissionRequestsByUser(string requesterId);
        Task<bool> RequestForPermissionsForTeamMembers(ActivityRequestedPermisssionsForTeamMembersDto model);
        Task<List<UserDto>> GetUsersThatGrantedAccessToLoggedInUser(string userId);
        Task<GenericResponse> ShareActivity(ShareActivityDto model, string loggedInUserId);
        Task<bool> CheckIfUserHasGrantedPermission(string userId, EventCategory eventCategory, string subdomain = null, bool isBulkTodoUpload = false);
        Task<GenericResponse> GetActivitySettings(string userId);
        Task<GenericResponse> GetCompanyActivitySettings(string subdomain);
    }
}
