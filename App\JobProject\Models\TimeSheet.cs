﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.JobProjectManagement.Models
{
    public class TimeSheet
    {
        [Key]
        public Guid Id { get; set; }
        public string TodoName { get; set; }
        public string? Summary { get; set; }
        public string CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTime? DateLogged { get; set; }
        public string TimeSpent { get; set; }
        public string AssignedTo { get; set; }
        public DateTime? LastUpdate { get; set; }
        public string? Description { get; set; }
        public ICollection<ProjectFile> ProjectFiles { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime StartDate { get; set; }
        public string Duration { get; set; }
        public TimeSheetStatus Status { get; set; } = TimeSheetStatus.Completed;
        public bool IsBillable { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal? AmountPerHour { get; set; }
        public string ActualTimeSpent { get; set; }
        public TimeSheetPriority? Priority { get; set; }
        public ICollection<ProjectTag> ProjectTags { get; set; }
        public string Comments { get; set; }
        public bool IsArchived { get; set; } = false;
        public string ProjectMgmt_ProjectId { get; set; }
        public ProjectMgmt_Project projectMgmt_Project { get; set; }
        public string UserId { get; set; }
        public string SprintId { get; set; }
        public string? ClientName { get; set; }
        public DateTime? CompletionDate { get; set; }
    }
}