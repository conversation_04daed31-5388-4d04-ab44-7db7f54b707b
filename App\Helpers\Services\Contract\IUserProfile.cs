using System.Threading.Tasks;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.Tenant.Contract
{
    public interface IUserProfileServices
    {
        /// <summary>
        /// This gets the user profile details with company details
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<ApiResponse<UserProfile>> GetTenantUserProfileFromPublicUserId(string userId);

        /// <summary>
        /// This gets all the user in jobpro with company details
        /// </summary>
        /// <returns></returns>
        Task<GenericResponse> GetAllUserInJobproWithCompanyDetails();

        /// <summary>
        /// This sends an email verification request to a company's super admin
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> SendVerificationRequest(SendVerificationRequestDto model);
    }
}
