﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class wiki_tables_updates_more : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public wiki_tables_updates_more(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "UploadedDate",
                schema: _Schema,
                table: "WikiFiles",
                newName: "CreatedDate");

            migrationBuilder.RenameColumn(
                name: "UploadedById",
                schema: _Schema,
                table: "WikiFiles",
                newName: "CreatedBy");

            migrationBuilder.AlterColumn<string>(
                name: "FileType",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<long>(
                name: "FileSize",
                schema: _Schema,
                table: "WikiFiles",
                type: "bigint",
                nullable: true,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AlterColumn<string>(
                name: "FileName",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "AwsKey",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CreatedDate",
                schema: _Schema,
                table: "WikiFiles",
                newName: "UploadedDate");

            migrationBuilder.RenameColumn(
                name: "CreatedBy",
                schema: _Schema,
                table: "WikiFiles",
                newName: "UploadedById");

            migrationBuilder.AlterColumn<string>(
                name: "FileType",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<long>(
                name: "FileSize",
                schema: _Schema,
                table: "WikiFiles",
                type: "bigint",
                nullable: false,
                defaultValue: 0L,
                oldClrType: typeof(long),
                oldType: "bigint",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FileName",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "AwsKey",
                schema: _Schema,
                table: "WikiFiles",
                type: "character varying(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(500)",
                oldMaxLength: 500,
                oldNullable: true);
        }
    }
}
