﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface IProjectServices
    {
        Task<bool> Edit_Project(ProjectMgmt_ProjectVM model, string userid, string projectId);
        Task<GenericResponse> GetProjectAnalyticData(string projectId, DateTime startDate, DateTime endDate);
        Task<bool> MakeProjectActive(string projectId);
        Task<ProjectMgmt_Project> Add_Project(AddProjectDto modelDto, string userId, string subDomain);
        Task<List<ProjectFile>> GetUplaodedProjectFiles(string projectId);
        Task<Page<ProjectMgmt_Project>> GetProjectByUserId(string userId, PaginationParameters parameters);
        Task<Page<ProjectMgmt_Project>> GetAllProjectMgmt_Projects(PaginationParameters parameters);
        Task<ProjectMgmt_Project> GetProjectMgmt_SprintId(Guid id);
        Task<GenericResponse> SearchAllProjectAndTodoAndSprint(string nameParam);
        Task<ProjectMgmt_Project> GetProjectMgmt_ProjectId(Guid id);
        Task<ActualProjectValueResponse> GetActualProjectValue(string projectId);
        Task<bool> UpdateProjectMgmt_Project(ProjectMgmt_ProjectUpdateVM model, ProjectMgmt_Project project, string loggedInUserId);
        Task<bool> AddMembersToProject(ProjectMembersVm projectMembersVm, ProjectMgmt_Project projectMgmt_Project);
        Task<List<UserDto>> GetProjectMembers(Guid ProjectId);
        Task<ProjectReportVm> GetTotalProjectCountReport();
        Task<ProjectReportVm> GetTotalProjectReportById(string projectId);
        Task<ProjectMgmt_Project> UpdateProjectStatus(Guid Id, ProjectStatus projectStatus, string loggedInUserId);
        Task<bool> DeleteProjectById(string Id, string loggedInUserId);
        Task<bool> AddAmountPerUser(string projectId, List<string> userIds, decimal amount, string currency);
        Task<List<ProjectReportSummaryDto>> GetProjectReportSummary();
        Task<ProjectReportDetailsDto> GetProjectReportDetails(Guid projectId);
        Task<ProjectPDFExportReportSummaryDto> GetProjectPDFExportReportSummary(Guid projectId);
        Task<List<ProjectCountStatisticDto>> GetProjectCount();
        Task<List<TopPerformingCompanyDto>> GetTopPerformingCompaniesPercentage();
        Task<Page<ProjectCreationCompanyDetail>> GetProjectCompanyDetail(CompanyProjectFilter filter, int pageSize, int pageNumber);
        Task<Page<CompanyProjectMetrics>> GetProjectMetrics(ProjectMetricsQueryParameters queryParameters);
        Task<ProjectMetricPercentage> GetProjectMetricsSummary(ProjectMetricsQueryParameters queryParameters);
        Task<GenericResponse> GetAllProjectsWithSprints();
    }
}
