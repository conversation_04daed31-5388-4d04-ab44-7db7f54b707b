using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Jobid.App.JobProject.Enums
{
    public class Enums
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public enum WikiAccessStatus
        {
            Access = 1,
            NoAccess
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum TodoFilterForMobile
        {
            Personal = 1,
            OthersTodo,
            All,
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum ActivityLogDuration
        {
            All = 1,
            Today,
            Yesterday,
            ThisWeek,
            LastWeek,
            ThisMonth,
            LastMonth,
            ThisYear,
            LastYear,
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum EventCategory
        {
            All,
            Project,
            ProjectTrigger,
            Sprint,
            Todo,
            Calender,
            TeamSheet,
            TimeSheet,
            Trigger,
            User,
            Admin,
            Chat,
            Call,
            Application,
            Activity,
            Comment,
            CommentUpdate,
            ClientAdmin,
            Login,
            SalesModule,
            Campaign,
            ContactNote
        }

        [<PERSON>son<PERSON>onverter(typeof(StringEnumConverter))]
        public enum EraseAcitivity
        {
            Now = 1,
            After7days,
            After1Month,
            After3Months,
            After6Months,
            Never
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum LogActivity
        {
            All,
            Messages,
            Todos,
            Calls
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum TypeForInviteUrl
        {
            Todo,
            Sprint,
            Project,
            Team
        }

        [JsonConverter(typeof(StringEnumConverter))]
        public enum AmountFrequency
        {
            Hourly,
            Daily,
            Weekly,
            Monthly
        }
    }
}
