﻿using Jobid.App.Helpers.Enums;
using Jobid.App.JobProjectManagement.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class ProjectTriggerDto
    {
        public Guid Id { get; set; } = new Guid();
        public string TriggerName { get; set; }
        public string ProjectName { get; set; }
        public string TriggerReason { get; set; }
        public List<string> TriggerReasons { get; set; }
        public DateTime TriggerTime { get; set; }
        public string ParticipantsIds { get; set; }
        public List<TriggerSequenceDto> TriggerSequences { get; set; } = new List<TriggerSequenceDto> { };
        public bool Notification { get; set; }
        public bool SMS { get; set; }
        public bool Email { get; set; }
    }
}
