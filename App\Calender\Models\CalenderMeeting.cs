﻿using CsvHelper.Configuration.Attributes;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Calender.Models
{
    public class CalenderMeeting 
    {
        [Key]
        public Guid Id { get; set; }

        public string Name { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EndTime { get; set; }

        public string MeetingId { get; set; }

        public DateTime? EndDate { get; set; }

        public string Location { get; set; }

        public int? MeetLength { get; set; }

        public string MeetingDuration { get; set; }

        public bool MakeSchdulePrivate { get; set; }

        public NotifyMeVia NotifyMe { get; set; }

        public CalenderScheduleType ScheduleType { get; set; }

        public string Frequency { get; set; }

        public bool HasCustomFrequency { get; set; } = false;

        public string MeetingLink { get; set; }

        // This is in minutes. Eg 30 mins before meeting
        public int NotifyMembersIn { get; set; }

        public bool IsCancelled { get; set; } = false;

        public bool HasThisMeetingHappened { get; set; } = false;

        public Guid CreatedBy { get; set; }

        [DefaultValue(0)]
        public int RescheduleCount { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public CustomFrequency CustomFrequency { get; set; }
        
        [NotMapped]
        public CalendarMeetingResponse MeetingResponse { get; set; }

        [NotMapped]
        public List<UserMDVm> Members { get; set; } = new List<UserMDVm>();

        [NotMapped]
        public List<SubsequentMeeting> SubsequentMeetingDates { get; set; } = new List<SubsequentMeeting>();
        public string Attachment { get; set; }

        [NotMapped]
        public UserMDVm MeetingOwner { get; set; }  = new UserMDVm();

        [NotMapped]
        public List<string> ExternalMembers { get; set; } = new List<string>();

        [NotMapped]
        public List<string> AttachmentUrls { get; set; } = new List<string>();

        public MeetingOwnerTypes MeetingOwnerType { get; set; }

        public ProposedDateDetail? ProposedDateDetail { get; set; }

        [NotMapped]
        public string MeetingRecordingLink { get; set; }
        public bool CreatedByAI { get; set; } 
       
    }
}
