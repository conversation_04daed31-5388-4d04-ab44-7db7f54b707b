using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Dto
{    /// <summary>
     /// DTO for creating a new contact
     /// </summary>
    public class CreateUserContactDto
    {
        public string UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        [Required]
        [Phone]
        [StringLength(20)]
        public string PhoneNumber { get; set; }

        public string Industry { get; set; }
    }    /// <summary>
         /// DTO for updating an existing contact
         /// </summary>
    public class UpdateUserContactDto
    {
        [Required]
        public Guid Id { get; set; }

        public string UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; }

        [Required]
        [Phone]
        [StringLength(20)]
        public string PhoneNumber { get; set; }

        public string Industry { get; set; }
    }    /// <summary>
         /// DTO for contact response
         /// </summary>
    public class UserContactDto
    {
        public Guid Id { get; set; }
        public string UserId { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string Industry { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
    }

    /// <summary>
    /// DTO for paginated contact list
    /// </summary>
    public class PaginatedContactsDto
    {
        public List<UserContactDto> Contacts { get; set; } = new List<UserContactDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// DTO for contact upload response
    /// </summary>
    public class ContactUploadResponseDto
    {
        public int TotalProcessed { get; set; }
        public int SuccessfulUploads { get; set; }
        public int FailedUploads { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
    }

    /// <summary>
    /// DTO for bulk contact creation
    /// </summary>
    public class BulkCreateContactsDto
    {
        public List<CreateUserContactDto> Contacts { get; set; } = new List<CreateUserContactDto>();
    }
}
