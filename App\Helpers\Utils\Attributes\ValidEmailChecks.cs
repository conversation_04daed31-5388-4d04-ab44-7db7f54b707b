﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.RegularExpressions;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class ValidEmailChecks : ValidationAttribute
    {
        public ValidEmailChecks() { }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success;
            }

            var emails = value as List<string>;
            if (emails is not null && emails.Any())
            {
                foreach (var email in emails)
                {
                    if (string.IsNullOrEmpty(email))
                    {
                        continue;
                    }

                    string pattern = @"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$";
                    Regex regex = new Regex(pattern);

                    // Check if the email matches the pattern
                    if (regex.IsMatch(email))
                    {
                        continue;
                    }
                    else
                    {
                        return new ValidationResult($"Email - {email} is not valid");
                    }
                }
            }

            return ValidationResult.Success;
        }
    }
}
