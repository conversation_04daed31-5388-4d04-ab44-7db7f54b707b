﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added_tbls_for_telephony : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added_tbls_for_telephony(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AnsweredAt",
                schema: _Schema,
                table: "CallRecords",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AnsweredBy",
                schema: _Schema,
                table: "CallRecords",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "LiveKitRooms",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RoomName = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberId = table.Column<Guid>(type: "uuid", nullable: true),
                    CallRecordId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    MaxParticipants = table.Column<int>(type: "integer", nullable: false),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    ClosedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    WebSocketUrl = table.Column<string>(type: "text", nullable: true),
                    ServerUrl = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LiveKitRooms", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LiveKitParticipants",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    LiveKitRoomId = table.Column<Guid>(type: "uuid", nullable: false),
                    ParticipantIdentity = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    AccessToken = table.Column<string>(type: "text", nullable: true),
                    JoinedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LeftAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LiveKitParticipants", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LiveKitParticipants_LiveKitRooms_LiveKitRoomId",
                        column: x => x.LiveKitRoomId,
                        principalSchema: _Schema,
                        principalTable: "LiveKitRooms",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LiveKitParticipants_LiveKitRoomId",
                schema: _Schema,
                table: "LiveKitParticipants",
                column: "LiveKitRoomId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LiveKitParticipants",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "LiveKitRooms",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "AnsweredAt",
                schema: _Schema,
                table: "CallRecords");

            migrationBuilder.DropColumn(
                name: "AnsweredBy",
                schema: _Schema,
                table: "CallRecords");
        }
    }
}
