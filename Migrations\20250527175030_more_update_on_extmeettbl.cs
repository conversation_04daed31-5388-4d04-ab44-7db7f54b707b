﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class more_update_on_extmeettbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public more_update_on_extmeettbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MaxNoOfBookingsPerSlot",
                schema: _Schema,
                table: "ExternalMeeting",
                type: "integer",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MaxNoOfBookingsPerSlot",
                schema: _Schema,
                table: "ExternalMeeting");
        }
    }
}
