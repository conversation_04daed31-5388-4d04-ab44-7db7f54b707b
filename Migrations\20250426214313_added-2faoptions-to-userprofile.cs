﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added2faoptionstouserprofile : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added2faoptionstouserprofile(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TwoFactorSecretKey",
                schema: _Schema,
                table: "UserProfiles",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "_2FAOptions",
                schema: _Schema,
                table: "UserProfiles",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "ProposedDateDetails",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    MeetingId = table.Column<Guid>(type: "uuid", nullable: false),
                    NewProposedStartDateAndTime = table.Column<DateTime>(type: "timestamp", nullable: false),
                    NewProposedEndDateAndTime = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: true),
                    ProposedByEmail = table.Column<string>(type: "text", nullable: true),
                    PropsedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    AcceptedOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProposedDateDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProposedDateDetails_CalenderMeetings_MeetingId",
                        column: x => x.MeetingId,
                        principalSchema: _Schema,
                        principalTable: "CalenderMeetings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ProposedDateDetails_MeetingId",
                schema: _Schema,
                table: "ProposedDateDetails",
                column: "MeetingId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ProposedDateDetails",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "TwoFactorSecretKey",
                schema: _Schema,
                table: "UserProfiles");

            migrationBuilder.DropColumn(
                name: "_2FAOptions",
                schema: _Schema,
                table: "UserProfiles");
        }
    }
}
