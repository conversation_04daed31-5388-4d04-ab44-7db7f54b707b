syntax = "proto3";

option csharp_namespace = "Jobid";

service CommunicationService {
    rpc FetchAvailableNumber (FetchAvailableNumberRequest) returns (FetchAvailableNumbersResponse) {}
    rpc BuyPhoneNumber (BuyPhoneNumberRequest) returns (BuyPhoneNumberResponse) {}
    rpc MakeCall (callMakingRequest) returns (callMakingResponse) {}
    rpc HandleCallBack (handleCallBackRequest) returns (handleCallBackResponse) {}
    rpc CallHistory (callHistoryRequest) returns (callHistoryResponse) {}
    rpc AssignPhoneNumber (assignPhoneNumberRequest) returns (assignPhoneNumberResponse) {}
    rpc EndCall (endCallRequest) returns (endCallResponse) {}
    rpc CallDetails (callDetailsRequest) returns (callDetailsResponse) {}
}
message callDetailsRequest {
    string callSid = 1;
}
message callDetailsResponse {
    string duration = 1;
    string start_time = 2;
    string end_time = 3;
    string price = 4;
    string price_unit = 5;
    string status = 6;
    string to = 7;
    string calledCountry = 8;
    string callSid = 9;
}
message endCallRequest {
    string callSid = 1;
}
message endCallResponse {
    string message = 1;
}
message assignPhoneNumberRequest {
    string purchasedPhoneNumber = 1;
    string tenantMetadata = 2;
    string userID = 3;
}
message assignPhoneNumberResponse {
    string message = 1;
}
message callHistoryRequest {
    string userMetadata = 1;
}
message callHistoryResponse {
    repeated callMakingResponse callHistory = 1;
}
message handleCallBackResponse {
    string message = 1;
}
message handleCallBackRequest {
    string Price = 1;
    string CallStatus = 2;
    string CallDuration = 3;
    string callIdentifier = 4;
    string CalledCountry = 5;
}
message callMakingRequest {
    string receiverPhoneNumber = 1;
    string userMetadata = 2;
}
message callMakingResponse {
    string duration = 1;
    string start_time = 2;
    string end_time = 3;
    string price = 4;
    string price_unit = 5;
    string status = 6;
    string to = 7;
    string calledCountry = 8;
    string callSid = 9;
}
message BuyPhoneNumberRequest {
    string phoneNumber = 1;
    string tenantMetadata = 2;
}
message BuyPhoneNumberResponse {
    string friendlyName = 1;
    string phoneNumber = 2;
    string locality = 3;
    string latitude = 4;
    string longitude = 5;
    string postalCode = 6;
    string isoCountry = 7;
    Capabilities capabilities = 8;
    string sms_application_sid = 9;
    string sms_fallback_method = 10;
    string sms_fallback_url = 11;
    string sms_method = 12;
    string sms_url = 13;
    string status_callback = 14;
    string status_callback_method = 15;
    string trunk_sid = 16;
    string uri = 17;
    string voice_application_sid = 18;
    string voice_caller_id_lookup = 19;
    string voice_fallback_method = 20;
    string voice_fallback_url = 21;
    string voice_method = 22;
    string voice_url = 23;
    string bundle_sid = 24;
    string voice_receive_mode = 25;
    string status = 26;
}
message FetchAvailableNumberRequest {
    string countryCode = 1;
    string metadata = 2;
}
message FetchAvailableNumbersResponse {
    repeated PhoneNumber availableNumbers = 1;
}
message PhoneNumber {
    string friendlyName = 1;
    string phoneNumber = 2;
    string locality = 3;
    string latitude = 4;
    string longitude = 5;
    string postalCode = 6;
    string isoCountry = 7;
    Capabilities capabilities = 8;
  }
  message Capabilities {
    bool voice = 1;
    bool SMS = 2;
    bool MMS = 3;
  }


/* service CommunicationService {
    rpc FetchAvailableNumber (FetchAvailableNumberRequest) returns (FetchAvailableNumbersResponse) {}
    rpc BuyPhoneNumber (BuyPhoneNumberRequest) returns (BuyPhoneNumberResponse) {}
    rpc MakeCall (callMakingRequest) returns (callMakingResponse) {}
    rpc HandleCallBack (handleCallBackRequest) returns (handleCallBackResponse) {}
    rpc CallHistory (callHistoryRequest) returns (callHistoryResponse) {}
    rpc AssignPhoneNumber (assignPhoneNumberRequest) returns (assignPhoneNumberResponse) {}
}
message assignPhoneNumberRequest {
    string purchasedPhoneNumber = 1;
    string tenantMetadata = 2;
    string userID = 3;
}
message assignPhoneNumberResponse {
    string message = 1;
}
message callHistoryRequest {
    string userMetadata = 1;
}
message callHistoryResponse {
    repeated callMakingResponse callHistory = 1;
}
message handleCallBackResponse {
    string message = 1;
}
message handleCallBackRequest {
    string Price = 1;
    string CallStatus = 2;
    string CallDuration = 3;
    string callIdentifier = 4;
    string CalledCountry = 5;
}
message callMakingRequest {
    string receiverPhoneNumber = 1;
    string userMetadata = 2;
}
message callMakingResponse {
    string duration = 1;
    string start_time = 2;
    string end_time = 3;
    string price = 4;
    string price_unit = 5;
    string status = 6;
    string to = 7;
}
message BuyPhoneNumberRequest {
    string phoneNumber = 1;
    string tenantMetadata = 2;
}
message BuyPhoneNumberResponse {
    string friendlyName = 1;
    string phoneNumber = 2;
    string locality = 3;
    string latitude = 4;
    string longitude = 5;
    string postalCode = 6;
    string isoCountry = 7;
    Capabilities capabilities = 8;
    string sms_application_sid = 9;
    string sms_fallback_method = 10;
    string sms_fallback_url = 11;
    string sms_method = 12;
    string sms_url = 13;
    string status_callback = 14;
    string status_callback_method = 15;
    string trunk_sid = 16;
    string uri = 17;
    string voice_application_sid = 18;
    string voice_caller_id_lookup = 19;
    string voice_fallback_method = 20;
    string voice_fallback_url = 21;
    string voice_method = 22;
    string voice_url = 23;
    string bundle_sid = 24;
    string voice_receive_mode = 25;
    string status = 26;
}
message FetchAvailableNumberRequest {
    string countryCode = 1;
    string metadata = 2;
}
message FetchAvailableNumbersResponse {
    repeated PhoneNumber availableNumbers = 1;
}
message PhoneNumber {
    string friendlyName = 1;
    string phoneNumber = 2;
    string locality = 3;
    string latitude = 4;
    string longitude = 5;
    string postalCode = 6;
    string isoCountry = 7;
    Capabilities capabilities = 8;
  }
  message Capabilities {
    bool voice = 1;
    bool SMS = 2;
    bool MMS = 3;
  } */

