using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Tenant.Model
{
    public class Tenant
    {
        [Key]
        public Guid Id { get; set; }
        public string CompanyName { get; set; }
        public string CompanyAddress { get; set; }
        public string WorkSpace { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string VerifiedEmailDomain { get; set; }
        public DateTime DateCreated { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime LastUpdate { get; set; }
        public bool isSchemaCreated { get; set; }
        public DateTime LastMigration { get; set; }
        public string AdminId { get; set; }

        [ForeignKey("AdminId")]
        public virtual User Admin { get; set; }
        public string LogoUrl { get; set; }
        public int CompanySize { get; set; }
        public string Status { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string Region { get; set; }
        public string CompanyType { get; set; }
        public string RegNumber { get; set; }

        [Column(TypeName = "varchar(24)")]
        public Industries Industry { get; set; }
    }
}
