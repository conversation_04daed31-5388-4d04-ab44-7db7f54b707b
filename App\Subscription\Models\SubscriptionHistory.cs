﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Models
{
    public class SubscriptionHistory
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public Guid PricingPlanId { get; set; }
        public string UserId { get; set; }
        public Guid? TenantId { get; set; }
        public string PaymentMethod { get; set; }
        public double Amount { get; set; }
        public string Status { get; set; }
        public string PaymentId { get; set; }
        public DateTime? TransactionDate { get; set; }
        public DateTime? ActivatedOn { get; set; }
        public DateTime? ExpiresOn { get; set; }
        public string MollieCustomerId { get; set; }
        public string StripeCustomerId { get; set; }
        public string SubscriptionId { get; set; }
        public string TransactionCode { get; set; }
        public Currency Currency { get; set; } = Currency.USD;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public int RetrySubAttempt { get; set; }
        public Applications Application { get; set; }
        public int? SubscriptionFor { get; set; }
        public string MandateId { get; set; }
        public string PayPalEmail { get; set; }
        public string PaypalBillingAgreementId { get; set; }
        public string MandateReference { get; set; }
        public string ConsumerAccount { get; set; }
        public Guid? BillingAddressId { get; set; }
        public PaymentProviders? PaymentProvider { get; set; }
        public string Interval { get; set; }
        public string StripePriceId { get; set; }
        public bool FreeTrialOptionSelected { get; set; }
        public int SubscriptionCount { get; set; }
        public bool IsCancelled { get; set; }

        // Navigation Properties
        public PricingPlan PricingPlan { get; set; }
        public Tenant.Model.Tenant Tenant { get; set; }
        public BillingAddress BillingAddress { get; set; }
    }
}
