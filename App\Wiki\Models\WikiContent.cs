using Jobid.App.Wiki.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Wiki.Models
{
    public class WikiContent
    {
        [Key]
        public Guid Id { get; set; }
        
        [StringLength(255)]
        public string FileName { get; set; }
        
        [StringLength(50)]
        public string FileType { get; set; }
        
        public long? FileSize { get; set; } // Size in bytes
        
        [StringLength(500)]
        public string AwsKey { get; set; }
        
        public WikiFileUploadStatus UploadStatus { get; set; }
        
        [Required]
        public DateTime CreatedDate { get; set; }
        
        [Required]
        public Guid CreatedBy { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public DateTime? LastAccessedDate { get; set; }
        
        public DateTime? DeletedDate { get; set; }
        
        public bool IsDeleted { get; set; }

        public bool IsAIProcessed { get; set; }

        // Batch identifier for grouping multiple files uploaded together
        [StringLength(100)]
        public string BatchId { get; set; }
        
        // Type of wiki file (Training material or Attachment)
        public WikiFileType WikiFileType { get; set; }
        public string TextContent { get; set; }

        // Navigation properties
        public virtual ICollection<WikiFileDepartmentAccess> DepartmentAccess { get; set; }
    }
}
