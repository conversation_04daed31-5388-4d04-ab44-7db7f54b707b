using System;
using System.Text.RegularExpressions;

namespace Jobid.App.Helpers.Utils
{
    /// <summary>
    /// Helper class for optimizing connection strings for different services
    /// </summary>
    public static class ConnectionStringOptimizer
    {
        /// <summary>
        /// Adds connection pooling parameters to a connection string
        /// </summary>
        /// <param name="connectionString">The original connection string</param>
        /// <param name="maxPoolSize">The maximum pool size</param>
        /// <param name="minPoolSize">The minimum pool size</param>
        /// <param name="connectionLifetime">The connection lifetime in seconds</param>
        /// <param name="connectionIdleLifetime">The connection idle lifetime in seconds</param>
        /// <returns>The optimized connection string</returns>
        public static string OptimizeConnectionString(string connectionString, int maxPoolSize = 100, int minPoolSize = 1, 
            int connectionLifetime = 300, int connectionIdleLifetime = 300)
        {
            if (string.IsNullOrEmpty(connectionString))
                return connectionString;

            // Create a new connection string with pooling settings
            var optimizedConnectionString = connectionString;

            // Add MaxPoolSize if not present
            if (!optimizedConnectionString.Contains("MaxPoolSize="))
            {
                optimizedConnectionString += $";MaxPoolSize={maxPoolSize}";
            }
            else
            {
                // Update MaxPoolSize if present
                optimizedConnectionString = Regex.Replace(
                    optimizedConnectionString,
                    @"MaxPoolSize=\d+",
                    $"MaxPoolSize={maxPoolSize}");
            }

            // Add MinPoolSize if not present
            if (!optimizedConnectionString.Contains("MinPoolSize="))
            {
                optimizedConnectionString += $";MinPoolSize={minPoolSize}";
            }

            // Add ConnectionLifetime if not present
            if (!optimizedConnectionString.Contains("ConnectionLifetime="))
            {
                optimizedConnectionString += $";ConnectionLifetime={connectionLifetime}";
            }

            // Add ConnectionIdleLifetime if not present
            if (!optimizedConnectionString.Contains("ConnectionIdleLifetime="))
            {
                optimizedConnectionString += $";ConnectionIdleLifetime={connectionIdleLifetime}";
            }

            // Ensure pooling is enabled
            if (!optimizedConnectionString.Contains("Pooling="))
            {
                optimizedConnectionString += ";Pooling=true";
            }

            return optimizedConnectionString;
        }

        /// <summary>
        /// Optimizes a connection string for Hangfire
        /// </summary>
        /// <param name="connectionString">The original connection string</param>
        /// <returns>The optimized connection string for Hangfire</returns>
        public static string OptimizeForHangfire(string connectionString)
        {
            // Hangfire should have a smaller pool since it's used less frequently
            return OptimizeConnectionString(connectionString, maxPoolSize: 20, minPoolSize: 1, 
                connectionLifetime: 300, connectionIdleLifetime: 300);
        }

        /// <summary>
        /// Optimizes a connection string for the main application
        /// </summary>
        /// <param name="connectionString">The original connection string</param>
        /// <returns>The optimized connection string for the main application</returns>
        public static string OptimizeForMainApp(string connectionString)
        {
            // Main application should have a larger pool
            return OptimizeConnectionString(connectionString, maxPoolSize: 100, minPoolSize: 5, 
                connectionLifetime: 600, connectionIdleLifetime: 300);
        }

        /// <summary>
        /// Optimizes a connection string for background services
        /// </summary>
        /// <param name="connectionString">The original connection string</param>
        /// <returns>The optimized connection string for background services</returns>
        public static string OptimizeForBackgroundServices(string connectionString)
        {
            // Background services should have a moderate pool
            return OptimizeConnectionString(connectionString, maxPoolSize: 30, minPoolSize: 2, 
                connectionLifetime: 300, connectionIdleLifetime: 300);
        }
    }
}
