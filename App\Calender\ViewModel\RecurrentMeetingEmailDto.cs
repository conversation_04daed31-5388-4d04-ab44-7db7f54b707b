﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Helpers.Models;
using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class RecurrentMeetingEmailDto
    {
        public string Invitee { get; set; }
        public string InviteeEmail {  get; set; }
        public string Template { get; set; }
        public List<string> InvitedMemberNames { get; set; }
        public List<UserProfile> InvitedUsers { get; set; }
        public List<string> InvitedMemberEmails { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public string AiName { get; set; }
        public string AiImage { get; set; }
        public List<string> FileUrls { get; set; }

    }
}
