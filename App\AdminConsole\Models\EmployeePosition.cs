﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    public class EmployeePosition
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public Guid DepartmentId { get; set; }
        public string PositionName { get; set; }
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long index { get; set; }
        public long BelongsTo { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
    }
}