﻿using Microsoft.AspNetCore.Http;
using System.Linq;
using System;

public class BrowserDetection
{
    private readonly HttpRequest _request;

    public BrowserDetection(HttpRequest request)
    {
        _request = request ?? throw new ArgumentNullException(nameof(request));
    }

    public BrowserInfoDto GetBrowserInfo()
    {
        string userAgent = _request.Headers["User-Agent"].ToString();
        var browserInfo = new BrowserInfoDto();

        // Check for common bots
        if (IsBot(userAgent))
        {
            browserInfo.IsBot = true;
            browserInfo.Name = "Bot";
            return browserInfo;
        }

        // Check for mobile devices
        browserInfo.IsMobile = IsMobile(userAgent);

        // Detect browser name and version
        if (userAgent.Contains("Firefox"))
        {
            browserInfo.Name = "Firefox";
            browserInfo.Version = GetVersion(userAgent, "Firefox/");
        }
        else if (userAgent.Contains("Chrome") && !userAgent.Contains("Chromium"))
        {
            browserInfo.Name = "Chrome";
            browserInfo.Version = GetVersion(userAgent, "Chrome/");
        }
        else if (userAgent.Contains("Safari") && !userAgent.Contains("Chrome"))
        {
            browserInfo.Name = "Safari";
            browserInfo.Version = GetVersion(userAgent, "Version/");
        }
        else if (userAgent.Contains("Edge"))
        {
            browserInfo.Name = "Edge";
            browserInfo.Version = GetVersion(userAgent, "Edge/");
        }
        else if (userAgent.Contains("MSIE") || userAgent.Contains("Trident/"))
        {
            browserInfo.Name = "Internet Explorer";
            browserInfo.Version = GetVersion(userAgent, "MSIE ");
        }
        else
        {
            browserInfo.Name = "Unknown";
            browserInfo.Version = "0.0";
        }

        // Detect platform
        if (userAgent.Contains("Windows"))
            browserInfo.Platform = "Windows";
        else if (userAgent.Contains("Mac"))
            browserInfo.Platform = "MacOS";
        else if (userAgent.Contains("Linux"))
            browserInfo.Platform = "Linux";
        else if (userAgent.Contains("Android"))
            browserInfo.Platform = "Android";
        else if (userAgent.Contains("iPhone") || userAgent.Contains("iPad"))
            browserInfo.Platform = "iOS";
        else
            browserInfo.Platform = "Unknown";

        return browserInfo;
    }

    private bool IsBot(string userAgent)
    {
        string[] botKeywords = new[] {
            "bot", "crawler", "spider", "googlebot", "bingbot", "slurp", "duckduckbot",
            "baiduspider", "yandexbot", "sogou", "exabot", "facebookexternalhit"
        };

        return botKeywords.Any(keyword =>
            userAgent.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsMobile(string userAgent)
    {
        string[] mobileKeywords = new[] {
            "Mobile", "Android", "iPhone", "iPad", "Windows Phone",
            "webOS", "BlackBerry", "Opera Mini", "Opera Mobi"
        };

        return mobileKeywords.Any(keyword =>
            userAgent.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    private string GetVersion(string userAgent, string browser)
    {
        try
        {
            int startIndex = userAgent.IndexOf(browser) + browser.Length;
            int endIndex = userAgent.IndexOf(' ', startIndex);
            if (endIndex == -1) endIndex = userAgent.Length;

            string version = userAgent.Substring(startIndex, endIndex - startIndex);
            return string.Join(".", version.Split('.').Take(2));
        }
        catch
        {
            return "0.0";
        }
    }

    public class BrowserInfoDto
    {
        public string Name { get; set; }
        public string Version { get; set; }
        public bool IsMobile { get; set; }
        public string Platform { get; set; }
        public bool IsBot { get; set; }
    }
}