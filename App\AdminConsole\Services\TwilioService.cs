﻿using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Twilio;
using Twilio.Rest.Api.V2010.Account;

namespace Jobid.App.AdminConsole.Services
{
    public class TwilioService : ITwilioService
    {
        private readonly IConfiguration _configuration;

        public TwilioService(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

            // Initialize Twilio
            var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? _configuration["Twilio:AccountSID"];
            var authToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN") ?? _configuration["Twilio:AuthToken"];
            TwilioClient.Init(accountSid, authToken);
        }

        /// <summary>
        ///  Get details of a specific call by its SID
        /// </summary>
        /// <param name="callSid"></param>
        /// <returns></returns>
        public async Task<CallDetailsDto> GetCallDetailsAsync(string callSid)
        {
            var call = await CallResource.FetchAsync(callSid);

            if (call == null)
                return null;

            return new CallDetailsDto
            {
                CallSid = call.Sid,
                AccountSid = call.AccountSid,
                From = call.From,
                To = call.To,
                CallerName = call.CallerName,
                Direction = call.Direction,
                Status = call.Status?.ToString(),
                Duration = call.Duration,
                StartTime = call.StartTime,
                EndTime = call.EndTime,
                Price = call.Price,
                PriceUnit = call.PriceUnit,
                ForwardedFrom = call.ForwardedFrom,
                PhoneNumberSid = call.PhoneNumberSid,
                ParentCallSid = call.ParentCallSid,
                AnsweredBy = call.AnsweredBy,
                ApiVersion = call.ApiVersion,
                Uri = call.Uri?.ToString(),

                RecordingsUri = call.SubresourceUris != null && call.SubresourceUris.TryGetValue("recordings", out var recordingsUri) ? recordingsUri : null,
                FeedbackUri = call.SubresourceUris != null && call.SubresourceUris.TryGetValue("feedback", out var feedbackUri) ? feedbackUri : null,
                NotificationsUri = call.SubresourceUris != null && call.SubresourceUris.TryGetValue("notifications", out var notificationsUri) ? notificationsUri : null
            };
        }

        public async Task<List<TranscriptionDto>> GetCallTranscriptionsAsync(string callSid)
        {
            var transcriptions = new List<TranscriptionDto>();

            try
            {
                var recordings = await RecordingResource.ReadAsync(callSid: callSid);

                foreach (var recording in recordings)
                {
                    var transcriptionList = await TranscriptionResource.ReadAsync(recording.Sid);

                    foreach (var transcription in transcriptionList)
                    {
                        // Extract RecordingSid from the URI
                        var recordingSid = ExtractRecordingSidFromUri(transcription.Uri?.ToString());

                        transcriptions.Add(new TranscriptionDto
                        {
                            TranscriptionSid = transcription.Sid,
                            RecordingSid = recordingSid,
                            Status = transcription.Status?.ToString(),
                            TranscriptionText = transcription.TranscriptionText,
                            Uri = transcription.Uri?.ToString()
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                throw new ApplicationException($"Failed to get transcriptions for call SID {callSid}: {ex.Message}", ex);
            }

            return transcriptions;
        }

        private string ExtractRecordingSidFromUri(string uri)
        {
            if (string.IsNullOrEmpty(uri))
                return null;

            // Example: /2010-04-01/Accounts/ACxxx/Recordings/RE1234567890abcdef/Transcriptions/TRxxx.json
            var segments = uri.Split('/');
            var recIndex = Array.IndexOf(segments, "Recordings");
            if (recIndex >= 0 && recIndex + 1 < segments.Length)
            {
                return segments[recIndex + 1];
            }

            return null;
        }
    }
}
