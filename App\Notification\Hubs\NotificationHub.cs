﻿using Microsoft.AspNetCore.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Notification.Hubs
{
    public sealed class NotificationHub : Hub<INotificationClient>
    {
        //public override Task OnConnectedAsync()
        //{
        //    var (email, role) = GetEmailAndRoleFromClaims();
        //    var connectionId = Context?.ConnectionId;

        //    if (!string.IsNullOrEmpty(connectionId) && !string.IsNullOrEmpty(role))
        //    {
        //        if (IsSuperAdminOrAdmin(role))
        //            Groups.AddToGroupAsync(connectionId, "SuperAdmin");
        //        ConnectionIdInfoMap[connectionId] = (email, role);
        //    }
        //    return base.OnConnectedAsync();
        //}

        //public override Task OnDisconnectedAsync(Exception? exception)
        //{
        //    var connectionId = Context?.ConnectionId;
        //    if (!string.IsNullOrEmpty(connectionId))
        //        ConnectionIdInfoMap.Remove(connectionId);

        //    return base.OnDisconnectedAsync(exception);
        //}
        //private static readonly Dictionary<string, (string email, string role)> ConnectionIdInfoMap = new();
        //private string GetEmailAndRoleFromClaims()
        //{
        //    var emailClaim = Context?.User?.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Email || c.Type == ClaimTypes.Email);
        //    var roleClaim = Context?.User?.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role);

        //    var email = emailClaim?.Value ?? string.Empty;
        //    var role = roleClaim?.Value ?? "Admin";

        //    return (email, role);
        //}
        //private static bool IsSuperAdminOrAdmin(string role)
        //    => role == "SuperAdmin" || role == "Admin";
        public async Task InvokeMethod()
            => await Clients.All.RecieveNotification();
    }

    public interface INotificationClient
    {
        Task RecieveNotification();
    }
}
