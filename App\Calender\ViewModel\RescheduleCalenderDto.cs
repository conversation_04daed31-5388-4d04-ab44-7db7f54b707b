﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class RescheduleCalenderDto
    {
        [Required]
        public Guid CalenderId { get; set; }

        [Required]
        public string UserId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndTime { get; set; }

        public DateTime EndDate { get; set; }

        public int NotifyMeInMinutes { get; set; }

        public string MeetingDuration { get; set; }

        [JsonIgnore]
        public Guid UpdatedBy { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
