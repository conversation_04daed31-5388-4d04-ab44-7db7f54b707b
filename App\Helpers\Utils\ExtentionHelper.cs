﻿using System.Linq;
using System.Security.Claims;

namespace Jobid.App.Helpers.Utils._Helper
{
    public static class ExtentionHelper
    {   
        public static string GetUserId(this ClaimsPrincipal claimsPrincipal)
            => claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;


        public static string GetUserName(this ClaimsPrincipal claimsPrincipal)
           => claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;

        public static string GetRegion(this ClaimsPrincipal claimsPrincipal)
           => claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Locality)?.Value;


        public static string GetUserEmail(this ClaimsPrincipal claimsPrincipal)
           => claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value;


        public static string GetUserRole(this ClaimsPrincipal claimsPrincipal)
          => claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value;
    }
}
