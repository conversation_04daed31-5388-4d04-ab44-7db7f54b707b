﻿using DocumentFormat.OpenXml.Office2021.DocumentTasks;
using Jobid.App.Notification.Models;
using Jobid.App.Notification.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Notification.Contracts
{
    public interface INotificationsService
    {
        Task<string> AddNotification(AddNotificationDto model);
        Task<bool> AddUserNotification(List<string> userIds, Guid notificationId);
        Task<IEnumerable<UserNotification>> GetUserNotifications(Guid userId);
        Task<bool> MarkNotificationAsRead(Guid notificationId, Guid userId, bool markAllAsRead);
        Task<bool> MarkAllNotificationsAsRead(Guid userId);
    }
}
