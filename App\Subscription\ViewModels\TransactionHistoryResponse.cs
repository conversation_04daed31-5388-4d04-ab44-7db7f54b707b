﻿using Jobid.App.Helpers.Enums;
using System;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.ViewModels
{
    public class TransactionHistoryResponse
    {
        public string CompanyName { get; set; }
        public DateTime? TransactionDate { get; set; }
        public double Amount { get; set; }
        public string Package { get; set; }
        public string TransactionType { get; set; }
        public PaymentProviders? PaymentProviders { get; set; }
        public string? Plans { get; set; }
    }

    public class GetAllWalletbalancesResponse
    {
        public double Amount { get; set; }
        public Applications Package { get; set; }
    }
}
