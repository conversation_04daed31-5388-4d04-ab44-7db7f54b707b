﻿using Jobid.App.Helpers.Context;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.Services.Contract;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class TenantClientService : ITenantClientService
    {

        #region Properties and Constructor
        private JobProDbContext Db;
        public JobProDbContext Dbo;

        public TenantClientService(JobProDbContext _db, JobProDbContext publicSchemaContext)
        {
            Db = _db;
            Dbo = publicSchemaContext;

        }


        #endregion

        //  public async Task<TenantClient> AddTenantClients(List<string> names) { }

        public async Task<List<TenantClient>> GetTenantClients()
        {
            var clients = await Db.TenantClients.ToListAsync();
            return clients;
        }
    }
}
