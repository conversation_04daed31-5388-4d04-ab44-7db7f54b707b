﻿using System.Collections.Generic;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.ViewModel
{
    public class ActivitySettingsDto
    {
        public List<string> UserIds { get; set; }
        public List<string> Categories { get; set; }
        public string TeamId { get; set; }
        public EraseAcitivity EraseAcitivity { get; set; } = EraseAcitivity.After3Months;
        public bool? LogActivity { get; set; }
        public bool? TurnOnOrOffForAllUsers { get; set; }

        public ActivitySettingsDto()
        {
            UserIds = new List<string>();
            Categories = new List<string>();
        }
    }
}
