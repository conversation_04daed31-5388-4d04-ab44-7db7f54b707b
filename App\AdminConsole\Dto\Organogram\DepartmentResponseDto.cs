﻿using System;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class DepartmentResponseDto
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public string DepartmentName { get; set; }
        public string BranchColor { get; set; }
        public long Index { get; set; }
        public long BelongsTo { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
    }
}
