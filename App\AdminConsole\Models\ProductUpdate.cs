﻿using Jobid.App.AdminConsole.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models
{
    public class ProductUpdate
    {
        [Key]
        public Guid Id { get; set; }
        public string Package { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public string ImageUrl { get; set; }
        public PublishStatus PublishStatus { get; set; }
        public string Category { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
