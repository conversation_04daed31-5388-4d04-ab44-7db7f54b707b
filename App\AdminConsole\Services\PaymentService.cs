using System;
using System.Threading.Tasks;
using Stripe;
using Mollie.Api.Models.Payment.Request;
using Microsoft.Extensions.Configuration;
using Jobid.App.AdminConsole.Contract;
using Mollie.Api.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Microsoft.EntityFrameworkCore;
using Jobid.App.Helpers.Extensions;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.Subscription.Configuration;
using Microsoft.Extensions.Options;
using System.Collections.Generic;
using System.Linq;
using static Jobid.App.Helpers.Utils.Utility;
using Mollie.Api.Client.Abstract;
using Jobid.App.AdminConsole.Models.Wallet;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;

namespace Jobid.App.AdminConsole.Services
{
    public class PaymentService : IPaymentService
    {
        private readonly string _baseUrl;
        private readonly string _adminUrl;
        private readonly IPaymentClient _paymentClient;
        private readonly JobProDbContext _publicContext;
        private readonly string _conString;
        private readonly JobProDbContext _context;
        private readonly IOptions<StripeOptions> _options;
        private readonly string _walletWebHookSecret;

        public PaymentService(IConfiguration configuration, JobProDbContext context, 
            IOptions<StripeOptions> options, IPaymentClient paymentClient, JobProDbContext publicContext)
        {
            _baseUrl = Constants.BACKEND_BASE_URL;
            _adminUrl = Constants.ADMIN_URL;
            _paymentClient = paymentClient;
            _publicContext = publicContext;
            _conString = GlobalVariables.ConnectionString;

            _context = context;
            _options = options;
            StripeConfiguration.ApiKey = options.Value.SecretKey;

            _walletWebHookSecret = configuration["StripeOptions:WalletWebHookSecret"];
        }        
        
        #region Initiate Stripe Payment
        public async Task<string> InitiateStripePaymentAsync(decimal amount, string currency, string subdomain)
        {
            // Get companyId using subdomain
            Guid? companyId = await _publicContext.Tenants.Where(x => x.Subdomain == subdomain.ToLower())
                .Select(com => com.Id).FirstOrDefaultAsync();
            if (companyId == null)
                throw new RecordNotFoundException("Company not found");

            // Create wallet transaction record first
            var transactionReference = Guid.NewGuid().ToString();

            // Get or create wallet
            var wallet = await GetOrCreateWalletAsync();
            var walletTransaction = new Models.Wallet.WalletTransaction
            {
                WalletId = wallet.Id,
                Amount = amount,
                Type = TransactionType.Credit,
                PaymentMethod = AdminConsole.Enums.PaymentMethod.Stripe,
                TransactionReference = transactionReference,
                Description = $"Wallet funding via Stripe - Amount: {amount} {currency.ToUpper()}",
                Status = TransactionStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            var options = new PaymentIntentCreateOptions
            {
                Amount = (long)(amount * 100), // Convert to cents
                Currency = currency.ToLower(),
                AutomaticPaymentMethods = new PaymentIntentAutomaticPaymentMethodsOptions
                {
                    Enabled = true,
                },
                Metadata = new Dictionary<string, string>
                    {
                        { "transaction_id", walletTransaction.Id.ToString() },
                        { "wallet_id", wallet.Id.ToString() },
                        { "transaction_reference", transactionReference }
                    },
                Description = $"Wallet funding for {amount} {currency.ToUpper()}",
                ReceiptEmail = null // Will be set based on logged-in user if available
            };

            var service = new PaymentIntentService();
            var intent = await service.CreateAsync(options);

            walletTransaction.PaymentId = intent.Id;
            _context.WalletTransactions.Add(walletTransaction);
            await _context.SaveChangesAsync();

            // Add maping to compant
            var walletMappings = new WalletTranToCompanyMapping
            {
                TransactionId = walletTransaction.Id.ToString(),
                TenantId = companyId.Value
            };

            _publicContext.WalletTranToCompanyMappings.Add(walletMappings);
            await _publicContext.SaveChangesAsync();

            // Update transaction with Stripe payment intent ID
            walletTransaction.TransactionReference = intent.Id;
            _context.WalletTransactions.Update(walletTransaction);
            await _context.SaveChangesAsync();

            return intent.ClientSecret;
        }  
        #endregion
        
        #region Initiate Mollie Payment
        public async Task<string> InitiateMolliePaymentAsync(decimal amount, string currency, string subdomain)
        {
            // Get companyId using subdomain
            Guid? companyId = await _publicContext.Tenants.Where(x => x.Subdomain == subdomain.ToLower())
                .Select(com => com.Id).FirstOrDefaultAsync();
            if (companyId == null)
                throw new RecordNotFoundException("Company not found");

            // Create wallet transaction record first
            var transactionReference = Guid.NewGuid().ToString();

            // Get or create wallet
            var wallet = await GetOrCreateWalletAsync();

            var paymentRequest = new PaymentRequest
            {
                Amount = new Amount(currency, amount),
                Description = $"Wallet funding for {amount} {currency.ToUpper()}",
                RedirectUrl = $"{_adminUrl}/dashboard",
                WebhookUrl = $"{_baseUrl}/wallet/webhook/mollie",
                Metadata = transactionReference
            };

            var payment = await _paymentClient.CreatePaymentAsync(paymentRequest);

            var walletTransaction = new AdminConsole.Models.Wallet.WalletTransaction
            {
                WalletId = wallet.Id,
                Amount = amount,
                Type = TransactionType.Credit,
                PaymentMethod = AdminConsole.Enums.PaymentMethod.Mollie,
                TransactionReference = transactionReference,
                Description = $"Wallet funding via Mollie - Amount: {amount} {currency.ToUpper()}",
                Status = TransactionStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            walletTransaction.PaymentId = payment.Id;
            _context.WalletTransactions.Add(walletTransaction);
            await _context.SaveChangesAsync();

            // Update transaction with Mollie payment ID
            walletTransaction.TransactionReference = payment.Id;
            _context.WalletTransactions.Update(walletTransaction);
            await _context.SaveChangesAsync();

            // Add mapping to company
            var walletMappings = new WalletTranToCompanyMapping
            {
                TransactionId = payment.Id,
                TenantId = companyId.Value
            };

            _publicContext.WalletTranToCompanyMappings.Add(walletMappings);
            await _publicContext.SaveChangesAsync();

            return payment.Links.Checkout.Href;
        }
        #endregion

        #region Verify Stripe Payment
        public async Task<GenericResponse> VerifyStripePaymentAsync(string json, string signature)
        {
            try
            {
                Event stripeEvent = EventUtility.ConstructEvent(
                    json,
                    signature,
                    _walletWebHookSecret,
                    throwOnApiVersionMismatch: false
                );

                switch (stripeEvent.Type)
                {
                    case "payment_intent.succeeded":
                        var paymentIntent = stripeEvent.Data.Object as PaymentIntent;
                        await HandleSuccessfulPayment(paymentIntent);
                        break;

                    case "payment_intent.payment_failed":
                        var failedPaymentIntent = stripeEvent.Data.Object as PaymentIntent;
                        await HandleFailedPayment(failedPaymentIntent);
                        break;

                    case "payment_intent.canceled":
                        var canceledPaymentIntent = stripeEvent.Data.Object as PaymentIntent;
                        await HandleCanceledPayment(canceledPaymentIntent);
                        break;

                    default:
                        return new GenericResponse
                        {
                            ResponseCode = "200",
                            ResponseMessage = $"Unhandled event type: {stripeEvent.Type}",
                            Data = false
                        };
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Stripe webhook processed successfully",
                    Data = true
                };
            }
            catch (StripeException ex)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Stripe verification failed: {ex.Message}. Secret: {_options.Value.WalletWebHookSecret}",
                    Data = false
                };
            }
            catch (Exception ex)
            {
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = $"Payment verification failed: {ex.Message}. Secret: {_options.Value.WalletWebHookSecret}",
                    Data = false
                };
            }
        }
        #endregion

        #region Verify Mollie Payment
        public async Task<GenericResponse> VerifyMolliePaymentAsync(string paymentId)
        {
            var response = await _paymentClient.GetPaymentAsync(paymentId);
            if (response == null)
                throw new RecordNotFoundException("Payment not found.");

            // Get subdomain from wallet mappings and build a context from it
            var walletMapping = await _publicContext.WalletTranToCompanyMappings
                .FirstOrDefaultAsync(m => m.TransactionId == paymentId);

            if (walletMapping == null)
                throw new RecordNotFoundException("Wallet transaction mapping not found for the payment.");

            var companyId = walletMapping.TenantId;
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == companyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

            var walletTransaction = await context.WalletTransactions
                .FirstOrDefaultAsync(t => t.PaymentId == response.Id);
            if (walletTransaction == null)
                throw new RecordNotFoundException("Wallet transaction not found for the payment.");

            var wallet = await context.CompanyWallets
                    .FirstOrDefaultAsync(w => w.Id == walletTransaction.WalletId);
            if (wallet == null)
                throw new RecordNotFoundException("Wallet not found for the transaction.");

            switch (response.Status)
            {
                case "paid":
                    walletTransaction.Status = TransactionStatus.Completed;

                    // Update wallet balance
                    wallet.Balance += walletTransaction.Amount;
                    break;
                case "open":
                    walletTransaction.Status = TransactionStatus.Pending;
                    break;
                case "failed":
                    walletTransaction.Status = TransactionStatus.Failed;
                    break;
                case "canceled":
                    walletTransaction.Status = TransactionStatus.Cancelled;
                    break;
                case "expired":
                    walletTransaction.Status = TransactionStatus.Expired;
                    break;
                default:
                    throw new Exception("Unknown payment status.");
            }

            walletTransaction.UpdatedAt = DateTime.UtcNow;
            context.WalletTransactions.Update(walletTransaction);

            wallet.UpdatedAt = DateTime.UtcNow;
            context.CompanyWallets.Update(wallet);
            await context.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Payment verified successfully."
            };
        }
        #endregion

        #region Get Wallet Balance - Private Method
        private async Task<AdminConsole.Models.Wallet.CompanyWallet> GetOrCreateWalletAsync()
        {
            var wallet = await _context.CompanyWallets
                .FirstOrDefaultAsync();

            if (wallet == null)
            {
                wallet = new AdminConsole.Models.Wallet.CompanyWallet
                {
                    Id = Guid.NewGuid(),
                    Balance = 0,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.CompanyWallets.Add(wallet);
                await _context.SaveChangesAsync();
            }

            return wallet;
        }
        #endregion

        #region Handle Successsful Webhook Events - Private Method
        private async Task HandleSuccessfulPayment(PaymentIntent paymentIntent)
        {
            if (paymentIntent?.Metadata == null || !paymentIntent.Metadata.ContainsKey("transaction_id"))
                return;

            // Get subdomain from wallet mappings and build a context from it
            var transactionId = paymentIntent.Metadata["transaction_id"];
            var walletMapping = await _publicContext.WalletTranToCompanyMappings
                .FirstOrDefaultAsync(m => m.TransactionId == transactionId);

            if (walletMapping == null)
                throw new RecordNotFoundException("Wallet transaction mapping not found for the payment.");

            var companyId = walletMapping.TenantId;
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == companyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

            if (!Guid.TryParse(transactionId, out var transactionGuid))
                return;

            var walletTransaction = await context.WalletTransactions
                .FirstOrDefaultAsync(t => t.Id == transactionGuid);

            if (walletTransaction == null)
                return;

            var wallet = await context.CompanyWallets
                .FirstOrDefaultAsync(w => w.Id == walletTransaction.WalletId);

            if (wallet == null)
                return;

            // Update transaction status
            walletTransaction.Status = TransactionStatus.Completed;
            walletTransaction.UpdatedAt = DateTime.UtcNow;

            // Update wallet balance
            wallet.Balance += walletTransaction.Amount;
            wallet.UpdatedAt = DateTime.UtcNow;

            context.WalletTransactions.Update(walletTransaction);
            context.CompanyWallets.Update(wallet);
            await context.SaveChangesAsync();
        }
        #endregion 

        #region Handle Failed and Canceled Webhook Events
        private async Task HandleFailedPayment(PaymentIntent paymentIntent)
        {
            if (paymentIntent?.Metadata == null || !paymentIntent.Metadata.ContainsKey("transaction_id"))
                return;

            var transactionId = paymentIntent.Metadata["transaction_id"];
            var walletMapping = await _publicContext.WalletTranToCompanyMappings
               .FirstOrDefaultAsync(m => m.TransactionId == transactionId);

            if (walletMapping == null)
                throw new RecordNotFoundException("Wallet transaction mapping not found for the payment.");

            var companyId = walletMapping.TenantId;
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == companyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

            if (!Guid.TryParse(transactionId, out var transactionGuid))
                return;

            var walletTransaction = await context.WalletTransactions
                .FirstOrDefaultAsync(t => t.Id == transactionGuid);

            if (walletTransaction == null)
                return;

            // Update transaction status to failed
            walletTransaction.Status = TransactionStatus.Failed;
            walletTransaction.UpdatedAt = DateTime.UtcNow;

            context.WalletTransactions.Update(walletTransaction);
            await context.SaveChangesAsync();
        }
        #endregion

        #region Handle Canceled Webhook Events
        private async Task HandleCanceledPayment(PaymentIntent paymentIntent)
        {
            if (paymentIntent?.Metadata == null || !paymentIntent.Metadata.ContainsKey("transaction_id"))
                return;

            var transactionId = paymentIntent.Metadata["transaction_id"];
            var walletMapping = await _publicContext.WalletTranToCompanyMappings
                    .FirstOrDefaultAsync(m => m.TransactionId == transactionId);

            if (walletMapping == null)
                throw new RecordNotFoundException("Wallet transaction mapping not found for the payment.");

            var companyId = walletMapping.TenantId;
            var subdomain = await _publicContext.Tenants
                .Where(t => t.Id == companyId)
                .Select(t => t.Subdomain)
                .FirstOrDefaultAsync();

            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));

            if (!Guid.TryParse(transactionId, out var transactionGuid))
                return;

            var walletTransaction = await context.WalletTransactions
                .FirstOrDefaultAsync(t => t.Id == transactionGuid);

            if (walletTransaction == null)
                return;

            // Update transaction status to cancelled
            walletTransaction.Status = TransactionStatus.Cancelled;
            walletTransaction.UpdatedAt = DateTime.UtcNow;

            context.WalletTransactions.Update(walletTransaction);
            await context.SaveChangesAsync();
        }
        #endregion

        #region Get Payment Status
        public async Task<GenericResponse> GetPaymentStatusAsync(string paymentIntentId)
        {
            try
            {
                var service = new PaymentIntentService();
                var paymentIntent = await service.GetAsync(paymentIntentId);

                var transactionId = paymentIntent.Metadata?.ContainsKey("transaction_id") == true 
                    ? paymentIntent.Metadata["transaction_id"] 
                    : null;

                AdminConsole.Models.Wallet.WalletTransaction walletTransaction = null;
                if (transactionId != null && Guid.TryParse(transactionId, out var transactionGuid))
                {
                    walletTransaction = await _context.WalletTransactions
                        .FirstOrDefaultAsync(t => t.Id == transactionGuid);
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Payment status retrieved successfully",
                    Data = new
                    {
                        PaymentIntentId = paymentIntent.Id,
                        Status = paymentIntent.Status,
                        Amount = paymentIntent.Amount / 100.0, // Convert from cents
                        Currency = paymentIntent.Currency?.ToUpper(),
                        WalletTransaction = walletTransaction != null ? new
                        {
                            Id = walletTransaction.Id,
                            Status = walletTransaction.Status.ToString(),
                            Amount = walletTransaction.Amount,
                            CreatedAt = walletTransaction.CreatedAt,
                            UpdatedAt = walletTransaction.UpdatedAt
                        } : null
                    }
                };
            }
            catch (StripeException ex)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Error retrieving payment status: {ex.Message}",
                    Data = null
                };
            }
        }
        #endregion

        #region Get Wallet Transaction History
        public async Task<GenericResponse> GetWalletTransactionsByPaymentMethodAsync(AdminConsole.Enums.PaymentMethod paymentMethod, int pageSize = 10, int pageNumber = 1)
        {
            try
            {
                var query = _context.WalletTransactions
                    .Where(t => t.PaymentMethod == paymentMethod)
                    .OrderByDescending(t => t.CreatedAt);

                var totalCount = await query.CountAsync();
                var transactions = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(t => new
                    {
                        t.Id,
                        t.Amount,
                        t.Type,
                        t.PaymentMethod,
                        t.TransactionReference,
                        t.Description,
                        t.Status,
                        t.CreatedAt,
                        t.UpdatedAt
                    })
                    .ToListAsync();

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Wallet transactions retrieved successfully",
                    Data = new
                    {
                        Transactions = transactions,
                        TotalCount = totalCount,
                        PageNumber = pageNumber,
                        PageSize = pageSize,
                        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                };
            }
            catch (Exception ex)
            {
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = $"Error retrieving transactions: {ex.Message}",
                    Data = null
                };
            }
        }
        #endregion

        #region Initiate Wallet Funding
        public async Task<GenericResponse> InitiateWalletFundingAsync(decimal amount, string currency, AdminConsole.Enums.PaymentProvider provider, string subdomain)
        {
            try
            {
                switch (provider)
                {
                    case AdminConsole.Enums.PaymentProvider.Stripe:
                        var stripeClientSecret = await InitiateStripePaymentAsync(amount, currency, subdomain);
                        return new GenericResponse
                        {
                            ResponseCode = "200",
                            ResponseMessage = "Stripe payment initiated successfully",
                            Data = new
                            {
                                PaymentProvider = "Stripe",
                                ClientSecret = stripeClientSecret,
                                Currency = currency.ToUpper(),
                                Amount = amount,
                                Instructions = "Use the clientSecret to complete payment on the frontend"
                            }
                        };

                    case AdminConsole.Enums.PaymentProvider.Mollie:
                        var mollieCheckoutUrl = await InitiateMolliePaymentAsync(amount, currency, subdomain);
                        return new GenericResponse
                        {
                            ResponseCode = "200",
                            ResponseMessage = "Mollie payment initiated successfully",
                            Data = new
                            {
                                PaymentProvider = "Mollie",
                                CheckoutUrl = mollieCheckoutUrl,
                                Currency = currency.ToUpper(),
                                Amount = amount,
                                Instructions = "Redirect user to the checkoutUrl to complete payment"
                            }
                        };

                    default:
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = "Unsupported payment provider",
                            Data = null
                        };
                }
            }
            catch (Exception ex)
            {
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = $"Failed to initiate wallet funding: {ex.Message}",
                    Data = null
                };
            }
        }
        #endregion
    }
}
