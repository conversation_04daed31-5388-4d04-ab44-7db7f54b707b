﻿using CsvHelper.Configuration.Attributes;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Models
{
    public class EnterprizeSubscriptionPayment
    {
        public Guid Id { get; set; }

        [Required]
        public string CompanyEmail { get; set; }

        [Required]
        public string CompanyName { get; set; }
        public double Amount { get; set; }
        public SubscriptionInterval Frequency { get; set; }
        public Applications Application { get; set; }
        public PaymentProviders? PaymentProvider { get; set; }
        public Currency Currency { get; set; }
        public string PaymentId { get; set; }
        public string PaymentLink { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
        public string Address { get; set; }
        public string PhoneNumber { get; set; }
        public bool PaymentUsed { get; set; }
        public DateTime? PaymentDate { get; set; }

        [ForeignKey("Tenant")]
        public Guid? TenantId { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string PersonalEmail { get; set; }
        public bool IsExistingUser { get; set; } = false;
        public bool EnterprizeFreePlan { get; set; } = false;

        // Navigational properties
        public Tenant.Model.Tenant Tenant { get; set; }

        public EnterprizeSubscriptionPayment()
        {
            Id = Guid.NewGuid();
        }
    }
}
