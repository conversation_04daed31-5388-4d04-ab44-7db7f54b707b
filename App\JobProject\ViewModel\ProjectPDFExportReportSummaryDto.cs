﻿using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProject.ViewModel
{
    public class ProjectPDFExportReportSummaryDto
    {
        public string ProjectName { get; set; }
        public string totalTimeTracked { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<UserDto> TeamMembers { get; set; } = new List<UserDto>();
        public string Summary { get; set; }
        public string Description { get; set; }
        public List<string> ProjectFilesUrls { get; set; } = new List<string>();
        public List<SprintDFExportReportSummaryDto> SprintSummary { get; set; } = new List<SprintDFExportReportSummaryDto>();
    }
}
