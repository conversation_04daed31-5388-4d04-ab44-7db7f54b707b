﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class UpdateBookedExtMeetingDto
    {
        [Required]
        public string BookedExternalMeetingId { get; set; }

        [Required]
        public int DurationInMinutes { get; set; }
        public DateTime SelectedDateAndTime { get; set; }
        public List<string> InternalMemberIds { get; set; }
        public List<string> ExternalMemberEmails { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }

        public UpdateBookedExtMeetingDto()
        {
            InternalMemberIds = new List<string>();
            ExternalMemberEmails = new List<string>();
        }
    }
}
