using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using WatchDog;
using Jobid.App.Helpers.Utils;

namespace Jobid
{
    public class Program
    {
        // Native methods for setting DLL directory search path
        [DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool SetDllDirectory(string lpPathName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetDefaultDllDirectories(uint directoryFlags);

        // Constants for SetDefaultDllDirectories
        private const uint LOAD_LIBRARY_SEARCH_DEFAULT_DIRS = 0x00001000;
        private const uint LOAD_LIBRARY_SEARCH_USER_DIRS = 0x00000400;
        
        public static void Main(string[] args)
        {
            try
            {
                // Setup DLL search paths on Windows
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    Console.WriteLine("Running on Windows - configuring additional DLL search paths");
                    
                    // Configure Windows to search user-defined DLL directories
                    SetDefaultDllDirectories(LOAD_LIBRARY_SEARCH_DEFAULT_DIRS | LOAD_LIBRARY_SEARCH_USER_DIRS);
                    
                    // Add DinkToPdf folder to the DLL search path
                    var baseDir = AppContext.BaseDirectory;
                    var dllDir = Path.Combine(baseDir, "DinkToPdf");
                    
                    Console.WriteLine($"Adding DLL search path: {dllDir}");
                    SetDllDirectory(dllDir);
                    
                    // Also add the project's DinkToPdf folder as a fallback
                    var projectDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                    var projectDllDir = Path.Combine(projectDir, "DinkToPdf");
                    
                    if (Directory.Exists(projectDllDir))
                    {
                        Console.WriteLine($"Adding additional DLL search path: {projectDllDir}");
                        SetDllDirectory(projectDllDir);
                    }
                }
                
                //// Check if the wkhtmltopdf library exists before initializing
                //var appBaseDir = AppContext.BaseDirectory;
                //var windowsDllPath = Path.Combine(appBaseDir, "DinkToPdf", "libwkhtmltox.dll");
                //var linuxLibPath = "/usr/local/lib/libwkhtmltox.so";
                //var alternativeLinuxPath = "/usr/lib/libwkhtmltox.so";
                
                //bool libraryExists = File.Exists(windowsDllPath) || 
                //                   File.Exists(linuxLibPath) || 
                //                   File.Exists(alternativeLinuxPath);

                //Console.WriteLine($"Checking for wkhtmltopdf library: {libraryExists}");
                
                //if (!libraryExists)
                //{
                //    // Initialize DinkToPdf library loader to ensure native DLLs are loaded
                //    Console.WriteLine("wkhtmltopdf library found. Initializing DinkToPdfLibraryLoader...");
                //    DinkToPdfLibraryLoader.Init();
                //}
                //else
                //{
                //    Console.WriteLine("wkhtmltopdf library not found. PDF generation will be disabled.");
                //    Console.WriteLine("This is expected when running without wkhtmltopdf installed.");
                //}
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing DinkToPdf: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                // Continue execution even if DinkToPdf initialization fails
                // The application can still run, but PDF generation will not work
            }
            
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureLogging(logging =>
                {
                    logging.AddWatchDogLogger();
                })
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseStartup<Startup>();
                });
    }
}
