﻿// <auto-generated />
using System;
using Jobid.App.Helpers.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Jobid.Migrations
{
    [DbContext(typeof(JobProDbContext))]
    [Migration("20250628200311_added-transcription-tbl_update")]
    partial class addedtranscriptiontbl_update
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("public")
                .HasAnnotation("Relational:MaxIdentifierLength", 63)
                .HasAnnotation("ProductVersion", "5.0.11")
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActivitySummary")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("By")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("EventCategory")
                        .HasColumnType("integer");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<string>("GenericUrl")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Activities");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ActivityRequestedPermisssions", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("EventCategories")
                        .HasColumnType("text");

                    b.Property<DateTime>("FromDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("RequesterId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ToDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ActivityRequestedPermisssions");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ActivitySettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("EraseAcitivity")
                        .HasColumnType("integer");

                    b.Property<bool>("LogActivity")
                        .HasColumnType("boolean");

                    b.Property<bool?>("TurnOnOrOffForAllUsers")
                        .HasColumnType("boolean");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ActivitySettings");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ActivityView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActivitySummary")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("EventCategory")
                        .HasColumnType("integer");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ActivityViews");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.LogAttachment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("LogAttachments");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ShareActivityParams", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AnyOneCanView")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("EventCategory")
                        .HasColumnType("text");

                    b.Property<string>("MemberId")
                        .HasColumnType("text");

                    b.Property<string>("ShareId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("ShareActivityParams");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.BasicInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AvailableNumberOfLeaveDays")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateOfHire")
                        .HasColumnType("timestamp");

                    b.Property<decimal>("GrossSalary")
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime?>("LastDayOfContract")
                        .HasColumnType("timestamp");

                    b.Property<int>("LengthOfContract")
                        .HasColumnType("integer");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("BasicInfos");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.BillingInformation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BusinessRegistrationNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CompanyAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("VATNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("BillingInformations");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Calls.CallRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AnsweredAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("AnsweredBy")
                        .HasColumnType("text");

                    b.Property<decimal>("Cost")
                        .HasColumnType("numeric(18,2)");

                    b.Property<int>("Direction")
                        .HasColumnType("integer");

                    b.Property<decimal>("Duration")
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("FromNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PhoneNumberId")
                        .HasColumnType("uuid");

                    b.Property<string>("RecordingUrl")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("ToNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TwilioCallSid")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PhoneNumberId");

                    b.ToTable("CallRecords");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Calls.CallTranscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<int>("ActionStatus")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CallDate")
                        .HasColumnType("timestamp");

                    b.Property<int>("CallDirection")
                        .HasColumnType("integer");

                    b.Property<double>("CallDuration")
                        .HasColumnType("double precision");

                    b.Property<string>("CompanyId")
                        .HasColumnType("text");

                    b.Property<string>("CustomerEmail")
                        .HasColumnType("text");

                    b.Property<string>("CustomerName")
                        .HasColumnType("text");

                    b.Property<double?>("DurationInMinutes")
                        .HasColumnType("double precision");

                    b.Property<string>("EmployeeName")
                        .HasColumnType("text");

                    b.Property<string>("FromNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("MeetingDate")
                        .HasColumnType("timestamp");

                    b.Property<TimeSpan?>("MeetingTime")
                        .HasColumnType("interval");

                    b.Property<string>("ToNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Transcription")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("CallTranscriptions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.CampaignContact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.HasIndex("ContactId");

                    b.ToTable("CampaignContacts");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.CompanyDeletionRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActionPerfomedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateRequested")
                        .HasColumnType("timestamp");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequestId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RequestedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TenantId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("CompanyDeletionRequests");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.ContactCampaign", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SalePitch")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ContactCampaigns");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchColor")
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("DepartmentName")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmergencyInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmergencyContactAddress")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactAlternativePhone")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactEmailAddress")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactName")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactRelationship")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("EmergencyInfos");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeeComplaint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("ActionWithdrawn")
                        .HasColumnType("boolean");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<string>("ComplaintRemark")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateIssued")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("IssuedBy")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmployeeComplaints");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeePermission", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("PackageName")
                        .HasColumnType("text");

                    b.Property<string>("PermissionCategory")
                        .HasColumnType("text");

                    b.Property<string>("PermissionName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmployeePermissions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeePosition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("PositionName")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("EmployeePositions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeeRoles", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("PackageName")
                        .HasColumnType("text");

                    b.Property<string>("RoleName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmployeeRoles");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeeRolesPermission", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("PermissionId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId");

                    b.ToTable("EmployeeRolesPermissions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Individual", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchColor")
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsHeadofDepartment")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("PositionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("Individuals");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.LiveKit.LiveKitParticipant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccessToken")
                        .HasColumnType("text");

                    b.Property<DateTime>("JoinedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("LeftAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("LiveKitRoomId")
                        .HasColumnType("uuid");

                    b.Property<string>("ParticipantIdentity")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("LiveKitRoomId");

                    b.ToTable("LiveKitParticipants");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.LiveKit.LiveKitRoom", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CallRecordId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ClosedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("MaxParticipants")
                        .HasColumnType("integer");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<Guid?>("PhoneNumberId")
                        .HasColumnType("uuid");

                    b.Property<string>("RoomName")
                        .HasColumnType("text");

                    b.Property<string>("ServerUrl")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("WebSocketUrl")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("LiveKitRooms");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Localization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AmountFormat")
                        .HasColumnType("text");

                    b.Property<string>("CalenderWeekStartsOn")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("DateFormat")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LanguagePreference")
                        .HasColumnType("text");

                    b.Property<string>("TimeFormat")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Localizations");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.OrganogramCompany", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchColor")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int>("CompanyType")
                        .HasColumnType("integer");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<int>("EntityType")
                        .HasColumnType("integer");

                    b.Property<string>("FullAddress")
                        .HasColumnType("text");

                    b.Property<int>("Industry")
                        .HasColumnType("integer");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("OrganogramCompanies");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.PasswordPolicy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("MinimumPasswordLength")
                        .HasColumnType("integer");

                    b.Property<int>("PasswordExpirationDays")
                        .HasColumnType("integer");

                    b.Property<bool>("ProhibitUserNameAsPassword")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireAtLeastOneLowercase")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireAtLeastOneNumber")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireAtLeastOneSpecialCharacter")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequireAtLeastOneUppercase")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequirePasswordExpiration")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("PasswordPolicies");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Phone.PhoneNumber", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Balance")
                        .HasColumnType("numeric");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("FriendlyName")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRegistered")
                        .HasColumnType("boolean");

                    b.Property<string>("Number")
                        .HasColumnType("text");

                    b.Property<string>("TwilioSid")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("PhoneNumbers");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Phone.PhoneNumberAssignment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("AssignedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("PhoneNumberId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PhoneNumberId");

                    b.ToTable("PhoneNumberAssignments");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Phone.PhoneNumberCapability", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("CapabilityType")
                        .HasColumnType("integer");

                    b.Property<Guid>("PhoneNumberId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("PhoneNumberId");

                    b.ToTable("PhoneNumberCapabilities");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.ProductUpdate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("text");

                    b.Property<string>("Package")
                        .HasColumnType("text");

                    b.Property<int>("PublishStatus")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("ProductUpdates");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.SuspendedEmployee", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsIndefinite")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("SuspendedOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("UnSuspendedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SuspendedEmployees");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.TwoFactorSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("ImidiatelyRequire2FA")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Require2FA")
                        .HasColumnType("boolean");

                    b.Property<bool>("Require2FAOnSecondFailedLoginAttempt")
                        .HasColumnType("boolean");

                    b.Property<bool>("Require2FAOnThirdLogin")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int>("options")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("TwoFactorSettings");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.UserContact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(450)
                        .HasColumnType("character varying(450)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("UserContacts");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Wallet.CompanyWallet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Balance")
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("CompanyWallets");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Wallet.WalletTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TransactionReference")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("WalletId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("WalletId");

                    b.ToTable("WalletTransactions");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BackGroundJobId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("JobId")
                        .HasColumnType("text");

                    b.Property<int>("JobType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("BackGroundJobIds");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedExternalMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdditionalInfo")
                        .HasColumnType("text");

                    b.Property<DateTime>("BookedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("CancelMeetingLink")
                        .HasColumnType("text");

                    b.Property<int>("DurationInMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GuestEmails")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("PersonalScheduleId")
                        .HasColumnType("text");

                    b.Property<string>("ReScheduleMeetingLink")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ReScheduledOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("ReasonForCancelling")
                        .HasColumnType("text");

                    b.Property<string>("ReasonForReSchedulling")
                        .HasColumnType("text");

                    b.Property<DateTime>("SelectedDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.ToTable("BookedExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedMeetingMember", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BookedMeetingId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<int>("InviteResponse")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("NotifyMeInMinutes")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BookedMeetingMembers");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderExternalIntegration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("IcalenderEventId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MeetingId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CalenderExternalIntegrations");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Attachment")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("CreatedByAI")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("Frequency")
                        .HasColumnType("text");

                    b.Property<bool>("HasCustomFrequency")
                        .HasColumnType("boolean");

                    b.Property<bool>("HasThisMeetingHappened")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<bool>("MakeSchdulePrivate")
                        .HasColumnType("boolean");

                    b.Property<int?>("MeetLength")
                        .HasColumnType("integer");

                    b.Property<string>("MeetingDuration")
                        .HasColumnType("text");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("MeetingLink")
                        .HasColumnType("text");

                    b.Property<int>("MeetingOwnerType")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("NotifyMe")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMembersIn")
                        .HasColumnType("integer");

                    b.Property<int>("RescheduleCount")
                        .HasColumnType("integer");

                    b.Property<int>("ScheduleType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("CalenderMeetings");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderMeetingRecording", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("MeetingRecordingUrl")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CalenderMeetingRecordings");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderUpload", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MeetingId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SubsequentMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("MeetingId");

                    b.HasIndex("SubsequentMeetingId");

                    b.ToTable("CalenderUploads");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomFrequency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CalenderMeetingId")
                        .HasColumnType("uuid");

                    b.Property<int>("EndStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("EndsAfter")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndsOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("RepeatCount")
                        .HasColumnType("integer");

                    b.Property<string>("RepeatEvery")
                        .HasColumnType("text");

                    b.Property<string>("RepeatOn")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CalenderMeetingId")
                        .IsUnique();

                    b.ToTable("CustomFrequency");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("Options")
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .HasColumnType("text");

                    b.Property<int>("QuestionType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.ToTable("CustomQuestions");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestionAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Answer")
                        .HasColumnType("text");

                    b.Property<Guid>("BookedExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CustomQuestionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BookedExternalMeetingId");

                    b.ToTable("CustomQuestionAnswers");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AvoidConflicts")
                        .HasColumnType("boolean");

                    b.Property<string>("BookingLink")
                        .HasColumnType("text");

                    b.Property<bool>("CanBeChosenBasedOnAvailability")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("CanBookEndDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("CanBookStartDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("CancellationPolicy")
                        .HasColumnType("boolean");

                    b.Property<int>("ComfirmationPageOptions")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("EmailComfirmation")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailFollowUpAfter")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailRemindersBefore")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ExternalMeetingQuestionId")
                        .HasColumnType("uuid");

                    b.Property<int>("ExternalMeetingType")
                        .HasColumnType("integer");

                    b.Property<int>("ExternalMeetingTypePlan")
                        .HasColumnType("integer");

                    b.Property<string>("Guests")
                        .HasColumnType("text");

                    b.Property<string>("InternalNote")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("MakeMeetingPrivate")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaxInvitePerMeeting")
                        .HasColumnType("integer");

                    b.Property<int?>("MaxNoOfBookingsPerSlot")
                        .HasColumnType("integer");

                    b.Property<int>("MeetingBuffer")
                        .HasColumnType("integer");

                    b.Property<int>("MeetingDuration")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("MeetingEndDateRange")
                        .HasColumnType("timestamp");

                    b.Property<int>("MeetingFrequency")
                        .HasColumnType("integer");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("MeetingLink")
                        .HasColumnType("text");

                    b.Property<string>("MeetingName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MeetingOwnerId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("MeetingStartDateRange")
                        .HasColumnType("timestamp");

                    b.Property<string>("OtherMeetingHostsIds")
                        .HasColumnType("text");

                    b.Property<Guid>("PersonalScheduleId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Price")
                        .HasColumnType("numeric");

                    b.Property<bool>("PushMeetingToWebsite")
                        .HasColumnType("boolean");

                    b.Property<string>("SiteUrl")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingQuestionId");

                    b.HasIndex("PersonalScheduleId");

                    b.ToTable("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingMembers", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("ExternalMeetingId")
                        .HasColumnType("text");

                    b.Property<int>("InviteResponse")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMeInMinutes")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SelectedDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ExternalMeetingMembers");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AddGuests")
                        .HasColumnType("boolean");

                    b.Property<bool>("AdditionalInfo")
                        .HasColumnType("boolean");

                    b.Property<bool>("AdditionalInfoRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("AskEmail")
                        .HasColumnType("boolean");

                    b.Property<bool>("AskFullName")
                        .HasColumnType("boolean");

                    b.Property<bool>("AskLocation")
                        .HasColumnType("boolean");

                    b.Property<string>("CustomQuestion")
                        .HasColumnType("text");

                    b.Property<bool>("CustomQuestionRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("FullNameRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("LocationRequired")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("ExternalMeetingQuestion");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingTimeManagement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("Date")
                        .HasColumnType("timestamp");

                    b.Property<string>("DayOfTheWeek")
                        .HasColumnType("text");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("IsLockedTill")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("PersonalScheduleId")
                        .HasColumnType("uuid");

                    b.Property<string>("ScheduleName")
                        .HasColumnType("text");

                    b.Property<string>("SelectedTimeSlots")
                        .HasColumnType("text");

                    b.Property<string>("TimeBreakDown")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.HasIndex("PersonalScheduleId");

                    b.ToTable("ExternalMeetingTimeManagements");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.MeetingNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("SubsequentMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MeetingId");

                    b.HasIndex("SubsequentMeetingId");

                    b.ToTable("MeetingNotes");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.MeetingSkillSuggestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("MeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("Skills")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("MeetingSkillSuggestions");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.PersonalSchedule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Available")
                        .HasColumnType("boolean");

                    b.Property<bool>("AvailableForMeetingsEmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<bool>("AvailableForSprintsEmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<bool>("AvailableForTasksEmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<string>("Day")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<bool>("ScheduleIsPublic")
                        .HasColumnType("boolean");

                    b.Property<int>("ScheduleName")
                        .HasColumnType("integer");

                    b.Property<string>("SelectedTimeSlots")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("TeamMatesCanSee")
                        .HasColumnType("boolean");

                    b.Property<string>("TimeBreakDown")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId1")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId1");

                    b.ToTable("PersonalSchedule");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ProposedDateDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("AcceptedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("MeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("NewProposedEndDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("NewProposedStartDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("ProposedByEmail")
                        .HasColumnType("text");

                    b.Property<DateTime>("PropsedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("MeetingId")
                        .IsUnique();

                    b.ToTable("ProposedDateDetails");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.RoundRobinHostingOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<int>("HostCount")
                        .HasColumnType("integer");

                    b.Property<string>("HostId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("MeetingDateAndTime")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.ToTable("RoundRobinHostingOrders");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.SubsequentMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CalenderMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("HasMeetingHappeed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCanceled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MakeSchdulePrivate")
                        .HasColumnType("boolean");

                    b.Property<int?>("MeetLength")
                        .HasColumnType("integer");

                    b.Property<string>("MeetingDuration")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("NotificationSent")
                        .HasColumnType("boolean");

                    b.Property<int>("NotifyMe")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMembersIn")
                        .HasColumnType("integer");

                    b.Property<int>("RescheduleCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("SubsequentMeetingDateTime")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("CalenderMeetingId");

                    b.ToTable("SubsequentMeetings");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.UserIdCalenderId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CalenderId")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<int>("InviteResponse")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMeInMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("SubsequentMeetingId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("UserIdMeetingIds");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.AccountDeletionRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActionPerfomedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateRequested")
                        .HasColumnType("timestamp");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<string>("RequestId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AccountDeletionRequests");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.AppPermissions", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("Agent")
                        .HasColumnType("integer");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("boolean");

                    b.Property<bool>("MakeFavorite")
                        .HasColumnType("boolean");

                    b.Property<string>("SubscriptionStatus")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AppPermissions");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.BlacklistedToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BlacklistedTokens");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.BrowserInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BrowserId")
                        .HasColumnType("text");

                    b.Property<bool>("IsBot")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMobile")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Platform")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BrowserInfos");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("RoleName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ClientRoles");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRoleRoleModule", b =>
                {
                    b.Property<Guid>("ClientRoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleModuleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.HasKey("ClientRoleId", "RoleModuleId");

                    b.HasIndex("RoleModuleId");

                    b.ToTable("ClientRoleRoleModules");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CompanySettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("WorkSpaceName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CompanySettings");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CompanyUserInvite", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("InviteCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.ToTable("CompanyUserInvites");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Country", b =>
                {
                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("financialService")
                        .HasColumnType("integer");

                    b.HasKey("Code");

                    b.ToTable("Countries");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.DefaultCompany", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("DefaultCompanies");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.EndpointCallTracker", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("Controller")
                        .HasColumnType("text");

                    b.Property<int>("Count")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<string>("HttpMethod")
                        .HasColumnType("text");

                    b.Property<string>("Section")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("EndpointCallTrackers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.LoginLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("LastLoginDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("LoginLogs");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.MicroService", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Key")
                        .HasColumnType("text");

                    b.Property<string>("ServiceId")
                        .HasColumnType("text");

                    b.Property<string>("ServiceName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("MicroServices");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.NationalLaguage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("NationalLaguages");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.OTP", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .HasColumnType("text");

                    b.Property<string>("IdentifierType")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<string>("TokenType")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("OTP");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Create")
                        .HasColumnType("boolean");

                    b.Property<bool>("Delete")
                        .HasColumnType("boolean");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.Property<Guid>("RoleModuleId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Update")
                        .HasColumnType("boolean");

                    b.Property<bool>("View")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("RoleModuleId");

                    b.ToTable("Permissions");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.PricingAndFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DurationType")
                        .HasColumnType("text");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLimited")
                        .HasColumnType("boolean");

                    b.Property<string>("LimitedTo")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("PricingAndFeatures");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SprintProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TimeSheetId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.HasIndex("SprintProjectId");

                    b.HasIndex("TimeSheetId");

                    b.ToTable("ProjectFile");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Project", b =>
                {
                    b.Property<Guid>("ProjectId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("AmountFrequency")
                        .HasColumnType("integer");

                    b.Property<decimal?>("AmountPerSelectedFrequency")
                        .HasColumnType("numeric");

                    b.Property<string>("Clients")
                        .HasColumnType("text");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("ExpectedProjectValue")
                        .HasColumnType("numeric");

                    b.Property<int>("Hours")
                        .HasColumnType("integer");

                    b.Property<bool>("IsBillable")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProjectStatus")
                        .HasColumnType("integer");

                    b.Property<string>("SprintProjectId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("tenantId")
                        .HasColumnType("text");

                    b.HasKey("ProjectId");

                    b.ToTable("ProjectMgmt_Projects");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_ProjectUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.ToTable("projectMgmt_ProjectUsers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Todo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActualTimeSpent")
                        .HasColumnType("text");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("ApprovalStatus")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ClientName")
                        .HasColumnType("text");

                    b.Property<long?>("CompanyReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp");

                    b.Property<long?>("ContactReferenceId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("CreatedByAI")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<long?>("DealReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("ExistingTodoLink")
                        .HasColumnType("text");

                    b.Property<bool>("HasCustomFrequency")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBillable")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMeasurable")
                        .HasColumnType("boolean");

                    b.Property<long?>("KpiReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<long?>("LeadReferenceId")
                        .HasColumnType("bigint");

                    b.Property<bool>("LockTodo")
                        .HasColumnType("boolean");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<Guid?>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SprintProjectId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("TempCreationId")
                        .HasColumnType("text");

                    b.Property<string>("TimeLeft")
                        .HasColumnType("text");

                    b.Property<string>("TimeSpent")
                        .HasColumnType("text");

                    b.Property<string>("TodoDescription")
                        .HasColumnType("text");

                    b.Property<string>("TodoId")
                        .HasColumnType("text");

                    b.Property<string>("TodoName")
                        .HasColumnType("text");

                    b.Property<string>("TodoStatus")
                        .HasColumnType("text");

                    b.Property<string>("TodoSummary")
                        .HasColumnType("text");

                    b.Property<string>("WillBeDueIn")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.HasIndex("SprintProjectId");

                    b.ToTable("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_TodoUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("ExternalMemberEmail")
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("projectMgmt_TodoUsers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.RoleModule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("ModuleName")
                        .HasColumnType("text");

                    b.Property<string>("RegionName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("RoleModules");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.SecDomain", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Domain")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SecDomains");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.TenantProjectView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TenantProjectViews");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.TodoTimeSequence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("TodoStatus")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("TodoTimeSequence");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("BaseCurrency")
                        .HasColumnType("text");

                    b.Property<string>("CV_URL")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<Guid?>("ClientRoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyId")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("Created_At")
                        .HasColumnType("timestamp");

                    b.Property<string>("DateOfBirth")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("EmailVerificationToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EmailVerificationTokenExpiry")
                        .HasColumnType("timestamp");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ForgotPasswordToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ForgotPasswordTokenExpiry")
                        .HasColumnType("timestamp");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<string>("GoogleAuthId")
                        .HasColumnType("text");

                    b.Property<string>("GovernmentId")
                        .HasColumnType("text");

                    b.Property<string>("HouseNo")
                        .HasColumnType("text");

                    b.Property<int>("IndividualUserAccountStatus")
                        .HasColumnType("integer");

                    b.Property<string>("InvitedBy")
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPhoneNumberVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("JobPaysPin")
                        .HasColumnType("text");

                    b.Property<string>("JobProId")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MicrosoftAuthId")
                        .HasColumnType("text");

                    b.Property<string>("MiddleName")
                        .HasColumnType("text");

                    b.Property<DateTime>("Modified_At")
                        .HasColumnType("timestamp");

                    b.Property<string>("NewReference")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("OldReference")
                        .HasColumnType("text");

                    b.Property<bool>("PasswordCreatedByAdmin")
                        .HasColumnType("boolean");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneCountryCode")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("PhoneNumberVerificationToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PhoneNumberVerificationTokenExpiry")
                        .HasColumnType("timestamp");

                    b.Property<string>("Profession")
                        .HasColumnType("text");

                    b.Property<string>("ProfileImageUrl")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProofOfResidence")
                        .HasColumnType("text");

                    b.Property<string>("ReferralCode")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryEmail")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("SendMail")
                        .HasColumnType("boolean");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StatusComment")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserInCompanyRole")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.Property<string>("WeavrId")
                        .HasColumnType("text");

                    b.Property<string>("WeavrPasscode")
                        .HasColumnType("text");

                    b.Property<bool>("WeavrPasscodeForPublic")
                        .HasColumnType("boolean");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.Property<bool>("lockIpAddress")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("ClientRoleId");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("AspNetUsers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserAndRoleId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserProId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAndRoleIds");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserCompanies", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Active")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<int>("EmployementType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<int>("LocationType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("UserCompanies");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<bool>("AuthUserCreated")
                        .HasColumnType("boolean");

                    b.Property<bool>("AuthUserPasswordCreated")
                        .HasColumnType("boolean");

                    b.Property<string>("Bio")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("varchar(24)");

                    b.Property<string>("DOB")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Designation")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EmployerUserID")
                        .HasColumnType("text");

                    b.Property<int>("EraseAcitivity")
                        .HasColumnType("integer");

                    b.Property<string>("EventCategory")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<int>("InternalOrExternal")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSignatory")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("JobPaysPin")
                        .HasColumnType("text");

                    b.Property<string>("JobProId")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("LogActivity")
                        .HasColumnType("boolean");

                    b.Property<string>("MiddleName")
                        .HasColumnType("text");

                    b.Property<string>("MobileLoginPin")
                        .HasColumnType("text");

                    b.Property<int>("OnlineStatus")
                        .HasColumnType("integer");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Profession")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePictureUrl")
                        .HasColumnType("text");

                    b.Property<byte[]>("QrCode")
                        .HasColumnType("bytea");

                    b.Property<string>("SecondaryEmail")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("SignatoryId")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.Property<string>("SuspendedEmployeeId")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<string>("TwoFactorSecretKey")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("UserLoggedOut")
                        .HasColumnType("boolean");

                    b.Property<string>("WeaverCompanyIndustry")
                        .HasColumnType("text");

                    b.Property<string>("WeavrId")
                        .HasColumnType("text");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.Property<int>("_2FAOptions")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("UserProfiles");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserRefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedByIp")
                        .HasColumnType("text");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("timestamp");

                    b.Property<string>("ReplacedByToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("Revoked")
                        .HasColumnType("timestamp");

                    b.Property<string>("RevokedByIp")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserSkill", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Skill")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserSkills");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.WaitingEmailList", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Applications")
                        .HasColumnType("text");

                    b.Property<int>("CompanySize")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("TimeToLaunch")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("WaitingEmailLists");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectPermission", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobProjectPermission");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectRoles", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobProjectRoles");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectRolesPermissions", b =>
                {
                    b.Property<string>("PermissionsId")
                        .HasColumnType("text");

                    b.Property<string>("RolesId")
                        .HasColumnType("text");

                    b.ToTable("JobProjectRolesPermissions");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CannotResceduleWithin")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("EmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<bool>("OutOfOffice")
                        .HasColumnType("boolean");

                    b.Property<string>("OverDueWaitingPeriod")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<bool>("WorkingHours")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("JobProjectSettings");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectUserPermissions", b =>
                {
                    b.Property<string>("PermissionsId")
                        .HasColumnType("text");

                    b.Property<string>("UsersId")
                        .HasColumnType("text");

                    b.ToTable("JobProjectUserPermissions");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectUserRoles", b =>
                {
                    b.Property<string>("RolesId")
                        .HasColumnType("text");

                    b.Property<string>("UsersId")
                        .HasColumnType("text");

                    b.ToTable("JobProjectUserRoles");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.TenantClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ClientName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TenantClients");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.TodoCustomFrequency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("EndStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("EndsAfter")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndsOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<int?>("RepeatCount")
                        .HasColumnType("integer");

                    b.Property<string>("RepeatEvery")
                        .HasColumnType("text");

                    b.Property<string>("RepeatOn")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("TodoCustomFrequency");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.UserPerformanceActivityScoreView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDateForTheWeek")
                        .HasColumnType("timestamp");

                    b.Property<double>("Friday")
                        .HasColumnType("double precision");

                    b.Property<string>("Industries")
                        .IsRequired()
                        .HasColumnType("varchar(24)");

                    b.Property<double>("Monday")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("StartDateForTheWeek")
                        .HasColumnType("timestamp");

                    b.Property<double>("Thursday")
                        .HasColumnType("double precision");

                    b.Property<double>("TotalPercentage")
                        .HasColumnType("double precision");

                    b.Property<double>("Tuesday")
                        .HasColumnType("double precision");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<double>("Wednesday")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.ToTable("PerformanceMetricAnalyses");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectMetricsView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int>("Month")
                        .HasColumnType("integer");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.Property<double>("TotalHours")
                        .HasColumnType("double precision");

                    b.Property<int>("TotalSprints")
                        .HasColumnType("integer");

                    b.Property<int>("TotalTodo")
                        .HasColumnType("integer");

                    b.Property<int>("Year")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("ProjectMetricsViews");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectSprintMemberId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric(18,4)");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("ExternalMemberEmail")
                        .HasColumnType("text");

                    b.Property<string>("MemberId")
                        .HasColumnType("text");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ProjectSprintMemberIds");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<string>("TagName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TimeSheetId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.HasIndex("TimeSheetId");

                    b.ToTable("ProjectTag");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTrigger", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("Email")
                        .HasColumnType("boolean");

                    b.Property<bool>("Notification")
                        .HasColumnType("boolean");

                    b.Property<string>("ParticipantsIds")
                        .HasColumnType("text");

                    b.Property<string>("ProjectMgmt_ProjectId")
                        .HasColumnType("text");

                    b.Property<string>("Reasons")
                        .HasColumnType("text");

                    b.Property<bool>("SMS")
                        .HasColumnType("boolean");

                    b.Property<string>("TriggerName")
                        .HasColumnType("text");

                    b.Property<int>("TriggerReason")
                        .HasColumnType("integer");

                    b.Property<DateTime>("TriggerTime")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("projectMgmt_ProjectProjectId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("projectMgmt_ProjectProjectId");

                    b.ToTable("ProjectTriggers");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.SprintProject", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("SprintProjects");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TagId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("PrjectTagId")
                        .HasColumnType("text");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.Property<string>("TodoId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TagId");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.Team", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Teams");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TeamMembers", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Role")
                        .HasColumnType("text");

                    b.Property<Guid>("TeamId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TeamId");

                    b.ToTable("TeamMembers");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActualTimeSpent")
                        .HasColumnType("text");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric(18,4)");

                    b.Property<string>("AssignedTo")
                        .HasColumnType("text");

                    b.Property<string>("ClientName")
                        .HasColumnType("text");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CompletionDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DateLogged")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBillable")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<int?>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("ProjectMgmt_ProjectId")
                        .HasColumnType("text");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<string>("TimeSpent")
                        .HasColumnType("text");

                    b.Property<string>("TodoName")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<Guid?>("projectMgmt_ProjectProjectId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("projectMgmt_ProjectProjectId");

                    b.ToTable("TimeSheet");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheetMemberId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("MemberId")
                        .HasColumnType("text");

                    b.Property<string>("TimesheetId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TimeSheetMemberIds");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoComments", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .HasColumnType("text");

                    b.Property<string>("CommentedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("TodoId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("TodoId");

                    b.ToTable("TodoComments");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("SprintId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<Guid>("TodoId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SprintId");

                    b.HasIndex("TodoId")
                        .IsUnique();

                    b.ToTable("TodoOrder");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SprintId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("SprintId");

                    b.ToTable("TodoStatus");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TriggerSequence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<DateTime>("DateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Email")
                        .HasColumnType("boolean");

                    b.Property<bool>("Notification")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ProjectTriggerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("SMS")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("ProjectTriggerId");

                    b.ToTable("TriggerSequence");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Event")
                        .HasColumnType("integer");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.UserNotification", b =>
                {
                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Disappear")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ReadOn")
                        .HasColumnType("timestamp");

                    b.HasKey("UserProfileId", "NotificationId");

                    b.HasIndex("NotificationId");

                    b.ToTable("UserNotifications");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AIAgent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Agent")
                        .HasColumnType("text");

                    b.Property<double>("AmountPerMonthPerUser")
                        .HasColumnType("double precision");

                    b.Property<double>("AmountPerYearPerUser")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("AIAgents");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AISubscriptionDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Agent")
                        .HasColumnType("integer");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("NoOfUserSubscribedFor")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("AISubscriptionDetails");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AdditionalLiecense", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Updated")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserIdsJson")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("AdditionalLiecenses");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.BillingAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("StreetName")
                        .HasColumnType("text");

                    b.Property<string>("StreetNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BillingAddresses");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.ClientCards", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("CardNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CardNumberFirstSix")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CardNumberLastFour")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("CardType")
                        .HasColumnType("integer");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<bool>("Country")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Cvv")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryMmyy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<bool>("ZipCode")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("ClientCards");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.CompanySubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("AIAgent")
                        .HasColumnType("integer");

                    b.Property<int?>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TenantId");

                    b.ToTable("CompanySubscriptions");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterpriseSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("ActivityLogHistoryLimit")
                        .HasColumnType("integer");

                    b.Property<bool>("AiAssistant")
                        .HasColumnType("boolean");

                    b.Property<double>("AmountPaid")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<int>("CalenderLimit")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<int>("DataRetentionPeriodInMonth")
                        .HasColumnType("integer");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("InternalCommunicationHistoryLimit")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PlanId")
                        .HasColumnType("text");

                    b.Property<int>("ProjectLimit")
                        .HasColumnType("integer");

                    b.Property<int>("StorageLimit")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<bool>("TimeSheetManagement")
                        .HasColumnType("boolean");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("UsersLimit")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TenantId");

                    b.ToTable("EnterpriseSubscriptions");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterprizeSubscriptionPayment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("CompanyEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<bool>("EnterprizeFreePlan")
                        .HasColumnType("boolean");

                    b.Property<int>("Frequency")
                        .HasColumnType("integer");

                    b.Property<bool>("IsExistingUser")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentLink")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("integer");

                    b.Property<bool>("PaymentUsed")
                        .HasColumnType("boolean");

                    b.Property<string>("PersonalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("EnterprizeSubscriptionPayments");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.Feature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("FeatureName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Features");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.FeedbackReviewsAndRatings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("FeedBack")
                        .HasColumnType("text");

                    b.Property<int>("StarRating")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("FeedbackReviewsAndRatings");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.PackagePricing", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<double?>("PricePerMonth")
                        .HasColumnType("double precision");

                    b.Property<double?>("PricePerMonthForYearlyOption")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int?>("Region")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("PackagePricing");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.PricingPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("PricingPlans");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.StripeSubscriptionDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AIAgent")
                        .HasColumnType("boolean");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("Plan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PriceId")
                        .HasColumnType("text");

                    b.Property<string>("ProductId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StripeSubscriptionDetails");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ContractEndsOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("FreeTrialOptionSelected")
                        .HasColumnType("boolean");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<bool>("IsAISubscription")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RetrySubAttempt")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int>("SubscriptionCount")
                        .HasColumnType("integer");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.ToTable("Subscriptions");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.SubscriptionHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("FreeTrialOptionSelected")
                        .HasColumnType("boolean");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RetrySubAttempt")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int>("SubscriptionCount")
                        .HasColumnType("integer");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.ToTable("SubscriptionHistory");
                });

            modelBuilder.Entity("Jobid.App.Tenant.Model.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdminId")
                        .HasColumnType("text");

                    b.Property<string>("CompanyAddress")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int>("CompanySize")
                        .HasColumnType("integer");

                    b.Property<string>("CompanyType")
                        .HasColumnType("text");

                    b.Property<string>("ContactNo")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Industry")
                        .IsRequired()
                        .HasColumnType("varchar(24)");

                    b.Property<DateTime>("LastMigration")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("RegNumber")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("VerifiedEmailDomain")
                        .HasColumnType("text");

                    b.Property<string>("WorkSpace")
                        .HasColumnType("text");

                    b.Property<bool>("isSchemaCreated")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("AdminId");

                    b.ToTable("Tenants");
                });

            modelBuilder.Entity("Jobid.App.Wiki.Models.WikiAccess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessStatus")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("WikiAccesses");
                });

            modelBuilder.Entity("Jobid.App.Wiki.Models.WikiContent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AwsKey")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("BatchId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FileName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastAccessedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("TextContent")
                        .HasColumnType("text");

                    b.Property<int>("UploadStatus")
                        .HasColumnType("integer");

                    b.Property<int>("WikiFileType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("WikiFiles");
                });

            modelBuilder.Entity("Jobid.App.Wiki.Models.WikiFileDepartmentAccess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("RemovedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("WikiFileId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("WikiFileId");

                    b.ToTable("WikiFileDepartmentAccess");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.LogAttachment", b =>
                {
                    b.HasOne("Jobid.App.ActivityLog.Model.Activity", "Activity")
                        .WithMany("LogAttachments")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.BillingInformation", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Calls.CallRecord", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.Phone.PhoneNumber", "PhoneNumber")
                        .WithMany()
                        .HasForeignKey("PhoneNumberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PhoneNumber");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.CampaignContact", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.ContactCampaign", "Campaign")
                        .WithMany("CampaignContacts")
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.AdminConsole.Models.UserContact", "Contact")
                        .WithMany()
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeeRolesPermission", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.EmployeePermission", "EmployeePermission")
                        .WithMany("EmployeeRolesPermissions")
                        .HasForeignKey("PermissionId");

                    b.HasOne("Jobid.App.AdminConsole.Models.EmployeeRoles", "EmployeeRoles")
                        .WithMany("EmployeeRolesPermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("EmployeePermission");

                    b.Navigation("EmployeeRoles");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.LiveKit.LiveKitParticipant", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.LiveKit.LiveKitRoom", "LiveKitRoom")
                        .WithMany()
                        .HasForeignKey("LiveKitRoomId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LiveKitRoom");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Phone.PhoneNumberAssignment", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.Phone.PhoneNumber", "PhoneNumber")
                        .WithMany("Assignments")
                        .HasForeignKey("PhoneNumberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PhoneNumber");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Phone.PhoneNumberCapability", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.Phone.PhoneNumber", "PhoneNumber")
                        .WithMany("Capabilities")
                        .HasForeignKey("PhoneNumberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PhoneNumber");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Wallet.WalletTransaction", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.Wallet.CompanyWallet", "Wallet")
                        .WithMany()
                        .HasForeignKey("WalletId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Wallet");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedExternalMeeting", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderUpload", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "CalenderMeeting")
                        .WithMany()
                        .HasForeignKey("MeetingId");

                    b.HasOne("Jobid.App.Calender.Models.SubsequentMeeting", "SubsequentMeeting")
                        .WithMany()
                        .HasForeignKey("SubsequentMeetingId");

                    b.Navigation("CalenderMeeting");

                    b.Navigation("SubsequentMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomFrequency", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "CalenderMeeting")
                        .WithOne("CustomFrequency")
                        .HasForeignKey("Jobid.App.Calender.Models.CustomFrequency", "CalenderMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CalenderMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestion", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany("CustomQuestion")
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestionAnswer", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.BookedExternalMeeting", "BookedExternalMeeting")
                        .WithMany("CustomQuestionAnswer")
                        .HasForeignKey("BookedExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BookedExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeeting", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeetingQuestion", "ExternalMeetingQuestion")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Calender.Models.PersonalSchedule", "PersonalSchedule")
                        .WithMany()
                        .HasForeignKey("PersonalScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeetingQuestion");

                    b.Navigation("PersonalSchedule");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingTimeManagement", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Calender.Models.PersonalSchedule", "PersonalSchedule")
                        .WithMany()
                        .HasForeignKey("PersonalScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");

                    b.Navigation("PersonalSchedule");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.MeetingNote", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "Meeting")
                        .WithMany()
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Calender.Models.SubsequentMeeting", "SubsequentMeeting")
                        .WithMany()
                        .HasForeignKey("SubsequentMeetingId");

                    b.Navigation("Meeting");

                    b.Navigation("SubsequentMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.PersonalSchedule", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId1");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ProposedDateDetail", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "Meeting")
                        .WithOne("ProposedDateDetail")
                        .HasForeignKey("Jobid.App.Calender.Models.ProposedDateDetail", "MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Meeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.RoundRobinHostingOrder", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.SubsequentMeeting", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "CalenderMeeting")
                        .WithMany()
                        .HasForeignKey("CalenderMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CalenderMeeting");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRoleRoleModule", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ClientRole", "ClientRole")
                        .WithMany("ClientRoleRoleModules")
                        .HasForeignKey("ClientRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.RoleModule", "RoleModule")
                        .WithMany("ClientRoleRoleModules")
                        .HasForeignKey("RoleModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClientRole");

                    b.Navigation("RoleModule");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Permission", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.RoleModule", "Rolemodule")
                        .WithMany("Permission")
                        .HasForeignKey("RoleModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rolemodule");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.PricingAndFeature", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Feature", "Feature")
                        .WithMany()
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Feature");

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectFile", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("ProjectMgmt_ProjectId");

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "projectMgmt_Todo")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "SprintProject")
                        .WithMany()
                        .HasForeignKey("SprintProjectId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.TimeSheet", "TimeSheet")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("TimeSheetId");

                    b.Navigation("projectMgmt_Project");

                    b.Navigation("projectMgmt_Todo");

                    b.Navigation("SprintProject");

                    b.Navigation("TimeSheet");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_ProjectUser", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("projectMgmt_ProjectUsers")
                        .HasForeignKey("ProjectMgmt_ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("projectMgmt_Project");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Todo", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("Todos")
                        .HasForeignKey("ProjectMgmt_ProjectId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "SprintProject")
                        .WithMany()
                        .HasForeignKey("SprintProjectId");

                    b.Navigation("projectMgmt_Project");

                    b.Navigation("SprintProject");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_TodoUser", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "ProjectMgmt_Todo")
                        .WithMany("ProjectMgmt_TodoUsers")
                        .HasForeignKey("ProjectMgmt_TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.SecDomain", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.TodoTimeSequence", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "ProjectMgmt_Todo")
                        .WithMany("TodoTimeSequence")
                        .HasForeignKey("ProjectMgmt_TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.User", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ClientRole", "ClientRole")
                        .WithMany("user")
                        .HasForeignKey("ClientRoleId");

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", null)
                        .WithMany("Members")
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.Navigation("ClientRole");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserAndRoleId", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Models.EmployeeRoles", "EmployeeRoles")
                        .WithMany("UserEmployeeAppRole")
                        .HasForeignKey("RoleId");

                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany("UserEmployeeAppRole")
                        .HasForeignKey("UserId");

                    b.Navigation("EmployeeRoles");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserCompanies", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.User", "user")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("tenant");

                    b.Navigation("user");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserRefreshToken", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserSkill", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.TodoCustomFrequency", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "ProjectMgmt_Todo")
                        .WithMany()
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.Navigation("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTag", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("ProjectTags")
                        .HasForeignKey("ProjectMgmt_ProjectId");

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "projectMgmt_Todo")
                        .WithMany("ProjectTags")
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.TimeSheet", "TimeSheet")
                        .WithMany("ProjectTags")
                        .HasForeignKey("TimeSheetId");

                    b.Navigation("projectMgmt_Project");

                    b.Navigation("projectMgmt_Todo");

                    b.Navigation("TimeSheet");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTrigger", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("ProjectTriggers")
                        .HasForeignKey("projectMgmt_ProjectProjectId");

                    b.Navigation("projectMgmt_Project");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TeamMembers", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.Team", "Team")
                        .WithMany()
                        .HasForeignKey("TeamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Team");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheet", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("TimeSheet")
                        .HasForeignKey("projectMgmt_ProjectProjectId");

                    b.Navigation("projectMgmt_Project");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoComments", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "Todo")
                        .WithMany("TodoComments")
                        .HasForeignKey("TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Todo");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoOrder", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "Sprint")
                        .WithMany()
                        .HasForeignKey("SprintId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "Todo")
                        .WithOne("TodoOrder")
                        .HasForeignKey("Jobid.App.JobProjectManagement.Models.TodoOrder", "TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sprint");

                    b.Navigation("Todo");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoStatus", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "Sprint")
                        .WithMany()
                        .HasForeignKey("SprintId");

                    b.Navigation("Sprint");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TriggerSequence", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.ProjectTrigger", "ProjectTrigger")
                        .WithMany()
                        .HasForeignKey("ProjectTriggerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectTrigger");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.UserNotification", b =>
                {
                    b.HasOne("Jobid.App.Notification.Models.Notification", "Notification")
                        .WithMany("UserNotification")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "UserProfile")
                        .WithMany()
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AISubscriptionDetail", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AdditionalLiecense", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.ClientCards", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.CompanySubscription", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId");

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterpriseSubscription", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterprizeSubscriptionPayment", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.FeedbackReviewsAndRatings", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.PackagePricing", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.Subscription", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.BillingAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.Navigation("BillingAddress");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.SubscriptionHistory", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.BillingAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.Navigation("BillingAddress");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Tenant.Model.Tenant", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "Admin")
                        .WithMany()
                        .HasForeignKey("AdminId");

                    b.Navigation("Admin");
                });

            modelBuilder.Entity("Jobid.App.Wiki.Models.WikiFileDepartmentAccess", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.Team", "Team")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Wiki.Models.WikiContent", "WikiFile")
                        .WithMany("DepartmentAccess")
                        .HasForeignKey("WikiFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Team");

                    b.Navigation("WikiFile");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.Activity", b =>
                {
                    b.Navigation("LogAttachments");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.ContactCampaign", b =>
                {
                    b.Navigation("CampaignContacts");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeePermission", b =>
                {
                    b.Navigation("EmployeeRolesPermissions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.EmployeeRoles", b =>
                {
                    b.Navigation("EmployeeRolesPermissions");

                    b.Navigation("UserEmployeeAppRole");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Models.Phone.PhoneNumber", b =>
                {
                    b.Navigation("Assignments");

                    b.Navigation("Capabilities");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedExternalMeeting", b =>
                {
                    b.Navigation("CustomQuestionAnswer");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderMeeting", b =>
                {
                    b.Navigation("CustomFrequency");

                    b.Navigation("ProposedDateDetail");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeeting", b =>
                {
                    b.Navigation("CustomQuestion");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRole", b =>
                {
                    b.Navigation("ClientRoleRoleModules");

                    b.Navigation("user");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Project", b =>
                {
                    b.Navigation("ProjectFiles");

                    b.Navigation("projectMgmt_ProjectUsers");

                    b.Navigation("ProjectTags");

                    b.Navigation("ProjectTriggers");

                    b.Navigation("TimeSheet");

                    b.Navigation("Todos");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Todo", b =>
                {
                    b.Navigation("Members");

                    b.Navigation("ProjectFiles");

                    b.Navigation("ProjectMgmt_TodoUsers");

                    b.Navigation("ProjectTags");

                    b.Navigation("TodoComments");

                    b.Navigation("TodoOrder");

                    b.Navigation("TodoTimeSequence");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.RoleModule", b =>
                {
                    b.Navigation("ClientRoleRoleModules");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.User", b =>
                {
                    b.Navigation("UserEmployeeAppRole");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheet", b =>
                {
                    b.Navigation("ProjectFiles");

                    b.Navigation("ProjectTags");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.Notification", b =>
                {
                    b.Navigation("UserNotification");
                });

            modelBuilder.Entity("Jobid.App.Wiki.Models.WikiContent", b =>
                {
                    b.Navigation("DepartmentAccess");
                });
#pragma warning restore 612, 618
        }
    }
}
