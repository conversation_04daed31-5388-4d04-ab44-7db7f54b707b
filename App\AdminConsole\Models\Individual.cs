﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    public class Individual
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public Guid DepartmentId { get; set; }
        public Guid CompanyId { get; set; }
        //    public Guid UserId { get; set; }
        public Guid PositionId { get; set; }
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long index { get; set; }
        public string EmailAddress { get; set; }
        public long BelongsTo { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string Name { get; set; }
        public string Title { get; set; }
        public string BranchColor { get; set; }
        public bool IsHeadofDepartment { get; set; }
    }
}