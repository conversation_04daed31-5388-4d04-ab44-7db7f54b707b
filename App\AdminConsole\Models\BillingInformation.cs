using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    /// <summary>
    /// Billing information for company
    /// </summary>
    public class BillingInformation
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Tenant ID this billing information belongs to
        /// </summary>
        [Required]
        public Guid TenantId { get; set; }

        /// <summary>
        /// First name of billing contact
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; }

        /// <summary>
        /// Last name of billing contact
        /// </summary>
        [Required]
        [StringLength(100)]
        public string LastName { get; set; }

        /// <summary>
        /// Email address for billing contact
        /// </summary>
        [Required]
        [StringLength(255)]
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// Country for billing address
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Country { get; set; }

        /// <summary>
        /// Company address for billing
        /// </summary>
        [Required]
        [StringLength(500)]
        public string CompanyAddress { get; set; }

        /// <summary>
        /// Business registration number
        /// </summary>
        [StringLength(100)]
        public string BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// VAT number
        /// </summary>
        [StringLength(100)]
        public string VATNumber { get; set; }

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When this record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created this record
        /// </summary>
        [StringLength(450)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// User who last updated this record
        /// </summary>
        [StringLength(450)]
        public string UpdatedBy { get; set; }

        /// <summary>
        /// Navigation property to Tenant
        /// </summary>
        [ForeignKey("TenantId")]
        public virtual Tenant.Model.Tenant Tenant { get; set; }
    }
}
