﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class addedjobproidtouserstbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public addedjobproidtouserstbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "RescheduleCount",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "JobProId",
                schema: _Schema,
                table: "AspNetUsers",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RescheduleCount",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "JobProId",
                schema: _Schema,
                table: "AspNetUsers");
        }
    }
}
