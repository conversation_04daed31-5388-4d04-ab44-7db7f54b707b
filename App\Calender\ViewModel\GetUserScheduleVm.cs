﻿using Microsoft.Graph.Models;
using Microsoft.Kiota.Abstractions;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class GetUserScheduleVm
    {
        [Required]
        public string UserId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? DailyDate { get; set; }
        public bool CreatedByAI { get; set; } = false;
    }
}
