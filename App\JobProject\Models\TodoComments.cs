﻿using Jobid.App.Helpers.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Jobid.App.Helpers.Utils.Extensions;

namespace Jobid.App.JobProjectManagement.Models
{
    public class TodoComments
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public string Comment { get; set; }
        [ForeignKey(nameof(ProjectMgmt_Todo))]
        public Guid TodoId { get; set; }
        public string CommentedBy { get; set; }
        public DateTime CreatedAt { get; set; } = GetAdjustedDateTimeBasedOnTZNow();
        public DateTime UpdatedAt { get; set; } = GetAdjustedDateTimeBasedOnTZNow();

        // Navigational properties
        public ProjectMgmt_Todo Todo { get; set; }
        [NotMapped]
        public UserDto Commenter { get; set; }
    }
}
