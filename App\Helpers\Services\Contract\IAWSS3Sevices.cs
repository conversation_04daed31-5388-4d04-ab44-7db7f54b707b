using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface IAWSS3Sevices
    {
        // Existing methods
        Task<string> GetSignedUrlAsync(string fileNameToRead, int timeOutInMinutes = 10080);
        Task<string> UploadFileAsync(IFormFile fileToUpload, string fileNameToSave);
        Task<bool> DeleteFileAsync(string fileNameToDelete);
        Task<string> DownloadFileAndConvertToBase64(string fileNameToDownload);
        Task<string> UploadFileWithLimitAsync(IFormFile fileToUpload, string fileNameToSave, int fileSizeLimit = 5);
        Task<string> UploadLargeFileAsync(IFormFile fileToUpload, string fileNameToSave, int fileSizeThresholdInMB = 50);
        
        // New methods for Wiki file uploads
        Task<string> UploadFileFromStreamAsync(Stream fileStream, string fileNameToSave, string contentType);
        Task<(string uploadId, string key)> InitiateMultipartUploadAsync(string fileNameToSave, string contentType);
        Task<string> UploadPartAsync(string key, string uploadId, int partNumber, Stream partStream, long partSize);
        Task<bool> CompleteMultipartUploadAsync(string key, string uploadId, Dictionary<int, string> partETags);
        Task<bool> AbortMultipartUploadAsync(string key, string uploadId);
        Task<string> GetPresignedUrlForKeyAsync(string key, int expiryTimeInMinutes = 60);
    }
}
