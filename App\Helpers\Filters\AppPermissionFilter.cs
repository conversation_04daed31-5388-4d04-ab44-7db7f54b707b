﻿/*using Google;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Jobid.App.Helpers.Context;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Jobid.App.Helpers.Filters
{
    public class AppPermissionFilter
    {
    }
    public class PackageAuthorizationFilter : IAsyncAuthorizationFilter
    {
        private readonly JobProDbContext _dbContext;
        private readonly string _requiredPackage;

        public PackageAuthorizationFilter(JobProDbContext dbContext, string requiredPackage)
        {
            _dbContext = dbContext;
            _requiredPackage = requiredPackage;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var userIdentity = context.HttpContext.User.Identity;

            if (userIdentity.IsAuthenticated)
            {
                // Get the user's ID based on their username (or another identifier)
                var userId = await _dbContext.Users
                    .Where(u => u.UserName == userIdentity.Name)
                    .Select(u => u.Id)
                    .FirstOrDefaultAsync();

                // Check if the user has access to the required package
                var hasAccess = await _dbContext.UserPackages
                    .AnyAsync(up => up.UserId == userId && up.PackageName == _requiredPackage);

                if (hasAccess)
                {
                    // User has access to the required package
                    // No need to set any context.Result here
                    // They can continue accessing the endpoint without restrictions
                }
            }
            else
            {
                // User is not authenticated, return 401 Unauthorized or handle as needed
                context.Result = new UnauthorizedResult();
            }
        }

        Task IAsyncAuthorizationFilter.OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            throw new System.NotImplementedException();
        }
    }
 
}


*/

using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Jobid.App.Helpers.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;

public class RolePermissionAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly string _packageName;
    private readonly string _permission;
    private readonly string[] _roles;
    private readonly JobProDbContext _dbContext;

    public RolePermissionAttribute(string packageName, string permission, params string[] roles)
    {
        _packageName = packageName;
        _permission = permission;
        _roles = roles;
        _dbContext = new JobProDbContext();
    }

    async Task IAsyncAuthorizationFilter.OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        // Skip authorization if action is decorated with [AllowAnonymous] attribute
        if (context.ActionDescriptor.EndpointMetadata
            .Any(item => item.GetType().Equals(typeof(AllowAnonymousAttribute))))
        {
            return;
        }
       
        var userId = context.HttpContext.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            context.Result = new JsonResult(new { Status = "Failed", Message = "Unauthorized User" })
            { StatusCode = 401 };
            return;
        }
        var userRole = await _dbContext.Users.Where(u => u.Id == userId).Select(u => u.UserInCompanyRole).FirstOrDefaultAsync();
        if (userRole == null)
        {
            context.Result = new JsonResult(new { Status = "Failed", Message = "Unauthorized User" })
            { StatusCode = 401 };
            return;
            //userRole = "Admin";
         }
        if (!string.IsNullOrEmpty(_packageName) && _roles.Contains(userRole))
        {
            var roleDetails = await _dbContext.EmployeeRoles.Where(e => e.RoleName == userRole && e.PackageName == _packageName).FirstOrDefaultAsync();
            var permission = await _dbContext.EmployeeRolesPermissions.Include(r => r.EmployeePermission).Where(r => r.RoleId == roleDetails.Id && r.EmployeePermission.PackageName == _packageName)
                .Select(r => r.EmployeePermission.PermissionName).ToListAsync();
            if (!string.IsNullOrEmpty(_permission) && permission.Any(p => p.Contains(_permission)))
            {
                return;
            }
            else if (string.IsNullOrEmpty(_permission))
            {
                context.Result = new UnauthorizedResult();
            }
        }
        context.Result = new UnauthorizedResult();

    }


}
