﻿using System;

namespace Jobid.App.AdminConsole.Dto
{
    public class CallDetailsDto
    {
        public string CallSid { get; set; }
        public string AccountSid { get; set; }
        public string From { get; set; }
        public string To { get; set; }
        public string CallerName { get; set; }
        public string Direction { get; set; }
        public string Status { get; set; }
        public string Duration { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string Price { get; set; }
        public string PriceUnit { get; set; }
        public string ForwardedFrom { get; set; }
        public string PhoneNumberSid { get; set; }
        public string ParentCallSid { get; set; }
        public string Annotation { get; set; }
        public string AnsweredBy { get; set; }
        public string ApiVersion { get; set; }
        public string Uri { get; set; }

        // Subresources
        public string RecordingsUri { get; set; }
        public string FeedbackUri { get; set; }
        public string NotificationsUri { get; set; }
    }
}
