﻿using AutoMapper;
using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.ViewModel;
using Jobid.App.RabbitMQ;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WatchDog;
using static Jobid.App.RabbitMQ.Records;

namespace Calendar.Service.RabbitMQ.Consumers
{
    public class CreateMeetingConsumer : BackgroundService
    {
        private readonly IMapper _mapper;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private Serilog.ILogger _logger = Log.ForContext<CreateMeetingConsumer>();
        private readonly Dictionary<string, object> args;
        private readonly ICalenderService _calenderService;

        public CreateMeetingConsumer(IMapper mapper, IServiceScopeFactory serviceScopeFactory)
        {
            _mapper = mapper;
            _serviceScopeFactory = serviceScopeFactory;

            // Get the generic consumer from the service scope factory
            using var scope = serviceScopeFactory.CreateScope();
            _calenderService = scope.ServiceProvider.GetRequiredService<ICalenderService>();

            // Set how long the message should live on the queue i.e ttl
            var expiration = **********; // 100 hours
            args = new Dictionary<string, object>
            {
                {"x-message-ttl", expiration}
            };
        }

        public async virtual Task StartConsumer(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                stoppingToken.ThrowIfCancellationRequested();
                var model = new ConsumeModel(RabbitMQConstants.AICreateMeetingQueue, RabbitMQConstants.AICreateMeetingEvent, "", ExchangeType.Fanout);

                using var scope = _serviceScopeFactory.CreateScope();
                var rabbitMQConnectionService = scope.ServiceProvider.GetRequiredService<IRabbitMQConnectionService>();
                var channel = rabbitMQConnectionService._channel;

                if (channel is not null)
                {
                    channel.QueueDeclare(queue: model.QueueName, durable: true, exclusive: false, autoDelete: false, arguments: args);
                    channel.QueueBind(queue: model.QueueName, exchange: model.ExchangeName, routingKey: "");
                    //channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);
                    var consumer = new AsyncEventingBasicConsumer(channel);

                    Console.WriteLine("Monolithic Service - CreateMeetingConsumer: Waiting for messages...");
                    consumer.Received += async (ch, ea) =>
                    {
                        Console.WriteLine("CreateMeetingConsumer - Inside: Waiting for messages...");
                        var message = Encoding.UTF8.GetString(ea.Body.ToArray());
                        await CreateMeeting(message);

                        _logger.Information($"{model.QueueName} message received: {message}");
                        WatchLogger.Log($"{model.QueueName} message received: {message}");
                        //channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                    };

                    channel.BasicConsume(queue: model.QueueName, autoAck: true, consumer: consumer);
                    await Task.Delay(100);
                }
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Run(() => StartConsumer(stoppingToken));
        }

        private async Task CreateMeeting(string message)
        {
            var meetingPayload = JsonConvert.DeserializeObject<CalenderVm>(message);
            if (meetingPayload != null)
            {
                var response = await _calenderService.CreateMeeting(meetingPayload);
                if (response == null)
                    _logger.Error("CreateMeetingConsumer: Meeting creation failed");
            }
        }
    }
}
