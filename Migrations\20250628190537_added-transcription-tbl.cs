﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class addedtranscriptiontbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public addedtranscriptiontbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CallTranscriptions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyId = table.Column<string>(type: "text", nullable: true),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    FromNumber = table.Column<string>(type: "text", nullable: false),
                    ToNumber = table.Column<string>(type: "text", nullable: false),
                    Transcription = table.Column<string>(type: "text", nullable: false),
                    CustomerName = table.Column<string>(type: "text", nullable: true),
                    CallDuration = table.Column<double>(type: "double precision", nullable: false),
                    CallDirection = table.Column<int>(type: "integer", nullable: false),
                    CustomerEmail = table.Column<string>(type: "text", nullable: true),
                    EmployeeName = table.Column<string>(type: "text", nullable: true),
                    MeetingDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    MeetingTime = table.Column<TimeSpan>(type: "interval", nullable: true),
                    DurationInMinutes = table.Column<int>(type: "integer", nullable: true),
                    ActionStatus = table.Column<int>(type: "integer", nullable: false),
                    CallDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CallTranscriptions", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CallTranscriptions",
                schema: _Schema);
        }
    }
}
