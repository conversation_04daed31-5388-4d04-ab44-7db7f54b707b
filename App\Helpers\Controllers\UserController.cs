﻿using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Utils._Helper;
using Jobid.App.Helpers.ViewModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Controllers
{
    [Route("api/[controller]")]
    [Produces("Application/json")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly IUserServices _userServices;

        public UserController(IUserServices userServices)
        {
            _userServices = userServices;
        }

        #region Add or Update User Skills
        /// <summary>
        /// Adds or updates user skills.
        /// </summary>
        /// <param name="addOrUpdateSkillsDto"></param>
        /// <returns></returns>
        [HttpPost("AddOrUpdateSkills")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> AddOrUpdateSkills([FromBody] AddOrUpdateSkillsDto addOrUpdateSkillsDto)
        {
            var result = await _userServices.AddOrUpdateUserSkills(addOrUpdateSkillsDto);
            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
        #endregion

        #region Get User Skills
        /// <summary>
        /// Get user skills.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("GetUserSkills")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetSkills([FromQuery] string userId)
        {
            var result = await _userServices.GetUserSkills(userId);
            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
        #endregion

        #region Delete User Skills
        /// <summary>
        /// Delete user skills.
        /// </summary>
        /// <param name="skillIds"></param>
        /// <returns></returns>
        [HttpGet("DeleteUserSkills")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> DeleteSkills([FromBody] List<string> skillIds)
        {
            var result = await _userServices.DeleteUserSkills(skillIds);
            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
        #endregion

        #region Account Deletion Request
        /// <summary>
        /// Account Deletion Request
        /// </summary>
        /// <param name="requestForAccountDeletionDto"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost("AccountDeletionRequest")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> RequestForAccountDeletion([FromBody] RequestForAccountDeletionDto requestForAccountDeletionDto)
        {
            requestForAccountDeletionDto.RequestedBy = User.GetUserId();
            var result = await _userServices.RequestForAccountDeletion(requestForAccountDeletionDto);
            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
        #endregion

        #region Cancel Account Deletion Request
        /// <summary>
        /// Cancel Account Deletion Request
        /// </summary>
        /// <param name="requestId"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost("CancelAccountDeletion/{requestId}")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> CancelAccountDeletion([FromRoute] string requestId)
        {
            var result = await _userServices.CancelAccountDeletion(requestId);
            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
        #endregion

        #region Get Account Deletion Request
        /// <summary>
        /// Get Account Deletion Request
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet("GetAccountDeletionRequest")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetAccountDeletionRequests()
        {
            var userId = User.GetUserId();
            var result = await _userServices.GetCompanyDeletionRequests(userId);
            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
        #endregion
    }
}
