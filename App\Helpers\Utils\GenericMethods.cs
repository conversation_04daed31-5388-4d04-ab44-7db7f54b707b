﻿using Microsoft.AspNetCore.Http;
using OfficeOpenXml;
using System.Collections.Generic;
using System.IO;
using System;
using System.Threading.Tasks;
using Jobid.App.Helpers.Exceptions;
using ExcelDataReader;
using System.Data;
using System.Text;

namespace Jobid.App.Helpers.Utils
{
    public static class GenericMethods
    {
        #region Excel Reader Processor using EPPlus
        public static async Task<List<T>> ProcessExcelFileAsync<T>(IFormFile file, Func<ExcelRange, T> mapFunction) where T : class
        {
            var entities = new List<T>();

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                using (var package = new ExcelPackage(stream))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[0]; // Assuming the first worksheet
                    int rowCount = worksheet.Dimension.Rows;

                    for (int row = 2; row <= rowCount; row++) // Start from row 2 to skip the header
                    {
                        var entity = mapFunction(worksheet.Cells[row, 1, row, worksheet.Dimension.Columns]);

                        if (entity != null)
                        {
                            entities.Add(entity);
                        }
                    }
                }
            }

            return entities;
        }
        #endregion

        #region Excel Reader Processor using ExcelDataReader
        public static async Task<List<T>> ProcessExcelFileAsync<T>(IFormFile file, Func<IDataRecord, T> mapFunction) where T : class
        {
            var entities = new List<T>();
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0; // Reset the stream position after copying

                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    // Skip the header row
                    if (reader.Read())
                    {
                        while (reader.Read())
                        {
                            var entity = mapFunction(reader);
                            if (entity != null)
                            {
                                entities.Add(entity);
                            }
                        }
                    }
                }
            }

            return entities;
        }
        #endregion

        #region CSV Reader Processor
        public static async Task<List<T>> ProcessCsvFileAsync<T>(IFormFile file, Func<string[], T> mapFunction) where T : class
        {
            var entities = new List<T>();

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0; // Reset the stream position after copying

                using (var reader = new StreamReader(stream, Encoding.UTF8))
                {
                    bool isFirstRow = true;

                    while (!reader.EndOfStream)
                    {
                        var line = await reader.ReadLineAsync();
                        if (isFirstRow)
                        {
                            // Skip the header row
                            isFirstRow = false;
                            continue;
                        }

                        if (!string.IsNullOrEmpty(line))
                        {
                            var columns = line.Split(','); // Adjust separator if needed
                            var entity = mapFunction(columns);

                            if (entity != null)
                            {
                                entities.Add(entity);
                            }
                        }
                    }
                }
            }

            return entities;
        }
        #endregion
    }
}
