﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System;
using System.ComponentModel.DataAnnotations;
using Jobid.App.Helpers.Models;

namespace Jobid.App.AdminConsole.Models
{
    public class EmployeeRoles
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string RoleName { get; set; }
        public string PackageName { get; set; }
        public List<EmployeeRolesPermission> EmployeeRolesPermissions { get; set; } = new List<EmployeeRolesPermission>();
        public List<UserAndRoleId> UserEmployeeAppRole { get; set; } = new List<UserAndRoleId>();
    }

    public class EmployeePermission
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PermissionName { get; set; }
        public string PackageName { get; set; }
        public string PermissionCategory { get; set; }
        public List<EmployeeRolesPermission> EmployeeRolesPermissions { get; set; } = new List<EmployeeRolesPermission>();
    }

    public class EmployeeRolesPermission
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        [ForeignKey("EmployeeRoles")]
        public string RoleId { get; set; }
        public EmployeeRoles EmployeeRoles { get; set; }
        [ForeignKey("EmployeePermission")]
        public string PermissionId { get; set; }
        public EmployeePermission EmployeePermission { get; set; }
    }
}
