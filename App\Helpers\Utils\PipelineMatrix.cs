﻿using QRCoder;

namespace Jobid.App.Helpers.Utils
{
    public class PipelineMatrix
    {
        public bool[,] Matrix { get; set; }

        public PipelineMatrix(QRCodeData qrCodeData)
        {
            Matrix = new bool[qrCodeData.ModuleMatrix.Count, qrCodeData.ModuleMatrix.Count];
            for (int i = 0; i < qrCodeData.ModuleMatrix.Count; i++)
            {
                for (int j = 0; j < qrCodeData.ModuleMatrix.Count; j++)
                {
                    Matrix[i, j] = qrCodeData.ModuleMatrix[i][j];
                }
            }
        }
    }
}
