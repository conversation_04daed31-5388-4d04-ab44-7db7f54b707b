using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using DinkToPdf;

namespace Jobid.App.Helpers.Utils
{
    public static class DinkToPdfLibraryLoader
    {
        public static void Init()
        {
            // First, ensure the DLL is properly copied to output directories
            EnsureDllInOutputDirectory();
            
            // Try to load the library directly
            if (!LoadLibrary())
            {
                // If direct loading fails, rely on system's library loading mechanism
                // This is especially important for Linux environments in Docker containers
                Console.WriteLine("Direct library loading failed, relying on system library loading");
                
                // Just create an instance to trigger library loading through DinkToPdf
                try
                {
                    var tools = new PdfTools();
                    Console.WriteLine("PdfTools initialized successfully");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error initializing PdfTools: {ex.Message}");
                    Console.WriteLine(ex.StackTrace);
                    throw; // Important to throw to signal the issue
                }
            }
        }

        private static bool LoadLibrary()
        {
            try
            {
                var isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
                var libraryName = isWindows ? "libwkhtmltox.dll" : "libwkhtmltox.so";
                
                // Search paths in order of likelihood
                var searchPaths = new List<string>
                {
                    // Current directory paths
                    Path.Combine(Directory.GetCurrentDirectory(), "DinkToPdf", libraryName),
                    Path.Combine(Directory.GetCurrentDirectory(), libraryName),
                    
                    // Assembly location paths
                    Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "DinkToPdf", libraryName),
                    Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), libraryName),
                    
                    // bin/Debug or bin/Release paths for development scenarios
                    Path.Combine(Directory.GetCurrentDirectory(), "bin", "Debug", "net5.0", "DinkToPdf", libraryName),
                    Path.Combine(Directory.GetCurrentDirectory(), "bin", "Release", "net5.0", "DinkToPdf", libraryName)
                };
                
                // Add Windows-specific paths if on Windows
                if (isWindows)
                {
                    // Add Program Files paths for system installs
                    searchPaths.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "wkhtmltopdf", "bin", libraryName));
                    searchPaths.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "wkhtmltopdf", "bin", libraryName));
                    
                    // Check if we could be in a Debug build configuration under bin folder
                    var binPath = Path.Combine(Directory.GetCurrentDirectory(), "bin");
                    if (Directory.Exists(binPath))
                    {
                        searchPaths.Add(Path.Combine(binPath, "Debug", "net5.0", "DinkToPdf", libraryName));
                        searchPaths.Add(Path.Combine(binPath, "Release", "net5.0", "DinkToPdf", libraryName));
                    }
                }

                // Log search paths for debugging
                Console.WriteLine("Searching for library in the following paths:");
                foreach (var path in searchPaths)
                {
                    Console.WriteLine($"  - {path}");
                }

                foreach (var path in searchPaths)
                {
                    if (File.Exists(path))
                    {
                        Console.WriteLine($"Found library at: {path}");
                        Console.WriteLine($"Loading library from: {path}");
                        if (isWindows)
                        {
                            return LoadWin32Library(path) != IntPtr.Zero;
                        }
                        else
                        {
                            return LoadLinuxLibrary(path) != IntPtr.Zero;
                        }
                    }
                }

                Console.WriteLine("Library not found in any of the expected locations.");
                
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // For Windows, suggest installing via provided script
                    Console.WriteLine("Please run the install-wkhtmltopdf.bat script to install the necessary components.");
                    Console.WriteLine("If already installed, ensure the DLL is properly copied to one of the search paths.");
                }
                else
                {
                    // For Linux/macOS
                    Console.WriteLine("Please run the install-wkhtmltopdf.sh script to install the necessary components.");
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in LoadLibrary: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                return false;
            }
        }

        [DllImport("kernel32.dll", CharSet = CharSet.Unicode)]
        private static extern IntPtr LoadWin32Library(string lpFileName);

        [DllImport("libdl.so.2")]
        private static extern IntPtr LoadLinuxLibrary(string filename);
        
        /// <summary>
        /// Copies the DLL from source to destination directory if it doesn't exist
        /// </summary>
        private static void EnsureDllInOutputDirectory()
        {
            try
            {
                var isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
                var libraryName = isWindows ? "libwkhtmltox.dll" : "libwkhtmltox.so";
                
                // Check if DLL exists in project's DinkToPdf folder
                var sourcePath = Path.Combine(Directory.GetCurrentDirectory(), "DinkToPdf", libraryName);
                
                if (File.Exists(sourcePath))
                {
                    // Copy to output directories if they don't exist
                    var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                    var destPath = Path.Combine(assemblyDir, "DinkToPdf", libraryName);
                    
                    Directory.CreateDirectory(Path.Combine(assemblyDir, "DinkToPdf"));
                    
                    if (!File.Exists(destPath))
                    {
                        Console.WriteLine($"Copying DLL from {sourcePath} to {destPath}");
                        File.Copy(sourcePath, destPath, true);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error copying DLL: {ex.Message}");
            }
        }
    }
}
