﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class TagService : ITagService
    {
        private JobProDbContext Db;
        public JobProDbContext Dbo;

        public TagService(JobProDbContext _db, JobProDbContext publicSchemaContext)
        {
            Db = _db;
            Dbo = publicSchemaContext;
        }

        public async Task<ProjectTag> GetTag(Guid id)
        {
            //<PERSON><PERSON> wrote this
            return await Db.ProjectTag.Where(x => x.Id == id).FirstOrDefaultAsync();
        }

        public async Task<ProjectTag> UpdateTag(UpdateTagVm updateTagVm, ProjectTag projectTag)
        {
            projectTag.TagName = updateTagVm.TagName.ToTitleCase();
            Db.ProjectTag.Update(projectTag);
            var result = await Db.SaveChangesAsync();
            return result > 0 ? projectTag : null;
        }

        public async Task<ProjectTag> AddTag(UpdateTagVm updateTagVm)
        {
            var tag = new ProjectTag()
            {
                TagName = updateTagVm.TagName.ToTitleCase(),
                TenantId = updateTagVm.TenantId,
                UserId = updateTagVm.UserId
            };

            Db.ProjectTag.Add(tag);
            var result = await Db.SaveChangesAsync();
            return result > 0 ? tag : null;
        }

        public async Task<ProjectTag> DeleteTag(ProjectTag projectTag)
        {

            Db.ProjectTag.Remove(projectTag);
            var result = await Db.SaveChangesAsync();
            return result > 0 ? projectTag : null;
        }

        public async Task<List<TagDto>> GetAllTags()
        {
            return await Db.ProjectTag.Select(x => new TagDto()
            {
                TagName = x.TagName,
                Id = x.Id
            }).ToListAsync();
        }

        public async Task<List<ProjectTag>> GetTagsByUserId(string UserId)
        {
            return Db.ProjectTag.Where(x => x.UserId == UserId).Distinct().ToList();
        }

        public async Task<List<ProjectTag>> GetTagsByProjectId(string Id)
        {
            var projectTags = new List<ProjectTag>();
            var tags = Db.TagId.Where(x => x.PrjectTagId == Id).ToList();

            foreach (var tag in tags)
            {
                var itemTag = await Db.ProjectTag.Where(x => x.Id.ToString() == tag.PrjectTagId).FirstOrDefaultAsync();
                if (itemTag != null)
                {
                    projectTags.Add(itemTag);
                }
            }

            return projectTags;
        }

        public async Task<List<ProjectTag>> GetTagsByTodoId(string Id)
        {
            var projectTags = new List<ProjectTag>();
            var tags = Db.TagId.Where(x => x.TodoId == Id).ToList();

            foreach (var tag in tags)
            {
                var itemTag = await Db.ProjectTag.Where(x => x.Id.ToString() == tag.PrjectTagId).FirstOrDefaultAsync();
                if (itemTag != null)
                {
                    projectTags.Add(itemTag);
                }
            }
            return projectTags;
        }

        public async Task<bool> AddSprintTag(List<string> tags, string sprintId)
        {
            var projectTag = new List<TagId>();

            foreach (var tag in tags)
            {
                projectTag.Add(new TagId()
                {
                    SprintId = sprintId,
                    PrjectTagId = tag
                });
            }

            Db.TagId.UpdateRange(projectTag);
            var result = await Db.SaveChangesAsync();
            return result > 0 ? true : false;
        }


        public async Task<bool> AddTodoTag(List<ProjectTag> tags, string todoId)
        {
            var projectTag = new List<TagId>();

            foreach (var tag in tags)
            {
                projectTag.Add(new TagId()
                {
                    TodoId = todoId,
                    PrjectTagId = tag.Id.ToString()
                });
            }

            Db.TagId.UpdateRange(projectTag);
            var result = await Db.SaveChangesAsync();
            return result > 0 ? true : false;
        }

    }
}
