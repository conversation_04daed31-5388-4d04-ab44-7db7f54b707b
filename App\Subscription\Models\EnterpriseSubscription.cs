﻿using CsvHelper.Configuration.Attributes;
using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Models
{
    public class EnterpriseSubscription
    {
        public Guid Id { get; set; } = new Guid();
        public Guid TenantId { get; set; }
        public Guid SubscriptionId { get; set; }
        public Applications Application { get; set; }
        public PaymentProviders PaymentProvider { get; set; }

        // Enterprise plan Id
        public string PlanId { get; set; }

        // Number of projects that the comapny can create where 0 means unlimited
        public int ProjectLimit { get; set; }

        // Number in days of how long the company's internal comm will be kept where 0 means unlimited
        public int InternalCommunicationHistoryLimit { get; set; }

        public bool TimeSheetManagement { get; set; }

        // Number in days of how long the company's activity can be retained where 0 means unlimited
        public int ActivityLogHistoryLimit { get; set; }

        // An interger for the number of meeetings the campany can book/create where 0 means unlimited
        public int CalenderLimit { get; set; }

        // Storage limit in number of the company in GB where 0 means unlimited
        public int StorageLimit { get; set; }

        // Max Number of users that the comapny can add where 0 means unlimited
        public int UsersLimit { get; set; }
        public int DataRetentionPeriodInMonth { get; set; }
        // The ammount the company paid for all the features above
        [Required]
        public double AmountPaid { get; set; }

        // AI Assitant
        public bool AiAssistant { get; set; }

        public DateTime? ActivatedOn { get; set; }

        public DateTime ExpiresOn { get; set; }

        public Currency Currency { get; set; }

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedOn { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }

        public DateTime? DeletedOn { get; set; }

        public string? DeletedBy { get; set; }

        [Default(false)]
        public bool IsDeleted { get; set; }


        // Navigational Property
        public Tenant.Model.Tenant Tenant { get; set; }

        public Subscription Subscription { get; set; }
    }
}
