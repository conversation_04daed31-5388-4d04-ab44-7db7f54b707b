﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Xunit.Sdk;

namespace Jobid.App.ActivityLog.ViewModel
{
    public class ActivityRequestedPermisssionsForTeamMembersDto
    {
        public List<string> TeamIds { get; set; } = new List<string>();
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        [JsonIgnore]
        public string RequesterId { get; set; }
    }
}
