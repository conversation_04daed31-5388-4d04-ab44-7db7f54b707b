using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.Model
{
    public class ActivityView
    {
        [Key]
        public Guid Id { get; set; }
        public EventCategory EventCategory { get; set; } = EventCategory.Application;
        [Required]
        public string ActivitySummary { get; set; }
        public string Description { get; set; }
        public Applications? Application { get; set; }
        public string EventId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string SubDomain { get; set; }
    }
}
