using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Rest.Api.V2010.Account.Conference;
using Twilio.Jwt.AccessToken;
using Twilio.Types;
using Twilio.TwiML;
using Twilio.TwiML.Voice;
using Jobid.App.Helpers.Context;
using Jobid.App.AdminConsole.Models.Calls;
using Jobid.App.AdminConsole.Dto.TwilioConference;
using Jobid.App.AdminConsole.Contract;
using Task = System.Threading.Tasks.Task;

namespace Jobid.App.AdminConsole.Services
{
    /// <summary>
    /// Service for managing Twilio-only audio conferences
    /// Handles PSTN and web participants in Twilio conference rooms
    /// </summary>
    public class TwilioConferenceService : ITwilioConferenceService
    {
        private readonly ILogger<TwilioConferenceService> _logger;
        private readonly IConfiguration _configuration;
        private readonly JobProDbContext _context;
        
        // Track active conference sessions
        private readonly ConcurrentDictionary<string, ConferenceSession> _activeSessions;
        
        public TwilioConferenceService(
            ILogger<TwilioConferenceService> logger,
            IConfiguration configuration,
            JobProDbContext context)
        {
            _logger = logger;
            _configuration = configuration;
            _context = context;
            _activeSessions = new ConcurrentDictionary<string, ConferenceSession>();
            
            // Initialize Twilio
            var accountSid = _configuration["Twilio:AccountSid"];
            var authToken = _configuration["Twilio:AuthToken"];
            TwilioClient.Init(accountSid, authToken);
        }

        /// <summary>
        /// Create a new Twilio conference room
        /// </summary>
        public Task<string> CreateConference(string conferenceName, int maxParticipants = 10)
        {
            try
            {
                _logger.LogInformation("Creating Twilio conference {ConferenceName}", conferenceName);

                // In Twilio, conferences are created implicitly when participants join
                // We just track the conference session for management
                var conferenceId = Guid.NewGuid().ToString();
                var session = new ConferenceSession
                {
                    ConferenceSid = conferenceId,
                    ConferenceName = conferenceName,
                    StartTime = DateTime.UtcNow,
                    IsActive = true,
                    MaxParticipants = maxParticipants,
                    Participants = new List<ConferenceParticipant>()
                };

                _activeSessions.TryAdd(conferenceName, session);

                _logger.LogInformation("Prepared Twilio conference {ConferenceName} with ID {ConferenceId}", 
                    conferenceName, conferenceId);

                return Task.FromResult(conferenceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Twilio conference {ConferenceName}", conferenceName);
                throw;
            }
        }

        /// <summary>
        /// Add PSTN participant to conference
        /// </summary>
        public async Task<string> AddPstnParticipant(string conferenceName, string phoneNumber, string fromNumber)
        {
            try
            {
                _logger.LogInformation("Adding PSTN participant {PhoneNumber} to conference {ConferenceName}", 
                    phoneNumber, conferenceName);

                if (!_activeSessions.TryGetValue(conferenceName, out var session))
                {
                    throw new InvalidOperationException($"Conference {conferenceName} not found");
                }

                // Create TwiML response using Voice SDK
                var response = new VoiceResponse();
                response.Say("Connecting you to the conference. Please hold.", voice: Say.VoiceEnum.Alice);
                
                var dial = new Dial();
                dial.Conference(conferenceName, 
                    startConferenceOnEnter: true, 
                    endConferenceOnExit: false);
                response.Append(dial);

                // Create call to add PSTN participant
                var call = await CallResource.CreateAsync(
                    to: new Twilio.Types.PhoneNumber(phoneNumber),
                    from: new Twilio.Types.PhoneNumber(fromNumber),
                    twiml: new Twilio.Types.Twiml(response.ToString()),
                    statusCallback: new Uri($"{_configuration["BaseUrl"]}/api/twilio-conference/call-status"),
                    statusCallbackEvent: new List<string> { "answered", "completed" }
                );

                // Track participant
                var participant = new ConferenceParticipant
                {
                    CallSid = call.Sid,
                    PhoneNumber = phoneNumber,
                    ParticipantType = ParticipantType.PSTN,
                    JoinedAt = DateTime.UtcNow,
                    Status = ParticipantStatus.Connecting
                };

                session.Participants.Add(participant);

                _logger.LogInformation("PSTN participant {PhoneNumber} added to conference {ConferenceName} with call SID {CallSid}", 
                    phoneNumber, conferenceName, call.Sid);

                return call.Sid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding PSTN participant {PhoneNumber} to conference {ConferenceName}", 
                    phoneNumber, conferenceName);
                throw;
            }
        }

        /// <summary>
        /// Generate access token for web participants
        /// </summary>
        public string GenerateWebAccessToken(string identity, string conferenceName, int ttlMinutes = 60)
        {
            try
            {
                // For now, return a placeholder token
                // In production, you would implement proper Twilio Voice SDK token generation
                var token = $"twilio-voice-token-{identity}-{conferenceName}-{DateTime.UtcNow.Ticks}";
                
                _logger.LogDebug("Generated web access token for identity {Identity} in conference {ConferenceName}", 
                    identity, conferenceName);

                return token;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating web access token for identity {Identity}", identity);
                throw;
            }
        }

        /// <summary>
        /// Add web participant to conference
        /// </summary>
        public Task<string> AddWebParticipant(string conferenceName, string identity, string displayName)
        {
            try
            {
                _logger.LogInformation("Adding web participant {Identity} to conference {ConferenceName}", 
                    identity, conferenceName);

                if (!_activeSessions.TryGetValue(conferenceName, out var session))
                {
                    throw new InvalidOperationException($"Conference {conferenceName} not found");
                }

                // Track participant
                var participant = new ConferenceParticipant
                {
                    Identity = identity,
                    DisplayName = displayName,
                    ParticipantType = ParticipantType.Web,
                    JoinedAt = DateTime.UtcNow,
                    Status = ParticipantStatus.Connected
                };

                session.Participants.Add(participant);

                _logger.LogInformation("Web participant {Identity} added to conference {ConferenceName}", 
                    identity, conferenceName);

                return Task.FromResult(identity);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding web participant {Identity} to conference {ConferenceName}", 
                    identity, conferenceName);
                throw;
            }
        }

        /// <summary>
        /// Remove participant from conference
        /// </summary>
        public async Task RemoveParticipant(string conferenceName, string participantId)
        {
            try
            {
                _logger.LogInformation("Removing participant {ParticipantId} from conference {ConferenceName}", 
                    participantId, conferenceName);

                if (!_activeSessions.TryGetValue(conferenceName, out var session))
                {
                    _logger.LogWarning("Conference {ConferenceName} not found", conferenceName);
                    return;
                }

                var participant = session.Participants.FirstOrDefault(p => 
                    p.CallSid == participantId || p.Identity == participantId);

                if (participant != null)
                {
                    participant.Status = ParticipantStatus.Disconnected;
                    participant.LeftAt = DateTime.UtcNow;

                    // If it's a PSTN participant, hang up the call
                    if (participant.ParticipantType == ParticipantType.PSTN && !string.IsNullOrEmpty(participant.CallSid))
                    {
                        await CallResource.UpdateAsync(
                            pathSid: participant.CallSid,
                            status: CallResource.UpdateStatusEnum.Completed
                        );
                    }

                    _logger.LogInformation("Participant {ParticipantId} removed from conference {ConferenceName}", 
                        participantId, conferenceName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing participant {ParticipantId} from conference {ConferenceName}", 
                    participantId, conferenceName);
            }
        }

        /// <summary>
        /// End conference and disconnect all participants
        /// </summary>
        public async Task EndConference(string conferenceName)
        {
            try
            {
                _logger.LogInformation("Ending conference {ConferenceName}", conferenceName);

                if (!_activeSessions.TryGetValue(conferenceName, out var session))
                {
                    _logger.LogWarning("Conference {ConferenceName} not found", conferenceName);
                    return;
                }

                // Disconnect all PSTN participants
                foreach (var participant in session.Participants.Where(p => p.ParticipantType == ParticipantType.PSTN))
                {
                    if (!string.IsNullOrEmpty(participant.CallSid))
                    {
                        await CallResource.UpdateAsync(
                            pathSid: participant.CallSid,
                            status: CallResource.UpdateStatusEnum.Completed
                        );
                    }
                }

                // Update conference status
                if (!string.IsNullOrEmpty(session.ConferenceSid))
                {
                    await ConferenceResource.UpdateAsync(
                        pathSid: session.ConferenceSid,
                        status: ConferenceResource.UpdateStatusEnum.Completed
                    );
                }

                session.IsActive = false;
                session.EndTime = DateTime.UtcNow;

                _activeSessions.TryRemove(conferenceName, out _);

                _logger.LogInformation("Conference {ConferenceName} ended successfully", conferenceName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ending conference {ConferenceName}", conferenceName);
            }
        }

        /// <summary>
        /// Get active conference sessions for monitoring
        /// </summary>
        public async Task<object> GetActiveConferences()
        {
            return await Task.FromResult(_activeSessions.Values.Select(s => new
            {
                s.ConferenceSid,
                s.ConferenceName,
                s.StartTime,
                s.EndTime,
                Duration = s.EndTime?.Subtract(s.StartTime) ?? DateTime.UtcNow.Subtract(s.StartTime),
                s.IsActive,
                s.MaxParticipants,
                ParticipantCount = s.Participants.Count(p => p.Status == ParticipantStatus.Connected),
                Participants = s.Participants.Select(p => new
                {
                    p.Identity,
                    p.DisplayName,
                    p.PhoneNumber,
                    p.ParticipantType,
                    p.Status,
                    p.JoinedAt,
                    p.LeftAt
                })
            }).ToList());
        }

        /// <summary>
        /// Get conference details
        /// </summary>
        public async Task<ConferenceSession> GetConference(string conferenceName)
        {
            return await Task.FromResult(_activeSessions.TryGetValue(conferenceName, out var session) ? session : null);
        }

        /// <summary>
        /// Handle Twilio conference status callbacks
        /// </summary>
        public async Task HandleConferenceStatusCallback(string conferenceSid, string statusCallbackEvent, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("Conference status callback: {Event} for conference {ConferenceSid}", 
                    statusCallbackEvent, conferenceSid);

                var session = _activeSessions.Values.FirstOrDefault(s => s.ConferenceSid == conferenceSid);
                if (session == null)
                {
                    _logger.LogWarning("Conference session not found for SID {ConferenceSid}", conferenceSid);
                    return;
                }

                switch (statusCallbackEvent.ToLower())
                {
                    case "conference-start":
                        session.ActualStartTime = DateTime.UtcNow;
                        _logger.LogInformation("Conference {ConferenceName} started", session.ConferenceName);
                        break;

                    case "conference-end":
                        session.IsActive = false;
                        session.EndTime = DateTime.UtcNow;
                        _logger.LogInformation("Conference {ConferenceName} ended", session.ConferenceName);
                        break;

                    case "participant-join":
                        var joinCallSid = parameters.GetValueOrDefault("CallSid");
                        var participant = session.Participants.FirstOrDefault(p => p.CallSid == joinCallSid);
                        if (participant != null)
                        {
                            participant.Status = ParticipantStatus.Connected;
                            participant.ActualJoinedAt = DateTime.UtcNow;
                        }
                        _logger.LogInformation("Participant joined conference {ConferenceName}", session.ConferenceName);
                        break;

                    case "participant-leave":
                        var leaveCallSid = parameters.GetValueOrDefault("CallSid");
                        var leavingParticipant = session.Participants.FirstOrDefault(p => p.CallSid == leaveCallSid);
                        if (leavingParticipant != null)
                        {
                            leavingParticipant.Status = ParticipantStatus.Disconnected;
                            leavingParticipant.LeftAt = DateTime.UtcNow;
                        }
                        _logger.LogInformation("Participant left conference {ConferenceName}", session.ConferenceName);
                        break;
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling conference status callback for {ConferenceSid}", conferenceSid);
            }
        }
    }

    /// <summary>
    /// Conference session tracking
    /// </summary>
    public class ConferenceSession
    {
        public string ConferenceSid { get; set; }
        public string ConferenceName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? ActualStartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsActive { get; set; }
        public int MaxParticipants { get; set; }
        public List<ConferenceParticipant> Participants { get; set; } = new List<ConferenceParticipant>();
    }

    /// <summary>
    /// Conference participant tracking
    /// </summary>
    public class ConferenceParticipant
    {
        public string CallSid { get; set; }
        public string Identity { get; set; }
        public string DisplayName { get; set; }
        public string PhoneNumber { get; set; }
        public ParticipantType ParticipantType { get; set; }
        public ParticipantStatus Status { get; set; }
        public DateTime JoinedAt { get; set; }
        public DateTime? ActualJoinedAt { get; set; }
        public DateTime? LeftAt { get; set; }
    }

    /// <summary>
    /// Participant types
    /// </summary>
    public enum ParticipantType
    {
        PSTN,
        Web
    }

    /// <summary>
    /// Participant status
    /// </summary>
    public enum ParticipantStatus
    {
        Connecting,
        Connected,
        Disconnected
    }
}
