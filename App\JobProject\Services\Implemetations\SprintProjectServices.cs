﻿using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Hangfire;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant.Contract;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using static Jobid.App.JobProject.Enums.Enums;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;
using User = Jobid.App.Helpers.Models.User;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class SprintProjectServices : ISprintProjectServices
    {
        #region Private Variables And Constructor
        private readonly JobProDbContext _db;
        private readonly IEmailService _emailService;
        private readonly JobProDbContext _dbo;
        private readonly IMapper _mapper;
        private readonly UserManager<User> _userManager;
        private readonly IWebHostEnvironment _env;
        private readonly ITenantService _tenantService;
        private readonly ICompanyUserInvite _companyUserInvite;
        private readonly IWebHostEnvironment _environment;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IAdminService _adminService;

        public SprintProjectServices(JobProDbContext _db, IEmailService emailService, JobProDbContext publicSchemaContext, IMapper mapper, UserManager<User> userManager, IWebHostEnvironment env, ITenantService tenantService, ICompanyUserInvite companyUserInvite, IWebHostEnvironment environment, IAWSS3Sevices aWSS3Sevices, IAdminService adminService)
        {
            this._db = _db;
            _emailService = emailService;
            _dbo = publicSchemaContext;
            _mapper = mapper;
            _userManager = userManager;
            _env = env;
            _tenantService = tenantService;
            _companyUserInvite = companyUserInvite;
            _environment = environment;
            _aWSS3Sevices = aWSS3Sevices;
            _adminService = adminService;
        }
        #endregion

        #region Add Sprint
        /// <summary>
        /// Add sprint to a project
        /// </summary>
        /// <param name="addSprintDto"></param>
        /// <param name="project"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<AddSprintDto> AddSprint_ToProject(AddSprintDto addSprintDto, ProjectMgmt_Project project)
        {
            // Check if a project with the sprint name already exists under the same project
            var sprintExists = await _db.SprintProjects.AnyAsync(x => x.Name.ToLower() == addSprintDto.Name.ToLower() && x.ProjectMgmt_ProjectId == project.ProjectId);
            if (sprintExists)
                throw new RecordAlreadyExistException($"Sprint with the supplied sprint name already exists under {project.Name}");

            addSprintDto.Name = addSprintDto.Name.CapitalizeFirstLetter();

            // Get TenantId
            var tenantId = (await _tenantService.GetTenantBySubdomain(addSprintDto.SubDomian)).Id;

            // Get the invitee name
            var invitee = _db.UserProfiles.Where(x => x.UserId == addSprintDto.UserId.ToString())
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            // Generate a unique id for the sprint
            addSprintDto.SprintId = "SP" + Utility.GenerateRandomNumbers().ToString();

            var sprint = _mapper.Map<SprintProject>(addSprintDto);
            sprint.CreatedBy = addSprintDto.UserId;
            _db.SprintProjects.Add(sprint);

            addSprintDto.Id = sprint.Id.ToString();

            // Add tags to the sprint and Get added tags
            var tags = new List<ProjectTag>() { };
            var tagList = new List<TagId>() { };
            if (addSprintDto.TagIds.Any())
            {
                addSprintDto.TagIds.ForEach
                    (
                        async x =>
                        {
                            try
                            {
                                var todoTag = _db.ProjectTag.FirstOrDefault(p => p.Id.ToString() == x);
                                if (todoTag == null) { throw new RecordNotFoundException("Wrong tag id"); }
                                tags.Add(todoTag);
                            }
                            catch (Exception ex)
                            {
                                throw ex;
                            }
                        }
                    );

                var Tags = addSprintDto.TagIds.Select(x => new TagId()
                {
                    PrjectTagId = x,
                    SprintId = sprint.Id.ToString(),

                }).ToList();

                tagList.AddRange(Tags);
                await _db.TagId.AddRangeAsync(tagList);
                addSprintDto.Tags = tags;
            }


            // Add members to the sprint 
            var userEmailList = new List<string>();
            var userFirstNamesList = new List<string>();
            List<ProjectSprintMemberId> memberIds = new List<ProjectSprintMemberId>();

            if (!string.IsNullOrEmpty(addSprintDto.TeamId))
            {
                var teamMemberIds = await _db.TeamMembers.Where(x => x.Id.ToString() == addSprintDto.TeamId)
                    .Select(teamMember => teamMember.UserId).ToListAsync();
                addSprintDto.memberIds.AddRange(teamMemberIds);
                addSprintDto.memberIds = addSprintDto.memberIds.Distinct().ToList();
            }

            if (addSprintDto.memberIds.Any())
            {
                foreach (var member in addSprintDto.memberIds)
                {
                    var user = _db.UserProfiles.Where(x => x.UserId == member).FirstOrDefault();
                    userEmailList.Add(user.Email);
                    userFirstNamesList.Add(user.FirstName);

                    if (user != null)
                    {
                        memberIds.Add(new ProjectSprintMemberId()
                        {
                            SprintId = sprint.Id.ToString(),
                            MemberId = member
                        });
                    }
                }
            }

            await _db.ProjectSprintMemberIds.AddRangeAsync(memberIds);

            var templatePath = string.Empty;
            var template = string.Empty;
            var index = 0;
            templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/sprint-internal-invited-email.html");
            foreach (var email in userEmailList)
            {
                // Send email notification to added member
                var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, addSprintDto.SubDomian) + $"/suite/pkg/project/sprint/{sprint.Id}";
                template = File.ReadAllText(templatePath);
                template = template.Replace("{company}", addSprintDto.SubDomian).Replace("{project}", project.Name ?? addSprintDto.SubDomian).Replace("{name}", invitee).Replace("{url}", url).Replace("{sprint}", sprint.Name).Replace("{user}", userFirstNamesList[index]);
                index++;

                BackgroundJob.Enqueue(() => _emailService.SendEmail(template, email, "Sprint Addition"));
            }

            // Send mail to external memebers
            if (addSprintDto.ExternalMembersEmails.Any())
            {
                var externalMembers = new List<ProjectSprintMemberId>();
                foreach (var mail in addSprintDto.ExternalMembersEmails)
                {
                    var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                    {
                        Email = mail,
                        Application = Applications.Joble,
                    }, tenantId.ToString());

                    externalMembers.Add(new ProjectSprintMemberId
                    {
                        SprintId = sprint.Id.ToString(),
                        ExternalMemberEmail = mail,
                    });

                }

                // Add external members to sprint
                await _db.ProjectSprintMemberIds.AddRangeAsync(externalMembers);

                // Send mail to external members
                templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/sprint-external-invited-email.html");
                foreach (var mail in addSprintDto.ExternalMembersEmails)
                {
                    var encodedEmail = HttpUtility.UrlEncode(mail);
                    var inviteUrl = string.Format(Utility.Constants.INVITE_URL, addSprintDto.SubDomian, invitee, tenantId, Applications.Joble, encodedEmail) + $"&type={TypeForInviteUrl.Sprint.ToString()}&id={sprint.Id}";

                    template = File.ReadAllText(templatePath);
                    template = template.Replace("{company}", addSprintDto.SubDomian).Replace("{project}", project.Name ?? addSprintDto.SubDomian).Replace("{name}", invitee).Replace("{url}", inviteUrl).Replace("{sprint}", sprint.Name);

                    string subject = $"Invitation to JobPro - You've been Added to the {sprint.Name} Sprint";
                    var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, mail, subject));
                }
            }

            _db.SprintProjects.Add(sprint);
            var result = await _db.SaveChangesAsync();

            if (result > 0)
            {
                // Create todoStatus for the sprint
                var status = Enum.GetNames(typeof(ProjectManagementStatus)).ToList();
                var statusToAdd = status.Select(x => new TodoStatus()
                {
                    Status = x,
                    SprintId = sprint.Id,
                });

                await _db.TodoStatus.AddRangeAsync(statusToAdd);
                await _db.SaveChangesAsync();

                return addSprintDto;
            }

            throw new Exception("Failed to add sprint to project");
        }
        #endregion

        #region Add External Member to Sprint
        /// <summary>
        /// Add external member to sprint
        /// </summary>
        /// <param name="externalMember"></param>
        /// <returns></returns>
        public async Task<bool> AddExternalMemberSprintProject(ExternalMember externalMember)
        {
            // Get TenantId
            var tenant = await _tenantService.GetTenantBySubdomain(externalMember.SubDomain);

            // Get the invitee name
            var invitee = _db.UserProfiles.Where(x => x.UserId == externalMember.UserId)
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();
            if (invitee == null) { throw new RecordNotFoundException("user not found"); }

            // Get the sprint
            var sprint = await _db.SprintProjects.Where(x => x.Id.ToString() == externalMember.SprintId).FirstOrDefaultAsync();
            if (sprint == null) { throw new RecordNotFoundException("Sprint not found"); }
            var project = await _db.ProjectMgmt_Projects.Where(x => x.ProjectId == sprint.ProjectMgmt_ProjectId).FirstOrDefaultAsync();
            if (sprint == null) { throw new RecordNotFoundException("Sprint not found"); }

            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/sprint-external-invited-email.html");
            var template = File.ReadAllText(templatePath);

            //var template = await _emailService.GetSprintExternalEmailTemplate();

            if (externalMember.Emails.Any())
            {
                foreach (var email in externalMember.Emails)
                {
                    var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                    {
                        Email = email,
                        Application = Applications.Joble,
                    }, tenant.Id.ToString());

                    var encodedEmail = HttpUtility.UrlEncode(email);
                    var inviteUrl = string.Format(Utility.Constants.INVITE_URL, externalMember.SubDomain, invitee, tenant.Id, Applications.Joble, encodedEmail) + $"&type={TypeForInviteUrl.Sprint.ToString()}&id={sprint.Id}";

                    template = template.Replace("{company}", tenant.CompanyName).Replace("{project}", project.Name ?? tenant.CompanyName).Replace("{name}", invitee).Replace("{url}", inviteUrl).Replace("{sprint}", sprint.Name);

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, email, $"You've been Added to the {sprint.Name} Sprint"));
                }

                return true;
            }

            return false;
        }
        #endregion

        #region Get Sprint Members
        /// <summary>
        /// Get sprint members
        /// </summary>
        /// <returns></returns>
        public async Task<List<UserDto>> GetSprintMember(string sprintId)
        {
            var sprintmembers = _db.ProjectSprintMemberIds.Where(x => x.SprintId == sprintId).ToList();
            // Get todos that belong to the sprint
            var todoIds = await _db.ProjectMgmt_Todo.Where(x => x.SprintProjectId.Value.ToString() == sprintId)
                .Select(todo => todo.Id.ToString()).ToListAsync();
            var todoMembers = await _db.projectMgmt_TodoUsers.Where(x => todoIds.Contains(x.ProjectMgmt_TodoId.ToString())).ToListAsync();

            var users = new List<UserProfile>();
            foreach (var member in sprintmembers)
            {
                var user = await _db.UserProfiles.Where(x => x.UserId == member.MemberId || x.Email == member.ExternalMemberEmail).FirstOrDefaultAsync();

                if (user != null)
                    users.Add(user);
            }

            foreach (var member in todoMembers)
            {
                var user = await _db.UserProfiles.Where(x => x.UserId == member.UserId || x.Email == member.ExternalMemberEmail)
                    .FirstOrDefaultAsync();
                if (user != null)
                    users.Add(user);
            }

            var members = users.Select(x => new UserDto
            {
                Id = x.UserId,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Email = x.Email,
                ProfileUrl = x.ProfilePictureUrl
            }).ToList();

            foreach (var member in members)
                member.ProfileUrl = member.ProfileUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(member.ProfileUrl) : null;

            return members;
        }
        #endregion

        #region Add internal members to a sprint
        /// <summary>
        /// Add internal members to a sprint
        /// </summary>
        /// <param name="internalMember"></param>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        public async Task<bool> AddInternalMemberSprintProject(InternalMember internalMember, string sprintId)
        {
            var members = internalMember.MemberId.Select(x => new ProjectSprintMemberId()
            {
                SprintId = sprintId,
                MemberId = x

            });

            // Get the invitee name
            var invitee = _db.UserProfiles.Where(x => x.UserId == internalMember.UserId)
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();
            if (invitee == null) { throw new RecordNotFoundException("user not found"); }

            // Get comapny name using subdomain name
            var tenant = await _tenantService.GetTenantBySubdomain(internalMember.Subdomain);
            var sprint = _db.SprintProjects.Where(x => x.Id.ToString() == sprintId).FirstOrDefault();
            var project = await _db.ProjectMgmt_Projects.Where(x => x.ProjectId == sprint.ProjectMgmt_ProjectId).FirstOrDefaultAsync();
            if (sprint == null) { throw new RecordNotFoundException("Sprint not found"); }

            _db.ProjectSprintMemberIds.AddRange(members);
            int result = await _db.SaveChangesAsync();

            // Send email notification to the members
            if (result > 0)
            {
                // Get user details of the added members
                var users = await _dbo.UserProfiles.Where(x => internalMember.MemberId.Contains(x.UserId)).ToListAsync();
                var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/sprint-internal-invited-email.html");
                var template = File.ReadAllText(templatePath);
                //var template = await _emailService.GetSprintInternalEmailTemplate();

                foreach (var user in users)
                {
                    var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, internalMember.Subdomain) + $"/suite/pkg/project/sprint/{sprint.Id}";
                    template = template.Replace("{company}", tenant.CompanyName).Replace("{project}", project.Name ?? tenant.CompanyName).Replace("{name}", invitee).Replace("{url}", url).Replace("{sprint}", sprint.Name).Replace("{user}", user.FirstName);

                    var subject = "Sprint Addition";
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, user.Email, subject));
                }
            }

            if (result > 0) { return true; } else { return false; }

        }
        #endregion

        #region Remove Members from a sprint
        /// <summary>
        /// Remove members from a sprint
        /// </summary>
        /// <param name="memberIds"></param>
        /// <param name="sprintId"></param>
        /// <param name="loggedInUser"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> RemoveMemberFromSprint(List<string> memberIds, string sprintId, string loggedInUser)
        {
            var sprint = await _db.SprintProjects.Where(x => x.Id.ToString() == sprintId).FirstOrDefaultAsync();
            if (sprint == null)
                throw new RecordNotFoundException("Sprint not found");

            await CheckPermission(sprint.CreatedBy.ToString(), loggedInUser);

            var members = await _db.ProjectSprintMemberIds
                .Where(x => memberIds.Contains(x.MemberId) && x.SprintId == sprintId).ToListAsync();
            if (!members.Any()) { throw new RecordNotFoundException("No Member found"); }

            // Remove the member from the project
            var projectId = _db.SprintProjects.Where(x => x.Id.ToString() == sprintId)
                .Select(x => x.ProjectMgmt_ProjectId.ToString()).FirstOrDefault();
            var sprintMemberEmails = members.Where(m => m.ExternalMemberEmail != null)
                .Select(x => x.ExternalMemberEmail).ToList();
            foreach (var email in sprintMemberEmails)
            {
                var userDetails = await _db.UserProfiles.Where(x => x.Email == email).FirstOrDefaultAsync();
                if (userDetails != null)
                {
                    memberIds.Add(userDetails.UserId);
                }
            }

            RemoveMembersFromProject(projectId, memberIds);

            _db.ProjectSprintMemberIds.RemoveRange(members);
            var result = await _db.SaveChangesAsync();

            if (result > 0)
            {
                // Log Activity
                return true;
            }
            else { return false; }
        }
        #endregion

        #region Update Sprint
        /// <summary>
        /// Update Sprint
        /// </summary>
        /// <param name="sprintProjectVm"></param>
        /// <param name="sprintProject"></param>
        /// <returns></returns>
        public async Task<bool> UpdateSprintProject(SprintProjectVm sprintProjectVm, SprintProject sprintProject)
        {
            await CheckPermission(sprintProject.CreatedBy.ToString(), sprintProjectVm.LoggedInUserId);

            sprintProjectVm.Name = sprintProjectVm.Name.CapitalizeFirstLetter();
            sprintProjectVm.Summary = sprintProjectVm.Summary.CapitalizeFirstLetter();

            // Get TenantId
            var tenant = await _tenantService.GetTenantBySubdomain(sprintProjectVm.SubDomian);
            var tenantId = tenant.Id;

            // Get the invitee name
            var invitee = _db.UserProfiles.Where(x => x.UserId == sprintProjectVm.UserId.ToString())
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            var project = await _db.ProjectMgmt_Projects.Where(x => x.ProjectId == sprintProject.ProjectMgmt_ProjectId)
                .FirstOrDefaultAsync();

            sprintProject.UpdatedBy = sprintProjectVm.UserId;
            sprintProject.Name = sprintProjectVm.Name;
            sprintProject.Description = sprintProjectVm.Description;
            sprintProject.StartDate = sprintProjectVm.StartDate;
            sprintProject.EndDate = sprintProjectVm.EndDate;
            sprintProject.Duration = sprintProjectVm.Duration;
            sprintProject.Status = sprintProjectVm.Status;
            sprintProject.Summary = sprintProjectVm.Summary;
            sprintProject.StartTime = sprintProjectVm.StartTime;
            sprintProject.EndTime = sprintProjectVm.EndTime;

            // Delete all sprint tags
            var sprintTags = _db.TagId.Where(x => x.SprintId == sprintProject.Id.ToString()).ToList();
            if (sprintTags.Any())
                _db.TagId.RemoveRange(sprintTags);

            // Add new sprint tags
            var tags = sprintProjectVm.TagIds.Select(x => new TagId()
            {
                SprintId = sprintProject.Id.ToString(),
                PrjectTagId = x
            });

            await _db.TagId.AddRangeAsync(tags);

            // Delete all members of the sprint
            var members = _db.ProjectSprintMemberIds.Where(x => x.SprintId == sprintProject.Id.ToString()).ToList();
            if (members.Any())
                _db.ProjectSprintMemberIds.RemoveRange(members);

            // Add new members to the sprint
            var userList = new List<UserProfile>();
            List<ProjectSprintMemberId> memberIds = new List<ProjectSprintMemberId>();
            if (sprintProjectVm.memberIds.Any())
            {
                foreach (var member in sprintProjectVm.memberIds)
                {
                    var user = _db.UserProfiles.Where(x => x.UserId == member).FirstOrDefault();
                    userList.Add(user);

                    if (user != null)
                    {
                        memberIds.Add(new ProjectSprintMemberId()
                        {
                            SprintId = sprintProject.Id.ToString(),
                            MemberId = member
                        });
                    }
                }
            }

            await _db.ProjectSprintMemberIds.AddRangeAsync(memberIds);

            // Send email notification to added member
            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/sprint-internal-invited-email.html");
            var template = File.ReadAllText(templatePath);
            //var template = await _emailService.GetSprintInternalEmailTemplate();
            foreach (var user in userList)
            {
                var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, sprintProjectVm.SubDomian) + $"/suite/pkg/project/sprint/{sprintProject.Id}";
                template = template.Replace("{company}", tenant.CompanyName).Replace("{project}", project.Name ?? tenant.CompanyName).Replace("{name}", invitee).Replace("{url}", url).Replace("{sprint}", sprintProject.Name).Replace("{user}", user.FirstName);

                BackgroundJob.Enqueue(() => _emailService.SendEmail(template, user.Email, "Update - Sprint Addition"));
            }

            // Send mail to external memebers
            //template = await _emailService.GetSprintExternalEmailTemplate();
            templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/sprint-external-invited-email.html");
            template = File.ReadAllText(templatePath);
            if (sprintProjectVm.ExternalMembersEmails.Any())
            {
                foreach (var mail in sprintProjectVm.ExternalMembersEmails)
                {
                    var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                    {
                        Email = mail,
                        Application = Applications.Joble,
                    }, tenantId.ToString());

                    var encodedEmail = HttpUtility.UrlEncode(mail);
                    var inviteUrl = string.Format(Utility.Constants.INVITE_URL, sprintProjectVm.SubDomian, invitee, tenantId, Applications.Joble, encodedEmail) + $"&type={TypeForInviteUrl.Sprint.ToString()}&id={sprintProject.Id}";

                    template = template.Replace("{company}", tenant.CompanyName).Replace("{project}", project.Name ?? tenant.CompanyName).Replace("{name}", invitee).Replace("{url}", inviteUrl).Replace("{sprint}", sprintProject.Name);

                    string subject = "Update - Invitation to JobPro - New Sprint Alert";
                    var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, mail, subject));
                }
            }

            _db.SprintProjects.Update(sprintProject);
            var result = await _db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }

        }
        #endregion

        #region Move Todos to a different/new sprint
        public async Task<bool> MoveTodosToDifferentOrNewSprint(List<string> todoIds, string sprintId)
        {
            var todos = await _db.ProjectMgmt_Todo.Where(x => todoIds.Contains(x.Id.ToString())).ToListAsync();
            if (todos is not null)
            {
                foreach (var todo in todos)
                {
                    todo.SprintProjectId = Guid.Parse(sprintId);
                }

                _db.ProjectMgmt_Todo.UpdateRange(todos);
                var result = await _db.SaveChangesAsync();

                if (result > 0) { return true; } else { return false; }
            }
            else
            {
                return false;
            }
        }
        #endregion

        #region Update Sprint Status
        /// <summary>
        /// Update Sprint Status
        /// </summary>
        /// <param name="sprintId"></param>
        /// <param name="status"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateSprintStatus(string sprintId, SprintStatus status, string loggedInUserId)
        {
            var sprint = await _db.SprintProjects.Where(x => x.Id.ToString() == sprintId).FirstOrDefaultAsync();
            //await CheckPermission(sprint.CreatedBy.ToString(), loggedInUserId);
            sprint.Status = status;

            _db.SprintProjects.Update(sprint);
            var result = await _db.SaveChangesAsync();

            if (result > 0) { return true; } else { return false; }
        }
        #endregion

        #region Get all Sprint
        /// <summary>
        /// Get all Sprint
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public async Task<Page<SprintProject>> GetAllSprints(PaginationParameters parameters)
        {
            return await _db.SprintProjects.OrderByDescending(x => x.CreatedDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }
        #endregion

        #region Get Sprint by ProjectId
        /// <summary>
        /// Get Sprint by ProjectId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public async Task<List<SprintProject>> GetSprintProjectById(string projectId)
        {
            var sprints = await _db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId)
               .ToListAsync();

            foreach (var sprint in sprints)
            {
                var members = await GetSprintMember(sprint.Id.ToString());
                if (members.Any())
                    sprint.SprintMembers.AddRange(members);

                await CalculateCompletionPercentage(sprint);
            }

            return sprints;
        }
        #endregion

        #region Get Sprint by ProjectId Paginated
        /// <summary>
        /// Get Sprint by ProjectId Paginated
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public async Task<Page<SprintProject>> GetSprintProjectByIdPaginated(string projectId, PaginationParameters parameters)
        {
            var sprints = await _db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId)
               .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            foreach (var sprint in sprints.Items)
            {
                var members = await GetSprintMember(sprint.Id.ToString());
                if (members.Any())
                    sprint.SprintMembers.AddRange(members);

                // Calculate the percentage completion of the sprint
                await CalculateCompletionPercentage(sprint);
            }

            return sprints;
        }
        #endregion

        #region Get Sprint by Id
        /// <summary>
        /// Get Sprint by Id
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        public async Task<SprintProject> GetSprintById(string sprintId)
        {
            return _db.SprintProjects.Where(x => x.Id.ToString() == sprintId).FirstOrDefault();
        }
        #endregion

        #region Delete Sprint
        /// <summary>
        /// Delete Sprint
        /// </summary>
        /// <param name="sprintProject"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<bool> RemoveSprintProject(SprintProject sprintProject, string loggedInUserId)
        {
            try
            {
                //await CheckPermission(sprintProject.CreatedBy.ToString(), loggedInUserId);

                // Remove sprint members
                var sprintMembers = _db.ProjectSprintMemberIds.Where(x => x.SprintId == sprintProject.Id.ToString()).ToList();
                _db.ProjectSprintMemberIds.RemoveRange(sprintMembers);

                // Delete the sprint members from project members
                var projectId = sprintProject.ProjectMgmt_ProjectId.ToString();
                var sprintMemberIds = sprintMembers.Select(x => x.MemberId).ToList();
                var sprintMemberEmails = sprintMembers
                    .Where(m => m.ExternalMemberEmail != null).Select(x => x.ExternalMemberEmail).ToList();
                foreach (var email in sprintMemberEmails)
                {
                    var userDetails = await _db.UserProfiles.Where(x => x.Email == email).FirstOrDefaultAsync();
                    if (userDetails != null)
                    {
                        sprintMemberIds.Add(userDetails.UserId);
                    }
                }

                RemoveMembersFromProject(projectId, sprintMemberIds);

                // Remove sprint tags
                var sprintTags = _db.TagId.Where(x => x.SprintId == sprintProject.Id.ToString()).ToList();
                _db.TagId.RemoveRange(sprintTags);

                // Remove sprint todo
                var sprintTodos = _db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprintProject.Id).ToList();
                _db.ProjectMgmt_Todo.RemoveRange(sprintTodos);

                // Delete sprintid from TodoOrder table
                var sprintTodoOrder = _db.TodoOrder
                    .Where(x => x.SprintId == sprintProject.Id).ToList();
                _db.TodoOrder.RemoveRange(sprintTodoOrder);

                // Delete sprintid from TodoStatus table
                var sprintTodoStatus = _db.TodoStatus
                    .Where(x => x.SprintId == sprintProject.Id).ToList();
                _db.TodoStatus.RemoveRange(sprintTodoStatus);

                _db.SprintProjects.Remove(sprintProject);
                var result = await _db.SaveChangesAsync();
                if (result > 0) { return true; } else { return false; }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region Add Todo Status
        /// <summary>
        /// Add Todo Status
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<TodoStatus> AddTodoStatus(AddTodoStatusVm model)
        {
            var todoStatus = new TodoStatus()
            {
                Status = model.Status,
                SprintId = model.SprintId,
            };

            await _db.TodoStatus.AddAsync(todoStatus);

            var result = await _db.SaveChangesAsync();
            if (result > 0) { return todoStatus; } else { return null; }
        }
        #endregion

        #region Get Todo Status
        /// <summary>
        /// Get Todo Status
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        public async Task<List<TodoStatus>> GetTodoStatus(string sprintId)
        {
            return await _db.TodoStatus
                .Where(x => x.SprintId.ToString() == sprintId)
                .ToListAsync();
        }
        #endregion

        #region Update Todo Status
        /// <summary>
        /// Update Todo Status
        /// </summary>
        /// <param name="status"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<bool> UpdateTodoStatus(string Id, string status)
        {
            var todoStatus = _db.TodoStatus
                .Where(x => x.Id.ToString() == Id).FirstOrDefault();

            if (todoStatus.Status == "Todo")
                throw new Exception("Todo status cannot be updated");

            todoStatus.Status = status;
            _db.TodoStatus.Update(todoStatus);
            var result = await _db.SaveChangesAsync();

            if (result > 0) { return true; } else { return false; }
        }
        #endregion

        #region Delete Todo Status
        /// <summary>
        /// Delete Todo Status
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="loggedInuSerId"></param>
        /// <returns></returns>
        public async Task<bool> DeleteTodoStatus(string Id, string loggedInuSerId)
        {
            var todoStatus = _db.TodoStatus
                .Include(x => x.Sprint)
                .Where(x => x.Id.ToString() == Id).FirstOrDefault();

            if (todoStatus.Status == "Todo")
                throw new Exception("Todo status cannot be deleted");

            //await CheckPermission(todoStatus.Sprint.CreatedBy.ToString(), loggedInuSerId);

            _db.TodoStatus.Remove(todoStatus);
            var result = await _db.SaveChangesAsync();

            if (result > 0) { return true; } else { return false; }
        }
        #endregion

        #region Get Sprint Report Summary
        /// <summary>
        /// Get Sprint Report Summary
        /// </summary>
        /// <returns></returns>
        public async Task<List<SprintReportSummaryDto>> GetSprintReportSummary()
        {
            var sprints = await _db.SprintProjects
                .Select(x => new SprintReportSummaryDto
                {
                    Id = x.Id.ToString(),
                    SprintName = x.Name,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                    Status = x.Status.ToString(),
                    Duration = x.Duration
                }).ToListAsync();

            // Get project members
            foreach (var sprintDto in sprints)
            {
                var members = await GetSprintMember(sprintDto.Id);
                if (members.Any())
                    sprintDto.Members.AddRange(members);

                // Calculate the percentage completion of the sprint
                var sprint = await _db.SprintProjects.Where(x => x.Id.ToString() == sprintDto.Id).FirstOrDefaultAsync();
                if (sprint.Status == SprintStatus.Completed)
                    sprint.PercentageCompleted = "100%";
                else
                {
                    var todos = await _db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprint.Id).ToListAsync();
                    var completedTodosCount = todos.Count(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString());
                    var totalTodosCount = todos.Count();
                    var percentage = totalTodosCount > 0 ? completedTodosCount * 100 / totalTodosCount : 0;
                    sprintDto.PercentageCompleted = percentage.ToString() + "%";
                }
            }

            return sprints;
        }
        #endregion

        #region Get Sprint Summary Report By project Id
        /// <summary>
        /// Get Sprint Summary Report By project Id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public async Task<List<SprintReportSummaryDto>> GetSprintSummaryReportByProjectId(string projectId)
        {
            var sprintSummary = await _db.SprintProjects
                .Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId)
                .Select(x => new SprintReportSummaryDto
                {
                    Id = x.Id.ToString(),
                    SprintName = x.Name,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                    Status = x.Status.ToString(),
                    Duration = x.Duration
                }).ToListAsync();

            // Get sprint members
            foreach (var sprint in sprintSummary)
            {
                var members = await GetSprintMember(sprint.Id);
                if (members.Any())
                    sprint.Members.AddRange(members);

                sprint.Todos = await _db.ProjectMgmt_Todo.Where(todo => todo.SprintProjectId != null && todo.SprintProjectId.ToString() == sprint.Id).ToListAsync();
            }

            return sprintSummary;
        }
        #endregion

        #region Get Sprint Report Details
        /// <summary>
        /// Get Sprint Report Details
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<SprintReportDetailsDto> GetSprintReportDetails(string sprintId)
        {
            // Check if sprint exists
            var sprint = await _db.SprintProjects
                .Where(x => x.Id.ToString() == sprintId)
                .FirstOrDefaultAsync();
            if (sprint is null)
                throw new RecordNotFoundException("Sprint not found");

            // Get sprint report summary
            var sprintReportDetails = new SprintReportDetailsDto
            {
                Id = sprint.Id.ToString(),
                SprintName = sprint.Name,
                StartDate = sprint.StartDate,
                EndDate = sprint.EndDate,
                Status = sprint.Status.ToString(),
                Duration = sprint.Duration,
                Summary = sprint.Summary,
                Description = sprint.Description,
                TeamMembers = await GetSprintMember(sprintId)
            };

            // Calculate total time spent on project
            var totalTime = new TimeSpan();

            var todos = await _db.ProjectMgmt_Todo.Where(x => x.SprintProjectId.ToString() == sprintId).ToListAsync();
            foreach (var todo in todos)
            {
                if (todo.ActualTimeSpent == "0.00:00:00")
                    todo.ActualTimeSpent = null;

                TimeSpan time;
                if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTime += time;
            }

            sprintReportDetails.totalTimeTracked = totalTime.ToString();

            // Calculate for project analytics
            var months = new List<string> { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
            var monthsInt = new int[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12 };

            var sprintAnalytics = new SprintAnalyticsDto();
            foreach (var month in monthsInt)
            {
                var totalTodos = await _db.ProjectMgmt_Todo.Where(x => x.StartDateAndTime.Month == month).ToListAsync();
                var currentMonth = months[month - 1];
                var totalTodoCount = totalTodos.Count();
                var completedTodoCount = totalTodos.Count(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString());
                var overDueTodoCount = totalTodos.Count(x => x.TodoStatus == ProjectManagementTodoStatus.Overdue.ToString());

                // Calcualte % of completed projects and overdue projects
                if (totalTodoCount == 0)
                {
                    sprintAnalytics.CompletedTodos.Add(currentMonth, "0%");
                    sprintAnalytics.OverDueTodos.Add(currentMonth, "0%");
                    continue;
                }


                if (completedTodoCount == 0)
                    sprintAnalytics.CompletedTodos.Add(currentMonth, "0%");
                else
                {
                    double percentage = completedTodoCount / (double)totalTodoCount * 100;
                    sprintAnalytics.CompletedTodos.Add(currentMonth, percentage.ToString() + "%");
                }

                if (overDueTodoCount == 0)
                    sprintAnalytics.OverDueTodos.Add(currentMonth, "0%");
                else
                {
                    double percentage = overDueTodoCount / (double)totalTodoCount * 100;
                    sprintAnalytics.OverDueTodos.Add(currentMonth, percentage.ToString() + "%");
                }
            }

            sprintReportDetails.SprintAnalytics = sprintAnalytics;

            return sprintReportDetails;
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// This method removes members from a project
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="sprintMemberIds"></param>
        private void RemoveMembersFromProject(string projectId, List<string> sprintMemberIds)
        {
            var projectMembers = _db.projectMgmt_ProjectUsers.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && sprintMemberIds.Contains(x.UserId)).ToList();
            if (projectMembers.Any())
                _db.projectMgmt_ProjectUsers.RemoveRange(projectMembers);
        }

        /// <summary>
        /// This method checks if the logged in user has permission to perform the action
        /// </summary>
        /// <param name="createdBy"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        private async Task CheckPermission(string createdBy, string loggedInUserId)
        {
            // Check if the logged in user is a super admin
            var userRole = await _adminService.GetUserRole(loggedInUserId);
            if (createdBy != loggedInUserId && userRole != DataSeeder.SuperAdmin)
                throw new UnauthorizedAccessException("You are not authorized to perform this action");
        }

        private async Task CalculateCompletionPercentage(SprintProject sprint)
        {
            // Calculate the percentage completion of the sprint
            if (sprint.Status == SprintStatus.Completed)
                sprint.PercentageCompleted = "100%";
            else
            {
                var todos = await _db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprint.Id).ToListAsync();
                var completedTodosCount = todos.Count(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString());
                var totalTodosCount = todos.Count();
                var percentage = totalTodosCount > 0 ? completedTodosCount * 100 / totalTodosCount : 0;
                sprint.PercentageCompleted = percentage.ToString() + "%";
            }
        }
        #endregion
    }
}
