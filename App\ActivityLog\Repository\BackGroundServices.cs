using Hangfire;
using Jobid.App.ActivityLog.contract;
using Jobid.App.ActivityLog.Model;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.ActivityLog.Repository
{
    public class ActivityViewBackgroundService : IActivityViewBackgroundService
    {
        private readonly JobProDbContext _publicContext;

        public ActivityViewBackgroundService()
        {
            _publicContext = new JobProDbContext(new DbContextSchema());
            var tableExist = _publicContext.TableExists("CompanySubscriptions");
            if (tableExist)
            {
                GlobalVariables.Subdomains = _publicContext.CompanySubscriptions
                    .Include(x => x.Tenant)
                    .Where(x => x.Application == Applications.Joble && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active)
                    .Select(x => x.Tenant.Subdomain)
                    .ToList() ?? new List<string>();
            }
        }

        #region Update Tenant activity view
        /// <summary>
        /// Updates activities view
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("admin")]
        public async Task UpdateActivityView(List<string> subdomains)
        {
            if (subdomains == null) return;
            DateTime today = DateTime.Today;
            DateTime yesterday = today.AddDays(-1);

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var activities = await context.Activities
                    .Where(a => a.CreatedAt >= yesterday && a.CreatedAt < today)
                    .Select(av => new ActivityView
                    {
                        EventCategory = av.EventCategory,
                        ActivitySummary = av.ActivitySummary,
                        Description = av.Description,
                        Application = av.Application,
                        EventId = av.EventId,
                        CreatedAt = av.CreatedAt,
                        SubDomain = subdomain
                    })
                    .ToListAsync();
                await _publicContext.ActivityViews.AddRangeAsync(activities);
            }
            await _publicContext.SaveChangesAsync();
        }
        #endregion

        #region Delete old Tenant activity view
        /// <summary>
        /// Delete old tenant activities view
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("admin")]
        public void DeleteDataOlderThanSixMonths()
        {
            DateTime sixMonthsAgo = DateTime.Today.AddMonths(-6);

            var dataToDelete = _publicContext.ActivityViews
                .Where(a => a.CreatedAt < sixMonthsAgo);

            _publicContext.ActivityViews.RemoveRange(dataToDelete);
            _publicContext.SaveChanges();
        }
        #endregion
    }
}