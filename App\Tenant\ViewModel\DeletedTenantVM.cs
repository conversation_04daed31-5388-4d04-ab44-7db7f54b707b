using Jobid.App.Helpers.Enums;
using System;

namespace Jobid.App.Tenant.ViewModel
{
    public class DeletedTenantVM
    {
        public Guid Id { get; set; }
        public Guid OriginalTenantId { get; set; }
        public string CompanyName { get; set; }
        public string CompanyAddress { get; set; }
        public string WorkSpace { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string VerifiedEmailDomain { get; set; }
        public DateTime OriginalDateCreated { get; set; }
        public string LogoUrl { get; set; }
        public int CompanySize { get; set; }
        public string Status { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string Region { get; set; }
        public string CompanyType { get; set; }
        public string RegNumber { get; set; }
        public string AdminId { get; set; }
        public string Industry { get; set; }
        public DateTime DeletedAt { get; set; }
        public string DeletedBy { get; set; }
        public string DeletionReason { get; set; }
        public int TotalUsers { get; set; }
        public int ActiveSubscriptions { get; set; }
        public string LastMigrationDate { get; set; }
        public string DeletionNotes { get; set; }
    }
}