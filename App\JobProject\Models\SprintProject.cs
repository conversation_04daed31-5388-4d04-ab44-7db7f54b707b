﻿using Jobid.App.Helpers.Enums;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Jobid.App.Helpers.Utils.Extensions;

namespace Jobid.App.JobProjectManagement.Models
{
    public class SprintProject
    {
        public Guid Id { get; set; }
        public string SprintId { get; set; }
        public string Name { get; set; }
        public string Summary { get; set; }
        public string Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        [Required]
        public DateTime StartTime { get; set; }

        [Required]
        public DateTime EndTime { get; set; }
        public string Duration { get; set; }
        public SprintStatus Status { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; } = GetAdjustedDateTimeBasedOnTZNow();
        public Guid ProjectMgmt_ProjectId { get; set; }

        [NotMapped]
        public List<UserDto> SprintMembers { get; set; } = new List<UserDto>();

        [NotMapped]
        public string PercentageCompleted { get; set; }

    }
}
