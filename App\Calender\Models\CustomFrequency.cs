﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class CustomFrequency
    {
        [Key]
        public Guid Id { get; set; }
        public string RepeatEvery { get; set; }
        public int RepeatCount { get; set;}
        public string RepeatOn { get; set;}
        public EndStatus EndStatus { get; set;}
        public DateTime? EndsOn { get; set;}
        public int? EndsAfter { get; set;}
        public Guid CalenderMeetingId { get; set;}
        public CalenderMeeting CalenderMeeting { get; set; }
    }

    public enum EndStatus
    {
        Never,
        On,
        After
    }

    public enum RepeatEvery
    {
        Day,
        Week,
        Month,
        Year
    }
}
