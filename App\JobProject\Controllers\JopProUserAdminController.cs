﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    public class JopProUserAdminController : BaseController
    {
        private readonly IUnitofwork _unitofWork;
        private readonly ITenantSchema _tenantSchema;
        private readonly ILogger _logger = Log.ForContext<JopProUserAdminController>();

        public JopProUserAdminController(IUnitofwork unitofwork, ITenantSchema tenantSchema)
        {
            _unitofWork = unitofwork;
            _tenantSchema = tenantSchema;
        }

        #region Get User Profile
        /// <summary>
        /// Get User Profile Details
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        [Route("get-user-profile/{userId}")]
        public async Task<IActionResult> GetUserProfile(string userId)
        {
            try
            {
                var user = await _unitofWork.UserAdminServices.GetUserProfile(userId);
                if (user == null)
                {
                    return BadRequest(new ApiResponse<UserProfile>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "User not found",
                        Data = null
                    });
                }
                return Ok(new ApiResponse<UserProfile>
                {
                    ResponseCode = "200",
                    ResponseMessage = "User details retrived successfully",
                    Data = user
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<UserProfile>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Update User Profile
        /// <summary>
        /// Update user profile
        /// </summary>
        /// <param name="updateUserProfileDto"></param>
        /// <returns>UserProfile</returns>
        [HttpPut("update-user-profile")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateUserProfile([FromForm] UpdateUserProfileDto updateUserProfileDto)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"];
            var user = await _unitofWork.UserAdminServices.UpdateUserProfile(updateUserProfileDto, subdomain);
            if (user == null)
            {
                return BadRequest(new ApiResponse<UserProfile>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Update failed",
                    Data = null
                });
            }

            return Ok(new ApiResponse<UserProfilesForAdminDto>
            {
                ResponseCode = "200",
                ResponseMessage = "User details updated successfully",
                Data = user
            });
        }
        #endregion

        #region Get JobPro Permissions
        /// <summary>
        /// Get Permissions
        /// </summary>
        /// <returns></returns>
        [HttpGet("get-all-permissions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetPermissions()
        {
            try
            {
                return Ok(new ApiResponse<IEnumerable<JobProjectPermission>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Permissions retrived successfully",
                    Data = _unitofWork.UserAdminServices.GetJobProPermissions()

                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<IEnumerable<JobProjectPermission>>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Get JobProject Permissions for all Roles
        /// <summary>
        /// Get JobProject Permissions for all Roles
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("get-permissions-for-all-roles")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllJobProjectPermissionsForAllRoles()
        {
            try
            {
                return Ok(new ApiResponse<IEnumerable<JobProjectRoles>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Roles retrived successfully",
                    Data = await _unitofWork.UserAdminServices.GetPermissionsForAllRoles()
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<IEnumerable<JobProjectRoles>>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Add Permission to role
        /// <summary>
        /// Add permission to role
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="permissionId"></param>
        /// <returns></returns>
        [HttpPost("add-permission-to-role")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AddPermissionToRole(string roleId, string permissionId)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var response = await _unitofWork.UserAdminServices.AddPermissionToRole(roleId, permissionId, subdomain);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Permission added to role successfully",
                        ResponseCode = "200",
                        Data = response
                    });

                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to add permission",
                    Data = response
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Remove Permission from role
        /// <summary>
        /// Add permission to role
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="permissionId"></param>
        /// <returns></returns>
        [HttpPost("remove-permission-from-role")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RemovePermissionFromRole(string roleId, string permissionId)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var response = await _unitofWork.UserAdminServices.RemovePermissionFromRole(roleId, permissionId, subdomain);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Permission removed from role successfully",
                        ResponseCode = "200",
                        Data = response
                    });

                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to remove permission",
                    Data = response
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Add permission to user
        /// <summary>
        /// Add permission to user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="permissionId"></param>
        /// <returns></returns>
        [HttpPost("add-permission-to-user")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AddPermissionToUser(string userId, string permissionId)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var response = await _unitofWork.UserAdminServices.AddPermissionToUser(userId, permissionId, subdomain);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Permission added to user successfully",
                        ResponseCode = "200",
                        Data = response
                    });

                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to add permission",
                    Data = response
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Remove permission from user
        /// <summary>
        /// Remove permission from user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="permissionId"></param>
        /// <returns></returns>
        [HttpDelete("remove-permission-from-user")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> RemovePermissionFromUser(string userId, string permissionId)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var response = await _unitofWork.UserAdminServices.RemovePermissionFromUser(userId, permissionId, subdomain);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Permission removed from user successfully",
                        ResponseCode = "200",
                        Data = response
                    });

                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to remove permission",
                    Data = response
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Get all permissions for a user
        /// <summary>
        /// Get all permissions for a user
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("get-all-permissions-for-user")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllPermissionsForUser(string userId)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var response = await _unitofWork.UserAdminServices.GetUserPermissions(userId, subdomain);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Delete, Suspend and Unsuspend User
        /// <summary>
        /// Delete Suspend and Unsuspend User
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="IsDeleted"></param>
        /// <param name="IsSuspended"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("delete-suspend-unsuspend-user")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteSuspendUnsuspendUser(string userId, bool? IsDeleted, bool? IsSuspended)
        {
            try
            {
                var response = await _unitofWork.UserAdminServices.DeleteSuspendOrUnSuspendUser(userId, IsDeleted.Value, IsSuspended.Value);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "User updated successfully",
                        ResponseCode = "200",
                        Data = response
                    });

                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to update user",
                    Data = response
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Update user role
        /// <summary>
        /// Update user role
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("update-user-role")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateUserRole(string userId, string roleId)
        {
            try
            {
                var response = await _unitofWork.UserAdminServices.UpdateUserRole(userId, roleId);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "User role updated successfully",
                        ResponseCode = "200",
                        Data = response
                    });

                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to update user role",
                    Data = response
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion

        #region Update JobProject Settings
        /// <summary>
        /// Update JobProject Settings
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("update-jobproject-settings")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateJobProjectSettings(JobProjectSettingsDto model)
        {
            try
            {
                // Get user from token
                var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
                model.UserId = userId;
                var response = await _unitofWork.UserAdminServices.UpdateJobProjectSettings(model);
                if (response)
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "User role updated successfully",
                        ResponseCode = "200",
                        Data = response
                    });
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to update user role",
                    Data = response
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion
    }
}
