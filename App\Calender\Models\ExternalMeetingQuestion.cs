﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class ExternalMeetingQuestion
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public bool AskFullName { get; set; }
        public bool FullNameRequired { get; set; }
        public bool AskEmail { get; set; }
        public bool EmailRequired { get; set; }
        public bool AskLocation { get; set; }
        public bool LocationRequired { get; set; }
        public bool AddGuests { get; set; }
        public bool AdditionalInfo { get; set; }
        public bool AdditionalInfoRequired { get; set; }
        public string CustomQuestion { get; set; }
        public bool CustomQuestionRequired { get; set; }
    }
}
