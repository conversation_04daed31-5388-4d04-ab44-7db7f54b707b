﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Models;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface IUserAdminServices
    {
        Task<UserProfilesForAdminDto> UpdateUserProfile(UpdateUserProfileDto updateUserProfileDto, string subdomain);
        Task<List<JobProjectRoles>> GetPermissionsForAllRoles();
        IEnumerable<JobProjectPermission> GetJobProPermissions();
        Task<bool> AddPermissionToRole(string roleId, string permissionId, string subdomain);
        Task<bool> RemovePermissionFromRole(string roleId, string permissionId, string subdomain);
        Task<bool> AddPermissionToUser(string userId, string permissionId, string subdomain);
        Task<bool> RemovePermissionFromUser(string userId, string permissionId, string subdomain);
        Task<GenericResponse> GetUserPermissions(string userId, string subdomain);
        Task<bool> DeleteSuspendOrUnSuspendUser(string userId, bool IsDeleted = false, bool IsSuspended = false);
        Task<bool> UpdateUserRole(string userId, string roleId);
        Task<bool> UpdateJobProjectSettings(JobProjectSettingsDto model);
        //Task<UserProfiles> GetUserProfile(string userId, string subdomain);
        Task<UserProfile> GetUserProfile(string userId);

    }
}
