using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Filters;
using Jobid.App.JobProjectManagement.Controllers;

namespace Jobid.App.AdminConsole.Controllers
{
    /// <summary>
    /// Controller for managing campaigns
    /// </summary>
    [CustomAuthorize]
    [EndpointTrackerFilter(Applications.Joble, ApplicationSection.Other)]
    [Produces("Application/json")]
    [ApiController]
    [Route("api/[controller]")]
    public class CampaignController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private string CurrentUserIdString => CurrentUserId?.ToString() ?? string.Empty;

        public CampaignController(IUnitofwork unitofwork)
        {
            Services_Repo = unitofwork;
        }

        /// <summary>
        /// Create a new campaign
        /// </summary>
        /// <param name="campaignDto">Campaign creation data</param>
        /// <returns>Created campaign</returns>
        [HttpPost]
        [Route("CreateCampaign")]
        public async Task<IActionResult> CreateCampaign([FromBody] CreateCampaignDto campaignDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<CampaignDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            campaignDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.CampaignService.CreateCampaignAsync(campaignDto);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Update an existing campaign
        /// </summary>
        /// <param name="campaignDto">Campaign update data</param>
        /// <returns>Updated campaign</returns>
        [HttpPut]
        [Route("UpdateCampaign")]
        public async Task<IActionResult> UpdateCampaign([FromBody] UpdateCampaignDto campaignDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<CampaignDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            campaignDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.CampaignService.UpdateCampaignAsync(campaignDto);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Delete a campaign
        /// </summary>
        /// <param name="campaignId">Campaign ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete]
        [Route("DeleteCampaign/{campaignId}")]
        public async Task<IActionResult> DeleteCampaign(Guid campaignId)
        {
            var result = await Services_Repo.CampaignService.DeleteCampaignAsync(campaignId, CurrentUserIdString);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Get campaign by ID
        /// </summary>
        /// <param name="campaignId">Campaign ID</param>
        /// <returns>Campaign details</returns>
        [HttpGet]
        [Route("GetCampaignById/{campaignId}")]
        public async Task<IActionResult> GetCampaignById(Guid campaignId)
        {
            var result = await Services_Repo.CampaignService.GetCampaignByIdAsync(campaignId);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }            return BadRequest(result);
        }
        
        /// <summary>
        /// Get campaigns for the current user with pagination
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="status">Optional status filter</param>
        /// <returns>Paginated campaign list</returns>
        [HttpGet]
        [Route("GetUserCampaigns")]
        public async Task<IActionResult> GetUserCampaigns(int pageNumber = 1, int pageSize = 10, CampaignStatus? status = null)
        {
            var result = await Services_Repo.CampaignService.GetUserCampaignsAsync(CurrentUserIdString, pageNumber, pageSize, status);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Get all campaigns with pagination
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated campaign list</returns>
        [HttpGet]
        [Route("GetAllCampaigns")]
        public async Task<IActionResult> GetAllCampaigns(int pageNumber = 1, int pageSize = 10)
        {
            var result = await Services_Repo.CampaignService.GetAllCampaignsAsync(pageNumber, pageSize);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Update campaign status
        /// </summary>
        /// <param name="statusDto">Campaign status update data</param>
        /// <returns>Updated campaign</returns>
        [HttpPatch]
        [Route("UpdateCampaignStatus")]
        public async Task<IActionResult> UpdateCampaignStatus([FromBody] UpdateCampaignStatusDto statusDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<CampaignDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            statusDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.CampaignService.UpdateCampaignStatusAsync(statusDto);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Get campaign statistics for the current user
        /// </summary>
        /// <returns>Campaign statistics</returns>
        [HttpGet]
        [Route("GetCampaignStats")]
        public async Task<IActionResult> GetCampaignStats()
        {
            var result = await Services_Repo.CampaignService.GetCampaignStatsAsync(CurrentUserIdString);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            return BadRequest(result);
        }
    }
}
