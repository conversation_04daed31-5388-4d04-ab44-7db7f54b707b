﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using static Jobid.App.Subscription.Enums.Enums;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;
using System.Text.Json.Serialization;

namespace Jobid.App.Subscription.ViewModels
{
    public class EnterprizeSubscriptionPaymentDto
    {
        [Required]
        [ValidCompanyEmailDomian]
        public string CompanyEmail { get; set; }

        [Required]
        public string CompanyName { get; set; }
        public double Amount { get; set; }
        public SubscriptionInterval Frequency { get; set; }
        public int NoOfUser { get; set; }
        public Applications Application { get; set; }
        public PaymentProviders? PaymentProvider { get; set; }
        public Currency Currency { get; set; }
        public string PaymentId { get; set; }
        public string PaymentLink { get; set; }
        public PaymentStatus PaymentStatus { get; set; }
        public string Address { get; set; }

        [Required]
        public string PhoneNumber { get; set; }
        public bool PaymentUsed { get; set; }
        public DateTime? PaymentDate { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string? PersonalEmail { get; set; }
        public bool IsExistingUser { get; set; } = false;
        public bool EnterprizeFreePlan { get; set; }

        [JsonIgnore]
        public string LoggedInUser { get; set; }

        public EnterprizeSubscriptionPaymentDto()
        {
            PaymentStatus = PaymentStatus.Pending;
            PaymentUsed = false;
            CreatedOn = DateTime.UtcNow;
        }
    }
}
