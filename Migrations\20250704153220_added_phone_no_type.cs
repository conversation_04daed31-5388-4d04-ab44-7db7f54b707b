﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added_phone_no_type : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added_phone_no_type(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "NumberType",
                schema: _Schema,
                table: "PhoneNumbers",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "PhoneNumberMaintenanceCharges",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    TotalPhoneNumbers = table.Column<int>(type: "integer", nullable: false),
                    ChargeAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    PerNumberFee = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    ChargeDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    PaidDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    RetryDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    TransactionReference = table.Column<string>(type: "text", nullable: true),
                    WalletTransactionId = table.Column<string>(type: "text", nullable: true),
                    FailureReason = table.Column<string>(type: "text", nullable: true),
                    NotificationSent = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhoneNumberMaintenanceCharges", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhoneNumberMaintenanceCharges_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PhoneNumberMaintenanceCharges_TenantId",
                schema: _Schema,
                table: "PhoneNumberMaintenanceCharges",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PhoneNumberMaintenanceCharges",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "NumberType",
                schema: _Schema,
                table: "PhoneNumbers");
        }
    }
}
