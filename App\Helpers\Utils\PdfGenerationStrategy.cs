using DinkToPdf;
using DinkToPdf.Contracts;
using Jobid.App.Helpers.Controllers;
using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Serilog;
using WatchDog;

namespace Jobid.App.Helpers.Utils
{
    /// <summary>
    /// Strategy pattern implementation for PDF generation with multiple fallback options
    /// </summary>
    public static class PdfGenerationStrategy
    {
        /// <summary>
        /// Attempts to convert HTML to PDF using multiple strategies
        /// </summary>
        /// <param name="html">HTML content to convert</param>
        /// <param name="fileName">Output file name</param>
        /// <returns>MemoryStream containing the PDF</returns>
        public static async Task<MemoryStream> ConvertHtmlToPdfWithFallback(string html, string fileName)
        {
            // Strategy 1: Try DinkToPdf first
            try
            {
                return await UseLibWkHtmlToPdf(html, fileName);
            }
            catch (Exception ex)
            {
                WatchLogger.LogError($"DinkToPdf failed: {ex.Message}");
                
                // Strategy 2: Try using system wkhtmltopdf command-line if available
                try
                {
                    WatchLogger.Log("Attempting PDF generation with system wkhtmltopdf command-line");
                    return await UseSystemWkHtmlToPdf(html, fileName);
                }
                catch (Exception ex2)
                {
                    WatchLogger.LogError($"System wkhtmltopdf failed: {ex2.Message}");
                    
                    // Throw the original exception to maintain the original error context
                    throw new Exception($"All PDF generation strategies failed. Original error: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Uses DinkToPdf library for conversion
        /// </summary>
        private static Task<MemoryStream> UseLibWkHtmlToPdf(string html, string fileName)
        {
            var converter = new SynchronizedConverter(new PdfTools());
            
            // Create a MemoryStream to capture the PDF output
            var memoryStream = new MemoryStream();
            
            var doc = new HtmlToPdfDocument
            {
                GlobalSettings = new GlobalSettings
                {
                    ColorMode = ColorMode.Color,
                    Orientation = Orientation.Portrait,
                    PaperSize = PaperKind.A4,
                    Out = null,
                    DocumentTitle = Path.GetFileNameWithoutExtension(fileName)
                }
            };
            
            // Add the HTML content as an object
            doc.Objects.Add(new ObjectSettings
            {
                HtmlContent = html,
                WebSettings = { DefaultEncoding = "utf-8" },
                FooterSettings = { FontSize = 9, Right = "Page [page] of [toPage]", Line = true },
                HeaderSettings = { FontSize = 9, Line = true }
            });
            
            // Convert and write the output directly into the MemoryStream
            var pdfBytes = converter.Convert(doc);
            memoryStream.Write(pdfBytes, 0, pdfBytes.Length);
            
            // Reset the position of the MemoryStream for reading
            memoryStream.Position = 0;
            return Task.FromResult(memoryStream);
        }

        /// <summary>
        /// Uses system wkhtmltopdf command-line tool as a fallback strategy
        /// </summary>
        private static async Task<MemoryStream> UseSystemWkHtmlToPdf(string html, string fileName)
        {
            // Create temporary files for input and output
            string tempDir = Path.GetTempPath();
            string tempInputFile = Path.Combine(tempDir, $"input_{Guid.NewGuid()}.html");
            string tempOutputFile = Path.Combine(tempDir, $"output_{Guid.NewGuid()}.pdf");
            
            try
            {
                // Write HTML to temp file
                await File.WriteAllTextAsync(tempInputFile, html, Encoding.UTF8);
                
                // Determine the appropriate command and arguments based on platform
                string command;
                string arguments;
                
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    command = "wkhtmltopdf";
                    arguments = $"\"{tempInputFile}\" \"{tempOutputFile}\"";
                }
                else // Linux/macOS
                {
                    command = "/usr/local/bin/wkhtmltopdf";
                    if (!File.Exists(command))
                    {
                        command = "/usr/bin/wkhtmltopdf";
                    }
                    arguments = $"\"{tempInputFile}\" \"{tempOutputFile}\"";
                }
                
                // Start the process
                using (var process = new Process())
                {
                    process.StartInfo.FileName = command;
                    process.StartInfo.Arguments = arguments;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.CreateNoWindow = true;
                    
                    // Event handlers to capture output
                    var outputBuilder = new StringBuilder();
                    var errorBuilder = new StringBuilder();
                    
                    process.OutputDataReceived += (sender, e) => { if (e.Data != null) outputBuilder.AppendLine(e.Data); };
                    process.ErrorDataReceived += (sender, e) => { if (e.Data != null) errorBuilder.AppendLine(e.Data); };
                    
                    // Start the process
                    process.Start();
                    
                    process.BeginOutputReadLine();
                    process.BeginErrorReadLine();
                    
                    // Wait for the process to exit
                    await Task.Run(() => process.WaitForExit(30000)); // 30-second timeout
                    
                    // Check if process exited successfully
                    if (!process.HasExited || process.ExitCode != 0)
                    {
                        string error = errorBuilder.ToString();
                        throw new Exception($"wkhtmltopdf process failed with exit code {process.ExitCode}. Error: {error}");
                    }
                    
                    // Read the PDF file into memory and return as MemoryStream
                    if (File.Exists(tempOutputFile))
                    {
                        var memoryStream = new MemoryStream();
                        using (var fileStream = new FileStream(tempOutputFile, FileMode.Open, FileAccess.Read))
                        {
                            await fileStream.CopyToAsync(memoryStream);
                        }
                        memoryStream.Position = 0;
                        return memoryStream;
                    }
                    else
                    {
                        throw new FileNotFoundException("Output PDF file was not created");
                    }
                }
            }
            finally
            {
                // Clean up temp files
                try
                {
                    if (File.Exists(tempInputFile)) File.Delete(tempInputFile);
                    if (File.Exists(tempOutputFile)) File.Delete(tempOutputFile);
                }
                catch
                {
                    // Ignore cleanup errors
                }
            }
        }
    }
}
