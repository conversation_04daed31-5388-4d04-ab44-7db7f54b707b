using Jobid.App.Helpers;
using Jobid.App.Helpers.Models;
using Jobid.App.Wiki.ViewModel;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Wiki.Services.Contract
{
    public interface IWikiFileService
    {
        /// <summary>
        /// Upload multiple files to the S3 bucket and save their details in the database
        /// </summary>
        /// <param name="model">The model containing multiple files to upload</param>
        /// <param name="userId">ID of the user uploading the files</param>
        /// <returns>Response containing the uploaded files details grouped by batch ID</returns>
        Task<GenericResponse> UploadFileAsync(WikiUploadRequest model, Guid userId);
        
        /// <summary>
        /// Add text content to the Wiki without file upload
        /// </summary>
        /// <param name="model">The model containing the text content and optional department IDs</param>
        /// <param name="userId">ID of the user adding the text content</param>
        /// <returns>Response containing the added text content details</returns>
        Task<GenericResponse> AddTextContentAsync(WikiTextContentRequest model, Guid userId);
        
        /// <summary>
        /// Update file details
        /// </summary>
        /// <param name="fileUpdateDto">The file update data</param>
        /// <returns>Response containing the updated file details</returns>
        Task<GenericResponse> UpdateFileDetailsAsync(WikiFileUpdateDto fileUpdateDto);
        
        /// <summary>
        /// Update department access for a file
        /// </summary>
        /// <param name="accessUpdateDto">The access update data</param>
        /// <returns>Response containing the updated file details</returns>
        Task<GenericResponse> UpdateFileDepartmentAccessAsync(WikiFileDepartmentAccessUpdateDto accessUpdateDto);
        
        /// <summary>
        /// Delete a file (soft delete)
        /// </summary>
        /// <param name="fileId">ID of the file to delete</param>
        /// <returns>Response indicating success or failure</returns>
        Task<GenericResponse> DeleteFileAsync(Guid fileId);
        
        /// <summary>
        /// Get a file by its ID
        /// </summary>
        /// <param name="fileId">ID of the file to retrieve</param>
        /// <returns>Response containing the file details</returns>
        Task<GenericResponse> GetFileByIdAsync(Guid fileId);
        
        /// <summary>
        /// Get all files with optional filtering and pagination
        /// </summary>
        /// <param name="filter">Filter parameters</param>
        /// <returns>Response containing the list of files and pagination metadata</returns>
        Task<GenericResponse> GetFilesAsync(WikiFileFilterDto filter);
        
        /// <summary>
        /// Check if a user has access to a file through department membership
        /// </summary>
        /// <param name="fileId">ID of the file</param>
        /// <param name="userId">ID of the user</param>
        /// <returns>True if the user has access, false otherwise</returns>
        Task<bool> UserHasAccessToFileAsync(Guid fileId, Guid userId);
        
        /// <summary>
        /// Get a presigned URL for a file
        /// </summary>
        /// <param name="fileId">ID of the file</param>
        /// <param name="userId">ID of the user requesting the URL</param>
        /// <returns>Response containing the presigned URL</returns>
        Task<GenericResponse> GetFilePresignedUrlAsync(Guid fileId, Guid userId);

        /// <summary>
        /// Get all files accessible to a user based on their permissions and department access
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        Task<GenericResponse> GetUserAccessibleFilesAsync(Guid userId, WikiFileFilterDto filter);

        /// <summary>
        /// Get all company files for all companies/tenants in the system.
        /// </summary>
        /// <returns></returns>
        Task<GenericResponse> GetAllFilesInTheSystemAsync();

        /// <summary>
        /// Update the status of files to indicate they have been processed by AI
        /// </summary>
        /// <param name="fileIds"></param>
        /// <returns></returns>
        Task<GenericResponse> UpdateFilesAsProcessedAsync(List<Guid> fileIds);
    }
}
