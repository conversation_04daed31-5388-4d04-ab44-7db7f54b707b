﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProjectManagement.Controllers;
using Jobid.App.Notification.Models;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Jobid.App.Notification.Controllers
{
    public class NotificationController : BaseController
    {
        private readonly ILogger _logger = Log.ForContext<NotificationController>();
        private readonly IUnitofwork _unitOfWork;

        public NotificationController(IUnitofwork unitofwork)
        {
            _unitOfWork = unitofwork;
        }

        #region Get User Notifications
        /// <summary>
        /// Get User Notifications
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("get-notification")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetNotificationsByUserId()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            _logger.Information($"Attempt to retrieve notofications for userId {userId}");

            var response = await _unitOfWork.NotificationsService.GetUserNotifications(Guid.Parse(userId));
            if (response.Any()) 
            {
                var res = new ApiResponse<IEnumerable<UserNotification>> { ResponseCode = "200", ResponseMessage = "Notifcation retrieved successfully", Data = response };
                return Ok(await ConvertDateTimeToLocalDateTime(res));
            }
            else
                return NotFound(new ApiResponse<IEnumerable<UserNotification>> { ResponseCode = "400", ResponseMessage = "No notification found", Data = null });
        }
        #endregion

        #region Mark Notification as Read
        /// <summary>
        /// Mark Notification as Read
        /// </summary>
        /// <param name="notificationId"></param>
        /// <param name="markAllAsRead"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("mark-as-read/{notificationId}")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> MarkNotificationAsRead(Guid notificationId, bool markAllAsRead = false)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            _logger.Information($"Attempt to mark notofications as read for userId {userId}");

            var response = await _unitOfWork.NotificationsService.MarkNotificationAsRead(notificationId, Guid.Parse(userId), markAllAsRead);
            if (response) { return Ok(new ApiResponse<bool> { ResponseCode = "200", ResponseMessage = "Notifcation marked as read successfully", Data = true }); }
            else
                return BadRequest(new ApiResponse<bool> { ResponseCode = "400", ResponseMessage = "Operations failed", Data = false });
        }
        #endregion

        #region Mark All Notifications as Read
        /// <summary>
        /// Mark All Notifications as Read for the current user
        /// </summary>
        /// <returns></returns>
        [HttpPut]
        [Route("mark-all-as-read")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> MarkAllNotificationsAsRead()
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            _logger.Information($"Attempt to mark all notifications as read for userId {userId}");

            var response = await _unitOfWork.NotificationsService.MarkAllNotificationsAsRead(Guid.Parse(userId));

            if (response)
            {
                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "200",
                    ResponseMessage = "All notifications marked as read successfully",
                    Data = true
                });
            }
            else
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to mark all notifications as read",
                    Data = false
                });
            }
        }
        #endregion
    }
}