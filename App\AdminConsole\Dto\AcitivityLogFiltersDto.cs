﻿using Jobid.App.Helpers.Enums;
using System.Collections.Generic;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.AdminConsole.Dto
{
    public class AcitivityLogFiltersDto
    {
        public Applications Package { get; set; }
        public ActivityLogDuration? Duration { get; set; }
        public List<string> MemberIds { get; set; } = new List<string>();
        public List<string> EventCategory { get; set; }
        public List<string> TeamIds { get; set; } = new List<string>();
        public string SearchParam { get; set; } = string.Empty;
        public int PageNumber { get; set; }
        public int PageSize { get; set; }

        public AcitivityLogFiltersDto()
        {
            PageNumber = 1;
            PageSize = 100;
        }
    }
}
