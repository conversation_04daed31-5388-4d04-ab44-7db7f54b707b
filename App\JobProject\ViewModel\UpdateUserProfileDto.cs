﻿using Microsoft.AspNetCore.Http;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class UpdateUserProfileDto
    {
        [Required]
        public string UserId { get; set; }

        [Required]
        public string PhoneNumber { get; set; }

        public string Address { get; set; }

        public string ZipCode { get; set; }

        public string CountryCode { get; set; }

        public string Gender { get; set; }

        public string Country { get; set; }

        public string TimeZone { get; set; }

        public string State { get; set; }

        public string Bio { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public string Profession { get; set; }

        public string Designation { get; set; }

        public IFormFile? ProfilePicture { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        public string MiddleName { get; set; }

        public string Email { get; set; }

        public string TenantWorkSpace { get; set; }

        public string TenantCompanyAddress { get; set; }

        public string TenantBusinessRegistrationNumber { get; set; }

    }
}
