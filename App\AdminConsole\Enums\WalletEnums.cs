using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Jobid.App.AdminConsole.Enums
{
    [<PERSON>son<PERSON>onverter(typeof(StringEnumConverter))]
    public enum TransactionType
    {
        Credit,
        Debit
    }
    
    [J<PERSON><PERSON>onverter(typeof(StringEnumConverter))]
    public enum TransactionStatus
    {
        Pending,
        Completed,
        Failed,
        Refunded,
        Cancelled,
        Expired
    }
    
    [Json<PERSON>onverter(typeof(StringEnumConverter))]
    public enum PaymentProvider
    {
        Mollie,
        Stripe,
        Weavr,
        VFD,
        Providus,
        BridgeCard
    }

    [J<PERSON><PERSON>onverter(typeof(StringEnumConverter))]
    public enum PaymentMethod
    {
        Stripe,
        <PERSON>llie,
        None
    }

    [J<PERSON>Converter(typeof(StringEnumConverter))]
    public enum WalletType
    {
        CompanyExpense,
        CompanyGeneralAccount,
        Personal
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum TransactionRange
    {
        Inter,
        Intra
    }

    [Json<PERSON>onverter(typeof(StringEnumConverter))]
    public enum FundingType
    {
        Direct,         // Direct/Admin funding - immediately adds to wallet
        PaymentGateway  // Goes through payment processing
    }
}