﻿using Castle.Core.Configuration;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Options;
using System;
using System.Linq;

namespace Jobid.App.Helpers.Attributes
{
    //[AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class ApiKeyAttribute : ActionFilterAttribute
    {
        private readonly RtcSettings _rtcSettings;

        public ApiKeyAttribute(IOptions<RtcSettings> rtcSettings)
        {
            _rtcSettings = rtcSettings.Value;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var apiKey = ExtractHeader("ApiKey", context);
            if (string.IsNullOrWhiteSpace(apiKey))
            {
                context.Result = new JsonResult(new
                {
                    Status = "Failed",
                    Message = "Unauthorized, Api Key is required"
                })
                { StatusCode = 401 };
                return;
            }
            var savedApiKey = Environment.GetEnvironmentVariable("JOBPRO_RTC_NOTIFICATION_KEY") ?? _rtcSettings.ApiKey;
            if (savedApiKey != apiKey)
            {
                context.Result = new JsonResult(new
                {
                    Status = "Failed",
                    Message = "You are Unauthorized"
                })
                { StatusCode = 401 };
                return;
            }
            base.OnActionExecuting(context);
        }

        private string ExtractHeader(string key, ActionExecutingContext actionExecutingContext)
        {
            Microsoft.Extensions.Primitives.StringValues values;
            var apiKey = string.Empty;
            try
            {
                if(actionExecutingContext.HttpContext.Request.Headers.TryGetValue(key, out values))
                {
                    apiKey = values.FirstOrDefault();
                }
            }
            catch
            {
                apiKey = string.Empty;
            }

            return apiKey;
        }

        public class RtcSettings
        {
            public string ApiKey { get; set; }
        }
    }
}
