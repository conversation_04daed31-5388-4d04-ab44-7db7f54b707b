﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Subscription.Enums;
using Jobid.App.Tenant.ViewModel;

namespace Jobid.App.Tenant.Contract
{
    public interface ITenantService
    {
        /// <summary>
        ///  Gets a tenant by its id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public Task<TenantModelVM> GetTenantById(Guid id);

        /// <summary>
        /// Creates a new tenant
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Task<GenericResponse> RegisterTenant(RegisterTenantVM model);

        /// <summary>
        /// Creates a new tenant for an existing user
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<GenericResponse> CreateCompanyForExistingUser(CreateTenantForExistingUserVM model);

        /// <summary>
        ///  Runs migration for tenants
        /// </summary>
        /// <returns></returns>
        Task<TenantMigrationResultVM> MigrateTenants();

        /// <summary>
        /// Adds user to a company/tenant
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="tenantId"></param>
        /// <param name="companyEmail"></param>
        /// <returns></returns>
        public Task<UserCompanies> AddUserToCompany(string userId, Guid tenantId, string companyEmail);
        Task<GenericResponse> DowngradeUserToBasicAccess(DowngradeUserToBasicAccessDto model);
        Task<GenericResponse> RevokeUserAccess(RevokeUserAccessDto model);
        Task<GenericResponse> DeleteUserFromCompany(string userId, string subdomain);
        public Task<TenantModelVM> GetUserTenant(string userId);
        Task<GenericResponse> UpdateTenantDetails(UpdateTenantDto model);
        Task<ApiResponse<bool>> DeleteTenant(string subdomain, string subdomainFromHeader, string deletedBy = null, string deletionReason = null);
        Task<TenantModelVM> GetTenantBySubdomain(string subdomain);
        Task<ApiResponse<List<UserCompaniesVM>>> GetTenantUsers(string subdomain);
        Task<bool> InviteUserToTenant(List<string> emails, string userId, string subDomain);
        Task<bool> UserExisitsOnCompanySchema(string userId, string email);
        Task<bool> AddUserToAppPermission(string userId, string app, string loggedInUserId, string tenantId = null, AIAgents? agent = null);
        Task<AppPermissions> GetUserAppPermission(string userId, string application, string tenantId = null, AIAgents? agent = null);
        Task<List<AppPermissions>> GetUserAppPermissions(string userId, bool getFavorites, string tenantId = null, JobProDbContext context = null);
        Task<bool> CheckIfTenantExists(string subdomain);
        Task<GenericResponse> TopPerformingCountries();
        Task<GenericResponse> GetAllCountriesPercentage();
        Task<GenericResponse> GetAllTenants();
        Task<GenericResponse> GetDeletedTenants();
        Task<GenericResponse> GetUserPermittedApps(string userId, string subdomain);
        Task<bool> SendFreeTrialEndNotification(SendTrialEndedMailDto model);
        Task<GenericResponse> MigrateFromSQLServerToPostgres(MigrateFromSqlToPostgresDto model);
    }
}
