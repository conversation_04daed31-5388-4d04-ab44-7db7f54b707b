﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Jobid.App.Calender.Models.RTC.Requests
{
    public class UpdateParticipantRequest
    {
       
        [JsonProperty("coHostJobAuthIds")]
        public List<string> CoHostJobAuthIds { get; set; }

        [JsonProperty("guestJobAuthIds")]
        public List<string> GuestJobAuthIds { get; set; }

        [JsonProperty("externalGuestEmails")]
        public List<string> ExternalGuestEmails { get; set; }
    }
}
