﻿using Jobid.App.Helpers.Enums;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.ViewModel.IdentityVM
{
    public class AuthenticationModel
    {

        [Required]
        [DataType(DataType.EmailAddress)]
        public string Email { get; set; }

        [Required]
        public string Password { get; set; }

        public bool RememberMe { get; set; }
        public Applications? Application { get; set; }
        public bool FromMobile { get; set; }
        public string TempToken { get; set; }
        public string Code { get; set; }
    }

    public class GoogleAuthModel
    {
        [Required]
        public string GoogleAuthId { get; set; }
        
        [Required]
        [DataType(DataType.EmailAddress)]
        public string Email { get; set; }
        [Required]
        public string GoogleAuthToken { get; set; }
    }

    public class MicrosoftAuthModel
    {

        [Required]
        public string MicrosoftAuthId { get; set; }
        
        [Required]
        [DataType(DataType.EmailAddress)]
        public string Email { get; set; }

        // [Required]
        // public string Name { get; set; }
        // [Required]
        // public string imageUrl { get; set; }
        [Required]
        public string MicrosoftAccessToken { get; set; }
    }

    public class MicrosoftUserBasicProfile
{
    public string Sub { get; set; }
    public string Name { get; set; }
    public string GivenName { get; set; }
    public string FamilyName { get; set; }
    public string Picture { get; set; }
}
}
