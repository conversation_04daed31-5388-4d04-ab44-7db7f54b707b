﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class CalenderExternalIntegration : BaseModel
    {
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public string MeetingId { get; set; }

        [Required]
        public string IcalenderEventId { get; set; }
        public string UserId { get; set; }
        public string Email { get; set; }
    }
}
