﻿using Jobid.App.Helpers.Utils.Attributes;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProject.ViewModel
{
    public class JobProjectSettingsDto
    {
        [Required]
        [JsonIgnore]
        public string UserId { get; set; }

        [Required]
        [AllowedTimeFormat]
        public string CannotResceduleWithin { get; set; }

        [Required]
        [AllowedTimeFormat]
        public string OverDueWaitingPeriod { get; set; }
        public bool OutOfOffice { get; set; }
        public bool WorkingSchedule { get; set; } = true;
        public bool EmergencySchedule { get; set; }
    }
}
