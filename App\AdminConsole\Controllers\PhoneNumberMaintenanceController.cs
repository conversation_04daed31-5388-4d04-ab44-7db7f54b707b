using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Utils;

namespace Jobid.App.AdminConsole.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PhoneNumberMaintenanceController : ControllerBase
    {
        private readonly IPhoneNumberMaintenanceService _maintenanceService;

        public PhoneNumberMaintenanceController(IPhoneNumberMaintenanceService maintenanceService)
        {
            _maintenanceService = maintenanceService;
        }

        /// <summary>
        /// Get maintenance charge history for a company
        /// </summary>
        /// <param name="tenantId">Company tenant ID</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <returns>Maintenance charge history</returns>
        [HttpGet("history/{tenantId}")]
        public async Task<IActionResult> GetMaintenanceChargeHistory(
            Guid tenantId, 
            int pageSize = 10, 
            int pageNumber = 1)
        {
            try
            {
                var result = await _maintenanceService.GetMaintenanceChargeHistory(tenantId, pageSize, pageNumber);
                
                if (result.ResponseCode == "200")
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while retrieving maintenance charge history",
                    Data = ex.Message
                });
            }
        }

        /// <summary>
        /// Get pending maintenance charges for all companies or a specific company
        /// </summary>
        /// <param name="tenantId">Optional company tenant ID to filter by</param>
        /// <returns>Pending maintenance charges</returns>
        [HttpGet("pending")]
        public async Task<IActionResult> GetPendingMaintenanceCharges(Guid? tenantId = null)
        {
            try
            {
                var result = await _maintenanceService.GetPendingMaintenanceCharges(tenantId);
                
                if (result.ResponseCode == "200")
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while retrieving pending maintenance charges",
                    Data = ex.Message
                });
            }
        }

        /// <summary>
        /// Manually charge maintenance fee for a specific company (Admin only)
        /// </summary>
        /// <param name="tenantId">Company tenant ID</param>
        /// <param name="subdomain">Company subdomain</param>
        /// <returns>Charge result</returns>
        [HttpPost("charge/{tenantId}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> ManualChargeMaintenanceFee(Guid tenantId, string subdomain)
        {
            try
            {
                var result = await _maintenanceService.ChargeMaintenanceFee(tenantId, subdomain);
                
                if (result.ResponseCode == "200")
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while charging maintenance fee",
                    Data = ex.Message
                });
            }
        }

        /// <summary>
        /// Send maintenance charge notification manually (Admin only)
        /// </summary>
        /// <param name="tenantId">Company tenant ID</param>
        /// <param name="notificationType">Type of notification</param>
        /// <param name="chargeAmount">Charge amount</param>
        /// <param name="phoneNumberCount">Number of phone numbers</param>
        /// <returns>Notification result</returns>
        [HttpPost("notify/{tenantId}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> SendMaintenanceChargeNotification(
            Guid tenantId, 
            int notificationType, 
            decimal chargeAmount, 
            int phoneNumberCount)
        {
            try
            {
                var result = await _maintenanceService.SendMaintenanceChargeNotification(
                    tenantId, 
                    (Jobid.App.AdminConsole.Enums.MaintenanceNotificationType)notificationType, 
                    chargeAmount, 
                    phoneNumberCount);
                
                if (result)
                {
                    return Ok(new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Notification sent successfully"
                    });
                }
                
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to send notification"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while sending notification",
                    Data = ex.Message
                });
            }
        }
    }
}
