﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Models
{
    public class ClientCards
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        [Required]
        public string Currency { get; set; }
        public CardType CardType { get; set; }
        [Required]
        public string CardNumber { get; set; }
        [Required]
        public string Cvv { get; set; }
        [Required]
        public string ExpiryMmyy { get; set; }
        [Required]
        public string CardNumberFirstSix { get; set; }
        [Required]
        public string CardNumberLastFour { get; set; }
        public bool Country { get; set; }
        public bool ZipCode { get; set; }
        public string State { get; set; }
        public string City { get; set; }
        public string Address { get; set; }
        public bool IsActive { get; set; } = true;
        public string Email { get; set; }
        public string UserId { get; set; }
        public Guid? TenantId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public User User { get; set; }
        public Tenant.Model.Tenant Tenant { get; set; }
    }
}
