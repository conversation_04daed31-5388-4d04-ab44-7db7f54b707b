﻿using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.ActivityLog.Model
{
    public class ActivityRequestedPermisssions
    {
        public Guid Id { get; set; } = new Guid();
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;
        public string EventCategories { get; set; }

        [Required]
        public string RequesterId { get; set; }
        [Required]
        public string UserId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
