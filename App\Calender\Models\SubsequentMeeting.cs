﻿using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class SubsequentMeeting
    {
        [Key]
        public Guid Id { get; set; } = new Guid();

        [ForeignKey("CalenderMeeting")]
        public Guid CalenderMeetingId { get; set; }

        public string Name { get; set; }

        public DateTime? EndTime { get; set; }

        public int? MeetLength { get; set; }

        public string MeetingDuration { get; set; }

        public bool MakeSchdulePrivate { get; set; }

        public NotifyMeVia NotifyMe { get; set; }

        // This is in minutes. Eg 30 mins before meeting
        public int NotifyMembersIn { get; set; }

        public DateTime SubsequentMeetingDateTime { get; set; }

        public bool NotificationSent { get; set; }

        public bool HasMeetingHappeed { get; set; }

        public bool IsCanceled { get; set; }

        public int RescheduleCount { get; set; }

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        [NotMapped]
        public CalendarMeetingResponse MeetingResponse { get; set; }

        [NotMapped]
        public List<UserMDVm> Members { get; set; } = new List<UserMDVm>();

        [NotMapped]
        public List<string> ExternalMembers { get; set; } = new List<string>();

        [NotMapped]
        public List<string> AttachmentUrls { get; set; } = new List<string>();

        [NotMapped]
        public string MeetingRecordingLink { get; set; }

        public CalenderMeeting CalenderMeeting { get; set; }
    }
}
