﻿using System.Runtime.Serialization;
using System;

namespace Jobid.App.Helpers.Exceptions
{
    [Serializable]
    public class SubscriptionFailedException : Exception
    {
        public SubscriptionFailedException()
        {
        }

        public SubscriptionFailedException(string message) : base(message)
        {
        }

        public SubscriptionFailedException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected SubscriptionFailedException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
