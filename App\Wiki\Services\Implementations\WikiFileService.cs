using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Wiki.Enums;
using Jobid.App.Wiki.Models;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.Wiki.ViewModel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using MongoDB.Bson;
using Jobid.App.Helpers.Utils;
using PaginationMetadata = Jobid.App.Wiki.ViewModel.PaginationMetadata;
using Jobid.App.AdminConsole.Services;

namespace Jobid.App.Wiki.Services.Implementations
{
    public class WikiFileService : IWikiFileService
    {
        #region Private Fields And  Constructor
        private readonly JobProDbContext _dbContext;
        private readonly IAWSS3Sevices _s3Service;
        private readonly IWikiFileBackgroundService _backgroundService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly JobProDbContext _publicContext;
        private readonly string _conString;
        private ILogger _logger = Log.ForContext<WikiFileService>();

        // File size thresholds in bytes (loaded from appsettings.json)
        private readonly long _directUploadThreshold;
        private readonly long _backgroundUploadThreshold;

        public WikiFileService(JobProDbContext dbContext, IAWSS3Sevices s3Service, IWikiFileBackgroundService backgroundService, IWebHostEnvironment webHostEnvironment, IConfiguration configuration, JobProDbContext publicContext)
        {
            _dbContext = dbContext;
            _s3Service = s3Service;
            _backgroundService = backgroundService;
            _webHostEnvironment = webHostEnvironment;
            _publicContext = publicContext;
            _conString = GlobalVariables.ConnectionString;

            // Load file threshold settings from appsettings.json
            _directUploadThreshold = configuration.GetValue<long>("WikiFileUploadSettings:DirectUploadThreshold");
            _backgroundUploadThreshold = configuration.GetValue<long>("WikiFileUploadSettings:BackgroundUploadThreshold");
        }
        #endregion

        #region UploadFilesAsync
        public async Task<GenericResponse> UploadFileAsync(WikiUploadRequest model, Guid userId)
        {
            if (model.File == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "No file provided for upload",
                    Data = null
                };
            }

            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Batch number is required for file uploads",
                    Data = null
                };
            }

            // Ensure that the batch ID is unique
            //var existingBatch = await _dbContext.WikiFiles
            //    .AnyAsync(f => f.BatchId == model.BatchId && !f.IsDeleted);
            //if (existingBatch)
            //{
            //    return new GenericResponse
            //    {
            //        ResponseCode = "400",
            //        ResponseMessage = "A file with this batch ID already exists",
            //        Data = null
            //    };
            //}

            // Use the batch ID provided from the frontend
            string batchId = model.BatchId;

            List<WikiFileDto> uploadedFiles = new List<WikiFileDto>();
            List<string> failedFiles = new List<string>();

            var file = model.File;
            
            // Validate file size
            if (file.Length > _backgroundUploadThreshold)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"{file.FileName} exceeds the maximum file size limit",
                    Data = null
                };
            }

            // Generate a unique AWS key for the file
            string fileExtension = Path.GetExtension(file.FileName);
            string awsKey = $"wiki/{Guid.NewGuid()}{fileExtension}";

            // Create the file record in the database
            var wikiFile = new WikiContent
            {
                Id = Guid.NewGuid(),
                FileName = file.FileName,
                FileType = file.ContentType,
                FileSize = file.Length,
                AwsKey = awsKey,
                UploadStatus = WikiFileUploadStatus.Pending,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = userId,
                Description = model.Description,
                BatchId = batchId,
                WikiFileType = model.WikiFileType,
                IsDeleted = false
            };

            bool uploadSuccess = false;
            WikiFileDto fileDto = null;
            
            // Use execution strategy to handle transactions with retry logic
            var executionStrategy = _dbContext.Database.CreateExecutionStrategy();
            
            try
            {
                await executionStrategy.ExecuteAsync(async () =>
                {
                    using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                    {
                        try
                        {
                            // Add the file record to the database
                            await _dbContext.WikiFiles.AddAsync(wikiFile);
                            await _dbContext.SaveChangesAsync();

                            // Create department access records if any
                            if (model.DepartmentIds != null && model.DepartmentIds.Any())
                            {
                                foreach (var departmentId in model.DepartmentIds)
                                {
                                    // Comfirm that the department id is a valid team Id
                                    var isDepartmentIdValid = await _dbContext.Teams
                                        .FirstOrDefaultAsync(x => x.Id == departmentId);
                                    if (isDepartmentIdValid == null)
                                    {
                                        throw new InvalidOperationException("Invalid department ID provided");
                                    }

                                    await _dbContext.WikiFileDepartmentAccess.AddAsync(new WikiFileDepartmentAccess
                                    {
                                        Id = Guid.NewGuid(),
                                        WikiFileId = wikiFile.Id,
                                        DepartmentId = departmentId,
                                        CreatedAt = DateTime.UtcNow,
                                        IsActive = true
                                    });
                                }
                                await _dbContext.SaveChangesAsync();
                            }

                            // Handle file upload based on size
                            if (file.Length <= _directUploadThreshold)
                            {
                                // For smaller files, upload directly
                                string uploadResult = await _s3Service.UploadFileAsync(file, awsKey);
                                uploadSuccess = !string.IsNullOrEmpty(uploadResult);

                                wikiFile.UploadStatus = uploadSuccess ? WikiFileUploadStatus.Completed : WikiFileUploadStatus.Failed;
                                await _dbContext.SaveChangesAsync();
                            }
                            else
                            {
                                // For larger files, queue for background processing
                                wikiFile.UploadStatus = WikiFileUploadStatus.InProgress;
                                await _dbContext.SaveChangesAsync();

                                // Create a temporary file path to store the file for Hangfire processing
                                string tempFilePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + Path.GetExtension(file.FileName));

                                try
                                {
                                    // Save the file temporarily for Hangfire to process
                                    using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                                    {
                                        await file.CopyToAsync(fileStream);
                                    }

                                    BackgroundJob.Enqueue(() => _backgroundService.ProcessLargeFileUploadAsync(wikiFile.Id, tempFilePath, awsKey));

                                    // Set success to true since we've successfully queued the job
                                    uploadSuccess = true;
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error(ex, "Error preparing file for background processing: {FileId}", wikiFile.Id);
                                    uploadSuccess = false;

                                    // Update status to failed
                                    wikiFile.UploadStatus = WikiFileUploadStatus.Failed;
                                    await _dbContext.SaveChangesAsync();

                                    // Clean up temp file if it exists
                                    if (File.Exists(tempFilePath))
                                    {
                                        File.Delete(tempFilePath);
                                    }
                                    
                                    // Don't throw here, as we want to commit the transaction with the failed status
                                }
                            }

                            // Commit the transaction if everything succeeded
                            await transaction.CommitAsync();
                        }
                        catch (Exception ex)
                        {
                            // Log the exception
                            _logger.Error(ex, "Error during file upload transaction: {FileName}", file.FileName);
                            
                            // Rollback the transaction
                            await transaction.RollbackAsync();
                            
                            // Re-throw to be handled by the outer try-catch
                            throw;
                        }
                    }
                });

                // Get file details after transaction is committed
                if (uploadSuccess)
                {
                    fileDto = await GetFileDtoAsync(wikiFile.Id);
                    uploadedFiles.Add(fileDto);
                }
                else
                {
                    failedFiles.Add(file.FileName);
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Invalid department"))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Something went wrong, Invalid department."
                };
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Error(ex, "Error during file upload process: {FileName}", file.FileName);
                
                // Add to failed files
                failedFiles.Add(file.FileName);
                uploadSuccess = false;
            }

            // Create batch response with the single file
            var batchResponse = CreateBatchResponse(batchId, model.WikiFileType, uploadedFiles, model.TextContent);

            string responseMessage = uploadedFiles.Count > 0 ?
                "File uploaded successfully" :
                "File upload failed";

            return new GenericResponse
            {
                ResponseCode = uploadedFiles.Count > 0 ? "200" : "500",
                ResponseMessage = responseMessage,
                Data = batchResponse
            };
        }
        #endregion

        #region AddTextContentAsync
        public async Task<GenericResponse> AddTextContentAsync(WikiTextContentRequest model, Guid userId)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(model.TextContent))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Text content cannot be empty",
                    Data = null
                };
            }
            
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Batch number is required",
                    Data = null
                };
            }

            string batchId = model.BatchId;
            Guid wikiFileId = Guid.NewGuid();
            WikiFileBatchResponse batchResponse = null;
            
            var wikiFile = new WikiContent
            {
                Id = wikiFileId,
                UploadStatus = WikiFileUploadStatus.Completed,
                CreatedDate = DateTime.UtcNow,
                CreatedBy = userId,
                Description = model.Description,
                BatchId = batchId,
                WikiFileType = model.WikiFileType,
                IsDeleted = false,
                TextContent = model.TextContent
            };

            // Use execution strategy to handle transactions with retry logic
            var executionStrategy = _dbContext.Database.CreateExecutionStrategy();
            
            try
            {
                await executionStrategy.ExecuteAsync(async () =>
                {
                    using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                    {
                        try
                        {
                            // Add the wiki file record to the database
                            await _dbContext.WikiFiles.AddAsync(wikiFile);
                            await _dbContext.SaveChangesAsync();
                            
                            // If department IDs are provided, associate the text content with those departments
                            if (model.DepartmentIds != null && model.DepartmentIds.Any())
                            {
                                // Add department access entries
                                foreach (var departmentId in model.DepartmentIds)
                                {
                                    await _dbContext.WikiFileDepartmentAccess.AddAsync(new WikiFileDepartmentAccess
                                    {
                                        Id = Guid.NewGuid(),
                                        WikiFileId = wikiFile.Id,
                                        DepartmentId = departmentId,
                                        CreatedAt = DateTime.UtcNow,
                                        IsActive = true
                                    });
                                }
                                await _dbContext.SaveChangesAsync();
                            }
                            
                            // Commit the transaction
                            await transaction.CommitAsync();
                        }
                        catch (Exception ex)
                        {
                            // Log the exception
                            _logger.Error(ex, "Error during text content addition transaction: {BatchId}", batchId);
                            
                            // Rollback the transaction
                            await transaction.RollbackAsync();
                            
                            // Re-throw to be handled by the outer try-catch
                            throw;
                        }
                    }
                });
                
                // Create a batch response with the text content but no files
                batchResponse = CreateBatchResponse(batchId, model.WikiFileType, new List<WikiFileDto>(), model.TextContent);
                
                // Set the text content file ID in the response
                batchResponse.TextContentFileId = wikiFile.Id;
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Error(ex, "Error during text content addition: {BatchId}", batchId);
                
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to add text content due to a database error",
                    Data = null
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Text content added successfully",
                Data = batchResponse
            };
        }
        #endregion

        #region UpdateFileDetailsAsync
        public async Task<GenericResponse> UpdateFileDetailsAsync(WikiFileUpdateDto fileUpdateDto)
        {
            var file = await _dbContext.WikiFiles
                .FirstOrDefaultAsync(f => f.Id == fileUpdateDto.FileId && !f.IsDeleted);
            
            if (file == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "File not found",
                    Data = null
                };
            }

            WikiFileDto fileDto = null;
            
            // Use execution strategy to handle transactions with retry logic
            var executionStrategy = _dbContext.Database.CreateExecutionStrategy();
            
            try
            {
                await executionStrategy.ExecuteAsync(async () =>
                {
                    using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                    {
                        // Update file details
                        file.Description = fileUpdateDto.Description;
                        file.WikiFileType = fileUpdateDto.WikiFileType;
                        await _dbContext.SaveChangesAsync();

                        // Commit the transaction
                        await transaction.CommitAsync();
                    }
                });

                // Get the updated file details after transaction is committed
                fileDto = await GetFileDtoAsync(file.Id);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating file details for file ID {FileId}", fileUpdateDto.FileId);
                
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while updating file details",
                    Data = null
                };
            }
            
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "File details updated successfully",
                Data = fileDto
            };
        }
        #endregion

        #region UpdateFileDepartmentAccessAsync
        public async Task<GenericResponse> UpdateFileDepartmentAccessAsync(WikiFileDepartmentAccessUpdateDto accessUpdateDto)
        {
            var batchFiles = await _dbContext.WikiFiles
                    .Include(f => f.DepartmentAccess)
                    .Where(f => f.BatchId == accessUpdateDto.BatchId && !f.IsDeleted)
                    .ToListAsync();

            if (batchFiles == null || !batchFiles.Any())
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "No files found with the provided batch ID",
                    Data = null
                };
            }

            List<WikiFileDto> updatedFileDtos = new List<WikiFileDto>();

            // Use execution strategy to handle transactions with retry logic
            var executionStrategy = _dbContext.Database.CreateExecutionStrategy();
            
            try
            {
                await executionStrategy.ExecuteAsync(async () =>
                {
                    using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                    {
                        try
                        {
                            // Process each file in the batch
                            foreach (var file in batchFiles)
                            {
                                foreach (var deptAccess in accessUpdateDto.WikiDepartmentAccess)
                                {
                                    if (deptAccess.IsActive)
                                    {
                                        var existingAccess = file.DepartmentAccess
                                            .FirstOrDefault(a => a.DepartmentId == deptAccess.DepartmentId && !a.IsActive);

                                        if (existingAccess != null)
                                        {
                                            existingAccess.IsActive = true;
                                            existingAccess.RemovedAt = null;
                                        }
                                        else
                                        {
                                            var activeAccess = file.DepartmentAccess
                                                .FirstOrDefault(a => a.DepartmentId == deptAccess.DepartmentId && a.IsActive);
                                            
                                            if (activeAccess == null)
                                            {
                                                // Comfirm that the department id is a valid team Id
                                                var isDepartmentIdValid = await _dbContext.Teams
                                                    .FirstOrDefaultAsync(x => x.Id == deptAccess.DepartmentId);
                                                if (isDepartmentIdValid == null)
                                                {
                                                    throw new InvalidOperationException("Invalid department ID provided");
                                                }

                                                await _dbContext.WikiFileDepartmentAccess.AddAsync(new WikiFileDepartmentAccess
                                                {
                                                    Id = Guid.NewGuid(),
                                                    WikiFileId = file.Id,
                                                    DepartmentId = deptAccess.DepartmentId,
                                                    CreatedAt = DateTime.UtcNow,
                                                    IsActive = true
                                                });
                                            }
                                        }
                                    }
                                    else
                                    {
                                        var access = file.DepartmentAccess
                                            .FirstOrDefault(a => a.DepartmentId == deptAccess.DepartmentId && a.IsActive);

                                        if (access != null)
                                        {
                                            access.IsActive = false;
                                            access.RemovedAt = DateTime.UtcNow;
                                        }
                                    }
                                }
                            }

                            await _dbContext.SaveChangesAsync();
                            
                            await transaction.CommitAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.Error(ex, "Error updating department access for batch ID {BatchId}", accessUpdateDto?.BatchId);
                           
                            await transaction.RollbackAsync();
                            throw;
                        }
                    }
                });
                
                // Get updated file details for all files in the batch
                foreach (var file in batchFiles)
                {
                    var fileDto = await GetFileDtoAsync(file.Id);
                    if (fileDto != null)
                    {
                        updatedFileDtos.Add(fileDto);
                    }
                }
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("Invalid department"))
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Something went wrong, Invalid department."
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating department access for batch ID {BatchId}", accessUpdateDto?.BatchId);
                
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while updating department access",
                    DevResponseMessage = ex.ToString(),
                    Data = null
                };
            }

            // Create batch response with updated files
            var batchResponse = CreateBatchResponse(accessUpdateDto.BatchId, batchFiles.FirstOrDefault()?.WikiFileType ?? 0, updatedFileDtos, null);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "File department access updated successfully",
                Data = batchResponse
            };
        }
        #endregion

        #region Delete Uploaded File
        public async Task<GenericResponse> DeleteFileAsync(Guid fileId)
        {
            var file = await _dbContext.WikiFiles
                    .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);
            if (file == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "File not found",
                    Data = null
                };
            }

            // Use execution strategy to handle transactions with retry logic
            var executionStrategy = _dbContext.Database.CreateExecutionStrategy();
            
            try
            {
                await executionStrategy.ExecuteAsync(async () =>
                {
                    using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                    {
                        // Soft delete the file
                        file.IsDeleted = true;
                        file.DeletedDate = DateTime.UtcNow;
                        await _dbContext.SaveChangesAsync();
                        
                        // Commit the transaction
                        await transaction.CommitAsync();
                    }
                });
            }
            catch (Exception ex)
            {
                // Log the exception
                _logger.Error(ex, "Error deleting file with ID {FileId}", fileId);
                
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while deleting the file",
                    Data = null
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "File deleted successfully",
                Data = null
            };
        }
        #endregion

        #region Get File By Id Async
        public async Task<GenericResponse> GetFileByIdAsync(Guid fileId)
        {
            var file = await _dbContext.WikiFiles
                    .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);
            if (file == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "File not found",
                    Data = null
                };
            }

            // Get file details
            var fileDto = await GetFileDtoAsync(fileId);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "File retrieved successfully",
                Data = fileDto
            };
        }
        #endregion

        #region Get Files Async
        /// <summary>
        /// Gets files based on the provided filter criteria, including pagination and search options.
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetFilesAsync(WikiFileFilterDto filter)
        {
            // Start with base query
            var query = _dbContext.WikiFiles.AsQueryable();

            // Apply filters
            if (!filter.IncludeDeleted)
            {
                query = query.Where(f => !f.IsDeleted);
            }

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                query = query.Where(f => f.FileName.Contains(filter.SearchTerm) ||
                                        f.Description.Contains(filter.SearchTerm));
            }

            if (filter.UploadStatuses != null && filter.UploadStatuses.Any())
            {
                query = query.Where(f => filter.UploadStatuses.Contains(f.UploadStatus));
            }

            if (filter.UploadedFrom.HasValue)
            {
                query = query.Where(f => f.CreatedDate >= filter.UploadedFrom.Value);
            }

            if (filter.UploadedTo.HasValue)
            {
                query = query.Where(f => f.CreatedDate <= filter.UploadedTo.Value);
            }

            if (filter.DepartmentIds != null && filter.DepartmentIds.Any())
            {
                query = query.Where(f => f.DepartmentAccess.Any(a =>
                    filter.DepartmentIds.Contains(a.DepartmentId) && a.IsActive));
            }
            
            if (filter.FileTypes != null && filter.FileTypes.Any())
            {
                query = query.Where(f => filter.FileTypes.Contains(f.WikiFileType));
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var files = await query
                .OrderByDescending(f => f.CreatedDate)
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Create DTOs for each file
            var fileDtos = new List<WikiFileDto>();
            foreach (var file in files)
            {
                fileDtos.Add(await GetFileDtoAsync(file.Id));
            }

            // Create pagination metadata
            var paginationMetadata = new PaginationMetadata
            {
                CurrentPage = filter.PageNumber,
                PageSize = filter.PageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)filter.PageSize)
            };

            // Check if we need to group files by batch ID
            if (filter.GroupByBatch)
            {
                // Group files by batch ID
                var batchGroups = fileDtos
                    .Where(f => !string.IsNullOrEmpty(f.BatchId))
                    .GroupBy(f => new { f.BatchId, f.WikiFileType })
                    .Select(g => new WikiFileBatchResponse
                    {
                        BatchId = g.Key.BatchId,
                        WikiFileType = g.Key.WikiFileType,
                        Files = g.ToList()
                    })
                    .ToList();

                // Add files without batch ID separately
                var filesWithoutBatch = fileDtos.Where(f => string.IsNullOrEmpty(f.BatchId)).ToList();

                // Create response with grouped files
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Files retrieved successfully",
                    Data = new
                    {
                        BatchGroups = batchGroups,
                        IndividualFiles = filesWithoutBatch,
                        Pagination = paginationMetadata
                    }
                };
            }

            // Create standard response without grouping
            var response = new WikiFileListResponse
            {
                Files = fileDtos,
                Pagination = paginationMetadata
            };

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Files retrieved successfully",
                Data = response
            };
        }
        #endregion

        #region Get User Accessible Files
        /// <summary>
        /// Gets files that a user has access to based on their department memberships and other filters.
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetUserAccessibleFilesAsync(Guid userId, WikiFileFilterDto filter)
        {
            // Get user's departments
            var userDepartments = await _dbContext.TeamMembers
                .Where(d => d.UserId == userId.ToString())
                .Select(d => d.TeamId)
                .ToListAsync();

            // Start with base query
            var query = _dbContext.WikiFiles
                .Where(f => !f.IsDeleted && f.DepartmentAccess.Any(a => userDepartments.Contains(a.DepartmentId) && a.IsActive));

            // Apply filters
            if (!filter.IncludeDeleted)
            {
                query = query.Where(f => !f.IsDeleted);
            }

            if (!string.IsNullOrWhiteSpace(filter.SearchTerm))
            {
                query = query.Where(f => f.FileName.Contains(filter.SearchTerm) ||
                                                       f.Description.Contains(filter.SearchTerm));
            }

            if (filter.UploadStatuses != null && filter.UploadStatuses.Any())
            {
                query = query.Where(f => filter.UploadStatuses.Contains(f.UploadStatus));
            }

            if (filter.UploadedFrom.HasValue)
            {
                query = query.Where(f => f.CreatedDate >= filter.UploadedFrom.Value);
            }

            if (filter.UploadedTo.HasValue)
            {
                query = query.Where(f => f.CreatedDate <= filter.UploadedTo.Value);
            }
            
            if (filter.FileTypes != null && filter.FileTypes.Any())
            {
                query = query.Where(f => filter.FileTypes.Contains(f.WikiFileType));
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var files = await query
                .OrderByDescending(f => f.CreatedDate)
                .Skip((filter.PageNumber - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            // Create DTOs for each file
            var fileDtos = new List<WikiFileDto>();
            foreach (var file in files)
            {
                fileDtos.Add(await GetFileDtoAsync(file.Id));
            }

            // Create pagination metadata
            var paginationMetadata = new PaginationMetadata
            {
                CurrentPage = filter.PageNumber,
                PageSize = filter.PageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)filter.PageSize)
            };

            // Check if we need to group files by batch ID
            if (filter.GroupByBatch)
            {
                // Group files by batch ID
                var batchGroups = fileDtos
                    .Where(f => !string.IsNullOrEmpty(f.BatchId))
                    .GroupBy(f => new { f.BatchId, f.WikiFileType })
                    .Select(g => new WikiFileBatchResponse
                    {
                        BatchId = g.Key.BatchId,
                        WikiFileType = g.Key.WikiFileType,
                        Files = g.ToList()
                    })
                    .ToList();

                // Add files without batch ID separately
                var filesWithoutBatch = fileDtos.Where(f => string.IsNullOrEmpty(f.BatchId)).ToList();

                // Create response with grouped files
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "User accessible files retrieved successfully",
                    Data = new
                    {
                        BatchGroups = batchGroups,
                        IndividualFiles = filesWithoutBatch,
                        Pagination = paginationMetadata
                    }
                };
            }

            // Create standard response without grouping
            var response = new WikiFileListResponse
            {
                Files = fileDtos,
                Pagination = paginationMetadata
            };

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "User accessible files retrieved successfully",
                Data = response
            };
        }
        #endregion

        #region User Has Access To File
        public async Task<bool> UserHasAccessToFileAsync(Guid fileId, Guid userId)
        {
            var userDepartments = await _dbContext.TeamMembers
                   .Where(d => d.UserId == userId.ToString())
                   .Select(d => d.TeamId)
                   .ToListAsync();

            // Check if the file exists and is not deleted
            var file = await _dbContext.WikiFiles
                .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

            if (file == null)
            {
                return false;
            }

            // Check if the user is the uploader
            if (file.CreatedBy == userId)
            {
                return true;
            }

            // Check if any of the user's departments have access to the file
            var hasAccess = await _dbContext.WikiFileDepartmentAccess
                .AnyAsync(a => a.WikiFileId == fileId &&
                              userDepartments.Contains(a.DepartmentId) &&
                              a.IsActive);

            return hasAccess;
        }
        #endregion

        #region Get File Presigned URL Async
        public async Task<GenericResponse> GetFilePresignedUrlAsync(Guid fileId, Guid userId)
        {
            // Check if the user has access to the file
            bool hasAccess = await UserHasAccessToFileAsync(fileId, userId);
            if (!hasAccess)
            {
                return new GenericResponse
                {
                    ResponseCode = "403",
                    ResponseMessage = "You do not have access to this file",
                    Data = null
                };
            }

            // Get the file
            var file = await _dbContext.WikiFiles
                .FirstOrDefaultAsync(f => f.Id == fileId && !f.IsDeleted);

            if (file == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "File not found",
                    Data = null
                };
            }

            // Check if the file upload is complete
            if (file.UploadStatus != WikiFileUploadStatus.Completed)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"File upload is not complete. Current status: {file.UploadStatus}",
                    Data = null
                };
            }

            // Generate presigned URL for download
            string presignedUrl = await _s3Service.GetPresignedUrlForKeyAsync(file.AwsKey, 10080);

            if (string.IsNullOrEmpty(presignedUrl))
            {
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Failed to generate presigned URL",
                    Data = null
                };
            }

            // Update last accessed date
            file.LastAccessedDate = DateTime.UtcNow;
            await _dbContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Presigned URL generated successfully",
                Data = new { PresignedUrl = presignedUrl }
            };
        }
        #endregion

        #region Get All Files Uploaded To All Companies
        public async Task<GenericResponse> GetAllFilesInTheSystemAsync()
        {
            var tenants = await _publicContext.Tenants
                .ToListAsync();

            // Add the files to a dictonary grouped by tennat(companyId)
            var groupedFiles = new Dictionary<string, List<WikiFileDto>>();

            foreach (var tenant in tenants)
            {
                await using var context = new JobProDbContext(_conString, new DbContextSchema(tenant.Subdomain));

                var files = await context.WikiFiles
                    .Where(f => !f.IsDeleted && !f.IsAIProcessed)
                    .OrderByDescending(f => f.CreatedDate)
                    .ToListAsync();

                // Create DTOs for each file
                var fileDtos = new List<WikiFileDto>();
                foreach (var file in files)
                {
                    fileDtos.Add(await GetFileDtoAsync(file.Id));
                }

                groupedFiles[tenant.Subdomain] = fileDtos;
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "All files retrieved successfully",
                Data = groupedFiles
            };
        }
        #endregion

        #region Update Files As Processed
        public async Task<GenericResponse> UpdateFilesAsProcessedAsync(List<Guid> fileIds)
        {
            if (fileIds == null || !fileIds.Any())
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "No file IDs provided",
                    Data = null
                };
            }

            // Use execution strategy to handle transactions with retry logic
            var executionStrategy = _dbContext.Database.CreateExecutionStrategy();
            
            try
            {
                await executionStrategy.ExecuteAsync(async () =>
                {
                    using (var transaction = await _dbContext.Database.BeginTransactionAsync())
                    {
                        try
                        {
                            var filesToUpdate = await _dbContext.WikiFiles
                                .Where(f => fileIds.Contains(f.Id) && !f.IsDeleted)
                                .ToListAsync();

                            if (!filesToUpdate.Any())
                            {
                                throw new InvalidOperationException("No files found with the provided IDs");
                            }

                            foreach (var file in filesToUpdate)
                            {
                                file.IsAIProcessed = true;
                            }

                            await _dbContext.SaveChangesAsync();
                            
                            // Commit the transaction
                            await transaction.CommitAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.Error(ex, "Error updating files as processed: {FileIds}", string.Join(", ", fileIds));
                            
                            // Rollback the transaction
                            await transaction.RollbackAsync();
                            throw;
                        }
                    }
                });
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("No files found"))
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "No files found with the provided IDs",
                    Data = null
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating files as processed: {FileIds}", string.Join(", ", fileIds));
                
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while updating files as processed",
                    DevResponseMessage = ex.ToString(),
                    Data = null
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Files updated as processed successfully",
                Data = null
            };
        }
        #endregion

        #region Private Methods
        // Helper method to get file details including department access and presigned URL
        private async Task<WikiFileDto> GetFileDtoAsync(Guid fileId)
        {
            var file = await _dbContext.WikiFiles
                .Include(f => f.DepartmentAccess)
                .FirstOrDefaultAsync(f => f.Id == fileId);
            
            if (file == null)
            {
                return null;
            }
            
            // Get uploader name
            string uploaderName = "Unknown";
            var uploader = await _dbContext.UserProfiles
                .Where(x => x.UserId == file.CreatedBy.ToString()).FirstOrDefaultAsync();
            if (uploader != null)
            {
                uploaderName = $"{uploader.FirstName} {uploader.LastName}";
            }
            
            // Get department names
            var departmentAccess = new List<WikiFileDepartmentAccessDto>();
            foreach (var access in file.DepartmentAccess)
            {
                string departmentName = "Unknown";
                var department = await _dbContext.Teams.FindAsync(access.DepartmentId);
                if (department != null)
                {
                    departmentName = department.Name;
                }
                
                departmentAccess.Add(new WikiFileDepartmentAccessDto
                {
                    DepartmentId = access.DepartmentId,
                    DepartmentName = departmentName,
                    IsActive = access.IsActive
                });
            }
            
            // Generate presigned URL if the file upload is complete
            string presignedUrl = null;
            string fileSizeFormatted = null; // Default value to null
            if (file.UploadStatus == WikiFileUploadStatus.Completed && file.FileName != null)
            {
                presignedUrl = await _s3Service.GetPresignedUrlForKeyAsync(file.AwsKey);

                // Format file size
                fileSizeFormatted = FormatFileSize(file.FileSize.Value);
            }         
            
            return new WikiFileDto
            {
                Id = file.Id,
                FileName = file.FileName,
                FileType = file.FileType,
                FileSize = file.FileSize != null ? file.FileSize : null,
                FileSizeFormatted = fileSizeFormatted,
                UploadStatus = file.UploadStatus,
                UploadedDate = file.CreatedDate,
                UploadedBy = uploaderName,
                UploadedById = file.CreatedBy,
                Description = file.Description,
                LastAccessedDate = file.LastAccessedDate,
                PresignedUrl = presignedUrl,
                BatchId = file.BatchId,
                WikiFileType = file.WikiFileType,
                DepartmentAccess = departmentAccess,
                TextContent = file.TextContent
            };
        }

        // Helper method to ensure all files have batch IDs
        private void EnsureAllFilesHaveBatchIds(List<WikiFileDto> files)
        {
            // Group files without batch IDs by upload date (within a small window) and file type
            var filesWithoutBatchId = files.Where(f => string.IsNullOrEmpty(f.BatchId)).ToList();
            
            if (filesWithoutBatchId.Any())
            {
                // Group by upload date (rounded to the nearest minute) and file type
                var groups = filesWithoutBatchId
                    .GroupBy(f => new { 
                        UploadDate = new DateTime(f.UploadedDate.Year, f.UploadedDate.Month, f.UploadedDate.Day, 
                                            f.UploadedDate.Hour, f.UploadedDate.Minute, 0),
                        FileType = f.WikiFileType
                    })
                    .ToList();
                
                // Assign the same batch ID to files uploaded at the same time with the same type
                foreach (var group in groups)
                {
                    string batchId = Guid.NewGuid().ToString();
                    foreach (var file in group)
                    {
                        file.BatchId = batchId;
                    }
                }
            }
        }

        // Helper method to format file size
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }

            return $"{Math.Round(size, 2):0.##} {sizes[order]}";
        }

        // Helper method to create a WikiFileBatchResponse with the appropriate properties
        private WikiFileBatchResponse CreateBatchResponse(string batchId, WikiFileType wikiFileType, List<WikiFileDto> files, string textContent = null)
        {
            return new WikiFileBatchResponse
            {
                BatchId = batchId,
                WikiFileType = wikiFileType,
                Files = files,
                TextContent = textContent
            };
        }
        
        // Helper method to group files by batch ID
        private List<WikiFileBatchResponse> GroupFilesByBatchId(List<WikiFileDto> files)
        {
            return files
                .Where(f => !string.IsNullOrEmpty(f.BatchId))
                .GroupBy(f => new { f.BatchId, f.WikiFileType })
                .Select(g => CreateBatchResponse(g.Key.BatchId, g.Key.WikiFileType, g.ToList(), null))
                .ToList();
        }
        #endregion
    }
}
