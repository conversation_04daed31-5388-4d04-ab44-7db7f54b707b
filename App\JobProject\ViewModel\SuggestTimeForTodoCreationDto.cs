﻿using Newtonsoft.Json;
using System;
using System.Threading;

namespace Jobid.App.JobProject.ViewModel
{
    public class SuggestTimeForTodoCreationDto
    {
        public string AssigneeUserId { get; set; }
        public DateTime Date { get; set; }
        public string Duration { get; set; }

        [JsonIgnore]
        public CancellationToken CancellationToken { get; set; } = default;
    }
}
