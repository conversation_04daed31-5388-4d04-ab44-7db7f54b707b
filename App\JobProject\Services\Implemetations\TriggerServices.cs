﻿using Hangfire;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Notification.Hubs;
using Jobid.App.Notification.Models;
using Jobid.App.Notification.ViewModel;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using static Jobid.App.Helpers.Utils.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.Helpers.Services.Contract;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class TriggerServices : ITriggerServices
    {
        private JobProDbContext Db;
        public JobProDbContext Dbo;
        private readonly IEmailService _emailService;
        private readonly IHubContext<NotificationHub, INotificationClient> _hubContext;

        public TriggerServices(JobProDbContext _db, JobProDbContext publicSchemaContext, IEmailService emailService, IHubContext<NotificationHub, INotificationClient> hubContext)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _emailService = emailService;
            _hubContext = hubContext;
        }

        #region Add Trigger
        /// <summary>
        /// Add trigger
        /// </summary>
        /// <param name="triggerVm"></param>
        /// <param name="project"></param>
        /// <returns></returns>
        public async Task<ProjectTrigger> AddTrigger(TriggerVm triggerVm, ProjectMgmt_Project project = null)
        {
            triggerVm.ParticipantsIds.Add(triggerVm.UserId);
            var trigger = new ProjectTrigger()
            {
                TriggerName = triggerVm.TriggerName.ToTitleCase(),
               // TriggerReason = triggerVm.TriggerReason,
                Reasons = string.Join(",", triggerVm.Reasons),
                ProjectMgmt_ProjectId = triggerVm?.ProjectId == null ? null : triggerVm?.ProjectId.ToString(),
                ParticipantsIds = string.Join(",", triggerVm.ParticipantsIds),
                CreatedAt = GetAdjustedDateTimeBasedOnTZNow(),
                CreatedBy = triggerVm.UserId,
                SMS = triggerVm.SMS,
                Email = triggerVm.Email,
                Notification = triggerVm.Notification,
                TriggerReasons = triggerVm.Reasons

            };
            await Db.ProjectTriggers.AddAsync(trigger);

            var triggerSequences = triggerVm.TriggerDateAndTime.Select(x => new TriggerSequence()
            {
                DateAndTime = x,
                ProjectTriggerId = trigger.Id,
                Action = triggerVm.Action,
                SMS = triggerVm.SMS,
                Email = triggerVm.Email,
                Notification = triggerVm.Notification
            }).ToList();

            await Db.TriggerSequence.AddRangeAsync(triggerSequences);

            int result = await Db.SaveChangesAsync();
            if (result > 0)
            {
                // Get participants emails and send email
                var email = Db.UserProfiles
                    .Where(x => x.UserId == triggerVm.UserId)
                    .Select(e => e.Email).FirstOrDefault();
                
                    foreach (var triggerSequence in triggerSequences)
                    {
                       
                            
                                if (triggerSequence.Email)
                                {
                                    var subject = "Action Required: " + triggerVm.TriggerName;
                                    var body = "You have a trigger due on " + triggerSequence.DateAndTime.ToString("dd/MM/yyyy");
                                    BackgroundJob.Schedule(() => _emailService.SendEmail(body, email, subject), triggerSequence.DateAndTime);
                                }                               
                                // Todo: Add SMS Service

                                // Todo: Add Notification service
                                if(triggerSequence.Notification)
                                {

                                    var notification = new AddNotificationDto
                                    {
                                        Message = "You have a trigger for: " + trigger.TriggerName + " on " + triggerSequence.DateAndTime.ToString("dd/MM/yyyy"),
                                        Event = EventCategory.Trigger,
                                        EventId = trigger.Id.ToString(),
                                        CreatedBy = triggerVm.UserId
                                    };
                                    BackgroundJob.Schedule(() => AddInAppNotification(triggerVm, notification).ConfigureAwait(false), triggerSequence.DateAndTime);
                                }
                                                           
                                                             
                    }
                

                return trigger;
            }
            else { return null; }
        }
        #endregion

        #region Get Trigger By Id
        /// <summary>
        /// Get Trigger by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ProjectTriggerDto> GetTriggerById(Guid id)
        {
            var trigger = await Db.ProjectTriggers
                .Where(x => x.Id == id).FirstOrDefaultAsync();

            if(trigger.Reasons is not null)
            {
                trigger.TriggerReasons = trigger.Reasons.Split(",").ToList();
            }

            var triggerSequences = new List<TriggerSequenceDto>();
            var triggerToReturn = new ProjectTriggerDto();

            var sequence = await Db.TriggerSequence.Where(x => x.ProjectTriggerId == trigger.Id).ToListAsync();
            triggerSequences.AddRange(sequence.Select(x => new TriggerSequenceDto
            {
                Id = x.Id,
                DateAndTime = x.DateAndTime,
                Action = x.Action,
                ProjectTriggerId = x.ProjectTriggerId,
                SMS = x.SMS,
                Email = x.Email,
                Notification = x.Notification
            }).ToList());

            var projectName = "";
            if (trigger?.ProjectMgmt_ProjectId is not null)
            {
                projectName = await Db.ProjectMgmt_Projects
                .Where(x => x.ProjectId == Guid.Parse(trigger.ProjectMgmt_ProjectId)).Select(x => x.Name)
                .FirstOrDefaultAsync();
            }

            return new ProjectTriggerDto
            {
                Id = trigger.Id,
                TriggerName = trigger.TriggerName,
                TriggerReason = trigger.TriggerReason.ToString(),
                ProjectName = projectName,
                TriggerTime = trigger.TriggerTime,
                TriggerSequences = triggerSequences,
                TriggerReasons = trigger.TriggerReasons,
                SMS = trigger.SMS,
                Email = trigger.Email,
                Notification = trigger.Notification
            };
        }
        #endregion

        #region Get All Triggers for a project
        /// <summary>
        /// Get All Triggers for a project
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public async Task<List<ProjectTriggerDto>> GetTriggersById(string projectId)
        {
            var triggerList = new List<ProjectTriggerDto>();
            var triggers = await Db.ProjectTriggers
                .Where(x => x.ProjectMgmt_ProjectId == projectId).ToListAsync();

            var triggerSequences = new List<TriggerSequenceDto>();
            foreach (var trigger in triggers)
            {
                if (trigger.Reasons is not null)
                {
                    trigger.TriggerReasons = trigger.Reasons.Split(",").ToList();
                }
                var sequence = await this.Db.TriggerSequence.Where(x => x.ProjectTriggerId == trigger.Id).ToListAsync();
                triggerSequences.AddRange(sequence.Select(x => new TriggerSequenceDto
                {
                    Id = x.Id,
                    DateAndTime = x.DateAndTime,
                    Action = x.Action,
                    ProjectTriggerId = x.ProjectTriggerId,
                    SMS = x.SMS,
                    Email = x.Email,
                    Notification = x.Notification
                }).ToList());

                var projectName = await Db.ProjectMgmt_Projects
                    .Where(x => x.ProjectId == Guid.Parse(trigger.ProjectMgmt_ProjectId)).Select(x => x.Name)
                    .FirstOrDefaultAsync();

                triggerList.Add(new ProjectTriggerDto
                {
                    Id = trigger.Id,
                    TriggerName = trigger.TriggerName,
                    ProjectName = projectName,
                    TriggerReason = trigger.TriggerReason.ToString(),
                    TriggerTime = trigger.TriggerTime,
                    TriggerSequences = triggerSequences,
                    TriggerReasons = trigger.TriggerReasons,
                    SMS = trigger.SMS,
                    Email = trigger.Email,
                    Notification = trigger.Notification
                });
            }

            return triggerList;
        }
        #endregion

        #region Get All Triggers for a user
        public async Task<List<ProjectTriggerDto>> GetTriggersByUserId(string userId)
        {
            var triggerList = new List<ProjectTriggerDto>();
            var triggers = await Db.ProjectTriggers
                .Where(x => x.ParticipantsIds.Contains(userId) || x.CreatedBy == userId).ToListAsync();

            foreach (var trigger in triggers.Distinct())
            {
                if (trigger.Reasons is not null)
                {
                    trigger.TriggerReasons = trigger.Reasons.Split(",").ToList();
                }
                var triggerSequences = new List<TriggerSequenceDto>();
                var sequence = await Db.TriggerSequence.Where(x => x.ProjectTriggerId == trigger.Id).ToListAsync();
                triggerSequences.AddRange(sequence.Select(x => new TriggerSequenceDto
                {
                    Id = x.Id,
                    DateAndTime = x.DateAndTime,
                    Action = x.Action,
                    ProjectTriggerId = x.ProjectTriggerId,
                    SMS = x.SMS,
                    Email = x.Email,
                    Notification = x.Notification
                }).ToList());

                var projectName = "";
                if (trigger?.ProjectMgmt_ProjectId is not null)
                {
                    projectName = await Db.ProjectMgmt_Projects
                    .Where(x => x.ProjectId == Guid.Parse(trigger.ProjectMgmt_ProjectId)).Select(x => x.Name)
                    .FirstOrDefaultAsync();
                }

                triggerList.Add(new ProjectTriggerDto
                {
                    Id = trigger.Id,
                    TriggerName = trigger.TriggerName,
                    ProjectName = projectName,
                    TriggerReason = trigger.TriggerReason.ToString(),
                    TriggerTime = trigger.TriggerTime,
                    TriggerSequences = triggerSequences,
                    TriggerReasons = trigger.TriggerReasons,
                    SMS = trigger.SMS,
                    Email = trigger.Email,
                    Notification = trigger.Notification
            });
            }

            return triggerList;
        }
        #endregion

        #region Private Methods - Notification
        public async Task AddInAppNotification(TriggerVm triggerVm, AddNotificationDto notificationDto)
        {
            var notificationId = await AddNotification(notificationDto);
            if (notificationId is not null)
                await AddUserNotification(new List<string> { triggerVm.UserId }, Guid.Parse(notificationId));

            // Todo: Invoke a frontend method using SignalR
            await _hubContext.Clients.All.RecieveNotification();
        }

        private async Task<string> AddNotification(AddNotificationDto model)
        {
            var notification = new Notification.Models.Notification
            {
                Message = model.Message,
                Event = model.Event,
                EventId = model.EventId,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = model.CreatedBy,
            };

            await Db.Notifications.AddAsync(notification);
            return notification.Id.ToString();
        }

        private async Task<bool> AddUserNotification(List<string> userIds, Guid notificationId)
        {
            var userProfileId = await Db.UserProfiles.Where(x => userIds.Contains(x.UserId))
                .Select(x => x.Id.ToString()).ToListAsync();

            var userNotifications = new List<UserNotification>();
            foreach (var userId in userProfileId)
            {
                userNotifications.Add(new UserNotification
                {
                    UserProfileId = userId,
                    NotificationId = notificationId
                });
            }

            Db.UserNotifications.AddRange(userNotifications);
            return true;
        }
        #endregion

        #region Get Upcoming Triggers by projectId
        public async Task<List<ProjectTrigger>> GetUpcomingTriggerById(Guid id)
        {
            var todayDate = DateTime.UtcNow;
            return await Db.ProjectTriggers
                .Where(x => x.ProjectMgmt_ProjectId == id.ToString() && (todayDate - x.TriggerTime).TotalMinutes < 30)
                .ToListAsync();
        }
        #endregion

        #region Delete Trigger
        /// <summary>
        /// Delete Trigger
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> DeleteTrigger(Guid id)
        {
            var trigger = await Db.ProjectTriggers.FindAsync(id);
            if (trigger is null)
                throw new RecordNotFoundException("Trigger not found");

            // Delete Trigger Sequences
            var triggerSequences = await Db.TriggerSequence.Where(x => x.ProjectTriggerId == id).ToListAsync();
            Db.TriggerSequence.RemoveRange(triggerSequences);

            Db.ProjectTriggers.Remove(trigger);
            return await Db.SaveChangesAsync() > 0;
        }
        #endregion
    }
}
