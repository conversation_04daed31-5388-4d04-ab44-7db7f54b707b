﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class update_ext_sub_tbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public update_ext_sub_tbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "EnterprizeFreePlan",
                schema: _Schema,
                table: "EnterprizeSubscriptionPayments",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EnterprizeFree<PERSON><PERSON>",
                schema: _Schema,
                table: "EnterprizeSubscriptionPayments");
        }
    }
}
