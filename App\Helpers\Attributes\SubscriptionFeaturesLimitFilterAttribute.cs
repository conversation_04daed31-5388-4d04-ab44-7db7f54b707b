﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.SchemaTenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Helpers.Attributes
{
    public class SubscriptionFeaturesLimitFilterAttribute : Attribute, IAsyncActionFilter
    {
        private readonly string _featureToCheck;
        private readonly Applications _apps;

        public SubscriptionFeaturesLimitFilterAttribute(CategoriesForFeatures projectManagement)
        {
            _featureToCheck = projectManagement.ToString();
        }

        public Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // Check user claims from token
            var claims = context.HttpContext.User.Claims.ToList();
            var userId = claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;


            // Get 'aaplication' header value from the header and allow if its 'Echo'
            var application = context.HttpContext.Request.Headers["application"].ToString();
            if (application == "Echo" || application == "echo")
            {
                return next();
            }

            // Get the required services
            var tenantConfig = context.HttpContext.RequestServices.GetService(typeof(ITenantConfig<JobProDbContext>)) as ITenantConfig<JobProDbContext>;
            var httpContextAccessor = context.HttpContext.RequestServices.GetService(typeof(IHttpContextAccessor)) as IHttpContextAccessor;

            var subdomainContext = tenantConfig.getRequestContext(httpContextAccessor.HttpContext);
            var publicContext = new JobProDbContext(new DbContextSchema());
            var subdomain = tenantConfig.getSubdomainName(httpContextAccessor.HttpContext);

            List<Subscription.Models.Subscription> subscriptions = new List<Subscription.Models.Subscription>();
            var response = new GenericResponse();
            response.Data = true;
            if (subdomain != "api" && !string.IsNullOrEmpty(subdomain))
            {
                var tenantId = publicContext.Tenants.FirstOrDefault(x => x.Subdomain == subdomain).Id;
                subscriptions = publicContext.Subscriptions.Where(x => x.TenantId == tenantId).ToList();
            }
            else
            {
                subscriptions = publicContext.Subscriptions.Where(x => x.UserId == userId).ToList();
            }

            #region Check if the company has reached the maximum number of projects for their current plan
            if (_featureToCheck == CategoriesForFeatures.Project.ToString())
            {
                var subscription = subscriptions.Where(x => x.Application == Applications.Joble).FirstOrDefault();
                var maxNumberOfProjects = 0;
                var plan = string.Empty;
                var isLimited = true;
                var pricingAndFeatures = publicContext.PricingAndFeatures
                    .Include(x => x.PricingPlan)
                    .Include(x => x.Feature).Where(x => x.PricingPlanId.ToString() == subscription.PricingPlanId.ToString()).ToList();
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    plan = pricingAndFeature.PricingPlan.Name;
                    if (plan == PricingPlans.Enterprise.ToString())
                    {
                        var planDetails = publicContext.EnterpriseSubscriptions.FirstOrDefault(sub => sub.SubscriptionId == subscription.Id && sub.Application == Applications.Joble);
                        if (planDetails != null)
                        {
                            if (planDetails.ProjectLimit == 0)
                                isLimited = false;
                            else
                                maxNumberOfProjects = planDetails.ProjectLimit;
                            break;
                        }
                    }
                    else
                    {
                        if (pricingAndFeature.Feature.FeatureName.Replace(" ", "") == CategoriesForFeatures.Project.ToString() 
                            && pricingAndFeature.Feature.Application == Applications.Joble.ToString())
                        {
                            if (pricingAndFeature.IsLimited)
                            {
                                maxNumberOfProjects = Convert.ToInt32(pricingAndFeature.LimitedTo);
                                break;
                            }
                            else
                                isLimited = false;
                        }
                    }                  
                }

                var noOfProjectsAlreadyCreated = subdomainContext.ProjectMgmt_Projects.Count();
                if (maxNumberOfProjects > 0 && noOfProjectsAlreadyCreated == maxNumberOfProjects && isLimited)
                {
                    response.ResponseMessage = $"You company have reached the maximum number of projects for {plan} plan. Please contact your admin";
                    response.Data = false;
                    response.ResponseCode = "400";
                }
            }
            #endregion

            #region Check if the company has reached the maximum number of users under their current plan
            else if (_featureToCheck == CategoriesForFeatures.User.ToString())
            {
                var subscription = subscriptions.FirstOrDefault();
                var maxNumberOfUsers = 0;
                var plan = string.Empty;
                var pricingAndFeatures = publicContext.PricingAndFeatures.Include(x => x.Feature).Where(x => x.PricingPlanId.ToString() == subscription.PricingPlanId.ToString()).ToList();
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    plan = pricingAndFeature.Feature.FeatureName;
                    plan = pricingAndFeature.Feature.FeatureName;
                    if (plan == PricingPlans.Enterprise.ToString())
                    {
                        var planDetails = publicContext.EnterpriseSubscriptions.FirstOrDefault(sub => sub.SubscriptionId == subscription.Id && sub.Application == Applications.Joble);
                        if (planDetails != null)
                        {
                            maxNumberOfUsers = planDetails.UsersLimit;
                            break;
                        }
                    }
                    else
                    {
                        if (pricingAndFeature.Feature.FeatureName.Replace(" ", "") == CategoriesForFeatures.Project.ToString() && pricingAndFeature.Feature.Application == Applications.Joble.ToString())
                        {
                            if (pricingAndFeature.IsLimited)
                            {
                                maxNumberOfUsers = Convert.ToInt32(pricingAndFeature.LimitedTo);
                                break;
                            }
                        }
                    }
                }

                var userAlreadyAddCount = subdomainContext.AppPermissions.Count(per => per.TenantId == subscription.TenantId.ToString() && per.IsEnabled == true && per.SubscriptionStatus == SubscriptionStatus.Active.ToString());
                if (userAlreadyAddCount >= maxNumberOfUsers && maxNumberOfUsers != 0)
                {
                    response.ResponseMessage = $"You company have reached the maximum number of users for {plan} plan";
                    response.Data = false;
                    response.ResponseCode = "400";
                }
            }
            #endregion

            if ((bool)response.Data == false)
            {
                context.Result = new BadRequestObjectResult(response);
                return Task.CompletedTask;
            }
            
            return next();
        }
    }
}
