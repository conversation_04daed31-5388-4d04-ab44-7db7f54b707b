﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Jobid.App.Helpers.Enums;

namespace Jobid.App.Helpers.ViewModel.IdentityVM
{
    public class UpdateModel
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string UserImage { get; set; }
        public string ProofOfResidence { get; set; }
        public string GovernmentId { get; set; }
        public string PhoneNumber { get; set; }
        public string DateOfBirth { get; set; }
        public string CompanyId { get; set; }
        public string Status { get; set; }
        public string StatusComment { get; set; }
        public string CV_URL { get; set; }
        public string PortfolioPlatform { get; set; }
        public string LinkedinPlatform { get; set; }
        public string UpdatedBy { get; set; }
        public bool IsVerified { get; set; }
        public string NewReference { get; set; }
        public bool IsEmailVerified { get; set; }
        public bool IsPhoneNumberVerified { get; set; }
        public Region Region { get; set; }
    }
}
