﻿using Microsoft.AspNetCore.SignalR;
using Serilog;
using System;
using System.Threading.Tasks;

namespace Jobid.App.Notification.Hubs
{
    public sealed class SmartLoginHub : Hub<ISmartLoginClient>
    {
        private ILogger _logger = Log.ForContext<SmartLoginHub>();

        public override async Task OnConnectedAsync()
        {
            var connectionId = Context.ConnectionId;
            _logger.Information($"New connection established: {connectionId}");
            Console.WriteLine($"New connection established: {connectionId}");

            await base.OnConnectedAsync();
        }

        public async Task RegisterConnection(string browserId)
        {
            if (string.IsNullOrEmpty(browserId) || browserId.Length != 40)
            {
                _logger.Error("Invalid browser ID. Must be 40 characters.");
                throw new HubException("Invalid browser ID. Must be 40 characters.");
            }

            // Remove from any existing groups first
            var existingGroups = Groups;
            if (existingGroups != null)
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, browserId);
            }

            await Groups.AddToGroupAsync(Context.ConnectionId, browserId);
        }

        public async Task UnregisterConnection(string browserId)
        {
            if (string.IsNullOrEmpty(browserId) || browserId.Length != 40)
            {
                _logger.Error("Invalid browser ID. Must be 40 characters.");
                throw new HubException("Invalid browser ID. Must be 40 characters.");
            }

            await Groups.RemoveFromGroupAsync(Context.ConnectionId, browserId);
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            // Cleanup can be done here if needed
            await base.OnDisconnectedAsync(exception);
        }
    }

    public interface ISmartLoginClient
    {
        Task Login(object loginResponse);
    }
}
