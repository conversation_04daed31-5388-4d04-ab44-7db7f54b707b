using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models;
using Microsoft.AspNetCore.Http;

namespace Jobid.App.AdminConsole.Dto
{       
    /// <summary>
    /// DTO for creating a new contact group
    /// </summary>
    public class CreateContactGroupDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        [StringLength(1000)]
        public string SalePitch { get; set; }

        [Required]
        public List<Guid> ContactIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Language for the group communications
        /// </summary>
        [StringLength(100)]
        public string Language { get; set; } = "English";

        /// <summary>
        /// Preferred voice AI for the group
        /// </summary>
        public PreferredVoiceAI PreferredVoiceAI { get; set; } = PreferredVoiceAI.Jude;

        public string UserId { get; set; }

        /// <summary>
        /// Optional file upload for the contact group
        /// </summary>
        public IFormFile AttachmentFile { get; set; }
    }
    
    /// <summary>
    /// DTO for updating an existing contact group
    /// </summary>
    public class UpdateContactGroupDto
    {
        [Required]
        public Guid Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        [StringLength(1000)]
        public string SalePitch { get; set; }   
        
        [Required]
        public List<Guid> ContactIds { get; set; } = new List<Guid>();

        /// <summary>
        /// Language for the group communications
        /// </summary>
        [StringLength(100)]
        public string Language { get; set; }

        /// <summary>
        /// Preferred voice AI for the group
        /// </summary>
        public PreferredVoiceAI PreferredVoiceAI { get; set; }

        public ContactGroupStatus Status { get; set; }

        public string UserId { get; set; }
    }    
      /// <summary>
    /// DTO for contact group response
    /// </summary>
    public class ContactGroupDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string SalePitch { get; set; }        
        public List<Guid> ContactIds { get; set; } = new List<Guid>();
        public List<UserContactDto> Contacts { get; set; } = new List<UserContactDto>();
        public ContactGroupStatus Status { get; set; }
        
        /// <summary>
        /// Language for the group communications
        /// </summary>
        public string Language { get; set; }

        /// <summary>
        /// Preferred voice AI for the group
        /// </summary>
        public PreferredVoiceAI PreferredVoiceAI { get; set; }

        public string UserId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }

        /// <summary>
        /// File attachment information
        /// </summary>
        public string AttachmentFileName { get; set; }
        public string AttachmentFileUrl { get; set; }
        public long? AttachmentFileSize { get; set; }
        public string AttachmentFileType { get; set; }
    }
    
    /// <summary>
    /// DTO for paginated contact group list
    /// </summary>
    public class PaginatedContactGroupsDto
    {
        public List<ContactGroupDto> Groups { get; set; } = new List<ContactGroupDto>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }    
    
    /// <summary>
    /// DTO for contact group statistics
    /// </summary>
    public class ContactGroupStatsDto
    {
        public int TotalGroups { get; set; }
        public int ActiveGroups { get; set; }
        public int DraftGroups { get; set; }
        public int CompletedGroups { get; set; }
        public int TotalContacts { get; set; }
    }    
    
    /// <summary>
    /// DTO for adding/removing contacts from contact group
    /// </summary>
    public class UpdateContactGroupContactsDto
    {
        [Required]
        public Guid GroupId { get; set; }

        [Required]
        public List<Guid> ContactIds { get; set; } = new List<Guid>();

        public string UserId { get; set; }
    }    
    
    /// <summary>
    /// DTO for updating contact group status
    /// </summary>
    public class UpdateContactGroupStatusDto
    {
        [Required]
        public Guid GroupId { get; set; }        
        [Required]
        public ContactGroupStatus Status { get; set; }

        public string UserId { get; set; }
    }

    /// <summary>
    /// DTO for uploading a file to an existing contact group
    /// </summary>
    public class UploadContactGroupFileDto
    {
        [Required]
        public Guid GroupId { get; set; }

        [Required]
        public IFormFile AttachmentFile { get; set; }

        public string UserId { get; set; }
    }

    /// <summary>
    /// DTO for deleting a file from an existing contact group
    /// </summary>
    public class DeleteContactGroupFileDto
    {
        [Required]
        public Guid GroupId { get; set; }

        public string UserId { get; set; }
    }
}
