using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Helpers.Models
{
    public class UserCompanies
    {
        [Key]
        public Guid Id { get; set; }
        public string UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User user { get; set; }
        public bool Active { get; set; }
        public string Email { get; set; }
        public EmploymentType EmployementType { get; set; }
        public LocationType LocationType { get; set; }
        public DateTime StartDate { get; set; }
        public Guid TenantId { get; set; }

        [ForeignKey("TenantId")]
        public virtual Tenant.Model.Tenant tenant { get; set; }
        public DateTime DateCreated { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime LastUpdate { get; set; }

        [NotMapped]
        public string LastVisited { get; set; }
    }
}
