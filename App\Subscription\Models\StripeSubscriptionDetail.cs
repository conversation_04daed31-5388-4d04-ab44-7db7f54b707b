﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Subscription.Models
{
    public class StripeSubscriptionDetail
    {
        [Key]
        public Guid Id { get; set; } = new Guid();

        [Required]
        public string Plan { get; set; }
        public string Application { get; set; }
        public bool AIAgent { get; set; }
        public string ProductId { get; set; }
        public string PriceId { get; set; }
        public string Interval { get; set; }
        public string Currency { get; set; }
    }
}
