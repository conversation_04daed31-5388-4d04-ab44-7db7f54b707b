﻿using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers.Enums;
using System;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class OrganogramCompanyResponseDto
    {
        public Guid Id { get; set; }
        public long Index { get; set; }
        public string CompanyName { get; set; }
        public string Country { get; set; }
        public string FullAddress { get; set; }
        public string EmailAddress { get; set; }
        public string BranchColor { get; set; }
        public long BelongsTo { get; set; }
        public EntityType EntityType { get; set; }
        public Industries Industry { get; set; }
        public CompanyType CompanyType { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
    }
}
