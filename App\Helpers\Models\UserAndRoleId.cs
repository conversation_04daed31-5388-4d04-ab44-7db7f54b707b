﻿using Jobid.App.AdminConsole.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Helpers.Models
{
    public class UserAndRoleId
    {
        [Key]
        public Guid Id { get; set; } = new Guid();

        [ForeignKey(nameof(EmployeeRoles))]
        public string RoleId { get; set; }
        public string UserProId { get; set; }

        // Navigational Properties
        public EmployeeRoles EmployeeRoles { get; set; }
    }
}
