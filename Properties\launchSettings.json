{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Jobid": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:6001;http://localhost:6000", "dotnetRunMessages": true}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "launchUrl": "https://localhost:5001/swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:5001;http://localhost:5000"}, "distributionName": ""}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:21297", "sslPort": 44376}}}