using Hangfire;
using Jobid.App.ActivityLog.BackGroundJobs;
using Jobid.App.ActivityLog.contract;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Calender.Contracts;
using Jobid.App.JobProject.Services.Contract;
using System;

namespace Jobid.App.Helpers.Utils
{
    public class HangFireJobScheduler
    {
        private readonly IBackGroundServices _backGroundServices;
        private readonly IProductUpdateReceiverService _productUpdateReceiverService;
        private readonly IActivityViewBackgroundService _activityBackgroundService;
        private readonly IBackgroungService _adminConsoleBackgroungService;
        private readonly IActivityBackgroundService _activityBackgroundServiceMain;
        private readonly IBackGroundService _calenderBackGroundService;
        private readonly IPhoneNumberMaintenanceService _phoneNumberMaintenanceService;

        public HangFireJobScheduler(IBackGroundServices backGroundServices, IProductUpdateReceiverService productUpdateReceiverService, IActivityViewBackgroundService activityBackgroundService, IBackgroungService adminConsoleBackgroungService, IActivityBackgroundService activityBackgroundServiceMain, IBackGroundService calenderBackGroundService, IPhoneNumberMaintenanceService phoneNumberMaintenanceService)
        {
            _backGroundServices = backGroundServices;
            _productUpdateReceiverService = productUpdateReceiverService;
            _activityBackgroundService = activityBackgroundService;
            _adminConsoleBackgroungService = adminConsoleBackgroungService;
            _activityBackgroundServiceMain = activityBackgroundServiceMain;
            _calenderBackGroundService = calenderBackGroundService;
            _phoneNumberMaintenanceService = phoneNumberMaintenanceService;
        }

        public void ScheduleRecurringJobs()
        {
            // Delete notifications after 30 days
            RecurringJob.RemoveIfExists("delete-notification");
            RecurringJob.AddOrUpdate("delete-notification", () => _backGroundServices.DeleteNotificationsAfterSomeDays(GlobalVariables.Subdomains), Cron.Daily);
            RecurringJob.TriggerJob("delete-notification");

            // Delete user notifications after 24 hours
            RecurringJob.RemoveIfExists("delete-user-notification");
            RecurringJob.AddOrUpdate("delete-user-notification", () => _backGroundServices.DisapperaReadNotificationsAfter24Hours(GlobalVariables.Subdomains), Cron.Daily);
            RecurringJob.TriggerJob("delete-user-notification");

            // Update todo status to OverDue for overdue todos
            RecurringJob.RemoveIfExists("update-todo-status");
            RecurringJob.AddOrUpdate("update-todo-status", () => _backGroundServices.UpdateTodoStatusToOverDue(GlobalVariables.Subdomains), Cron.Hourly);
            RecurringJob.TriggerJob("update-todo-status");

            // Update project status to OverDue for overdue projects
            //RecurringJob.RemoveIfExists("update-project-status");
            //RecurringJob.AddOrUpdate("update-project-status", () => _backGroundServices.UpdateProjectStatusToOverDue(GlobalVariables.Subdomains), Cron.Daily);
            //RecurringJob.TriggerJob("update-project-status");

            // Update sprint status to OverDue for overdue sprints
            //RecurringJob.RemoveIfExists("update-sprint-status");
            //RecurringJob.AddOrUpdate("update-sprint-status", () => _backGroundServices.UpdateSprintStatusToOverDue(GlobalVariables.Subdomains), Cron.Daily);
            //RecurringJob.TriggerJob("update-sprint-status");

            //Update metrics
            RecurringJob.RemoveIfExists("update-project-metrics-view");
            RecurringJob.AddOrUpdate("update-project-metrics-view", () => _backGroundServices.UpdateProjectMetricsViews(GlobalVariables.Subdomains), Cron.Daily);
            RecurringJob.TriggerJob("update-project-metrics-view");

            // Process product updates from rabbitmq messages
            //RecurringJob.RemoveIfExists("publish-product-update");
            //RecurringJob.AddOrUpdate("publish-product-update", () => _productUpdateReceiverService.ReceiveMessage(), Cron.Hourly());
            //RecurringJob.TriggerJob("publish-product-update");

            //Activity logs 
            RecurringJob.RemoveIfExists("update-activity-view");
            RecurringJob.AddOrUpdate("update-activity-view", () => _activityBackgroundService.UpdateActivityView(GlobalVariables.Subdomains), Cron.Daily());
            RecurringJob.TriggerJob("update-activity-view");

            RecurringJob.RemoveIfExists("delete-user-activities");
            RecurringJob.AddOrUpdate("delete-user-activities", () => _activityBackgroundServiceMain.DeleteUserActivities(GlobalVariables.Subdomains), Cron.Daily());
            RecurringJob.TriggerJob("delete-user-activities");

            RecurringJob.RemoveIfExists("remove-activity-view");
            RecurringJob.AddOrUpdate("remove-activity-view", () => _activityBackgroundService.DeleteDataOlderThanSixMonths(), Cron.Monthly());
            RecurringJob.TriggerJob("remove-activity-view");

            // Admin Console
            RecurringJob.RemoveIfExists("unsuspend-employees");
            RecurringJob.AddOrUpdate("unsuspend-employees", () => _adminConsoleBackgroungService.UnSuspendDueEmplyees(GlobalVariables.Subdomains), Cron.Hourly());
            RecurringJob.TriggerJob("unsuspend-employees");

            //Reschedule meetings
            RecurringJob.RemoveIfExists("reschedule-meeting");
            RecurringJob.AddOrUpdate("reschedule-meeting", () => _calenderBackGroundService.ResheduleMeetingsThatDidntHappen(GlobalVariables.Subdomains), Cron.Hourly());
            RecurringJob.TriggerJob("reschedule-meeting");

            //Daily Activity score
            RecurringJob.RemoveIfExists("update-activity-score");
            RecurringJob.AddOrUpdate("update-activity-score", () => _backGroundServices.UpdateActivityScoreTable(GlobalVariables.Subdomains), "0 23 * * *");
            RecurringJob.TriggerJob("update-activity-score");

            //// Phone Number Maintenance Charges - Monthly on 1st of each month at 2 AM
            //RecurringJob.RemoveIfExists("process-monthly-maintenance-charges");
            //RecurringJob.AddOrUpdate("process-monthly-maintenance-charges", () => _phoneNumberMaintenanceService.ProcessMonthlyMaintenanceCharges(GlobalVariables.Subdomains), "0 2 1 * *");
            //RecurringJob.TriggerJob("process-monthly-maintenance-charges");

            //// Phone Number Maintenance Charge Retries - Daily at 3 AM
            //RecurringJob.RemoveIfExists("retry-maintenance-charges");
            //RecurringJob.AddOrUpdate("retry-maintenance-charges", () => _phoneNumberMaintenanceService.ProcessMaintenanceChargeRetries(GlobalVariables.Subdomains), "0 3 * * *");
            //RecurringJob.TriggerJob("retry-maintenance-charges");
        }

        public void ScheduleOneTimeJobs()
        {
            // Update project view 
            BackgroundJob.Enqueue(() => _backGroundServices.UpdateTenantProjectView(GlobalVariables.Subdomains));
        }
    }
}
