﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.ViewModel
{
    public class ProposeNewDateAndTimeDto
    {
        public DateTime NewProposedStartDateAndTime { get; set; }
        public DateTime NewProposedEndDateAndTime { get; set; }
        
        [Required(ErrorMessage = "Meeting Id is required")]
        public string MeetingId { get; set; }
        public string RequesterEmail { get; set; }
        public string Reason { get; set; }
    }
}
