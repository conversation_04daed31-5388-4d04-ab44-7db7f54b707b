﻿using Jobid.App.Helpers.Utils;
using OtpNet;
using QRCoder;
using SkiaSharp;
using System;
using System.IO;
using System.Net.Http;

public static class TwoFactorHelper
{
    public static string GenerateSecretKey()
    {
        var key = KeyGeneration.GenerateRandomKey(20);
        return Base32Encoding.ToString(key);
    }

    public static string GenerateQrCodeImage(string email, string secretKey, string issuer, string logoUrl = null)
    {
        // Generate the OTP URI to be used in the QR code
        string uri = $"otpauth://totp/{issuer}:{email}?secret={secretKey}&issuer={issuer}&digits=6";

        using var qrGenerator = new QRCodeGenerator();
        var qrCodeData = qrGenerator.CreateQrCode(uri, QRCodeGenerator.ECCLevel.Q);

        var qrCode = new PipelineMatrix(qrCodeData);
        var matrix = qrCode.Matrix;

        int moduleSize = 20;
        int margin = moduleSize * 2;
        int size = matrix.GetLength(0) * moduleSize + margin * 2;

        using var bitmap = new SKBitmap(size, size);
        using var canvas = new SKCanvas(bitmap);

        canvas.Clear(SKColors.White);

        // Create paint object for QR code modules
        using var paint = new SKPaint
        {
            Color = SKColors.Black,
            IsAntialias = true
        };

        // Draw QR code modules
        for (int x = 0; x < matrix.GetLength(0); x++)
        {
            for (int y = 0; y < matrix.GetLength(1); y++)
            {
                if (matrix[x, y])
                {
                    float xPos = x * moduleSize + margin;
                    float yPos = y * moduleSize + margin;
                    canvas.DrawRect(xPos, yPos, moduleSize, moduleSize, paint);
                }
            }
        }

        // Add company logo at the center if available
        if (!string.IsNullOrEmpty(logoUrl))
        {
            try
            {
                // Download the logo image
                using var httpClient = new HttpClient();
                var logoBytes = httpClient.GetByteArrayAsync(logoUrl).GetAwaiter().GetResult();

                using var logoStream = new MemoryStream(logoBytes);
                using var logoData = SKData.Create(logoStream);
                using var logoImage = SKImage.FromEncodedData(logoData);

                if (logoImage != null)
                {
                    // Calculate logo size (20% of QR code size)
                    int logoSize = size / 5;

                    // Calculate center position
                    float centerX = size / 2 - logoSize / 2;
                    float centerY = size / 2 - logoSize / 2;

                    // Create a white background circle
                    using var bgPaint = new SKPaint
                    {
                        Color = SKColors.White,
                        IsAntialias = true
                    };

                    // Draw white background circle
                    float circleRadius = logoSize / 2 + (logoSize * 0.1f);
                    canvas.DrawCircle(size / 2, size / 2, circleRadius, bgPaint);

                    // Create a circular clip path for the logo
                    using var clipPath = new SKPath();
                    clipPath.AddCircle(size / 2, size / 2, logoSize / 2);

                    // Save the canvas state before applying the clip
                    canvas.Save();
                    canvas.ClipPath(clipPath);

                    // Create a high-quality paint for the logo
                    using var logoPaint = new SKPaint
                    {
                        FilterQuality = SKFilterQuality.High,
                        IsAntialias = true
                    };

                    // Draw the logo within the circular clip
                    var destRect = new SKRect(centerX, centerY, centerX + logoSize, centerY + logoSize);
                    canvas.DrawImage(logoImage, destRect, logoPaint);

                    // Restore the canvas state
                    canvas.Restore();
                }
            }
            catch (Exception)
            {
                // If there's any error loading the logo, just continue without it
            }
        }

        // Convert to PNG bytes
        using var image = SKImage.FromBitmap(bitmap);
        using var data = image.Encode(SKEncodedImageFormat.Png, 100);
        var bytes = data.ToArray();

        // Convert to base64 string
        var base64String = Convert.ToBase64String(bytes);
        return $"data:image/png;base64,{base64String}";
    }

    public static bool ValidateCode(string secretKey, string code)
    {
        var bytes = Base32Encoding.ToBytes(secretKey);
        var totp = new Totp(bytes);
        return totp.VerifyTotp(code, out _, new VerificationWindow(previous: 1, future: 1));
    }
}
