﻿using System;
using AutoMapper;
using Jobid.App.ActivityLog.Model;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Dto.Organogram;
using Jobid.App.AdminConsole.Models;
using Jobid.App.AdminConsole.Models.Calls;
using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Subscription.Models;
using Jobid.App.Subscription.ViewModels;
using Jobid.App.Tenant.ViewModel;

namespace Jobid.App.Helpers.Utils
{
    public class AutoMapper : Profile
    {
        public AutoMapper()
        {
            CreateMap<AddSprintDto, SprintProject>()
                .ForMember(dest => dest.Duration, opt => opt.MapFrom(src =>
                        Utility.DateDurationCalculator((int)(src.EndDate - src.StartDate).TotalDays)));

            CreateMap<CalenderVm, CalenderMeeting>()
                .ForMember(dest => dest.NotifyMembersIn, opt => opt.MapFrom(src => src.NotifyMeInMinutes))
                .ReverseMap();

            CreateMap<Tenant.Model.Tenant, TenantDetailsVM>()
                .ForMember(dest => dest.Industry, opt => opt.MapFrom(src => src.Industry.ToString()));

            // Add mapping for deleted tenants
            CreateMap<Tenant.Model.Tenant, Tenant.Model.DeletedTenant>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.OriginalTenantId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.OriginalDateCreated, opt => opt.MapFrom(src => src.DateCreated))
                .ForMember(dest => dest.DeletedAt, opt => opt.Ignore())
                .ForMember(dest => dest.DeletedBy, opt => opt.Ignore())
                .ForMember(dest => dest.DeletionReason, opt => opt.Ignore())
                .ForMember(dest => dest.TotalUsers, opt => opt.Ignore())
                .ForMember(dest => dest.ActiveSubscriptions, opt => opt.Ignore())
                .ForMember(dest => dest.LastMigrationDate, opt => opt.Ignore())
                .ForMember(dest => dest.DeletionNotes, opt => opt.Ignore());

            CreateMap<Tenant.Model.DeletedTenant, TenantDetailsVM>()
                .ForMember(dest => dest.Industry, opt => opt.MapFrom(src => src.Industry.ToString()));

            CreateMap<Tenant.Model.DeletedTenant, DeletedTenantVM>()
                .ForMember(dest => dest.Industry, opt => opt.MapFrom(src => src.Industry.ToString()));

            CreateMap<CallTranscription, CallTransactionDto>()
                .ReverseMap();

            CreateMap<AddOrUpdateLocalizationDto, Localization>().ReverseMap();
            CreateMap<PasswordPolicyDto, PasswordPolicy>().ReverseMap();
            CreateMap<TwoFactorSettingsDto, TwoFactorSetting>().ReverseMap();

            CreateMap<PersonalScheduleDto, PersonalSchedule>().ReverseMap();
            CreateMap<CustomFrequencyDto, CustomFrequency>().ReverseMap();
            CreateMap<ExternalMeetingDto, ExternalMeeting>().ReverseMap();
            CreateMap<ExternalMeetingQuestionDto, ExternalMeetingQuestion>().ReverseMap();
            CreateMap<SubscriptionHistory, Subscription.Models.Subscription>().ReverseMap();
            CreateMap<LogAttachment, LogAttachmentDto>().ReverseMap();
            CreateMap<User, UserDetails>().ReverseMap();
            CreateMap<EnterprizeSubscriptionPayment, EnterprizeSubscriptionPaymentDto>().ReverseMap();

            CreateMap<BookExternalMeetingDto, BookedExternalMeeting>()
                .ForMember(dest => dest.GuestEmails, opt => opt.MapFrom(src => string.Join(",", src.GuestEmails)))
                .ReverseMap();

            CreateMap<TodoCommentDto, TodoComments>().ReverseMap();
            CreateMap<ActivityRequestedPermisssionsDto, ActivityRequestedPermisssions>().ReverseMap()
                .ForMember(dest => dest.EventCategories, options => options.MapFrom(src => string.Join("-", src.EventCategories)));
            CreateMap<JobProjectSettingsDto, JobProjectSettings>().ReverseMap();
            CreateMap<Subscription.Models.BillingAddress, BillingAddressDto>().ReverseMap();
            CreateMap<EnterPriseOptionsDto, EnterpriseSubscription>()
                .ForMember(dest => dest.SubscriptionId, opt => opt.MapFrom(src => Guid.Parse(src.SubscriptionId)))
                .ReverseMap();
            CreateMap<AddFeedbackReviewsAndRatingsDto, FeedbackReviewsAndRatings>().ReverseMap()
                .ForMember(dest => dest.Application, opt => opt.MapFrom(src => src.Application.ToString()));
            CreateMap<TodoCustomFrequencyDto, TodoCustomFrequencyDto>().ReverseMap();

            // Billing Information mappings
            CreateMap<AddBillingInformationDto, BillingInformation>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.TenantId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Tenant, opt => opt.Ignore());

            CreateMap<UpdateBillingInformationDto, BillingInformation>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.TenantId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Tenant, opt => opt.Ignore());

            CreateMap<BillingInformation, BillingInformationResponseDto>();

            CreateMap<CreateParentCompanyDto, OrganogramCompany>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyType, opt => opt.Ignore())
                .ForMember(dest => dest.EntityType, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.Subdomain, opt => opt.Ignore());

            CreateMap<CreateSubsidiaryCompanyDto, OrganogramCompany>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyType, opt => opt.Ignore())
                .ForMember(dest => dest.EntityType, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.Subdomain, opt => opt.Ignore());

            CreateMap<UpdateOrganogramCompanyDto, OrganogramCompany>()
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyType, opt => opt.Ignore())
                .ForMember(dest => dest.EntityType, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.Subdomain, opt => opt.Ignore());

            CreateMap<OrganogramCompany, OrganogramCompanyResponseDto>()
                .ForMember(dest => dest.Index, opt => opt.MapFrom(src => src.index));

            // Department Mappings
            CreateMap<CreateDepartmentDto, Department>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore());

            CreateMap<UpdateDepartmentDto, Department>()
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore());

            CreateMap<Department, DepartmentResponseDto>()
                .ForMember(dest => dest.Index, opt => opt.MapFrom(src => src.index));

            // Position Mappings
            CreateMap<CreateEmployeePositionDto, EmployeePosition>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore());

            CreateMap<UpdateEmployeePositionDto, EmployeePosition>()
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.DepartmentId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore());

            CreateMap<EmployeePosition, EmployeePositionResponseDto>()
                .ForMember(dest => dest.Index, opt => opt.MapFrom(src => src.index));

            // Individual Mappings
            CreateMap<CreateIndividualDto, Individual>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore());

            CreateMap<UpdateIndividualDto, Individual>()
                .ForMember(dest => dest.index, opt => opt.Ignore())
                .ForMember(dest => dest.DepartmentId, opt => opt.Ignore())
                .ForMember(dest => dest.CompanyId, opt => opt.Ignore())
                .ForMember(dest => dest.PositionId, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.BelongsTo, opt => opt.Ignore());

            CreateMap<Individual, IndividualResponseDto>()
                .ForMember(dest => dest.Index, opt => opt.MapFrom(src => src.index))
                .ForMember(dest => dest.BranchColor, opt => opt.MapFrom(src => src.BranchColor));

            // Hierarchy DTO Mappings
            CreateMap<OrganogramCompany, ParentCompanyDto>();
            CreateMap<OrganogramCompany, SubsidiaryCompanyDto>();
            CreateMap<Department, DepartmentHierarchyDto>()
                .ForMember(dest => dest.Positions, opt => opt.Ignore());
            CreateMap<EmployeePosition, PositionHierarchyDto>()
                .ForMember(dest => dest.Individuals, opt => opt.Ignore());
            CreateMap<Individual, IndividualHierarchyDto>()
                .ForMember(dest => dest.BranchColor, opt => opt.MapFrom(src => src.BranchColor))
                .ForMember(dest => dest.IsHeadOfDepartment, opt => opt.MapFrom(src => src.IsHeadofDepartment));

            //Basic Info mappings
            CreateMap<BasicInfo, BasicInfoResponseDto>();

            CreateMap<UpdateBasicInfoDto, BasicInfo>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Subdomain, opt => opt.Ignore());

            CreateMap<CreateBasicInfoDto, BasicInfo>()
               .ForMember(dest => dest.Id, opt => opt.Ignore())
               .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
               .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
               .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
               .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
               .ForMember(dest => dest.Subdomain, opt => opt.Ignore());

            //Emergency Info  mappings
            CreateMap<EmergencyInfo, EmergencyContactResponseDto>();

            CreateMap<UpdateEmergencyContactDto, EmergencyInfo>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Subdomain, opt => opt.Ignore());

            CreateMap<CreateEmergencyContactDto, EmergencyInfo>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedOn, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.Subdomain, opt => opt.Ignore());
        }
    }
}
