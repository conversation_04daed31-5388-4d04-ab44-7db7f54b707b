﻿using Hangfire;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.AdminConsole.Services
{
    public class BackgroundService : IBackgroungService
    {
        public BackgroundService()
        {
        }

        #region UnSuspend Due Employees
        /// <summary>
        /// UnSuspend Due Employees
        /// </summary>
        /// <param name="subdomains"></param>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("admin")]
        public void UnSuspendDueEmplyees(List<string> subdomains)
        {
            if (subdomains == null) return;
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var suspendedEmployees = context.SuspendedEmployees
                .Where(s => s.IsIndefinite == false && s.IsSuspended == true && s.EndDate.Date <= DateTime.UtcNow.Date).ToList();

                foreach (var suspended in suspendedEmployees)
                {
                    suspended.UnSuspendedOn = DateTime.UtcNow;
                    suspended.IsSuspended = false;
                    context.SuspendedEmployees.Update(suspended);

                    var profile = context.UserProfiles.FirstOrDefault(u => u.Id == suspended.UserProfileId);
                    profile.IsSuspended = false;
                    context.UserProfiles.Update(profile);

                    var userAppPermissions = context.AppPermissions.Where(u => u.UserId == profile.UserId)
                            .ToList();
                    userAppPermissions.ForEach(u => u.IsSuspended = false);
                    context.AppPermissions.UpdateRange(userAppPermissions);
                }

                context.SaveChanges();
            }
        }
        #endregion
    }
}
