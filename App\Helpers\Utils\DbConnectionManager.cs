using Jobid.App.Helpers.Context;
using Jobid.App.Tenant.SchemaTenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Utils
{
    /// <summary>
    /// Manages database connections to ensure proper disposal and connection pool management
    /// </summary>
    public class DbConnectionManager
    {
        private readonly ILogger<DbConnectionManager> _logger;

        public DbConnectionManager(ILogger<DbConnectionManager> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Executes an action using a DbContext that is properly disposed afterward
        /// </summary>
        /// <typeparam name="TResult">The type of result to return</typeparam>
        /// <param name="connectionString">The connection string to use</param>
        /// <param name="schema">The schema to use (optional)</param>
        /// <param name="action">The action to execute with the context</param>
        /// <returns>The result of the action</returns>
        public TResult ExecuteWithContext<TResult>(string connectionString, DbContextSchema schema, Func<JobProDbContext, TResult> action)
        {
            using var context = schema != null 
                ? new JobProDbContext(connectionString, schema) 
                : new JobProDbContext(connectionString);
            
            try
            {
                return action(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing database operation");
                throw;
            }
        }

        /// <summary>
        /// Executes an async action using a DbContext that is properly disposed afterward
        /// </summary>
        /// <typeparam name="TResult">The type of result to return</typeparam>
        /// <param name="connectionString">The connection string to use</param>
        /// <param name="schema">The schema to use (optional)</param>
        /// <param name="action">The async action to execute with the context</param>
        /// <returns>The result of the action</returns>
        public async Task<TResult> ExecuteWithContextAsync<TResult>(string connectionString, DbContextSchema schema, Func<JobProDbContext, Task<TResult>> action)
        {
            await using var context = schema != null 
                ? new JobProDbContext(connectionString, schema) 
                : new JobProDbContext(connectionString);
            
            try
            {
                return await action(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing async database operation");
                throw;
            }
        }

        /// <summary>
        /// Executes an async action using a DbContext that is properly disposed afterward
        /// </summary>
        /// <param name="connectionString">The connection string to use</param>
        /// <param name="schema">The schema to use (optional)</param>
        /// <param name="action">The async action to execute with the context</param>
        public async Task ExecuteWithContextAsync(string connectionString, DbContextSchema schema, Func<JobProDbContext, Task> action)
        {
            await using var context = schema != null 
                ? new JobProDbContext(connectionString, schema) 
                : new JobProDbContext(connectionString);
            
            try
            {
                await action(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing async database operation with no result");
                throw;
            }
        }
    }
}
