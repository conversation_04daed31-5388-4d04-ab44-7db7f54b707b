﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Grpc.Core;
using Jobid.App.Helpers.Contract;
using Jobid.App.JobProject.ViewModel;
using Serilog;
using Status = Grpc.Core.Status;

namespace Jobid.App.JobProject.Services.Grpc.Server
{
    public class ProjectGrpcService : GrpcProjectService.GrpcProjectServiceBase
    {
        private readonly IUnitofwork _unitofwork;
        private readonly ILogger _logger = Log.ForContext<ProjectGrpcService>();

        public ProjectGrpcService(IUnitofwork unitofwork)
        {
            _unitofwork = unitofwork ?? throw new ArgumentNullException(nameof(unitofwork));
        }

        public override async Task<ProjectCountStatisticResponse> GetProjectCount(Empty request, ServerCallContext context)
        {
            _logger.Information("Getting project count statistics");
            var projectCountStatisticDtos = await _unitofwork.ProjectService.GetProjectCount();
            var response = new ProjectCountStatisticResponse();
            response.ProjectCount.AddRange(projectCountStatisticDtos.Select(item => new ProjectCount() { Month = item.Month, Count = item.Count }));
            return response;
        }

        public override async Task<TopPerformingCompanies> GetTopPerformingCompaniesPercentage(Empty request, ServerCallContext context)
        {
            _logger.Information("Getting top performing companies");
            var performingCompanies = await _unitofwork.ProjectService.GetTopPerformingCompaniesPercentage();
            var response = new TopPerformingCompanies();
            response.TopPerformers
                .AddRange(performingCompanies
                    .Select(company => new TopPerformingCompany()
                    {
                        CompanyName = company.CompanyName,
                        PercentageIncrement = company.PercentageIncrement,
                        TotalProjectCount = company.TotalProjectCount
                    }));
            return response;
        }

        public override async Task<PagedProjectCompanyDetail> GetProjectCompanyDetail(GetProjectCompanyDetailRequest request, ServerCallContext context)
        {
            _logger.Information("Getting top performing companies");
            var enumValue = ParseEnum<CompanyProjectFilter>(request.Filter, "Error parsing filter");
            var projectCompanies = await _unitofwork.ProjectService.GetProjectCompanyDetail(enumValue, request.PageSize, request.PageNumber);
            return new PagedProjectCompanyDetail
            {
                Items = {
                    projectCompanies.Items.Select(company => new ProjectCompanyDetail {
                        CompanyName = company.CompanyName,
                        CreationDate = company.CreationDate == null ? string.Empty : company.CreationDate.ToString(),
                        ProjectCount = company.ProjectCount,
                        StaffSize = company.StaffSize,
                        TotalProjectCount = company.TotalProjectCount
                    })
                },
                TotalSize = projectCompanies.TotalSize,
                PageNumber = projectCompanies.PageNumber,
                PageSize = projectCompanies.PageSize
            };
        }

        private TEnum ParseEnum<TEnum>(string value, string errorMessage) where TEnum : struct
        {
            if (!Enum.TryParse<TEnum>(value, out var enumValue))
            {
                _logger.Error(errorMessage);
                throw new RpcException(new Status(StatusCode.InvalidArgument, errorMessage));
            }
            return enumValue;
        }
    }
}