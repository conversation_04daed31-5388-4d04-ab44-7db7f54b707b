#region Using Statements
using AutoMapper;
using Hangfire;
using Jobid.App.ActivityLog.contract;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Notification.Hubs;
using Jobid.App.Notification.Models;
using Jobid.App.Notification.ViewModel;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using static Jobid.App.JobProject.Enums.Enums;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;
using static Jobid.App.Helpers.Utils.Extensions;
using RestSharp;
using Jobid.App.Calender.ViewModel;
using Path = System.IO.Path;
using Jobid.App.JobProject.Services.Contract;
using System.Text.RegularExpressions;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Tenant.Model;
#endregion

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class TodoServices : ITodoServices
    {
        #region Properties
        private JobProDbContext Db;
        public JobProDbContext Dbo;
        private readonly IEmailService _emailService;
        private readonly ITenantService _tenantService;
        private readonly ICompanyUserInvite _companyUserInvite;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IWebHostEnvironment _environment;
        private readonly IMapper _mapper;
        private readonly IHubContext<NotificationHub, INotificationClient> _hubContext;
        private readonly IActivityService _activityService;
        private readonly IAdminService _adminService;
        private readonly IRedisCacheService _redisCacheService;
        private readonly ILogService _logService;
        private readonly IApiCallService _apiCallService;
        private readonly string _redisKey;
        private readonly string _conString;

        public TodoServices(JobProDbContext _db, JobProDbContext publicSchemaContext, IEmailService emailService, ITenantService tenantService, ICompanyUserInvite companyUserInvite, IAWSS3Sevices aWSS3Sevices, IWebHostEnvironment environment, IHubContext<NotificationHub, INotificationClient> hubContext, IMapper mapper, IActivityService activityService, IAdminService adminService, IRedisCacheService redisCacheService, ILogService logService, IApiCallService apiCallService)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _emailService = emailService;
            _tenantService = tenantService;
            _companyUserInvite = companyUserInvite;
            _aWSS3Sevices = aWSS3Sevices;
            _environment = environment;
            _mapper = mapper;
            _hubContext = hubContext;
            _activityService = activityService;
            _adminService = adminService;
            _redisCacheService = redisCacheService;
            _logService = logService;
            _apiCallService = apiCallService;
            _redisKey = $"{GlobalVariables.Subdomain}-todo";
            _conString = GlobalVariables.ConnectionString;
        }
        #endregion

        #region Create Todo
        /// <summary>
        /// Add todo to the project/sprint
        /// </summary>
        /// <param name="model"></param>
        /// <param name="project"></param>
        /// <returns></returns>
        public async Task<List<ProjectMgmt_Todo>> AddProjectMgmt_Todo(CreateTodoDto model, ProjectMgmt_Project project = null)
        {
            if (model.ExternalTeamMembers.Any() && model.ExternalTeamMembers[0] is not null && !model.CreatedByAdmin)
                throw new DirtyFormException("Todo cannot be assigned to external team members when creating a todo. Only admins can perform this action. Please inform an admin to do this or invite the users to the company workspace");

            // Check if tagIds privided is not null or empty and check if the tagIds exists in the database
            if (model.TagIds is not null && model.TagIds.Any() && !string.IsNullOrEmpty(model.TagIds[0]))
            {
                foreach (var tagId in model.TagIds)
                {
                    var tag = await Db.ProjectTag.FirstOrDefaultAsync(x => x.Id.ToString() == tagId);
                    if (tag == null)
                        throw new RecordNotFoundException($"Tag not found - {tagId}");
                }
            }

            // Check if its a bulk todo request and create a context usong the subdomain
            if (model.IsBulkTodoUpload)
            {
                var pubContext = new JobProDbContext(_conString);
                var context = new JobProDbContext(_conString, new DbContextSchema(model.SubDomain));
                Db = context;
                Dbo = pubContext;
                model.Application = Applications.Joble;
                // model.Priority = ProjectManagementPriority.Low;
            }

            // Form validations
            ProjectMgmt_Project sprintProject;
            PayloadValidations(model, project, out sprintProject);
            if (sprintProject != null)
                project = sprintProject;

            var tenant = await _tenantService.GetTenantBySubdomain(model.SubDomain);
            var tenantId = tenant.Id;

            // Get the invitee name
            var invitee = Dbo.Users.Where(x => x.Id == model.UserId.ToString())
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            if (!string.IsNullOrEmpty(model.TeamId))
            {
                var teamMemberIds = await Db.TeamMembers.Where(x => x.Id.ToString() == model.TeamId)
                    .Select(teamMember => teamMember.UserId).ToListAsync();
                model.MemberIds.AddRange(teamMemberIds);
                model.MemberIds = model.MemberIds.Distinct().ToList();
            }

            var totalMinutes = (int)(model.EndTime - model.StartDateAndTime).TotalMinutes;
            (int hours, int minutes) = ConvertMinutesToHoursAndMinutes(totalMinutes);
            var duration = string.IsNullOrEmpty(model?.TimeEstimate) || model?.TimeEstimate == "undefined" ? $"{hours}h {minutes}m" : model?.TimeEstimate;

            // Generate random todo id
            Random rand = new Random();
            var TodoId = "RN-" + rand.Next(100, 1000).ToString();
            var tempCreationId = rand.Next(100, 10000000).ToString();

            ProjectMgmt_Todo todo = new ProjectMgmt_Todo
            {
                CreatedDate = GetAdjustedDateTimeBasedOnTZNow(),
                TodoId = TodoId,
                TodoName = model.TodoSummary,
                Priority = model.Priority,
                TodoStatus = model.DueDate <= DateTime.UtcNow ? Helpers.Models.TodoStatus.OverDue.ToString() : model.Status.ToString(),
                TodoDescription = model.TodoDescription,
                TodoSummary = model.TodoSummary,
                Duration = duration,
                StartDateAndTime = model.StartDateAndTime,
                EndTime = model.EndTime, // This is for both endtime and end date
                DueDate = model.DueDate,
                CreatedBy = model.UserId.ToString(),
                TimeSpent = "0.00:00:00",
                ActualTimeSpent = "0.00:00:00",
                Application = model.Application,
                ClientName = model.ClientName,
                KpiReferenceId = model.KpiReferenceId,
                CompanyReferenceId = model.CompanyReferenceId,
                LeadReferenceId = model.LeadReferenceId,
                DealReferenceId = model.DealReferenceId,
                ContactReferenceId = model.ContactReferenceId,
                IsMeasurable = model.IsMeasurable,
                TempCreationId = tempCreationId,
                LastUpdate = GetAdjustedDateTimeBasedOnTZNow(),
                CreatedByAI = model.CreatedByAI
            };

            if (project != null)
            {
                if (project.IsBillable)
                    todo.IsBillable = true;
            }

            if (model.TodoCustomFrequency is not null)
            {
                if (model.TodoCustomFrequency.RepeatCount is not null && model.TodoCustomFrequency.RepeatOn is not null)
                    todo.HasCustomFrequency = true;
            }

            // Calculate time left
            todo.TimeLeft = (model.EndTime - model.StartDateAndTime).ToString();

            if (!string.IsNullOrEmpty(model.SprintId))
            {
                var sprintExists = await Db.SprintProjects.AnyAsync(x => x.Id.ToString() == model.SprintId && x.CreatedBy == model.UserId);
                TodoId = await GenerateTodoId(project.Name, model.SprintId);
                todo.TodoId = TodoId;
                todo.SprintProjectId = new Guid(model.SprintId);
                todo.ProjectMgmt_ProjectId = project.ProjectId;
            }

            if (project is not null && string.IsNullOrEmpty(model.SprintId))
            {
                TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                todo.ProjectMgmt_ProjectId = project.ProjectId;
            }

            var todoToDuplicate = todo;
            await Db.ProjectMgmt_Todo.AddAsync(todo);
            var todoUser = new ProjectMgmt_TodoUser();
            var todos = new List<ProjectMgmt_Todo>();
            var invitedMembers = new List<string>();

            var templatePath = string.Empty;
            var template = string.Empty;
            var count = 0;
            ProjectMgmt_TodoUser? proj = null;
            if (model.MemberIds.Any() && model.MemberIds[0] is not null)
            {
                foreach (var member in model.MemberIds)
                {
                    //var optimalStartTime = await CalculateOptimalTodoStartDate(member, todo);
                    invitedMembers.Add(member);
                    var newTodo = todoToDuplicate;
                    if (count > 0)
                    {
                        newTodo.Id = Guid.NewGuid();
                        //newTodo.StartDateAndTime = optimalStartTime;
                        //newTodo.EndTime = optimalStartTime.AddHours(ConvertTodoDurationToHours(todo.Duration));
                        if (!string.IsNullOrEmpty(model.SprintId))
                            newTodo.TodoId = await GenerateTodoId(project.Name, model.SprintId);
                        else if (project is not null && string.IsNullOrEmpty(model.SprintId))
                            newTodo.TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                        else
                            newTodo.TodoId = "RN-" + rand.Next(100, 1000).ToString();
                        //var todoSequences = await CreateTodoTimeSequence(member, newTodo);
                        //newTodo.TodoTimeSequence = todoSequences;                        
                        await Db.ProjectMgmt_Todo.AddAsync(newTodo);
                    }
                    else
                    {
                        //var todoSequences = await CreateTodoTimeSequence(member, newTodo);
                        //newTodo.TodoTimeSequence = todoSequences;
                        //newTodo.StartDateAndTime = optimalStartTime;
                        //newTodo.EndTime = optimalStartTime.AddHours(ConvertTodoDurationToHours(todo.Duration));
                        await Db.ProjectMgmt_Todo.AddAsync(newTodo);
                    }

                    var res = await Db.SaveChangesAsync();
                    count++;
                }
            }

            // Send mail to external team member
            if (model.ExternalTeamMembers.Any() && model.ExternalTeamMembers[0] is not null)
            {
                foreach (var memberEmail in model.ExternalTeamMembers)
                {
                    invitedMembers.Add(memberEmail);

                    // Add an invite to the database
                    var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                    {
                        Email = memberEmail,
                        Application = Applications.Joble,
                    }, tenantId.ToString());

                    var newTodo = todoToDuplicate;
                    if (count > 0)
                    {
                        newTodo.Id = Guid.NewGuid();
                        if (!string.IsNullOrEmpty(model.SprintId))
                            newTodo.TodoId = await GenerateTodoId(project.Name, model.SprintId);
                        else if (project is not null && string.IsNullOrEmpty(model.SprintId))
                            newTodo.TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                        else
                            newTodo.TodoId = "RN-" + rand.Next(100, 1000).ToString();

                        await Db.ProjectMgmt_Todo.AddAsync(newTodo);
                    }
                    else
                    {
                        await Db.ProjectMgmt_Todo.AddAsync(newTodo);
                    }

                    var res = await Db.SaveChangesAsync();
                    count++;
                }
            }

            // Add and Get added tags
            todos = await Db.ProjectMgmt_Todo.Where(x => x.TempCreationId == tempCreationId).ToListAsync();

            // Add todo users and send out notifications
            if (invitedMembers.Count == todos.Count)
            {
                for (var i = 0; i < todos.Count; i++)
                {
                    if (invitedMembers[i].Contains("@")) //External members
                    {
                        todoUser = new ProjectMgmt_TodoUser()
                        {
                            ProjectMgmt_TodoId = todos[i].Id,
                            ExternalMemberEmail = invitedMembers[i]
                        };
                        await Db.projectMgmt_TodoUsers.AddAsync(todoUser);
                        todos[i].ProjectMgmt_TodoUsers = new List<ProjectMgmt_TodoUser> { todoUser };
                        templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo-invited-email.html");
                        SendOutExternalNotification(model.SubDomain, project, tenant.CompanyName, tenantId, invitee, templatePath, invitedMembers[i], todos[i].Id, todos[i].TodoSummary);
                    }
                    else
                    {
                        todoUser = new ProjectMgmt_TodoUser()
                        {
                            ProjectMgmt_TodoId = todos[i].Id,
                            UserId = invitedMembers[i]
                        };
                        await Db.projectMgmt_TodoUsers.AddAsync(todoUser);
                        todos[i].ProjectMgmt_TodoUsers = new List<ProjectMgmt_TodoUser> { todoUser };
                        templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo/todo-internal-invited-email.html");
                        var userProfile = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == invitedMembers[i]);
                        //SendOutNotification(model.SubDomain, project.Name, tenant.CompanyName, invitee, todos[i].Id, templatePath, userProfile.Email, todos[i].TodoSummary);
                        SendInternalTodoNotification(model.SubDomain, project, invitee, todos[i].Id, userProfile.Email, todos[i], userProfile.FirstName + " " + userProfile.LastName);

                        if (invitedMembers[i] == model.UserId.ToString())
                            continue;

                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"You have been assigned to a todo - {todos[i].TodoName} by {invitee}",
                            Event = EventCategory.Todo,
                            EventId = todos[i].Id.ToString(),
                            CreatedBy = model.UserId.ToString()
                        };

                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                            await AddUserNotification(new List<string> { invitedMembers[i] }, Guid.Parse(notificationId));

                        // Todo: Invoke a frontend method using SignalR
                        await _hubContext.Clients.All.RecieveNotification();
                    }
                }
            }

            await Db.SaveChangesAsync();
            await UpdateTodoOrderForSprint(model.SprintId, todos);

            var tags = new List<ProjectTag>() { };
            var tagList = new List<TagId>() { };
            if (model.TagIds.Any() && !string.IsNullOrEmpty(model.TagIds[0]))
            {
                foreach (var addedTodo in todos)
                {
                    model.TagIds.ForEach
                    (
                        async x =>
                        {
                            var todoTag = Db.ProjectTag.FirstOrDefault(p => p.Id.ToString() == x);
                            if (todoTag != null)
                            {
                                tags.Add(todoTag);
                            }
                        }
                    );

                    var Tags = model.TagIds.Select(x => new TagId()
                    {
                        PrjectTagId = x,
                        TodoId = addedTodo.Id.ToString(),
                    }).ToList();

                    tagList.AddRange(Tags);
                }
            }
            await Db.TagId.AddRangeAsync(tagList);
            todo.ProjectTags = tags;

            // Add custome frequency for the todos based on the number of users it was assigned to
            if (todo.HasCustomFrequency)
            {
                var customTodoFreqList = new List<TodoCustomFrequency>();
                foreach (var t in todos)
                {
                    var customTodoFrequency = _mapper.Map<TodoCustomFrequency>(model.TodoCustomFrequency);
                    customTodoFrequency.ProjectMgmt_TodoId = t.Id;
                    customTodoFreqList.Add(customTodoFrequency);
                    t.TodoCustomFrequency = customTodoFrequency;
                }
                await Db.TodoCustomFrequency.AddRangeAsync(customTodoFreqList);
            }

            int result = await Db.SaveChangesAsync();

            // Log the activity
            await LogActivity(model.UserId.ToString(), EventCategory.Todo, "Todo created", "Todo created", todo.Id.ToString(), model.SubDomain, model.IsBulkTodoUpload);

            // Upload file to S3
            if (model.UploadFile.Any() && model.UploadFile.Count > 0)
                await UploadTodoFiles(model.UploadFile, todo.Id);

            // Delete records from redis cache for assigned internal users
            model.MemberIds.Add(model.UserId.ToString());
            await DeleteDataFromRedis(model.MemberIds);
            if (!todos.Any())
                todos.Add(todo);

            return todos;
        }
        #endregion

        #region Add Bulk Todo
        /// <summary>
        /// Add Bulk Todo to the DB
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> AddBulkTodo(BulkTodoVM model)
        {
            // Get the invitee name
            var todoOwner = Dbo.Users.Where(x => x.Id == model.UserId.ToString())
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (todoOwner == null)
                throw new RecordNotFoundException("User not found");

            var todosToAdd = new List<CreateTodoDto>();

            foreach (var todoDetails in model.TodoDetails)
            {
                if (todoDetails.StartDateAndTime > todoDetails.DueDate)
                {
                    throw new InvalidOperationException($"Start date ({todoDetails.StartDateAndTime}) cannot be greater than due date ({todoDetails.DueDate})");
                }
                var invitedInternalMembers = new List<string>(); // This is the Ids of employees
                var invitedExternalMembers = new List<string>(); // Emails of external members

                foreach (var item in todoDetails.AssignedTo)
                {
                    // Check if the email is attached to an employee
                    var profile = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == item);
                    if (profile is not null)
                        invitedInternalMembers.Add(profile.UserId);
                    else
                        invitedExternalMembers.Add(item);
                };

                var todo = new CreateTodoDto
                {
                    SubDomain = model.SubDomain,
                    UserId = Guid.Parse(model.UserId),
                    TodoSummary = todoDetails.TodoSummary,
                    TodoDescription = todoDetails.TodoDescription,
                    StartDateAndTime = todoDetails.StartDateAndTime,
                    EndTime = todoDetails.DueDate,
                    DueDate = todoDetails.DueDate,
                    MemberIds = invitedInternalMembers,
                    ExternalTeamMembers = invitedExternalMembers,
                    IsBulkTodoUpload = true,
                    Priority = todoDetails.Priority,
                    TagIds = todoDetails.TagIds,
                    TimeEstimate = todoDetails.TimeEstimate,
                    CreatedByAI = todoDetails.CreatedByAI,
                    Status = todoDetails.Status ?? ProjectManagementStatus.Todo,
                };

                todosToAdd.Add(todo);
            };

            await BulkTodoBackgroundJob(todosToAdd);
            // Background Job: Loop through the todosToAdd and call the add todo method
            //BackgroundJob.Enqueue(() => BulkTodoBackgroundJob(todosToAdd));

            return true;
        }
        #endregion

        #region Private Methods
        private (int, int) ConvertMinutesToHoursAndMinutes(int totalMinutes)
        {
            int hours = totalMinutes / 60;
            int remainingMinutes = totalMinutes % 60;

            return (hours, remainingMinutes);
        }

        private async Task LogActivity(string userId, EventCategory eventCategory, string desc, string summary, string eventId = null, string subdoamin = null, bool bulkTodoUpload = false)
        {
            var canLogActivity = await _activityService.CheckIfUserHasGrantedPermission(userId, eventCategory, subdoamin, bulkTodoUpload);
            if (canLogActivity)
            {
                var currentUser = await Db.UserProfiles.Where(u => u.UserId == userId)
                    .Select(x => x.FirstName + " " + x.LastName)
                    .FirstOrDefaultAsync();
                var activity = new ActivityDto
                {
                    Description = $"{desc} by {currentUser}",
                    ActivitySummary = summary,
                    EventCategory = eventCategory,
                    UserId = userId,
                    By = currentUser,
                    EventId = eventId,
                    Application = Applications.Joble,
                    subdomain = subdoamin,
                    IsBulkTodoUpload = bulkTodoUpload
                };

                await _activityService.CreateLog(activity);
            }
        }

        private async Task UploadTodoFiles(List<IFormFile> files, Guid todoId)
        {
            var todoFiles = new List<ProjectFile>();
            foreach (var file in files)
            {
                Guid guid = Guid.NewGuid();
                var fileTrimmed = file.FileName.Replace(" ", "");
                var fileName = guid.ToString()
                                        .Replace('-', '0')
                                        .Replace('_', '0')
                                        .ToUpper() + "-" + fileTrimmed;

                var imageUrl = await _aWSS3Sevices.UploadFileAsync(file, fileName);

                var todoFile = new ProjectFile()
                {
                    FileName = fileName,
                    ProjectMgmt_TodoId = todoId
                };
                todoFiles.Add(todoFile);

                if (string.IsNullOrEmpty(imageUrl))
                    throw new FileUploadException("Todo updated successfully but File upload to AWS failed");
            }

            await Db.ProjectFile.AddRangeAsync(todoFiles);
            var res = await Db.SaveChangesAsync();

            if (res <= 0)
                throw new FileUploadException("Todo updated successfully but File storage to the database failed");
        }
        #endregion

        #region ReSchedule Todo
        /// <summary>
        /// Reschedule todo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> RescheduleTodo(ReScheduleTodoDto model)
        {
            var todo = await GetProjectMgmt_Todo(model.TodoId);
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            // Check permisiions
            await CheckPermission(todo.CreatedBy, model.UserId);

            // Map todo to TodoUpdateVm
            var todoUpdateVm = new TodoUpdateVm()
            {
                TodoDescription = todo.TodoDescription,
                Status = Enum.Parse<ProjectManagementStatus>(todo.TodoStatus),
                TodoSummary = todo.TodoSummary,
                Priority = todo.Priority,
                StartDateAndTime = model.StartDateAndTime,
                EndTime = model.EndTime,
                ExistingTodoLink = todo.ExistingTodoLink,
                UserId = new Guid(model.UserId),
                SubDomain = model.Subdomain,
                TodoCustomFrequency = model.TodoCustomFrequency,
                IsMeasurable = todo.IsMeasurable
            };

            var todoMember = await Db.projectMgmt_TodoUsers
                       .Where(x => x.ProjectMgmt_TodoId == model.TodoId).FirstOrDefaultAsync();
            if (todoMember is not null)
            {
                if (todoMember.ExternalMemberEmail != null)
                    todoUpdateVm.ExternalTeamMember = todoMember.ExternalMemberEmail;
                else
                    todoUpdateVm.MemberId = todoMember.UserId;
            }

            // Update todo
            return await UpdateProjectTodo(todoUpdateVm, model.TodoId);
        }
        #endregion

        #region Update Todo
        /// <summary>
        /// Edit Todo
        /// </summary>
        /// <param name="model"></param>
        /// <param name="todoId"></param>
        /// <param name="project"></param>
        /// <returns></returns>
        public async Task<bool> UpdateProjectTodo(TodoUpdateVm model, Guid todoId, ProjectMgmt_Project project = null)
        {
            if (!string.IsNullOrEmpty(model.ExternalTeamMember) && !model.UpdatedByAdmin)
                throw new DirtyFormException("Todo cannot be assigned to external team members when creating a todo. Only admins can perform this action. Please inform an admin to do this or invite the users to the company workspace");

            if (!string.IsNullOrEmpty(model.MemberId) && !string.IsNullOrEmpty(model.ExternalTeamMember))
                throw new DirtyFormException("Todo cannot be assigned to both internal and external team members");

            var todo = await Db.ProjectMgmt_Todo
                .Where(x => x.Id == todoId)
                .FirstOrDefaultAsync();
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            await CheckPermission(todo.CreatedBy, model.LoggedInUserId);

            var tenant = await _tenantService.GetTenantBySubdomain(model.SubDomain);
            var tenantId = tenant.Id;

            // Get the invitee name
            var invitee = Db.UserProfiles.Where(x => x.UserId == model.UserId.ToString())
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            var totalMinutes = (int)(model.EndTime - model.StartDateAndTime).TotalMinutes;
            (int hours, int minutes) = ConvertMinutesToHoursAndMinutes(totalMinutes);
            var duration = string.IsNullOrEmpty(model?.TimeEstimate) || model?.TimeEstimate == "undefined" ? $"{hours}h {minutes}m" : model?.TimeEstimate;

            todo.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
            todo.TodoName = model.TodoSummary;
            todo.TodoDescription = model.TodoDescription;
            todo.TodoStatus = model.Status.ToString();
            todo.Priority = model.Priority;
            todo.TodoDescription = model.TodoDescription;
            todo.StartDateAndTime = model.StartDateAndTime;
            todo.TodoSummary = model.TodoSummary;
            todo.EndTime = model.EndTime;
            todo.Duration = duration;
            todo.DueDate = model.DueDate != null ? model.DueDate : todo.DueDate;
            todo.ExistingTodoLink = model.ExistingTodoLink;
            todo.IsMeasurable = model.IsMeasurable;

            if (model.TodoCustomFrequency?.ProjectMgmt_TodoId is not null)
            {
                todo.HasCustomFrequency = true;
                // Get old record of custome Frequency
                var oldCustomTodoFreq = await Db.TodoCustomFrequency.Where(cus => cus.ProjectMgmt_TodoId == todoId).FirstOrDefaultAsync();
                if (oldCustomTodoFreq != null)
                {
                    oldCustomTodoFreq.EndStatus = model.TodoCustomFrequency.EndStatus.Value;
                    oldCustomTodoFreq.EndsOn = model.TodoCustomFrequency.EndsOn;
                    oldCustomTodoFreq.EndsAfter = model.TodoCustomFrequency.EndsAfter;
                    oldCustomTodoFreq.RepeatEvery = model.TodoCustomFrequency.RepeatEvery;
                    oldCustomTodoFreq.RepeatOn = model.TodoCustomFrequency.RepeatOn;
                    oldCustomTodoFreq.RepeatCount = model.TodoCustomFrequency.RepeatCount.Value;

                    Db.TodoCustomFrequency.Update(oldCustomTodoFreq);
                }
                else
                {
                    var customTodoFrequency = _mapper.Map<TodoCustomFrequency>(model.TodoCustomFrequency);
                    customTodoFrequency.ProjectMgmt_TodoId = todoId;

                    await Db.TodoCustomFrequency.AddAsync(customTodoFrequency);
                }
            }

            if (project != null)
            {
                if (project.IsBillable)
                    todo.IsBillable = true;
            }

            // Calculate time left
            todo.TimeLeft = (model.EndTime - model.StartDateAndTime).ToString();
            Random rand = new Random();
            if (!string.IsNullOrEmpty(model.SprintId))
            {
                if (model.SprintId != todo.SprintProjectId?.ToString())
                {
                    todo.TodoId = await GenerateTodoId(project?.Name, model.SprintId);
                    todo.SprintProjectId = string.IsNullOrEmpty(model.SprintId) ? null : new Guid(model.SprintId);
                    todo.ProjectMgmt_ProjectId = project?.ProjectId;
                }
                else if (project is not null && project.ProjectId == todo.ProjectMgmt_ProjectId.Value)
                {
                    todo.TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                    todo.ProjectMgmt_ProjectId = project.ProjectId;
                }
                else
                {
                    todo.SprintProjectId = null;
                }
            }
            else
            {
                todo.SprintProjectId = null;
                if (project is null)
                {
                    todo.TodoId = "RN-" + rand.Next(100, 1000).ToString();
                    todo.ProjectMgmt_ProjectId = null;
                }
            }


            if (!string.IsNullOrEmpty(model.Comment))
            {
                // Delete existing comments
                var comments = Db.TodoComments.Where(x => x.TodoId == todo.Id).ToList();
                if (comments.Any())
                    Db.TodoComments.RemoveRange(comments);

                // Add new comments
                var comment = new TodoComments()
                {
                    Comment = model.Comment,
                    TodoId = todo.Id,
                    CommentedBy = model.UserId.ToString()
                };
                Db.TodoComments.Add(comment);
            }

            // Add new tags
            if (model.TagIds.Any() && !string.IsNullOrEmpty(model.TagIds[0]))
            {
                // Delete existing tags
                var existingTags = Db.TagId.Where(x => x.TodoId == todoId.ToString()).ToList();
                if (existingTags.Any())
                    Db.TagId.RemoveRange(existingTags);

                var newTags = model.TagIds.Select(tag => new TagId()
                {
                    TodoId = todoId.ToString(),
                    PrjectTagId = tag,

                }).ToList();
                await Db.TagId.AddRangeAsync(newTags);
            }

            var invitedMemberNew = false;

            if (!string.IsNullOrEmpty(model.MemberId))
            {
                // Delete existing member
                var members = Db.projectMgmt_TodoUsers
                        .Where(x => x.ProjectMgmt_TodoId == todoId).ToList();
                if (members.Any())
                {
                    if (members[0].UserId != model.MemberId)
                        invitedMemberNew = true;

                    Db.projectMgmt_TodoUsers.RemoveRange(members);
                }
            }

            var existingSequences = await Db.TodoTimeSequence.Where(x => x.ProjectMgmt_TodoId == todoId).ToListAsync();
            Db.RemoveRange(existingSequences);

            //var newTodoTimeSequences = await CreateTodoTimeSequence(model.UserId.ToString(), todo);
            //todo.TodoTimeSequence = newTodoTimeSequences;
            //Db.AddRange(newTodoTimeSequences);
            //await Db.SaveChangesAsync();

            var userEmailList = new List<string>();
            var todoUser = new List<ProjectMgmt_TodoUser>();
            var templatePath = string.Empty;
            var template = string.Empty;
            var temp = Directory.GetCurrentDirectory();
            if (!string.IsNullOrEmpty(model.MemberId) && string.IsNullOrEmpty(model.ExternalTeamMember))
            {
                var user = Db.UserProfiles.Where(x => x.UserId == model.MemberId).FirstOrDefault();

                if (user != null)
                {
                    userEmailList.Add(user.Email);

                    // Check of the todo is already assigned to a user
                    var existingTodoUser = Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id).FirstOrDefault();
                    if (existingTodoUser is not null)
                    {
                        if (existingTodoUser.UserId != model.MemberId)
                            Db.projectMgmt_TodoUsers.Remove(existingTodoUser);
                    }

                    var proj = new ProjectMgmt_TodoUser()
                    {
                        UserId = model.MemberId,
                        ProjectMgmt_TodoId = todo.Id
                    };

                    todoUser.Add(proj);
                    await Db.projectMgmt_TodoUsers.AddRangeAsync(todoUser);
                }

                // Send mail
                templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo/todo-internal-invited-email.html");
                template = File.ReadAllText(templatePath);
                var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, model.SubDomain) + $"/suite/pkg/project/todo-dashboard?type={TypeForInviteUrl.Todo.ToString()}&id={todo.Id}";
                template = template.Replace("{company}", tenant.CompanyName).Replace("{todo}", todo.TodoName.ToUpper()).Replace("{invitee}", invitee).Replace("{url}", url).Replace("{name}", user.FirstName).Replace("{description}", todo.TodoDescription ?? "No description was provided"); ;

                var subject = "";
                if (invitedMemberNew)
                {
                    subject = $"New Task Assigned: {todo.TodoSummary}";

                    if (model.MemberId != todo.CreatedBy)
                    {
                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"You have been assigned to a todo - {todo.TodoName} by {invitee}",
                            Event = EventCategory.Todo,
                            EventId = todo.Id.ToString(),
                            CreatedBy = model.UserId.ToString()
                        };

                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                            await AddUserNotification(new List<string> { model.MemberId }, Guid.Parse(notificationId));

                        // Todo: Invoke a frontend method using SignalR
                        await _hubContext.Clients.All.RecieveNotification();
                    }
                }
                else
                {
                    subject = "Update - New Task Assigned";

                    if (model.MemberId != todo.CreatedBy)
                    {
                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"A todo - {todo.TodoName} assigned to you has been updated by {invitee}",
                            Event = EventCategory.Todo,
                            EventId = todo.Id.ToString(),
                            CreatedBy = model.UserId.ToString()
                        };

                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                            await AddUserNotification(new List<string> { model.MemberId }, Guid.Parse(notificationId));

                        // Todo: Invoke a frontend method using SignalR
                        await _hubContext.Clients.All.RecieveNotification();
                    }
                }

                var taskId = BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(template, userEmailList, subject));
            }

            // Send mail to external team member
            if (!string.IsNullOrEmpty(model.ExternalTeamMember) && string.IsNullOrEmpty(model.MemberId))
            {
                // Add an invite to the database
                var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                {
                    Email = model.ExternalTeamMember,
                    Application = Applications.Joble,
                }, tenantId.ToString());

                // Add member to the todo
                // Check of the todo is already assigned to a user
                var existingTodoUser = Db.projectMgmt_TodoUsers.Where(x => x.ExternalMemberEmail == model.ExternalTeamMember)
                    .FirstOrDefault();
                if (existingTodoUser is not null)
                {
                    if (existingTodoUser.ExternalMemberEmail != model.ExternalTeamMember)
                        Db.projectMgmt_TodoUsers.Remove(existingTodoUser);
                }

                Db.projectMgmt_TodoUsers.Add(new ProjectMgmt_TodoUser { ProjectMgmt_TodoId = todo.Id, ExternalMemberEmail = model.ExternalTeamMember });                
                var encodedEmail = HttpUtility.UrlEncode(model.ExternalTeamMember);
                var inviteUrl = string.Format(Utility.Constants.INVITE_URL, model.SubDomain, invitee, tenantId, Applications.Joble, encodedEmail) + $"&type={TypeForInviteUrl.Todo.ToString()}&id={todo.Id}";

                templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo-invited-email.html");
                template = File.ReadAllText(templatePath);
                //template = await _emailService.GetTodoExternalEmailTemplate();

                template = template.Replace("{company}", tenant.CompanyName).Replace("{project}", project == null ? tenant.CompanyName : project.Name).Replace("{name}", invitee).Replace("{url}", inviteUrl);

                string subject = "Update: Invitation to JobPro - New Task Alert";
                var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, model.ExternalTeamMember, subject));
            }

            Db.ProjectMgmt_Todo.Update(todo);
            int dbResult = await Db.SaveChangesAsync();
            if (dbResult > 0)
            {
                // Log Activity
                await LogActivity(model.UserId.ToString(), EventCategory.Todo, "Todo updated", "Todo Updated", todo.Id.ToString());

                // Remove uploaded files from the database and add uploaded ones to the database
                if (model.UploadFile.Any() && !string.IsNullOrEmpty(model.UploadFile[0].FileName))
                {
                    var files = Db.ProjectFile.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToList();
                    if (files.Any())
                        Db.ProjectFile.RemoveRange(files);

                    // Upload file to S3
                    await UploadTodoFiles(model.UploadFile, todo.Id);
                }


                // Delete records from redis cache for assigned internal users
                await DeleteDataFromRedis(new List<string> { model.MemberId, todo.CreatedBy });

                return true;
            }
            else { return false; }
        }
        #endregion

        #region Move Todo to another Sprint or Project
        /// <summary>
        /// Move todo to another sprint or project
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="sprintId"></param>
        /// <param name="projectId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<bool> MoveTodoToProjectOrSprint(Guid todoId, string userId, string sprintId = null, string projectId = null)
        {
            // Get todo to be updated
            ProjectMgmt_Todo todo = await GetProjectMgmt_Todo(todoId);
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            // Check permission
            await CheckPermission(todo.CreatedBy, userId);

            if (!string.IsNullOrEmpty(sprintId))
            {
                // Check if sprint exists
                var sprint = await Db.SprintProjects.Where(x => x.SprintId.ToString() == sprintId).FirstOrDefaultAsync();
                if (sprint == null)
                    throw new RecordNotFoundException("Sprint not found");

                // Get the project using sprintId
                Guid id = await Db.SprintProjects.Where(x => x.Id.ToString() == sprintId).Select(x => x.ProjectMgmt_ProjectId).FirstOrDefaultAsync();
                var project = await Db.ProjectMgmt_Projects.Where(z => z.ProjectId == id).FirstOrDefaultAsync();
                if (project == null)
                    throw new RecordNotFoundException("Project not found");

                todo.TodoId = await GenerateTodoId(project.Name, sprintId);
                todo.SprintProjectId = new Guid(sprintId);
                todo.ProjectMgmt_ProjectId = project.ProjectId;
            }

            if (!string.IsNullOrEmpty(projectId) && string.IsNullOrEmpty(sprintId))
            {
                // Get the project using projectId
                var project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId.ToString() == projectId).FirstOrDefaultAsync();
                if (project == null)
                    throw new RecordNotFoundException("Project not found");

                todo.TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                todo.ProjectMgmt_ProjectId = project.ProjectId;
            }

            Db.ProjectMgmt_Todo.Update(todo);
            int result = await Db.SaveChangesAsync();

            if (result > 0)
                await LogActivity(userId, EventCategory.Todo, "Todo moved to another sprint", "Todo moved", todo.Id.ToString());

            return result > 0 ? true : false;
        }
        #endregion

        #region Todo Id Generator - Private Method
        /// <summary>
        /// Generate Todo Id
        /// </summary>
        /// <param name="projectName"></param>
        /// <param name="sprintId"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        private async Task<string> GenerateTodoId(string projectName, string sprintId = null, string projectId = null)
        {
            string todoId = string.Empty;

            List<string> todoIds = new List<string>();
            if (sprintId != null)
            {
                todoIds = await Db.ProjectMgmt_Todo.Where(x => x.SprintProjectId.ToString() == sprintId)
                    .OrderByDescending(x => x.CreatedDate).ThenByDescending(x => x.TodoId)
                    .Select(x => x.TodoId).ToListAsync();
            }

            if (projectId != null)
            {
                todoIds = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId)
                    .OrderByDescending(x => x.CreatedDate).ThenByDescending(x => x.TodoId)
                    .Select(x => x.TodoId).ToListAsync();
            }

            if (todoIds.Count == 0 || todoIds[0] == null)
            {
                if (!string.IsNullOrEmpty(projectName))
                {
                    string[] projectNames = projectName.Split(" ");
                    if (projectNames.Length < 2)
                        todoId = projectName[0].ToString().ToUpper() + projectName[1].ToString().ToUpper() + "-";
                    else
                        todoId = projectNames[0][0].ToString().ToUpper() + projectNames[1][0].ToString().ToUpper() + "-";

                    todoId += "001";
                }
            }
            else
            {
                var res = int.Parse(todoIds[0].Split("-")[1]);
                res++;
                if (res < 9)
                    todoId = todoIds[0].Split("-")[0] + "-00" + res;
                else if (res < 99)
                    todoId = todoIds[0].Split("-")[0] + "-0" + res;
                else
                    todoId = todoIds[0].Split("-")[0] + "-" + res;
            }

            return todoId;
        }
        #endregion

        #region Get uploaded files for a todo using todoId
        /// <summary>
        /// Get uploaded files for a todo
        /// </summary>
        /// <param name="todoId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<List<TodoFilesDto>> GetTodoUplaodedFiles(string todoId)
        {
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            var files = await Db.ProjectFile.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
            var filesToReturn = new List<TodoFilesDto>();
            foreach (var file in files)
            {
                filesToReturn.Add(new TodoFilesDto
                {
                    Base64String = await _aWSS3Sevices.DownloadFileAndConvertToBase64(file.FileName),
                    TodoFileName = file.FileName.Split("-")[1],
                });
            }

            await LogActivity(todo.CreatedBy, EventCategory.Todo, "Todo files retrieved", "Todo files retrieved", todo.Id.ToString());

            return filesToReturn;
        }
        #endregion

        #region Get todos by userId
        /// <summary>
        /// Gets todos for a user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="statusParam"></param>
        /// <param name="meMode"></param>
        /// <param name="strictStartDate"></param>
        /// <param name="app"></param>
        /// <param name="parameters"></param>
        /// <param name="createdByAI"></param>
        /// <param name="todoFilterForMobile"></param>
        /// <param name="searchParam"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<Page<ProjectMgmt_Todo>> GetTodoSByUserId(string userId, Applications? app, PaginationParameters parameters, string statusParam = null, bool meMode = false, bool strictStartDate = false, bool createdByAI = false, TodoFilterForMobile? todoFilterForMobile = null, string searchParam = null)
        {
            parameters.StartDate = parameters.StartDate ?? new DateTime(2023, 01, 01);
            parameters.EndDate = parameters.EndDate ?? new DateTime(4000, 01, 01);

            var cacheKey = $"todo_{userId}_{statusParam}_{app}_{parameters.PageNumber}_{parameters.PageSize}" +
                $"_{parameters?.StartDate.Value.ToShortDateString()}_{parameters.EndDate.Value.ToShortDateString()}_{meMode}_{strictStartDate}_{createdByAI}";
            Utility.AddKeyToCacheKeys(_redisKey, cacheKey);

            //var todosFromCache = await _redisCacheService.GetDataAsync<Page<ProjectMgmt_Todo>>(cacheKey);
            //if (todosFromCache != null)
            //    return todosFromCache;

            // Get the user
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            var todos = new List<ProjectMgmt_Todo>();

            if (!string.IsNullOrEmpty(statusParam))
            {

                todos = await Db.ProjectMgmt_Todo
                    .Include(x => x.projectMgmt_Project)
                    .Include(x => x.ProjectMgmt_TodoUsers)
                    .Include(x => x.TodoTimeSequence)
                    .Where(x => x.ProjectMgmt_TodoUsers.Any(y => y.UserId == userId || y.ExternalMemberEmail == user.Email)
                            && x.TodoStatus == statusParam && x.StartDateAndTime >= parameters.StartDate && x.EndTime <= parameters.EndDate && x.CreatedByAI == createdByAI)
                    .ToListAsync();
                if (!meMode)
                    todos.AddRange(await Db.ProjectMgmt_Todo
                   .Include(x => x.projectMgmt_Project)
                   .Where(x => x.CreatedBy == userId && x.TodoStatus == statusParam && x.StartDateAndTime >= parameters.StartDate && x.EndTime <= parameters.EndDate).ToListAsync());
                if (strictStartDate)
                    todos = todos.Where(x => x.StartDateAndTime.Date == parameters.StartDate).ToList();

                foreach (var todo in todos)
                {
                    var totalMinutes = (int)(todo.EndTime - GetAdjustedDateTimeBasedOnTZNow().ToLocalTime()).TotalDays;
                    (int hours, int minutes) = ConvertMinutesToHoursAndMinutes(totalMinutes);
                    todo.WillBeDueIn = $"{hours}h {minutes}m";
                }

                if (app != null)
                    todos = todos.Where(x => x.Application == app).ToList();
            }
            else
            {
                todos = await Db.ProjectMgmt_Todo
                    .Include(x => x.projectMgmt_Project)
                    .Include(x => x.ProjectMgmt_TodoUsers)
                    .Include(x => x.TodoTimeSequence)
                    .Where(x => x.ProjectMgmt_TodoUsers.Any(y => y.UserId == userId || y.ExternalMemberEmail == user.Email) && x.StartDateAndTime >= parameters.StartDate && x.EndTime <= parameters.EndDate && x.CreatedByAI == createdByAI)
                        .ToListAsync();
                if (!meMode)
                    todos.AddRange(await Db.ProjectMgmt_Todo
                    .Include(x => x.projectMgmt_Project)
                    .Where(x => x.CreatedBy == userId && x.StartDateAndTime >= parameters.StartDate && x.EndTime <= parameters.EndDate && x.CreatedByAI == createdByAI).ToListAsync());
                if (strictStartDate)
                    todos = todos.Where(x => x.StartDateAndTime.Date == parameters.StartDate).ToList();
            }

            foreach (var todo in todos)
            {
                var totalMinutes = (int)(todo.EndTime - GetAdjustedDateTimeBasedOnTZNow().ToLocalTime()).TotalDays;
                (int hours, int minutes) = ConvertMinutesToHoursAndMinutes(totalMinutes);
                todo.WillBeDueIn = $"{hours}h {minutes}m";

                // Get user that the todo is assigned to
                var todoUsers = await Db.projectMgmt_TodoUsers
                    .Where(x => x.ProjectMgmt_TodoId == todo.Id)
                    .ToListAsync();

                foreach (var userTodo in todoUsers)
                {
                    var todoUser = await Db.UserProfiles.Where(x => x.UserId == userTodo.UserId)
                        .FirstOrDefaultAsync();
                    if (todoUser is not null)
                    {
                        todo.AssignedTo = new UserDto
                        {
                            Id = userTodo.UserId,
                            FirstName = todoUser.FirstName,
                            LastName = todoUser.LastName,
                            Email = todoUser.Email,
                            ProfileUrl = todoUser.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(todoUser.ProfilePictureUrl) : null,
                        };
                        break;
                    }
                }
            }

            if (app != null)
                todos = todos.Where(x => x.Application == app && x.StartDateAndTime >= parameters.StartDate && x.EndTime <= parameters.EndDate).ToList();

            if (todoFilterForMobile != null)
            {
                switch (todoFilterForMobile)
                {
                    case TodoFilterForMobile.Personal:
                        todos = todos.Where(x => x.AssignedTo?.Id == userId).ToList();
                        break;
                    case TodoFilterForMobile.OthersTodo:
                        todos = todos.Where(x => x.AssignedTo?.Id != userId && x.CreatedBy == userId).ToList();
                        break;
                    case TodoFilterForMobile.All:
                        break;
                }
            }

            var query = todos.Distinct();
            if (!string.IsNullOrEmpty(searchParam))
            {
                query = query.Where(x =>
                     x.TodoSummary != null && x.TodoSummary.ToLower().Contains(searchParam.ToLower()) ||
                     x.TodoDescription != null && x.TodoDescription.ToLower().Contains(searchParam.ToLower()) ||
                     x.AssignedTo != null && (
                         (x.AssignedTo.FirstName != null && x.AssignedTo.FirstName.ToLower().Contains(searchParam.ToLower())) ||
                         (x.AssignedTo.LastName != null && x.AssignedTo.LastName.ToLower().Contains(searchParam.ToLower())) ||
                         (x.AssignedTo.MiddleName != null && x.AssignedTo.MiddleName.ToLower().Contains(searchParam.ToLower()))
                     )
                 );
            }

            // Handle for parameter Recent and Older params
            if (parameters.Recent != null && parameters.Recent.Value)
                query = query.OrderByDescending(x => x.CreatedDate);
            else if (parameters.Oldest != null && parameters.Oldest.Value)
                query = query.OrderBy(x => x.CreatedDate);
            else
                query = query
                    .OrderByDescending(x => x.TodoStatus == ProjectManagementStatus.OverDue.ToString())
                    .ThenByDescending(x => x.CreatedDate);

            var todosToReturn = query
                .ToList()
                .ToPageList(parameters.PageNumber, parameters.PageSize);

            // Get tags for each todo
            foreach (var todo in todosToReturn.Items)
            {
                var tags = await Db.TagId
                    .Where(x => x.TodoId == todo.Id.ToString())
                    .Select(x => x.PrjectTagId)
                    .ToListAsync();

                todo.ProjectTags = await Db.ProjectTag
                    .Where(x => tags.Contains(x.Id.ToString()))
                    .Distinct()
                    .ToListAsync();

            }

            await LogActivity(userId, EventCategory.Todo, "Todos retrieved", "Todos retrieved", string.Join(",", todos.Select(x => x.Id.ToString())));

            // Add to redis cache
            await AddDataToRedis(userId, cacheKey, todosToReturn);
            return todosToReturn;
        }
        #endregion

        #region Get UserWeeklyTodoAnalytics
        public async Task<WeeklyActivityAnalytics> GetUserWeeklyTodoAnalytics(string userId, string subdomain, string token)
        {
            DateTime date = DateTime.Today;
            //DateTime date = new DateTime(2024,02,23);
            var weeklyAnalytics = new Dictionary<string, double>()
            {
                {DayOfWeek.Monday.ToString(), 0.0 },
                {DayOfWeek.Tuesday.ToString(), 0.0 },
                {DayOfWeek.Wednesday.ToString(), 0.0 },
                {DayOfWeek.Thursday.ToString(), 0.0 },
                {DayOfWeek.Friday.ToString(), 0.0 },
                {DayOfWeek.Saturday.ToString(), 0.0 }

            };
            var todos = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == userId).Select(x => x.ProjectMgmt_Todo).ToListAsync();

            //If its sunday just return Zeros for the new week
            if (!todos.Any() || date.DayOfWeek == DayOfWeek.Sunday)
            {
                return new WeeklyActivityAnalytics()
                {
                    Grade = "F",
                    DailyPercentage = weeklyAnalytics,
                    WeeklyPercentage = 0.0,
                    PercentageDifference = 0.0
                };
            }
            int offset = date.DayOfWeek - DayOfWeek.Monday;
            DateTime lastMonday = date.AddDays(-offset);
            DateTime nextFriday = date.AddDays(5 - (int)date.DayOfWeek);
            var messageCount = await GetMessageCount(userId, subdomain, token);
            var userWeeklyTodo = todos.Where(x => x.EndTime <= nextFriday && x.StartDateAndTime >= lastMonday).ToList();
            var previousWeeklyTodo = todos.Where(x => x.EndTime <= nextFriday.AddDays(-7) && x.StartDateAndTime >= lastMonday.AddDays(-7)).ToList();

            //var weeklyAnalytics = new Dictionary<string, double>();
            double weeklyTotal = 0;
            double previousWeekTotal = 0;
            int dayCount = 0;
            for (DateTime i = lastMonday; i <= date; i = i.AddDays(+1))
            {
                //Stop Once you go beyond today
                if (dayCount > offset) break;
                //Completed Todos -  50 percent
                double todoPercentage = 0;
                double previousWeekTodoPercentage = 0;
                var previousWeekday = i.AddDays(-7);
                var userDailyTodos = todos.Where(x => x.LastUpdate.Date == i.Date || x.LastUpdate.Date == previousWeekday.Date).ToList();

                //Check if user has todos from current week and if there are any completed update weekly total
                var currentWeekDayTodos = userDailyTodos.Where(x => x.StartDateAndTime.Date == i.Date).ToList();
                if (currentWeekDayTodos.Count != 0)
                {
                    var completedTodos = currentWeekDayTodos.Where(x => x.TodoStatus == "Completed" || x.LastUpdate.Date == i.Date).Count();
                    todoPercentage = (double)completedTodos / currentWeekDayTodos.Count * 50.0;
                }

                //Check if user has todos from previous week and if there are any completed update weekly total
                var previousWeekDayTodos = userDailyTodos.Where(x => x.StartDateAndTime.Date == previousWeekday.Date).ToList();
                if (previousWeekDayTodos.Count != 0)
                {
                    var completedTodos = previousWeekDayTodos.Where(x => x.TodoStatus == "Completed" || x.LastUpdate.Date == previousWeekday.Date).Count();
                    previousWeekTodoPercentage = (double)completedTodos / previousWeekDayTodos.Count * 50.0;
                }

                //Colaboration on JobChat Percentage
                //var jobChatPercentage = 20.0;  //Pending when its been integrated
                var chatPercentage = 0;
                var prevChatPercentage = 0;
                if (messageCount.ContainsKey(i.Date))
                {
                    chatPercentage = messageCount[i.Date] > 0 ? 20 : 0;
                    prevChatPercentage = messageCount[previousWeekday.Date] > 0 ? 20 : 0;
                }

                //Internal Meetings -  (If > 2 meetings: 10%, 1 meeting: 5%, 0 meeting: 0percent)
                var meetings = await Db.CalenderMeetings.Where(x => x.CreatedBy.ToString() == userId && (x.CreatedAt.Date == i.Date || x.CreatedAt.Date == previousWeekday.Date))
                                                        .ToListAsync();
                var internalMeetingPercentage = meetings.Where(x => x.CreatedAt.Date == i).Count() >= 2 ? 10
                                                        : meetings.Where(x => x.CreatedAt.Date == i.Date).Count() == 1 ? 5 : 0;

                var previousWeekDayInternalMeetingPercentage = meetings.Where(x => x.CreatedAt.Date == previousWeekday.Date).Count() >= 2 ? 10
                                                                    : meetings.Where(x => x.CreatedAt.Date == previousWeekday.Date).Count() == 1 ? 5 : 0;

                //External Meetings - 20 percents
                var externalMeetsPercentage = 0;
                var prevExternalMeetsPercentage = 0;
                var externalMeets = await Db.ExternalMeeting.Where(x => x.CreatedBy.ToString() == userId && (x.CreatedAt.Date == i.Date || x.CreatedAt.Date == previousWeekday.Date))
                                                            .ToListAsync();
                if (externalMeets.Where(x => x.CreatedAt.Date == i.Date).Any()) externalMeetsPercentage = 20;
                else
                {
                    var externalInvites = await Db.ExternalMeetingMembers.Where(x => x.UserId == userId).Select(x => x.ExternalMeetingId).ToListAsync();
                    if (!externalInvites.Any())
                        foreach (var meetingId in externalInvites)
                        {
                            var meets = await Db.ExternalMeeting.Where(x => x.Id.ToString() == meetingId && x.MeetingStartDateRange == i.Date).ToListAsync();
                            if (!meets.Any()) continue;
                            else
                            {
                                externalMeetsPercentage = 20;
                                break;
                            }
                        }
                }

                if (externalMeets.Where(x => x.CreatedAt.Date == previousWeekday.Date).Any()) prevExternalMeetsPercentage = 20;
                else
                {
                    var externalInvites = await Db.ExternalMeetingMembers.Where(x => x.UserId == userId).Select(x => x.ExternalMeetingId).ToListAsync();
                    if (!externalInvites.Any())
                        foreach (var meetingId in externalInvites)
                        {
                            var meets = await Db.ExternalMeeting.Where(x => x.Id.ToString() == meetingId && x.MeetingStartDateRange == previousWeekday.Date).ToListAsync();
                            if (!meets.Any()) continue;
                            else
                            {
                                prevExternalMeetsPercentage = 20;
                                break;
                            }
                        }
                }

                //Project and Sprint Percentile 
                var projectAndSprintPercentage = 0;
                var prevWeekProjectAndSprintPercentage = 0;
                var projects = await Db.ProjectMgmt_Projects.Where(x => x.CreatedBy == userId && (x.CreatedTime == i.Date || x.CreatedTime == previousWeekday.Date)).ToListAsync();
                if (!projects.Where(x => x.CreatedTime == i).Any())
                {
                    var sprint = await Db.SprintProjects.AnyAsync(x => x.CreatedBy.ToString() == userId && x.CreatedDate.Date == i.Date);
                    if (sprint) projectAndSprintPercentage = 5;
                }
                else projectAndSprintPercentage = 5;

                if (!projects.Where(x => x.CreatedTime == previousWeekday.Date).Any())
                {
                    var sprint = await Db.SprintProjects.AnyAsync(x => x.CreatedBy.ToString() == userId && x.CreatedDate.Date == previousWeekday.Date);
                    if (sprint) prevWeekProjectAndSprintPercentage = 5;
                }
                else prevWeekProjectAndSprintPercentage = 5;

                //Created Todos = Percentage 5
                var createdTodos = await Db.ProjectMgmt_Todo.Where(x => x.CreatedBy == userId && (x.CreatedDate.Date == i.Date || x.CreatedDate.Date == previousWeekday.Date))
                                                            .ToListAsync();
                var createdTodoPercentage = createdTodos.Where(x => x.CreatedDate.Date == i.Date).ToList().Count > 2 ? 5 : 0;
                var prevWeekCreatedTodoPercentage = createdTodos.Where(x => x.CreatedDate.Date == previousWeekday.Date).ToList().Count > 2 ? 5 : 0;

                //Add all percentage and create entry in Dictionary
                var dailyPercentage = todoPercentage + internalMeetingPercentage + externalMeetsPercentage + projectAndSprintPercentage + createdTodoPercentage + chatPercentage;
                var prevWeekDailyPercentage = previousWeekTodoPercentage + previousWeekDayInternalMeetingPercentage + prevExternalMeetsPercentage
                                                            + prevWeekProjectAndSprintPercentage + prevWeekCreatedTodoPercentage + prevChatPercentage;
                weeklyAnalytics[i.DayOfWeek.ToString()] = Math.Round(dailyPercentage, 0, MidpointRounding.AwayFromZero);
                // weeklyAnalytics.Add(i.DayOfWeek.ToString(), dailyPercentage);
                weeklyTotal += dailyPercentage;
                previousWeekTotal += prevWeekDailyPercentage;
                dayCount++;

            }
            //getting the weekly Average grade to calculate grade
            double weeklyPercentage = Math.Round((double)weeklyTotal / dayCount, 0, MidpointRounding.AwayFromZero);
            double previousWeeklyPercentage = Math.Round((double)previousWeekTotal / dayCount, 0, MidpointRounding.AwayFromZero);

            string weeklyGrade;
            if (weeklyPercentage > 80) weeklyGrade = "A";
            else if (weeklyPercentage > 60 && weeklyPercentage <= 80) weeklyGrade = "B";
            else if (weeklyPercentage > 40 && weeklyPercentage <= 60) weeklyGrade = "C";
            else if (weeklyPercentage > 20 && weeklyPercentage <= 40) weeklyGrade = "D";
            else if (weeklyPercentage > 10 && weeklyPercentage <= 20) weeklyGrade = "E";
            else weeklyGrade = "F";

            var percentageDifference = weeklyPercentage - previousWeeklyPercentage;
            return new WeeklyActivityAnalytics()
            {
                Grade = weeklyGrade,
                DailyPercentage = weeklyAnalytics,
                WeeklyPercentage = weeklyPercentage,
                PercentageDifference = percentageDifference
            };
        }
        #endregion

        #region Time Suggestions For Todo
        public async Task<GenericResponse> SuggestTimeForTodoCreation(SuggestTimeForTodoCreationDto model)
        {
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == model.AssigneeUserId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            var personalSchedule = await Db.PersonalSchedule
                        .Where(p => p.UserId.ToString() == user.Id && p.Available && p.Day.ToLower() == model.Date.Day.ToString().ToLower())
                        .FirstOrDefaultAsync(model.CancellationToken);
            var duration = ConvertTodoDurationToHours(model.Duration);

            var startTime = personalSchedule != null ? personalSchedule.StartTime : model.Date.Date.ToLocalTime().AddHours(8); // 8 AM          
            var availableHours = personalSchedule != null ? personalSchedule.EndTime - startTime : model.Date.Date.ToLocalTime().AddHours(17) - startTime;

            // Loop through the availableHours by 30mins intervals, check if the user already has a todo at that time.
            // For Eg: If the user has a todo from 9am to 10am, the suggested time should be 10am to 11am
            DateTime? suggestedStartTime = null;
            DateTime? suggestedEndTime = null;
            for (var i = startTime; i < startTime.Add(availableHours); i = i.AddMinutes(30))
            {
                var endTime = i.AddHours(duration);

                var todoIds = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == model.AssigneeUserId).Select(x => x.ProjectMgmt_TodoId).ToListAsync();
                var todos = await Db.ProjectMgmt_Todo
                    .Where(x => x.StartDateAndTime.Date == model.Date.Date &&
                                (x.CreatedBy == model.AssigneeUserId || todoIds.Contains(x.Id)))
                    .ToListAsync(model.CancellationToken);

                // Now filter in memory using TimeOfDay
                var todo = todos
                    .FirstOrDefault(x =>
                        x.StartDateAndTime.TimeOfDay >= i.TimeOfDay &&
                        x.EndTime.TimeOfDay <= endTime.TimeOfDay);


                if (todo == null)
                {
                    suggestedEndTime = endTime;
                    suggestedStartTime = i;

                    // Check that the current time is not greater than the suggestedStartTime
                    if (DateTime.UtcNow.TimeOfDay < suggestedStartTime.Value.TimeOfDay)
                        break;
                }
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Suggested time for todo creation",
                Data = suggestedStartTime != null ? new
                {
                    SuggestedStartTime = suggestedStartTime,
                    SuggestedEndTime = suggestedEndTime
                } : null
            };
        }
        #endregion

        #region Get todo users by todoId
        public async Task<List<UserDto>> GetTodoUsersByTodoId(string todoId)
        {
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            var todoUsers = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
            var todoUsersToReturn = new List<UserDto>();
            foreach (var todoUser in todoUsers)
            {
                var user = await Db.UserProfiles
                    .FirstOrDefaultAsync(x => x.UserId == todoUser.UserId || x.Email == todoUser.ExternalMemberEmail);
                if (user is not null)
                {
                    todoUsersToReturn.Add(new UserDto
                    {
                        Id = user.UserId,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email,
                        ProfileUrl = await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl)
                    });
                }
            }

            await LogActivity(todo.CreatedBy, EventCategory.Todo, "Todo users retrieved", "Todo users retrieved", string.Join(",", todoUsersToReturn.Select(x => x.Id.ToString())));

            return todoUsersToReturn;
        }
        #endregion

        #region Assign external or internal user to a todo
        public async Task<bool> AssignUserToTodo(string todoId, AssignUserToTodoDto model)
        {
            Random rand = new Random();
            var todo = await Db.ProjectMgmt_Todo.Where(x => x.Id.ToString() == todoId).FirstOrDefaultAsync();
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            // Check permission
            await CheckPermission(todo.CreatedBy, model.LoggedInUserId);

            if (!string.IsNullOrEmpty(model.TeamId))
            {
                var teamMemberIds = await Db.TeamMembers.Where(x => x.Id.ToString() == model.TeamId)
                    .Select(teamMember => teamMember.UserId).ToListAsync();
                model.UserIds.AddRange(teamMemberIds);
                model.UserIds = model.UserIds.Distinct().ToList();
            }

            var invitee = await Db.UserProfiles.Where(x => x.UserId == model.LoggedInUserId)
                .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();

            var project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId == todo.ProjectMgmt_ProjectId);

            var tenant = await Dbo.Tenants.FirstOrDefaultAsync(x => x.Subdomain == model.Subdomain);
            var count = 0;
            //var existingSequences = await Db.TodoTimeSequence.Where(x => x.ProjectMgmt_TodoId.ToString() == todoId).ToListAsync();
            //Db.RemoveRange(existingSequences);

            ProjectMgmt_TodoUser todoUser = null;
            var tempCreationId = todo.TempCreationId;
            var invitedMembers = new List<string>();
            var isAssignedToMemebersMoreThanOne = false;
            var existingTodoUsers = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
            if (existingTodoUsers.Any())
                Db.projectMgmt_TodoUsers.RemoveRange(existingTodoUsers);
            var existingTodoUserIds = existingTodoUsers.Select(x => x.UserId).ToList();
            var newTodos = new List<ProjectMgmt_Todo>();
            if (model.UserIds?.Count > 1 || model.ExternalMemberEmail?.Count > 1 || model.UserIds.Any() && model.ExternalMemberEmail.Any())
                isAssignedToMemebersMoreThanOne = true;
            else
            {
                if (model.ExternalMemberEmail.Any())
                    invitedMembers.Add(model.ExternalMemberEmail[0]);
                else if (model.UserIds.Any())
                    invitedMembers.Add(model.UserIds[0]);
            }

            if (model.UserIds.Any() && isAssignedToMemebersMoreThanOne)
            {
                foreach (var userId in model.UserIds)
                {
                    //   var optimalStartTime = await CalculateOptimalTodoStartDate(userId, todo);

                    invitedMembers.Add(userId);
                    if (count == 0)
                    {
                        count++;
                        continue;
                    }
                    var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
                    if (user == null)
                        throw new RecordNotFoundException("User not found");

                    var newTodo = todo;
                    if (count > 0)
                    {
                        newTodo.Id = Guid.NewGuid();
                        //newTodo.StartDateAndTime = optimalStartTime;
                        //newTodo.EndTime = optimalStartTime.AddHours(ConvertTodoDurationToHours(todo.Duration));
                        if (!string.IsNullOrEmpty(newTodo.SprintProjectId.ToString()))
                            newTodo.TodoId = await GenerateTodoId(project.Name, newTodo.SprintProjectId.ToString());
                        if (project is not null && string.IsNullOrEmpty(newTodo.SprintProjectId.ToString()))
                            newTodo.TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                        else
                            newTodo.TodoId = "RN-" + rand.Next(100, 1000).ToString();

                        //var newTodoTimeSequences = await CreateTodoTimeSequence(userId.ToString(), newTodo);
                        //newTodo.TodoTimeSequence = newTodoTimeSequences;
                        //Db.AddRange(newTodoTimeSequences);
                        await Db.ProjectMgmt_Todo.AddAsync(newTodo);
                        newTodos.Add(newTodo);
                    }

                    await Db.SaveChangesAsync();
                    count++;
                }
            }
            else if (model.UserIds.Any())
            {
                // Add a record for projectMgmt_TodoUsers
                var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == model.UserIds[0]);
                if (user == null)
                    throw new RecordNotFoundException($"User ({model.UserIds[0]}) not found");
                //var newTodoTimeSequences = await CreateTodoTimeSequence(model.UserIds[0], todo);
                //todo.TodoTimeSequence = newTodoTimeSequences;
                //Db.AddRange(newTodoTimeSequences);
                //var optimalStartTime = await CalculateOptimalTodoStartDate(model.UserIds[0], todo);
                //todo.StartDateAndTime = optimalStartTime;
                //todo.EndTime = optimalStartTime.AddHours(ConvertTodoDurationToHours(todo.Duration));
                newTodos.Add(todo);
            }

            if (model.ExternalMemberEmail.Any() && isAssignedToMemebersMoreThanOne)
            {
                foreach (var email in model.ExternalMemberEmail)
                {
                    invitedMembers.Add(email);
                    if (count == 0)
                    {
                        count++;
                        continue;
                    }

                    var newTodo = todo;
                    if (count > 0)
                    {
                        newTodo.Id = Guid.NewGuid();
                        if (!string.IsNullOrEmpty(newTodo.SprintProjectId.ToString()))
                            newTodo.TodoId = await GenerateTodoId(project.Name, newTodo.SprintProjectId.ToString());
                        if (project is not null && string.IsNullOrEmpty(newTodo.SprintProjectId.ToString()))
                            newTodo.TodoId = await GenerateTodoId(project.Name, null, project.ProjectId.ToString());
                        else
                            newTodo.TodoId = "RN-" + rand.Next(100, 1000).ToString();

                        await Db.ProjectMgmt_Todo.AddAsync(newTodo);
                    }

                    await Db.SaveChangesAsync();
                    newTodos.Add(newTodo);
                }
            }
            else if (model.ExternalMemberEmail.Any())
            {
                newTodos.Add(todo);
            }

            // Add todo to the todoOrder Table
            var todos = await Db.ProjectMgmt_Todo.Where(x => x.TempCreationId == tempCreationId && x.Id.ToString() != todoId).ToListAsync();
            if (todos.Any())
                Db.ProjectMgmt_Todo.RemoveRange(todos);
            var templatePath = string.Empty;

            // Add todo users and send out notifications
            todos = newTodos;
            if (invitedMembers.Count == todos.Count)
            {
                for (var i = 0; i < todos.Count; i++)
                {
                    if (invitedMembers[i].Contains("@"))
                    {
                        var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                        {
                            Email = invitedMembers[i],
                            Application = Applications.Joble,
                        }, tenant.Id.ToString());

                        todoUser = new ProjectMgmt_TodoUser()
                        {
                            ProjectMgmt_TodoId = todos[i].Id,
                            ExternalMemberEmail = invitedMembers[i]
                        };

                        await Db.projectMgmt_TodoUsers.AddAsync(todoUser);
                        todos[i].ProjectMgmt_TodoUsers = new List<ProjectMgmt_TodoUser> { todoUser };
                        templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo-invited-email.html");
                        SendOutExternalNotification(model.Subdomain, project, tenant.CompanyName, tenant.Id, invitee, templatePath, invitedMembers[i], todos[i].Id, todos[i].TodoSummary);
                    }
                    else
                    {
                        todoUser = new ProjectMgmt_TodoUser()
                        {
                            ProjectMgmt_TodoId = todos[i].Id,
                            UserId = invitedMembers[i]
                        };
                        await Db.projectMgmt_TodoUsers.AddAsync(todoUser);
                        todos[i].ProjectMgmt_TodoUsers = new List<ProjectMgmt_TodoUser> { todoUser };
                        templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo/todo-internal-invited-email.html");
                        var userEmail = await Db.UserProfiles.Where(x => x.UserId == invitedMembers[i]).Select(x => x.Email).FirstOrDefaultAsync();
                        SendOutNotification(model.Subdomain, project?.Name, tenant.CompanyName, invitee, todos[i].Id, templatePath, userEmail, todos[i].TodoDescription);

                        // Send notification to the user
                        var notification = new AddNotificationDto
                        {
                            Message = $"You have been assigned to a todo - {todos[i].TodoName} by {invitee}",
                            Event = EventCategory.Todo,
                            EventId = todos[i].Id.ToString(),
                            CreatedBy = model.LoggedInUserId,
                        };

                        var notificationId = await AddNotification(notification);
                        if (notificationId is not null)
                            await AddUserNotification(new List<string> { todos[i].Id.ToString() }, Guid.Parse(notificationId));

                        // Todo: Invoke a frontend method using SignalR
                        await _hubContext.Clients.All.RecieveNotification();
                    }
                }
            }

            // Delete esisting members from project members
            var existingTodoMemberEmails = await Db.projectMgmt_TodoUsers
                .Where(x => x.ProjectMgmt_TodoId == todo.Id && x.ExternalMemberEmail != null)
                .Select(x => x.ExternalMemberEmail).ToListAsync();
            foreach (var email in existingTodoMemberEmails)
            {
                var memberDetails = await Db.UserProfiles.FirstOrDefaultAsync(x => x.Email == email);
                existingTodoUserIds.Add(memberDetails.UserId);
            }
            var projectMembers = await Db.projectMgmt_ProjectUsers
                .Where(x => x.ProjectMgmt_ProjectId == todo.ProjectMgmt_ProjectId && existingTodoUserIds.Distinct().Contains(x.ProjectMgmt_ProjectId.ToString())).ToListAsync();
            if (projectMembers.Any())
                Db.projectMgmt_ProjectUsers.RemoveRange(projectMembers);

            await Db.SaveChangesAsync();
            await UpdateTodoOrderForSprint(todo.SprintProjectId.ToString(), todos);

            // Delete records from redis cache for assigned internal users
            var redisIdsToDelete = new List<string>() { todo.CreatedBy };
            redisIdsToDelete.AddRange(invitedMembers);
            redisIdsToDelete.AddRange(existingTodoUserIds);
            await DeleteDataFromRedis(redisIdsToDelete.Distinct().ToList());

            return true;
        }
        #endregion

        #region Get todos by sprintId
        /// <summary>
        /// Get todos by sprintId
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="sprintId"></param>
        /// <param name="filters"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_TodobysprintId(PaginationParameters parameters, Guid sprintId, string userId, GetTodoBySprintFilters filters = null)
        {
            Page<ProjectMgmt_Todo> todos = null;
            if (filters is null)
                filters = new GetTodoBySprintFilters();

            // Assign last year date to startdate and 50 years in the future to enddate if they are null
            filters.StartDate = filters?.StartDate ?? new DateTime(2023, 1, 1);
            filters.EndDate = filters?.EndDate ?? GetAdjustedDateTimeBasedOnTZNow().AddYears(50);

            if (!filters.Tags.Any() && !string.IsNullOrEmpty(filters.TodoStatus))
            {
                todos = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                    .Include(x => x.TodoOrder)
                    .Where(x => x.SprintProjectId == sprintId && x.TodoStatus == filters.TodoStatus && x.StartDateAndTime >= filters.StartDate && x.EndTime <= filters.EndDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }
            else if (filters.Tags.Any() && string.IsNullOrEmpty(filters.TodoStatus))
            {
                todos = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                    .Include(x => x.TodoOrder)
                    .Where(x => x.SprintProjectId == sprintId && x.ProjectTags.Any(y => filters.Tags.Contains(y.TagName)) && x.StartDateAndTime >= filters.StartDate && x.EndTime <= filters.EndDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }
            else if (filters.Tags.Any() && !string.IsNullOrEmpty(filters.TodoStatus))
            {
                todos = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                    .Include(x => x.TodoOrder)
                    .Where(x => x.SprintProjectId == sprintId && x.ProjectTags.Any(y => filters.Tags.Contains(y.TagName)) && x.TodoStatus == filters.TodoStatus && x.StartDateAndTime >= filters.StartDate && x.EndTime <= filters.EndDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }
            else
                todos = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                    .Include(x => x.TodoOrder)
                    .Where(x => x.SprintProjectId == sprintId && x.StartDateAndTime >= filters.StartDate && x.EndTime <= filters.EndDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            todos.Items = todos.Items.OrderByDescending(x => x.CreatedDate).ToArray();

            foreach (var todo in todos.Items)
            {
                // Get the user todo was assigned to
                var todoUser = await Db.projectMgmt_TodoUsers.FirstOrDefaultAsync(x => x.ProjectMgmt_TodoId == todo.Id);
                if (filters.TeamMemberIds.Any())
                {
                    if (todoUser != null && filters.TeamMemberIds.Contains(todoUser.UserId))
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == todoUser.UserId).Select(x => new UserDto
                        {
                            Id = x.UserId,
                            FirstName = x.FirstName,
                            LastName = x.LastName,
                            Email = x.Email,
                            ProfileUrl = x.ProfilePictureUrl != null ? Utility.ConvertSignedUrlToBase64(_aWSS3Sevices.GetSignedUrlAsync(x.ProfilePictureUrl, 30).Result) : null
                        }).FirstOrDefaultAsync();

                        if (user != null)
                        {
                            todo.AssignedTo = user;
                        }
                    }
                }
                else
                {
                    if (todoUser != null)
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == todoUser.UserId).Select(x => new UserDto
                        {
                            Id = x.UserId,
                            FirstName = x.FirstName,
                            LastName = x.LastName,
                            Email = x.Email,
                            ProfileUrl = x.ProfilePictureUrl != null ? Utility.ConvertSignedUrlToBase64(_aWSS3Sevices.GetSignedUrlAsync(x.ProfilePictureUrl, 30).Result) : null
                        }).FirstOrDefaultAsync();

                        if (user != null)
                        {
                            todo.AssignedTo = user;
                        }
                    }
                }

                var totalMinutes = (int)(todo.EndTime - GetAdjustedDateTimeBasedOnTZNow().ToLocalTime()).TotalDays;
                (int hours, int minutes) = ConvertMinutesToHoursAndMinutes(totalMinutes);
                todo.WillBeDueIn = $"{hours}h {minutes}m";
            }

            if (filters.TeamMemberIds.Any())
            {
                ProjectMgmt_Todo[] todosByTeamMember = new ProjectMgmt_Todo[todos.Items.Length];
                var index = 0;
                foreach (var todo in todos.Items)
                {
                    // Remove all todos that the AssignedTo value is null
                    if (todo.AssignedTo != null)
                    {
                        todosByTeamMember[index] = todo;
                        index++;
                    }
                }

                todos.Items = todosByTeamMember;
            }

            await LogActivity(userId, EventCategory.Todo, "Get todos by sprintId", "Get todos", sprintId.ToString());

            return todos;
        }
        #endregion

        #region Get todos status by sprintid
        /// <summary>
        /// Get todos status by sprintid
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        public async Task<List<TodoStatus>> GetTodoStatusBySprintId(Guid sprintId)
        {
            var todoStatus = await Db.TodoStatus.Where(x => x.SprintId == sprintId).ToListAsync();
            return todoStatus;
        }
        #endregion

        #region Update Todo Order And Status
        /// <summary>
        /// Update Todo Order
        /// </summary>
        /// <param name="model"></param>
        /// <param name="loggedInUserId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UpdateTodoStatusAndOrder(List<UpdateTodoOrderDto> model, string loggedInUserId, string subdomain)
        {
            List<TodoOrder> todoOrders = new List<TodoOrder>();
            foreach (var item in model)
            {
                var todoOrder = await Db.TodoOrder.FirstOrDefaultAsync(x => x.TodoId.ToString() == item.TodoId);

                if (todoOrder == null)
                    throw new RecordNotFoundException("Todo Order not found");

                todoOrder.Order = item.Order;
                todoOrder.Status = item.Status;
                todoOrders.Add(todoOrder);
            }

            // Get the todo that has a different status from others,its status will be updated
            foreach (var item in model)
            {
                var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == item.TodoId);
                if (todo == null)
                    throw new RecordNotFoundException("Todo not found");

                var oldStatus = todo.TodoStatus;
                var todoOwner = await Db.UserProfiles.Where(x => x.UserId == todo.CreatedBy).FirstOrDefaultAsync();

                if (todo.TodoStatus != item.Status)
                {
                    todo.TodoStatus = item.Status;
                    var todoSequence = await Db.TodoTimeSequence.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
                    if (todoSequence.Any())
                    {
                        foreach (var sequence in todoSequence)
                            sequence.TodoStatus = item.Status;
                    }
                    Db.TodoTimeSequence.UpdateRange(todoSequence);
                    if (item.Status == ProjectManagementStatus.Completed.ToString())
                        todo.CompletedAt = GetAdjustedDateTimeBasedOnTZNow();

                    todo.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
                    Db.ProjectMgmt_Todo.Update(todo);
                    await Db.SaveChangesAsync();

                    if (todoOwner.UserId == loggedInUserId) continue;

                    var loggedInUserName = await Db.UserProfiles.Where(x => x.UserId == loggedInUserId)
                        .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();

                    // Send an email notification to the user
                    var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo-update-template.html");
                    var template = File.ReadAllText(templatePath);

                    var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL, subdomain) + $"?type={TypeForInviteUrl.Todo}&id={todo.Id}";
                    template = template.Replace("{url}", url).Replace("{todoId}", todo.TodoId).Replace("{TodoName}", todo.TodoName).Replace("{name}", loggedInUserName).Replace("{old}", oldStatus).Replace("{new}", item.Status);

                    string subject = $"({todo.TodoId}) - Update On {todo.TodoName}";
                    var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, todoOwner.Email, subject));
                }
            }

            Db.TodoOrder.UpdateRange(todoOrders);
            var res = await Db.SaveChangesAsync();
            if (res > 0)
            {
                await LogActivity(loggedInUserId, EventCategory.Todo, "Update todo order and status", "Update todo order and status", null);
                return true;
            }

            return false;
        }
        #endregion

        #region Update Todo Status
        public async Task<ApiResponse<bool>> UpdateTodoStatus(string todoId, ProjectManagementStatus status, string loggedInUserId, string subdomain)
        {
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (todo == null)
                throw new RecordNotFoundException("Todo not found");

            var todoMemberIds = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id).Select(x => x.UserId).ToListAsync();

            if (status == ProjectManagementStatus.InProgress)
            {
                if (!todoMemberIds.Any())
                {
                    throw new InvalidOperationException("Assigned user for Todo not found, please assign a user before starting this TODO item.");
                }
            }

            if (status == ProjectManagementStatus.Completed)
                todo.CompletedAt = GetAdjustedDateTimeBasedOnTZNow();

            var oldStatus = todo.TodoStatus;
            todo.TodoStatus = status.ToString();
            todo.LastUpdate = DateTime.UtcNow;
            Db.ProjectMgmt_Todo.Update(todo);
            var todoSequence = await Db.TodoTimeSequence.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
            if (todoSequence.Any())
            {
                foreach (var sequence in todoSequence)
                    sequence.TodoStatus = status.ToString();
            }
            Db.TodoTimeSequence.UpdateRange(todoSequence);
            await Db.SaveChangesAsync();

            var loggedInUserName = await Db.UserProfiles.Where(x => x.UserId == loggedInUserId)
                       .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();
            var todoOwnerEmail = await Db.UserProfiles.Where(x => x.UserId == todo.CreatedBy)
                .Select(u => u.Email).FirstOrDefaultAsync();

            // Send an email notification to the user
            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo/todo_status_update.html");
            var template = File.ReadAllText(templatePath);

            todoMemberIds.Add(todo.CreatedBy);

            var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL, subdomain) + $"?type={TypeForInviteUrl.Todo}&id={todo.Id}";
            // Replace placeholders in the new template
            template = template.Replace("Social Ads Brief", todo.TodoName)
                              .Replace("[user_name]", loggedInUserName)
                              .Replace("[status]", status.ToString())
                              .Replace("[date_and_time]", DateTime.Now.ToString("MMM dd, yyyy 'at' h:mm tt"))
                              .Replace("href=\"#\"", $"href=\"{url}\""); // Update the View Task button URL

            string subject = $"({todo.TodoId}) - Update On {todo.TodoName}";
            var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, todoOwnerEmail, subject));

            // Delete records from redis cache for assigned internal users
            await DeleteDataFromRedis(todoMemberIds);

            return new ApiResponse<bool>
            {
                ResponseCode = "200",
                ResponseMessage = "Updated successfully",
                Data = true
            };
        }
        #endregion

        #region Delete Todo
        /// <summary>
        /// Delete Todo
        /// </summary>
        /// <param name="id"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<bool> DeleteProjectMgmt_Todo(string id, string loggedInUserId)
        {
            try
            {
                if (string.IsNullOrEmpty(id)) { throw new ArgumentException("id cannot be null"); }

                var todo = await Db.ProjectMgmt_Todo.Where(x => x.Id == Guid.Parse(id)).FirstOrDefaultAsync();
                if (todo == null)
                    throw new RecordNotFoundException("No Todo Record Found");

                // Check permissions
                await CheckPermission(todo.CreatedBy, loggedInUserId);

                // Delete this todo tags
                var tags = Db.TagId.Where(x => x.TodoId == id).ToList();
                if (tags.Any())
                    Db.TagId.RemoveRange(tags);

                // Delete this todo members
                var members = Db.projectMgmt_TodoUsers
                    .Where(x => x.ProjectMgmt_TodoId == todo.Id).ToList();
                if (members.Any())
                    Db.projectMgmt_TodoUsers.RemoveRange(members);

                var todoSequence = await Db.TodoTimeSequence.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
                if (todoSequence.Any())
                    Db.TodoTimeSequence.RemoveRange(todoSequence);

                // Delete todo members from project members
                var memberIds = members.Select(x => x.UserId).ToList();
                var memberEmails = members.Where(m => m.ExternalMemberEmail != null)
                    .Select(x => x.ExternalMemberEmail).ToList();
                foreach (var email in memberEmails)
                {
                    var memberDetails = await Db.UserProfiles.FirstOrDefaultAsync(x => x.Email == email);
                    memberIds.Add(memberDetails.UserId);
                }

                var projectMembers = Db.projectMgmt_ProjectUsers
                    .Where(x => memberIds.Contains(x.UserId) && x.ProjectMgmt_ProjectId == todo.ProjectMgmt_ProjectId).ToList();
                if (projectMembers.Any())
                {
                    Db.projectMgmt_ProjectUsers.RemoveRange(projectMembers);
                }

                // Delete this todo tags from projectTga
                var projectTag = Db.ProjectTag
                    .Where(x => x.ProjectMgmt_TodoId == todo.Id).ToList();
                if (projectTag.Any())
                    Db.ProjectTag.RemoveRange(projectTag);

                // Delete Uploaded files
                var files = Db.ProjectFile
                    .Where(x => x.ProjectMgmt_TodoId == todo.Id).ToList();
                if (files.Any())
                    Db.ProjectFile.RemoveRange(files);

                // Delete todoId from TodoOrder table
                var todoOrder = Db.TodoOrder
                    .Where(x => x.TodoId == todo.Id).ToList();
                if (todoOrder.Any())
                    Db.TodoOrder.RemoveRange(todoOrder);

                Db.ProjectMgmt_Todo.Remove(todo);
                int result = await Db.SaveChangesAsync();

                if (result > 0)
                {
                    // Delete records from redis cache for assigned internal users
                    var todoMemberIds = await Db.projectMgmt_TodoUsers
                        .Where(x => x.ProjectMgmt_TodoId == todo.Id).Select(x => x.UserId).ToListAsync();
                    todoMemberIds.Add(todo.CreatedBy);
                    await DeleteDataFromRedis(todoMemberIds);

                    await LogActivity(todo.CreatedBy, EventCategory.Todo, "Todo deleted", "Todo deleted", null);
                    return true;
                }
                else { return false; }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion

        #region Get Todo By Id
        /// <summary>
        /// Gets todo by todo id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ProjectMgmt_Todo> GetProjectMgmt_Todo(Guid id)
        {
            ProjectMgmt_Todo result = await Db.ProjectMgmt_Todo.AsNoTracking()
                .Include(x => x.projectMgmt_Project)
                .Include(x => x.ProjectMgmt_TodoUsers)
                .Include(x => x.TodoTimeSequence)
                .Include(x => x.ProjectTags).Where(x => x.Id == id).FirstOrDefaultAsync();

            // Get user that the todo is assigned to
            var todoUsers = await Db.projectMgmt_TodoUsers
                .Where(x => x.ProjectMgmt_TodoId == id)
                .ToListAsync();
            foreach (var userTodo in todoUsers)
            {
                var todoUser = await Db.UserProfiles.Where(x => x.UserId == userTodo.UserId)
                    .FirstOrDefaultAsync();
                if (todoUser is not null)
                {
                    result.AssignedTo = new UserDto
                    {
                        Id = userTodo.UserId,
                        FirstName = todoUser.FirstName,
                        LastName = todoUser.LastName,
                        Email = todoUser.Email,
                        ProfileUrl = todoUser.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(todoUser.ProfilePictureUrl) : null,
                    };
                    break;
                }
            }

            var todoTagIds = await Db.TagId.Where(x => x.TodoId == id.ToString()).ToListAsync();
            foreach (var tag in todoTagIds.Distinct())
            {
                var tagName = await Db.ProjectTag.FirstOrDefaultAsync(x => x.Id.ToString() == tag.PrjectTagId);
                if (tagName != null)
                {
                    if (result.ProjectTags.Any(x => x.TagName != tagName.TagName))
                        result.ProjectTags.Add(tagName);
                }
            }

            return result;
        }
        #endregion

        #region Get All Todo Paginated
        /// <summary>
        /// Gets all todo paginated
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="statusParam"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        public async Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_Todo(PaginationParameters parameters, Applications? app, string statusParam = null)
        {
            parameters.StartDate = parameters.StartDate ?? new DateTime(2023, 01, 01);
            parameters.EndDate = parameters.EndDate ?? new DateTime(4000, 01, 01);

            if (statusParam != null)
            {
                if (!string.IsNullOrEmpty(app.ToString()))
                    return await this.Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == statusParam && x.Application == app && x.CreatedDate >= parameters.StartDate && x.CreatedDate <= parameters.EndDate)
                        .OrderByDescending((x) => x.CreatedDate)
                        .Include(x => x.TodoTimeSequence)
                        .Include(x => x.ProjectTags)
                        .ToPageListAsync(parameters.PageNumber, parameters.PageSize);
                else
                    return await this.Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == statusParam && x.CreatedDate >= parameters.StartDate && x.CreatedDate <= parameters.EndDate)
                        .OrderByDescending((x) => x.CreatedDate)
                        .Include(x => x.ProjectTags)
                        .Include(x => x.TodoTimeSequence)
                        .ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }

            if (!string.IsNullOrEmpty(app.ToString()))
                return await this.Db.ProjectMgmt_Todo.Where(x => x.Application == app && x.CreatedDate >= parameters.StartDate && x.CreatedDate <= parameters.EndDate)
                    .OrderByDescending((x) => x.TodoStatus == "Overdue")
                    .ThenByDescending(x => x.CreatedDate)
                    .Include(x => x.ProjectTags)
                    .Include(x => x.TodoTimeSequence)
                    .ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            else
                return await this.Db.ProjectMgmt_Todo.Where(x => x.CreatedDate >= parameters.StartDate && x.CreatedDate <= parameters.EndDate)
                    .OrderByDescending(x => x.CreatedDate)
                    .ThenByDescending((x) => x.TodoStatus == "Overdue")
                    .Include(x => x.TodoTimeSequence)
                    .Include(x => x.ProjectTags).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }
        #endregion

        #region Get All Todo By Project Id
        /// <summary>
        /// Gets all todo by project id
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public async Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_TodobyprojectId(PaginationParameters parameters, Guid projectId)
        {
            var projetcts = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                .Where(x => x.ProjectMgmt_ProjectId == projectId).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            return projetcts;
        }
        #endregion

        #region Get All Todo without Project or Sprint
        /// <summary>
        /// Get all todo without project or sprint
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        public async Task<Page<ProjectMgmt_Todo>> GetAllTodoWithoutSprintOrProject(PaginationParameters parameters, Applications? app)
        {
            if (app is not null)
                return await Db.ProjectMgmt_Todo
                    .Where(x => x.SprintProjectId == null && x.ProjectMgmt_ProjectId == null && x.Application == app).OrderBy((x) => x.CreatedDate)
                    .Include(x => x.ProjectTags).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            else
                return await Db.ProjectMgmt_Todo
                    .Where(x => x.SprintProjectId == null && x.ProjectMgmt_ProjectId == null).OrderBy((x) => x.CreatedDate)
                    .Include(x => x.ProjectTags).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }
        #endregion

        #region Update the Todo Count Down
        /// <summary>
        /// Update the todo count down
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="timeSpent"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateTodoCountDownAndTimeSpent(Guid todoId, string timeSpent, string loggedInUserId, string status = null)
        {
            try
            {
                var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id == todoId);
                if (todo == null)
                    throw new RecordNotFoundException("No Todo Record Found");

                if (todo.LockTodo)
                    throw new Exception("Todo is locked, you can not update the time spent on this todo");

                TimeSpan suppliedTime;
                if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out suppliedTime))
                {
                    throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                TimeSpan oldTime;
                if (!TimeSpan.TryParse(todo.TimeLeft, CultureInfo.InvariantCulture, out oldTime))
                {
                    throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                TimeSpan oldTimeSpent;
                if (!TimeSpan.TryParse(todo.TimeSpent, CultureInfo.InvariantCulture, out oldTimeSpent))
                {
                    throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                if (suppliedTime <= oldTime)
                    todo.TimeLeft = (oldTime + oldTimeSpent - suppliedTime).ToString();
                else
                    todo.TimeLeft = "00:00:00";

                todo.TimeSpent = suppliedTime.ToString();
                if (!string.IsNullOrEmpty(status))
                {
                    todo.TodoStatus = status;
                    var todoSequence = await Db.TodoTimeSequence.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
                    if (todoSequence.Any())
                    {
                        foreach (var sequence in todoSequence)
                            sequence.TodoStatus = status;
                    }
                    Db.TodoTimeSequence.UpdateRange(todoSequence);
                }


                todo.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
                Db.ProjectMgmt_Todo.Update(todo);
                int result = await Db.SaveChangesAsync();
                if (result > 0)
                {
                    await LogActivity(loggedInUserId, EventCategory.Todo, $"Updated the time spent", "Todo updated", todoId.ToString());

                    // Delete records from redis cache for assigned internal users
                    var todoMemberIds = await Db.projectMgmt_TodoUsers
                        .Where(x => x.ProjectMgmt_TodoId == todo.Id).Select(x => x.UserId).ToListAsync();

                    todoMemberIds.Add(todo.CreatedBy);
                    await DeleteDataFromRedis(todoMemberIds);

                    return true;
                }
                else { return false; }
            }
            catch (Exception)
            {
                throw;
            }
        }
        #endregion

        #region Get GetUserWeeklyGlobalActivityScore

        /// <summary>
        /// Retrieves the weekly global activity scores for a specific user within a given date range.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="subdomain">The subdomain for database schema.</param>
        /// <param name="startDateRange">The optional start date for the date range. Defaults to the start of the current week if not provided.</param>
        /// <param name="endDateRange">The optional end date for the date range. Defaults to the end of the current week if not provided.</param>
        /// <returns>A list of <see cref="CompanyWeeklyAnalytics"/> representing the user's activity scores for the specified date range.</returns>
        /// <exception cref="OperationNotAllowedException">Thrown if the end date is earlier than the start date.</exception>
        public async Task<List<CompanyWeeklyAnalytics>> GetUserWeeklyGlobalActivityScore(string subdomain, string userId, DateTime? startDateRange, DateTime? endDateRange)
        {
            DateTime today = DateTime.Today;
            int offset = today.DayOfWeek - DayOfWeek.Monday;
            DateTime defaultStartDate = today.AddDays(-offset);
            DateTime defaultEndDate = defaultStartDate.AddDays(4);

            DateTime startDate = startDateRange ?? defaultStartDate;
            DateTime endDate = endDateRange ?? defaultEndDate;

            if (endDate < startDate)
            {
                throw new OperationNotAllowedException("End date must be greater than or equal to the start date.");
            }

            // 1. Get the user profile using the userId
            using var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
            UserProfile userProfile = await context.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);

            // 2. If no user profile return empty list
            if (userProfile == null)
            {
                return new List<CompanyWeeklyAnalytics>();
            }

            var userDomain = await Dbo.Tenants.FirstOrDefaultAsync(t => t.Subdomain == subdomain);
            if (userDomain == null) return new List<CompanyWeeklyAnalytics>();

            var userCompanyName = userDomain.CompanyName;
            var userCompanyLogo = string.IsNullOrEmpty(userDomain.LogoUrl) ? "" : await _aWSS3Sevices.GetSignedUrlAsync(userDomain.LogoUrl);

            // 3. Get the user scores from the PerformanceMetricAnalyses table
            var userScore = await context.PerformanceMetricAnalyses
                .FirstOrDefaultAsync(x => x.StartDateForTheWeek >= startDate &&
                                          x.EndDateForTheWeek <= endDate &&
                                          x.UserId == userId);

            var userWeeklyActivityScores = new List<CompanyWeeklyAnalytics>();

            // 4. If the user score is null, there is nothing to compare , return an empty list
            if (userScore == null)
            {
                return new List<CompanyWeeklyAnalytics>();
            }
            else
            {
                userWeeklyActivityScores.Add(new CompanyWeeklyAnalytics
                {
                    UserId = userProfile.Id,
                    CompanyLogo = userCompanyLogo,
                    CompanyName = userCompanyName,
                    Grade = GetGrade(userScore.TotalPercentage),
                    WeeklyPercentage = userScore.TotalPercentage,
                    Rank = 0
                });
            }

            var subDomains = GlobalVariables.Subdomains;
            subDomains = subDomains.Where(x => x != subdomain).ToList();

            foreach (var domain in subDomains)
            {
                using var domainContext = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(domain));

                var domainTenant = await Dbo.Tenants.FirstOrDefaultAsync(t => t.Subdomain == domain);
                if (domainTenant == null) continue;

                var companyName = domainTenant.CompanyName;
                var companyLogo = string.IsNullOrEmpty(domainTenant.LogoUrl) ? "" : await _aWSS3Sevices.GetSignedUrlAsync(domainTenant.LogoUrl);


                // Get the highest score for each subdomain (excluding the user subdomain)
                var highestScore = await domainContext.PerformanceMetricAnalyses
                    .Where(x => x.StartDateForTheWeek >= startDate &&
                                x.EndDateForTheWeek <= endDate)
                    .OrderByDescending(x => x.TotalPercentage)
                    .FirstOrDefaultAsync();

                // If there are no scores for a subdomain, return a default with Total Percentage = 0
                if (highestScore == null)
                {
                    userWeeklyActivityScores.Add(new CompanyWeeklyAnalytics
                    {
                        UserId = "N/A",
                        CompanyLogo = companyLogo,
                        CompanyName = companyName,
                        Grade = GetGrade(0),
                        WeeklyPercentage = 0,
                        Rank = 0
                    });
                }
                else
                {
                    userWeeklyActivityScores.Add(new CompanyWeeklyAnalytics
                    {
                        UserId = highestScore.UserId,
                        CompanyLogo = companyLogo,
                        CompanyName = companyName,
                        Grade = GetGrade(highestScore.TotalPercentage),
                        WeeklyPercentage = highestScore.TotalPercentage,
                        Rank = 0
                    });
                }
            }

            // Assign the rank based on percentage
            userWeeklyActivityScores = userWeeklyActivityScores.OrderByDescending(x => x.WeeklyPercentage).ToList();
            for (int i = 0; i < userWeeklyActivityScores.Count; i++)
            {
                userWeeklyActivityScores[i].Rank = i + 1;
            }

            //Find the user's score and handle their positioning
            var userScoreEntry = userWeeklyActivityScores.FirstOrDefault(x => x.UserId == userId);
            if (userScoreEntry != null)
            {
                userWeeklyActivityScores.Remove(userScoreEntry);

                if (userScoreEntry.Rank <= 3)
                {
                    userWeeklyActivityScores.Insert(userScoreEntry.Rank - 1, userScoreEntry);
                }
                else
                {
                    userWeeklyActivityScores.Insert(3, userScoreEntry); // Place the user in the 4th position
                }
            }

            // Ensure the list is trimmed to a maximum of 7 results
            return userWeeklyActivityScores.Take(7).ToList();
        }


        /// <summary>
        /// Retrieves the weekly global activity scores for a specific user within a given date range.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="subdomain">The subdomain for database schema.</param>
        /// <param name="startDateRange">The optional start date for the date range. Defaults to the start of the current week if not provided.</param>
        /// <param name="endDateRange">The optional end date for the date range. Defaults to the end of the current week if not provided.</param>
        /// <returns>A list of <see cref="CompanyWeeklyAnalytics"/> representing the user's activity scores for the specified date range.</returns>
        /// <exception cref="OperationNotAllowedException">Thrown if the end date is earlier than the start date.</exception>
        public async Task<List<CompanyWeeklyAnalytics>> GetUserWeeklyIndustryActivityScore(string subdomain, string userId, DateTime? startDateRange, DateTime? endDateRange)
        {
            DateTime today = DateTime.Today;
            int offset = today.DayOfWeek - DayOfWeek.Monday;
            DateTime defaultStartDate = today.AddDays(-offset);
            DateTime defaultEndDate = defaultStartDate.AddDays(4);

            DateTime startDate = startDateRange ?? defaultStartDate;
            DateTime endDate = endDateRange ?? defaultEndDate;

            if (endDate < startDate)
            {
                throw new OperationNotAllowedException("End date must be greater than or equal to the start date.");
            }

            // 1. Get the user profile using the userId
            using var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
            UserProfile userProfile = await context.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);

            // 2. If no user profile return empty list
            if (userProfile == null)
            {
                return new List<CompanyWeeklyAnalytics>();
            }

            var userDomain = await Dbo.Tenants.FirstOrDefaultAsync(t => t.Subdomain == subdomain);
            if (userDomain == null) return new List<CompanyWeeklyAnalytics>();

            var userCompanyName = userDomain.CompanyName;
            var userCompanyLogo = string.IsNullOrEmpty(userDomain.LogoUrl) ? "" : await _aWSS3Sevices.GetSignedUrlAsync(userDomain.LogoUrl);

            // 3. Get the user scores from the PerformanceMetricAnalyses table
            var userScore = await context.PerformanceMetricAnalyses
                .FirstOrDefaultAsync(x => x.StartDateForTheWeek >= startDate &&
                                          x.EndDateForTheWeek <= endDate &&
                                          x.UserId == userId);

            var userWeeklyActivityScores = new List<CompanyWeeklyAnalytics>();


            // 4. If the user score is null, there is nothing to compare , return an empty list
            if (userScore == null)
            {
                return new List<CompanyWeeklyAnalytics>();
            }
            else
            {
                userWeeklyActivityScores.Add(new CompanyWeeklyAnalytics
                {
                    UserId = userProfile.Id,
                    CompanyLogo = userCompanyLogo,
                    CompanyName = userCompanyName,
                    Grade = GetGrade(userScore.TotalPercentage),
                    WeeklyPercentage = userScore.TotalPercentage,
                    Rank = 0
                });
            }

            var subDomains = GlobalVariables.Subdomains;
            subDomains = subDomains.Where(x => x != subdomain).ToList();

            foreach (var domain in subDomains)
            {
                using var domainContext = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(domain));

                var domainTenant = await Dbo.Tenants.FirstOrDefaultAsync(t => t.Subdomain == domain && t.Industry == userDomain.Industry);
                if (domainTenant == null) continue;

                var companyName = domainTenant.CompanyName;
                var companyLogo = string.IsNullOrEmpty(domainTenant.LogoUrl) ? "" : await _aWSS3Sevices.GetSignedUrlAsync(domainTenant.LogoUrl);


                // Get the highest score for each subdomain (excluding the user subdomain)
                var highestScore = await domainContext.PerformanceMetricAnalyses
                    .Where(x => x.StartDateForTheWeek >= startDate &&
                                x.EndDateForTheWeek <= endDate)
                    .OrderByDescending(x => x.TotalPercentage)
                    .FirstOrDefaultAsync();

                // If there are no scores for a subdomain, return a default with Total Percentage = 0
                if (highestScore == null)
                {
                    userWeeklyActivityScores.Add(new CompanyWeeklyAnalytics
                    {
                        UserId = "N/A",
                        CompanyLogo = companyLogo,
                        CompanyName = companyName,
                        Grade = GetGrade(0),
                        WeeklyPercentage = 0,
                        Rank = 0
                    });
                }
                else
                {
                    userWeeklyActivityScores.Add(new CompanyWeeklyAnalytics
                    {
                        UserId = highestScore.UserId,
                        CompanyLogo = companyLogo,
                        CompanyName = companyName,
                        Grade = GetGrade(highestScore.TotalPercentage),
                        WeeklyPercentage = highestScore.TotalPercentage,
                        Rank = 0
                    });
                }
            }

            // Assign the rank based on percentage
            userWeeklyActivityScores = userWeeklyActivityScores.OrderByDescending(x => x.WeeklyPercentage).ToList();
            for (int i = 0; i < userWeeklyActivityScores.Count; i++)
            {
                userWeeklyActivityScores[i].Rank = i + 1;
            }

            //Find the user's score and handle their positioning
            var userScoreEntry = userWeeklyActivityScores.FirstOrDefault(x => x.UserId == userId);
            if (userScoreEntry != null)
            {
                userWeeklyActivityScores.Remove(userScoreEntry);

                if (userScoreEntry.Rank <= 3)
                {
                    userWeeklyActivityScores.Insert(userScoreEntry.Rank - 1, userScoreEntry);
                }
                else
                {
                    userWeeklyActivityScores.Insert(3, userScoreEntry); // Place the user in the 4th position
                }
            }

            // Ensure the list is trimmed to a maximum of 7 results
            return userWeeklyActivityScores.Take(7).ToList();
        }


        /// <summary>
        /// Retrieves the weekly global activity scores for a specific user within a given date range.
        /// </summary>
        /// <param name="subdomain">The subdomain for database schema.</param>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="startDateRange">The optional start date for the date range. Defaults to the start of the current week if not provided.</param>
        /// <param name="endDateRange">The optional end date for the date range. Defaults to the end of the current week if not provided.</param>
        /// <returns>A list of <see cref="UserWeeklyActivityScore"/> representing the user's activity scores for the specified date range.</returns>
        /// <exception cref="OperationNotAllowedException">Thrown if the end date is earlier than the start date.</exception>
        public async Task<List<UserWeeklyActivityScore>> GetUserWeeklyCompanyActivityScore(string subdomain, string userId, DateTime? startDateRange, DateTime? endDateRange)
        {
            DateTime today = DateTime.Today;
            int offset = today.DayOfWeek - DayOfWeek.Monday;
            DateTime defaultStartDate = today.AddDays(-offset);
            DateTime defaultEndDate = defaultStartDate.AddDays(4);

            DateTime startDate = startDateRange ?? defaultStartDate;
            DateTime endDate = endDateRange ?? defaultEndDate;

            if (endDate < startDate)
            {
                throw new OperationNotAllowedException("End date must be greater than or equal to the start date.");
            }

            using var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));

            var allRoles = await context.UserAndRoleIds
                .Include(i => i.EmployeeRoles)
                .ToListAsync();

            UserProfile userProfile = await context.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (userProfile == null)
            {
                return new List<UserWeeklyActivityScore>();
            }

            var scores = await context.PerformanceMetricAnalyses
                .Where(x => x.StartDateForTheWeek >= startDate && x.EndDateForTheWeek <= endDate)
                .ToListAsync();

            var userIds = scores.Select(s => s.UserId).Distinct().ToList();
            var profiles = await context.UserProfiles
                .Where(p => userIds.Contains(p.UserId))
                .ToListAsync();

            var userWeeklyActivityScores = scores
                .Select(score =>
                {
                    var profile = profiles.FirstOrDefault(p => p.UserId == score.UserId);
                    var role = allRoles.FirstOrDefault(x => x.UserProId == score.UserId)?.EmployeeRoles.RoleName;
                    var userPhoto = string.IsNullOrEmpty(profile?.ProfilePictureUrl) ? "" : _aWSS3Sevices.GetSignedUrlAsync(profile?.ProfilePictureUrl).Result;

                    return new UserWeeklyActivityScore
                    {
                        UserId = score.UserId,
                        FirstName = profile?.FirstName,
                        LastName = profile?.LastName,
                        MiddleName = profile?.MiddleName,
                        ProfilePictureUrl = userPhoto,
                        Email = profile?.Email,
                        Grade = GetGrade(score.TotalPercentage),
                        WeeklyPercentage = score.TotalPercentage,
                        Role = role,
                        Rank = 0
                    };
                })
                .OrderByDescending(x => x.WeeklyPercentage)
                .ToList();

            // Assign rank based on the weekly percentage
            for (int i = 0; i < userWeeklyActivityScores.Count; i++)
            {
                userWeeklyActivityScores[i].Rank = i + 1;
            }

            // Find the user's score and move it to the top
            var userScore = userWeeklyActivityScores.FirstOrDefault(x => x.UserId == userId);
            if (userScore != null)
            {
                userWeeklyActivityScores.Remove(userScore);
                userScore.Rank = 1; // Reassign the rank to 1
                userWeeklyActivityScores.Insert(0, userScore);
            }

            // Ensure the list is trimmed to a maximum of 7 results, keeping the user's score at the top
            return userWeeklyActivityScores.Take(7).ToList();
        }


        private string GetGrade(double percentage)
        {
            return percentage switch
            {
                > 80 => "A",
                > 60 => "B",
                > 40 => "C",
                > 20 => "D",
                > 10 => "E",
                _ => "F"
            };
        }

        #endregion

        #region Private Methods - Notification
        private async Task<string> AddNotification(AddNotificationDto model)
        {
            var notification = new Notification.Models.Notification
            {
                Message = model.Message,
                Event = model.Event,
                EventId = model.EventId,
                CreatedAt = GetAdjustedDateTimeBasedOnTZNow(),
                CreatedBy = model.CreatedBy,
            };

            await Db.Notifications.AddAsync(notification);
            return notification.Id.ToString();
        }

        private async Task<bool> AddUserNotification(List<string> userIds, Guid notificationId)
        {
            var userProfileIds = await Db.UserProfiles.Where(x => userIds.Contains(x.UserId))
                .Select(x => x.Id.ToString()).ToListAsync();

            var userNotifications = new List<UserNotification>();
            foreach (var userId in userProfileIds)
            {
                userNotifications.Add(new UserNotification
                {
                    UserProfileId = userId,
                    NotificationId = notificationId
                });
            }

            Db.UserNotifications.AddRange(userNotifications);
            return true;
        }
        #endregion

        #region Get todo under a project by name
        public async Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_TodobyTodoName(PaginationParameters parameters, Guid projectId, string todoName)
        {
            return await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags).Where(x => x.ProjectMgmt_ProjectId == projectId && x.TodoName.Contains(todoName)).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }
        #endregion

        #region Get todos by kpi reference id
        public async Task<Page<ProjectMgmt_Todo>> GetAllTodoByKpiReferenceId(PaginationParameters parameters, long kpiId)
        {
            var projetcts = await Db.ProjectMgmt_Todo
                .Where(x => x.KpiReferenceId == kpiId)
                .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            return projetcts;
        }
        #endregion

        #region Get todos by company reference id
        public async Task<Page<ProjectMgmt_Todo>> GetAllTodoByCompanyReferenceId(PaginationParameters parameters, long companyId)
        {
            var projetcts = await Db.ProjectMgmt_Todo
                .Where(x => x.CompanyReferenceId == companyId)
                .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            return projetcts;
        }
        #endregion

        #region Get todos by lead reference id
        public async Task<Page<ProjectMgmt_Todo>> GetAllTodoByLeadReferenceId(PaginationParameters parameters, long LeadId)
        {
            var projetcts = await Db.ProjectMgmt_Todo
                .Where(x => x.LeadReferenceId == LeadId)
                .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            return projetcts;
        }
        #endregion

        #region Get todos by deal reference id
        public async Task<Page<ProjectMgmt_Todo>> GetAllTodoByDealReferenceId(PaginationParameters parameters, long DealId)
        {
            var projetcts = await Db.ProjectMgmt_Todo
                .Where(x => x.DealReferenceId == DealId)
                .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            return projetcts;
        }
        #endregion

        #region Get todos by Contact reference id
        public async Task<Page<ProjectMgmt_Todo>> GetAllTodoByContactReferenceId(PaginationParameters parameters, long ContactId)
        {
            var projetcts = await Db.ProjectMgmt_Todo
                .Where(x => x.ContactReferenceId == ContactId)
                .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            return projetcts;
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// Todo payload validations
        /// </summary>
        /// <param name="model"></param>
        /// <param name="project"></param>
        /// <param name="sprintProject"></param>
        /// <returns></returns>
        /// <exception cref="DirtyFormException"></exception>
        /// <exception cref="RecordNotFoundException"></exception>
        private void PayloadValidations(CreateTodoDto model, ProjectMgmt_Project project, out ProjectMgmt_Project sprintProject)
        {
            sprintProject = null;
            if (model.UploadFile.Any())
            {
                var allowedExtensions = new string[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx", ".doc" };
                foreach (var file in model.UploadFile)
                {
                    var extension = Path.GetExtension(file.FileName);

                    if (!allowedExtensions.Contains(extension.ToLower()))
                    {
                        throw new DirtyFormException("File type not supported");
                    }
                }
            }

            //if (model.EndTime <= model.StartDateAndTime || (int)(model.EndTime - model.StartDateAndTime).TotalHours > 24)
            //{
            //    throw new DirtyFormException((int)(model.EndTime - model.StartDateAndTime).TotalHours > 24 ? "Time allocation cannot be more than 24 hours" : "Invalid Date Range. Start time cannot be greater than nor equal to End time");
            //}

            if (project is not null)
            {
                // Check that the todo start date is within the project start and end date
                if (model.StartDateAndTime < project.StartDate || model.StartDateAndTime > project.EndDate)
                    throw new DirtyFormException($"Todo start date and time must be within: {project.StartDate.ToShortDateString()} {project.StartDate.ToShortTimeString()} and {project.EndDate.ToShortDateString()} {project.EndDate.ToShortTimeString()}");
            }

            if (model.SprintId is not null)
            {
                // Check if the todo name already exist under the sprint
                var doesTodoExist = Db.ProjectMgmt_Todo
                    .Where(todo => todo.SprintProjectId.ToString() == model.SprintId && todo.TodoName.ToLower() == model.TodoSummary.ToLower()).FirstOrDefault();
                if (doesTodoExist != null)
                    throw new DirtyFormException($"Todo with {model.TodoSummary} already exist under this sprint");

                var sprint = Db.SprintProjects.FirstOrDefaultAsync(x => x.Id.ToString() == model.SprintId).Result;
                if (sprint is null)
                    throw new RecordNotFoundException("Sprint not found");

                if (project is null)
                {
                    project = Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId == sprint.ProjectMgmt_ProjectId).Result;
                    if (project is null)
                        throw new RecordNotFoundException("Project not found");
                    sprintProject = project;
                }

                // Check that the todo start date is within the sprint start and end date
                if (model.StartDateAndTime < sprint.StartDate || model.StartDateAndTime > sprint.EndDate)
                    throw new DirtyFormException($"Todo start date and time must be within: {sprint.StartDate.ToShortDateString()} {sprint.StartDate.ToShortTimeString()} and {sprint.EndDate.ToShortDateString()} {sprint.EndDate.ToShortTimeString()}");
            }
        }

        /// <summary>
        /// This method checks if the logged in user has permission to perform the action
        /// </summary>
        /// <param name="createdBy"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        private async Task CheckPermission(string createdBy, string loggedInUserId)
        {
            // Check if the logged in user is a super admin
            var userRole = await _adminService.GetUserRole(loggedInUserId);
            if (createdBy != loggedInUserId && userRole != DataSeeder.SuperAdmin)
                throw new UnauthorizedAccessException("You are not authorized to perform this action");
        }

        /// <summary>
        /// Send out external notification to the user
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="project"></param>
        /// <param name="companyName"></param>
        /// <param name="tenantId"></param>
        /// <param name="invitee"></param>
        /// <param name="templatePath"></param>
        /// <param name="memberEmail"></param>
        /// <param name="todoId"></param>
        private void SendOutExternalNotification(string subdomain, ProjectMgmt_Project project, string companyName, Guid tenantId, string invitee, string templatePath, string memberEmail, Guid todoId)
        {
            string template = File.ReadAllText(templatePath);
            var encodedEmail = HttpUtility.UrlEncode(memberEmail);
            var inviteUrl = string.Format(Utility.Constants.INVITE_URL, subdomain, invitee, tenantId, Applications.Joble, encodedEmail) + $"&type={TypeForInviteUrl.Todo}&id={todoId}";

            template = template.Replace("{company}", companyName).Replace("{project}", project == null ? companyName : project.Name).Replace("{name}", invitee).Replace("{url}", inviteUrl);

            string subject = $"Invitation to JobPro - New Task Alert";
            var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, memberEmail, subject));
        }

        /// <summary>
        /// Send out external notification to the user with method overloading to include description
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="project"></param>
        /// <param name="companyName"></param>
        /// <param name="tenantId"></param>
        /// <param name="invitee"></param>
        /// <param name="templatePath"></param>
        /// <param name="memberEmail"></param>
        /// <param name="todoId"></param>
        /// <param name="todoSummary"></param>
        private void SendOutExternalNotification(string subdomain, ProjectMgmt_Project project, string companyName, Guid tenantId, string invitee, string templatePath, string memberEmail, Guid todoId, string todoSummary)
        {
            string template = File.ReadAllText(templatePath);
            var encodedEmail = HttpUtility.UrlEncode(memberEmail);
            var inviteUrl = string.Format(Utility.Constants.INVITE_URL, subdomain, invitee, tenantId.ToString(), Applications.Joble, encodedEmail, "") + $"&type={TypeForInviteUrl.Todo.ToString()}&id={todoId}";

            template = template.Replace("{company}", companyName).Replace("{project}", project == null ? companyName : project.Name).Replace("{name}", invitee).Replace("{url}", inviteUrl).Replace("{description}", todoSummary);

            string subject = $"Invitation to JobPro - New Task Alert";
            var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, memberEmail, subject));
        }

        /// <summary>
        /// Send out notification to the user
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="projectName"></param>
        /// <param name="companyName"></param>
        /// <param name="invitee"></param>
        /// <param name="todoId"></param>
        /// <param name="templatePath"></param>
        /// <param name="email"></param>
        /// <param name="todoSummary"></param>
        private void SendOutNotification(string subdomain, string projectName, string companyName, string invitee, Guid todoId, string templatePath, string email, string todoSummary)
        {
            string template = File.ReadAllText(templatePath);
            var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, subdomain) + $"/suite/pkg/project/todo-dashboard?type={TypeForInviteUrl.Todo}&id={todoId}";
            template = template.Replace("{company}", companyName).Replace("{project}", string.IsNullOrEmpty(projectName) ? companyName : projectName).Replace("{name}", invitee).Replace("{url}", url).Replace("{description}", todoSummary ?? "No description was provided");

            string subject = $"New Task Assigned: {todoSummary}";
            var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, email, subject));
        }

        private void SendInternalTodoNotification(string subdomain, ProjectMgmt_Project project, string invitee, Guid todoId, string email, ProjectMgmt_Todo todo, string assignedTo)
        {
            var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, subdomain) + $"/suite/pkg/project/todo-dashboard?type={TypeForInviteUrl.Todo}&id={todoId}";

            var parameters = new Dictionary<string, string>
            {
                { "{date}", todo.StartDateAndTime.ToShortDateString() },
                { "{project}", project == null ? "No Project" : project.Name },
                { "{time}", todo.StartDateAndTime.ToShortTimeString() },
                { "{link}", url },
                { "{duration}", todo.Duration == null ? "Duration not set yet" : todo?.Duration.ToString() },
                { "{description}", todo.TodoDescription ?? "No description was provided." },
                { "{creator}", invitee },
                { "{assigned-to}", assignedTo },
                { "{invitee}", invitee },
                { "{todo}", todo.TodoName.ToUpper() },
                { "{name}", assignedTo },
                { "{url}", url }
            };

            var template = UpdateTemplateWithParams("todo/todo-internal-invited-email", _environment, parameters);
            string subject = $"New Task Assigned: {todo.TodoSummary}";
            var taskId = BackgroundJob.Enqueue(() => _emailService.SendEmail(template, email, subject));
        }

        /// <summary>
        /// This method updates the todo order for a sprint
        /// </summary>
        /// <param name="sprintId"></param>
        /// <param name="todos"></param>
        /// <returns></returns>
        private async Task UpdateTodoOrderForSprint(string sprintId, List<ProjectMgmt_Todo> todos)
        {
            // Add todo to the todoOrder Table
            if (!string.IsNullOrEmpty(sprintId))
            {
                foreach (var todo in todos)
                {
                    var todosFromTodoOrder = Db.TodoOrder.Where(x => x.SprintId.ToString() == sprintId && x.Status == todo.TodoStatus).AsNoTracking()
                        .OrderByDescending(x => x.Order).ToList();

                    if (todosFromTodoOrder.Any(x => x.TodoId == todo.Id))
                        continue;

                    var todoOrder = new TodoOrder
                    {
                        TodoId = todo.Id,
                        SprintId = new Guid(sprintId),
                        Order = todosFromTodoOrder.Count > 0 ? todosFromTodoOrder[0].Order + 1 : 1,
                        Status = todo.TodoStatus
                    };

                    await Db.TodoOrder.AddAsync(todoOrder);
                    todo.TodoOrder = todoOrder;
                }

                await Db.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Deletes records from redis cache
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task DeleteDataFromRedis(List<string> ids)
        {
            var todoCacheKeys = new List<string>();
            if (GlobalVariables.CacheKeys.ContainsKey(_redisKey))
                todoCacheKeys = GlobalVariables.CacheKeys[_redisKey];

            foreach (var key in todoCacheKeys)
            {
                if (key.Split('_').ToList().Any(k => ids.Contains(k)))
                    await _redisCacheService.RemoveDataAsync(key);
            }
        }

        /// <summary>
        /// Create Todo Time Sequence
        /// </summary>
        /// <returns></returns>
        private async Task<List<TodoTimeSequence>> CreateTodoTimeSequence(string userId, ProjectMgmt_Todo todo)
        {
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);

            var personalSchedule = await Db.PersonalSchedule
                        .Where(p => p.UserId.ToString() == user.Id && p.Available && p.Day.ToLower() == todo.StartDateAndTime.Day.ToString().ToLower())
                        .FirstOrDefaultAsync();
            var duration = ConvertTodoDurationToHours(todo.Duration);
            var startTime = personalSchedule != null ? personalSchedule.StartTime : todo.StartDateAndTime.AddHours(8); // 8 AM          
            var availableHours = personalSchedule != null ? personalSchedule.EndTime - startTime : todo.StartDateAndTime.Date.AddHours(17) - startTime;
            var endOfDay = personalSchedule != null ? personalSchedule.EndTime : todo.StartDateAndTime.Date.AddHours(17);
            var endTime = availableHours.TotalHours >= duration ? startTime.AddHours(duration) : startTime.AddHours(availableHours.TotalHours);
            var daySequence = await Db.TodoTimeSequence.Where(x => x.StartTime.Date == todo.StartDateAndTime.Date && x.TodoStatus != Helpers.Models.TodoStatus.Completed.ToString())
                .OrderBy(x => x.EndTime).LastOrDefaultAsync();

            var sequences = new List<TodoTimeSequence>() { };

            for (int count = 0; count <= 10000; count++)
            {
                if (count > 0)
                {
                    personalSchedule = await Db.PersonalSchedule.Where(p => p.UserId.ToString() == user.Id && p.Available && p.Day.ToLower() == todo.StartDateAndTime.AddDays(count).Day.ToString().ToLower())
                                        .FirstOrDefaultAsync();
                    startTime = personalSchedule != null ? personalSchedule.StartTime : todo.StartDateAndTime.AddDays(count).Date.AddHours(8);
                    availableHours = personalSchedule != null ? personalSchedule.EndTime - startTime : todo.StartDateAndTime.AddDays(count).Date.AddHours(17) - startTime;
                    endTime = availableHours.TotalHours >= duration ? startTime.AddHours(duration) : startTime.AddHours(availableHours.TotalHours);
                    endOfDay = personalSchedule != null ? personalSchedule.EndTime : todo.StartDateAndTime.Date.AddHours(17);
                    daySequence = await Db.TodoTimeSequence.Where(x => x.StartTime.Date == todo.StartDateAndTime.AddDays(count).Date && x.TodoStatus != Helpers.Models.TodoStatus.Completed.ToString())
                        .OrderBy(x => x.EndTime).LastOrDefaultAsync();
                }

                if (todo.DueDate <= endTime)
                    throw new ArgumentException(user.FirstName + " does not have enough time to execute the todo with duration " + todo.Duration + " before the due date " + todo.DueDate.ToString());

                if (daySequence is not null)
                {
                    if (daySequence.EndTime >= endOfDay)   //If you already have todos that gets to the end of that day, move to the next day
                        continue;
                    startTime = daySequence.EndTime;
                    availableHours = endOfDay - startTime;
                    endTime = startTime.AddHours(availableHours.TotalHours);
                }


                var todoSequence = new TodoTimeSequence()
                {
                    ProjectMgmt_TodoId = todo.Id,
                    UserId = user.Id,
                    StartTime = startTime,
                    EndTime = endTime,
                    TodoStatus = todo.TodoStatus
                };

                duration = availableHours.TotalHours >= duration ? 0 : duration - availableHours.TotalHours;
                sequences.Add(todoSequence);
                if (duration == 0)
                    break;

            }
            await Db.TodoTimeSequence.AddRangeAsync(sequences);
            //await Db.SaveChangesAsync();
            return sequences;
        }

        private async Task<DateTime> CalculateOptimalTodoStartDate(TodoOptimalTimeCheck model)
        {
            // Initialize the optimal start time with the proposed start time from the model
            DateTime optimalStartTime = model.ProposedStartDateAndTime;

            // Loop until an optimal start time is found
            while (true)
            {
                // Check if there is any existing todo for the user that overlaps with the proposed start time
                var conflictingTodo = await Db.ProjectMgmt_Todo
                    .Include(x => x.ProjectMgmt_TodoUsers)
                    .FirstOrDefaultAsync(x => x.ProjectMgmt_TodoUsers.Any(y => y.UserId == model.UserId)
                                      && x.StartDateAndTime < optimalStartTime.AddHours(ConvertTodoDurationToHours(model.Duration))
                                      && x.EndTime > optimalStartTime);

                // If no conflicting todo is found, the optimal start time is available
                if (conflictingTodo == null)
                {
                    break;
                }

                // If a conflicting todo is found, set the optimal start time to the end time of the conflicting todo
                optimalStartTime = conflictingTodo.EndTime;
            }

            // Return the optimal start time
            return optimalStartTime;
        }

        private double ConvertTodoDurationToHours(string timeString)
        {
            if (string.IsNullOrWhiteSpace(timeString))
                throw new ArgumentException("Time string cannot be empty or null.");

            // Match patterns like "2h 30m", "1h", "45m", etc.
            var regex = new Regex(@"(?:(\d+)\s*h)?\s*(?:(\d+)\s*m)?", RegexOptions.IgnoreCase);
            var match = regex.Match(timeString.Trim());

            if (!match.Success)
                throw new ArgumentException("Invalid time format. Expected format like '2h 30m', '45m', etc.");

            double hours = 0;
            double minutes = 0;

            if (match.Groups[1].Success)
                hours = double.Parse(match.Groups[1].Value);

            if (match.Groups[2].Success)
                minutes = double.Parse(match.Groups[2].Value);

            return hours + (minutes / 60);
        }
        #endregion

        #region Add data to redis cache
        /// <summary>
        /// Add data to redis - Background job
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="req"></param>
        /// <param name="redisKey"></param>
        /// <param name="totalTodos"></param>
        /// <returns></returns>
        private async Task AddDataToRedis<T>(object req, string redisKey, T totalTodos)
        {
            var redisRes = await _redisCacheService.SetDataAsync(redisKey, totalTodos, DateTimeOffset.Now.AddDays(1));
            if (!redisRes)
                _logService.LogTypeResponse(req, totalTodos, nameof(AddDataToRedis), "Adding response to Redis failed after retries");
        }
        #endregion

        #region Bulk Add Todos Background Job
        /// <summary>
        /// This method bulk adds todos to the database
        /// </summary>
        /// <param name="todosToAdd"></param>
        /// <returns></returns>
        public async Task BulkTodoBackgroundJob(List<CreateTodoDto> todosToAdd)
        {
            foreach (var todo in todosToAdd)
            {
                await AddProjectMgmt_Todo(todo, null);
            }
        }
        #endregion

        #region User completed todo Analytics
        /// <summary>
        /// This method bulk adds todos to the database
        /// </summary>
        /// /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<TodoAnalyticsVM> GetUserTodoCompletedAnalyticsVM(string userId)
        {
            DateTime date = DateTime.Today;
            //DateTime date = new DateTime(2024,02,23);
            var weeklyAnalytics = new Dictionary<string, double>()
            {
                {DayOfWeek.Monday.ToString(), 0.0 },
                {DayOfWeek.Tuesday.ToString(), 0.0 },
                {DayOfWeek.Wednesday.ToString(), 0.0 },
                {DayOfWeek.Thursday.ToString(), 0.0 },
                {DayOfWeek.Friday.ToString(), 0.0 },
                {DayOfWeek.Saturday.ToString(), 0.0 }

            };
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");
            var todos = await Db.ProjectMgmt_Todo
                                .Include(x => x.ProjectMgmt_TodoUsers)
                                .Where(x => x.ProjectMgmt_TodoUsers.Any(y => y.UserId == userId || y.ExternalMemberEmail == user.Email))
                                .ToListAsync();
            if (!todos.Any())
                return new TodoAnalyticsVM
                {
                    TodoCompletedOnTime = 0,
                    TodoCompletionRateonTime = 0,
                    WeeklyAnalytics = weeklyAnalytics,
                    WeeklyTotalTodoCompletedOnTime = 0
                };

            var completedTodosOnTimeGeneral = todos.Where(x => x.DueDate >= x.EndTime && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).ToList();
            var completedTodoOnTimePercentage = (double)completedTodosOnTimeGeneral.Count / todos.Count * 100.0;

            int offset = date.DayOfWeek - DayOfWeek.Monday;
            DateTime lastMonday = date.AddDays(-offset);
            DateTime nextFriday = date.AddDays(5 - (int)date.DayOfWeek);
            var userWeeklyTodo = todos.Where(x => x.EndTime.Date <= nextFriday.Date && x.StartDateAndTime.Date >= lastMonday.Date).ToList();
            var userWeeklyCompletedTodo = todos.Where(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString() && x.LastUpdate >= lastMonday && x.LastUpdate <= nextFriday);
            var previousWeeklyTodo = todos.Where(x => x.EndTime.Date <= nextFriday.AddDays(-7).Date && x.StartDateAndTime.Date >= lastMonday.AddDays(-7).Date).ToList();
            var pendingTasksCount = todos.Where(x => x.TodoStatus == ProjectManagementStatus.Todo.ToString() && x.CreatedDate >= lastMonday && x.CreatedDate <= nextFriday).ToList();
            //var weeklyAnalytics = new Dictionary<string, double>();
            int weeklyTotalCompleted = 0;
            //int weeklyTotal = 0;
            int dayCount = 0;
            for (DateTime i = lastMonday; i <= date; i = i.AddDays(+1))
            {
                //If its a sunday, just return default figures and generalTodoCompletedOnTime
                if (i.DayOfWeek.Equals(DayOfWeek.Sunday))
                {
                    return new TodoAnalyticsVM
                    {
                        TodoCompletedOnTime = completedTodosOnTimeGeneral.Count,
                        TodoCompletionRateonTime = Math.Round(completedTodoOnTimePercentage, 0, MidpointRounding.AwayFromZero),
                        WeeklyAnalytics = weeklyAnalytics,
                        WeeklyTotalTodoCompletedOnTime = weeklyTotalCompleted,
                        TotalTodo = todos.Count
                    };
                }
                //Stop Once you go beyond today
                if (dayCount > offset) break;

                var dailyCompletedTodo = userWeeklyCompletedTodo.Where(x => x.LastUpdate.Date == i.Date).Count();
                // var dailyTodos = todos.Where(x => x.StartDateAndTime.Date == i.Date || x.EndTime.Date == i.Date || x.LastUpdate.Date == i.Date).Count();
                weeklyAnalytics[i.DayOfWeek.ToString()] = dailyCompletedTodo;
                weeklyTotalCompleted += dailyCompletedTodo;
                //weeklyTotal += dailyTodos;
                dayCount++;
            }
            var weeklyCompletionRate = weeklyTotalCompleted > userWeeklyTodo.Count ? 100.0 : (double)weeklyTotalCompleted / userWeeklyTodo.Count * 100.0;
            return new TodoAnalyticsVM
            {
                TodoCompletedOnTime = completedTodosOnTimeGeneral.Count,
                TodoCompletionRateonTime = Math.Round(completedTodoOnTimePercentage, 0, MidpointRounding.AwayFromZero),
                WeeklyAnalytics = weeklyAnalytics,
                WeeklyTotalTodoCompletedOnTime = weeklyTotalCompleted,
                WeeklyTotalTodo = userWeeklyTodo.Count,
                TotalTodo = todos.Count,
                TotalCompletedTodo = todos.Where(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Count(),
                WeeklyTotalTodoCompleted = userWeeklyCompletedTodo.Count(),
                WeeklyTodoCompletionRateOnTime = Math.Round(weeklyCompletionRate, 0, MidpointRounding.AwayFromZero),
                PendingTasksCount = pendingTasksCount.Count,
            };
        }
        #endregion

        #region User todo Speed And Stamina 
        /// <summary>
        /// Calculates the speed and stamina metrics for a user based on their todo history.
        /// Speed = percentage of todos completed on time
        /// Stamina = measure of user's productivity for the current day
        /// </summary>
        /// <param name="userId">The ID of the user to calculate metrics for</param>
        /// <returns>TodoSpeedStaminaVM containing Speed and Stamina values</returns>
        public async Task<TodoSpeedStaminaVM> GetTodoSpeedStamina(string userId)
        {
            // Validation - Find and verify user exists
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");
            
            // Get all todos assigned to the user (both internal and external)
            var todos = await Db.ProjectMgmt_Todo
                                .Include(x => x.ProjectMgmt_TodoUsers)
                                .Where(x => x.ProjectMgmt_TodoUsers.Any(y => y.UserId == userId || y.ExternalMemberEmail == user.Email) ||
                                            (x.ProjectMgmt_TodoUsers == null && x.CreatedBy == userId))
                                .ToListAsync();
                                
            // If no todos found, return 0 for both metrics
            if (!todos.Any())
                return new TodoSpeedStaminaVM
                {
                    Speed = 0,
                    Stamina = 0
                };
                
            // Calculate Speed metric: percentage of todos completed on or before due date
            int totalTodosWithDueDate = todos.Count(x => x.DueDate.HasValue);
            if (totalTodosWithDueDate == 0)
                totalTodosWithDueDate = 1; // Avoid division by zero
                
            var completedTodosOnTime = todos
                .Where(x => x.CompletedAt.HasValue && 
                           x.DueDate.HasValue && 
                           x.DueDate.Value >= x.CompletedAt.Value && 
                           x.TodoStatus == ProjectManagementStatus.Completed.ToString())
                .ToList();
            
            var speed = (double)completedTodosOnTime.Count / totalTodosWithDueDate * 100.0;
            
            // Calculate Stamina metric
            DateTime now = DateTime.UtcNow;
            DateTime localNow = GetAdjustedDateTimeBasedOnTZNow(); // Use timezone adjusted time
            DateTime startOfWorkDay = new DateTime(localNow.Year, localNow.Month, localNow.Day, 9, 0, 0); // 9 AM
            DateTime endOfWorkDay = new DateTime(localNow.Year, localNow.Month, localNow.Day, 17, 0, 0);  // 5 PM
            
            // Default stamina to 100% outside of working hours
            var stamina = 100.0;
            
            // If before work hours, return full stamina with calculated speed
            if (localNow.Hour < 9)
                return new TodoSpeedStaminaVM
                {
                    Speed = Math.Round(speed, 0, MidpointRounding.AwayFromZero),
                    Stamina = stamina
                };
                
            // Calculate elapsed working hours (cap at 8 hours if after work hours)
            double hoursGone;
            if (localNow >= startOfWorkDay && localNow <= endOfWorkDay)
            {
                hoursGone = (localNow - startOfWorkDay).TotalHours;
            }
            else if (localNow > endOfWorkDay)
            {
                hoursGone = 8.0; // Full work day
            }            else
            {
                hoursGone = 0.1; // Minimal value to avoid division by zero
            }
            
            // Get todos worked on today
            var todayTodos = todos.Where(x => 
                // Created today
                x.CreatedDate.Date == localNow.Date || 
                // Or worked on/completed today
                (x.CompletedAt.HasValue && x.CompletedAt.Value.Date == localNow.Date) ||
                // Or has time entries for today
                (!string.IsNullOrEmpty(x.ActualTimeSpent) && x.LastUpdate.Date == localNow.Date)
            ).ToList();
            
            if (!todayTodos.Any())
                return new TodoSpeedStaminaVM
                {
                    Speed = Math.Round(speed, 0, MidpointRounding.AwayFromZero),
                    Stamina = 0.0
                };

            // Calculate accumulated productive hours from todos
            var accumulatedHours = 0.0;
            foreach (var todo in todayTodos)
            {
                double hours = 0;

                // If completed today, use the duration
                if (todo.CompletedAt.HasValue && todo.CompletedAt.Value.Date == localNow.Date && 
                   !string.IsNullOrEmpty(todo.Duration))
                {
                    try
                    {
                        hours = ConvertTodoDurationToHours(todo.Duration);
                    }
                    catch
                    {
                        // If conversion fails, try using TimeSpent
                        if (!string.IsNullOrEmpty(todo.TimeSpent) && TimeSpan.TryParse(todo.TimeSpent, out var ts))
                        {
                            hours = ts.TotalHours;
                        }
                    }
                }
                // Otherwise, use time spent
                else if (!string.IsNullOrEmpty(todo.TimeSpent) && TimeSpan.TryParse(todo.TimeSpent, out var ts))
                {
                    // If updated today, use the full time spent
                    if (todo.LastUpdate.Date == localNow.Date)
                    {
                        hours = ts.TotalHours;
                    }
                }
                // If no time data, but worked on today, use 1 hour as default
                else if (todo.LastUpdate.Date == localNow.Date)
                {
                    hours = 1.0;
                }

                accumulatedHours += hours;
            }

            // Get all meetings for the day
            // First get calendar meetings where user is a participant
            var meetingIds = await Db.UserIdMeetingIds
                .Where(x => x.UserId == userId)
                .Select(x => x.CalenderId)
                .ToListAsync();

            // Get calendar meetings including those created by this user
            var meetings = await Db.CalenderMeetings
                .Where(x => (meetingIds.Contains(x.Id.ToString()) || x.CreatedBy.ToString() == userId) &&
                       x.ScheduleType == Helpers.Enums.CalenderScheduleType.Meeting &&
                       x.IsCancelled == false && 
                       x.StartDate.Date == localNow.Date)
                .ToListAsync();
                
            // Consolidate meeting IDs
            meetingIds.AddRange(meetings.Select(x => x.Id.ToString()));
            meetingIds = meetingIds.Distinct().ToList();
            
            // Get meeting durations
            var meetingDurations = meetings
                .Where(x => x.MeetLength != null)
                .Select(x => x.MeetLength)
                .ToList();

            // Add subsequent meeting durations
            meetingDurations.AddRange(await Db.SubsequentMeetings
                .Where(x => meetingIds.Contains(x.CalenderMeetingId.ToString()) && 
                       x.IsCanceled == false && 
                       x.SubsequentMeetingDateTime.Date == localNow.Date)
                .Select(x => x.MeetLength)
                .ToListAsync());

            // Add meeting hours to accumulated hours
            foreach (var meetingDuration in meetingDurations.Where(m => m != null))
            {
                try
                {
                    accumulatedHours += ConvertTodoDurationToHours(meetingDuration.ToString());
                }
                catch
                {
                    // Default to 1 hour if conversion fails
                    accumulatedHours += 1.0;
                }
            }

            // Get external meetings
            var externalMeetingIds = await Db.ExternalMeeting
                .Where(x => x.CreatedBy.ToString() == userId)
                .Select(x => x.Id.ToString())
                .ToListAsync();

            // Get booked external meetings for today
            var bookedMeetingIds = await Db.BookedExternalMeeting
                .Where(x => x.IsCancelled == false && 
                       externalMeetingIds.Contains(x.ExternalMeetingId.ToString()) && 
                       x.SelectedDateAndTime.Date == localNow.Date)
                .Select(x => x.Id.ToString())
                .ToListAsync();

            // Add meetings where user is a member
            bookedMeetingIds.AddRange(await Db.BookedMeetingMembers
                .Where(x => x.UserId == userId)
                .Select(x => x.BookedMeetingId)
                .ToListAsync());
                
            bookedMeetingIds = bookedMeetingIds.Distinct().ToList();
            
            // Get external meeting durations
            var bookedMeetingDurations = await Db.BookedExternalMeeting
                .Where(x => bookedMeetingIds.Contains(x.Id.ToString()))
                .Select(b => b.DurationInMinutes)
                .ToListAsync();

            // Add external meeting hours
            foreach (var meetingDuration in bookedMeetingDurations.Where(d => d > 0))
            {
                // Convert minutes to hours
                accumulatedHours += meetingDuration / 60.0;
            }

            // Calculate stamina percentage
            // Cap at 100% but don't go below 0%
            stamina = Math.Min(100.0, Math.Max(0.0, (accumulatedHours / hoursGone) * 100.0));
            
            return new TodoSpeedStaminaVM
            {
                Speed = Math.Round(speed, 0, MidpointRounding.AwayFromZero),
                Stamina = Math.Round(stamina, 0, MidpointRounding.AwayFromZero)
            };
        }
        #endregion

        #region User Monthly Todo Completed by weeks analytics
        public async Task<MonthlyTodoCompletedByWeek> GetMonthlyTodoCompletedByWeek(string userId)
        {
            MonthlyTodoCompletedByWeek todoDetails = new MonthlyTodoCompletedByWeek();
            DateTime now = DateTime.UtcNow;

            DateTime firstDayOfMonth = new DateTime(now.Year, now.Month, 1);
            int offset = DayOfWeek.Saturday - firstDayOfMonth.DayOfWeek;
            DateTime lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            DateTime firstDayOfWeek = firstDayOfMonth;
            DateTime lastDayOfCurrentWeek = firstDayOfWeek.AddDays(offset);
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");
            var todos = await Db.ProjectMgmt_Todo
                                .Include(x => x.ProjectMgmt_TodoUsers)
                                .Where(x => x.ProjectMgmt_TodoUsers.Any(y => y.UserId == userId || y.ExternalMemberEmail == user.Email))
                                .ToListAsync();
            todos.AddRange(await Db.ProjectMgmt_Todo
                                .Include(x => x.ProjectMgmt_TodoUsers)
                                .Where(x => x.ProjectMgmt_TodoUsers == null && x.CreatedBy == userId)
                                .ToListAsync());
            if (todos.Count == 0)
                return todoDetails;

            int weekCount = 1;
            while (weekCount < 6)
            {
                WeeklyTodoCompletedDto completedWeekTodos = new WeeklyTodoCompletedDto();
                var currentWeekTodos = todos.Where(x => x.StartDateAndTime >= firstDayOfWeek && x.StartDateAndTime <= lastDayOfCurrentWeek && x.EndTime <= lastDayOfMonth).ToList();
                completedWeekTodos.Completed = currentWeekTodos.Where(x => x.TodoStatus == "Completed").Count();
                completedWeekTodos.Pending = currentWeekTodos.Where(x => x.TodoStatus != "Completed").Count();

                switch (weekCount)
                {
                    case 1:
                        todoDetails.WeekOne = completedWeekTodos;
                        break;
                    case 2:
                        todoDetails.WeekTwo = completedWeekTodos;
                        break;
                    case 3:
                        todoDetails.WeekThree = completedWeekTodos;
                        break;
                    case 4:
                        todoDetails.WeekFour = completedWeekTodos;
                        break;
                    case 5:
                        todoDetails.WeekFive = completedWeekTodos;
                        break;

                }
                weekCount += 1;
                firstDayOfWeek = lastDayOfCurrentWeek.AddDays(1);
                lastDayOfCurrentWeek = firstDayOfWeek.AddDays(6) <= lastDayOfMonth ? firstDayOfWeek.AddDays(6) : lastDayOfMonth;

            }
            return todoDetails;
        }
        #endregion

        #region User Daily Chat Count
        private async Task<Dictionary<DateTime, int>> GetMessageCount(string userId, string subdomain, string token)
        {
            DateTime date = DateTime.Today;

            var weeklyTodoMessageCount = new Dictionary<DateTime, int>();
            var weekDays = new List<DateTime>();
            int offset = date.DayOfWeek - DayOfWeek.Monday;
            DateTime lastMonday = date.AddDays(-offset);
            DateTime nextFriday = date.AddDays(5 - (int)date.DayOfWeek);
            for (DateTime i = lastMonday; i <= date; i = i.AddDays(1))
            {
                if (i <= nextFriday)
                    weekDays.Add(i);
            }
            Parallel.ForEach(weekDays, async day =>
            {
                var pathNow = userId + "?startDate=" + day.ToString() + "&endDate=" + day.AddHours(18);
                var pathPast = userId + "?startDate=" + day.AddDays(-7).ToString() + "&endDate=" + day.AddDays(-7).AddHours(18);
                var headers = new Dictionary<string, string>()
                {
                   {"subdomain", subdomain },
                   {"Authorization", token }
                };
                var countNow = await _apiCallService.MakeApiCallGenericAsync<object, GetMessageCountDto>(Utility.Constants.CHAT_SERVICE_BASEURL, pathNow, Method.Get, null, headers);
                var countPast = await _apiCallService.MakeApiCallGenericAsync<object, GetMessageCountDto>(Utility.Constants.CHAT_SERVICE_BASEURL, pathPast, Method.Get, null, headers);

                if (countNow != null && countNow.messageCount > 0)
                    weeklyTodoMessageCount.Add(day.Date, countNow.messageCount);
                if (countPast != null && countPast.messageCount > 0)
                    weeklyTodoMessageCount.Add(day.AddDays(-7), countPast.messageCount);

            });
            return weeklyTodoMessageCount;
        }
        #endregion

        #region User User Todo Analytics By Status
        public async Task<TodoStatusAnalyticsDictionary> GetUserTodoAnalyticsByStatus(string userId, DateTime startDate, DateTime? endDate = null)
        {
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            // Set default end date to today if not provided
            DateTime actualEndDate = endDate ?? DateTime.UtcNow;

            // Make sure startDate doesn't exceed endDate
            if (startDate > actualEndDate)
                throw new OperationNotAllowedException("Start date cannot be greater than end date");

            // Get all todos for the user within date range
            var todos = await Db.ProjectMgmt_Todo
                                .Include(x => x.ProjectMgmt_TodoUsers)
                                .Where(x => (x.ProjectMgmt_TodoUsers.Any(y => y.UserId == userId || y.ExternalMemberEmail == user.Email) || x.CreatedBy == userId) &&
                                       x.StartDateAndTime >= startDate && x.StartDateAndTime <= actualEndDate)
                                .ToListAsync();

            if (todos.Count == 0)
                throw new RecordNotFoundException("You do not have any todos in the selected date range");

            DateTime now = DateTime.UtcNow;
            var response = new TodoStatusAnalyticsDictionary();
            var personal = new TodoStatusAnalyticsVM()
            {
                Todo = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Todo.ToString().ToUpper()).Count(),
                InProgress = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.InProgress.ToString().ToUpper()).Count(),
                Completed = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Completed.ToString().ToUpper()).Count(),
                Overdue = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.OverDue.ToString().ToUpper()).Count(),
                Pending = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Pending.ToString().ToUpper()).Count()
            };

            var personalToday = new TodoStatusAnalyticsVM()
            {
                Todo = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Todo.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                InProgress = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.InProgress.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                Completed = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Completed.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                Overdue = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.OverDue.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                Pending = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Pending.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count()
            };
            response.Personal = personal;
            response.PersonalToday = personalToday;

            // Get all todos created by the user within date range
            var createdTodos = await Db.ProjectMgmt_Todo
                                    .Where(x => x.CreatedBy == userId &&
                                           x.StartDateAndTime >= startDate && x.StartDateAndTime <= actualEndDate)
                                    .ToListAsync();

            // Combine and deduplicate todos
            todos.AddRange(createdTodos);
            todos = todos.Distinct().ToList();

            var general = new TodoStatusAnalyticsVM()
            {
                Todo = todos.Distinct().Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Todo.ToString().ToUpper()).Count(),
                InProgress = todos.Distinct().Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.InProgress.ToString().ToUpper()).Count(),
                Completed = todos.Distinct().Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Completed.ToString().ToUpper()).Count(),
                Overdue = todos.Distinct().Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.OverDue.ToString().ToUpper()).Count(),
                Pending = todos.Distinct().Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Pending.ToString().ToUpper()).Count()
            };
            var generalToday = new TodoStatusAnalyticsVM()
            {
                Todo = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Todo.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                InProgress = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.InProgress.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                Completed = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Completed.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                Overdue = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.OverDue.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count(),
                Pending = todos.Where(x => x.TodoStatus.ToUpper() == Helpers.Models.TodoStatus.Pending.ToString().ToUpper() && x.StartDateAndTime.Date == now.Date).Count()
            };
            response.General = general;
            response.GeneralToday = generalToday;
            return response;
        }
        #endregion

        #region User User Todo Optimal Start Time
        public async Task<List<TodoOptimalTimeResponse>> GetUserTodoStartOptimalTime(List<TodoOptimalTimeCheck> model)
        {
            var response = new List<TodoOptimalTimeResponse>();
            foreach (var check in model)
            {
                var optimalTime = await CalculateOptimalTodoStartDate(check);
                var optimalTimeObject = new TodoOptimalTimeResponse()
                {
                    UserId = check.UserId,
                    OptimalStartDateAndTime = optimalTime
                };
                response.Add(optimalTimeObject);
            }
            return response;
        }

        #endregion
    }
}
