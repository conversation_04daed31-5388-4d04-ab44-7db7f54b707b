﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Notification.Contracts;
using Jobid.App.Notification.Models;
using Jobid.App.Notification.ViewModel;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Notification.Repository
{
    public class NotificationsService : INotificationsService
    {
        private readonly JobProDbContext _context;
        private readonly IAWSS3Sevices _aWSS3Sevices;

        public NotificationsService(JobProDbContext context, IAWSS3Sevices aWSS3Sevices)
        {
            _context = context;
            _aWSS3Sevices = aWSS3Sevices;
        }

        #region Add Notification
        public async Task<string> AddNotification(AddNotificationDto model)
        {
            var notification = new Notification.Models.Notification
            {
                Message = model.Message,
                Event = model.Event,
                EventId = model.EventId,
                CreatedAt = DateTime.UtcNow.AddHours(1),
                CreatedBy = model.CreatedBy
            };

            _context.Notifications.Add(notification);
            var res = await _context.SaveChangesAsync();

            return res > 0 ? notification.Id.ToString() : null;
        }
        #endregion

        #region Add User Notification
        public async Task<bool> AddUserNotification(List<string> userIds, Guid notificationId)
        {
            var userProfileId = await _context.UserProfiles.Where(x => userIds.Contains(x.Id))
                .Select(x => x.Id.ToString()).ToListAsync();

            var userNotifications = new List<UserNotification>();
            foreach (var userId in userProfileId)
            {
                userNotifications.Add(new UserNotification
                {
                    UserProfileId = userId,
                    NotificationId = notificationId
                });
            }

            _context.UserNotifications.AddRange(userNotifications);
            var res = await _context.SaveChangesAsync();

            return res > 0;
        }
        #endregion

        #region Get User Notifications
        public async Task<IEnumerable<UserNotification>> GetUserNotifications(Guid userId)
        {
            var userProfileId = await _context.UserProfiles.Where(x => x.UserId == userId.ToString())
                .Select(x => x.Id.ToString()).FirstOrDefaultAsync();

            var userNotofications = await _context.UserNotifications.Include(x => x.Notification)
                 .Where(x => x.UserProfileId == userProfileId && !x.Disappear).OrderByDescending(x => x.CreatedOn).ToListAsync();

            foreach (var notification in userNotofications)
            {
                if (notification.Notification.CreatedBy is not null)
                {
                    notification.Notification.CreatedByProfileDetails = await _context.UserProfiles
                          .Where(x => x.UserId == notification.Notification.CreatedBy)
                          .Select(x => new UserDto
                          {
                              Id = x.Id.ToString(),
                              FirstName = x.FirstName,
                              LastName = x.LastName,
                              Email = x.Email,
                              PhoneNumber = x.PhoneNumber,
                              ProfileUrl = x.ProfilePictureUrl
                          }).FirstOrDefaultAsync();

                    notification.Notification.CreatedByProfileDetails.ProfileUrl = !string.IsNullOrEmpty(notification.Notification.CreatedByProfileDetails.ProfileUrl) ? _aWSS3Sevices.GetSignedUrlAsync(notification.Notification.CreatedByProfileDetails.ProfileUrl).Result : null;
                }
            }

            return userNotofications;
        }
        #endregion

        #region Mark Notification As Read
        public async Task<bool> MarkNotificationAsRead(Guid notificationId, Guid userId, bool markAllAsRead)
        {
            var userProfileId = await _context.UserProfiles.Where(x => x.UserId == userId.ToString())
               .Select(x => x.Id.ToString()).FirstOrDefaultAsync();

            if (markAllAsRead)
            {
                var userNotifications = _context.UserNotifications.Where(x => x.UserProfileId == userProfileId && !x.Disappear).ToList();
                foreach (var userNotification in userNotifications)
                {
                    userNotification.IsRead = true;
                    userNotification.ReadOn = DateTime.UtcNow;

                    _context.UserNotifications.UpdateRange(userNotifications);
                }
            }
            else
            {
                var userNotification = _context.UserNotifications.FirstOrDefault(x => x.NotificationId == notificationId && x.UserProfileId == userProfileId);
                userNotification.IsRead = true;
                userNotification.ReadOn = DateTime.UtcNow;

                _context.UserNotifications.Update(userNotification);
            }

            var res = await _context.SaveChangesAsync();

            return res > 0;
        }
        #endregion

        #region Mark All Notifications As Read
        /// <summary>
        /// Mark all notifications as read for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> MarkAllNotificationsAsRead(Guid userId)
        {
            try
            {
                var userProfileId = await _context.UserProfiles
                    .Where(x => x.UserId == userId.ToString())
                    .Select(x => x.Id.ToString())
                    .FirstOrDefaultAsync();

                if (string.IsNullOrEmpty(userProfileId))
                {
                    return false; // User profile not found
                }

                var userNotifications = await _context.UserNotifications
                    .Where(x => x.UserProfileId == userProfileId && !x.IsRead && !x.Disappear)
                    .ToListAsync();

                if (!userNotifications.Any())
                {
                    return true; // No unread notifications to mark as read
                }

                foreach (var userNotification in userNotifications)
                {
                    userNotification.IsRead = true;
                    userNotification.ReadOn = DateTime.UtcNow;
                }

                _context.UserNotifications.UpdateRange(userNotifications);
                var result = await _context.SaveChangesAsync();

                return result > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }
        #endregion
    }
}
