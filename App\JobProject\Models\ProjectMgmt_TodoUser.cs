﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Models
{
    public class ProjectMgmt_TodoUser
    {
        [Key]
        public Guid Id { get; set; }
        public Guid ProjectMgmt_TodoId { get; set; }
        public decimal? AmountPerHour { get; set; }
        public ProjectMgmt_Todo ProjectMgmt_Todo { get; set; }
        public string UserId { get; set; }
        public string ExternalMemberEmail { get; set; }
        public string CurrencySymbol { get; set; } = Currency.USD.ToString();
    }
}
