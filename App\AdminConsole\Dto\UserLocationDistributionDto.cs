using System.Collections.Generic;

namespace Jobid.App.AdminConsole.DTO
{
    /// <summary>
    /// DTO for user distribution by location
    /// </summary>
    public class UserLocationDistributionDto
    {
        /// <summary>
        /// Dictionary with country as key and count as value
        /// </summary>
        public Dictionary<string, int> CountryDistribution { get; set; } = new Dictionary<string, int>();
        
        /// <summary>
        /// Total number of users
        /// </summary>
        public int TotalUsers { get; set; }
        
        /// <summary>
        /// Number of users with known location
        /// </summary>
        public int UsersWithLocation { get; set; }
        
        /// <summary>
        /// Number of users with unknown location
        /// </summary>
        public int UsersWithoutLocation { get; set; }
    }
}
