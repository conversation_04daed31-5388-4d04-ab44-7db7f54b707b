﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Jobid.Migrations
{
    public partial class update_on_entities : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public update_on_entities(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ProjectMgmt_Projects_Companies_CompanyId",
                schema: _Schema,
                table: "ProjectMgmt_Projects");

            migrationBuilder.DropForeignKey(
                name: "FK_VendorWallets_PayrollVendors_PayrollVendorId",
                schema: _Schema,
                table: "VendorWallets");

            migrationBuilder.DropTable(
                name: "AccountWalletBeneficiaries",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Actors",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "AutoSaveTransactions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Beneficiaries",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "BridgeCardTransactionHistories",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CandidateSummary",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Cards",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycAddresses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycCategories",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycDataClasses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycDocuments",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycDynamicFields",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycSignatories",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanyKycUBOs",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "ContactNoteNoteOwner",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CreditTransactions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMAudioRecordings",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCampaignOnwer",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCampaignSequences",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCollaborator",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMComments",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCompanyCRMCompanyCollaborator",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCompanyCRMCompanyTag",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMContactCRMContactCollaborator",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMContactCRMContactTag",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMContactOwners",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMDealCRMDealCollaborator",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMDealStatuses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMEmail",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMEmailRecords",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiActivities",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiCollaborators",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiExpectedOutComes",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiIncentives",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiMeasurements",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiProcesses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpiSources",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMLeadCRMLeadCollaborator",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMLeadCRMTag",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMLeadOwners",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMNotification",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMPackagePricings",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMPricingAndFeatures",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMSequenceMails",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMSocialMedias",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMSources",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMStripeSubscriptionDetails",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMSubscriptions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMTagCRMTodo",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMTasks",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "DataClassCategories",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "DealActivity",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "DealEmail",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "DealsContacts",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "EmailSignatures",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "EmailTemplates",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "EmployeeKycDynamicFields",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "EventNodes",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Events",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "IncomingSms",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "InterviewStage",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysAppSettings",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysCards",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysClientCompanyAddresses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysCompanySubscriptions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysEnterpriseSubscriptions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysInvite",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysPackagePricing",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysPricingAndFeatures",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysStripeSubscriptionDetails",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysSubscriptionHistory",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobSkill",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KpiCollaborators",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KpiExpectedOutcome",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KycCategories",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KycDataClasses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KycEmployeeGuarantors",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "LeadNTag",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "LeadTransferHistory",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Module",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "NFCs",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "NoteUploads",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "OutgoingSms",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PayrollServices",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PayrollTransactions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PhoneNumberTransactions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Profiles",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "QuestionOption",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "SalePerson",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "ScheduledActionNodes",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "ScheduledActions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "SimSettings",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Subscriber",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "TempUsers",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Transactions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "UnregisteredUserCompanies",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "VoiceNoteUploads",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "weavrCorpKycs",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "WorkingHours",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMPhoneNumbers",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "AutoSaveSettings",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "BeneficiaryBatches",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompaniesKYCs",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "NoteOwners",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "SimWallets",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMMeetings",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCompanyCollaborators",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCompanies",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCompanyTags",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMContactCollaborator",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMContactTags",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMDealCollaborators",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMDeals",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMKpis",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMLeadCollaborators",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMLeads",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMEmailSequences",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMBuisinessCards",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMBillingAddresses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMPricingPlans",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMTags",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMTodos",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Contacts",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Deals",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysClientCompanies",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysSubscriptions",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysFeature",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Kpis",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Categories",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "DataClasses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Tags",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysAccountClasses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KYCs",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PayrollRequests",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobQuestion",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "AccountWallets",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "ContactNotes",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "WorkingDays",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Capabilities",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMCampaigns",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobApplication",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Leads",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysBillingAddresses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysPricingPlans",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KpiDepartment",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KpiProcess",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "KpiSource",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobPaysAccounts",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMContacts",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CRMWorkSchedules",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "JobVacancy",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Campaigns",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Companies",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Location",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CampaignsTemplates",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "Sequences",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PayrollVendors",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "VendorWallets",
                schema: _Schema);

            migrationBuilder.DropIndex(
                name: "IX_ProjectMgmt_Projects_CompanyId",
                schema: _Schema,
                table: "ProjectMgmt_Projects");

            migrationBuilder.DropColumn(
                name: "AccountType",
                schema: _Schema,
                table: "UserProfiles");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AccountType",
                schema: _Schema,
                table: "UserProfiles",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "AutoSaveSettings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    All = table.Column<bool>(type: "boolean", nullable: false),
                    DateStarted = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Deposit = table.Column<bool>(type: "boolean", nullable: false),
                    InterestRatePA = table.Column<double>(type: "double precision", nullable: false),
                    MaturityDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    PercentageToSave = table.Column<int>(type: "integer", nullable: false),
                    Salary = table.Column<bool>(type: "boolean", nullable: false),
                    Transfer = table.Column<bool>(type: "boolean", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoSaveSettings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoSaveSettings_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CandidateSummary",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Summary = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateSummary", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CandidateSummary_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Capabilities",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    MMS = table.Column<bool>(type: "boolean", nullable: false),
                    SMS = table.Column<bool>(type: "boolean", nullable: false),
                    Voice = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Capabilities", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Categories",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    IsPredefined = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Companies",
                schema: _Schema,
                columns: table => new
                {
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    Address = table.Column<string>(type: "text", nullable: true),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    EmailAddress = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Location = table.Column<string>(type: "text", nullable: true),
                    LogoUrl = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Size = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    VATReistration = table.Column<string>(type: "text", nullable: true),
                    Website = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Companies", x => x.CompanyId);
                    table.ForeignKey(
                        name: "FK_Companies_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CompaniesKYCs",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    BusinessName = table.Column<string>(type: "text", nullable: true),
                    CompanyDetailsDynamicFieldsJson = table.Column<string>(type: "text", nullable: true),
                    CompanyEmail = table.Column<string>(type: "text", nullable: true),
                    CompanyLogo = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<string>(type: "text", nullable: true),
                    Dispute = table.Column<int>(type: "integer", nullable: false),
                    ExpiredDate = table.Column<string>(type: "text", nullable: true),
                    HighTrustDynamicFieldsJson = table.Column<string>(type: "text", nullable: true),
                    IndustryType = table.Column<string>(type: "text", nullable: true),
                    InviteeName = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsInvited = table.Column<bool>(type: "boolean", nullable: false),
                    LastUpdate = table.Column<string>(type: "text", nullable: true),
                    LegalFormEntity = table.Column<string>(type: "text", nullable: true),
                    Location = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    RegisteredName = table.Column<string>(type: "text", nullable: true),
                    RegistrationNumber = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Tag = table.Column<int>(type: "integer", nullable: false),
                    TradeName = table.Column<string>(type: "text", nullable: true),
                    VATNumber = table.Column<string>(type: "text", nullable: true),
                    VerificationLevel = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompaniesKYCs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycDynamicFields",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DataType = table.Column<string>(type: "text", nullable: true),
                    FieldName = table.Column<string>(type: "text", nullable: true),
                    FieldType = table.Column<int>(type: "integer", nullable: false),
                    TrustLevel = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycDynamicFields", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Contacts",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AnnualRevenue = table.Column<decimal>(type: "numeric", nullable: false),
                    CompanyName = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    EmailAddress = table.Column<string>(type: "text", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    Identifier = table.Column<string>(type: "text", nullable: true),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Contacts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMAudioRecordings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ContactId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    FileName = table.Column<string>(type: "text", nullable: true),
                    FilePath = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMAudioRecordings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMBillingAddresses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    City = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    PostalCode = table.Column<string>(type: "text", nullable: true),
                    Region = table.Column<string>(type: "text", nullable: true),
                    StreetName = table.Column<string>(type: "text", nullable: true),
                    StreetNumber = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMBillingAddresses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMBuisinessCards",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BookingLink = table.Column<string>(type: "text", nullable: true),
                    Company = table.Column<string>(type: "text", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Location = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PictureUrl = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMBuisinessCards", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMCampaigns",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Action = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    StartDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCampaigns", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMCampaignSequences",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Action = table.Column<string>(type: "text", nullable: true),
                    CRMCampaignSequenceId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsCondition = table.Column<bool>(type: "boolean", nullable: false),
                    PreviousActionId = table.Column<long>(type: "bigint", nullable: true),
                    PreviousActionRequired = table.Column<bool>(type: "boolean", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCampaignSequences", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMCampaignSequences_CRMCampaignSequences_CRMCampaignSequen~",
                        column: x => x.CRMCampaignSequenceId,
                        principalSchema: _Schema,
                        principalTable: "CRMCampaignSequences",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMCompanies",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Address = table.Column<string>(type: "text", nullable: true),
                    CompanySize = table.Column<int>(type: "integer", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealSize = table.Column<int>(type: "integer", nullable: true),
                    DealType = table.Column<string>(type: "text", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DueDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    EmailAddress = table.Column<string>(type: "text", nullable: true),
                    IsArchive = table.Column<bool>(type: "boolean", nullable: false),
                    IsConvertedToDeals = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    LogoUrl = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    VATRegistration = table.Column<string>(type: "text", nullable: true),
                    Website = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCompanies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMCompanyCollaborators",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Email = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCompanyCollaborators", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMCompanyTags",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCompanyTags", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMContactCollaborator",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Email = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMContactCollaborator", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMContacts",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Address = table.Column<string>(type: "text", nullable: true),
                    Company = table.Column<string>(type: "text", nullable: true),
                    CompanyId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealReferenceId = table.Column<long>(type: "bigint", nullable: true),
                    Details = table.Column<string>(type: "text", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    IsArchived = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    LeadReferenceId = table.Column<long>(type: "bigint", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMContacts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMContactTags",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMContactTags", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMDealCollaborators",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Email = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMDealCollaborators", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMDeals",
                schema: _Schema,
                columns: table => new
                {
                    DealId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Company = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateConverted = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsConverted = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    ReferenceId = table.Column<long>(type: "bigint", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMDeals", x => x.DealId);
                });

            migrationBuilder.CreateTable(
                name: "CRMEmailSequences",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Action = table.Column<string>(type: "text", nullable: true),
                    Condition = table.Column<string>(type: "text", nullable: true),
                    ConditionType = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsCondition = table.Column<bool>(type: "boolean", nullable: false),
                    PreviousActionId = table.Column<long>(type: "bigint", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMEmailSequences", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpis",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    AnalysisFrequency = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DueDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Monitor = table.Column<bool>(type: "boolean", nullable: false),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    StartTime = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    UpdateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpis", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMLeadCollaborators",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Email = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMLeadCollaborators", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMMeetings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Author = table.Column<string>(type: "text", nullable: true),
                    Date = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    EndTime = table.Column<string>(type: "text", nullable: true),
                    ImageUrl = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsCancelled = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsRecurring = table.Column<bool>(type: "boolean", nullable: false),
                    Location = table.Column<string>(type: "text", nullable: true),
                    MeeetingLink = table.Column<string>(type: "text", nullable: true),
                    MeetingId = table.Column<string>(type: "text", nullable: true),
                    StartTime = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMMeetings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMNotification",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMNotification", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMNotification_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMPricingPlans",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMPricingPlans", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMSources",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DatedCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    URL = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMSources", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMStripeSubscriptionDetails",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    Interval = table.Column<string>(type: "text", nullable: true),
                    Plan = table.Column<string>(type: "text", nullable: false),
                    PriceId = table.Column<string>(type: "text", nullable: true),
                    ProductId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMStripeSubscriptionDetails", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMTags",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DatedCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMTags", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMTodos",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    EndTime = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    StartTime = table.Column<string>(type: "text", nullable: true),
                    TaskPriority = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    TodoDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMTodos", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CRMWorkSchedules",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMWorkSchedules", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataClasses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataClasses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EmailSignatures",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Company = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    JobTitle = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    Website = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailSignatures", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EmailTemplates",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    Template = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmailTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EmployeeKycDynamicFields",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DataType = table.Column<string>(type: "text", nullable: true),
                    FieldName = table.Column<string>(type: "text", nullable: true),
                    FieldType = table.Column<int>(type: "integer", nullable: false),
                    TrustLevel = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmployeeKycDynamicFields", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EventNodes",
                schema: _Schema,
                columns: table => new
                {
                    ParentNodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    NodeName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EventNodes", x => x.ParentNodeId);
                });

            migrationBuilder.CreateTable(
                name: "Events",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Channel = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Events", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "IncomingSms",
                schema: _Schema,
                columns: table => new
                {
                    MessageSid = table.Column<string>(type: "text", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateSent = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    IncomingMessage = table.Column<string>(type: "text", nullable: true),
                    MessageStatus = table.Column<string>(type: "text", nullable: true),
                    ResposeMessage = table.Column<string>(type: "text", nullable: true),
                    SenderNumber = table.Column<string>(type: "text", nullable: true),
                    TwilioPhoneNumber = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IncomingSms", x => x.MessageSid);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysAccountClasses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysAccountClasses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysAccounts",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AccountClassId = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    GrossPay = table.Column<long>(type: "bigint", nullable: true),
                    LoginPin = table.Column<string>(type: "text", nullable: true),
                    NetPay = table.Column<long>(type: "bigint", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    UserProfileId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysAccounts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysAppSettings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    AppName = table.Column<string>(type: "text", nullable: true),
                    ContentType = table.Column<string>(type: "text", nullable: true),
                    FileName = table.Column<string>(type: "text", nullable: true),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    PropertyName = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysAppSettings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysBillingAddresses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    City = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    PostalCode = table.Column<string>(type: "text", nullable: true),
                    Region = table.Column<string>(type: "text", nullable: true),
                    StreetName = table.Column<string>(type: "text", nullable: true),
                    StreetNumber = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysBillingAddresses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysClientCompanies",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    BusinessName = table.Column<string>(type: "text", nullable: true),
                    City = table.Column<string>(type: "text", nullable: true),
                    ClientId = table.Column<string>(type: "text", nullable: true),
                    ClientType = table.Column<int>(type: "integer", nullable: false),
                    ContactFirstName = table.Column<string>(type: "text", nullable: true),
                    ContactLastName = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    RcNumber = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    UserProfileId = table.Column<string>(type: "text", nullable: true),
                    Vat = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysClientCompanies", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysFeature",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<string>(type: "text", nullable: true),
                    FeatureName = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysFeature", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysInvite",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AccountClassId = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    GrossPay = table.Column<decimal>(type: "numeric", nullable: false),
                    InviteCode = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    NetPay = table.Column<decimal>(type: "numeric", nullable: false),
                    Phone = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysInvite", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysPricingPlans",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysPricingPlans", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysStripeSubscriptionDetails",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<string>(type: "text", nullable: true),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    Interval = table.Column<string>(type: "text", nullable: true),
                    Plan = table.Column<string>(type: "text", nullable: false),
                    PriceId = table.Column<string>(type: "text", nullable: true),
                    ProductId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysStripeSubscriptionDetails", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "KpiDepartment",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KpiDepartment", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "KpiProcess",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KpiProcess", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "KpiSource",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KpiSource", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "KYCs",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AMLVerified = table.Column<bool>(type: "boolean", nullable: false),
                    AMLVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    AddressFile = table.Column<string>(type: "text", nullable: true),
                    AddressVerified = table.Column<bool>(type: "boolean", nullable: false),
                    AddressVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    BVN = table.Column<string>(type: "text", nullable: true),
                    BVNVerified = table.Column<bool>(type: "boolean", nullable: false),
                    BVNVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Contract = table.Column<string>(type: "text", nullable: true),
                    DOBVerified = table.Column<bool>(type: "boolean", nullable: false),
                    DOBVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateCreated = table.Column<string>(type: "text", nullable: true),
                    Disputes = table.Column<int>(type: "integer", nullable: false),
                    DriversLicenceFile = table.Column<string>(type: "text", nullable: true),
                    DriversLicenceFileBack = table.Column<string>(type: "text", nullable: true),
                    DriversLicenceIdValue = table.Column<string>(type: "text", nullable: true),
                    DriversLicenceVerified = table.Column<bool>(type: "boolean", nullable: false),
                    DriversLicenceVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    EmailVerified = table.Column<bool>(type: "boolean", nullable: false),
                    EmailVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    EmployeeCompanyName = table.Column<string>(type: "text", nullable: true),
                    EmployeeEmail = table.Column<string>(type: "text", nullable: true),
                    EmployeeKycStatus = table.Column<int>(type: "integer", nullable: false),
                    ExpiringDate = table.Column<string>(type: "text", nullable: true),
                    FaceRecognitionFile = table.Column<string>(type: "text", nullable: true),
                    FaceRecognitionVerified = table.Column<bool>(type: "boolean", nullable: false),
                    FaceRecognitionVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    HighTrustDynamicFieldsJson = table.Column<string>(type: "text", nullable: true),
                    InviteEmployee = table.Column<bool>(type: "boolean", nullable: false),
                    JobRole = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    MidTrustDynamicFieldsJson = table.Column<string>(type: "text", nullable: true),
                    NFCVerified = table.Column<bool>(type: "boolean", nullable: false),
                    NFCVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    NINFile = table.Column<string>(type: "text", nullable: true),
                    NINFileBack = table.Column<string>(type: "text", nullable: true),
                    NINVerified = table.Column<bool>(type: "boolean", nullable: false),
                    NINVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Nationality = table.Column<string>(type: "text", nullable: true),
                    NinIdValue = table.Column<string>(type: "text", nullable: true),
                    PEPVerified = table.Column<bool>(type: "boolean", nullable: false),
                    PEPVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    PassportFile = table.Column<string>(type: "text", nullable: true),
                    PassportIdValue = table.Column<string>(type: "text", nullable: true),
                    PassportVerified = table.Column<bool>(type: "boolean", nullable: false),
                    PassportVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    PhoneVerified = table.Column<bool>(type: "boolean", nullable: false),
                    PhoneVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    PictureUrl = table.Column<string>(type: "text", nullable: true),
                    Tag = table.Column<string>(type: "text", nullable: true),
                    TrustLevel = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    UserProfileId = table.Column<string>(type: "text", nullable: true),
                    WatchListVerified = table.Column<bool>(type: "boolean", nullable: false),
                    WatchListVerifiedDate = table.Column<string>(type: "text", nullable: true),
                    kycStatus = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KYCs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KYCs_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_KYCs_UserProfiles_UserProfileId",
                        column: x => x.UserProfileId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Location",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Region = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Location", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NoteOwners",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Email = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NoteOwners", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PhoneNumberTransactions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Amount = table.Column<decimal>(type: "numeric", nullable: false),
                    Country = table.Column<string>(type: "text", nullable: true),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    DatePurchased = table.Column<DateTime>(type: "timestamp", nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PurchasedBy = table.Column<string>(type: "text", nullable: true),
                    ReferenceId = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhoneNumberTransactions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Profiles",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AcademicAndProfessionalCertificateUrls = table.Column<string>(type: "text", nullable: true),
                    AddressDocumentUrl = table.Column<string>(type: "text", nullable: true),
                    CV_URL = table.Column<string>(type: "text", nullable: true),
                    City = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    GithubPlatform = table.Column<string>(type: "text", nullable: true),
                    Guarantor1_FullName = table.Column<string>(type: "text", nullable: true),
                    Guarantor2_EmailAddress = table.Column<string>(type: "text", nullable: true),
                    Guarantor2_Fullname = table.Column<string>(type: "text", nullable: true),
                    Guarantor2_PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Guarantor2_RelationshipToKin = table.Column<string>(type: "text", nullable: true),
                    GuarantorI_EmailAddress = table.Column<string>(type: "text", nullable: true),
                    GuarantorI_Phone = table.Column<string>(type: "text", nullable: true),
                    GuarantorI_RelationshipToKin = table.Column<string>(type: "text", nullable: true),
                    IdDocumentType = table.Column<string>(type: "text", nullable: true),
                    IdDocumentUrl = table.Column<string>(type: "text", nullable: true),
                    IsAddressVerified = table.Column<bool>(type: "boolean", nullable: false),
                    IsGuarantorsVerified = table.Column<bool>(type: "boolean", nullable: false),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LinkedinPlatform = table.Column<string>(type: "text", nullable: true),
                    PortfolioPlatform = table.Column<string>(type: "text", nullable: true),
                    PostalCode = table.Column<string>(type: "text", nullable: true),
                    Profession = table.Column<string>(type: "text", nullable: true),
                    SalaryExpectationPerHour = table.Column<string>(type: "text", nullable: true),
                    SelfietUrl = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    Street = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    YearsOfExperience = table.Column<string>(type: "text", nullable: true),
                    isGovernmentIdDocumentVerified = table.Column<bool>(type: "boolean", nullable: false),
                    isPoofOfExpertiseVerified = table.Column<bool>(type: "boolean", nullable: false),
                    isSelfieVerified = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Profiles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Profiles_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ScheduledActionNodes",
                schema: _Schema,
                columns: table => new
                {
                    NodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    ScheduleActionId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScheduledActionNodes", x => x.NodeId);
                });

            migrationBuilder.CreateTable(
                name: "ScheduledActions",
                schema: _Schema,
                columns: table => new
                {
                    NodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    Body = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    CurrentlyExecuting = table.Column<bool>(type: "boolean", nullable: false),
                    Delay = table.Column<string>(type: "text", nullable: true),
                    ExecutionNo = table.Column<int>(type: "integer", nullable: false),
                    Node = table.Column<int>(type: "integer", nullable: false),
                    ParentNode = table.Column<int>(type: "integer", nullable: false),
                    Response = table.Column<bool>(type: "boolean", nullable: false),
                    ScheduleActionId = table.Column<Guid>(type: "uuid", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    TiedToOutcome = table.Column<bool>(type: "boolean", nullable: false),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScheduledActions", x => x.NodeId);
                });

            migrationBuilder.CreateTable(
                name: "Sequences",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    SaveHasTemplate = table.Column<bool>(type: "boolean", nullable: false),
                    ScheduledActionId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sequences", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SimSettings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CallCharges = table.Column<decimal>(type: "numeric", nullable: false),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    SMSCost = table.Column<decimal>(type: "numeric", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SimSettings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SimWallets",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Amount = table.Column<decimal>(type: "numeric", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    WalletId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SimWallets", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Tags",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tags", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TempUsers",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    IsEmailVerified = table.Column<bool>(type: "boolean", nullable: false),
                    IsPhoneNumberVerified = table.Column<bool>(type: "boolean", nullable: false),
                    Phone = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TempUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UnregisteredUserCompanies",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyName = table.Column<string>(type: "text", nullable: true),
                    Industry = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnregisteredUserCompanies", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnregisteredUserCompanies_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AutoSaveTransactions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Amount = table.Column<long>(type: "bigint", nullable: false),
                    AutoSaveSettingId = table.Column<string>(type: "text", nullable: true),
                    TransDate = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoSaveTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoSaveTransactions_AutoSaveSettings_AutoSaveSettingId",
                        column: x => x.AutoSaveSettingId,
                        principalSchema: _Schema,
                        principalTable: "AutoSaveSettings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMPhoneNumbers",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CapabilitiesId = table.Column<long>(type: "bigint", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    CountryName = table.Column<string>(type: "text", nullable: true),
                    DatePurchased = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsExpired = table.Column<bool>(type: "boolean", nullable: false),
                    PhoneNo = table.Column<string>(type: "text", nullable: true),
                    Price = table.Column<decimal>(type: "numeric", nullable: false),
                    PurchaseBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMPhoneNumbers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMPhoneNumbers_Capabilities_CapabilitiesId",
                        column: x => x.CapabilitiesId,
                        principalSchema: _Schema,
                        principalTable: "Capabilities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycAddresses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CompanyBranchAddress = table.Column<string>(type: "text", nullable: true),
                    CompanyBranchCity = table.Column<string>(type: "text", nullable: true),
                    CompanyBranchCountry = table.Column<string>(type: "text", nullable: true),
                    CompanyBranchPostalCode = table.Column<string>(type: "text", nullable: true),
                    CompanyBranchState = table.Column<string>(type: "text", nullable: true),
                    CompanyHeadquarterAddress = table.Column<string>(type: "text", nullable: true),
                    CompanyHeadquarterCity = table.Column<string>(type: "text", nullable: true),
                    CompanyHeadquarterCountry = table.Column<string>(type: "text", nullable: true),
                    CompanyHeadquarterPostalCode = table.Column<string>(type: "text", nullable: true),
                    CompanyHeadquarterState = table.Column<string>(type: "text", nullable: true),
                    CompanyId = table.Column<string>(type: "text", nullable: true),
                    CompanyKycId = table.Column<string>(type: "text", nullable: true),
                    IsCompanyTheHeadQuarter = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycAddresses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyKycAddresses_CompaniesKYCs_CompanyKycId",
                        column: x => x.CompanyKycId,
                        principalSchema: _Schema,
                        principalTable: "CompaniesKYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycCategories",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CategoryId = table.Column<string>(type: "text", nullable: true),
                    CompanyKycId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyKycCategories_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalSchema: _Schema,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompanyKycCategories_CompaniesKYCs_CompanyKycId",
                        column: x => x.CompanyKycId,
                        principalSchema: _Schema,
                        principalTable: "CompaniesKYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycDocuments",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CompanyKycId = table.Column<string>(type: "text", nullable: true),
                    DocumentFilePath = table.Column<string>(type: "text", nullable: true),
                    DocumentName = table.Column<string>(type: "text", nullable: true),
                    DocumentUploadDate = table.Column<string>(type: "text", nullable: true),
                    IsDocumentSubmitted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyKycDocuments_CompaniesKYCs_CompanyKycId",
                        column: x => x.CompanyKycId,
                        principalSchema: _Schema,
                        principalTable: "CompaniesKYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycUBOs",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CompanyKycId = table.Column<string>(type: "text", nullable: true),
                    DateUploaded = table.Column<string>(type: "text", nullable: true),
                    Equity = table.Column<string>(type: "text", nullable: true),
                    ExpiryDate = table.Column<string>(type: "text", nullable: true),
                    Location = table.Column<string>(type: "text", nullable: true),
                    Role = table.Column<string>(type: "text", nullable: true),
                    SignatoryLevel = table.Column<int>(type: "integer", nullable: false),
                    UBODynamicFieldsJson = table.Column<string>(type: "text", nullable: true),
                    UboDateOfBirth = table.Column<string>(type: "text", nullable: true),
                    UboFirstName = table.Column<string>(type: "text", nullable: true),
                    UboLastName = table.Column<string>(type: "text", nullable: true),
                    UboMiddleName = table.Column<string>(type: "text", nullable: true),
                    UboNationality = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycUBOs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyKycUBOs_CompaniesKYCs_CompanyKycId",
                        column: x => x.CompanyKycId,
                        principalSchema: _Schema,
                        principalTable: "CompaniesKYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMSocialMedias",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMBuisinessCardId = table.Column<long>(type: "bigint", nullable: true),
                    Link = table.Column<string>(type: "text", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMSocialMedias", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMSocialMedias_CRMBuisinessCards_CRMBuisinessCardId",
                        column: x => x.CRMBuisinessCardId,
                        principalSchema: _Schema,
                        principalTable: "CRMBuisinessCards",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMCampaignOnwer",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMCampaignId = table.Column<long>(type: "bigint", nullable: true),
                    OwnerName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCampaignOnwer", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMCampaignOnwer_CRMCampaigns_CRMCampaignId",
                        column: x => x.CRMCampaignId,
                        principalSchema: _Schema,
                        principalTable: "CRMCampaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMEmailRecords",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMCampaignId = table.Column<long>(type: "bigint", nullable: true),
                    CampaignId = table.Column<long>(type: "bigint", nullable: false),
                    Clicks = table.Column<double>(type: "double precision", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    EmailSent = table.Column<int>(type: "integer", nullable: false),
                    Interested = table.Column<double>(type: "double precision", nullable: false),
                    Replies = table.Column<double>(type: "double precision", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMEmailRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMEmailRecords_CRMCampaigns_CRMCampaignId",
                        column: x => x.CRMCampaignId,
                        principalSchema: _Schema,
                        principalTable: "CRMCampaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMLeads",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMCampaignId = table.Column<long>(type: "bigint", nullable: true),
                    Company = table.Column<string>(type: "text", nullable: true),
                    ContactReferenceId = table.Column<long>(type: "bigint", nullable: true),
                    DateConverted = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    EmployeeRole = table.Column<string>(type: "text", nullable: true),
                    IsArchived = table.Column<bool>(type: "boolean", nullable: false),
                    IsConvertedToDeals = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IsTransfered = table.Column<bool>(type: "boolean", nullable: false),
                    LastContacted = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Source = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMLeads", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMLeads_CRMCampaigns_CRMCampaignId",
                        column: x => x.CRMCampaignId,
                        principalSchema: _Schema,
                        principalTable: "CRMCampaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMCompanyCRMCompanyCollaborator",
                schema: _Schema,
                columns: table => new
                {
                    CRMCompaniesId = table.Column<long>(type: "bigint", nullable: false),
                    CRMCompanyCollaboratorsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCompanyCRMCompanyCollaborator", x => new { x.CRMCompaniesId, x.CRMCompanyCollaboratorsId });
                    table.ForeignKey(
                        name: "FK_CRMCompanyCRMCompanyCollaborator_CRMCompanies_CRMCompaniesId",
                        column: x => x.CRMCompaniesId,
                        principalSchema: _Schema,
                        principalTable: "CRMCompanies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMCompanyCRMCompanyCollaborator_CRMCompanyCollaborators_CR~",
                        column: x => x.CRMCompanyCollaboratorsId,
                        principalSchema: _Schema,
                        principalTable: "CRMCompanyCollaborators",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMCompanyCRMCompanyTag",
                schema: _Schema,
                columns: table => new
                {
                    CRMCompaniesId = table.Column<long>(type: "bigint", nullable: false),
                    CompanyTagsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCompanyCRMCompanyTag", x => new { x.CRMCompaniesId, x.CompanyTagsId });
                    table.ForeignKey(
                        name: "FK_CRMCompanyCRMCompanyTag_CRMCompanies_CRMCompaniesId",
                        column: x => x.CRMCompaniesId,
                        principalSchema: _Schema,
                        principalTable: "CRMCompanies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMCompanyCRMCompanyTag_CRMCompanyTags_CompanyTagsId",
                        column: x => x.CompanyTagsId,
                        principalSchema: _Schema,
                        principalTable: "CRMCompanyTags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContactNotes",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMContactId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Detail = table.Column<string>(type: "text", nullable: true),
                    Location = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContactNotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContactNotes_CRMContacts_CRMContactId",
                        column: x => x.CRMContactId,
                        principalSchema: _Schema,
                        principalTable: "CRMContacts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMContactCRMContactCollaborator",
                schema: _Schema,
                columns: table => new
                {
                    CRMContactCollaboratorsId = table.Column<long>(type: "bigint", nullable: false),
                    CRMContactsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMContactCRMContactCollaborator", x => new { x.CRMContactCollaboratorsId, x.CRMContactsId });
                    table.ForeignKey(
                        name: "FK_CRMContactCRMContactCollaborator_CRMContactCollaborator_CRM~",
                        column: x => x.CRMContactCollaboratorsId,
                        principalSchema: _Schema,
                        principalTable: "CRMContactCollaborator",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMContactCRMContactCollaborator_CRMContacts_CRMContactsId",
                        column: x => x.CRMContactsId,
                        principalSchema: _Schema,
                        principalTable: "CRMContacts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMContactOwners",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ContactId = table.Column<long>(type: "bigint", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMContactOwners", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMContactOwners_CRMContacts_ContactId",
                        column: x => x.ContactId,
                        principalSchema: _Schema,
                        principalTable: "CRMContacts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMContactCRMContactTag",
                schema: _Schema,
                columns: table => new
                {
                    CRMContactsId = table.Column<long>(type: "bigint", nullable: false),
                    TagsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMContactCRMContactTag", x => new { x.CRMContactsId, x.TagsId });
                    table.ForeignKey(
                        name: "FK_CRMContactCRMContactTag_CRMContacts_CRMContactsId",
                        column: x => x.CRMContactsId,
                        principalSchema: _Schema,
                        principalTable: "CRMContacts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMContactCRMContactTag_CRMContactTags_TagsId",
                        column: x => x.TagsId,
                        principalSchema: _Schema,
                        principalTable: "CRMContactTags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMDealCRMDealCollaborator",
                schema: _Schema,
                columns: table => new
                {
                    CRMDealCollaboratorsId = table.Column<long>(type: "bigint", nullable: false),
                    CRMDealsDealId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMDealCRMDealCollaborator", x => new { x.CRMDealCollaboratorsId, x.CRMDealsDealId });
                    table.ForeignKey(
                        name: "FK_CRMDealCRMDealCollaborator_CRMDealCollaborators_CRMDealColl~",
                        column: x => x.CRMDealCollaboratorsId,
                        principalSchema: _Schema,
                        principalTable: "CRMDealCollaborators",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMDealCRMDealCollaborator_CRMDeals_CRMDealsDealId",
                        column: x => x.CRMDealsDealId,
                        principalSchema: _Schema,
                        principalTable: "CRMDeals",
                        principalColumn: "DealId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMDealStatuses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMDealId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMDealStatuses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMDealStatuses_CRMDeals_CRMDealId",
                        column: x => x.CRMDealId,
                        principalSchema: _Schema,
                        principalTable: "CRMDeals",
                        principalColumn: "DealId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMSequenceMails",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Body = table.Column<string>(type: "text", nullable: true),
                    CRMEmailSequenceId = table.Column<long>(type: "bigint", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMSequenceMails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMSequenceMails_CRMEmailSequences_CRMEmailSequenceId",
                        column: x => x.CRMEmailSequenceId,
                        principalSchema: _Schema,
                        principalTable: "CRMEmailSequences",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiActivities",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    TeamMemberId = table.Column<string>(type: "text", nullable: true),
                    TeamMemberProfilePic = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiActivities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiActivities_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiCollaborators",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TeamMemberEmail = table.Column<string>(type: "text", nullable: true),
                    TeamMemberId = table.Column<string>(type: "text", nullable: true),
                    TeamMemberName = table.Column<string>(type: "text", nullable: true),
                    TeamMemberProfilePic = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiCollaborators", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiCollaborators_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiExpectedOutComes",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    Date = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Max = table.Column<double>(type: "double precision", nullable: false),
                    Min = table.Column<double>(type: "double precision", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiExpectedOutComes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiExpectedOutComes_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiIncentives",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Type = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    Value = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiIncentives", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiIncentives_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiMeasurements",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Unit = table.Column<double>(type: "double precision", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiMeasurements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiMeasurements_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiProcesses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Achievement = table.Column<int>(type: "integer", nullable: false),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Quantity = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiProcesses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiProcesses_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMKpiSources",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMKpiId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMKpiSources", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMKpiSources_CRMKpis_CRMKpiId",
                        column: x => x.CRMKpiId,
                        principalSchema: _Schema,
                        principalTable: "CRMKpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMPackagePricings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    PricePerMonth = table.Column<double>(type: "double precision", nullable: true),
                    PricePerMonthForYearlyOption = table.Column<double>(type: "double precision", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMPackagePricings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMPackagePricings_CRMPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "CRMPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMPricingAndFeatures",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Category = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DurationType = table.Column<string>(type: "text", nullable: true),
                    FeatureId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsLimited = table.Column<bool>(type: "boolean", nullable: false),
                    LimitedTo = table.Column<string>(type: "text", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMPricingAndFeatures", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMPricingAndFeatures_CRMPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "CRMPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMPricingAndFeatures_Features_FeatureId",
                        column: x => x.FeatureId,
                        principalSchema: _Schema,
                        principalTable: "Features",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMSubscriptions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ActivatedOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Amount = table.Column<double>(type: "double precision", nullable: false),
                    BillingAddressId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConsumerAccount = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Currency = table.Column<int>(type: "integer", nullable: false),
                    ExpiresOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Interval = table.Column<string>(type: "text", nullable: true),
                    IsCancelled = table.Column<bool>(type: "boolean", nullable: false),
                    MandateId = table.Column<string>(type: "text", nullable: true),
                    MandateReference = table.Column<string>(type: "text", nullable: true),
                    MollieCustomerId = table.Column<string>(type: "text", nullable: true),
                    PayPalEmail = table.Column<string>(type: "text", nullable: true),
                    PaymentId = table.Column<string>(type: "text", nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<int>(type: "integer", nullable: true),
                    PaypalBillingAgreementId = table.Column<string>(type: "text", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    RetrySubAttempt = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    StripeCustomerId = table.Column<string>(type: "text", nullable: true),
                    StripePriceId = table.Column<string>(type: "text", nullable: true),
                    SubscriptionFor = table.Column<int>(type: "integer", nullable: true),
                    SubscriptionId = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransactionCode = table.Column<string>(type: "text", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMSubscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMSubscriptions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CRMSubscriptions_CRMBillingAddresses_BillingAddressId",
                        column: x => x.BillingAddressId,
                        principalSchema: _Schema,
                        principalTable: "CRMBillingAddresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CRMSubscriptions_CRMPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "CRMPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMSubscriptions_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMCollaborator",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMMeetingId = table.Column<long>(type: "bigint", nullable: true),
                    CRMTodoId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TeamMemberId = table.Column<string>(type: "text", nullable: true),
                    TeamMemberName = table.Column<string>(type: "text", nullable: true),
                    TeamMemberProfilePic = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMCollaborator", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMCollaborator_CRMMeetings_CRMMeetingId",
                        column: x => x.CRMMeetingId,
                        principalSchema: _Schema,
                        principalTable: "CRMMeetings",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CRMCollaborator_CRMTodos_CRMTodoId",
                        column: x => x.CRMTodoId,
                        principalSchema: _Schema,
                        principalTable: "CRMTodos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMTagCRMTodo",
                schema: _Schema,
                columns: table => new
                {
                    TagsId = table.Column<long>(type: "bigint", nullable: false),
                    TodosId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMTagCRMTodo", x => new { x.TagsId, x.TodosId });
                    table.ForeignKey(
                        name: "FK_CRMTagCRMTodo_CRMTags_TagsId",
                        column: x => x.TagsId,
                        principalSchema: _Schema,
                        principalTable: "CRMTags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMTagCRMTodo_CRMTodos_TodosId",
                        column: x => x.TodosId,
                        principalSchema: _Schema,
                        principalTable: "CRMTodos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkingDays",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMWorkScheduleId = table.Column<long>(type: "bigint", nullable: true),
                    DayOfTheWeek = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkingDays", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkingDays_CRMWorkSchedules_CRMWorkScheduleId",
                        column: x => x.CRMWorkScheduleId,
                        principalSchema: _Schema,
                        principalTable: "CRMWorkSchedules",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycDataClasses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CompanyKycId = table.Column<string>(type: "text", nullable: true),
                    DataClassId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycDataClasses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyKycDataClasses_CompaniesKYCs_CompanyKycId",
                        column: x => x.CompanyKycId,
                        principalSchema: _Schema,
                        principalTable: "CompaniesKYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompanyKycDataClasses_DataClasses_DataClassId",
                        column: x => x.DataClassId,
                        principalSchema: _Schema,
                        principalTable: "DataClasses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DataClassCategories",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CategoryId = table.Column<string>(type: "text", nullable: true),
                    DataClassId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataClassCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataClassCategories_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalSchema: _Schema,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DataClassCategories_DataClasses_DataClassId",
                        column: x => x.DataClassId,
                        principalSchema: _Schema,
                        principalTable: "DataClasses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Module",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ClassId = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Module", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Module_JobPaysAccountClasses_ClassId",
                        column: x => x.ClassId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysAccountClasses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AccountWallets",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AccountName = table.Column<string>(type: "text", nullable: true),
                    AcountNumber = table.Column<string>(type: "text", nullable: true),
                    Balance = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BankAddress = table.Column<string>(type: "text", nullable: true),
                    BankCode = table.Column<string>(type: "text", nullable: true),
                    BankName = table.Column<string>(type: "text", nullable: true),
                    Bvn = table.Column<string>(type: "text", nullable: true),
                    CardHolderId = table.Column<string>(type: "text", nullable: true),
                    CbaCustomerID = table.Column<string>(type: "text", nullable: true),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    IBanState = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsIBANRequested = table.Column<bool>(type: "boolean", nullable: false),
                    IsIdVerified = table.Column<bool>(type: "boolean", nullable: false),
                    IssuingAppId = table.Column<string>(type: "text", nullable: true),
                    JobPaysAccountID = table.Column<string>(type: "text", nullable: true),
                    PaymentReference = table.Column<string>(type: "text", nullable: true),
                    Provider = table.Column<int>(type: "integer", nullable: false),
                    State = table.Column<string>(type: "text", nullable: true),
                    WalletType = table.Column<int>(type: "integer", nullable: false),
                    WeavrAccountId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountWallets", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AccountWallets_JobPaysAccounts_JobPaysAccountID",
                        column: x => x.JobPaysAccountID,
                        principalSchema: _Schema,
                        principalTable: "JobPaysAccounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BeneficiaryBatches",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    JobPaysAcctId = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    WeavrBatchId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryBatches", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BeneficiaryBatches_JobPaysAccounts_JobPaysAcctId",
                        column: x => x.JobPaysAcctId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysAccounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "weavrCorpKycs",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CorporateEmail = table.Column<string>(type: "text", nullable: true),
                    CorporateId = table.Column<string>(type: "text", nullable: true),
                    Details = table.Column<string>(type: "text", nullable: true),
                    JobPaysAccountId = table.Column<string>(type: "text", nullable: true),
                    OngoingStatus = table.Column<string>(type: "text", nullable: true),
                    RejectionComment = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_weavrCorpKycs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_weavrCorpKycs_JobPaysAccounts_JobPaysAccountId",
                        column: x => x.JobPaysAccountId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysAccounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysClientCompanyAddresses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    City = table.Column<string>(type: "text", nullable: true),
                    ClientCompanyId = table.Column<string>(type: "text", nullable: true),
                    CompanyClientId = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    PostalCode = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    Street = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysClientCompanyAddresses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysClientCompanyAddresses_JobPaysClientCompanies_Client~",
                        column: x => x.ClientCompanyId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysClientCompanies",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysPackagePricing",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    PricePerMonth = table.Column<double>(type: "double precision", nullable: true),
                    PricePerMonthForYearlyOption = table.Column<double>(type: "double precision", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysPackagePricing", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysPackagePricing_JobPaysPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysPricingAndFeatures",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Category = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DurationType = table.Column<string>(type: "text", nullable: true),
                    FeatureId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsLimited = table.Column<bool>(type: "boolean", nullable: false),
                    LimitedTo = table.Column<string>(type: "text", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysPricingAndFeatures", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysPricingAndFeatures_JobPaysFeature_FeatureId",
                        column: x => x.FeatureId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysFeature",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobPaysPricingAndFeatures_JobPaysPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysSubscriptionHistory",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ActivatedOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Amount = table.Column<double>(type: "double precision", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    ConsumerAccount = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Currency = table.Column<int>(type: "integer", nullable: false),
                    ExpiresOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Interval = table.Column<string>(type: "text", nullable: true),
                    MandateId = table.Column<string>(type: "text", nullable: true),
                    MandateReference = table.Column<string>(type: "text", nullable: true),
                    MollieCustomerId = table.Column<string>(type: "text", nullable: true),
                    PayPalEmail = table.Column<string>(type: "text", nullable: true),
                    PaymentId = table.Column<string>(type: "text", nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<int>(type: "integer", nullable: true),
                    PaypalBillingAgreementId = table.Column<string>(type: "text", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    StripeCustomerId = table.Column<string>(type: "text", nullable: true),
                    StripePriceId = table.Column<string>(type: "text", nullable: true),
                    SubscriptionFor = table.Column<int>(type: "integer", nullable: true),
                    SubscriptionId = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransactionCode = table.Column<string>(type: "text", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysSubscriptionHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptionHistory_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptionHistory_JobPaysPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptionHistory_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysSubscriptions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ActivatedOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Amount = table.Column<double>(type: "double precision", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    BillingAddressId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConsumerAccount = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Currency = table.Column<int>(type: "integer", nullable: false),
                    ExpiresOn = table.Column<DateTime>(type: "timestamp", nullable: true),
                    FreeTrialOptionSelected = table.Column<bool>(type: "boolean", nullable: false),
                    Interval = table.Column<string>(type: "text", nullable: true),
                    MandateId = table.Column<string>(type: "text", nullable: true),
                    MandateReference = table.Column<string>(type: "text", nullable: true),
                    MollieCustomerId = table.Column<string>(type: "text", nullable: true),
                    PayPalEmail = table.Column<string>(type: "text", nullable: true),
                    PaymentId = table.Column<string>(type: "text", nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<int>(type: "integer", nullable: true),
                    PaypalBillingAgreementId = table.Column<string>(type: "text", nullable: true),
                    PricingPlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    RetrySubAttempt = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    StripeCustomerId = table.Column<string>(type: "text", nullable: true),
                    StripePriceId = table.Column<string>(type: "text", nullable: true),
                    SubscriptionCount = table.Column<int>(type: "integer", nullable: false),
                    SubscriptionFor = table.Column<int>(type: "integer", nullable: true),
                    SubscriptionId = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: true),
                    TransactionCode = table.Column<string>(type: "text", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysSubscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptions_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptions_JobPaysBillingAddresses_BillingAddress~",
                        column: x => x.BillingAddressId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysBillingAddresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptions_JobPaysPricingPlans_PricingPlanId",
                        column: x => x.PricingPlanId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysPricingPlans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobPaysSubscriptions_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Kpis",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AnalysisFrequency = table.Column<string>(type: "text", nullable: true),
                    CanMeasureKpi = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    DueDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    KpiDepartmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    KpiSourceId = table.Column<Guid>(type: "uuid", nullable: false),
                    Measurement = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    ProcessId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Kpis", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Kpis_KpiDepartment_KpiDepartmentId",
                        column: x => x.KpiDepartmentId,
                        principalSchema: _Schema,
                        principalTable: "KpiDepartment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Kpis_KpiProcess_ProcessId",
                        column: x => x.ProcessId,
                        principalSchema: _Schema,
                        principalTable: "KpiProcess",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Kpis_KpiSource_KpiSourceId",
                        column: x => x.KpiSourceId,
                        principalSchema: _Schema,
                        principalTable: "KpiSource",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Kpis_UserProfiles_CreatedBy",
                        column: x => x.CreatedBy,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CompanyKycSignatories",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CompanyKycId = table.Column<string>(type: "text", nullable: true),
                    DateUploaded = table.Column<string>(type: "text", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    ExpiryDate = table.Column<string>(type: "text", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    InviteeName = table.Column<string>(type: "text", nullable: true),
                    KycId = table.Column<Guid>(type: "uuid", nullable: false),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    Role = table.Column<string>(type: "text", nullable: true),
                    SignatoryLevel = table.Column<int>(type: "integer", nullable: false),
                    SignatoryPicture = table.Column<string>(type: "text", nullable: true),
                    SignatoryPower = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyKycSignatories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyKycSignatories_CompaniesKYCs_CompanyKycId",
                        column: x => x.CompanyKycId,
                        principalSchema: _Schema,
                        principalTable: "CompaniesKYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompanyKycSignatories_KYCs_KycId",
                        column: x => x.KycId,
                        principalSchema: _Schema,
                        principalTable: "KYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KycCategories",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CategoryId = table.Column<string>(type: "text", nullable: true),
                    KycId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KycCategories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KycCategories_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalSchema: _Schema,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_KycCategories_KYCs_KycId",
                        column: x => x.KycId,
                        principalSchema: _Schema,
                        principalTable: "KYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KycDataClasses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DataClassId = table.Column<string>(type: "text", nullable: true),
                    KycId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KycDataClasses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KycDataClasses_DataClasses_DataClassId",
                        column: x => x.DataClassId,
                        principalSchema: _Schema,
                        principalTable: "DataClasses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_KycDataClasses_KYCs_KycId",
                        column: x => x.KycId,
                        principalSchema: _Schema,
                        principalTable: "KYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KycEmployeeGuarantors",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Address = table.Column<string>(type: "text", nullable: true),
                    AddressFile = table.Column<string>(type: "text", nullable: true),
                    AddressVerified = table.Column<bool>(type: "boolean", nullable: false),
                    AddressVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    City = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateOfBirth = table.Column<string>(type: "text", nullable: true),
                    DriversLicenceFile = table.Column<string>(type: "text", nullable: true),
                    DriversLicenceVerified = table.Column<bool>(type: "boolean", nullable: false),
                    DriversLicenceVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    ExpiringDate = table.Column<string>(type: "text", nullable: true),
                    FaceRecognitionFile = table.Column<string>(type: "text", nullable: true),
                    FaceRecognitionVerified = table.Column<bool>(type: "boolean", nullable: false),
                    FaceRecognitionVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    Gender = table.Column<string>(type: "text", nullable: true),
                    GuarantorEmail = table.Column<string>(type: "text", nullable: true),
                    GuarantorStatus = table.Column<int>(type: "integer", nullable: false),
                    IsInvited = table.Column<bool>(type: "boolean", nullable: false),
                    KycId = table.Column<Guid>(type: "uuid", nullable: false),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    NINFile = table.Column<string>(type: "text", nullable: true),
                    NINVerified = table.Column<bool>(type: "boolean", nullable: false),
                    NINVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Nationality = table.Column<string>(type: "text", nullable: true),
                    OtherName = table.Column<string>(type: "text", nullable: true),
                    PassportFile = table.Column<string>(type: "text", nullable: true),
                    PassportVerified = table.Column<bool>(type: "boolean", nullable: false),
                    PassportVerifiedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    PhoneNo = table.Column<string>(type: "text", nullable: true),
                    PostalCode = table.Column<string>(type: "text", nullable: true),
                    Relationship = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    ZeroTrustDynamicFieldsJson = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KycEmployeeGuarantors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KycEmployeeGuarantors_KYCs_KycId",
                        column: x => x.KycId,
                        principalSchema: _Schema,
                        principalTable: "KYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NFCs",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    KycId = table.Column<Guid>(type: "uuid", nullable: true),
                    com = table.Column<string>(type: "text", nullable: true),
                    cvca = table.Column<string>(type: "text", nullable: true),
                    dg1 = table.Column<string>(type: "text", nullable: true),
                    dg14 = table.Column<string>(type: "text", nullable: true),
                    dg15 = table.Column<string>(type: "text", nullable: true),
                    dg2 = table.Column<string>(type: "text", nullable: true),
                    dg5 = table.Column<string>(type: "text", nullable: true),
                    dg7 = table.Column<string>(type: "text", nullable: true),
                    isAAPassed = table.Column<bool>(type: "boolean", nullable: false),
                    isAASupported = table.Column<bool>(type: "boolean", nullable: false),
                    isBACPassed = table.Column<bool>(type: "boolean", nullable: false),
                    isBACSupported = table.Column<bool>(type: "boolean", nullable: false),
                    isCAPassed = table.Column<bool>(type: "boolean", nullable: false),
                    isCASupported = table.Column<bool>(type: "boolean", nullable: false),
                    isSACPassed = table.Column<bool>(type: "boolean", nullable: false),
                    isSACSupported = table.Column<bool>(type: "boolean", nullable: false),
                    mrz = table.Column<string>(type: "text", nullable: true),
                    sod = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NFCs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NFCs_KYCs_KycId",
                        column: x => x.KycId,
                        principalSchema: _Schema,
                        principalTable: "KYCs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobVacancy",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Body = table.Column<string>(type: "text", nullable: true),
                    Certification = table.Column<string>(type: "text", nullable: true),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactDetail = table.Column<string>(type: "text", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealId = table.Column<string>(type: "text", nullable: true),
                    EndDate = table.Column<string>(type: "text", nullable: true),
                    HourlyRate = table.Column<string>(type: "text", nullable: true),
                    HoursAWeek = table.Column<string>(type: "text", nullable: true),
                    InterviewStageType = table.Column<int>(type: "integer", nullable: false),
                    IsClosed = table.Column<bool>(type: "boolean", nullable: false),
                    JobCategory = table.Column<string>(type: "text", nullable: true),
                    JobDescription = table.Column<string>(type: "text", nullable: true),
                    JobEffective = table.Column<string>(type: "text", nullable: true),
                    JobExperience = table.Column<string>(type: "text", nullable: true),
                    JobExpire = table.Column<string>(type: "text", nullable: true),
                    JobLocation = table.Column<string>(type: "text", nullable: true),
                    JobTitle = table.Column<string>(type: "text", nullable: true),
                    JobType = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LocationId = table.Column<Guid>(type: "uuid", nullable: true),
                    NationalLaguageId = table.Column<Guid>(type: "uuid", nullable: true),
                    PublishJob = table.Column<bool>(type: "boolean", nullable: false),
                    RecruisitionStatus = table.Column<string>(type: "text", nullable: true),
                    Recruiter = table.Column<string>(type: "text", nullable: true),
                    SalaryRange = table.Column<string>(type: "text", nullable: true),
                    StartDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobVacancy", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobVacancy_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalSchema: _Schema,
                        principalTable: "Companies",
                        principalColumn: "CompanyId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobVacancy_Location_LocationId",
                        column: x => x.LocationId,
                        principalSchema: _Schema,
                        principalTable: "Location",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobVacancy_NationalLaguages_NationalLaguageId",
                        column: x => x.NationalLaguageId,
                        principalSchema: _Schema,
                        principalTable: "NationalLaguages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobVacancy_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CampaignsTemplates",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SequenceId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CampaignsTemplates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CampaignsTemplates_Sequences_SequenceId",
                        column: x => x.SequenceId,
                        principalSchema: _Schema,
                        principalTable: "Sequences",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CreditTransactions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Amount = table.Column<decimal>(type: "numeric", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    PurchasedBy = table.Column<string>(type: "text", nullable: true),
                    ReferenceId = table.Column<string>(type: "text", nullable: true),
                    SimWalletId = table.Column<long>(type: "bigint", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    Tenant = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TransactionId = table.Column<string>(type: "text", nullable: true),
                    TransactionType = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CreditTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CreditTransactions_SimWallets_SimWalletId",
                        column: x => x.SimWalletId,
                        principalSchema: _Schema,
                        principalTable: "SimWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Actors",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    DateAdded = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Email = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberId = table.Column<long>(type: "bigint", nullable: true),
                    ProfilePicture = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Actors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Actors_CRMPhoneNumbers_PhoneNumberId",
                        column: x => x.PhoneNumberId,
                        principalSchema: _Schema,
                        principalTable: "CRMPhoneNumbers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMLeadCRMLeadCollaborator",
                schema: _Schema,
                columns: table => new
                {
                    CRMLeadCollaboratorsId = table.Column<long>(type: "bigint", nullable: false),
                    CRMLeadsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMLeadCRMLeadCollaborator", x => new { x.CRMLeadCollaboratorsId, x.CRMLeadsId });
                    table.ForeignKey(
                        name: "FK_CRMLeadCRMLeadCollaborator_CRMLeadCollaborators_CRMLeadColl~",
                        column: x => x.CRMLeadCollaboratorsId,
                        principalSchema: _Schema,
                        principalTable: "CRMLeadCollaborators",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMLeadCRMLeadCollaborator_CRMLeads_CRMLeadsId",
                        column: x => x.CRMLeadsId,
                        principalSchema: _Schema,
                        principalTable: "CRMLeads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMLeadCRMTag",
                schema: _Schema,
                columns: table => new
                {
                    LeadsId = table.Column<long>(type: "bigint", nullable: false),
                    TagsId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMLeadCRMTag", x => new { x.LeadsId, x.TagsId });
                    table.ForeignKey(
                        name: "FK_CRMLeadCRMTag_CRMLeads_LeadsId",
                        column: x => x.LeadsId,
                        principalSchema: _Schema,
                        principalTable: "CRMLeads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CRMLeadCRMTag_CRMTags_TagsId",
                        column: x => x.TagsId,
                        principalSchema: _Schema,
                        principalTable: "CRMTags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMLeadOwners",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMLeadId = table.Column<long>(type: "bigint", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    ProfilePic = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMLeadOwners", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMLeadOwners_CRMLeads_CRMLeadId",
                        column: x => x.CRMLeadId,
                        principalSchema: _Schema,
                        principalTable: "CRMLeads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContactNoteNoteOwner",
                schema: _Schema,
                columns: table => new
                {
                    ContactNotesId = table.Column<long>(type: "bigint", nullable: false),
                    NoteOwnersId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContactNoteNoteOwner", x => new { x.ContactNotesId, x.NoteOwnersId });
                    table.ForeignKey(
                        name: "FK_ContactNoteNoteOwner_ContactNotes_ContactNotesId",
                        column: x => x.ContactNotesId,
                        principalSchema: _Schema,
                        principalTable: "ContactNotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ContactNoteNoteOwner_NoteOwners_NoteOwnersId",
                        column: x => x.NoteOwnersId,
                        principalSchema: _Schema,
                        principalTable: "NoteOwners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NoteUploads",
                schema: _Schema,
                columns: table => new
                {
                    NoteUploadId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMContactId = table.Column<long>(type: "bigint", nullable: false),
                    ContactNoteId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    FilePath = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NoteUploads", x => x.NoteUploadId);
                    table.ForeignKey(
                        name: "FK_NoteUploads_ContactNotes_ContactNoteId",
                        column: x => x.ContactNoteId,
                        principalSchema: _Schema,
                        principalTable: "ContactNotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "VoiceNoteUploads",
                schema: _Schema,
                columns: table => new
                {
                    VoiceNoteUploadId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CRMContactId = table.Column<long>(type: "bigint", nullable: false),
                    ContactNoteId = table.Column<long>(type: "bigint", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    FilePath = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VoiceNoteUploads", x => x.VoiceNoteUploadId);
                    table.ForeignKey(
                        name: "FK_VoiceNoteUploads_ContactNotes_ContactNoteId",
                        column: x => x.ContactNoteId,
                        principalSchema: _Schema,
                        principalTable: "ContactNotes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "WorkingHours",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EndTime = table.Column<string>(type: "text", nullable: true),
                    StartTime = table.Column<string>(type: "text", nullable: true),
                    WorkingDayId = table.Column<long>(type: "bigint", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkingHours", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkingHours_WorkingDays_WorkingDayId",
                        column: x => x.WorkingDayId,
                        principalSchema: _Schema,
                        principalTable: "WorkingDays",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AccountWalletBeneficiaries",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AccountName = table.Column<string>(type: "text", nullable: true),
                    AccountNumber = table.Column<string>(type: "text", nullable: true),
                    BankCode = table.Column<string>(type: "text", nullable: true),
                    BankName = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    WalletId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountWalletBeneficiaries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AccountWalletBeneficiaries_AccountWallets_WalletId",
                        column: x => x.WalletId,
                        principalSchema: _Schema,
                        principalTable: "AccountWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BridgeCardTransactionHistories",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    WalletId = table.Column<string>(type: "text", nullable: true),
                    bridgecard_transaction_reference = table.Column<string>(type: "text", nullable: true),
                    card_id = table.Column<string>(type: "text", nullable: true),
                    card_transaction_type = table.Column<string>(type: "text", nullable: true),
                    cardholder_id = table.Column<string>(type: "text", nullable: true),
                    client_transaction_reference = table.Column<string>(type: "text", nullable: true),
                    currency = table.Column<string>(type: "text", nullable: true),
                    description = table.Column<string>(type: "text", nullable: true),
                    interchange_revenue = table.Column<string>(type: "text", nullable: true),
                    interchange_revenue_refund = table.Column<string>(type: "text", nullable: true),
                    is_recurring = table.Column<bool>(type: "boolean", nullable: false),
                    issuing_app_id = table.Column<string>(type: "text", nullable: true),
                    livemode = table.Column<bool>(type: "boolean", nullable: false),
                    merchant_category_code = table.Column<string>(type: "text", nullable: true),
                    merchant_city = table.Column<string>(type: "text", nullable: true),
                    merchant_code = table.Column<string>(type: "text", nullable: true),
                    merchant_logo = table.Column<string>(type: "text", nullable: true),
                    merchant_name = table.Column<string>(type: "text", nullable: true),
                    merchant_website = table.Column<string>(type: "text", nullable: true),
                    partner_interchange_fee = table.Column<string>(type: "text", nullable: true),
                    partner_interchange_fee_refund = table.Column<string>(type: "text", nullable: true),
                    transaction_category = table.Column<string>(type: "text", nullable: true),
                    transaction_date = table.Column<DateTime>(type: "timestamp", nullable: false),
                    transaction_group = table.Column<string>(type: "text", nullable: true),
                    transaction_timestamp = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BridgeCardTransactionHistories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BridgeCardTransactionHistories_AccountWallets_WalletId",
                        column: x => x.WalletId,
                        principalSchema: _Schema,
                        principalTable: "AccountWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Cards",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AvailableToSpend = table.Column<long>(type: "bigint", nullable: false),
                    CardBrand = table.Column<string>(type: "text", nullable: true),
                    CardLevelClassification = table.Column<string>(type: "text", nullable: true),
                    CardNumber = table.Column<string>(type: "text", nullable: true),
                    CardNumberFirstSix = table.Column<string>(type: "text", nullable: true),
                    CardNumberLastFour = table.Column<string>(type: "text", nullable: true),
                    CardholderMobileNumber = table.Column<string>(type: "text", nullable: true),
                    CreationTimestamp = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    Cvv = table.Column<string>(type: "text", nullable: true),
                    ExpiryMmyy = table.Column<string>(type: "text", nullable: true),
                    Interval = table.Column<string>(type: "text", nullable: true),
                    ManagedAccountId = table.Column<string>(type: "text", nullable: true),
                    Mode = table.Column<string>(type: "text", nullable: true),
                    NameOnCard = table.Column<string>(type: "text", nullable: true),
                    NameOnCardLine2 = table.Column<string>(type: "text", nullable: true),
                    PendingActivation = table.Column<bool>(type: "boolean", nullable: false),
                    PinBlocked = table.Column<bool>(type: "boolean", nullable: false),
                    PrepaidAactualBalance = table.Column<int>(type: "integer", nullable: false),
                    PrepaidAvailableBalance = table.Column<int>(type: "integer", nullable: false),
                    RenewalType = table.Column<string>(type: "text", nullable: true),
                    StartMmyy = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<string>(type: "text", nullable: true),
                    WeavrCardId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Cards_AccountWallets_ManagedAccountId",
                        column: x => x.ManagedAccountId,
                        principalSchema: _Schema,
                        principalTable: "AccountWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysCards",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AvailableBalance = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BlockedDueToFraud = table.Column<bool>(type: "boolean", nullable: false),
                    BookBalance = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Brand = table.Column<string>(type: "text", nullable: true),
                    CardCurrency = table.Column<string>(type: "text", nullable: true),
                    CardId = table.Column<string>(type: "text", nullable: true),
                    CardNumber = table.Column<string>(type: "text", nullable: true),
                    CardType = table.Column<string>(type: "text", nullable: true),
                    CardholderIdd = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<long>(type: "bigint", nullable: false),
                    Cvv = table.Column<string>(type: "text", nullable: true),
                    ExpiryMonth = table.Column<string>(type: "text", nullable: true),
                    ExpiryYear = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    IssuingAppId = table.Column<string>(type: "text", nullable: true),
                    Last4 = table.Column<string>(type: "text", nullable: true),
                    LiveMode = table.Column<bool>(type: "boolean", nullable: false),
                    NardName = table.Column<string>(type: "text", nullable: true),
                    Pin3dsActivated = table.Column<bool>(type: "boolean", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    WalletId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysCards", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysCards_AccountWallets_WalletId",
                        column: x => x.WalletId,
                        principalSchema: _Schema,
                        principalTable: "AccountWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Transactions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BankAddress = table.Column<string>(type: "text", nullable: true),
                    BankCountry = table.Column<string>(type: "text", nullable: true),
                    BankIdentifierCode = table.Column<string>(type: "text", nullable: true),
                    BankName = table.Column<string>(type: "text", nullable: true),
                    BeneficiaryAccount = table.Column<string>(type: "text", nullable: true),
                    BeneficiaryBank = table.Column<string>(type: "text", nullable: true),
                    BeneficiaryBankCode = table.Column<string>(type: "text", nullable: true),
                    BeneficiaryName = table.Column<string>(type: "text", nullable: true),
                    CancellationReason = table.Column<string>(type: "text", nullable: true),
                    ChallengeExemptionReason = table.Column<string>(type: "text", nullable: true),
                    CreationTimestamp = table.Column<long>(type: "bigint", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    ExecutionTimestamp = table.Column<string>(type: "text", nullable: true),
                    InitiatorId = table.Column<string>(type: "text", nullable: true),
                    InvoiceUploaded = table.Column<bool>(type: "boolean", nullable: false),
                    Narration = table.Column<string>(type: "text", nullable: true),
                    OutWireTranferType = table.Column<string>(type: "text", nullable: true),
                    ProviderType = table.Column<string>(type: "text", nullable: true),
                    Range = table.Column<string>(type: "text", nullable: true),
                    Reference = table.Column<string>(type: "text", nullable: true),
                    ScheduledTimestamp = table.Column<string>(type: "text", nullable: true),
                    SessionId = table.Column<string>(type: "text", nullable: true),
                    Signature = table.Column<string>(type: "text", nullable: true),
                    SortCode = table.Column<string>(type: "text", nullable: true),
                    SourceAccount = table.Column<string>(type: "text", nullable: true),
                    SourceAccountName = table.Column<string>(type: "text", nullable: true),
                    State = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    TransDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TransactionCode = table.Column<string>(type: "text", nullable: true),
                    TransactionType = table.Column<string>(type: "text", nullable: true),
                    VFDTxnId = table.Column<string>(type: "text", nullable: true),
                    WalletID = table.Column<string>(type: "text", nullable: true),
                    WeavrTransactionId = table.Column<string>(type: "text", nullable: true),
                    beneficiaryAddress = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Transactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Transactions_AccountWallets_WalletID",
                        column: x => x.WalletID,
                        principalSchema: _Schema,
                        principalTable: "AccountWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Beneficiaries",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    BatchId = table.Column<string>(type: "text", nullable: true),
                    BenJobPaysAccountId = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    InstrumentId = table.Column<string>(type: "text", nullable: true),
                    WeavrBeneficiaryId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Beneficiaries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Beneficiaries_BeneficiaryBatches_BatchId",
                        column: x => x.BatchId,
                        principalSchema: _Schema,
                        principalTable: "BeneficiaryBatches",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysCompanySubscriptions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysCompanySubscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysCompanySubscriptions_JobPaysSubscriptions_Subscripti~",
                        column: x => x.SubscriptionId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysSubscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobPaysCompanySubscriptions_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "JobPaysEnterpriseSubscriptions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ActivityLogHistoryLimit = table.Column<int>(type: "integer", nullable: false),
                    AmountPaid = table.Column<double>(type: "double precision", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    CalenderLimit = table.Column<int>(type: "integer", nullable: false),
                    InternalCommunicationHistoryLimit = table.Column<int>(type: "integer", nullable: false),
                    PlanId = table.Column<string>(type: "text", nullable: true),
                    ProjectLimit = table.Column<int>(type: "integer", nullable: false),
                    StorageLimit = table.Column<int>(type: "integer", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    TimeSheetManagement = table.Column<string>(type: "text", nullable: true),
                    UsersLimit = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobPaysEnterpriseSubscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobPaysEnterpriseSubscriptions_JobPaysSubscriptions_Subscri~",
                        column: x => x.SubscriptionId,
                        principalSchema: _Schema,
                        principalTable: "JobPaysSubscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_JobPaysEnterpriseSubscriptions_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KpiCollaborators",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    KpiId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserProfileId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KpiCollaborators", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KpiCollaborators_Kpis_KpiId",
                        column: x => x.KpiId,
                        principalSchema: _Schema,
                        principalTable: "Kpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "KpiExpectedOutcome",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Date = table.Column<DateTime>(type: "timestamp", nullable: false),
                    KpiId = table.Column<Guid>(type: "uuid", nullable: true),
                    maximum = table.Column<double>(type: "double precision", nullable: false),
                    minimum = table.Column<double>(type: "double precision", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_KpiExpectedOutcome", x => x.Id);
                    table.ForeignKey(
                        name: "FK_KpiExpectedOutcome_Kpis_KpiId",
                        column: x => x.KpiId,
                        principalSchema: _Schema,
                        principalTable: "Kpis",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "InterviewStage",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    InterviewDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    JobVacancyId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InterviewStage", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InterviewStage_JobVacancy_JobVacancyId",
                        column: x => x.JobVacancyId,
                        principalSchema: _Schema,
                        principalTable: "JobVacancy",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "JobApplication",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationDate = table.Column<string>(type: "text", nullable: true),
                    AverageScore = table.Column<string>(type: "text", nullable: true),
                    CandidateId = table.Column<string>(type: "text", nullable: true),
                    InternalVacancyProcessId = table.Column<string>(type: "text", nullable: true),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    ScoreCardAssessorName = table.Column<string>(type: "text", nullable: true),
                    ScoreCardAssessorSignature = table.Column<string>(type: "text", nullable: true),
                    ScoreCardAverageScore = table.Column<string>(type: "text", nullable: true),
                    ScoreCardFinalComment = table.Column<string>(type: "text", nullable: true),
                    ScoreCardRecommended = table.Column<bool>(type: "boolean", nullable: false),
                    ScoreCardUrl = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    VacancyId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobApplication", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobApplication_AspNetUsers_CandidateId",
                        column: x => x.CandidateId,
                        principalSchema: _Schema,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_JobApplication_JobVacancy_VacancyId",
                        column: x => x.VacancyId,
                        principalSchema: _Schema,
                        principalTable: "JobVacancy",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "JobQuestion",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    InputType = table.Column<string>(type: "text", nullable: true),
                    JobVacancyId = table.Column<Guid>(type: "uuid", nullable: false),
                    Question = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobQuestion", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobQuestion_JobVacancy_JobVacancyId",
                        column: x => x.JobVacancyId,
                        principalSchema: _Schema,
                        principalTable: "JobVacancy",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "JobSkill",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    JobVacancyId = table.Column<Guid>(type: "uuid", nullable: true),
                    SkillName = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JobSkill", x => x.Id);
                    table.ForeignKey(
                        name: "FK_JobSkill_JobVacancy_JobVacancyId",
                        column: x => x.JobVacancyId,
                        principalSchema: _Schema,
                        principalTable: "JobVacancy",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SalePerson",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    JobVacancyId = table.Column<Guid>(type: "uuid", nullable: false),
                    Lastname = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalePerson", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalePerson_JobVacancy_JobVacancyId",
                        column: x => x.JobVacancyId,
                        principalSchema: _Schema,
                        principalTable: "JobVacancy",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Campaigns",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Channels = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsPublished = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    NewOwnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    OwnerId = table.Column<string>(type: "text", nullable: true),
                    PremiumRequired = table.Column<bool>(type: "boolean", nullable: false),
                    ProfilePictureRequired = table.Column<bool>(type: "boolean", nullable: false),
                    SequenceId = table.Column<Guid>(type: "uuid", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    TemplateId = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<string>(type: "text", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Campaigns", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Campaigns_CampaignsTemplates_TemplateId",
                        column: x => x.TemplateId,
                        principalSchema: _Schema,
                        principalTable: "CampaignsTemplates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Campaigns_Sequences_SequenceId",
                        column: x => x.SequenceId,
                        principalSchema: _Schema,
                        principalTable: "Sequences",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Campaigns_UserProfiles_OwnerId",
                        column: x => x.OwnerId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "QuestionOption",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    JobQuestionId = table.Column<Guid>(type: "uuid", nullable: false),
                    OptionName = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QuestionOption", x => x.Id);
                    table.ForeignKey(
                        name: "FK_QuestionOption_JobQuestion_JobQuestionId",
                        column: x => x.JobQuestionId,
                        principalSchema: _Schema,
                        principalTable: "JobQuestion",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMEmail",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Body = table.Column<string>(type: "text", nullable: true),
                    CampaignId = table.Column<Guid>(type: "uuid", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    RecipientEmail = table.Column<string>(type: "text", nullable: true),
                    SenderEmail = table.Column<string>(type: "text", nullable: true),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMEmail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMEmail_Campaigns_CampaignId",
                        column: x => x.CampaignId,
                        principalSchema: _Schema,
                        principalTable: "Campaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Leads",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AccountName = table.Column<string>(type: "text", nullable: true),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: true),
                    EmailAddress = table.Column<string>(type: "text", nullable: true),
                    Lead = table.Column<Guid>(type: "uuid", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Leads", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Leads_Campaigns_Lead",
                        column: x => x.Lead,
                        principalSchema: _Schema,
                        principalTable: "Campaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Leads_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "OutgoingSms",
                schema: _Schema,
                columns: table => new
                {
                    MessageSid = table.Column<string>(type: "text", nullable: false),
                    CampaignId = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateResponded = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateSent = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DateUpdated = table.Column<DateTime>(type: "timestamp", nullable: true),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    IsWhatsapp = table.Column<bool>(type: "boolean", nullable: false),
                    Message = table.Column<string>(type: "text", nullable: true),
                    MessageResponse = table.Column<string>(type: "text", nullable: true),
                    MessageStatus = table.Column<string>(type: "text", nullable: true),
                    RecieverNumber = table.Column<string>(type: "text", nullable: true),
                    SequenceId = table.Column<Guid>(type: "uuid", nullable: false),
                    TwilioPhoneNumber = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OutgoingSms", x => x.MessageSid);
                    table.ForeignKey(
                        name: "FK_OutgoingSms_Campaigns_CampaignId",
                        column: x => x.CampaignId,
                        principalSchema: _Schema,
                        principalTable: "Campaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OutgoingSms_Sequences_SequenceId",
                        column: x => x.SequenceId,
                        principalSchema: _Schema,
                        principalTable: "Sequences",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Subscriber",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CampaignId = table.Column<Guid>(type: "uuid", nullable: false),
                    Channels = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    LastUpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subscriber", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Subscriber_Campaigns_CampaignId",
                        column: x => x.CampaignId,
                        principalSchema: _Schema,
                        principalTable: "Campaigns",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CRMComments",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DueDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMComments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMComments_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: _Schema,
                        principalTable: "Leads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CRMComments_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CRMTasks",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DueDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LastUpdate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: true),
                    TaskName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CRMTasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CRMTasks_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: _Schema,
                        principalTable: "Leads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CRMTasks_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Deals",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyId = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactEmail = table.Column<string>(type: "text", nullable: true),
                    ContactFirstName = table.Column<string>(type: "text", nullable: true),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactLastName = table.Column<string>(type: "text", nullable: true),
                    ContactPhoneNumber = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateModified = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealSize = table.Column<string>(type: "text", nullable: true),
                    DealStage = table.Column<int>(type: "integer", nullable: false),
                    DealType = table.Column<int>(type: "integer", nullable: true),
                    JobApplicationId = table.Column<Guid>(type: "uuid", nullable: true),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: false),
                    ModifiedBy = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Deals", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Deals_Companies_CompanyId",
                        column: x => x.CompanyId,
                        principalSchema: _Schema,
                        principalTable: "Companies",
                        principalColumn: "CompanyId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Deals_JobApplication_JobApplicationId",
                        column: x => x.JobApplicationId,
                        principalSchema: _Schema,
                        principalTable: "JobApplication",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Deals_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: _Schema,
                        principalTable: "Leads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Deals_UserProfiles_UserId",
                        column: x => x.UserId,
                        principalSchema: _Schema,
                        principalTable: "UserProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "LeadNTag",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: false),
                    TagId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LeadNTag", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LeadNTag_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: _Schema,
                        principalTable: "Leads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_LeadNTag_Tags_TagId",
                        column: x => x.TagId,
                        principalSchema: _Schema,
                        principalTable: "Tags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "LeadTransferHistory",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    FromUserId = table.Column<string>(type: "text", nullable: true),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: false),
                    ToUserId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LeadTransferHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LeadTransferHistory_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: _Schema,
                        principalTable: "Leads",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DealActivity",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealId = table.Column<Guid>(type: "uuid", nullable: false),
                    Identifier = table.Column<string>(type: "text", nullable: true),
                    IdentifierTitle = table.Column<string>(type: "text", nullable: true),
                    PayloadBody = table.Column<string>(type: "text", nullable: true),
                    PayloadCompany = table.Column<string>(type: "text", nullable: true),
                    PayloadName = table.Column<string>(type: "text", nullable: true),
                    PayloadPhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PayloadTitle = table.Column<string>(type: "text", nullable: true),
                    PayloadWebsite = table.Column<string>(type: "text", nullable: true),
                    body = table.Column<string>(type: "text", nullable: true),
                    email = table.Column<string>(type: "text", nullable: true),
                    title = table.Column<string>(type: "text", nullable: true),
                    type = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DealActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DealActivity_Deals_DealId",
                        column: x => x.DealId,
                        principalSchema: _Schema,
                        principalTable: "Deals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DealEmail",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Body = table.Column<string>(type: "text", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealId = table.Column<Guid>(type: "uuid", nullable: false),
                    Destination = table.Column<string>(type: "text", nullable: true),
                    Subject = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DealEmail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DealEmail_Deals_DealId",
                        column: x => x.DealId,
                        principalSchema: _Schema,
                        principalTable: "Deals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DealsContacts",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ContactId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DealId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DealsContacts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DealsContacts_Contacts_ContactId",
                        column: x => x.ContactId,
                        principalSchema: _Schema,
                        principalTable: "Contacts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DealsContacts_Deals_DealId",
                        column: x => x.DealId,
                        principalSchema: _Schema,
                        principalTable: "Deals",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollTransactions",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Amount = table.Column<long>(type: "bigint", nullable: false),
                    Company = table.Column<string>(type: "text", nullable: true),
                    Date = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    PayrollRequestId = table.Column<string>(type: "text", nullable: true),
                    TransactionType = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollTransactions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PayrollRequests",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ChargePercentage = table.Column<int>(type: "integer", nullable: false),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    DateAccepted = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DateRequested = table.Column<DateTime>(type: "timestamp", nullable: false),
                    EmployeeEmail = table.Column<string>(type: "text", nullable: true),
                    EmployeeFullName = table.Column<string>(type: "text", nullable: true),
                    Gross = table.Column<decimal>(type: "numeric", nullable: false),
                    Interval = table.Column<int>(type: "integer", nullable: false),
                    NetPay = table.Column<decimal>(type: "numeric", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserProfileId = table.Column<string>(type: "text", nullable: true),
                    VendorId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollRequests_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollServices",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    PayrollVendorId = table.Column<string>(type: "text", nullable: true),
                    Percentage = table.Column<double>(type: "double precision", nullable: false),
                    ServiceName = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollServices", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VendorWallets",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    CurrentBalance = table.Column<decimal>(type: "numeric", nullable: false),
                    PayrollVendorId = table.Column<string>(type: "text", nullable: true),
                    TotalEarning = table.Column<decimal>(type: "numeric", nullable: false),
                    TotalTaxRemitance = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VendorWallets", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PayrollVendors",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Bio = table.Column<string>(type: "text", nullable: true),
                    CompanyName = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    Logo = table.Column<string>(type: "text", nullable: true),
                    User = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    VendorWalletId = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollVendors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollVendors_VendorWallets_VendorWalletId",
                        column: x => x.VendorWalletId,
                        principalSchema: _Schema,
                        principalTable: "VendorWallets",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ProjectMgmt_Projects_CompanyId",
                schema: _Schema,
                table: "ProjectMgmt_Projects",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_AccountWalletBeneficiaries_WalletId",
                schema: _Schema,
                table: "AccountWalletBeneficiaries",
                column: "WalletId");

            migrationBuilder.CreateIndex(
                name: "IX_AccountWallets_JobPaysAccountID",
                schema: _Schema,
                table: "AccountWallets",
                column: "JobPaysAccountID");

            migrationBuilder.CreateIndex(
                name: "IX_Actors_PhoneNumberId",
                schema: _Schema,
                table: "Actors",
                column: "PhoneNumberId");

            migrationBuilder.CreateIndex(
                name: "IX_AutoSaveSettings_UserId",
                schema: _Schema,
                table: "AutoSaveSettings",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AutoSaveTransactions_AutoSaveSettingId",
                schema: _Schema,
                table: "AutoSaveTransactions",
                column: "AutoSaveSettingId");

            migrationBuilder.CreateIndex(
                name: "IX_Beneficiaries_BatchId",
                schema: _Schema,
                table: "Beneficiaries",
                column: "BatchId");

            migrationBuilder.CreateIndex(
                name: "IX_BeneficiaryBatches_JobPaysAcctId",
                schema: _Schema,
                table: "BeneficiaryBatches",
                column: "JobPaysAcctId");

            migrationBuilder.CreateIndex(
                name: "IX_BridgeCardTransactionHistories_WalletId",
                schema: _Schema,
                table: "BridgeCardTransactionHistories",
                column: "WalletId");

            migrationBuilder.CreateIndex(
                name: "IX_Campaigns_OwnerId",
                schema: _Schema,
                table: "Campaigns",
                column: "OwnerId");

            migrationBuilder.CreateIndex(
                name: "IX_Campaigns_SequenceId",
                schema: _Schema,
                table: "Campaigns",
                column: "SequenceId");

            migrationBuilder.CreateIndex(
                name: "IX_Campaigns_TemplateId",
                schema: _Schema,
                table: "Campaigns",
                column: "TemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_CampaignsTemplates_SequenceId",
                schema: _Schema,
                table: "CampaignsTemplates",
                column: "SequenceId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateSummary_UserId",
                schema: _Schema,
                table: "CandidateSummary",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Cards_ManagedAccountId",
                schema: _Schema,
                table: "Cards",
                column: "ManagedAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_Companies_UserId",
                schema: _Schema,
                table: "Companies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycAddresses_CompanyKycId",
                schema: _Schema,
                table: "CompanyKycAddresses",
                column: "CompanyKycId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycCategories_CategoryId",
                schema: _Schema,
                table: "CompanyKycCategories",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycCategories_CompanyKycId",
                schema: _Schema,
                table: "CompanyKycCategories",
                column: "CompanyKycId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycDataClasses_CompanyKycId",
                schema: _Schema,
                table: "CompanyKycDataClasses",
                column: "CompanyKycId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycDataClasses_DataClassId",
                schema: _Schema,
                table: "CompanyKycDataClasses",
                column: "DataClassId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycDocuments_CompanyKycId",
                schema: _Schema,
                table: "CompanyKycDocuments",
                column: "CompanyKycId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycSignatories_CompanyKycId",
                schema: _Schema,
                table: "CompanyKycSignatories",
                column: "CompanyKycId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycSignatories_KycId",
                schema: _Schema,
                table: "CompanyKycSignatories",
                column: "KycId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanyKycUBOs_CompanyKycId",
                schema: _Schema,
                table: "CompanyKycUBOs",
                column: "CompanyKycId");

            migrationBuilder.CreateIndex(
                name: "IX_ContactNoteNoteOwner_NoteOwnersId",
                schema: _Schema,
                table: "ContactNoteNoteOwner",
                column: "NoteOwnersId");

            migrationBuilder.CreateIndex(
                name: "IX_ContactNotes_CRMContactId",
                schema: _Schema,
                table: "ContactNotes",
                column: "CRMContactId");

            migrationBuilder.CreateIndex(
                name: "IX_CreditTransactions_SimWalletId",
                schema: _Schema,
                table: "CreditTransactions",
                column: "SimWalletId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMCampaignOnwer_CRMCampaignId",
                schema: _Schema,
                table: "CRMCampaignOnwer",
                column: "CRMCampaignId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMCampaignSequences_CRMCampaignSequenceId",
                schema: _Schema,
                table: "CRMCampaignSequences",
                column: "CRMCampaignSequenceId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMCollaborator_CRMMeetingId",
                schema: _Schema,
                table: "CRMCollaborator",
                column: "CRMMeetingId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMCollaborator_CRMTodoId",
                schema: _Schema,
                table: "CRMCollaborator",
                column: "CRMTodoId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMComments_LeadId",
                schema: _Schema,
                table: "CRMComments",
                column: "LeadId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMComments_UserId",
                schema: _Schema,
                table: "CRMComments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMCompanyCRMCompanyCollaborator_CRMCompanyCollaboratorsId",
                schema: _Schema,
                table: "CRMCompanyCRMCompanyCollaborator",
                column: "CRMCompanyCollaboratorsId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMCompanyCRMCompanyTag_CompanyTagsId",
                schema: _Schema,
                table: "CRMCompanyCRMCompanyTag",
                column: "CompanyTagsId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMContactCRMContactCollaborator_CRMContactsId",
                schema: _Schema,
                table: "CRMContactCRMContactCollaborator",
                column: "CRMContactsId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMContactCRMContactTag_TagsId",
                schema: _Schema,
                table: "CRMContactCRMContactTag",
                column: "TagsId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMContactOwners_ContactId",
                schema: _Schema,
                table: "CRMContactOwners",
                column: "ContactId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMDealCRMDealCollaborator_CRMDealsDealId",
                schema: _Schema,
                table: "CRMDealCRMDealCollaborator",
                column: "CRMDealsDealId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMDealStatuses_CRMDealId",
                schema: _Schema,
                table: "CRMDealStatuses",
                column: "CRMDealId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMEmail_CampaignId",
                schema: _Schema,
                table: "CRMEmail",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMEmailRecords_CRMCampaignId",
                schema: _Schema,
                table: "CRMEmailRecords",
                column: "CRMCampaignId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiActivities_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiActivities",
                column: "CRMKpiId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiCollaborators_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiCollaborators",
                column: "CRMKpiId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiExpectedOutComes_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiExpectedOutComes",
                column: "CRMKpiId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiIncentives_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiIncentives",
                column: "CRMKpiId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiMeasurements_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiMeasurements",
                column: "CRMKpiId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiProcesses_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiProcesses",
                column: "CRMKpiId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMKpiSources_CRMKpiId",
                schema: _Schema,
                table: "CRMKpiSources",
                column: "CRMKpiId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMLeadCRMLeadCollaborator_CRMLeadsId",
                schema: _Schema,
                table: "CRMLeadCRMLeadCollaborator",
                column: "CRMLeadsId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMLeadCRMTag_TagsId",
                schema: _Schema,
                table: "CRMLeadCRMTag",
                column: "TagsId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMLeadOwners_CRMLeadId",
                schema: _Schema,
                table: "CRMLeadOwners",
                column: "CRMLeadId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMLeads_CRMCampaignId",
                schema: _Schema,
                table: "CRMLeads",
                column: "CRMCampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMNotification_UserId",
                schema: _Schema,
                table: "CRMNotification",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMPackagePricings_PricingPlanId",
                schema: _Schema,
                table: "CRMPackagePricings",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMPhoneNumbers_CapabilitiesId",
                schema: _Schema,
                table: "CRMPhoneNumbers",
                column: "CapabilitiesId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMPricingAndFeatures_FeatureId",
                schema: _Schema,
                table: "CRMPricingAndFeatures",
                column: "FeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMPricingAndFeatures_PricingPlanId",
                schema: _Schema,
                table: "CRMPricingAndFeatures",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMSequenceMails_CRMEmailSequenceId",
                schema: _Schema,
                table: "CRMSequenceMails",
                column: "CRMEmailSequenceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CRMSocialMedias_CRMBuisinessCardId",
                schema: _Schema,
                table: "CRMSocialMedias",
                column: "CRMBuisinessCardId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMSubscriptions_BillingAddressId",
                schema: _Schema,
                table: "CRMSubscriptions",
                column: "BillingAddressId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMSubscriptions_PricingPlanId",
                schema: _Schema,
                table: "CRMSubscriptions",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMSubscriptions_TenantId",
                schema: _Schema,
                table: "CRMSubscriptions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMSubscriptions_UserId",
                schema: _Schema,
                table: "CRMSubscriptions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMTagCRMTodo_TodosId",
                schema: _Schema,
                table: "CRMTagCRMTodo",
                column: "TodosId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMTasks_LeadId",
                schema: _Schema,
                table: "CRMTasks",
                column: "LeadId");

            migrationBuilder.CreateIndex(
                name: "IX_CRMTasks_UserId",
                schema: _Schema,
                table: "CRMTasks",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_DataClassCategories_CategoryId",
                schema: _Schema,
                table: "DataClassCategories",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_DataClassCategories_DataClassId",
                schema: _Schema,
                table: "DataClassCategories",
                column: "DataClassId");

            migrationBuilder.CreateIndex(
                name: "IX_DealActivity_DealId",
                schema: _Schema,
                table: "DealActivity",
                column: "DealId");

            migrationBuilder.CreateIndex(
                name: "IX_DealEmail_DealId",
                schema: _Schema,
                table: "DealEmail",
                column: "DealId");

            migrationBuilder.CreateIndex(
                name: "IX_Deals_CompanyId",
                schema: _Schema,
                table: "Deals",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Deals_JobApplicationId",
                schema: _Schema,
                table: "Deals",
                column: "JobApplicationId");

            migrationBuilder.CreateIndex(
                name: "IX_Deals_LeadId",
                schema: _Schema,
                table: "Deals",
                column: "LeadId");

            migrationBuilder.CreateIndex(
                name: "IX_Deals_UserId",
                schema: _Schema,
                table: "Deals",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_DealsContacts_ContactId",
                schema: _Schema,
                table: "DealsContacts",
                column: "ContactId");

            migrationBuilder.CreateIndex(
                name: "IX_DealsContacts_DealId",
                schema: _Schema,
                table: "DealsContacts",
                column: "DealId");

            migrationBuilder.CreateIndex(
                name: "IX_InterviewStage_JobVacancyId",
                schema: _Schema,
                table: "InterviewStage",
                column: "JobVacancyId");

            migrationBuilder.CreateIndex(
                name: "IX_JobApplication_CandidateId",
                schema: _Schema,
                table: "JobApplication",
                column: "CandidateId");

            migrationBuilder.CreateIndex(
                name: "IX_JobApplication_VacancyId",
                schema: _Schema,
                table: "JobApplication",
                column: "VacancyId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysCards_WalletId",
                schema: _Schema,
                table: "JobPaysCards",
                column: "WalletId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysClientCompanyAddresses_ClientCompanyId",
                schema: _Schema,
                table: "JobPaysClientCompanyAddresses",
                column: "ClientCompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysCompanySubscriptions_SubscriptionId",
                schema: _Schema,
                table: "JobPaysCompanySubscriptions",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysCompanySubscriptions_TenantId",
                schema: _Schema,
                table: "JobPaysCompanySubscriptions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysEnterpriseSubscriptions_SubscriptionId",
                schema: _Schema,
                table: "JobPaysEnterpriseSubscriptions",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysEnterpriseSubscriptions_TenantId",
                schema: _Schema,
                table: "JobPaysEnterpriseSubscriptions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysPackagePricing_PricingPlanId",
                schema: _Schema,
                table: "JobPaysPackagePricing",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysPricingAndFeatures_FeatureId",
                schema: _Schema,
                table: "JobPaysPricingAndFeatures",
                column: "FeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysPricingAndFeatures_PricingPlanId",
                schema: _Schema,
                table: "JobPaysPricingAndFeatures",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptionHistory_PricingPlanId",
                schema: _Schema,
                table: "JobPaysSubscriptionHistory",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptionHistory_TenantId",
                schema: _Schema,
                table: "JobPaysSubscriptionHistory",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptionHistory_UserId",
                schema: _Schema,
                table: "JobPaysSubscriptionHistory",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptions_BillingAddressId",
                schema: _Schema,
                table: "JobPaysSubscriptions",
                column: "BillingAddressId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptions_PricingPlanId",
                schema: _Schema,
                table: "JobPaysSubscriptions",
                column: "PricingPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptions_TenantId",
                schema: _Schema,
                table: "JobPaysSubscriptions",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_JobPaysSubscriptions_UserId",
                schema: _Schema,
                table: "JobPaysSubscriptions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_JobQuestion_JobVacancyId",
                schema: _Schema,
                table: "JobQuestion",
                column: "JobVacancyId");

            migrationBuilder.CreateIndex(
                name: "IX_JobSkill_JobVacancyId",
                schema: _Schema,
                table: "JobSkill",
                column: "JobVacancyId");

            migrationBuilder.CreateIndex(
                name: "IX_JobVacancy_CompanyId",
                schema: _Schema,
                table: "JobVacancy",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_JobVacancy_LocationId",
                schema: _Schema,
                table: "JobVacancy",
                column: "LocationId");

            migrationBuilder.CreateIndex(
                name: "IX_JobVacancy_NationalLaguageId",
                schema: _Schema,
                table: "JobVacancy",
                column: "NationalLaguageId");

            migrationBuilder.CreateIndex(
                name: "IX_JobVacancy_UserId",
                schema: _Schema,
                table: "JobVacancy",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_KpiCollaborators_KpiId",
                schema: _Schema,
                table: "KpiCollaborators",
                column: "KpiId");

            migrationBuilder.CreateIndex(
                name: "IX_KpiExpectedOutcome_KpiId",
                schema: _Schema,
                table: "KpiExpectedOutcome",
                column: "KpiId");

            migrationBuilder.CreateIndex(
                name: "IX_Kpis_CreatedBy",
                schema: _Schema,
                table: "Kpis",
                column: "CreatedBy");

            migrationBuilder.CreateIndex(
                name: "IX_Kpis_KpiDepartmentId",
                schema: _Schema,
                table: "Kpis",
                column: "KpiDepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_Kpis_KpiSourceId",
                schema: _Schema,
                table: "Kpis",
                column: "KpiSourceId");

            migrationBuilder.CreateIndex(
                name: "IX_Kpis_ProcessId",
                schema: _Schema,
                table: "Kpis",
                column: "ProcessId");

            migrationBuilder.CreateIndex(
                name: "IX_KycCategories_CategoryId",
                schema: _Schema,
                table: "KycCategories",
                column: "CategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_KycCategories_KycId",
                schema: _Schema,
                table: "KycCategories",
                column: "KycId");

            migrationBuilder.CreateIndex(
                name: "IX_KycDataClasses_DataClassId",
                schema: _Schema,
                table: "KycDataClasses",
                column: "DataClassId");

            migrationBuilder.CreateIndex(
                name: "IX_KycDataClasses_KycId",
                schema: _Schema,
                table: "KycDataClasses",
                column: "KycId");

            migrationBuilder.CreateIndex(
                name: "IX_KycEmployeeGuarantors_KycId",
                schema: _Schema,
                table: "KycEmployeeGuarantors",
                column: "KycId");

            migrationBuilder.CreateIndex(
                name: "IX_KYCs_UserId",
                schema: _Schema,
                table: "KYCs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_KYCs_UserProfileId",
                schema: _Schema,
                table: "KYCs",
                column: "UserProfileId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LeadNTag_LeadId",
                schema: _Schema,
                table: "LeadNTag",
                column: "LeadId");

            migrationBuilder.CreateIndex(
                name: "IX_LeadNTag_TagId",
                schema: _Schema,
                table: "LeadNTag",
                column: "TagId");

            migrationBuilder.CreateIndex(
                name: "IX_Leads_Lead",
                schema: _Schema,
                table: "Leads",
                column: "Lead");

            migrationBuilder.CreateIndex(
                name: "IX_Leads_UserId",
                schema: _Schema,
                table: "Leads",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_LeadTransferHistory_LeadId",
                schema: _Schema,
                table: "LeadTransferHistory",
                column: "LeadId");

            migrationBuilder.CreateIndex(
                name: "IX_Module_ClassId",
                schema: _Schema,
                table: "Module",
                column: "ClassId");

            migrationBuilder.CreateIndex(
                name: "IX_NFCs_KycId",
                schema: _Schema,
                table: "NFCs",
                column: "KycId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NoteUploads_ContactNoteId",
                schema: _Schema,
                table: "NoteUploads",
                column: "ContactNoteId");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingSms_CampaignId",
                schema: _Schema,
                table: "OutgoingSms",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_OutgoingSms_SequenceId",
                schema: _Schema,
                table: "OutgoingSms",
                column: "SequenceId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollRequests_TenantId",
                schema: _Schema,
                table: "PayrollRequests",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollRequests_VendorId",
                schema: _Schema,
                table: "PayrollRequests",
                column: "VendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollServices_PayrollVendorId",
                schema: _Schema,
                table: "PayrollServices",
                column: "PayrollVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollTransactions_PayrollRequestId",
                schema: _Schema,
                table: "PayrollTransactions",
                column: "PayrollRequestId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollVendors_VendorWalletId",
                schema: _Schema,
                table: "PayrollVendors",
                column: "VendorWalletId");

            migrationBuilder.CreateIndex(
                name: "IX_Profiles_UserId",
                schema: _Schema,
                table: "Profiles",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_QuestionOption_JobQuestionId",
                schema: _Schema,
                table: "QuestionOption",
                column: "JobQuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_SalePerson_JobVacancyId",
                schema: _Schema,
                table: "SalePerson",
                column: "JobVacancyId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Subscriber_CampaignId",
                schema: _Schema,
                table: "Subscriber",
                column: "CampaignId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_WalletID",
                schema: _Schema,
                table: "Transactions",
                column: "WalletID");

            migrationBuilder.CreateIndex(
                name: "IX_UnregisteredUserCompanies_UserId",
                schema: _Schema,
                table: "UnregisteredUserCompanies",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VendorWallets_PayrollVendorId",
                schema: _Schema,
                table: "VendorWallets",
                column: "PayrollVendorId");

            migrationBuilder.CreateIndex(
                name: "IX_VoiceNoteUploads_ContactNoteId",
                schema: _Schema,
                table: "VoiceNoteUploads",
                column: "ContactNoteId");

            migrationBuilder.CreateIndex(
                name: "IX_weavrCorpKycs_JobPaysAccountId",
                schema: _Schema,
                table: "weavrCorpKycs",
                column: "JobPaysAccountId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkingDays_CRMWorkScheduleId",
                schema: _Schema,
                table: "WorkingDays",
                column: "CRMWorkScheduleId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkingHours_WorkingDayId",
                schema: _Schema,
                table: "WorkingHours",
                column: "WorkingDayId");

            migrationBuilder.AddForeignKey(
                name: "FK_ProjectMgmt_Projects_Companies_CompanyId",
                schema: _Schema,
                table: "ProjectMgmt_Projects",
                column: "CompanyId",
                principalSchema: _Schema,
                principalTable: "Companies",
                principalColumn: "CompanyId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollTransactions_PayrollRequests_PayrollRequestId",
                schema: _Schema,
                table: "PayrollTransactions",
                column: "PayrollRequestId",
                principalSchema: _Schema,
                principalTable: "PayrollRequests",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollRequests_PayrollVendors_VendorId",
                schema: _Schema,
                table: "PayrollRequests",
                column: "VendorId",
                principalSchema: _Schema,
                principalTable: "PayrollVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PayrollServices_PayrollVendors_PayrollVendorId",
                schema: _Schema,
                table: "PayrollServices",
                column: "PayrollVendorId",
                principalSchema: _Schema,
                principalTable: "PayrollVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_VendorWallets_PayrollVendors_PayrollVendorId",
                schema: _Schema,
                table: "VendorWallets",
                column: "PayrollVendorId",
                principalSchema: _Schema,
                principalTable: "PayrollVendors",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
