using System.Threading.Tasks;
using Jobid.App.Helpers.Services.Contract;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class NullPdfService : IPdfService
    {
        public Task<byte[]> GeneratePdfAsync(string htmlContent)
        {
            // Return an empty byte array when PDF generation is disabled
            // This prevents the application from crashing when PDF generation is requested
            // but the PDF libraries are not available
            // 
            // Note: Calling code should check if the result is null or empty
            // before using it for file operations
            return Task.FromResult(new byte[0]);
        }
    }
}
