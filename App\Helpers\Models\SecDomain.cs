﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Helpers.Models
{
    public class SecDomain
    {
        public Guid Id { get; set; }
        public string Domain { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public string UpdatedBy { get; set; }

        [ForeignKey("Tenant")]
        public Guid TenantId { get; set; }

        public Tenant.Model.Tenant Tenant { get; set; }

        public SecDomain()
        {
            Id = Guid.NewGuid();
            CreatedOn = DateTime.UtcNow;
        }
    }
}
