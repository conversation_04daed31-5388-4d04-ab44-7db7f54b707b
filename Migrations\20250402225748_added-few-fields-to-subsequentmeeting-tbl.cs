﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class addedfewfieldstosubsequentmeetingtbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public addedfewfieldstosubsequentmeetingtbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CalenderUploads_CalenderMeetings_MeetingId",
                schema: _Schema,
                table: "CalenderUploads");

            migrationBuilder.AddColumn<DateTime>(
                name: "EndTime",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "timestamp",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "HasMeetingHappeed",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsCanceled",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "MakeSchdulePrivate",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "MeetLength",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MeetingDuration",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NotifyMe",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NotifyMembersIn",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                schema: _Schema,
                table: "SubsequentMeetings",
                type: "timestamp",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AlterColumn<Guid>(
                name: "MeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<Guid>(
                name: "SubsequentMeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CalenderUploads_SubsequentMeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                column: "SubsequentMeetingId");

            migrationBuilder.AddForeignKey(
                name: "FK_CalenderUploads_CalenderMeetings_MeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                column: "MeetingId",
                principalSchema: _Schema,
                principalTable: "CalenderMeetings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CalenderUploads_SubsequentMeetings_SubsequentMeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                column: "SubsequentMeetingId",
                principalSchema: _Schema,
                principalTable: "SubsequentMeetings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CalenderUploads_CalenderMeetings_MeetingId",
                schema: _Schema,
                table: "CalenderUploads");

            migrationBuilder.DropForeignKey(
                name: "FK_CalenderUploads_SubsequentMeetings_SubsequentMeetingId",
                schema: _Schema,
                table: "CalenderUploads");

            migrationBuilder.DropIndex(
                name: "IX_CalenderUploads_SubsequentMeetingId",
                schema: _Schema,
                table: "CalenderUploads");

            migrationBuilder.DropColumn(
                name: "EndTime",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "HasMeetingHappeed",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "IsCanceled",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "MakeSchdulePrivate",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "MeetLength",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "MeetingDuration",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "Name",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "NotifyMe",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "NotifyMembersIn",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                schema: _Schema,
                table: "SubsequentMeetings");

            migrationBuilder.DropColumn(
                name: "SubsequentMeetingId",
                schema: _Schema,
                table: "CalenderUploads");

            migrationBuilder.AlterColumn<Guid>(
                name: "MeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                type: "uuid",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CalenderUploads_CalenderMeetings_MeetingId",
                schema: _Schema,
                table: "CalenderUploads",
                column: "MeetingId",
                principalSchema: _Schema,
                principalTable: "CalenderMeetings",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
