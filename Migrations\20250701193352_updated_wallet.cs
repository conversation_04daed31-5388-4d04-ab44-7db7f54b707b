﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class updated_wallet : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updated_wallet(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PaymentId",
                schema: _Schema,
                table: "WalletTransactions",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "WalletTranToCompanyMappings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TransactionId = table.Column<string>(type: "text", nullable: true),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WalletTranToCompanyMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WalletTranToCompanyMappings_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WalletTranToCompanyMappings_TenantId",
                schema: _Schema,
                table: "WalletTranToCompanyMappings",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WalletTranToCompanyMappings",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "PaymentId",
                schema: _Schema,
                table: "WalletTransactions");
        }
    }
}
