﻿using Jobid.App.ActivityLog.Model;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ITimeSheetService
    {
        Task<TimeSheet> AddTimeSheet(TimeSheetVm timeSheetVm);
        Task<bool> UpdateTimeSheet(TimeSheetVm timeSheetVm, Guid id);
        Task<Page<TimeSheet>> GetTimeSheets(PaginationParameters parameters);
        Task<TimeSheet> GetTimeSheetById(Guid id);
        Task<Page<TimeSheet>> GetTimeSheetsByProjectId(PaginationParameters parameters, string Id);
        Task<bool> DeleteTimeSheet(TimeSheet timeSheet, string loggedInUserId);
        Task<Page<TimeSheet>> GetTimeSheetsByUserId(PaginationParameters parameters, string userId);
        Task<TimeSheetReport> GetTimeSheetReport(string projectId);
        Task<GenericResponse> GetTimeSheetRecords(PaginationParameters parameters, List<string> projectsIds, TimeSheetFilters filters, bool isArchived = false);
        Task<GenericResponse> GetTimeSheetRecordsForProjects(PaginationParameters parameters, TimeSheetFilters filters, bool isArchived = false);
        Task<GenericResponse> CalculateBillableandNonBillableHours(string projectId);
        Task<GenericResponse> CalculateBillableandNonBillableHours(PaginationParameters parameters, List<string> projectIds, TimeSheetFilters filters, bool isArchived = false);
        Task<UserDetailsForTeamSheetDto> GetUserDetailsById(string userId, string projectId, string loggedInUserId);
        Task<TimeSheetTodoDto> GetTodoDetailsById(string todoId, string loggedInUserId, string projectId = null);
        Task<bool> UpdateTodo(string todoId, UpdateTodoForTHDto model);
        Task<AllProjectsForTimeSheetDto> GetProjectDetailsById(string projectId, string loggedInUserId);
        Task<GenericResponse> CalculateDashboardData(TimeSheetDashboardFilters filters);
        Task<bool> UpdateRecordedTime(string Id, string recordedTime, string loggedInUserId);
        Task<bool> ArchiveTodos(List<string> todoIds, string loggedInUserId);
        Task<bool> UnArchiveTodos(List<string> todoIds, string loggedInUserId);
        Task<bool> LockOrUnlockTodos(List<string> todoIds, string loggedInUserId);
        Task<List<Activity>> GetTopRecentActivities(int numberOfActivities = 15);
        Task<bool> AddCommentToTodo(string todoId, string com, string loggedInUserId);
        Task<bool> EditComment(string commentId, string comment, string loggedInUserId);
        Task<List<TodoCommentDto>> GetCommentsForTodoAsync(string todoId);
        Task<bool> UpdateCompletionDate(string TimeSheetId, DateTime CompletionDate);
    }
}
