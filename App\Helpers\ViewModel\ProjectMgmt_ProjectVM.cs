﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.Helpers.ViewModel
{
    public class ProjectMgmt_ProjectVM
    {
        public string ProjectId { get; set; }
        public string Name { get; set; }
        public List<string> Members { get; set; }
        public string Description { get; set; }
        public string Summary { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<string> Tags { get; set; }
        public bool IsBillable { get; set; }
        public decimal? AmountPerSelectedFrequency { get; set; }
        public decimal? ExpectedProjectValue { get; set; }
        public string CurrencySymbol { get; set; }
        public AmountFrequency? AmountFrequency { get; set; }

        [CanInviteExternalUser(Helpers.Enums.Applications.Joble)]
        [ValidEmailChecks]
        public List<string> ExternalMembersEmails { get; set; }
        public ProjectStatus ProjectStatus { get; set; } = ProjectStatus.Active;
        public List<IFormFile> UploadFile { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
