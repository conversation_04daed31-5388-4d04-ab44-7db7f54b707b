﻿using Jobid.App.Helpers.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.ViewModel
{
    public class ActivityDto
    {
        public EventCategory EventCategory { get; set; } = EventCategory.Application;
        [Required]
        public string ActivitySummary { get; set; }
        public string Description { get; set; }
        public Applications? Application { get; set; }
        public string GenericUrl { get; set; }
        public string EventId { get; set; }
        public string By { get; set; }
        public string UserId { get; set; }
        public List<LogAttachmentDto>? LogAttachments { get; set; } = new List<LogAttachmentDto>();

        [JsonIgnore]
        public bool IsBulkTodoUpload { get; set; } = false;

        [JsonIgnore]
        public string subdomain { get; set; }
    }
}
