﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class wiki_tables : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public wiki_tables(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "WikiAccesses",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    AccessStatus = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WikiAccesses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WikiFiles",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FileName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FileType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    AwsKey = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    UploadStatus = table.Column<int>(type: "integer", nullable: false),
                    UploadedDate = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UploadedById = table.Column<Guid>(type: "uuid", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    LastAccessedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "timestamp", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WikiFiles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WikiFileDepartmentAccess",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    WikiFileId = table.Column<Guid>(type: "uuid", nullable: false),
                    DepartmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    RemovedAt = table.Column<DateTime>(type: "timestamp", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WikiFileDepartmentAccess", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WikiFileDepartmentAccess_Teams_DepartmentId",
                        column: x => x.DepartmentId,
                        principalSchema: _Schema,
                        principalTable: "Teams",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WikiFileDepartmentAccess_WikiFiles_WikiFileId",
                        column: x => x.WikiFileId,
                        principalSchema: _Schema,
                        principalTable: "WikiFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WikiFileDepartmentAccess_DepartmentId",
                schema: _Schema,
                table: "WikiFileDepartmentAccess",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_WikiFileDepartmentAccess_WikiFileId",
                schema: _Schema,
                table: "WikiFileDepartmentAccess",
                column: "WikiFileId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WikiAccesses",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "WikiFileDepartmentAccess",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "WikiFiles",
                schema: _Schema);
        }
    }
}
