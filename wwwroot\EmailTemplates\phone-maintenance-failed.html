<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Phone Number Maintenance Fee Failed</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f4f4f4;
      }
      .container {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #dc3545;
        margin-bottom: 10px;
      }
      .content {
        margin-bottom: 30px;
      }
      .details {
        background-color: #f8d7da;
        padding: 20px;
        border-radius: 5px;
        margin: 20px 0;
        border-left: 4px solid #dc3545;
      }
      .details h3 {
        color: #721c24;
        margin-top: 0;
      }
      .details ul {
        list-style: none;
        padding: 0;
      }
      .details li {
        padding: 8px 0;
        border-bottom: 1px solid #f5c6cb;
      }
      .details li:last-child {
        border-bottom: none;
      }
      .footer {
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #eee;
        color: #666;
      }
      .error-badge {
        display: inline-block;
        background-color: #dc3545;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        margin-bottom: 20px;
      }
      .action-button {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 5px;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Phone Number Maintenance Fee Failed</h1>
        <div class="error-badge">❌ Payment Failed</div>
      </div>

      <div class="content">
        <p>Dear {AdminName},</p>

        <p>
          We encountered an error while trying to charge your monthly phone
          number maintenance fee.
        </p>

        <div class="details">
          <h3>Charge Details</h3>
          <ul>
            <li><strong>Company:</strong> {CompanyName}</li>
            <li><strong>Phone Numbers:</strong> {PhoneNumberCount}</li>
            <li><strong>Fee per Number:</strong> ${PerNumberFee}</li>
            <li><strong>Amount:</strong> ${ChargeAmount}</li>
            <li><strong>Attempted Date:</strong> {ChargeDate}</li>
          </ul>
        </div>

        <p><strong>What you need to do:</strong></p>
        <ul>
          <li>Check your wallet balance and ensure sufficient funds</li>
          <li>Verify that your payment method is valid and active</li>
          <li>Contact our support team if you need assistance</li>
        </ul>

        <div style="text-align: center">
          <a href="#" class="action-button">Check Wallet & Retry</a>
        </div>

        <p>
          We will automatically retry the charge in a few days. However, we
          recommend addressing this issue immediately to avoid any service
          interruption.
        </p>

        <p>
          If you continue to experience issues, please contact our support team
          for assistance.
        </p>
      </div>

      <div class="footer">
        <p>This is an automated message from JobPro.</p>
        <p>© 2025 JobPro. All rights reserved.</p>
      </div>
    </div>
  </body>
</html>
