using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models.Wallet
{
    public class CompanyWallet
    {
        [Key]
        public Guid Id { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public CompanyWallet()
        {
            Id = Guid.NewGuid();
            Balance = 0.00m; // Initialize balance to zero
            CreatedAt = DateTime.UtcNow;
        }
    }
}
