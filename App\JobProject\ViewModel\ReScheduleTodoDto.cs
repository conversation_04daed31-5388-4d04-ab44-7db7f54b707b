﻿using System;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProject.ViewModel
{
    public class ReScheduleTodoDto
    {
        public Guid TodoId { get; set; }
        public DateTime StartDateAndTime { get; set; }
        public DateTime EndTime { get; set; }
        public DateTime? DueDate { get; set; }
        public TodoCustomFrequencyDto? TodoCustomFrequency { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }
        [JsonIgnore]
        public string Subdomain { get; set; }
    }
}
