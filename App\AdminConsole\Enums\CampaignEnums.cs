using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Jobid.App.AdminConsole.Enums
{
    /// <summary>
    /// Campaign status enumeration
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum CampaignStatus
    {
        Active = 1,
        Inactive = 2,
        Completed = 3,
        Cancelled = 4
    }

    /// <summary>
    /// Expected outcomes for campaigns
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum CampaignExpectedOutcome
    {
        BookInPersonMeeting = 1,
        BookOnlineMeeting = 2,
        GeneralAwareness = 3,
    }
}
