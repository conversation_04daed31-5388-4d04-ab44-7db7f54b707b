﻿using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface IGCPServices
    {
        Task<string> GetSignedUrlAsync(string fileNameToRead, int timeOutInMinutes = 10080);
        Task<string> UploadFileAsync(IFormFile fileToUpload, string fileNameToSave);
        Task<string> UploadFileFromMobileAsync(IFormFile fileToUpload, string fileNameToSave, string fileContentType);
        Task<bool> DeleteFileAsync(string fileNameToDelete);
        Task<string> DownloadFile(string fileNameToDownload);
    }
}
