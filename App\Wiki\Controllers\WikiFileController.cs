using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Controllers;
using Jobid.App.Wiki.ViewModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Wiki.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WikiFileController : BaseController
    {
        private readonly IUnitofwork _services;
        private ILogger _logger = Log.ForContext<IdentityController>();

        public WikiFileController(IUnitofwork services)
        {
            _services = services;
        }

        #region Upload File
        /// <summary>
        /// Upload a single file to the wiki with a required batch number
        /// </summary>
        /// <param name="model">The file to upload with batch number and file type</param>
        /// <returns>Response containing the uploaded file details</returns>
        [HttpPost("UploadFile")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UploadFile([FromForm] WikiUploadRequest model)
        {
            if (model.File == null)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "No file was uploaded",
                    Data = null
                });
            }

            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Batch number is required for file uploads",
                    Data = null
                });
            }

            var userId = CurrentUserId.Value;
            var result = await _services.WikiFile.UploadFileAsync(model, userId);
            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion
        
        #region Upload Files (Deprecated)
        /// <summary>
        /// [DEPRECATED] Upload multiple files to the wiki. Use UploadFile endpoint instead.
        /// This endpoint is maintained for backward compatibility only.
        /// </summary>
        /// <param name="model">The files to upload with batch ID and file type</param>
        /// <returns>Response containing the uploaded files details grouped by batch ID</returns>
        [HttpPost("UploadFiles")]
        [Obsolete("This endpoint is deprecated. Use UploadFile endpoint instead.")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UploadFiles([FromForm] WikiUploadRequest model)
        {
            // Redirect to the new endpoint with a warning message
            _logger.Warning("Deprecated UploadFiles endpoint was called. This endpoint will be removed in a future version.");
            
            return BadRequest(new GenericResponse
            {
                ResponseCode = "400",
                ResponseMessage = "This endpoint is deprecated. Please use the UploadFile endpoint with a single file and required batch number.",
                Data = null
            });
        }
        #endregion
        
        #region Add Text Content
        /// <summary>
        /// Add text content to the wiki without file upload
        /// </summary>
        /// <param name="model">The model containing text content, batch number, and optional department IDs</param>
        /// <returns>Response containing the added text content details</returns>
        [HttpPost("AddTextContent")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AddTextContent([FromBody] WikiTextContentRequest model)
        {
            if (string.IsNullOrWhiteSpace(model.TextContent))
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Text content cannot be empty",
                    Data = null
                });
            }
            
            if (string.IsNullOrWhiteSpace(model.BatchId))
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Batch number is required",
                    Data = null
                });
            }

            var userId = CurrentUserId.Value;
            var result = await _services.WikiFile.AddTextContentAsync(model, userId);
            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        //#region Upload File (Legacy)
        ///// <summary>
        ///// Upload a single file to the wiki (Legacy endpoint for backward compatibility)
        ///// </summary>
        ///// <param name="model">The file to upload</param>
        ///// <returns>Response containing the uploaded file details</returns>
        //[HttpPost("Upload")]
        //[ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        //[ProducesResponseType(typeof(GenericResponse), StatusCodes.Status400BadRequest)]
        //[ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        //public async Task<IActionResult> UploadFile([FromForm] WikiUploadRequest model)
        //{
        //    if (model.Files == null || !model.Files.Any())
        //    {
        //        return BadRequest(new GenericResponse
        //        {
        //            ResponseCode = "400",
        //            ResponseMessage = "No file was uploaded",
        //            Data = null
        //        });
        //    }

        //    var userId = CurrentUserId.Value;
        //    var result = await _services.WikiFile.UploadFilesAsync(model, userId);
        //    return Ok(await ConvertDateTimeToLocalDateTime(result));
        //}
        //#endregion

        #region Update File
        /// <summary>
        /// Update file details
        /// </summary>
        /// <param name="model">The file update data</param>
        /// <returns>Response containing the updated file details</returns>
        [HttpPut("UpdateDetails")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateFileDetails([FromBody] WikiFileUpdateDto model)
        {
            if (model == null || model.FileId == Guid.Empty)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid request data",
                    Data = null
                });
            }

            var result = await _services.WikiFile.UpdateFileDetailsAsync(model);
            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        #region Update File Department Access
        /// <summary>
        /// Update department access for a file
        /// </summary>
        /// <param name="model">The access update data</param>
        /// <returns>Response containing the updated file details</returns>
        [HttpPut("UpdateDepartmentAccess")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateFileDepartmentAccess([FromBody] WikiFileDepartmentAccessUpdateDto model)
        {
            var result = await _services.WikiFile.UpdateFileDepartmentAccessAsync(model);

            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        #region Delete File
        /// <summary>
        /// Delete a file (soft delete)
        /// </summary>
        /// <param name="fileId">ID of the file to delete</param>
        /// <returns>Response indicating success or failure</returns>
        [HttpDelete("Delete/{fileId}")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteFile(Guid fileId)
        {
            var result = await _services.WikiFile.DeleteFileAsync(fileId);
            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return Ok(result);
        }
        #endregion

        #region Get File by ID
        /// <summary>
        /// Get a file by its ID
        /// </summary>
        /// <param name="fileId">ID of the file to retrieve</param>
        /// <returns>Response containing the file details</returns>
        [HttpGet("GetById/{fileId}")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFileById(Guid fileId)
        {
            var result = await _services.WikiFile.GetFileByIdAsync(fileId);
            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        #region Get Files
        /// <summary>
        /// Get all files with optional filtering, pagination, and batch grouping
        /// </summary>
        /// <param name="filter">Filter parameters including options for grouping by batch ID and filtering by file type</param>
        /// <returns>Response containing the list of files and pagination metadata, optionally grouped by batch ID</returns>
        [HttpPost("GetFiles")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFiles([FromBody] WikiFileFilterDto filter)
        {
            if (filter == null)
            {
                filter = new WikiFileFilterDto();
            }

            var result = await _services.WikiFile.GetFilesAsync(filter);
            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        #region Get User Files
        /// <summary>
        /// Get files user has access to with optional filtering, pagination, and batch grouping
        /// </summary>
        /// <param name="model">Filter parameters including options for grouping by batch ID and filtering by file type</param>
        /// <returns>Response containing the list of accessible files and pagination metadata, optionally grouped by batch ID</returns>
        [HttpPost("GetUserFiles")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetUserFiles([FromBody] WikiFileFilterDto model)
        {
            var userId = CurrentUserId.Value;
            var result = await _services.WikiFile.GetUserAccessibleFilesAsync(userId, model);

            if (result.ResponseCode == "403")
            {
                return StatusCode(403, result);
            }

            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        #region Get All Files Uploaded To All Companies
        /// <summary>
        /// Get all files uploaded to all companies in the system
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpGet("GetAllFilesUploadedToAllCompanies")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllFilesUploadedToAllCompanies()
        {
            var userId = CurrentUserId.Value;
            var result = await _services.WikiFile.GetAllFilesInTheSystemAsync();

            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }
        #endregion

        #region Get File Presigned URL
        /// <summary>
        /// Get a presigned URL for a file
        /// </summary>
        /// <param name="fileId">ID of the file</param>
        /// <returns>Response containing the presigned URL</returns>
        [HttpGet("GetPresignedUrl/{fileId}")]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(GenericResponse), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetFilePresignedUrl(Guid fileId)
        {
            var userId = CurrentUserId.Value;
            var result = await _services.WikiFile.GetFilePresignedUrlAsync(fileId, userId);

            if (result.ResponseCode == "404")
            {
                return NotFound(result);
            }

            if (result.ResponseCode == "403")
            {
                return StatusCode(403, result);
            }

            return Ok(result);
        }
        #endregion
    }
}
