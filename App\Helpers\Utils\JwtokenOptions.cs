﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.Text;

namespace Jobid.App.Helpers.Utils._Helper
{
    public static class JwtokenOptions
    {
        public const string Issuer = "http://localhost";
        public const string Audience = "http://localhost";
        public static string Key = Environment.GetEnvironmentVariable("JOBPRO_JWT_SHARED_SECRET") ?? GlobalVariables.JwtKey;
        public const int JwtExpireDays = 30;

        public static SecurityKey GetSecurityKey() =>
            new SymmetricSecurityKey(Encoding.ASCII.GetBytes(Key));
    }
}


