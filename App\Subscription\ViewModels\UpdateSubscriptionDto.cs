﻿using Jobid.App.Helpers.Enums;
using System.Text.Json.Serialization;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.ViewModels
{
    public class UpdateSubscriptionDto
    {
        public string PlanId { get; set; }
        public Currency Currency { get; set; } = Currency.EUR;
        public bool ApplyAtTheEndOfCurrentSub { get; set; }
        public Applications Application { get; set; }
        public SubscriptionInterval Interval { get; set; } = SubscriptionInterval.Monthly;
        public int NumberOfUsersToSubFor { get; set; }
        public string TenantId { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }
        [JsonIgnore]
        public string Subdomain { get; set; }
    }
}
