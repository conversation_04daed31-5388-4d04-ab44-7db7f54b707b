﻿using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Subscription.Enums
{
    public enum AIAgents
    {
        [Display(Name = "Britney")]
        Britney = 1,

        [<PERSON><PERSON><PERSON>(Name = "<PERSON><PERSON>")]
        <PERSON><PERSON>,

        [<PERSON><PERSON><PERSON>(Name = "<PERSON>")]
        <PERSON>,

        [<PERSON><PERSON><PERSON>(Name = "<PERSON><PERSON>")]
        <PERSON><PERSON>,

        [<PERSON><PERSON><PERSON>(Name = "<PERSON>")]
        <PERSON>,

        [<PERSON><PERSON><PERSON>(Name = "<PERSON>")]
        <PERSON>
    }
}
