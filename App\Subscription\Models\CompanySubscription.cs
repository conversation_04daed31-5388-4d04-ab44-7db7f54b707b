﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Subscription.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Models
{
    public class CompanySubscription
    {
        [Key]
        public Guid Id { get; set; } = new Guid();

        [ForeignKey("TenantId")]
        public Guid TenantId { get; set; }

        [ForeignKey("SubscriptionId")]
        public Guid? SubscriptionId { get; set; }
        public Applications? Application { get; set; }
        public AIAgents? AIAgent { get; set; }
        public SubscriptionStatus Status { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public Tenant.Model.Tenant Tenant { get; set; }
        public Subscription Subscription { get; set; }
    }
}
