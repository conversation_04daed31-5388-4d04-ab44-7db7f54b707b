﻿using static Jobid.App.RabbitMQ.Records;
using System.Threading.Tasks;
using RabbitMQ.Client;
using System;
using Microsoft.Extensions.Configuration;
using Serilog;
using System.Collections.Generic;
using ILogger = Serilog.ILogger;
using System.Text;
using System.Text.Json;
using RabbitMQ.Client.Exceptions;
using WatchDog.src.Models;
using WatchDog;

namespace Jobid.App.RabbitMQ
{
    public class PublisherService : IPublisherService
    {
        #region Properties and Constructors
        private readonly ILogger _logger = Log.ForContext<PublisherService>();
        private readonly IConnection? _connection;
        private readonly IModel _channel;

        public PublisherService(IConfiguration config, IRabbitMQConnectionService rabbitMQConnectionService)
        {
            // Get rabbitmq connection string from the jobpro.configuration package
            if (_channel is null)
            {
                _channel = rabbitMQConnectionService._channel;
            }

            if (_connection is null)
            {
                _connection = rabbitMQConnectionService._connection;
            }
        }
        #endregion

        #region Generic publish
        /// <summary>
        /// Generic publish method
        /// </summary>
        /// <param name="record"></param>
        /// <returns></returns>
        public async Task<bool> GenericPublish(PublishModel record)
        {
            var exchangeName = record.ExchangeName;
            if (_channel is null)
            {
                _logger.Error("RabbitMQ channel is null");
                return false;
            }
            
            try
            {
                _channel.ExchangeDeclare(exchange: exchangeName, type: record.exchangeType);
            }
            catch (OperationInterruptedException ex)
            {
                _logger.Error("RabbitMQ operation interrupted exception: " + ex.Message);
                WatchLogger.LogError("RabbitMQ operation interrupted exception: " + ex.Message);
                return false;
            }

            var message = JsonSerializer.Serialize(record.payload);

            if (_connection is not null)
            {
                if (_connection.IsOpen)
                {
                    var res = await SendMessage(message, exchangeName, record.RoutingKey);
                    return res;
                }
                else
                {
                    _connection?.Close();
                    _connection?.Dispose();
                }
            }

            return false;
        }
        #endregion

        #region private
        private async Task<bool> SendMessage(string message, string exchangeName, string routingKey = "")
        {
            var body = Encoding.UTF8.GetBytes(message);

            _channel.BasicPublish(exchange: exchangeName, routingKey: routingKey, basicProperties: null, body: body);
            return await System.Threading.Tasks.Task.FromResult(true);
        }
        #endregion

        #region Get Connection
        //public IConnection? GetRabbitMQConnection(IConfiguration config)
        //{
        //    int num = 0;
        //    string url = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_URL") ?? config.GetSection("RabbitMQConfiguration").GetSection("Host").Value;
        //    string port = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PORT") ?? config.GetSection("RabbitMQConfiguration").GetSection("Port").Value;
        //    string username = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_USERNAME") ?? config.GetSection("RabbitMQConfiguration").GetSection("Username").Value;
        //    string password = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PASSWORD") ?? config.GetSection("RabbitMQConfiguration").GetSection("Password").Value;

        //    if (!string.IsNullOrEmpty(port))
        //    {
        //        num = Convert.ToInt32(port);
        //    }

        //    ConnectionFactory connectionFactory = new ConnectionFactory
        //    {
        //        Uri = new Uri(url),
        //    };
        //    try
        //    {
        //        return connectionFactory.CreateConnection();
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.WriteLine("RabbitMQ connection failed ------ " + ex.Message);
        //        _logger.Error("RabbitMQ connection failed >- " + ex.Message);
        //        return null;
        //    }
        //}
        //#endregion

        //#region Dispose
        //public void Dispose()
        //{
        //    _connection?.Close();
        //    _connection?.Dispose();
        //}
        #endregion
    }
}
