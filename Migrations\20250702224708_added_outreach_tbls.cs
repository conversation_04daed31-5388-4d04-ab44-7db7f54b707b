﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added_outreach_tbls : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added_outreach_tbls(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AttachmentAwsKey",
                schema: _Schema,
                table: "Groups",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AttachmentFileName",
                schema: _Schema,
                table: "Groups",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "AttachmentFileSize",
                schema: _Schema,
                table: "Groups",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AttachmentFileType",
                schema: _Schema,
                table: "Groups",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Campaigns",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    GroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    PhoneNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ExpectedOutcome = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true),
                    UpdatedBy = table.Column<string>(type: "character varying(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Campaigns", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Campaigns_Groups_GroupId",
                        column: x => x.GroupId,
                        principalSchema: _Schema,
                        principalTable: "Groups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Campaigns_GroupId",
                schema: _Schema,
                table: "Campaigns",
                column: "GroupId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Campaigns",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "AttachmentAwsKey",
                schema: _Schema,
                table: "Groups");

            migrationBuilder.DropColumn(
                name: "AttachmentFileName",
                schema: _Schema,
                table: "Groups");

            migrationBuilder.DropColumn(
                name: "AttachmentFileSize",
                schema: _Schema,
                table: "Groups");

            migrationBuilder.DropColumn(
                name: "AttachmentFileType",
                schema: _Schema,
                table: "Groups");
        }
    }
}
