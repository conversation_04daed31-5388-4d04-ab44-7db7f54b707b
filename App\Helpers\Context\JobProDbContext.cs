#region Using Statements
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Tenant.SchemaTenant;
using Jobid.App.Helpers.Models;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.Calender.Models;
using Jobid.App.ActivityLog.Model;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;
using Jobid.App.JobProject.Models;
using Jobid.App.Subscription.Models;
using Jobid.App.Wiki.Models;
using Jobid.App.Notification.Models;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers.Utils;
using static Jobid.App.Helpers.Utils.Utility;
using Jobid.App.AdminConsole.Models.Wallet;
using Jobid.App.AdminConsole.Models.Phone;
using Jobid.App.AdminConsole.Models.Calls;
using Jobid.App.AdminConsole.Models.AI;
#endregion

namespace Jobid.App.Helpers.Context
{
    public class JobProDbContext : IdentityDbContext<User>, IDbContextSchema
    {
        #region Properties And Constructors
        public JobProDbContext(DbContextOptions<JobProDbContext> options) : base(options)
        {
            _connectionString = GlobalVariables.ConnectionString;
        }

        public JobProDbContext(DbContextOptions<JobProDbContext> options,
                       IDbContextSchema schema = null) : base(options)
        {
            Schema = schema?.Schema;
            _connectionString = GlobalVariables.ConnectionString;
        }

        public JobProDbContext(string connectionString, IDbContextSchema schema = null)
        {
            Schema = schema?.Schema;
            _connectionString = connectionString;
        }
        public JobProDbContext(IDbContextSchema schema = null)
        {
            Schema = schema?.Schema;
            _connectionString = GlobalVariables.ConnectionString;
        }        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (Environment.GetEnvironmentVariable("IsTesting") == "true")
            {
                // services.AddDbContext<JobProDbContext>(options => options.UseInMemoryDatabase("InMemoryDb"));
                optionsBuilder.UseInMemoryDatabase("InMemoryDb").ReplaceService<IMigrationsAssembly, DbSchemaAwareMigrationAssembly>().ReplaceService<IModelCacheKeyFactory, DbSchemaAwareModelCacheKeyFactory>();
            }
            else
            {
                optionsBuilder.UseNpgsql(_connectionString, sqlOptions =>
                {
                    // Reduce command timeout from 3300 seconds (55 minutes) to a more reasonable 300 seconds (5 minutes)
                    sqlOptions.CommandTimeout(300);
                    sqlOptions.MigrationsHistoryTable("__EFMigrationsHistory", Schema);
                    // Enable connection resiliency with automatic retries
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);
                })
                .ReplaceService<IModelCacheKeyFactory, DbSchemaAwareModelCacheKeyFactory>()
                .ReplaceService<IMigrationsAssembly, DbSchemaAwareMigrationAssembly>();
            }

            base.OnConfiguring(optionsBuilder);
        }

        public string Schema { get; set; } = Constants.PUBLIC_SCHEMA;
        private readonly string _connectionString;
        #endregion

        #region DbSets
        public virtual DbSet<WalletTranToCompanyMapping> WalletTranToCompanyMappings { get; set; }
        public virtual DbSet<CompanyFilesSummary> CompanyFilesSummaries { get; set; }
        public virtual DbSet<CompanySummarizedInfo> CompanySummarizedInfos { get; set; }
        public virtual DbSet<PhoneNoToCompanyMapping> PhoneNoToCompanyMappings { get; set; }
        public virtual DbSet<CallTranscription> CallTranscriptions { get; set; }
        public virtual DbSet<ActivitySettings> ActivitySettings { get; set; }
        public virtual DbSet<AccountDeletionRequest> AccountDeletionRequests { get; set; }
        public virtual DbSet<ProposedDateDetail> ProposedDateDetails { get; set; }
        public virtual DbSet<TwoFactorSetting> TwoFactorSettings { get; set; }
        public virtual DbSet<PasswordPolicy> PasswordPolicies { get; set; }
        public virtual DbSet<Localization> Localizations { get; set; }
        public virtual DbSet<CompanyDeletionRequest> CompanyDeletionRequests { get; set; }
        public virtual DbSet<UserSkill> UserSkills { get; set; }
        public virtual DbSet<BookedMeetingMember> BookedMeetingMembers { get; set; }
        public DbSet<DefaultCompany> DefaultCompanies { get; set; }
        public virtual DbSet<MeetingNote> MeetingNotes { get; set; }
        public virtual DbSet<AdditionalLiecense> AdditionalLiecenses { get; set; }
        public virtual DbSet<EnterprizeSubscriptionPayment> EnterprizeSubscriptionPayments { get; set; }
        public virtual DbSet<AISubscriptionDetail> AISubscriptionDetails { get; set; }

        // Telephony and Wallet Models
        public virtual DbSet<Jobid.App.AdminConsole.Models.Phone.PhoneNumber> PhoneNumbers { get; set; }
        public virtual DbSet<PhoneNumberCapability> PhoneNumberCapabilities { get; set; }
        public virtual DbSet<PhoneNumberAssignment> PhoneNumberAssignments { get; set; }        
        public virtual DbSet<CallRecord> CallRecords { get; set; }
        public virtual DbSet<CompanyWallet> CompanyWallets { get; set; }
        public virtual DbSet<WalletTransaction> WalletTransactions { get; set; }
        public virtual DbSet<PhoneNumberMaintenanceCharge> PhoneNumberMaintenanceCharges { get; set; }
        
        // Telephony Models (Twilio only)
        public virtual DbSet<UserContact> UserContacts { get; set; }
        public virtual DbSet<Group> Groups { get; set; }
        public virtual DbSet<Jobid.App.AdminConsole.Models.GroupContact> GroupContacts { get; set; }
        public virtual DbSet<Campaign> Campaigns { get; set; }
        public virtual DbSet<AIAgent> AIAgents { get; set; }
        public virtual DbSet<SecDomain> SecDomains { get; set; }
        public virtual DbSet<BrowserInfo> BrowserInfos { get; set; }
        public virtual DbSet<WikiAccess> WikiAccesses { get; set; }
        public virtual DbSet<WikiContent> WikiFiles { get; set; }
        public virtual DbSet<WikiFileDepartmentAccess> WikiFileDepartmentAccess { get; set; }
        public virtual DbSet<ProjectMgmt_Project> ProjectMgmt_Projects { get; set; }
        public virtual DbSet<TenantClient> TenantClients { get; set; }
        public virtual DbSet<ProjectMgmt_Todo> ProjectMgmt_Todo { get; set; }
        public virtual DbSet<NationalLaguage> NationalLaguages { get; set; }
        public virtual DbSet<ClientRole> ClientRoles { get; set; }
        public virtual DbSet<RoleModule> RoleModules { get; set; }
        public virtual DbSet<Permission> Permissions { get; set; }
        public virtual DbSet<ClientRoleRoleModule> ClientRoleRoleModules { get; set; }
        public DbSet<ProjectFile> ProjectFile { get; set; }
        public DbSet<ProjectMgmt_ProjectUser> projectMgmt_ProjectUsers { get; set; }
        public DbSet<TodoTimeSequence> TodoTimeSequence { get; set; }
        public DbSet<ProjectMgmt_TodoUser> projectMgmt_TodoUsers { get; set; }
        public virtual DbSet<Tenant.Model.Tenant> Tenants { get; set; }
        public virtual DbSet<Tenant.Model.DeletedTenant> DeletedTenants { get; set; }
        public virtual DbSet<UserCompanies> UserCompanies { get; set; }
        public virtual DbSet<SuspendedEmployee> SuspendedEmployees { get; set; }
        public virtual DbSet<UserAndRoleId> UserAndRoleIds { get; set; }
        public virtual DbSet<EmployeeRoles> EmployeeRoles { get; set; }
        public virtual DbSet<EmployeePermission> EmployeePermissions { get; set; }
        public virtual DbSet<EmployeeRolesPermission> EmployeeRolesPermissions { get; set; }
        public virtual DbSet<CompanyUserInvite> CompanyUserInvites { get; set; }
        public virtual DbSet<UserProfile> UserProfiles { get; set; }
        public DbSet<ProjectTag> ProjectTag { get; set; }
        public DbSet<OTP> OTP { get; set; }
        public DbSet<TimeSheet> TimeSheet { get; set; }
        public DbSet<TimeSheetMemberId> TimeSheetMemberIds { get; set; }
        public DbSet<ProjectTrigger> ProjectTriggers { get; set; }
        public DbSet<SprintProject> SprintProjects { get; set; }
        public DbSet<ProjectMetricsView> ProjectMetricsViews { get; set; }
        public DbSet<CalenderMeeting> CalenderMeetings { get; set; }
        public DbSet<CalenderMeetingRecording> CalenderMeetingRecordings { get; set; }
        public DbSet<UserIdCalenderId> UserIdMeetingIds { get; set; }
        public virtual DbSet<TagId> TagId { get; set; }
        public virtual DbSet<ProjectSprintMemberId> ProjectSprintMemberIds { get; set; }
        public DbSet<PersonalSchedule> PersonalSchedule { get; set; }
        public DbSet<JobProjectRoles> JobProjectRoles { get; set; }
        public DbSet<JobProjectPermission> JobProjectPermission { get; set; }
        public DbSet<JobProjectUserRoles> JobProjectUserRoles { get; set; }
        public DbSet<JobProjectRolesPermissions> JobProjectRolesPermissions { get; set; }
        public DbSet<JobProjectUserPermissions> JobProjectUserPermissions { get; set; }
        public DbSet<CustomFrequency> CustomFrequency { get; set; }
        public DbSet<ExternalMeeting> ExternalMeeting { get; set; }
        public DbSet<ExternalMeetingQuestion> ExternalMeetingQuestion { get; set; }
        public DbSet<BookedExternalMeeting> BookedExternalMeeting { get; set; }
        public virtual DbSet<Activity> Activities { get; set; }
        public DbSet<ActivityView> ActivityViews { get; set; }
        public DbSet<TodoOrder> TodoOrder { get; set; }
        public DbSet<TodoStatus> TodoStatus { get; set; }
        public DbSet<WaitingEmailList> WaitingEmailLists { get; set; }
        public DbSet<AppPermissions> AppPermissions { get; set; }
        public DbSet<Team> Teams { get; set; }
        public DbSet<TeamMembers> TeamMembers { get; set; }
        public DbSet<TodoComments> TodoComments { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<TriggerSequence> TriggerSequence { get; set; }
        public DbSet<ActivityRequestedPermisssions> ActivityRequestedPermisssions { get; set; }
        public DbSet<CompanySettings> CompanySettings { get; set; }
        public DbSet<JobProjectSettings> JobProjectSettings { get; set; }
        public DbSet<Subscription.Models.Subscription> Subscriptions { get; set; }
        public DbSet<Feature> Features { get; set; }
        public DbSet<PricingAndFeature> PricingAndFeatures { get; set; }
        public DbSet<PricingPlan> PricingPlans { get; set; }
        public DbSet<ClientCards> ClientCards { get; set; }
        public DbSet<PackagePricing> PackagePricing { get; set; }
        public DbSet<SubscriptionHistory> SubscriptionHistory { get; set; }
        public DbSet<CompanySubscription> CompanySubscriptions { get; set; }
        public DbSet<ShareActivityParams> ShareActivityParams { get; set; }
        public DbSet<UserNotification> UserNotifications { get; set; }
        public DbSet<Notification.Models.Notification> Notifications { get; set; }
        public DbSet<Subscription.Models.BillingAddress> BillingAddresses { get; set; }
        public DbSet<EnterpriseSubscription> EnterpriseSubscriptions { get; set; }
        public DbSet<FeedbackReviewsAndRatings> FeedbackReviewsAndRatings { get; set; }
        public DbSet<SubsequentMeeting> SubsequentMeetings { get; set; }
        public DbSet<StripeSubscriptionDetail> StripeSubscriptionDetails { get; set; }
        public DbSet<TodoCustomFrequency> TodoCustomFrequency { get; set; }
        public DbSet<UserRefreshToken> RefreshTokens { get; set; }
        public DbSet<ExternalMeetingMembers> ExternalMeetingMembers { get; set; }
        public DbSet<RoundRobinHostingOrder> RoundRobinHostingOrders { get; set; }
        public DbSet<LoginLog> LoginLogs { get; set; }
        public DbSet<BackGroundJobId> BackGroundJobIds { get; set; }
        public DbSet<TenantProjectView> TenantProjectViews { get; set; }
        public DbSet<ExternalMeetingTimeManagement> ExternalMeetingTimeManagements { get; set; }
        public DbSet<ProductUpdate> ProductUpdates { get; set; }
        public DbSet<CustomQuestion> CustomQuestions { get; set; }
        public DbSet<CustomQuestionAnswer> CustomQuestionAnswers { get; set; }
        public DbSet<MicroService> MicroServices { get; set; }
        public DbSet<UserPerformanceActivityScoreView> PerformanceMetricAnalyses { get; set; }
        public DbSet<LogAttachment> LogAttachments { get; set; }
        public DbSet<CalenderUpload> CalenderUploads { get; set; }
        public DbSet<BlacklistedToken> BlacklistedTokens { get; set; }
        public DbSet<OrganogramCompany> OrganogramCompanies { get; set; }
        public DbSet<Individual> Individuals { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<EmployeePosition> EmployeePositions { get; set; }
        public DbSet<BasicInfo> BasicInfos { get; set; }
        public DbSet<EmergencyInfo> EmergencyInfos { get; set; }
        public DbSet<EmployeeComplaint> EmployeeComplaints { get; set; }
        public DbSet<EndpointCallTracker> EndpointCallTrackers { get; set; }
        public DbSet<MeetingSkillSuggestion> MeetingSkillSuggestions { get; set; }
        public DbSet<BillingInformation> BillingInformations { get; set; }


        #endregion

        #region JobPays Subscription
        public DbSet<CalenderExternalIntegration> CalenderExternalIntegrations { get; set; }
        #endregion

        #region Model Configurations
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema(Schema);
            base.OnModelCreating(modelBuilder);

            // Configure the default mapping for all DateTime properties
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                foreach (var property in entityType.GetProperties())
                {
                    if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                    {
                        property.SetColumnType("timestamp");
                    }
                }
            }

            modelBuilder.Entity<ClientRoleRoleModule>()
                .HasKey(cs => new { cs.ClientRoleId, cs.RoleModuleId });

            modelBuilder.Entity<ClientRoleRoleModule>()
                .HasOne<ClientRole>(sc => sc.ClientRole)
                .WithMany(s => s.ClientRoleRoleModules)
                .HasForeignKey(sc => sc.ClientRoleId);

            modelBuilder.Entity<ClientRoleRoleModule>()
                .HasOne<RoleModule>(sc => sc.RoleModule)
                .WithMany(s => s.ClientRoleRoleModules)
                .HasForeignKey(sc => sc.RoleModuleId);

            modelBuilder.Entity<JobProjectRolesPermissions>()
                .HasNoKey();

            modelBuilder.Entity<JobProjectUserPermissions>()
                .HasNoKey();

            modelBuilder.Entity<JobProjectUserRoles>()
                .HasNoKey();

            modelBuilder.Entity<UserNotification>()
                .HasKey(x => new { x.UserProfileId, x.NotificationId });

            modelBuilder.Entity<EmployeeRoles>()
                .HasMany(dc => dc.EmployeeRolesPermissions)
                .WithOne(dcc => dcc.EmployeeRoles)
                .OnDelete(DeleteBehavior.Cascade);
        }
        #endregion
    }
}
