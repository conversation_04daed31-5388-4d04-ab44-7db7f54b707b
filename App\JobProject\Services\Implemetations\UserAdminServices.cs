﻿using AutoMapper;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.RabbitMQ;
using Microsoft.EntityFrameworkCore;
using RabbitMQ.Client;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using WatchDog;
using static Jobid.App.RabbitMQ.Records;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class UserAdminServices : IUserAdminServices
    {
        private readonly JobProDbContext Db;
        public JobProDbContext Dbo;
        private readonly IMapper _mapper;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IPublisherService _publisherService;
        private ILogger _logger = Log.ForContext<UserAdminServices>();

        public UserAdminServices(JobProDbContext db, JobProDbContext publicSchemaContext, IMapper mapper, IAWSS3Sevices aWSS3Sevices, IPublisherService publisherService)
        {
            Db = db;
            Dbo = publicSchemaContext;
            _mapper = mapper;
            _aWSS3Sevices = aWSS3Sevices;
            _publisherService = publisherService;
        }

        #region Get User Profile
        public async Task<UserProfile> GetUserProfile(string userId)
        {
            var userProfile = await Db.UserProfiles.FirstOrDefaultAsync(u => u.UserId == userId);
            if (userProfile is not null)
            {
                if (userProfile.ProfilePictureUrl is not null)
                {
                    var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(userProfile?.ProfilePictureUrl);
                    userProfile.ProfilePictureUrl = Utility.ConvertSignedUrlToBase64(signedUrl);
                }
                else
                    userProfile.ProfilePictureUrl = null;
            }

            return userProfile;
        }
        #endregion

        #region Update User
        /// <summary>
        /// UpdateUser
        /// </summary>
        /// <param name="updateUserProfileDto"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<UserProfilesForAdminDto> UpdateUserProfile(UpdateUserProfileDto updateUserProfileDto, string subdomain)
        {
            var res = 0;
            var tenant = await Dbo.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);
            var userToUpdate = Db.UserProfiles.FirstOrDefault(x => x.UserId == updateUserProfileDto.UserId)
                ?? throw new RecordNotFoundException("User not found");

            UserProfilesForAdminDto userprofile = new UserProfilesForAdminDto();
            userToUpdate.Address = !string.IsNullOrEmpty(updateUserProfileDto.Address) ? updateUserProfileDto.Address : userToUpdate.Address;
            userToUpdate.PhoneNumber = !string.IsNullOrEmpty(updateUserProfileDto.PhoneNumber) ? updateUserProfileDto.PhoneNumber : userToUpdate.PhoneNumber;
            userToUpdate.FirstName = !string.IsNullOrEmpty(updateUserProfileDto.FirstName) ? updateUserProfileDto.FirstName : userToUpdate.FirstName;
            userToUpdate.LastName = !string.IsNullOrEmpty(updateUserProfileDto.LastName) ? updateUserProfileDto.LastName : userToUpdate.LastName;
            userToUpdate.MiddleName = !string.IsNullOrEmpty(updateUserProfileDto.MiddleName) ? updateUserProfileDto.MiddleName : userToUpdate.MiddleName;
            userToUpdate.ZipCode = !string.IsNullOrEmpty(updateUserProfileDto.ZipCode) ? updateUserProfileDto.ZipCode : userToUpdate.ZipCode;
            userToUpdate.Country = !string.IsNullOrEmpty(updateUserProfileDto.Country) ? updateUserProfileDto.Country : userToUpdate.Country;
            userToUpdate.CountryCode = !string.IsNullOrEmpty(updateUserProfileDto.CountryCode) ? updateUserProfileDto.CountryCode : userToUpdate.CountryCode;
            userToUpdate.State = !string.IsNullOrEmpty(updateUserProfileDto.State) ? updateUserProfileDto.State : userToUpdate.State;
            userToUpdate.Bio = !string.IsNullOrEmpty(updateUserProfileDto.Bio) ? updateUserProfileDto.Bio : userToUpdate.Bio;
            userToUpdate.TimeZone = !string.IsNullOrEmpty(updateUserProfileDto.TimeZone) ? updateUserProfileDto.TimeZone : userToUpdate.TimeZone;
            userToUpdate.Gender = !string.IsNullOrEmpty(updateUserProfileDto.Gender) ? updateUserProfileDto.Gender : userToUpdate.Gender;

            userToUpdate.DOB = updateUserProfileDto.DateOfBirth != null ? updateUserProfileDto.DateOfBirth.Value.ToShortDateString() : userToUpdate.DOB;

            userToUpdate.Profession = !string.IsNullOrEmpty(updateUserProfileDto.Profession) ? updateUserProfileDto.Profession : userToUpdate.Profession;
            userToUpdate.Designation = !string.IsNullOrEmpty(updateUserProfileDto.Designation) ? updateUserProfileDto.Designation : userToUpdate.Designation;

            userToUpdate.LastUpdate = DateTime.UtcNow;

            if (!string.IsNullOrEmpty(updateUserProfileDto.Email))
            {
                // Check if email is valid and a company email
                if (!updateUserProfileDto.Email.IsPersonalEmail())
                {
                    var emailDomain = updateUserProfileDto.Email.Split('@')[1];
                    if (emailDomain.ToLower() == tenant.VerifiedEmailDomain.ToLower())
                        userToUpdate.Email = updateUserProfileDto.Email;
                }
            }

            // Update the user profile
            userprofile.UserProfile = userToUpdate;

            if (!string.IsNullOrEmpty(updateUserProfileDto.TenantBusinessRegistrationNumber))
                userprofile.TenantBusinessRegistrationNumber = updateUserProfileDto.TenantBusinessRegistrationNumber;

            if (!string.IsNullOrEmpty(updateUserProfileDto.TenantWorkSpace))
                userprofile.TenantWorkSpace = updateUserProfileDto.TenantWorkSpace;

            if (!string.IsNullOrEmpty(updateUserProfileDto.TenantCompanyAddress))
                userprofile.TenantCompanyAddress = updateUserProfileDto.TenantCompanyAddress;


            // Upload profile pic
            if (updateUserProfileDto.ProfilePicture is not null)
            {
                var uploadedFile = updateUserProfileDto.ProfilePicture;
                var guid = Guid.NewGuid();
                var fileTrimmed = uploadedFile.FileName.Replace(" ", "");
                var fileName = guid.ToString()
                                        .Replace('-', '0')
                                        .Replace('_', '0')
                                        .ToUpper() + "-" + fileTrimmed;

                var imageUrl = await _aWSS3Sevices.UploadFileAsync(updateUserProfileDto.ProfilePicture, fileName);

                userToUpdate.ProfilePictureUrl = fileName;
            }

            Db.UserProfiles.Update(userToUpdate);
            res = await Db.SaveChangesAsync();
            if (tenant != null)
            {
                tenant.WorkSpace = updateUserProfileDto.TenantWorkSpace;
                tenant.RegNumber = updateUserProfileDto.TenantBusinessRegistrationNumber;
                tenant.CompanyAddress = updateUserProfileDto.TenantCompanyAddress;
                tenant.LastUpdate = DateTime.UtcNow;
                Dbo.Tenants.Update(tenant);
                var res1 = await Dbo.SaveChangesAsync();
            }

            // Publish an 'EmployeeCreatedEvent' to rabbimq
            var eventModel = new PublishModel
            (
                RabbitMQConstants.UserUpdatedEvent,
                "",
                ExchangeType.Fanout,
                userToUpdate
            );

            var eventRes = await _publisherService.GenericPublish(eventModel);
            if (!eventRes)
            {
                _logger.Error("User updated but event could not be published");
                WatchLogger.Log("User updated but event could not be published");
            }

            return res > 0 ? userprofile : null;
        }

        #endregion

        #region Get Permissions for all the roles
        /// <summary>
        /// Get Permissions for all the roles
        /// </summary>
        /// <returns></returns>
        public async Task<List<JobProjectRoles>> GetPermissionsForAllRoles()
        {
            var roles = await Db.JobProjectRoles.Include(x => x.Permissions).ToListAsync();
            return roles;
        }
        #endregion

        #region Get Job Project Permissions
        /// <summary>
        /// Get Job Project Permissions
        /// </summary>
        /// <returns></returns>
        public IEnumerable<JobProjectPermission> GetJobProPermissions()
        {
            return Db.JobProjectPermission;
        }
        #endregion

        #region Add Permission to role
        /// <summary>
        /// Add permission to role
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="permissionId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<bool> AddPermissionToRole(string roleId, string permissionId, string subdomain)
        {
            var table = subdomain + ".JobProjectRolesPermissions";

            // Check if the permission already exist for the role
            var permission = await Db.JobProjectRolesPermissions.FromSqlRaw($"SELECT * FROM {table} WHERE permissionsId = @permissionId AND rolesId = " +
                               $"@roleId",
                            new SqlParameter("permissionId", permissionId),
                            new SqlParameter("roleId", roleId)).FirstOrDefaultAsync();
            if (permission != null) { throw new RecordAlreadyExistException("Permission already assigned to the role"); }

            await Db.Database.ExecuteSqlRawAsync($"INSERT INTO {table} VALUES(@permissionId, @roleId)",
                new SqlParameter("permissionId", permissionId),
                new SqlParameter("roleId", roleId)
            );

            return true;
        }
        #endregion

        #region Remove Permission from role
        /// <summary>
        /// Remove Permission from role
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="permissionId"></param>
        /// <returns></returns>
        public async Task<bool> RemovePermissionFromRole(string roleId, string permissionId, string subdomain)
        {
            var table = subdomain + ".JobProjectRolesPermissions";

            // Check if the permission already exist for the role
            var permission = await Db.JobProjectRolesPermissions.FromSqlRaw($"SELECT * FROM {table} WHERE permissionsId = @permissionId AND rolesId = " +
                               $"@roleId",
                            new SqlParameter("permissionId", permissionId),
                            new SqlParameter("roleId", roleId)).FirstOrDefaultAsync();
            if (permission == null) { throw new RecordNotFoundException("Permission not found"); }

            await Db.Database.ExecuteSqlRawAsync($"DELETE FROM {table} WHERE permissionsId = @permissionId AND rolesId = " +
                $"@roleId",
                new SqlParameter("permissionId", permissionId),
                new SqlParameter("roleId", roleId)
            );

            return true;
        }
        #endregion

        #region Add permission to a user
        /// <summary>
        /// Add permission to a user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="permissionId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="RecordAlreadyExistException"></exception>
        public async Task<bool> AddPermissionToUser(string userId, string permissionId, string subdomain)
        {
            // Check if the user exist
            var user = Db.UserProfiles.FirstOrDefault(x => x.UserId == userId);
            if (user == null) { throw new RecordNotFoundException("User not found"); }

            // Check if the permission exist
            var permission = Db.JobProjectPermission.FirstOrDefault(x => x.Id == permissionId);
            if (permission == null) { throw new RecordNotFoundException("Permission not found"); }

            // Check if the permission is already assigned to the user
            var table = subdomain + ".JobProjectUserPermissions";
            var userPermission = await Db.JobProjectUserPermissions.FromSqlRaw($"SELECT * FROM {table} WHERE UsersId = @userId AND PermissionsId = @permissionId",
                    new SqlParameter("userId", userId),
                    new SqlParameter("permissionId", permissionId)).FirstOrDefaultAsync();
            if (userPermission != null) { throw new RecordAlreadyExistException("Permission already assigned to the user"); }

            await Db.Database.ExecuteSqlRawAsync($"INSERT INTO {table} VALUES(@permissionId, @userId)",
                               new SqlParameter("permissionId", permissionId),
                               new SqlParameter("userId", userId));
            return true;
        }
        #endregion

        #region Remove permission from a user
        /// <summary>
        /// Remove permission from a user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="permissionId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="RecordAlreadyExistException"></exception>
        public async Task<bool> RemovePermissionFromUser(string userId, string permissionId, string subdomain)
        {
            // Check if the user exist
            var user = Db.UserProfiles.FirstOrDefault(x => x.UserId == userId);
            if (user == null) { throw new RecordNotFoundException("User not found"); }

            var table = subdomain + ".JobProjectUserPermissions";

            // Check if the permission is already assigned to the user
            var userPermission = await Db.JobProjectUserPermissions.FromSqlRaw($"SELECT * FROM {table} WHERE UsersId = @userId AND PermissionsId = @permissionId",
                    new SqlParameter("userId", userId),
                    new SqlParameter("permissionId", permissionId)).FirstOrDefaultAsync();
            if (userPermission == null) { throw new RecordAlreadyExistException("Permission not found"); }

            await Db.Database.ExecuteSqlRawAsync($"DELETE FROM {table} WHERE permissionsId = @permissionId AND usersId = " +
                               $"@userId",
                                new SqlParameter("permissionId", permissionId),
                                new SqlParameter("userId", userId));
            return true;
        }
        #endregion

        #region Get a user's permissions
        /// <summary>
        /// Get a user's permissions
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetUserPermissions(string userId, string subdomain)
        {
            var table = subdomain + ".JobProjectUserPermissions";
            var permissions = await Db.JobProjectUserPermissions.FromSqlRaw($"SELECT * FROM {table} WHERE UsersId = @userId",
                    new SqlParameter("userId", userId)).ToListAsync();

            var userPermissions = new List<JobProjectPermission>();
            foreach (var permission in permissions)
            {
                userPermissions.Add(await Db.JobProjectPermission.FirstOrDefaultAsync(x => x.Id == permission.PermissionsId));
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "User permissions retrieved successfully",
                Data = userPermissions
            };
        }
        #endregion

        #region Delete, Suspend or Unsuspend user
        /// <summary>
        /// Delete, Suspend or Un-suspend user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="isDeleted"></param>
        /// <param name="isSuspended"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> DeleteSuspendOrUnSuspendUser(string userId, bool isDeleted = false, bool isSuspended = false)
        {
            // Check if user exists
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId) ?? throw new RecordNotFoundException("User not found");

            if (isSuspended)
                user.IsSuspended = isSuspended;

            if (isDeleted)
                user.IsDeleted = isDeleted;

            user.LastUpdate = DateTime.UtcNow;
            Db.UserProfiles.Update(user);
            var res = await Db.SaveChangesAsync();

            return res > 0;
        }
        #endregion

        #region Change user's role
        /// <summary>
        /// Change User Role
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UpdateUserRole(string userId, string roleId)
        {
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId) ?? throw new RecordNotFoundException("User not found");
            var role = await Db.JobProjectRoles.FirstOrDefaultAsync(x => x.Id == roleId) ?? throw new RecordNotFoundException("Role not found");

            // Get user role
            var userRoles = await Db.JobProjectUserRoles.FirstOrDefaultAsync(x => x.UsersId == userId);
            if (userRoles == null) { throw new RecordNotFoundException("User role not found"); }
            else
            {
                userRoles.RolesId = roleId;
                Db.JobProjectUserRoles.Update(userRoles);
            }

            // Delete all user permissions associated with the old role and assign new permissions
            var userPermissions = await Db.JobProjectUserPermissions.Where(x => x.UsersId == userId).ToListAsync();
            if (userPermissions.Count > 0)
            {
                Db.JobProjectUserPermissions.RemoveRange(userPermissions);
            }

            // Get all permissions associated with the new role
            var rolePermissions = await Db.JobProjectRolesPermissions.Where(x => x.RolesId == roleId).ToListAsync();
            var userPermissionsList = new List<JobProjectUserPermissions>();
            if (rolePermissions.Count > 0)
            {
                foreach (var permission in rolePermissions)
                {
                    var userPermission = new JobProjectUserPermissions
                    {
                        PermissionsId = permission.PermissionsId,
                        UsersId = userId
                    };

                    userPermissionsList.Add(userPermission);
                }

                await Db.JobProjectUserPermissions.AddRangeAsync(userPermissionsList);
            }

            var res = await Db.SaveChangesAsync();
            return res > 0;
        }
        #endregion

        #region Update JobProject Settings
        public async Task<bool> UpdateJobProjectSettings(JobProjectSettingsDto model)
        {
            var settings = _mapper.Map<JobProjectSettings>(model);
            settings.UpdatedOn = DateTime.UtcNow;

            await Db.JobProjectSettings.AddAsync(settings);
            var res = await Db.SaveChangesAsync();

            return res > 0;
        }
        #endregion
    }
}
