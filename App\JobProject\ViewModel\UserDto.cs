﻿using Jobid.App.Tenant.ViewModel;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class UserDto
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string ProfileUrl { get; set; }
        public string Role { get; set; }
        public string Designation { get; set; }
        public string Currency { get; set; }
        public decimal? AmountPerHour { get; set; }
        public TenantDetailsVM? CompanyDetails { get; set; }
    }
}
