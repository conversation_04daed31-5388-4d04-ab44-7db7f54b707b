﻿using Jobid.App.Helpers.Enums;
using System.Collections.Generic;
using System;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class OrganogramHierarchyDto
    {
        public ParentCompanyDto ParentCompany { get; set; }
        public List<SubsidiaryCompanyDto> Subsidiaries { get; set; } = new List<SubsidiaryCompanyDto>();
    }

    public class ParentCompanyDto
    {
        public Guid Id { get; set; }
        public string CompanyName { get; set; }
        public string Country { get; set; }
        public string FullAddress { get; set; }
        public string EmailAddress { get; set; }
        public string BranchColor { get; set; }
        public Industries Industry { get; set; }
    }

    public class SubsidiaryCompanyDto
    {
        public Guid Id { get; set; }
        public string CompanyName { get; set; }
        public string Country { get; set; }
        public string FullAddress { get; set; }
        public string EmailAddress { get; set; }
        public string BranchColor { get; set; }
        public Industries Industry { get; set; }
        public long BelongsTo { get; set; }
        public List<DepartmentHierarchyDto> Departments { get; set; } = new List<DepartmentHierarchyDto>();
    }

    public class DepartmentHierarchyDto
    {
        public Guid Id { get; set; }
        public string DepartmentName { get; set; }
        public string BranchColor { get; set; }
        public long BelongsTo { get; set; }
        public List<PositionHierarchyDto> Positions { get; set; } = new List<PositionHierarchyDto>();
    }

    public class PositionHierarchyDto
    {
        public Guid Id { get; set; }
        public string PositionName { get; set; }
        public long BelongsTo { get; set; }
        public List<IndividualHierarchyDto> Individuals { get; set; } = new List<IndividualHierarchyDto>();
    }

    public class IndividualHierarchyDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string Title { get; set; }
        public string EmailAddress { get; set; }
        public string BranchColor { get; set; }
        public bool IsHeadOfDepartment { get; set; }
        public long BelongsTo { get; set; }
    }
}