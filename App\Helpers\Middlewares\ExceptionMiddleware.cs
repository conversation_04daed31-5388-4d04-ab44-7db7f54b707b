﻿using Jobid.App.Helpers.Utils;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using WatchDog;
using Jobid.App.Helpers.Services.Implementations;

namespace Jobid.App.Helpers.Middlewares
{
    public class ExceptionMiddleware 
    {
        private readonly RequestDelegate _next;
        private readonly IHostEnvironment _env;
        private ILogger _logger = Log.ForContext<ExceptionMiddleware>();

        /// <summary>
        /// ExceptionMiddleware constructor
        /// </summary>
        /// <param name="next"></param>
        /// <param name="env"></param>
        public ExceptionMiddleware(RequestDelegate next, IHostEnvironment env)
        {
            _next = next;
            _env = env;
        }

        /// <summary>
        /// Method to invoke our exception
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }            catch (Exception ex)
            {
                _logger.Error($"JobPro-Monolithic: {ex.ToString()}", ex.ToString(), ex.TargetSite.DeclaringType.FullName, ex.StackTrace);
                
                // Use WatchDog wrapper with retry logic to handle potential serialization errors
                try
                {
                    WatchDogServiceWrapper.LogErrorWithRetry(ex.ToString(), string.Empty, ex.TargetSite?.DeclaringType?.FullName ?? "Unknown", ex.StackTrace);
                }
                catch (Exception watchDogEx)
                {
                    // If WatchDog fails, log to Serilog as fallback
                    _logger.Error(watchDogEx, "Failed to log exception to WatchDog: {OriginalException}", ex.ToString());
                }

                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                var env = GlobalVariables.Environment;

                var response = env == "PRODUCTION"
                    ? new ApiResponse<string>()
                    {
                        ResponseCode = context.Response.StatusCode.ToString(),
                        ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                        DevResponseMessage = null,
                        StackTrace = null,
                        Data = string.Empty
                    }
                    :
                    new ApiResponse<string>()
                    {
                        ResponseCode = context.Response.StatusCode.ToString(),
                        ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                        DevResponseMessage = ex.ToString(),
                        Data = string.Empty
                    };

                var options = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
                var json = JsonSerializer.Serialize(response, options);
                await context.Response.WriteAsync(json);
            }
        }

        /// <summary>
        /// ApiException class
        /// </summary>
        private class ApiException
        {
            public ApiException(int statusCode, string devResponseMessage, string userResponseMessage, string details = null)
            {
                StatusCode = statusCode;
                DevResponseMessage = devResponseMessage;
                UserResponseMessage = userResponseMessage;
                Details = details;
            }

            public int StatusCode { get; set; }
            public string DevResponseMessage { get; set; }
            public string UserResponseMessage { get; set; }
            public string Details { get; set; }
        }
    }
}
