﻿using System.Collections.Generic;

namespace Jobid.App.Helpers.ViewModel
{
    public class TodoAnalyticsVM
    {
        public double TodoCompletionRateonTime { get; set; } = 0.0;
        public double WeeklyTodoCompletionRateOnTime { get; set; }
        public int TodoCompletedOnTime { get; set; }
        public int WeeklyTotalTodoCompleted { get; set; }
        public int WeeklyTotalTodoCompletedOnTime { get; set; }
        public int WeeklyTotalTodo { get; set; } = 0;
        public int TotalTodo { get; set; } = 0;
        public int TotalCompletedTodo { get; set; } = 0;
        public int CurrentTodosCount { get; set; } = 0;
        public int PendingTasksCount { get; set; } = 0;
        public int PendingTasksPercentage { get; set; } = 0;
        public int StreakScore { get; set; } = 0;
        public int StreakPercentage { get; set; } = 0;
        public int AchievementsScore { get; set; } = 0;
        public int AchievementsPercentage { get; set; } = 0;
        public Dictionary<string, double> WeeklyAnalytics {  get; set; }
    }
}
