﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class updated_phone_assgnedmnts : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updated_phone_assgnedmnts(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PhoneNumberAssignments_AspNetUsers_UserId",
                schema: _Schema,
                table: "PhoneNumberAssignments");

            migrationBuilder.DropIndex(
                name: "IX_PhoneNumberAssignments_UserId",
                schema: _Schema,
                table: "PhoneNumberAssignments");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_PhoneNumberAssignments_UserId",
                schema: _Schema,
                table: "PhoneNumberAssignments",
                column: "UserId");

            migrationBuilder.AddForeignKey(
                name: "FK_PhoneNumberAssignments_AspNetUsers_UserId",
                schema: _Schema,
                table: "PhoneNumberAssignments",
                column: "UserId",
                principalSchema: _Schema,
                principalTable: "AspNetUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
