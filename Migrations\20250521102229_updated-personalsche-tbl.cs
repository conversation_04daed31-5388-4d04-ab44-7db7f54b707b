﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class updatedpersonalschetbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updatedpersonalschetbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExtMeetId",
                schema:_Schema,
                table: "PersonalSchedule");

            migrationBuilder.AddColumn<string>(
                name: "MeetingId",
                schema:_Schema,
                table: "PersonalSchedule",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MeetingId",
                schema:_Schema,
                table: "PersonalSchedule");

            migrationBuilder.AddColumn<Guid>(
                name: "ExtMeetId",
                schema:_Schema,
                table: "PersonalSchedule",
                type: "uuid",
                nullable: true);
        }
    }
}
