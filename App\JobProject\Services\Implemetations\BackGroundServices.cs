﻿using CsvHelper;
using Hangfire;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using RestSharp;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class BackGroundServices : IBackGroundServices
    {
        private readonly ITodoServices _todoService;
        private readonly JobProDbContext _publicContext;
        private readonly IApiCallService _apiCallService;
        private ILogger _logger = Log.ForContext<BackGroundServices>();

        public BackGroundServices(ITodoServices todoServices, IApiCallService apiCallService)
        {
            _todoService = todoServices;
            _apiCallService = apiCallService;
            _publicContext = new JobProDbContext(new DbContextSchema());
            var tableExist = _publicContext.TableExists("CompanySubscriptions");
            if (tableExist)
            {
                GlobalVariables.Subdomains = _publicContext.CompanySubscriptions
                    .Include(x => x.Tenant)
                    .Where(x => x.Application == Applications.Joble && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active)
                    .Select(x => x.Tenant.Subdomain)
                    .ToList() ?? new List<string>();
            }
        }

        #region Update notification IsDisappear status to true
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("notification")]
        public void DisapperaReadNotificationsAfter24Hours(List<string> subdomains)
        {
            if (subdomains == null) return;
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var userNotifications = context.UserNotifications.Where(x => x.IsRead && !x.Disappear).ToList();
                foreach (var userNotification in userNotifications)
                {
                    var timeSpan = DateTime.UtcNow - userNotification.ReadOn;
                    if (timeSpan.TotalHours >= 24)
                    {
                        userNotification.Disappear = true;
                        context.UserNotifications.Update(userNotification);
                    }
                }
                context.SaveChanges();
            }
        }
        #endregion

        #region Delete notifications after 30days
        /// <summary>
        /// Delete after 30days
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("notification")]
        public void DeleteNotificationsAfterSomeDays(List<string> subdomains)
        {
            if (subdomains == null) return;
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var notifications = context.Notifications.Where(x => x.CreatedAt <= DateTime.UtcNow.AddDays(-30)).ToList();
                context.Notifications.RemoveRange(notifications);

                var userNotifications = context.UserNotifications.Where(x => x.CreatedOn <= DateTime.UtcNow.AddDays(-30)).ToList();
                context.UserNotifications.RemoveRange(userNotifications);
                context.SaveChanges();
            }
        }
        #endregion

        #region Update Todo status To OverDue
        /// <summary>
        /// Updates todo status
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("todo")]
        public void UpdateTodoStatusToOverDue(List<string> subdomains)
        {
            if (subdomains == null) return;
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var todos = context.ProjectMgmt_Todo.Where(x => x.TodoStatus != ProjectManagementStatus.OverDue.ToString() && x.TodoStatus != ProjectManagementStatus.Completed.ToString() && x.TodoStatus != ProjectManagementStatus.Review.ToString() && x.EndTime < DateTime.UtcNow).ToList();
                if (todos.Count > 0)
                {
                    foreach (var todo in todos)
                    {
                        todo.TodoStatus = ProjectManagementStatus.OverDue.ToString();
                        context.ProjectMgmt_Todo.Update(todo);
                    }
                    context.SaveChanges();
                }
            }
        }
        #endregion

        #region Update project status to over due
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("project")]
        public void UpdateProjectStatusToOverDue(List<string> subdomains)
        {
            if (subdomains == null) return;
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var projects = context.ProjectMgmt_Projects.Where(x => x.ProjectStatus != ProjectStatus.Overdue && x.ProjectStatus != ProjectStatus.Completed && x.EndDate < DateTime.UtcNow).ToList();
                if (projects.Count > 0)
                {
                    foreach (var project in projects)
                    {
                        project.ProjectStatus = ProjectStatus.Overdue;
                        context.ProjectMgmt_Projects.Update(project);
                    }
                    context.SaveChanges();
                }
            }
        }
        #endregion

        #region Update sprint status to over due
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("sprint")]
        public void UpdateSprintStatusToOverDue(List<string> subdomains)
        {
            if (subdomains == null) return;
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var projects = context.SprintProjects.Where(x => x.Status != SprintStatus.EndSprint && x.Status != SprintStatus.Completed && x.EndDate < DateTime.UtcNow).ToList();
                if (projects.Count > 0)
                {
                    foreach (var project in projects)
                    {
                        project.Status = SprintStatus.EndSprint;
                        context.SprintProjects.Update(project);
                    }
                    context.SaveChanges();
                }
            }
        }
        #endregion

        #region Update Tenant project view
        /// <summary>
        /// Updates existing project into the tenant project view
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("general")]
        public async Task UpdateTenantProjectView(List<string> subdomains)
        {
            if (subdomains == null) return;
            var existingProjectViews = await _publicContext.TenantProjectViews
                .Where(tpv => subdomains.Contains(tpv.Subdomain))
                .ToListAsync();

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var projects = await context.ProjectMgmt_Projects.ToListAsync();

                var existingProjectsForTenant = existingProjectViews
                    .Where(tpv => tpv.Subdomain == subdomain)
                    .ToList();

                var newProjects = projects
                    .Where(project => !existingProjectsForTenant.Exists(tpv => tpv.ProjectId == project.ProjectId))
                    .Select(project => new TenantProjectView
                    {
                        Subdomain = subdomain,
                        CreatedTime = project.CreatedTime.Value,
                        ProjectId = project.ProjectId
                    })
                    .ToList();

                if (newProjects.Count > 0)
                    _publicContext.AddRange(newProjects);
            }
            await _publicContext.SaveChangesAsync();
        }
        #endregion

        #region Update Tenant project view
        /// <summary>
        /// Updates existing project into the tenant project view
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("general")]
        public async Task UpdateProjectMetricsViews(List<string> subdomains)
        {
            if (subdomains == null) return;
            var tenants = await _publicContext.Tenants.ToListAsync();

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                var sprintProjects = await context.SprintProjects.ToListAsync();

                var projectMetricsViews = sprintProjects
                    .GroupBy(sp => new { sp.StartDate.Year, sp.StartDate.Month })
                    .Select(group => new ProjectMetricsView
                    {
                        CompanyName = tenants.Find(t => t.Subdomain == subdomain)?.CompanyName,
                        SubDomain = subdomain,
                        TotalSprints = group.Distinct().Count(),
                        TotalTodo = group.SelectMany(sp => context.ProjectMgmt_Todo.Where(todo => todo.SprintProjectId == sp.Id)).Count(),
                        TotalHours = group.SelectMany(sp => context.ProjectMgmt_Todo.Where(todo => todo.SprintProjectId == sp.Id)).Sum(todo => ParseDurationToHours(todo.Duration)),
                        Year = group.Key.Year,
                        Month = group.Key.Month
                    })
                    .GroupBy(pm => new { pm.SubDomain, pm.Year, pm.Month })
                    .Select(group => new ProjectMetricsView
                    {
                        CompanyName = group.First().CompanyName,
                        SubDomain = group.Key.SubDomain,
                        TotalSprints = group.Sum(pm => pm.TotalSprints),
                        TotalTodo = group.Sum(pm => pm.TotalTodo),
                        TotalHours = group.Sum(pm => pm.TotalHours),
                        Year = group.Key.Year,
                        Month = group.Key.Month
                    });

                foreach (var sprintProjectView in projectMetricsViews)
                {
                    var existingRecord = _publicContext.ProjectMetricsViews
                        .FirstOrDefault(pmv =>
                            pmv.SubDomain == sprintProjectView.SubDomain &&
                            pmv.Year == sprintProjectView.Year && pmv.Month == sprintProjectView.Month
                        );

                    if (existingRecord != null)
                    {
                        existingRecord.TotalSprints = sprintProjectView.TotalSprints;
                        existingRecord.TotalTodo = sprintProjectView.TotalTodo;
                        existingRecord.TotalHours = sprintProjectView.TotalHours;
                        _publicContext.ProjectMetricsViews.Update(existingRecord);
                    }
                    else
                    {
                        await _publicContext.ProjectMetricsViews.AddAsync(sprintProjectView);
                    }
                }
            }
            await _publicContext.SaveChangesAsync();
        }
        #endregion

        #region Update Activity Score Table
        /// <summary>
        /// Updates Activity performance daily for yesterday's data
        /// </summary>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("general")]
        public async Task UpdateActivityScoreTable(List<string> subdomains)
        {
            try
            {
                if (subdomains == null) return;

                DateTime date = DateTime.Today;
                int offset = date.DayOfWeek - DayOfWeek.Monday;
                DateTime lastMonday = date.AddDays(-offset);
                DateTime nextFriday = lastMonday.AddDays(4); // Friday of the same week

                foreach (var subdomain in subdomains)
                {
                    try
                    {
                        var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                        var userProfiles = await context.UserProfiles.ToListAsync();
                        var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);

                        if (tenant == null) continue;

                        List<UserPerformanceActivityScoreView> newRecords = new List<UserPerformanceActivityScoreView>();
                        List<UserPerformanceActivityScoreView> updatedRecords = new List<UserPerformanceActivityScoreView>();

                        foreach (UserProfile user in userProfiles)
                        {
                            try
                            {
                                var userId = user.UserId;

                                // Check if there's already a record for this user and week
                                var existingRecord = await context.PerformanceMetricAnalyses
                                    .FirstOrDefaultAsync(x => x.UserId == userId && x.StartDateForTheWeek == lastMonday && x.EndDateForTheWeek == nextFriday);

                                var weeklyAnalytics = existingRecord ?? new UserPerformanceActivityScoreView
                                {
                                    Id = existingRecord?.Id ?? Guid.NewGuid(),
                                    UserId = userId,
                                    StartDateForTheWeek = lastMonday,
                                    EndDateForTheWeek = nextFriday,
                                    Industries = tenant.Industry,
                                    CreatedDate = existingRecord?.CreatedDate ?? DateTime.UtcNow
                                };

                                double weeklyTotal = 0;

                                // Collaboration on JobChat Percentage for the week
                                var chatPercentage = 0;

                                try
                                {
                                    var baseUrl = GlobalVariables.CustomAppSettings.UserColloborationCount.Replace("{jobId}", user.JobProId);
                                    var path = string.Empty;
                                    var response = await _apiCallService.MakeApiCallAsync<object, CollaborationCountDto>(baseUrl, path, Method.Get);
                                    if (response is null)
                                    {
                                        _logger.Error($"Failed to get collaboration count for user {user.JobProId}");
                                    }

                                    var messageCount = response != null ? response.MessageCount : 0;
                                    chatPercentage = messageCount >= 10 ? 10 : messageCount;
                                }
                                catch (Exception ex)
                                {
                                    // do nothing
                                }

                                for (int i = 0; i < 5; i++)
                                {
                                    DateTime currentDate = lastMonday.AddDays(i);

                                    // Todo Percentage - 50%
                                    var userDailyTodos = await context.projectMgmt_TodoUsers
                                        .Where(x => x.UserId == userId)
                                        .Select(x => x.ProjectMgmt_Todo)
                                        .Where(x => x.StartDateAndTime.Date == currentDate)
                                        .ToListAsync();

                                    double todoPercentage = 0;
                                    if (userDailyTodos.Count > 0)
                                    {
                                        var completedTodos = userDailyTodos.Count(x => x.TodoStatus == "Completed");
                                        todoPercentage = (double)completedTodos / userDailyTodos.Count * 50.0;
                                    }

                                    // Internal Meetings - 10%
                                    var meetings = await context.CalenderMeetings
                                        .CountAsync(x => x.CreatedBy.ToString() == userId && x.CreatedAt.Date == currentDate);
                                    var internalMeetingPercentage = meetings >= 2 ? 10 : meetings == 1 ? 5 : 0;

                                    // External Meetings - 20%
                                    var externalMeetingPercentage = 0;
                                    var externalMeets = await context.ExternalMeeting
                                        .AnyAsync(x => x.CreatedBy.ToString() == userId && x.CreatedAt.Date == currentDate);
                                    if (externalMeets)
                                    {
                                        externalMeetingPercentage = 20;
                                    }
                                    else
                                    {
                                        var externalInvites = await context.ExternalMeetingMembers
                                            .Where(x => x.UserId == userId)
                                            .Select(x => x.ExternalMeetingId)
                                            .ToListAsync();

                                        foreach (var meetingId in externalInvites)
                                        {
                                            var meets = await context.ExternalMeeting
                                                .AnyAsync(x => x.Id.ToString() == meetingId && x.MeetingStartDateRange == currentDate);
                                            if (meets)
                                            {
                                                externalMeetingPercentage = 20;
                                                break;
                                            }
                                        }
                                    }

                                    // Project and Sprint Percentage - 5%
                                    var projectAndSprintPercentage = 0;
                                    var projectCreated = await context.ProjectMgmt_Projects
                                        .AnyAsync(x => x.CreatedBy == userId && x.CreatedTime.Value.Date == currentDate);
                                    if (projectCreated)
                                    {
                                        projectAndSprintPercentage = 5;
                                    }
                                    else
                                    {
                                        var sprintCreated = await context.SprintProjects
                                            .AnyAsync(x => x.CreatedBy.ToString() == userId && x.CreatedDate.Date == currentDate);
                                        if (sprintCreated)
                                        {
                                            projectAndSprintPercentage = 5;
                                        }
                                    }

                                    // Created Todos - 5%
                                    var createdTodos = await context.ProjectMgmt_Todo
                                        .CountAsync(x => x.CreatedBy == userId && x.CreatedDate.Date == currentDate);
                                    var createdTodoPercentage = createdTodos > 2 ? 5 : 0;

                                    double dailyPercentage = todoPercentage + internalMeetingPercentage + externalMeetingPercentage +
                                                             projectAndSprintPercentage + createdTodoPercentage;

                                    switch (i)
                                    {
                                        case 0: weeklyAnalytics.Monday = dailyPercentage; break;
                                        case 1: weeklyAnalytics.Tuesday = dailyPercentage; break;
                                        case 2: weeklyAnalytics.Wednesday = dailyPercentage; break;
                                        case 3: weeklyAnalytics.Thursday = dailyPercentage; break;
                                        case 4: weeklyAnalytics.Friday = dailyPercentage; break;
                                    }

                                    weeklyTotal += dailyPercentage;
                                }

                                weeklyTotal += chatPercentage;
                                weeklyAnalytics.TotalPercentage = weeklyTotal / 5;

                                if (existingRecord == null)
                                {
                                    newRecords.Add(weeklyAnalytics);
                                }
                                else
                                {
                                    updatedRecords.Add(weeklyAnalytics);
                                }
                            }
                            catch (Exception ex)
                            {

                                _logger.Debug(ex.Message);
                            }

                        }

                        if (newRecords.Count > 0)
                        {
                            await context.PerformanceMetricAnalyses.AddRangeAsync(newRecords);
                        }

                        if (updatedRecords.Count > 0)
                        {
                            context.PerformanceMetricAnalyses.UpdateRange(updatedRecords);
                        }

                        await context.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {

                        _logger.Debug(ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex.Message);

                //do nothing
            }
        }
        #endregion

        #region Parse Duration To Hours
        private static double ParseDurationToHours(string duration)
        {
            var regex = new Regex(@"(?<hours>\d+)h\s*(?<minutes>\d+)?m?");
            var match = regex.Match(duration);

            if (match.Success)
            {
                var hours = int.Parse(match.Groups["hours"].Value);
                var minutes = match.Groups["minutes"].Success ? int.Parse(match.Groups["minutes"].Value) : 0;
                return hours + minutes / 60.0;
            }

            Console.WriteLine("Invalid duration format");
            return 0.0;
        }
        #endregion
    }
}
