﻿using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Utils._Helper;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.Controllers
{
    public class TeamSheetController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private readonly ITenantSchema _tenantSchema;
        private ILogger _logger = Log.ForContext<TeamSheetController>();

        public TeamSheetController(IUnitofwork unitofwork, ITenantSchema tenantSchema)
        {
            this.Services_Repo = unitofwork;
            _tenantSchema = tenantSchema;
        }

        #region Create Team
        /// <summary>
        /// Create Team
        /// </summary>
        /// <param name="teamDto"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("CreateTeam")]
        public async Task<IActionResult> CreateTeam(TeamDto teamDto)
        {
            try
            {
                Log.Information("Attempt to create a team");

                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                teamDto.Subdomain = subdomain;
                var team = await Services_Repo.TeamSheetService.AddTeam(teamDto);
                var userId = User.GetUserId();

                if (team is not null)
                {
                    // Log the activity
                    var description = $"{team.Name} team created by {CurrentUser}";
                    var summary = $"Team created";
                    await LogActivity(description, summary, team.Id.ToString());
                }

                return Ok(new ApiResponse<Team>()
                {
                    ResponseMessage = "Team Created Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = team
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                Log.Error(ex, "Error creating team");
                return Conflict(new ApiResponse<Team>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status400BadRequest.ToString(),
                    Data = null
                });
            }
        }
        #endregion

        #region Update Team
        /// <summary>
        /// Update Team
        /// </summary>
        /// <param name="teamDto"></param>
        /// <param name="teamId"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPut]
        [Route("UpdateTeam/{teamId}")]
        public async Task<IActionResult> UpdateTeam(TeamDto teamDto, string teamId)
        {
            try
            {
                Log.Information("Attempt to update a team");
                var team = await Services_Repo.TeamSheetService.UpdateTeam(teamDto, teamId);

                // Log the activity
                var description = $"{teamDto.Name} team updated by {CurrentUser}";
                var summary = $"Team updated";
                await LogActivity(description, summary, teamId);

                return Ok(new ApiResponse<bool>()
                {
                    ResponseMessage = "Team Updated Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = true
                });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, "Error updating team");
                return NotFound(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status400BadRequest.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Delete Team
        /// <summary>
        /// Delete Team
        /// </summary>
        /// <param name="teamId"></param>
        /// <returns></returns>
        [Authorize]
        [HttpDelete]
        [Route("DeleteTeam/{teamId}")]
        public async Task<IActionResult> DeleteTeam(string teamId)
        {
            try
            {
                Log.Information("Attempt to delete a team");
                var team = await Services_Repo.TeamSheetService.DeleteTeam(teamId);

                // Log the activity
                var description = $"Team deleted by {CurrentUser}";
                var summary = $"Team deleted";
                await LogActivity(description, summary, teamId);

                return Ok(new ApiResponse<bool>()
                {
                    ResponseMessage = "Team Deleted Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = true
                });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, "Error deleting team");
                return NotFound(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status400BadRequest.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Get Team By Id
        /// <summary>
        /// Get Team By Id
        /// </summary>
        /// <param name="teamId"></param>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetTeamById/{teamId}")]
        public async Task<IActionResult> GetTeamById(string teamId)
        {
            try
            {
                Log.Information($"Attempt to get team by id - {teamId}");
                var team = await Services_Repo.TeamSheetService.GetTeamById(teamId);

                // Log the activity
                var description = $"Team retrieved by {CurrentUser}";
                var summary = $"Team retrieved";
                await LogActivity(description, summary, teamId);

                return Ok(new ApiResponse<TeamVm>()
                {
                    ResponseMessage = "Team Members Retrieved Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = team
                });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, $"Error getting team by id - {teamId}");
                return BadRequest(new ApiResponse<TeamVm>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status400BadRequest.ToString(),
                    Data = null
                });
            }
        }
        #endregion

        #region Get All Teams
        /// <summary>
        /// Get All Teams
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetAllTeams")]
        public async Task<IActionResult> GetAllTeams()
        {
            Log.Information("Attempt to get all teams");
            var teams = await Services_Repo.TeamSheetService.GetAllTeams();

            // Log the activity
            var description = $"All teams retrieved by {CurrentUser}";
            var summary = $"All teams retrieved";
            await LogActivity(description, summary, null);

            return Ok(new ApiResponse<List<TeamVm>>()
            {
                ResponseMessage = "Teams Retrieved Successfully",
                ResponseCode = StatusCodes.Status200OK.ToString(),
                Data = teams
            });
        }
        #endregion

        #region Get All User Teams
        /// <summary>
        /// Get All Teams
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetAllUserTeams/{UserId}")]
        public async Task<IActionResult> GetAllUserTeams(String UserId)
        {
            Log.Information("Attempt to get all user teams");
            var teams = await Services_Repo.TeamSheetService.GetAllUserTeams(UserId);

            // Log the activity
            var description = $"All teams retrieved by {CurrentUser}";
            var summary = $"All teams retrieved";
            await LogActivity(description, summary, null);

            return Ok(new ApiResponse<List<Team>>()
            {
                ResponseMessage = "Teams Retrieved Successfully",
                ResponseCode = StatusCodes.Status200OK.ToString(),
                Data = teams
            });
        }
        #endregion

        #region Get Team members
        /// <summary>
        /// Get Team members
        /// </summary>
        /// <param name="teamId"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetTeamMembers/{teamId}")]
        public async Task<IActionResult> GetTeamMembers(string teamId, [FromQuery] GetAllTeamMembersFilter filters)
        {
            try
            {
                Log.Information($"Attempt to get team members for team - {teamId}");
                var teamMembers = await Services_Repo.TeamSheetService.GetTeamMembers(teamId, filters);

                // Log the activity
                var description = $"Team members retrieved by {CurrentUser}";
                var summary = $"Team members retrieved";
                await LogActivity(description, summary, teamId);

                return Ok(teamMembers);
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, $"Error getting team members for team - {teamId}");
                return BadRequest(new ApiResponse<List<UserDto>>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status400BadRequest.ToString(),
                    Data = null
                });
            }
        }
        #endregion

        #region Add internal team members
        /// <summary>
        /// Add internal team members
        /// </summary>
        /// <param name="teamId"></param>
        /// <param name="internalTeamMembers"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("AddInternalTeamMembers/{teamId}")]
        public async Task<IActionResult> AddInternalTeamMembers(string teamId, [FromBody] List<string> internalTeamMembers)
        {
            try
            {
                Log.Information("Attempt to add internal team members");
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var teamMembers = await Services_Repo.TeamSheetService.AddInternalTeamMembers(teamId, internalTeamMembers, subdomain, CurrentUserId.ToString());

                // Log the activity
                var description = $"Internal team members added by {CurrentUser}";
                var summary = $"Internal team members added";
                await LogActivity(description, summary, teamId);

                return Ok(new ApiResponse<bool>()
                {
                    ResponseMessage = "Team Members Added Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = true
                });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, "Error adding internal team members");
                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status500InternalServerError.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Add external team members
        /// <summary>
        /// Add external team members
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddExternalTeamMembers")]
        public async Task<IActionResult> AddExternalTeamMembers(AddExternalTeamMembersToTHDto model)
        {
            try
            {
                Log.Information("Attempt to add external team members");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.SubDomain = subdomain;

                var teamMembers = await Services_Repo.TeamSheetService.AddExternalTeamMembers(model);

                // Log the activity
                var description = $"External team members added by {CurrentUser}";
                var summary = $"External team members added";
                await LogActivity(description, summary, model.TeamId);

                return Ok(new ApiResponse<bool>()
                {
                    ResponseMessage = "Team Members Added Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = true
                });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, "Error adding external team members");
                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status500InternalServerError.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Remove team members
        /// <summary>
        /// Remove team members
        /// </summary>
        /// <param name="teamId"></param>
        /// <param name="userIds"></param>
        /// <returns></returns>
        [Authorize]
        [HttpDelete]
        [Route("RemoveTeamMember/{teamId}")]
        public async Task<IActionResult> RemoveTeamMember(string teamId, [FromBody] List<string> userIds)
        {
            try
            {
                Log.Information("Attempt to remove team member");
                var teamMembers = await Services_Repo.TeamSheetService.RemoveTeamMember(teamId, userIds);

                // Log the activity
                var description = $"Team member removed by {CurrentUser}";
                var summary = $"Team member removed";
                await LogActivity(description, summary, teamId);

                return Ok(new ApiResponse<bool>()
                {
                    ResponseMessage = "Team Member Removed Successfully",
                    ResponseCode = StatusCodes.Status200OK.ToString(),
                    Data = true
                });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex, "Error removing team member");
                return BadRequest(new ApiResponse<bool>()
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "An error has occured, Please try again later",
                    ResponseCode = StatusCodes.Status500InternalServerError.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Get Pending invitations
        /// <summary>
        /// Get pending invitations
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="paginationParameters"></param>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetPendingInvitations")]
        public async Task<IActionResult> GetPendingInvitations(string tenantId, [FromQuery] PaginationParameters paginationParameters)
        {
            Log.Information("Attempt to get pending invitations");
            var pendingInvitations = await Services_Repo.TeamSheetService.GetPendingInvitations(tenantId, paginationParameters);

            // Log the activity
            var description = $"Pending invitations retrieved by {CurrentUser}";
            var summary = $"Pending invitations retrieved";
            await LogActivity(description, summary, tenantId);

            return Ok(new ApiResponse<List<CompanyUserInviteVM>>()
            {
                ResponseMessage = "Pending Invitations Retrieved Successfully",
                ResponseCode = StatusCodes.Status200OK.ToString(),
                Data = pendingInvitations
            });
        }
        #endregion

        #region Get Team Composition
        /// <summary>
        /// Get Team Composition
        /// </summary>
        /// <returns></returns>
        [Authorize]
        [HttpGet]
        [Route("GetTeamComposition")]
        public async Task<IActionResult> GetTeamComposition()
        {
            Log.Information("Attempt to get team composition");
            var teamComposition = await Services_Repo.TeamSheetService.GetTeamComposition();

            // Log the activity
            var description = $"Team composition retrieved by {CurrentUser}";
            var summary = $"Team composition retrieved";
            await LogActivity(description, summary, null);

            return Ok(new ApiResponse<GenericResponse>()
            {
                ResponseMessage = "Team Composition Retrieved Successfully",
                ResponseCode = StatusCodes.Status200OK.ToString(),
                Data = teamComposition
            });
        }
        #endregion

        #region Invite external users to a company
        /// <summary>
        /// Invite external users to a company
        /// </summary>
        /// <param name="invites"></param>
        /// <param name="resendInvite"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("InviteExternalUsers")]
        public async Task<IActionResult> InviteExternalUsers([FromBody] List<BulkInviteDto> invites, bool resendInvite)
        {
            try
            {
                Log.Information("Attempt to invite external users");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var userId = User.GetUserId();
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                var response = await Services_Repo.TeamSheetService.InviteExternalUserToCompany(invites, subdomain, userId, resendInvite, token);

                if (response.ResponseCode == "200")
                {
                    // Log the activity
                    var description = $"{CurrentUser} invited user to JOBPRO";
                    var summary = $"External Invitation";
                    await LogActivity(description, summary, null);

                    return Ok(new ApiResponse<List<string>>()
                    {
                        ResponseMessage = response.ResponseMessage,
                        ResponseCode = StatusCodes.Status200OK.ToString(),
                        Data = response.Data as List<string>
                    });
                }

                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = response.ResponseMessage,
                    ResponseCode = response.ResponseCode,
                    Data = false
                });

            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex, "Error inviting external users");
                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status500InternalServerError.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Bulk Invite External Users
        /// <summary>
        /// Bulk Invite External Users
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("BulkInviteExternalUsers")]
        public async Task<IActionResult> BulkInviteExternalUsers([FromForm] UploadDocumentForBulkInviteDto file)
        {
            try
            {
                Log.Information("Attempt to bulk invite external users");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var userId = User.GetUserId();
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                var response = await Services_Repo.TeamSheetService.ProcessBulkInvite(file.File, subdomain, userId, token);

                if (response.ResponseCode == "200")
                {
                    // Log the activity
                    var description = $"{CurrentUser} invited users to Jobpro";
                    var summary = $"Bulk External Invitation";
                    await LogActivity(description, summary, null);

                    return Ok(new ApiResponse<List<string>>()
                    {
                        ResponseMessage = response.ResponseMessage,
                        ResponseCode = StatusCodes.Status200OK.ToString(),
                        Data = response.Data as List<string>
                    });
                }

                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = response.ResponseMessage,
                    ResponseCode = response.ResponseCode,
                    Data = false
                });

            }
            catch (DirtyFormException ex)
            {
                Log.Error(ex, "Error bulk inviting external users");
                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status500InternalServerError.ToString(),
                    Data = false
                });
            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex, "Error bulk inviting external users");
                return BadRequest(new ApiResponse<bool>()
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = StatusCodes.Status500InternalServerError.ToString(),
                    Data = false
                });
            }
        }
        #endregion

        #region Revoke Invitation
        [Authorize]
        [HttpDelete]
        [Route("RevokeInvitation/{invitationId}")]
        public async Task<IActionResult> RevokeInvitation(string invitationId)
        {
            try
            {
                Log.Information("Attempt to revoke invitation");
                var response = await Services_Repo.TeamSheetService.RevokeInvite(invitationId);

                if (response.ResponseCode == "200")
                {
                    // Log the activity
                    var description = $"{CurrentUser} revoked invitation";
                    var summary = $"Invitation Revoked";
                    await LogActivity(description, summary, null);

                    return Ok(response);
                }

                return BadRequest(response);

            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex, "Error revoking invitation");
                return BadRequest(new GenericResponse
                {
                    ResponseCode = 
                    StatusCodes.Status500InternalServerError.ToString(),
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Private Methods
        private async Task LogActivity(string description, string summary, string eventId = null)
        {
            var canActivityBeLogged = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId.ToString(), JobProject.Enums.Enums.EventCategory.TeamSheet);
            if (canActivityBeLogged)
            {
                var activity = new ActivityDto
                {
                    ActivitySummary = summary,
                    Description = description,
                    EventCategory = JobProject.Enums.Enums.EventCategory.TeamSheet,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                    EventId = eventId,
                    Application = Applications.Joble
                };
                await Services_Repo.ActivityService.CreateLog(activity);
            }
        }
        #endregion
    }
}
