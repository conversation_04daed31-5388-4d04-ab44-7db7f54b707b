﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Jobid.App.ActivityLog.ViewModel
{
    public class AcitivityLogFilters
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<string> MemberIds { get; set; } = new List<string>();
        public List<string> EventCategory { get; set; }
        public string SearchParam { get; set; } = string.Empty;
        public bool AnyOneCanView { get; set; }
        public string ShareId { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }
    }
}
