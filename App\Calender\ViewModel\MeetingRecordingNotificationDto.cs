﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.ViewModel
{
    public class MeetingRecordingNotificationDto
    {
        [Required]
        public string MeetingId { get; set; }

        public string MettingRecordingUrl { get; set; }

        [Required]
        public bool IsRecordingAvalable { get; set; }

        [JsonIgnore]
        public string Subdomain { get; set; }
    }
    public class MeetingRecordingNotificationResponse
    {
        public string MeetingId { get; set; }

        public string MettingRecordingUrl { get; set; }

        public bool IsRecordingAvalable { get; set; }
    }
}
