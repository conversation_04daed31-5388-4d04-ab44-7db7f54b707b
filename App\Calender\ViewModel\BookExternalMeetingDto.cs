﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class BookExternalMeetingDto
    {
        public int DurationInMinutes { get; set; }
        public string TimeZone { get; set; }
        public DateTime SelectedDateAndTime { get; set; }

        [Required]
        public string FullName { get; set; }

        [Required]
        public string Email { get; set; }
        public string Location { get; set; }
        public List<string> GuestEmails { get; set; }

        // To be separated with a '-'
        public List<CustomQuestionAnswerDto> CustomQuestionAnwsersDto { get; set; }
        public string AdditionalInfo { get; set; }
        public Guid ExternalMeetingId { get; set; }
        public string ReasonForCancelling { get; set; }
        public string ReasonForReSchedulling { get; set; }

        [JsonIgnore]
        public List<string> InternalMemberIds { get; set; } = new List<string>();

        [JsonIgnore]
        public List<string> ExternalMemberEmails { get; set; } = new List<string>();


        [JsonIgnore]
        public string SubDomain { get; set; }
    }

    public class CustomQuestionAnswerDto
    {
        public string Question { get; set; }
        public string Answer { get; set; }
        public string CustomQuestionId { get; set; }
    }
}
