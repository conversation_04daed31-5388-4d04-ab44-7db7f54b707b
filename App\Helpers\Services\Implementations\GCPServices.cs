﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.IO;
using System.Threading.Tasks;
using System;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.Storage.V1;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Configurations;
using Jobid.App.Helpers.Services.Contract;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class GCPServices : IGCPServices
    {
        private readonly GoogleCredential _googleCredential;
        private readonly GCPConfigOptions _options;

        /// <summary>
        /// This constructor is used to initialize the GCP Bucket Service.
        /// </summary>
        public GCPServices(IConfiguration config)
        {
            // Get the configuration options from appsettings.json
            _options = config.GetSection("GCPConfigOptions").Get<GCPConfigOptions>();

            try
            {
                var secretKeyPath = Path.Combine(Environment.CurrentDirectory, "Secrets", "gcp-account-key.json");
                _googleCredential = GoogleCredential.FromFile(secretKeyPath);

                //_googleCredential = GoogleCredential.FromJson($@"{{
                //    ""private_key"": ""{_options.PrivateKey}"",
                //    ""client_email"": ""{_options.ClientEmail}""
                //}}");
                GlobalVariables.Test = _googleCredential.ToString();
            }
            catch (Exception ex)
            {
                throw new OperationFailedException(ex.Message);
            }
        }

        #region Delete file from GCP Bucket
        /// <summary>
        /// This method is used to delete a file from the GCP Bucket. It takes the file name as input.
        /// </summary>
        /// <param name="fileNameToDelete"></param>
        /// <returns>bool</returns>
        public async Task<bool> DeleteFileAsync(string fileNameToDelete)
        {
            try
            {
                using (var storageClient = StorageClient.Create(_googleCredential))
                {
                    await storageClient.DeleteObjectAsync(_options.BucketName, fileNameToDelete);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new OperationFailedException(ex.Message);
            }
        }
        #endregion

        #region Get signed url of a file from GCP Bucket
        /// <summary>
        /// This method is used to get a signed url of a file from the GCP Bucket. It takes the file name as input.
        /// </summary>
        /// <param name="fileNameToRead"></param>
        /// <param name="timeOutInMinutes"></param>
        /// <returns></returns>
        public async Task<string> GetSignedUrlAsync(string fileNameToRead, int timeOutInMinutes = 10080)
        {
            try
            {
                var sac = _googleCredential.UnderlyingCredential as ServiceAccountCredential;
                var urlSigner = UrlSigner.FromServiceAccountCredential(sac);

                // provides limited permission and time to make a request: time here is mentioned for 30 minutes.
                var signedUrl = await urlSigner.SignAsync(_options.BucketName, fileNameToRead, TimeSpan.FromMinutes(timeOutInMinutes));

                return signedUrl.ToString();
            }
            catch (Exception ex)
            {
                return null;
                throw new OperationFailedException(ex.Message);
            }
        }
        #endregion

        #region Upload file to GCP Bucket
        /// <summary>
        /// This method is used to upload a file to the GCP Bucket. It takes the file and the file name as input.
        /// </summary>
        /// <param name="fileToUpload">The file to be uploaded.</param>
        /// <param name="fileNameToSave">The name under which the file will be saved in the bucket.</param>
        /// <returns>The URL of the uploaded file.</returns>
        /// <exception cref="FileUploadException">Thrown when the file size exceeds the limit of 5MB.</exception>
        /// <exception cref="OperationFailedException">Thrown when the upload operation fails.</exception>
        public async Task<string> UploadFileAsync(IFormFile fileToUpload, string fileNameToSave)
        {
            try
            {
                // Check if fileToUpload is null
                if (fileToUpload == null)
                {
                    throw new FileUploadException($"The file {nameof(fileToUpload)} to upload cannot be null.");
                }

                // Check if file size is greater than 0
                if (fileToUpload.Length <= 0)
                {
                    throw new FileUploadException($"The file size must be greater than 0.");
                }

                // Check if file size exceeds 5MB
                if (fileToUpload.Length > 5 * 1024 * 1024)
                {
                    throw new FileUploadException("File size exceeds the limit of 5MB.");
                }

                using (var memoryStream = new MemoryStream())
                {
                    await fileToUpload.CopyToAsync(memoryStream);

                    // Create Storage Client from Google Credential
                    using (var storageClient = StorageClient.Create(_googleCredential))
                    {
                        // Upload file stream
                        var uploadedFile = await storageClient.UploadObjectAsync(
                            _options.BucketName,
                            fileNameToSave,
                            fileToUpload.ContentType,
                            memoryStream
                        );

                        return uploadedFile.MediaLink;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new OperationFailedException($"An error occurred during file upload: {ex.Message}");
            }
        }
        #endregion

        #region Upload file from mobile to GCP Bucket
        /// <summary>
        /// This method is used to upload a file to the GCP Bucket. It takes the file, the file name, and the file content type as input.
        /// </summary>
        /// <param name="fileToUpload">The file to be uploaded.</param>
        /// <param name="fileNameToSave">The name under which the file will be saved in the bucket.</param>
        /// <param name="fileContentType">The content type of the file being uploaded.</param>
        /// <returns>The URL of the uploaded file.</returns>
        /// <exception cref="FileUploadException">Thrown when the file size exceeds the limit of 5MB.</exception>
        /// <exception cref="OperationFailedException">Thrown when the upload operation fails.</exception>
        public async Task<string> UploadFileFromMobileAsync(IFormFile fileToUpload, string fileNameToSave, string fileContentType)
        {
            try
            {
                // Check if fileToUpload is null
                if (fileToUpload == null)
                {
                    throw new FileUploadException($"The file {nameof(fileToUpload)} to upload cannot be null.");
                }

                // Check if file size is greater than 0
                if (fileToUpload.Length <= 0)
                {
                    throw new FileUploadException($"The file size must be greater than 0.");
                }

                // Check if file size exceeds 5MB
                if (fileToUpload.Length > 5 * 1024 * 1024)
                {
                    throw new FileUploadException("File size exceeds the limit of 5MB.");
                }

                using (var memoryStream = new MemoryStream())
                {
                    await fileToUpload.CopyToAsync(memoryStream);

                    // Create Storage Client from Google Credential
                    using (var storageClient = StorageClient.Create(_googleCredential))
                    {
                        // Upload file stream
                        var uploadedFile = await storageClient.UploadObjectAsync(
                            _options.BucketName,
                            fileNameToSave,
                            fileContentType,
                            memoryStream
                        );

                        return uploadedFile.MediaLink;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new OperationFailedException($"An error occurred during file upload: {ex.Message}");
            }
        }
        #endregion

        #region Download file from GCP Bucket and convert to base64 string
        public async Task<string> DownloadFile(string fileNameToDownload)
        {
            try
            {
                using (var storageClient = StorageClient.Create(_googleCredential))
                {
                    using (var memoryStream = new MemoryStream())
                    {
                        await storageClient.DownloadObjectAsync(_options.BucketName, fileNameToDownload, memoryStream);
                        var fileBytes = memoryStream.ToArray();
                        var fileBase64 = Convert.ToBase64String(fileBytes);
                        return fileBase64;
                    }
                }
            }
            catch
            {
                throw;
            }
        }
        #endregion
    }
}
