using System;
using System.ComponentModel.DataAnnotations;
using Jobid.App.AdminConsole.Enums;
using Newtonsoft.Json;

namespace Jobid.App.AdminConsole.Dto
{    public class FundWalletDto
    {
        [Required]
        public string TenantId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
        
        [Required]
        public PaymentProvider Provider { get; set; }
        
        public string Description { get; set; }
        
        public FundingType FundingType { get; set; } = FundingType.Direct;
        
        public string Currency { get; set; } = "USD";

        [JsonIgnore]
        public string Subdomain { get; set; }
    }
    
    public class WalletTransactionDto
    {
        [Required]
        public string WalletId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
        
        [Required]
        public TransactionType Type { get; set; }
        
        public string Description { get; set; }
        
        public Guid? PhoneNumberId { get; set; }
    }
    
    public class PaymentDto
    {
        [Required]
        public string TenantId { get; set; }
        
        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
        public decimal Amount { get; set; }
        
        [Required]
        public PaymentProvider Provider { get; set; }
        
        [Required]
        public string Currency { get; set; } = "USD";
        
        public string Description { get; set; }
        
        public string ReturnUrl { get; set; }
        
        public string CancelUrl { get; set; }
    }
    
    public class WalletResponseDto
    {
        public Guid Id { get; set; }
        public string TenantId { get; set; }
        public decimal Balance { get; set; }
        public string Currency { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
    
    public class TransactionResponseDto
    {
        public Guid Id { get; set; }
        public string WalletId { get; set; }
        public decimal Amount { get; set; }
        public TransactionType Type { get; set; }
        public TransactionStatus Status { get; set; }
        public string Description { get; set; }
        public string Reference { get; set; }
        public string PaymentProvider { get; set; }
        public string PaymentProviderReference { get; set; }
        public Guid? PhoneNumberId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
} 