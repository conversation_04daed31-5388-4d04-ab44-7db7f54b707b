﻿using Microsoft.Extensions.DependencyInjection;
using Mollie.Api.Client.Abstract;
using Mollie.Api.Client;
using Mollie.Api.Framework;
using Microsoft.Extensions.Configuration;
using System;

public static class MollieApiClientServiceExtensions
{
    public static IServiceCollection AddMollieApi(this IServiceCollection services, IConfiguration configuration)
    {
        MollieConfiguration mollieConfiguration = new MollieConfiguration();
        var apiKey = Environment.GetEnvironmentVariable("JOBPRO_MOLLIE_API_KEY_TEST") ?? configuration["Mollie:ApiKey"];
        mollieConfiguration.ApiKey = apiKey;

        services.AddHttpClient<IPaymentClient, PaymentClient>(httpClient => new PaymentClient(mollieConfiguration.ApiKey, httpClient));
        //.AddPolicyHandler(GetDefaultRetryPolicy());
        services.AddHttpClient<ICustomerClient, CustomerClient>(httpClient => new CustomerClient(mollieConfiguration.ApiKey, httpClient));
        services.AddHttpClient<ICustomerClient, CustomerClient>(httpClient => new CustomerClient(mollieConfiguration.ApiKey, httpClient));
        services.AddHttpClient<ISubscriptionClient, SubscriptionClient>(httpClient => new SubscriptionClient(mollieConfiguration.ApiKey, httpClient));
        services.AddHttpClient<IMandateClient, MandateClient>(httpClient => new MandateClient(mollieConfiguration.ApiKey, httpClient));
        services.AddHttpClient<IPaymentClient, PaymentClient>(httpClient => new PaymentClient(mollieConfiguration.ApiKey, httpClient));

        return services;
    }

    // Do not delete -- Dozie
    //static IAsyncPolicy<HttpResponseMessage> GetDefaultRetryPolicy()
    //{
    //    return HttpPolicyExtensions
    //        // Timeout errors or 5xx static code errors
    //        .HandleTransientHttpError()
    //        // Requests are retried three times, with different intervals
    //        .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2,
    //            retryAttempt)));
    //}
}