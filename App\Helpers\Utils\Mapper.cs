﻿using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Subscription.Models;
using Jobid.App.Tenant.ViewModel;
using System;

namespace Jobid.App.Helpers.Utils
{
    public static class Mapper
    {
        public static Tenant.Model.Tenant MapToTenant(this UpdateTenantDto user, Tenant.Model.Tenant tenant)
        {
            tenant.CompanyName = user.CompanyName;
            tenant.CompanySize = user.CompanySize;
            tenant.Country = user.Country;
            tenant.CompanyType = user.CompanyType;
            tenant.ContactNo = user.ContactNo;
            tenant.CountryCode = user.CountryCode;
            tenant.VerifiedEmailDomain = user.VerifiedEmailDomain;
            tenant.Status = user.Status;
            tenant.Subdomain = user.Subdomain;
            tenant.DateCreated = user.DateCreated;
            tenant.RegNumber = user.RegNumber;
            tenant.LastUpdate = user.LastUpdate;

            return tenant;
        }

        public static UserMDVm MapToUserMDVm(this User user)
        {
            return new UserMDVm
            {
                PhoneNumber = user.PhoneNumber,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                Country = user.Country,
                Id = user.Id,
                State = user.State,
                IsVerified = true,
                PersonalEmail = user.Email,
                ReferralCode = user.ReferralCode
            };
        }

        public static CalenderMeeting FromSubsequentToNormalMeeting(this SubsequentMeeting subsequentMeeting)
        {
            return new CalenderMeeting
            {
                Name = subsequentMeeting.Name,
                StartDate = subsequentMeeting.SubsequentMeetingDateTime,
                EndTime = subsequentMeeting.EndTime,
                MeetingDuration = subsequentMeeting.MeetingDuration,
                MakeSchdulePrivate = subsequentMeeting.MakeSchdulePrivate,
                NotifyMe = subsequentMeeting.NotifyMe,
                NotifyMembersIn = subsequentMeeting.NotifyMembersIn,
                EndDate = subsequentMeeting.SubsequentMeetingDateTime.AddMinutes(double.Parse(subsequentMeeting.MeetLength.ToString())),
                MeetLength = subsequentMeeting.MeetLength
            };
        }

        public static SubscriptionHistory Map(this Subscription.Models.Subscription subscription)
        {
            return new SubscriptionHistory()
            {
                Amount = subscription.Amount,
                CreatedAt = subscription.CreatedAt,
                MollieCustomerId = subscription.MollieCustomerId,
                StripeCustomerId = subscription.StripeCustomerId,
                PaymentId = subscription.PaymentId,
                SubscriptionId = subscription.SubscriptionId,
                PricingPlanId = subscription.PricingPlanId,
                Status = subscription.Status,
                TenantId = subscription.TenantId,
                TransactionDate = subscription.TransactionDate,
                UserId = subscription.UserId,
                Application = subscription.Application,
                SubscriptionFor = subscription.SubscriptionFor,
                Interval = subscription.Interval,
                Currency = subscription.Currency,
                PaymentMethod = subscription.PaymentMethod,
                ActivatedOn = subscription.ActivatedOn,
                ConsumerAccount = subscription.ConsumerAccount,
                ExpiresOn = subscription.ExpiresOn,
                MandateId = subscription.MandateId,
                PaymentProvider = subscription.PaymentProvider,
                PaypalBillingAgreementId = subscription.PaypalBillingAgreementId,
                PayPalEmail = subscription.PayPalEmail,
                UpdatedAt = subscription.UpdatedAt,
                MandateReference = subscription.MandateReference,
                TransactionCode = subscription.TransactionCode,
            };
        }

        public static ExternalMeeting Map(this ExternalMeetingDto model, ExternalMeeting? externalMeeting = null)
        {
            if (model == null) return null;

            if (externalMeeting != null)
            {
                externalMeeting.MeetingName = model.MeetingName;
                externalMeeting.Location = model.Location;
                externalMeeting.Description = model.Description;
                externalMeeting.CanBookEndDate = model.CanBookEndDate;
                externalMeeting.CanBookStartDate = model.CanBookStartDate;
                externalMeeting.ExternalMeetingType = model.ExternalMeetingType;
                externalMeeting.ExternalMeetingTypePlan = model.ExternalMeetingTypePlan;
                externalMeeting.Price = model.Price;
                externalMeeting.Currency = model.Currency;
                externalMeeting.EmailComfirmation = model.EmailComfirmation;
                externalMeeting.EmailRemindersBefore = model.EmailRemindersBefore;
                externalMeeting.EmailFollowUpAfter = model.EmailFollowUpAfter;
                externalMeeting.CancellationPolicy = model.CancellationPolicy;
                externalMeeting.ComfirmationPageOptions = model.ComfirmationPageOptions;
                externalMeeting.SiteUrl = model.SiteUrl;
                externalMeeting.PushMeetingToWebsite = model.PushMeetingToWebsite;
                externalMeeting.InternalNote = model.InternalNote;
                externalMeeting.MeetingDuration = model.MeetingDuration;
                externalMeeting.CanBeChosenBasedOnAvailability = model.CanBeChosenBasedOnAvailability;
                externalMeeting.MeetingFrequency = model.MeetingFrequency;
                externalMeeting.MeetingStartDateRange = model.MeetingStartDateRange;
                externalMeeting.MeetingEndDateRange = model.MeetingEndDateRange;
                externalMeeting.MeetingOwnerId = model.MeetingOwnerId;
                externalMeeting.MakeMeetingPrivate = model.MakeMeetingPrivate;
                externalMeeting.MeetingBuffer = model.MeetingBuffer;
                externalMeeting.AvoidConflicts = model.AvoidConflicts;
                externalMeeting.MaxNoOfBookingsPerSlot = model.MaxNoOfBookingsPerSlot;
                return externalMeeting;
            }

            return new ExternalMeeting
            {
                MeetingName = model.MeetingName,
                Location = model.Location,
                Description = model.Description,
                CanBookEndDate = model.CanBookEndDate,
                CanBookStartDate = model.CanBookStartDate,
                ExternalMeetingType = model.ExternalMeetingType,
                ExternalMeetingTypePlan = model.ExternalMeetingTypePlan,
                Price = model.Price,
                Currency = model.Currency,
                EmailComfirmation = model.EmailComfirmation,
                EmailRemindersBefore = model.EmailRemindersBefore,
                EmailFollowUpAfter = model.EmailFollowUpAfter,
                CancellationPolicy = model.CancellationPolicy,
                ComfirmationPageOptions = model.ComfirmationPageOptions,
                SiteUrl = model.SiteUrl,
                PushMeetingToWebsite = model.PushMeetingToWebsite,
                InternalNote = model.InternalNote,
                MeetingDuration = model.MeetingDuration,
                CanBeChosenBasedOnAvailability = model.CanBeChosenBasedOnAvailability,
                MeetingFrequency = model.MeetingFrequency,
                MeetingStartDateRange = model.MeetingStartDateRange,
                MeetingEndDateRange = model.MeetingEndDateRange,
                MeetingOwnerId = model.MeetingOwnerId,
                MakeMeetingPrivate = model.MakeMeetingPrivate,
                MeetingBuffer = model.MeetingBuffer,
                AvoidConflicts = model.AvoidConflicts,
                MaxNoOfBookingsPerSlot = model.MaxNoOfBookingsPerSlot
            };
        }

        public static ClientRole Map(this ClientRoleVm model)
        {
            return new ClientRole()
            {
                CreatedAt = DateTime.UtcNow,
                RoleName = model.RoleName,
                CreatedBy = model.CreatedBy
            };

        }

        public static Tenant.Model.Tenant Map(this TenantRegistrationVM model)
        {
            return new Tenant.Model.Tenant()
            {
                CompanyName = model.CompanyName,
                ContactNo = model.ContactNo,
                Subdomain = model.Subdomain,
                Country = model.Country,
                CountryCode = model.CountryCode,
                RegNumber = model.RegNumber,
                CompanyType = model.CompanyType,
                Industry = model.Industry,
            };

        }

        public static TenantModelVM Map(this Tenant.Model.Tenant model)
        {
            return new TenantModelVM()
            {
                CompanyName = model.CompanyName,
                ContactNo = model.ContactNo,
                DateCreated = model.DateCreated,
                Id = model.Id,
                LastUpdate = model.LastUpdate,
                Country = model.Country,
                Subdomain = model.Subdomain,
                UpdatedBy = model.UpdatedBy,
                LogoUrl = model.LogoUrl,
            };

        }

        public static CompanyUserInviteVM Map(this CompanyUserInvite model)
        {
            // return new CompanyUserInviteVM {
            //     DateCreated=model.DateCreated,
            //     Email=model.Email,
            //     Id=model.Id,
            //     LastUpdate=model.LastUpdate,
            //     Status=model.Status,
            // };
            CompanyUserInviteVM companyUserInviteVM = new CompanyUserInviteVM();
            companyUserInviteVM.DateCreated = model.DateCreated;
            companyUserInviteVM.Email = model.Email;
            companyUserInviteVM.Id = model.Id;
            companyUserInviteVM.LastUpdate = model.LastUpdate;
            companyUserInviteVM.Status = model.Status;
            companyUserInviteVM.InviteCode = model.InviteCode;

            return companyUserInviteVM;
        }

        public static UserCompaniesVM Map(this UserCompanies model)
        {
            UserCompaniesVM userCompaniesVM = new UserCompaniesVM();
            userCompaniesVM.Active = model.Active;
            userCompaniesVM.DateCreated = model.DateCreated;
            userCompaniesVM.Id = model.Id;
            userCompaniesVM.LastUpdate = model.LastUpdate;
            try
            {
                userCompaniesVM.tenant = model.tenant.Map();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }

            userCompaniesVM.TenantId = model.TenantId;
            userCompaniesVM.UpdatedBy = model.UpdatedBy;
            try
            {
                userCompaniesVM.user = model.user.Map();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
            userCompaniesVM.UserId = model.UserId;
            userCompaniesVM.Email = model.Email;


            return userCompaniesVM;
        }

        public static UserVm Map(this User model)
        {
            UserVm userVm = new UserVm();

            userVm.FirstName = model.FirstName;
            userVm.Id = model.Id;
            userVm.IpAddress = model.IpAddress;
            userVm.LastName = model.LastName;
            userVm.RegionName = model.Region;
            // RoleName=model.ro,
            return userVm;
        }

        public static OTPVM Map(this OTP model)
        {
            return new OTPVM
            {
                DateCreated = model.DateCreated,
                Id = model.Id,
                Identifier = model.Identifier,
                IdentifierType = model.IdentifierType,
                LastUpdate = model.LastUpdate,
                Status = model.Status,
                Token = model.Token,
            };
        }
    }
}
