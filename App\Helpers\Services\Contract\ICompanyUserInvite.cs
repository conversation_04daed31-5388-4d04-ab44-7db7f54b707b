using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.ViewModel;

namespace Jobid.App.Tenant.Contract
{
    public interface ICompanyUserInvite
    {
        Task<bool> CreateOrUpdateInvite(CompanyUserInviteVM model, string tenantId);
        Task<List<CompanyUserInviteVM>> GetInvites(string tenantId, PaginationParameters paginationParameter, CompanyUserInviteStatus status=CompanyUserInviteStatus.PendingFinalization);
        Task<CompanyUserInviteVM> GetInvite(string email);
        CompanyUserInviteVM GetInviteByCode(string code);
        Task<bool> UpdateInvite(CompanyUserInviteVM model);
    }
}
