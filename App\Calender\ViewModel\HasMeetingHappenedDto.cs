﻿using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class HasMeetingHappenedDto
    {
        public class SalesActivityResponseDtoModel
        {
            public string Day { get; set; }
            public string Activity { get; set; }
            public decimal Value { get; set; }
        }

        public class SalesActivityResponseDto
        {
            public List<SalesActivityResponseDtoModel> Meeting { get; set; }
            public List<SalesActivityResponseDtoModel> Conversion { get; set; }
            public List<SalesActivityResponseDtoModel> Call { get; set; }
            public List<SalesActivityResponseDtoModel> Email { get; set; }
        }

        public class SalesActivityParams
        {
            public string UserId { get; set; }
            public string StartDate { get; set; }
            public string EndDate { get; set; }
        }
    }
}
