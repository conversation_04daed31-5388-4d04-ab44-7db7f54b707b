﻿using AutoMapper;
using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.ViewModel;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.RabbitMQ;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using WatchDog;
using static Jobid.App.RabbitMQ.Records;

namespace Calendar.Service.RabbitMQ.Consumers
{
    public class CreateTodoConsumer : BackgroundService
    {
        private readonly IMapper _mapper;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private Serilog.ILogger _logger = Log.ForContext<CreateTodoConsumer>();
        private readonly Dictionary<string, object> args;
        private readonly ITodoServices _todoService;

        public CreateTodoConsumer(IMapper mapper, IServiceScopeFactory serviceScopeFactory)
        {
            _mapper = mapper;
            _serviceScopeFactory = serviceScopeFactory;

            // Get the generic consumer from the service scope factory
            using var scope = serviceScopeFactory.CreateScope();
            _todoService = scope.ServiceProvider.GetRequiredService<ITodoServices>();

            // Set how long the message should live on the queue i.e ttl
            var expiration = **********; // 100 hours
            args = new Dictionary<string, object>
            {
                {"x-message-ttl", expiration}
            };
        }

        public async virtual Task StartConsumer(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                stoppingToken.ThrowIfCancellationRequested();
                var model = new ConsumeModel(RabbitMQConstants.AICreateTodoQueue, RabbitMQConstants.AICreateTodoEvent, "", ExchangeType.Fanout);

                using var scope = _serviceScopeFactory.CreateScope();
                var rabbitMQConnectionService = scope.ServiceProvider.GetRequiredService<IRabbitMQConnectionService>();
                var channel = rabbitMQConnectionService._channel;

                if (channel is not null)
                {
                    channel.QueueDeclare(queue: model.QueueName, durable: true, exclusive: false, autoDelete: false, arguments: args);
                    channel.QueueBind(queue: model.QueueName, exchange: model.ExchangeName, routingKey: "");
                    //channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);
                    var consumer = new AsyncEventingBasicConsumer(channel);

                    consumer.Received += async (ch, ea) =>
                    {
                        var message = Encoding.UTF8.GetString(ea.Body.ToArray());
                        await CreateTodo(message);

                        _logger.Information($"{model.QueueName} message received: {message}");
                        WatchLogger.Log($"{model.QueueName} message received: {message}");
                        //channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                    };

                    channel.BasicConsume(queue: model.QueueName, autoAck: true, consumer: consumer);
                    await Task.Delay(100);
                }
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Run(() => StartConsumer(stoppingToken));
        }

        private async Task CreateTodo(string message)
        {
            var todoPayload = JsonConvert.DeserializeObject<CreateTodoDto>(message);
            if (todoPayload != null)
            {
                var response = await _todoService.AddProjectMgmt_Todo(todoPayload);
                if (response == null)
                    _logger.Error("CreateTodoConsumer: Todo creation failed");
            }
        }
    }
}
