﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class UserIdCalenderId
    {
        [Key]
        public Guid Id { get; set; }
        public string UserId { get; set; }
        public string CalenderId { get; set; }
        public string SubsequentMeetingId { get; set; }
        public string Email { get; set; }
        public int NotifyMeInMinutes { get; set; } = 10;
        public InviteResponse InviteResponse { get; set; } = InviteResponse.Pending;
    }

    public enum InviteResponse
    {
        Pending = 1,
        No,
        Yes,
        Maybe,
        Canceled
    }
}
