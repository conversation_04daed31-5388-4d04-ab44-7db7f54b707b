﻿using Jobid.App.Helpers.Enums;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TimeSheetTodoDto
    {
        public Guid Id { get; set; }
        public string TodoId { get; set; }
        public string TodoName { get; set; }
        public string SprintId { get; set; }
        public string SprintName { get; set; }
        public string TodoDescription { get; set; }
        public string TodoSummary { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdate { get; set; }
        public DateTime? DueDate { get; set; }
        public string Priority { get; set; }
        public string TodoStatus { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string ExpectedHours { get; set; }
        public string WillBeDueIn { get; set; }
        public string Comment { get; set; }
        public string ExistingTodoLink { get; set; }
        public string ActualHours { get; set; }
        public string RegisteredHours { get; set; }
        public string TimeLeft { get; set; }
        public bool IsBillable { get; set; }
        public decimal? AmountPerHour { get; set; }
        public List<UserDto> AssignedTo { get; set; } = new List<UserDto>();
        public string ProjectName { get; set; }
        public decimal TodoValue { get; set; }
        public List<string> Tags { get; set; } = new List<string>();
        public bool LockTodo { get; set; }
        public ApprovalStatus ApprovalStatus { get; set; }
        public List<TodoCommentDto> Comments { get; set; } = new List<TodoCommentDto>();
        public DateTime? CompletionDate { get; set; }
    }
}
