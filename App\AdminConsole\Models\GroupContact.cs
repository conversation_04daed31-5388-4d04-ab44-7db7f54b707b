using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    /// <summary>
    /// Junction table for Group and Contact many-to-many relationship
    /// </summary>
    public class GroupContact
    {
        public GroupContact()
        {
            Id = Guid.NewGuid();
        }

        [Key]
        public Guid Id { get; set; }

        /// <summary>
        /// Foreign key to ContactGroup
        /// </summary>
        [Required]
        public Guid GroupId { get; set; }

        /// <summary>
        /// Foreign key to UserContact
        /// </summary>
        [Required]
        public Guid ContactId { get; set; }

        /// <summary>
        /// Navigation property to ContactGroup
        /// </summary>
        [ForeignKey(nameof(GroupId))]
        public virtual Group Group { get; set; }

        /// <summary>
        /// Navigation property to UserContact
        /// </summary>
        [ForeignKey(nameof(ContactId))]
        public virtual UserContact Contact { get; set; }

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created this record
        /// </summary>
        [StringLength(450)]
        public string CreatedBy { get; set; }
    }
}
