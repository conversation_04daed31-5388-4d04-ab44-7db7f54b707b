﻿using Jobid.App.Subscription.Models;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Helpers.Models
{
    public class PricingAndFeature
    {
        public Guid Id { get; set; }
        public Guid PricingPlanId { get; set; }
        public Guid FeatureId { get; set; }
        public bool IsLimited { get; set; }
        public string LimitedTo { get; set; }
        public string Category { get; set; }
        public string DurationType { get; set; }
        public string Summary { get; set; }
        public string Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Feature Feature { get; set; }
        public PricingPlan PricingPlan { get; set; }

        [NotMapped]
        public PackagePricing PackagePricing { get; set; }
    }
}
