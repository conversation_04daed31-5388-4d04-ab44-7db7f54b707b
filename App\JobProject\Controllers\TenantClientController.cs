﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Calender.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.JobProject.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TenantClientController : ControllerBase
    {
        public IUnitofwork Services_Repo { get; }
        public TenantClientController(IUnitofwork unitofwork)
        {
            this.Services_Repo = unitofwork;
        }

        #region Get Tenant Clients
        /// <summary>
        /// Get Tenant Clients
        /// </summary>
        /// 
        /// <returns></returns>

        [HttpGet()]
        public async Task<IActionResult> GetClients()
        {
            try
            {


                var result = await Services_Repo.TenantClientService.GetTenantClients();

                if (result != null)
                {
                    return Ok(new ApiResponse<List<TenantClient>>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Tenant Clients.",
                        Data = result
                    });

                }
                else
                {
                    return BadRequest(new ApiResponse<List<TenantClient>>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Failed to get TenantClients.",
                        Data = result
                    });
                }

            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<List<TenantClient>> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "Failed to get TenantClients." });
            }
            
        }
        #endregion
    }
}
