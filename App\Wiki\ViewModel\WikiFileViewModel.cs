using Jobid.App.Wiki.Enums;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Wiki.ViewModel
{
    public class WikiFileUploadDto
    {
        public IFormFile File { get; set; }
        public string Description { get; set; }
        public List<Guid> DepartmentIds { get; set; } = new List<Guid>();
    }

    public class WikiFileUpdateDto
    {
        public Guid FileId { get; set; }
        public string Description { get; set; }
        public WikiFileType WikiFileType { get; set; }
    }

    public class WikiFileDepartmentAccessUpdateDto
    {
        [Required(ErrorMessage = "BatchId is required")]
        public string BatchId { get; set; }
        public List<WikiDepartmentAccessDto> WikiDepartmentAccess { get; set; } = new List<WikiDepartmentAccessDto>();
    }

    public class WikiDepartmentAccessDto
    {
        public Guid DepartmentId { get; set; }
        public bool IsActive { get; set; }
    }

    public class WikiFileDto
    {
        public Guid Id { get; set; }
        public string FileName { get; set; }
        public string FileType { get; set; }
        public long? FileSize { get; set; }
        public string FileSizeFormatted { get; set; }
        public WikiFileUploadStatus UploadStatus { get; set; }
        public DateTime UploadedDate { get; set; }
        public string UploadedBy { get; set; }
        public Guid UploadedById { get; set; }
        public string Description { get; set; }
        public DateTime? LastAccessedDate { get; set; }
        public string PresignedUrl { get; set; }
        public string BatchId { get; set; }
        public WikiFileType WikiFileType { get; set; }
        public string TextContent { get; set; }
        public List<WikiFileDepartmentAccessDto> DepartmentAccess { get; set; } = new List<WikiFileDepartmentAccessDto>();
    }

    public class WikiFileDepartmentAccessDto
    {
        public Guid DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public bool IsActive { get; set; }
    }

    public class WikiFileFilterDto
    {
        public string SearchTerm { get; set; }
        public List<Guid> DepartmentIds { get; set; } = new List<Guid>();
        public List<WikiFileUploadStatus> UploadStatuses { get; set; } = new List<WikiFileUploadStatus>();
        public DateTime? UploadedFrom { get; set; }
        public DateTime? UploadedTo { get; set; }
        public bool IncludeDeleted { get; set; } = false;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public bool GroupByBatch { get; set; } = false;
        public List<WikiFileType> FileTypes { get; set; } = new List<WikiFileType>();
    }

    public class WikiFileListResponse
    {
        public List<WikiFileDto> Files { get; set; } = new List<WikiFileDto>();
        public PaginationMetadata Pagination { get; set; }
    }

    public class WikiFileBatchResponse
    {
        public string BatchId { get; set; }
        public WikiFileType WikiFileType { get; set; }
        public List<WikiFileDto> Files { get; set; } = new List<WikiFileDto>();
        
        /// <summary>
        /// Optional text content that can be included alongside file uploads or as standalone content
        /// </summary>
        public string TextContent { get; set; }
        
        /// <summary>
        /// Reference to the file ID for text-only content when no actual files are uploaded
        /// This is used for tracking department access for text-only content
        /// </summary>
        public Guid? TextContentFileId { get; set; }
    }

    public class PaginationMetadata
    {
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public bool HasPrevious => CurrentPage > 1;
        public bool HasNext => CurrentPage < TotalPages;
    }
}
