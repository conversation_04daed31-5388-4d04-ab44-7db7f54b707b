﻿using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProject.ViewModel
{
    public class SprintDFExportReportSummaryDto
    {
        public string SprintName { get; set; }
        public string Status { get; set; }
        public string TotalTimeTracked { get; set; }
        public string Duration { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<TodoDFExportReportSummaryDto> TodoSummary { get; set; } = new List<TodoDFExportReportSummaryDto>();
    }
}
