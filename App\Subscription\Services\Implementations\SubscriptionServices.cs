#region Using Statements
using AutoMapper;
using Hangfire;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.Calender.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.HelperFiles;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Jobid.App.RabbitMQ;
using Jobid.App.Subscription.Configuration;
using Jobid.App.Subscription.Enums;
using Jobid.App.Subscription.Models;
using Jobid.App.Subscription.Services.Contract;
using Jobid.App.Subscription.ViewModels;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.Model;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Tenant.ViewModel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Models.Security;
using Mollie.Api.Client;
using Mollie.Api.Client.Abstract;
using Mollie.Api.Models;
using Mollie.Api.Models.Customer;
using Mollie.Api.Models.List;
using Mollie.Api.Models.Mandate;
using Mollie.Api.Models.Payment;
using Mollie.Api.Models.Payment.Request;
using Mollie.Api.Models.Payment.Response;
using Mollie.Api.Models.Subscription;
using RabbitMQ.Client;
using Serilog;
using Stripe;
using Stripe.Checkout;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using WatchDog;
using static Jobid.App.Helpers.Utils.Utility;
using static Jobid.App.RabbitMQ.Records;
using static Jobid.App.Subscription.Enums.Enums;
using ILogger = Serilog.ILogger;
using PaymentStatus = Jobid.App.Subscription.Enums.Enums.PaymentStatus;
using SubscriptionStatus = Jobid.App.Subscription.Enums.Enums.SubscriptionStatus;
#endregion

namespace Jobid.App.Subscription.Services.Implementations
{
    public class SubscriptionServices : ISubscriptionServices
    {
        #region Properties And Constructor
        private readonly JobProDbContext _publicContext;
        private readonly ITenantService _tenantService;
        private readonly IPaymentClient _paymentClient;
        private readonly ICustomerClient _customerClient;
        private readonly ISubscriptionClient _subscriptionClient;
        private readonly string _conString;
        private readonly IMemoryCache _cache;
        private readonly IPublisherService _publisherService;
        private readonly IBackgroundJobService _backgroundJobService;
        private readonly IApiCallService _apiCallService;
        private readonly IPdfService _pdfService;
        private readonly JobProDbContext _context;
        private readonly IConfiguration _config;
        private readonly IMandateClient _mandateClient;
        private readonly IMapper _mapper;
        private readonly IEmailService _emailService;
        private readonly IWebHostEnvironment _environment;
        private readonly IOptions<StripeOptions> _options;
        private ILogger _logger = Log.ForContext<SubscriptionServices>();

        public SubscriptionServices(JobProDbContext publicContext, ITenantService tenantService, IPaymentClient paymentClient, ICustomerClient customerClient, ISubscriptionClient subscriptionClient, IConfiguration config, IMandateClient mandateClient, IMapper mapper, JobProDbContext context, IEmailService emailService, IWebHostEnvironment environment, IOptions<StripeOptions> options, IMemoryCache cache, IPublisherService publisherService, IBackgroundJobService backgroundJobService, IApiCallService apiCallService, IPdfService pdfService)
        {
            _publicContext = publicContext;
            _tenantService = tenantService;
            _paymentClient = paymentClient;
            _customerClient = customerClient;
            _subscriptionClient = subscriptionClient;
            _config = config;
            _mandateClient = mandateClient;
            _mapper = mapper;
            _emailService = emailService;
            _environment = environment;
            _options = options;
            StripeConfiguration.ApiKey = options.Value.SecretKey;
            _conString = GlobalVariables.ConnectionString;
            _cache = cache;
            _publisherService = publisherService;
            _backgroundJobService = backgroundJobService;
            _apiCallService = apiCallService;
            _pdfService = pdfService;
            _context = context;
        }
        #endregion

        #region Get Subscription Plans
        /// <summary>
        /// This method is used to get all subscription plans/or plans for a specific application
        /// </summary>
        /// <param name="application"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetSubscriptionPlans(Applications? application, string subdomain)
        {
            SubscriptionRegions region = await GetSubRegion(subdomain);

            var plans = new List<SubscriptionPlansDto>();
            if (application == null)
            {
                plans = await _publicContext.PackagePricing
                    .Include(x => x.PricingPlan)
                    .Select(res => new SubscriptionPlansDto
                    {
                        Id = res.PricingPlan.Id.ToString(),
                        Name = res.PricingPlan.Name,
                        Currency = res.Currency,
                        Amount = res.PricePerMonth,
                        Application = res.Application,
                    }).ToListAsync();
            }
            else
            {
                plans = await _publicContext.PackagePricing
                   .Include(x => x.PricingPlan)
                   .Where(x => x.Application == application.ToString() && x.Region == region)
                   .Select(res => new SubscriptionPlansDto
                   {
                       Id = res.PricingPlan.Id.ToString(),
                       Name = res.PricingPlan.Name,
                       Currency = res.Currency,
                       Amount = res.PricePerMonth,
                       Application = res.Application,
                   }).ToListAsync();
            }

            if (application == Applications.Joble)
            {

            }

            return new GenericResponse
            {
                Data = plans,
                ResponseMessage = "Subscription plans fetched successfully",
                ResponseCode = "200"
            };
        }

        private async Task<SubscriptionRegions> GetSubRegion(string subdomain)
        {
            // Write list of all european countries
            var europeCountryList = new List<string>
            {
                "Albania", "Andorra", "Armenia", "Austria", "Azerbaijan", "Belarus", "Belgium", "Bosnia and Herzegovina",
                "Bulgaria", "Croatia", "Cyprus", "Czech Republic", "Denmark", "Estonia", "Finland", "France",
                "Georgia", "Germany", "Greece", "Hungary", "Iceland", "Ireland", "Italy", "Kazakhstan",
                "Kosovo", "Latvia", "Liechtenstein", "Lithuania", "Luxembourg", "Malta", "Moldova",
                "Monaco", "Montenegro", "Netherlands", "North Macedonia (FYROM)", "Norway",
                "Poland", "Portugal", "Romania", "Russia (partly in Europe)", 
                // Add more countries as needed
            };

            // Get company/tenant details
            var region = SubscriptionRegions.Europe;
            var comapnyDetails = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain.ToLower());
            if (comapnyDetails == null && subdomain != "api")
                throw new RecordNotFoundException($"Company with subdomain: {subdomain} not found");

            // Get admin phone number
            var phone = "";
            string foundCountry = null;
            if (comapnyDetails != null)
            {
                // First get billing info
                var billingInfo = await _publicContext.BillingInformations.FirstOrDefaultAsync(x => x.TenantId == comapnyDetails.Id);
                if (billingInfo != null)
                    foundCountry = billingInfo.Country;
                else
                {
                    phone = await _publicContext.Users
                        .Where(x => x.Id == comapnyDetails.AdminId)
                        .Select(x => x.PhoneNumber)
                        .FirstOrDefaultAsync();
                }
            }

            if (!string.IsNullOrEmpty(phone) && string.IsNullOrEmpty(foundCountry))
            {
                // Try to match the longest country code first                          
                var match = CountryCodeMap.CodeToCountry.Keys.OrderByDescending(k => k.Length)
                    .FirstOrDefault(code => phone.StartsWith(code));

                if (match != null)
                {
                    foundCountry = CountryCodeMap.CodeToCountry[match];
                }
            }

            if (foundCountry != null && foundCountry != "Nigeria" && !europeCountryList.Contains(foundCountry))
                region = SubscriptionRegions.RestOfTheWorld;
            else if (foundCountry != null && foundCountry == "Nigeria")
                region = SubscriptionRegions.Nigeria;
            return region;
        }
        #endregion

        #region Get AI Subscription Plans
        public async Task<GenericResponse> GetAISubscriptionPlans()
        {
            var plans = await _publicContext.AIAgents
                .ToListAsync();

            return new GenericResponse
            {
                Data = plans,
                ResponseMessage = "AI subscription plans fetched successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Get Subscription Details
        public async Task<GenericResponse> GetSubscriptionPlanDetails(Applications? app, string subdomain)
        {
            SubscriptionRegions? region = null;
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "api")
            {
                var company = await _tenantService.GetTenantBySubdomain(subdomain);
                if (company is null)
                    throw new RecordNotFoundException($"Company with subdomain: {subdomain} not found");

                var regionRes = await GetRegionAsEnum(company.Country, company.Id);
                if (regionRes.ResponseCode != "200")
                    return regionRes;

                region = regionRes.Data as SubscriptionRegions?;
            }

            var subscriptionDetails = await _publicContext.PricingAndFeatures
                .Include(x => x.PricingPlan)
                .Include(x => x.Feature)
                .ToListAsync();

            foreach (var item in subscriptionDetails)
            {
                if (region != null)
                {
                    item.PackagePricing = await _publicContext.PackagePricing
                        .Include(x => x.PricingPlan)
                        .FirstOrDefaultAsync(x => x.PricingPlanId == item.PricingPlanId && x.Region == region);
                }
                else
                {
                    item.PackagePricing = await _publicContext.PackagePricing
                        .Include(x => x.PricingPlan)
                        .FirstOrDefaultAsync(x => x.PricingPlanId == item.PricingPlanId);
                }
            }

            if (app is not null)
            {
                subscriptionDetails = subscriptionDetails.Where(sub => sub.PricingPlan.Application == app).ToList();
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Subscription details fetched successfully",
                Data = subscriptionDetails.Distinct()
            };
        }
        #endregion

        #region Get plan Price Details
        public async Task<GenericResponse> GetPlanPriceDetails(Applications? app, string subdomain)
        {
            List<PackagePricing> packagePricings = new List<PackagePricing>();
            SubscriptionRegions region = await GetSubRegion(subdomain);

            packagePricings = await _publicContext.PackagePricing
                    .Include(x => x.PricingPlan)
                    .Where(x => x.Region == region)
                    .ToListAsync();

            if (app is not null)
            {
                packagePricings = packagePricings.Where(sub => sub.Application == app.ToString()).ToList();
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Plan details fetched successfully",
                Data = packagePricings
            };
        }
        #endregion

        #region Get company Subscritpion Status
        public async Task<GenericResponse> GetCompanySubscriptionStatus(string subdomain, Applications application)
        {
            var freeTrialExpired = false;
            var status = SubscriptionStatus.Inactive;

            var company = await _tenantService.GetTenantBySubdomain(subdomain);
            if (company is null)
                throw new RecordNotFoundException($"Company with subdomain: {subdomain} not found");

            var subscription = await _publicContext.CompanySubscriptions
                .FirstOrDefaultAsync(x => x.TenantId == company.Id && x.Application == application);

            if (subscription is null)
            {
                // Check if free trial has expired. That is, if from that date the company was created has passed 14 days
                var dateCreated = company.DateCreated;
                var dateNow = DateTime.UtcNow;
                var days = (dateNow - dateCreated).TotalDays;
                if (days > 14)
                    freeTrialExpired = true;
                else
                    status = SubscriptionStatus.Active;
            }
            else
            {
                if (subscription.Status == SubscriptionStatus.Active)
                    status = SubscriptionStatus.Active;
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = new CompanySubscriptionStatusDto
                {
                    FreeTrialExpired = freeTrialExpired,
                    Status = status
                }
            };
        }
        #endregion

        #region Send Email Notification To Company Admin For Free Trial Expiry
        public async Task<GenericResponse> SendEmailNotificationToCompanyAdminForFreeTrialExpiry(string subdomain, Applications application)
        {
            var company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);
            if (company is null)
                throw new RecordNotFoundException($"Company with subdomain: {subdomain} not found");

            var superAdmin = await _publicContext.Users.FirstOrDefaultAsync(x => x.Id == company.AdminId);

            var subscription = await _publicContext.CompanySubscriptions
                .FirstOrDefaultAsync(x => x.TenantId == company.Id && x.Application == application);

            // Get billing information if it exists
            var billingInfo = await _publicContext.BillingInformations
                .FirstOrDefaultAsync(x => x.TenantId == company.Id);

            if (subscription is null)
            {
                var mailDto = new SendTrialEndedMailDto
                {
                    TenantId = company.Id.ToString()
                };

                // Use billing information if it exists, otherwise use super admin details
                if (billingInfo != null)
                {
                    mailDto.Email = billingInfo.Email;
                    mailDto.Name = $"{billingInfo.FirstName} {billingInfo.LastName}";
                }
                else
                {
                    mailDto.Email = superAdmin.Email;
                    mailDto.Name = superAdmin.FirstName;
                }

                var res = await _tenantService.SendFreeTrialEndNotification(mailDto);

                if (!res)
                {
                    return new GenericResponse
                    {
                        DevResponseMessage = "Failed to send email notification to company admin",
                        ResponseCode = "400",
                    };
                }
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Email notification sent successfully",
            };
        }
        #endregion

        #region Create Subscription
        /// <summary>
        /// This method is used to create subscription/upgrade an account
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<GenericResponse> CreateSubscription(SubscribeDto model)
        {
            if (model.Subdomain == "api")
                throw new RecordNotFoundException("Subdomain cannot be api");

            var user = await _publicContext.Users.FirstOrDefaultAsync(x => x.Id == model.UserId);
            if (user is null)
                throw new RecordNotFoundException("User not found");

            // Get the plan using plan Id
            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id.ToString() == model.PlanId);
            if (plan == null)
                throw new RecordNotFoundException("Plan does not exist");

            if (plan.Name == SubscriptionPlans.AIPackages.ToString())
            {
                model.IsAISubscription = true;
                if (model.AIAgentDetails == null)
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Incomplete or bad request. Try again later",
                        DevResponseMessage = "IsAISubscription property is required"
                    };
            }

            foreach (var ai in model.AIUserIds)
            {
                if (ai.Value.Count() > model.AIAgentDetails[ai.Key])
                    throw new InvalidOperationException($"Number of users for {ai.Key} cannot be greater than number of liecense {model.AIAgentDetails[ai.Key]} you intend to pay for.");
            }

            await using var _context = new JobProDbContext(_conString, new DbContextSchema(model.Subdomain));

            Enum.TryParse(string.Join("", plan.Name), out SubscriptionPlans planName);
            // Ensure that the planName is an instance of SubscriptionPlans
            if (!Enum.IsDefined(typeof(SubscriptionPlans), planName))
                throw new InvalidOperationException("Invalid plan name");

            Models.Subscription subscription = null;
            string tenantId = null;
            var res = 0;
            var paymentLink = string.Empty;
            var numberOfUsersToSubFor = 1;
            var hasExistingSubscription = false;
            PaymentResponse paymentRes = null;
            PackagePricing planDetails = null;
            SubscriptionCreateResponse subResponse = new SubscriptionCreateResponse();
            Tenant.Model.Tenant company = null;

            if (model.Subdomain != "api" && model.Subdomain != null)
            {
                // Get tenantId/CompanyId using subdomain
                tenantId = (await _tenantService.GetTenantBySubdomain(model.Subdomain)).Id.ToString();
                subscription = await _publicContext.Subscriptions
                    .FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == model.Application);
            }
            else
            {
                subscription = await _publicContext.Subscriptions
                    .FirstOrDefaultAsync(x => x.UserId == model.UserId && x.Application == model.Application);
            }

            company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenantId);

            numberOfUsersToSubFor = model.NumberOfUsers.Value;
            if (subscription is null)
            {
                subscription = new Models.Subscription();
                subscription.PricingPlanId = plan.Id;
                subscription.CreatedAt = DateTime.UtcNow;
                subscription.UpdatedAt = DateTime.UtcNow;
                subscription.UserId = model.UserId;
                subscription.Currency = model.Currency;
                subscription.Application = model.Application;
                subscription.SubscriptionFor = numberOfUsersToSubFor;
                subscription.PaymentMethod = model.Method.ToString();
                subscription.PaymentProvider = model.PaymentProvider;
                subscription.Interval = model.Interval.ToString();
            }
            else
            {
                // Check if the user has an active subscription
                if (subscription.Status == SubscriptionStatus.Active.ToString() && subscription.ExpiresOn > DateTime.UtcNow)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "201",
                        ResponseMessage = "Your subscription is still active",
                    };
                }

                hasExistingSubscription = true;
                subscription.UserId = model.UserId;
                subscription.PricingPlanId = plan.Id;
                subscription.UserId = model.UserId;
                subscription.UpdatedAt = DateTime.UtcNow;
                subscription.Currency = model.Currency;
                subscription.SubscriptionFor = numberOfUsersToSubFor;
                subscription.Interval = model.Interval.ToString();
                subscription.PaymentProvider = model.PaymentProvider;
                subscription.PaymentMethod = model.Method.ToString();
            }

            switch (planName)
            {
                case SubscriptionPlans.FullAccess:
                case SubscriptionPlans.AIPackages:

                    // Check if the number of selected users plus the user making the subscription is more than the number of liencese
                    if (model.UserIds.Count + 1 > numberOfUsersToSubFor && !model.IsAISubscription)
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"Number of liencenses {numberOfUsersToSubFor} cannot be less than the number of " +
                            $"selected users - {model.UserIds.Count + 1}",
                        };
                    }

                    if (model.UserIds.Any())
                    {
                        var validationRes = await ValidateUserIds(model.UserIds, _context, model.Application, false);
                        if (validationRes.ResponseCode != "200")
                            return validationRes;
                    }

                    // If the company's country is Nigeria, only paypal option is available for mollie. Stripe is available for all countries
                    if (model.PaymentProvider == PaymentProviders.Mollie)
                    {
                        if (company.Country.ToLower() == SubscriptionRegions.Nigeria.ToString().ToLower())
                        {
                            if (model.Method == PaymentMethods.Card)
                                return new GenericResponse
                                {
                                    ResponseCode = "400",
                                    ResponseMessage = "Card payment is not available for Nigeria. Please select PayPal or Use Stripe payment option",
                                };
                        }
                    }

                    if (model.Method == PaymentMethods.PayPal)
                    {
                        // Check that paypalemal and paypalbillingAgreementId is not empty
                        if (string.IsNullOrEmpty(model.PayPalEmail) || string.IsNullOrEmpty(model.PaypalBillingAgreementId))
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = "PayPal email and billing agreement id is required",
                            };

                        // Check if paypalbillingAgreementId format is valid
                        if (!model.PaypalBillingAgreementId.IsPayPalBillingAgreementFormatValid())
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = "Invalid billing agreement id",
                            };

                        subscription.PayPalEmail = model.PayPalEmail;
                        subscription.PaypalBillingAgreementId = model.PaypalBillingAgreementId;
                    }

                    if (model.Method == PaymentMethods.Card)
                        subscription.ConsumerAccount = model.ConsumerAccount;

                    var billingAddress = _mapper.Map<BillingAddress>(model.BillingAddress);
                    await _publicContext.BillingAddresses.AddAsync(billingAddress);
                    subscription.BillingAddressId = billingAddress.Id;
                    subscription.TenantId = Guid.Parse(tenantId);

                    // Get the amount to be paid for the plan
                    if (!model.IsAISubscription)
                    {
                        var region = await GetSubRegion(company.Subdomain);
                        planDetails = await _publicContext.PackagePricing
                            .FirstOrDefaultAsync(x => x.PricingPlanId == plan.Id && x.Region == region);

                        if (model.Interval == SubscriptionInterval.Monthly)
                            subscription.Amount = planDetails.PricePerMonth.Value * numberOfUsersToSubFor + model.VAT;
                        else
                            subscription.Amount = planDetails.PricePerMonthForYearlyOption.Value * numberOfUsersToSubFor * 12 + model.VAT;

                        // Convert planDetails.Current to current enum
                        if (!Enum.TryParse(planDetails.Currency, out Helpers.Enums.Currency currency))
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = "Invalid region",
                            };
                        subscription.Currency = currency;
                    }
                    else
                    {
                        subscription.IsAISubscription = true;

                        // Check if AIAgentDetails is not empty
                        if (model.AIAgentDetails.Count < 1)
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = "Incomplate request. AIAgentDetails cannot be empty",
                                Data = null
                            };

                        // Get the amount to be paid for the plan
                        var aiAgents = await _publicContext.AIAgents.ToListAsync();
                        var totalAmount = 0.0;
                        var totalUsers = 0;
                        foreach (var agent in model.AIAgentDetails)
                        {
                            if (!Enum.TryParse(agent.Key.ToString(), out AIAgents agentId))
                            {
                                return new GenericResponse
                                {
                                    ResponseCode = "400",
                                    ResponseMessage = "Invalid agent id",
                                    Data = null
                                };
                            };

                            var agentDetails = aiAgents.FirstOrDefault(x => x.Agent == agentId.ToString());
                            if (agentDetails is null)
                                return new GenericResponse
                                {
                                    ResponseCode = "400",
                                    ResponseMessage = "Invalid agent id",
                                    Data = null
                                };

                            var amount = model.Interval == SubscriptionInterval.Monthly ? agentDetails.AmountPerMonthPerUser : agentDetails.AmountPerYearPerUser;
                            totalAmount += amount * Convert.ToInt32(agent.Value);
                            totalUsers += Convert.ToInt32(agent.Value);

                            var existingSubDetails = await _publicContext.AISubscriptionDetails
                                .Where(x => x.SubscriptionId == subscription.Id && x.Agent == agentId).FirstOrDefaultAsync();
                            if (existingSubDetails != null)
                            {
                                existingSubDetails.NoOfUserSubscribedFor = Convert.ToInt32(model.AIAgentDetails[AIAgents.Britney]);
                                existingSubDetails.Amount = aiAgents.FirstOrDefault(x => x.Agent == AIAgents.Britney.ToString()).AmountPerMonthPerUser;

                                _publicContext.AISubscriptionDetails.Update(existingSubDetails);
                            }
                            else
                            {
                                _publicContext.AISubscriptionDetails.Add(new AISubscriptionDetail
                                {
                                    SubscriptionId = subscription.Id,
                                    Agent = agentId,
                                    NoOfUserSubscribedFor = Convert.ToInt32(model.AIAgentDetails[AIAgents.Britney]),
                                    Amount = aiAgents.FirstOrDefault(x => x.Agent == AIAgents.Britney.ToString()).AmountPerMonthPerUser
                                });
                            }
                        }

                        if (totalUsers > numberOfUsersToSubFor)
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = $"Number of liencenses (No of Users: {numberOfUsersToSubFor}) cannot be less than the number of " +
                                $"selected users - {totalUsers}",
                                Data = null
                            };

                        subscription.Amount = totalAmount + model.VAT;
                    }

                    subscription.Status = PaymentStatus.Pending.ToString();
                    subscription.PricingPlanId = plan.Id;
                    subscription.PaymentMethod = model.Method.ToString();
                    subscription.FreeTrialOptionSelected = model.FreeTrial;

                    // Create the customer on mollie, create first payment and return the payment link
                    // Get the customer Id from mollie
                    if (!string.IsNullOrEmpty(subscription.StripeCustomerId) || !string.IsNullOrEmpty(subscription.MollieCustomerId))
                    {
                        if (model.PaymentProvider == PaymentProviders.Mollie)
                        {
                            try
                            {
                                if (!string.IsNullOrEmpty(subscription.MollieCustomerId))
                                {
                                    var customer = await _customerClient.GetCustomerAsync(subscription.MollieCustomerId);
                                    if (customer is not null)
                                        subscription.MollieCustomerId = customer.Id;
                                }
                                else
                                {
                                    if (tenantId is null)
                                        subscription.MollieCustomerId = await CreateMollieCustomer(user);
                                    else
                                        subscription.MollieCustomerId = await CreateMollieCustomer(user, tenantId);
                                }
                            }
                            catch (MollieApiException ex)
                            {
                                throw ex;
                            }
                        }
                        else
                        {
                            // Get stripe customer
                            var customerService = new CustomerService();
                            try
                            {
                                if (!string.IsNullOrEmpty(subscription.StripeCustomerId))
                                {
                                    var customer = await customerService.GetAsync(subscription.StripeCustomerId);
                                    if (customer is not null)
                                        subscription.StripeCustomerId = customer.Id;
                                }
                                else
                                {
                                    if (tenantId is null)
                                        subscription.StripeCustomerId = await CreateStripeCustomer(user);
                                    else
                                        subscription.StripeCustomerId = await CreateStripeCustomer(user, tenantId);
                                }
                            }
                            catch (StripeException ex)
                            {
                                throw ex;
                            };
                        }
                    }
                    else
                    {
                        if (model.PaymentProvider == PaymentProviders.Mollie)
                        {
                            if (tenantId is null)
                                subscription.MollieCustomerId = await CreateMollieCustomer(user);
                            else
                                subscription.MollieCustomerId = await CreateMollieCustomer(user, tenantId);
                        }
                        else
                        {
                            if (tenantId is null)
                                subscription.StripeCustomerId = await CreateStripeCustomer(user);
                            else
                                subscription.StripeCustomerId = await CreateStripeCustomer(user, tenantId);
                        }

                    }

                    // Create the first payment
                    if (model.PaymentProvider == PaymentProviders.Mollie)
                    {
                        if (model.FreeTrial)
                        {
                            var amount = 0.5;
                            paymentRes = await CreatePaymentForCustomer(plan, amount, subscription.MollieCustomerId, model.Currency.ToString(), model.Method, model.FromClientAdmin);
                        }
                        else
                        {
                            paymentRes = await CreatePaymentForCustomer(plan, subscription.Amount, subscription.MollieCustomerId, model.Currency.ToString(), model.Method, model.FromClientAdmin);
                        }

                        subscription.PaymentId = paymentRes.Id;
                        paymentLink = paymentRes.Links.Checkout.Href;
                    }
                    else
                    {
                        // Create stripe subscription
                        if (model.FreeTrial)
                        {
                            // Create paymentIntent
                            var paymentIntentModel = new PaymentIntentCreationDto
                            {
                                Email = user.Email,
                                Currency = model.Currency.ToString(),
                                CustomerId = subscription.StripeCustomerId,
                            };
                            var paymentIntent = await CreatePaymentIntent(paymentIntentModel);
                            subResponse.ClientSecret = paymentIntent;
                        }
                        else
                        {
                            var productId = await CreateStripeProduct(planName.ToString());
                            var priceModel = new CreateStripePriceDto
                            {
                                ProductId = productId,
                                Currency = model.Currency,
                                Application = model.Application.ToString(),
                                Interval = model.Interval,
                                NumberOfUsersToSubFor = numberOfUsersToSubFor,
                                Plan = planName.ToString()
                            };

                            if (!model.IsAISubscription)
                                priceModel.Amount = model.Interval == SubscriptionInterval.Monthly ? (int)planDetails.PricePerMonth.Value : (int)planDetails.PricePerMonthForYearlyOption.Value;
                            else
                                priceModel.Amount = (int)subscription.Amount;

                            var priceId = await CreateStripePrice(priceModel);

                            var subdcriptionRequest = new CreateStripeSubscriptionDto
                            {
                                CustomerId = subscription.StripeCustomerId,
                                PriceId = priceId,
                                Currency = model.Currency.ToString(),
                                Application = model.Application.ToString(),
                                TenantId = tenantId,
                                FreeTrial = model.FreeTrial,
                                NumberOfUsersSubbedFor = numberOfUsersToSubFor
                            };
                            subResponse = await CreateStripeSubscription(subdcriptionRequest);
                            subscription.StripePriceId = priceId;
                            subscription.SubscriptionId = subResponse.SubscriptionId;
                        }
                    }

                    subscription.TransactionDate = DateTime.UtcNow;

                    if (hasExistingSubscription)
                        _publicContext.Subscriptions.Update(subscription);
                    else
                        await _publicContext.Subscriptions.AddAsync(subscription);

                    res = await _publicContext.SaveChangesAsync();
                    break;
                default:
                    break;
            }

            // Add permissions for the super admin of the company if the subscription is for a company
            if (tenantId is not null && !model.IsAISubscription)
            {
                company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenantId);
                var superAdminId = await _publicContext.Users
                    .Where(x => x.Id == company.AdminId).Select(x => x.Id)
                    .FirstOrDefaultAsync();

                model.UserIds.Add(superAdminId.ToString());
                foreach (var userId in model.UserIds)
                {
                    var permission = await _context.AppPermissions
                        .Where(x => x.UserId == userId && x.Application == model.Application.ToString())
                        .FirstOrDefaultAsync();

                    if (permission is null)
                    {
                        var appPermission = new AppPermissions(true)
                        {
                            UserId = userId,
                            Application = model.Application.ToString(),
                            IsEnabled = true,
                            SubscriptionStatus = SubscriptionStatus.Inactive.ToString(),
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            TenantId = tenantId
                        };

                        _context.AppPermissions.Add(appPermission);
                        var result = await _context.SaveChangesAsync();
                        if (result < 1)
                        {
                            return new GenericResponse
                            {
                                ResponseCode = "200",
                                ResponseMessage = "Subscription was activated successfully but an error occured while adding superadmin user to app permissions",
                                Data = res > 0 ? paymentLink : false,
                            };
                        }
                    }
                }
            }
            else
            {
                foreach (var ai in model.AIUserIds)
                {
                    foreach (var userId in ai.Value)
                    {
                        var permission = await _publicContext.AppPermissions
                           .Where(x => x.UserId == userId && x.Application == model.Application.ToString() && x.Agent == ai.Key && x.TenantId == tenantId)
                           .FirstOrDefaultAsync();
                        if (permission is null)
                        {
                            var appPermission = new AppPermissions(true)
                            {
                                UserId = user.Id,
                                Application = model.Application.ToString(),
                                IsEnabled = true,
                                SubscriptionStatus = SubscriptionStatus.Inactive.ToString(),
                                CreatedAt = DateTime.UtcNow,
                                UpdatedAt = DateTime.UtcNow,
                                Agent = ai.Key
                            };

                            _publicContext.AppPermissions.Add(appPermission);
                        }
                    }
                }

                await _publicContext.SaveChangesAsync();
            }

            if (model.PaymentProvider == PaymentProviders.Mollie)
                return new GenericResponse
                {
                    Data = res > 0 ? paymentLink : false,
                    ResponseMessage = res > 0 ? "Subscription created successfully" : "Subscription creation failed",
                    ResponseCode = res > 0 ? "200" : "400"
                };

            return new GenericResponse
            {
                Data = res > 0 ? subResponse : false,
                ResponseMessage = res > 0 ? "Subscription created successfully" : "Subscription creation failed",
                ResponseCode = res > 0 ? "200" : "400"
            };
        }
        #endregion

        #region Upgrade/Downgrade Subscription - Not In Use(Deprecated)
        public async Task<GenericResponse> UpgradeOrDowngradeSubscriptionPlan(UpdateSubscriptionDto model)
        {
            await using var _context = new JobProDbContext(_conString, new DbContextSchema(model.Subdomain));

            // Get the plan using plan Id
            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id.ToString() == model.PlanId);
            if (plan == null)
                throw new RecordNotFoundException("Plan Id does not exist");

            // Check if the user's current plan is the same as the plan he/she wants to upgrade/downgrade to
            var subscription = new Models.Subscription();
            string tenantId = null;
            var res = 0;
            var paymentLink = string.Empty;
            PackagePricing planDetails = null;
            var numberOfUsersToSubFor = model.NumberOfUsersToSubFor;
            if (model.Subdomain != "api" || model.Subdomain != null)
            {
                // Get tenantId/CompanyId using subdomain
                tenantId = (await _tenantService.GetTenantBySubdomain(model.Subdomain)).Id.ToString();
                subscription = await _publicContext.Subscriptions
                    .FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == model.Application);

                if (subscription is null)
                    throw new RecordNotFoundException($"No subscription found for {model.Application}");

                if (subscription.IsCancelled)
                    throw new InvalidOperationException("Subscription is already cancelled");

                // Get the max number of users for the selected plan
                if (model.Application != Applications.Echo)
                {
                    var maxNumberOfUsers = 0;
                    var pricingAndFeatures = await _publicContext.PricingAndFeatures
                        .Include(x => x.Feature)
                        .Where(x => x.PricingPlanId.ToString() == model.PlanId).ToListAsync();
                    foreach (var pricingAndFeature in pricingAndFeatures)
                    {
                        if (pricingAndFeature.Feature.FeatureName == CategoriesForFeatures.User.ToString() && pricingAndFeature.Feature.Application == model.Application.ToString())
                        {
                            if (pricingAndFeature.IsLimited)
                                maxNumberOfUsers = Convert.ToInt32(pricingAndFeature.LimitedTo);
                            break;
                        }
                    }

                    if (numberOfUsersToSubFor > maxNumberOfUsers)
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"You can only subscribe for {maxNumberOfUsers} users for the {plan.Name} plan. Select a higer plan if you wish to subscribe for more than {maxNumberOfUsers} users",
                            Data = null
                        };
                    }
                }
            }
            else
            {
                subscription = await _publicContext.Subscriptions
                     .FirstOrDefaultAsync(x => x.UserId.ToString() == model.UserId && x.Application == model.Application);
                if (subscription.IsCancelled)
                    throw new InvalidOperationException("Subscription is already cancelled");
            }

            if (subscription.PricingPlanId == plan.Id)
                throw new RecordAlreadyExistException("You are already on this plan");

            Enum.TryParse(string.Join("", plan.Name), out PricingPlans planName);

            // Get the amount to be paid for the plan
            planDetails = await _publicContext.PackagePricing
                .FirstOrDefaultAsync(x => x.PricingPlanId.ToString() == model.PlanId);
            subscription.Amount = model.Interval == SubscriptionInterval.Monthly ? (int)planDetails.PricePerMonth.Value * numberOfUsersToSubFor : (int)planDetails.PricePerMonthForYearlyOption.Value * 12 * numberOfUsersToSubFor;
            subscription.Status = PaymentStatus.Pending.ToString();
            subscription.SubscriptionFor = numberOfUsersToSubFor;
            subscription.Currency = model.Currency;
            subscription.Interval = model.Interval.ToString();

            // Update customer's subscription on mollie or stripe
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                var response = await UpdateSubscription(subscription, model.ApplyAtTheEndOfCurrentSub, model.PlanId);
                if (response is null)
                    throw new SubscriptionFailedException("Subscription update failed");

                subscription.Status = PaymentStatus.Successful.ToString();
            }
            else
            {
                var productId = await CreateStripeProduct(plan.Name);
                var priceDto = new CreateStripePriceDto
                {
                    ProductId = productId,
                    Application = model.Application.ToString(),
                    NumberOfUsersToSubFor = numberOfUsersToSubFor,
                    Amount = model.Interval == SubscriptionInterval.Monthly ? (int)planDetails.PricePerMonth.Value : (int)planDetails.PricePerMonthForYearlyOption.Value * 12,
                    Plan = planName.ToString(),
                    Interval = Enum.Parse<SubscriptionInterval>(subscription.Interval),
                };

                var priceId = await CreateStripePrice(priceDto);
                var response = await UpdateStripeSubscription(subscription, model.ApplyAtTheEndOfCurrentSub, priceId);
                if (!response)
                    throw new SubscriptionFailedException("Subscription update failed");
            }

            subscription.PricingPlanId = plan.Id;
            _publicContext.Update(subscription);
            res = await _publicContext.SaveChangesAsync();

            if (subscription.PaymentProvider == PaymentProviders.Stripe)
                return new GenericResponse
                {
                    Data = res > 0 ? true : false,
                    ResponseMessage = res > 0 ? "Subscription updated is being processed, you will be notified with status as soon processing is complete. " +
                    "This ususally takes less than a minute" : "Subscription update failed",
                    ResponseCode = res > 0 ? "200" : "400"
                };
            else
                return new GenericResponse
                {
                    Data = res > 0 ? true : false,
                    ResponseMessage = res > 0 ? "Subscription updated successfully. Please note that the updated price will be charged at the end of the current charge cycle. " +
                    "An email has been sent with detailed breakdown" : "Subscription update failed",
                    ResponseCode = res > 0 ? "200" : "400"
                };
        }
        #endregion

        #region Cancel Subscription
        /// <summary>
        /// Cancel subscription
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="subdomain"></param>
        /// <param name="applyAtTheEndOfCurrentSub"></param>
        /// <param name="isForEnterprisePlan"></param>
        /// <returns></returns>
        public async Task<GenericResponse> CancelSubscription(string subscriptionId, bool applyAtTheEndOfCurrentSub, string subdomain = null, bool isForEnterprisePlan = false)
        {
            var subscription = _publicContext.Subscriptions.FirstOrDefault(x => x.Id.ToString() == subscriptionId);
            if (subscription == null)
            {
                if (isForEnterprisePlan)
                    return new GenericResponse
                    {
                        ResponseMessage = "Subscription not found",
                        ResponseCode = "200",
                        Data = null
                    };
                else
                    return new GenericResponse
                    {
                        ResponseMessage = "Subscription not found, kindly cross check the supplied id",
                        ResponseCode = "400",
                        Data = null
                    };
            }

            // Delete the subscription on mollie or on stripe
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                // Get mollie subscription status
                var mollieSubscription = await _subscriptionClient.GetSubscriptionAsync(subscription.MollieCustomerId, subscription.SubscriptionId);
                if (mollieSubscription.Status == "active")
                    await _subscriptionClient.CancelSubscriptionAsync(subscription.MollieCustomerId, subscription.SubscriptionId);
            }
            else
            {
                var service = new SubscriptionService();
                var response = await CancelStripeSubscription(subscriptionId, applyAtTheEndOfCurrentSub);
                if (response != "canceled" && !applyAtTheEndOfCurrentSub)
                    return new GenericResponse
                    {
                        Data = false,
                        ResponseMessage = "Subscription could not be canceled at this time, please try again later",
                        ResponseCode = "500"
                    };
            }

            subscription.IsCancelled = true;
            _publicContext.Subscriptions.Update(subscription);

            if (subscription.TenantId is not null)
            {
                var companySubscription = await _publicContext.CompanySubscriptions.FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId.Value);
                companySubscription.Status = SubscriptionStatus.Cancelled;
                _publicContext.CompanySubscriptions.Update(companySubscription);
            }
            else
            {
                var userPermission = await _publicContext.AppPermissions.FirstOrDefaultAsync(per => per.UserId == subscription.UserId);
                userPermission.SubscriptionStatus = SubscriptionStatus.Cancelled.ToString();
                _publicContext.AppPermissions.Update(userPermission);
            }

            await _publicContext.SaveChangesAsync();

            // Update the subscription status on our database
            if (!applyAtTheEndOfCurrentSub)
                BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Cancelled, subscription.UserId, subscription.Id.ToString(), subdomain, subscription.TenantId.ToString(), subscription.Application.ToString(), null));
            else
                BackgroundJob.Schedule<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Cancelled, subscription.UserId, subscription.Id.ToString(), subdomain, subscription.TenantId.ToString(), subscription.Application.ToString(), null), subscription.ExpiresOn.Value.AddDays(1));

            // Publish subscription event
            BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscriptionId, SubscriptionStatusForRMQ.Cancelled, ReasonForSubscriptionStatus.CancelledByUser));

            return new GenericResponse
            {
                Data = true,
                ResponseMessage = "Subscription cancelled successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Cancel AI Subscription
        /// <summary>
        /// Cancel subscription
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> CancelAISubscription(CancelAISubscriptionDto model)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id.ToString() == model.SubscriptionId);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");

            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);

            if (!model.CancelAll)
            {
                var aiSubDetails = await _publicContext.AISubscriptionDetails
               .Where(x => x.SubscriptionId == subscription.Id && x.Agent == model.Agent).FirstOrDefaultAsync();
                if (aiSubDetails == null)
                    throw new RecordNotFoundException("AI Subscription details not found");

                // Calculate the new amount to pay
                var amountToPay = subscription.Amount - aiSubDetails.Amount; ;
                subscription.SubscriptionFor = subscription.SubscriptionFor - aiSubDetails.NoOfUserSubscribedFor;
                subscription.Amount = amountToPay;

                _publicContext.AISubscriptionDetails.Remove(aiSubDetails);

                // Update user's app permissions - disable the app permissions for the users
                var permissions = await _publicContext.AppPermissions
                    .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Agent == model.Agent && x.IsEnabled && x.SubscriptionStatus == SubscriptionStatus.Active.ToString()).ToListAsync();

                var companySub = await _publicContext.CompanySubscriptions
                    .Where(x => x.TenantId == subscription.TenantId && x.AIAgent == model.Agent).FirstOrDefaultAsync();

                // Update companies subscription
                if (subscription.PaymentProvider == PaymentProviders.Mollie)
                {
                    var response = await UpdateSubscription(subscription, true, subscription.PricingPlanId.ToString());
                    if (response.Status == "active")
                    {
                        foreach (var permission in permissions)
                        {
                            permission.IsEnabled = false;
                            permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                            permission.UpdatedAt = DateTime.UtcNow;
                        }

                        companySub.Status = SubscriptionStatus.Inactive;
                        companySub.UpdatedAt = DateTime.UtcNow;

                        _publicContext.CompanySubscriptions.Update(companySub);
                        _publicContext.AppPermissions.UpdateRange(permissions);
                        _publicContext.Subscriptions.Update(subscription);
                        var res = await _publicContext.SaveChangesAsync();

                        var message = "The new number of users have been removed successfully. Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription";

                        return new GenericResponse
                        {
                            ResponseCode = "200",
                            ResponseMessage = message,
                            Data = response
                        };
                    }
                }
                else
                {
                    var productId = "";
                    var existingPrices = await _publicContext.StripeSubscriptionDetails
                        .Where(x => x.AIAgent && x.Plan == plan.Name).FirstOrDefaultAsync();
                    if (existingPrices is null)
                        productId = await CreateStripeProduct(plan.Name);
                    else
                        productId = existingPrices.ProductId;

                    var priceDto = new CreateStripePriceDto
                    {
                        ProductId = productId,
                        NumberOfUsersToSubFor = subscription.SubscriptionFor.Value,
                        Amount = (int)amountToPay,
                        Plan = plan.Name,
                        Interval = Enum.Parse<SubscriptionInterval>(subscription.Interval),
                        IsForAI = true
                    };

                    var priceId = await CreateStripePrice(priceDto);
                    var response = await UpdateStripeSubscription(subscription, true, priceId);
                    if (response)
                    {
                        foreach (var permission in permissions)
                        {
                            permission.IsEnabled = false;
                            permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                            permission.UpdatedAt = DateTime.UtcNow;
                        }

                        companySub.Status = SubscriptionStatus.Inactive;
                        companySub.UpdatedAt = DateTime.UtcNow;

                        _publicContext.CompanySubscriptions.Update(companySub);
                        _publicContext.AppPermissions.UpdateRange(permissions);
                        _publicContext.Subscriptions.Update(subscription);
                        var res = await _publicContext.SaveChangesAsync();

                        var message = "The new number of users have been removed successfully. " +
                            "Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription";

                        return new GenericResponse
                        {
                            ResponseCode = "200",
                            ResponseMessage = message,
                            Data = response
                        };
                    }
                }

                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to cancel subscription"
                };
            }

            var aiSubDetailsList = await _publicContext.AISubscriptionDetails
              .Where(x => x.SubscriptionId == subscription.Id).ToListAsync();
            if (aiSubDetailsList == null || aiSubDetailsList.Any())
                throw new RecordNotFoundException("AI Subscription details not found");

            _publicContext.AISubscriptionDetails.RemoveRange(aiSubDetailsList);

            // Delete the subscription on mollie or on stripe
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                // Get mollie subscription status
                var mollieSubscription = await _subscriptionClient.GetSubscriptionAsync(subscription.MollieCustomerId, subscription.SubscriptionId);
                if (mollieSubscription.Status == "active")
                {
                    if (!model.ApplyAtTheEndOfCurrentSub)
                        await _subscriptionClient.CancelSubscriptionAsync(subscription.MollieCustomerId, subscription.SubscriptionId);
                    else
                        BackgroundJob.Schedule(() => CancelMollieSubscription(subscription.MollieCustomerId, subscription.SubscriptionId),
                            subscription.ExpiresOn.Value.AddDays(1));
                }
            }
            else
            {
                var service = new SubscriptionService();
                var response = await CancelStripeSubscription(model.SubscriptionId, model.ApplyAtTheEndOfCurrentSub);
                if (response != "canceled" && !model.ApplyAtTheEndOfCurrentSub)
                    return new GenericResponse
                    {
                        Data = false,
                        ResponseMessage = "Subscription could not be canceled at this time, please try again later",
                        ResponseCode = "500"
                    };
            }

            subscription.IsCancelled = true;
            _publicContext.Subscriptions.Update(subscription);

            if (subscription.TenantId is not null)
            {
                var companySubscription = await _publicContext.CompanySubscriptions.FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId.Value);
                companySubscription.Status = SubscriptionStatus.Cancelled;
                _publicContext.CompanySubscriptions.Update(companySubscription);
            }
            else
            {
                var userPermission = await _publicContext.AppPermissions.FirstOrDefaultAsync(per => per.UserId == subscription.UserId);
                userPermission.SubscriptionStatus = SubscriptionStatus.Cancelled.ToString();
                _publicContext.AppPermissions.Update(userPermission);
            }

            await _publicContext.SaveChangesAsync();

            // Update the subscription status on our database
            if (!model.ApplyAtTheEndOfCurrentSub)
                BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Cancelled, subscription.UserId, subscription.Id.ToString(), model.Subdomain, subscription.TenantId.ToString(), subscription.Application.ToString(), null));
            else
                BackgroundJob.Schedule<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Cancelled, subscription.UserId, subscription.Id.ToString(), model.Subdomain, subscription.TenantId.ToString(), subscription.Application.ToString(), null), subscription.ExpiresOn.Value.AddDays(1));

            // Publish subscription event
            BackgroundJob.Enqueue(() => PublishSubscriptionEvent(model.SubscriptionId, SubscriptionStatusForRMQ.Cancelled, ReasonForSubscriptionStatus.CancelledByUser));

            return new GenericResponse
            {
                Data = true,
                ResponseMessage = "Subscription cancelled successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Resume Subscription
        /// <summary>
        /// This method resumes a cancelled subscription
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="userId"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        public async Task<GenericResponse> ResumeSubscription(Applications app, string subdomain = null, string userId = null)
        {
            var subscription = new Models.Subscription();
            if (subdomain is not null)
            {
                var tenantId = (await _tenantService.GetTenantBySubdomain(subdomain)).Id.ToString();
                subscription = await _publicContext.Subscriptions
                    .FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == app);
            }
            else
                subscription = await _publicContext.Subscriptions
                    .FirstOrDefaultAsync(x => x.UserId.ToString() == userId && x.Application == app);

            if (subscription is null)
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "No subscription found. Kindly choose a plan and create a subscription"
                };

            // Create a new subscription on mollie/stripe for the customer and update the subscription Id on our database
            var subscriptionId = "";
            SubscriptionCreateResponse response = null;
            var oldExpiryDate = subscription.ExpiresOn;
            subscription.ExpiresOn = DateTime.UtcNow;
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                var mollieSubscription = await CreateSubscription(subscription, false);
                subscriptionId = mollieSubscription.Id;
            }
            else
            {
                var subscriptionRequest = new CreateStripeSubscriptionDto
                {
                    CustomerId = subscription.StripeCustomerId,
                    PriceId = subscription.StripePriceId,
                    Currency = subscription.Currency.ToString(),
                    Application = subscription.Application.ToString(),
                    TenantId = subscription.TenantId?.ToString(),
                    NumberOfUsersSubbedFor = subscription.SubscriptionFor.Value
                };
                response = await CreateStripeSubscription(subscriptionRequest);
                subscriptionId = response.SubscriptionId;
            }

            subscription.SubscriptionId = subscriptionId;
            subscription.Status = PaymentStatus.Pending.ToString();
            _publicContext.Update(subscription);
            var res = await _publicContext.SaveChangesAsync();

            return new GenericResponse
            {
                Data = response != null ? response : null,
                ResponseMessage = res > 0 ? "Subscription resumed successfully" : "Subscription resumption failed",
                ResponseCode = res > 0 ? "200" : "400"
            };
        }
        #endregion

        #region Retry Failed Subscription Payment
        /// <summary>
        /// Retry failed subscription payment initiated by the customer
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        public async Task<GenericResponse> RetryFailedSubscriptionPayment(string subscriptionId)
        {
            var res = new GenericResponse();
            res.Data = false;
            SubscriptionCreateResponse response = null;

            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id.ToString() == subscriptionId);

            // Update customer's subscription on mollie/stripe
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
                await UpdateSubscription(subscription, false, null);
            else
            {
                var subscriptionRequest = new CreateStripeSubscriptionDto
                {
                    CustomerId = subscription.StripeCustomerId,
                    PriceId = subscription.StripePriceId,
                    Currency = subscription.Currency.ToString(),
                    Application = subscription.Application.ToString(),
                    TenantId = subscription.TenantId?.ToString(),
                    SubscriptionId = subscriptionId,
                    NumberOfUsersSubbedFor = subscription.SubscriptionFor.Value
                };

                response = await CreateStripeSubscription(subscriptionRequest);
                subscription.SubscriptionId = response.SubscriptionId;
                _publicContext.Subscriptions.Update(subscription);
                await _publicContext.SaveChangesAsync();
            }

            return new GenericResponse
            {
                Data = response,
                ResponseMessage = response is null ? "Your subscription is being processed, you will be notified with status as soon processing is done" : "Success",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Verify Mollie Payment For FE Comfirmation
        /// <summary>
        /// Verify mollie payment for FE confirmation
        /// </summary>
        /// <param name="paymentId"></param>
        /// <returns></returns>
        public async Task<GenericResponse> VerifyMolliePaymentForFE(string paymentId)
        {
            PaymentResponse response = await _paymentClient.GetPaymentAsync(paymentId);
            var res = new GenericResponse();
            if (response.Status == "paid")
            {
                res.Data = MolliePaymentStatus.paid;
                res.ResponseMessage = "Subscription was successful";
                res.ResponseCode = "200";
            }
            else if (response.Status == "open")
            {
                res.Data = MolliePaymentStatus.open;
                res.ResponseMessage = "Subscription is still processing";
                res.ResponseCode = "200";
            }
            else if (response.Status == "failed")
            {
                res.Data = MolliePaymentStatus.failed;
                res.ResponseMessage = "Subscription failed";
                res.ResponseCode = "400";
            }
            else if (response.Status == "canceled")
            {
                res.Data = MolliePaymentStatus.canceled;
                res.ResponseMessage = "Subscription was canceled";
                res.ResponseCode = "400";
            }
            else
            {
                res.Data = MolliePaymentStatus.expired;
                res.ResponseMessage = "Subscription status is expired";
                res.ResponseCode = "400";
            }

            return res;
        }
        #endregion

        #region Verify First Subscription Payment
        /// <summary>
        /// Verify the first subscription payment
        /// </summary>
        /// <param name="paymentId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> VerifyFirstSubscriptionPayment(string paymentId)
        {
            var res = new GenericResponse();
            AdditionalLiecense additionalLiecense = null;
            res.Data = false;
            string subdomain = null;
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.PaymentId == paymentId);
            if (subscription is null)
            {
                // Get liecense record using the paymentId and then use the subscriptionId to get the subscription record
                additionalLiecense = await _publicContext.AdditionalLiecenses.FirstOrDefaultAsync(x => x.PaymentId == paymentId && !x.Updated);
                if (additionalLiecense is null)
                    throw new RecordNotFoundException("Subscription not found");

                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id == additionalLiecense.SubscriptionId);
                if (subscription is null)
                    throw new RecordNotFoundException("Subscription not found");
            }

            if (!string.IsNullOrEmpty(subscription.TenantId.ToString()))
            {
                subdomain = await _publicContext.Tenants.Where(sub => sub.Id == subscription.TenantId)
                    .Select(sub => sub.Subdomain).FirstOrDefaultAsync();
            }

            // Call mollie endpoint to get payment with paymentId and If payment is successful, update subscription status to active
            PaymentResponse response = await _paymentClient.GetPaymentAsync(paymentId);
            var schemaContextRes = 0;
            var company = new Tenant.Model.Tenant();
            var status = false;
            switch (response.Status)
            {
                case "paid":
                    status = true;

                    CompanySubscription companySub = null;

                    if (subscription.FreeTrialOptionSelected)
                        subscription.ExpiresOn = DateTime.UtcNow.AddDays(14);
                    subscription.UpdatedAt = DateTime.UtcNow;
                    subscription.MandateId = response.MandateId;

                    if (additionalLiecense is not null)
                    {
                        // Update additional liecense record
                        additionalLiecense.Status = PaymentStatus.Successful.ToString();
                        additionalLiecense.UpdatedAt = DateTime.UtcNow;
                        additionalLiecense.TransactionDate = DateTime.UtcNow;
                        additionalLiecense.Updated = true;
                        _publicContext.AdditionalLiecenses.Update(additionalLiecense);

                        // Update some records on subscription record
                        subscription.SubscriptionFor = subscription.SubscriptionFor + additionalLiecense.SubscriptionFor;
                        subscription.Amount = subscription.Amount + additionalLiecense.Amount;
                        subscription.MollieCustomerId = response.CustomerId;
                        subscription.PaymentId = paymentId;

                        // Get additional selected user if any from additionalLience record and update their subscription status
                        await using var _context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
                        await AddOrEnablePermission(additionalLiecense.UserIds, subscription.TenantId.Value.ToString(), _context, SubscriptionStatus.Active, subscription.Application);

                        // Update subscription
                        var updateRes = await UpdateSubscription(subscription, true, subscription.PricingPlanId.ToString());
                    }
                    else
                    {
                        subscription.Status = PaymentStatus.Successful.ToString();
                        subscription.ActivatedOn = DateTime.UtcNow;
                        subscription.ContractEndsOn = DateTime.UtcNow.AddYears(1);
                        subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1)
                             : DateTime.UtcNow.AddMonths(12);

                        if (subscription.TenantId is not null)
                        {
                            company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == subscription.TenantId);
                            if (!subscription.IsAISubscription)
                            {
                                companySub = await _publicContext.CompanySubscriptions.FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId && x.Application == subscription.Application);

                                if (companySub is null)
                                {
                                    companySub = new CompanySubscription
                                    {
                                        TenantId = (Guid)subscription.TenantId,
                                        Application = subscription.Application,
                                        Status = SubscriptionStatus.Active,
                                        CreatedAt = DateTime.UtcNow,
                                        SubscriptionId = subscription.Id,
                                        UpdatedAt = DateTime.UtcNow
                                    };

                                    await _publicContext.CompanySubscriptions.AddAsync(companySub);
                                }
                                else
                                {
                                    companySub.Status = SubscriptionStatus.Active;
                                    companySub.UpdatedAt = DateTime.UtcNow;
                                    companySub.SubscriptionId = subscription.Id;
                                    _publicContext.CompanySubscriptions.Update(companySub);
                                }
                            }
                            else
                            {
                                var aiSubDetails = await _publicContext.AISubscriptionDetails
                                    .Where(x => x.SubscriptionId == subscription.Id).ToListAsync();
                                if (aiSubDetails.Count < 1)
                                    throw new RecordNotFoundException("AI Subscription details not found");

                                foreach (var detail in aiSubDetails)
                                {
                                    companySub = await _publicContext.CompanySubscriptions
                                        .FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId && x.AIAgent == detail.Agent);
                                    if (companySub is null)
                                    {
                                        companySub = new CompanySubscription
                                        {
                                            TenantId = (Guid)subscription.TenantId,
                                            Status = SubscriptionStatus.Active,
                                            CreatedAt = DateTime.UtcNow,
                                            SubscriptionId = subscription.Id,
                                            UpdatedAt = DateTime.UtcNow,
                                            AIAgent = detail.Agent
                                        };

                                        await _publicContext.CompanySubscriptions.AddAsync(companySub);
                                    }
                                    else
                                    {
                                        companySub.Status = SubscriptionStatus.Active;
                                        companySub.UpdatedAt = DateTime.UtcNow;
                                        companySub.SubscriptionId = subscription.Id;
                                        _publicContext.CompanySubscriptions.Update(companySub);
                                    }
                                }
                            }

                            // Update app permission of the super admin user
                            if (!subscription.IsAISubscription)
                            {
                                await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
                                var superAdminId = await _publicContext.Users.Where(x => x.Id == company.AdminId).Select(x => x.Id).FirstOrDefaultAsync();
                                var permissions = await _context.AppPermissions
                                    .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Application == subscription.Application.ToString() && x.IsEnabled == true).ToListAsync();

                                if (permissions.Any())
                                {
                                    int count = 1;
                                    foreach (var permission in permissions)
                                    {
                                        if (count > subscription.SubscriptionFor)
                                        {
                                            res.ResponseMessage = $"Note: The number of employees with permissions exceeds the number of " +
                                                $"employees covered by your current subscription. Your sunscription current covers {subscription.SubscriptionFor} employees";
                                            break;
                                        }

                                        permission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                                        _context.AppPermissions.Update(permission);

                                        count++;
                                    }

                                    schemaContextRes = await _context.SaveChangesAsync();
                                }

                                _publicContext.Tenants.Update(company);
                            }
                            else
                            {
                                var aiSubDetails = await _publicContext.AISubscriptionDetails
                                    .Where(x => x.SubscriptionId == subscription.Id).ToListAsync();
                                if (aiSubDetails.Count < 1)
                                    throw new RecordNotFoundException("AI Subscription details not found");

                                foreach (var aiSub in aiSubDetails)
                                {
                                    await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
                                    var permissions = await _context.AppPermissions
                                        .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Application == subscription.Application.ToString() && x.IsEnabled == true && x.Agent == aiSub.Agent).ToListAsync();

                                    if (permissions.Any())
                                    {
                                        int count = 1;
                                        foreach (var permission in permissions)
                                        {
                                            if (count > aiSub.NoOfUserSubscribedFor)
                                            {
                                                res.ResponseMessage = $"Note: The number of employees with liecenses exceeds the number of " +
                                                    $"employees covered by your current subscription. Your sunscription current covers {aiSub.NoOfUserSubscribedFor} employees";
                                                break;
                                            }

                                            permission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                                            _context.AppPermissions.Update(permission);

                                            count++;
                                        }

                                        schemaContextRes = await _context.SaveChangesAsync();
                                    }

                                    _publicContext.Tenants.Update(company);
                                }
                            }
                        }
                        else
                        {
                            if (!subscription.IsAISubscription)
                            {
                                var userId = await _publicContext.Users
                                .Where(x => x.Id == subscription.UserId).Select(u => u.Id).FirstOrDefaultAsync();

                                // Update app permission of the user
                                var permission = await _publicContext.AppPermissions
                                    .Where(x => x.UserId == userId && x.Application == subscription.Application.ToString() && x.IsEnabled == true)
                                    .FirstOrDefaultAsync();

                                if (permission is not null)
                                {
                                    permission.IsEnabled = true;
                                    permission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                                    _publicContext.AppPermissions.Update(permission);
                                }
                                else
                                {
                                    var appPermission = new AppPermissions(true)
                                    {
                                        UserId = userId,
                                        Application = subscription.Application.ToString(),
                                        IsEnabled = true,
                                        SubscriptionStatus = SubscriptionStatus.Active.ToString(),
                                        CreatedAt = DateTime.UtcNow,
                                        UpdatedAt = DateTime.UtcNow
                                    };

                                    _publicContext.AppPermissions.Add(appPermission);
                                }
                            }
                        }

                        // Create a subscription for the customer
                        var freePlan = false;
                        if (response.Metadata == "Free-Trial")
                            freePlan = true;

                        var subscriptionResponse = await CreateSubscription(subscription, null, freePlan);
                        if (subscriptionResponse.Status != "active")
                        {
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = "Payment was successfully but subcription creation failed. " +
                                "Please click on activate subscription to activate your subscription",
                                Data = subscriptionResponse
                            };
                        }
                        subscription.SubscriptionId = subscriptionResponse.Id;

                        if (!subscription.FreeTrialOptionSelected)
                            subscription.SubscriptionCount = subscription.SubscriptionCount++;

                        // Add the subscription to the subscriptionhistory table if it doesn't exist
                        var subscriptionHistory = await _publicContext.SubscriptionHistory.FirstOrDefaultAsync(x => x.PaymentId == paymentId);
                        if (subscriptionHistory == null)
                        {
                            subscriptionHistory = subscription.Map();
                            subscriptionHistory.Id = Guid.NewGuid();
                            await _publicContext.SubscriptionHistory.AddAsync(subscriptionHistory);
                        }
                        else
                        {
                            subscriptionHistory.Status = subscription.Status;
                            subscriptionHistory.ExpiresOn = subscription.ExpiresOn;
                            subscriptionHistory.ActivatedOn = subscription.ActivatedOn;
                            subscriptionHistory.UpdatedAt = DateTime.UtcNow;

                            _publicContext.SubscriptionHistory.Update(subscriptionHistory);
                        }
                    }

                    res.Data = true;
                    res.ResponseMessage = "Subscription has been activated";
                    res.ResponseCode = "200";
                    break;
                case "open":
                    if (additionalLiecense is null)
                    {
                        subscription.Status = PaymentStatus.Pending.ToString();
                        subscription.UpdatedAt = DateTime.UtcNow;
                    }

                    res.Data = true;
                    res.ResponseMessage = "The payment is still proccessing";
                    res.ResponseCode = "200";
                    break;
                case "expired":
                    if (additionalLiecense is null)
                    {
                        subscription.Status = PaymentStatus.Failed.ToString();
                        subscription.UpdatedAt = DateTime.UtcNow;
                    }
                    else
                    {
                        additionalLiecense.Status = PaymentStatus.Failed.ToString();
                        additionalLiecense.UpdatedAt = DateTime.UtcNow;
                        _publicContext.AdditionalLiecenses.Update(additionalLiecense);
                    }

                    // Send an email to the customer that the payment has expired. Ask them to fund the account and try again.
                    // The system will try to make the payment again (3 times) 24 hours after each attempt

                    res.Data = true;
                    res.ResponseMessage = "The payment has expired";
                    res.ResponseCode = "400";
                    break;
                case "canceled":
                    if (additionalLiecense is null)
                    {
                        subscription.Status = PaymentStatus.Cancelled.ToString();
                        subscription.UpdatedAt = DateTime.UtcNow;
                    }
                    else
                    {
                        additionalLiecense.Status = PaymentStatus.Cancelled.ToString();
                        additionalLiecense.UpdatedAt = DateTime.UtcNow;
                        _publicContext.AdditionalLiecenses.Update(additionalLiecense);
                    }

                    res.Data = true;
                    res.ResponseMessage = "The payment has been cancelled";
                    res.ResponseCode = "400";
                    break;
                case "failed":
                    if (additionalLiecense is null)
                    {
                        subscription.Status = PaymentStatus.Failed.ToString();
                        subscription.UpdatedAt = DateTime.UtcNow;
                    }
                    else
                    {
                        additionalLiecense.Status = PaymentStatus.Failed.ToString();
                        additionalLiecense.UpdatedAt = DateTime.UtcNow;
                        _publicContext.AdditionalLiecenses.Update(additionalLiecense);
                    }

                    // Send an email to the customer that the payment failed. Ask to fund the account and try again.
                    // The system will try to make the payment again (3 times) 24 hours after each attempt

                    res.Data = true;
                    res.ResponseMessage = "The payment has failed";
                    res.ResponseCode = "400";
                    break;
            }

            _publicContext.Update(subscription);
            var dbResult = await _publicContext.SaveChangesAsync();

            if (dbResult > 0 && status)
            {
                if (subscription.FreeTrialOptionSelected)
                {
                    // Schedule a background job to update free trail status
                    BackgroundJob.Enqueue<IBackGroundServices>((x) => x.UpdateFreeTrialToFalse(subscription.Id.ToString()));

                    // Check if billing information exists for the tenant
                    var billingInfo = await _publicContext.BillingInformations.FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId);
                    var invitee = await _publicContext.Users.Where(x => x.Id == subscription.UserId)
                        .FirstOrDefaultAsync();
                    var template = string.Empty;
                    string recipientName;
                    string recipientEmail;

                    // Use billing information if available, otherwise use invitee details
                    if (billingInfo != null)
                    {
                        recipientName = $"{billingInfo.FirstName} {billingInfo.LastName}";
                        recipientEmail = billingInfo.Email;
                    }
                    else
                    {
                        recipientName = $"{invitee.FirstName} {invitee.LastName}";
                        recipientEmail = invitee.Email;
                    }

                    template = Extensions.ReadTemplateFromFile("free-plan-notification", _environment);
                    template = template.Replace("{name}", recipientName).Replace("{faq-url}", ApplicationFrontendURL).Replace("{contact-url}", ApplicationFrontendURL).Replace("{google}", ApplicationFrontendURL).Replace("{apple}", ApplicationFrontendURL);
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, recipientEmail, "Welcome to JobPro - Free Trial Activated"));
                }
                else
                {
                    if (additionalLiecense is not null)
                    {
                        // TODO: Send a different mail for addition liencense purchase instead of SendPaidPlanNotification
                    }
                    else
                        await SendPaidPlanNotification(res.ResponseCode, subscription);
                }

                // Publish subscription event
                BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.New, null));
            }
            else if (dbResult < 1)
                throw new Exception("Subscription activation failed");

            return res;
        }
        #endregion

        #region Verify Subsequent Subscription Payments
        /// <summary>
        /// This verifies subsequent subscription payments, verifies plan upgrades and updates subscription status accordingly
        /// </summary>
        /// <param name="paymentId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> VerifySubsequentSubscriptionPayment(string paymentId)
        {
            var res = new GenericResponse();
            // Call mollie endpoint to get payment with paymentId and If payment is successful, update subscription status to active
            PaymentResponse response = await _paymentClient.GetPaymentAsync(paymentId);
            // Get the subscription using paymentId
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.SubscriptionId == response.SubscriptionId);
            var subdomain = await _publicContext.Tenants.Where(sub => sub.Id == subscription.TenantId)
                .Select(sub => sub.Subdomain).FirstOrDefaultAsync();
            switch (response.Status)
            {
                case "paid":
                    subscription.Status = PaymentStatus.Successful.ToString();
                    subscription.ActivatedOn = DateTime.UtcNow;
                    subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                    subscription.UpdatedAt = DateTime.UtcNow;
                    subscription.RetrySubAttempt = 0;
                    subscription.PaymentId = paymentId;
                    subscription.SubscriptionCount = subscription.SubscriptionCount + 1;

                    //if (response.Metadata is not null)
                    //{
                    //    var metaData = response.GetMetadata<CustomUpdateSubMetaData>();
                    //    var newpricingPlanId = metaData.PricingPlanId;
                    //    if (newpricingPlanId != null)
                    //        subscription.PricingPlanId = Guid.Parse(newpricingPlanId);
                    //}

                    // Update clients subscription status to active if it's not active
                    BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Active, subscription.UserId, subscription.Id.ToString(), subdomain, subscription.TenantId.ToString(), subscription.Application.ToString(), null));

                    // Add the subscription to the subscriptionhistory table if it doesn't exist
                    var subscriptionHistory = subscription.Map();
                    subscriptionHistory.Id = Guid.NewGuid();
                    await _publicContext.SubscriptionHistory.AddAsync(subscriptionHistory);

                    res.Data = true;
                    break;
                case "failed":
                    subscription.Status = PaymentStatus.Failed.ToString();
                    subscription.UpdatedAt = DateTime.UtcNow;

                    // Send an email to the customer that the payment failed. Ask to fund the account and try again.
                    // The system will try to make the payment again (3 times) 24 hours after each attempt

                    // Trigger backgraound task that will update subscription status to inactive after 30 days
                    BackgroundJob.Enqueue<IBackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), subdomain, null));

                    // Schedule a subscription event to be published after 2 days from the time of the failed payment
                    BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Inactive, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));
                    break;
                case "expired":
                    subscription.Status = PaymentStatus.Failed.ToString();
                    subscription.UpdatedAt = DateTime.UtcNow;

                    res.Data = false;
                    break;
                case "canceled":
                    subscription.Status = PaymentStatus.Cancelled.ToString();
                    subscription.UpdatedAt = DateTime.UtcNow;

                    // Trigger backgraound task that will update subscription status to inactive after 30 days
                    BackgroundJob.Enqueue<IBackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), subdomain, null));

                    // Schedule a subscription event to be published after 2 days from the time of the failed payment
                    BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Inactive, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));

                    res.Data = false;
                    break;
            }

            subscription.PaymentId = paymentId;

            _publicContext.Update(subscription);
            var result = await _publicContext.SaveChangesAsync();

            if (result > 0)
                return res;
            else
                throw new Exception("Subscription activation failed");
        }
        #endregion

        #region Verify Enterprise Subscription Payment
        /// <summary>
        /// Verify the first subscription payment
        /// </summary>
        /// <param name="paymentId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> VerifyEnterpriseSubscriptionPayment(string paymentId)
        {
            var res = new GenericResponse();
            res.Data = false;
            var entPayment = await _publicContext.EnterprizeSubscriptionPayments.FirstOrDefaultAsync(x => x.PaymentId == paymentId);
            if (entPayment is null)
            {
                _logger.Error($"VerifyEnterpriseSubscriptionPayment:Enterprise payment record not found for payment ID: {paymentId}");
                throw new RecordNotFoundException("Enterprise payment record not found");
            }

            PaymentResponse response = await _paymentClient.GetPaymentAsync(paymentId);
            var status = false;
            switch (response.Status)
            {
                case "paid":
                    status = true;

                    if (!string.IsNullOrEmpty(entPayment.TenantId.ToString()))
                    {
                        var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == entPayment.TenantId);

                        // Activate subscription
                        var subscription = await _publicContext.Subscriptions
                            .FirstOrDefaultAsync(x => x.TenantId == entPayment.TenantId && x.Application == entPayment.Application);

                        if (subscription is not null)
                        {
                            subscription.ActivatedOn = DateTime.UtcNow;
                            subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                            subscription.UpdatedAt = DateTime.UtcNow;
                            subscription.Status = PaymentStatus.Successful.ToString();
                            subscription.PaymentId = paymentId;

                            _publicContext.Subscriptions.Update(subscription);

                            //var updateRes = await UpdateSubscriptionStatus(subscription, SubscriptionStatus.Active);
                            //if (updateRes.ResponseCode != "200")
                            //{
                            //    _logger.Error($"VerifyEnterpriseSubscriptionPayment:Failed to update subscription status for enterprise plan ID: {tenant.Id}");
                            //    throw new Exception($"Failed to update subscription status - {updateRes.ResponseMessage}");
                            //}

                            BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Active, subscription.UserId, subscription.Id.ToString(), tenant.Id.ToString(), subscription.TenantId.ToString(), subscription.Application.ToString(), entPayment.Id.ToString()));

                            // Trigger backgraound task that will update subscription status to inactive after 30 days
                            BackgroundJob.Enqueue<IBackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), tenant.Subdomain, null));

                            // Schedule a subscription event to be published after 2 days from the time of the failed payment
                            BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Active, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));
                        }
                    }

                    entPayment.PaymentStatus = PaymentStatus.Successful;
                    entPayment.PaymentDate = DateTime.UtcNow;
                    entPayment.UpdatedOn = DateTime.UtcNow;

                    _logger.Information($"VerifyEnterpriseSubscriptionPayment:Enterprise payment successful for payment ID: {paymentId}");
                    res.Data = true;
                    res.ResponseMessage = "Enterprise payment has been updated to successful";
                    res.ResponseCode = "200";
                    break;
                case "open":
                    entPayment.PaymentStatus = PaymentStatus.Pending;
                    entPayment.UpdatedOn = DateTime.UtcNow;

                    res.Data = true;
                    res.ResponseMessage = "The payment is still proccessing";
                    res.ResponseCode = "200";
                    break;
                case "expired":
                    entPayment.PaymentStatus = PaymentStatus.Failed;
                    entPayment.UpdatedOn = DateTime.UtcNow;

                    // TODO: Send an email to the customer that the payment has expired. To contact support for new payment link.
                    // TODO: Also send am email to zarttech support that a paylink has expired for them to generate another one

                    res.Data = true;
                    res.ResponseMessage = "The payment has expired";
                    res.ResponseCode = "400";
                    break;
                case "canceled":
                    entPayment.PaymentStatus = PaymentStatus.Cancelled;
                    entPayment.UpdatedOn = DateTime.UtcNow;

                    res.Data = true;
                    res.ResponseMessage = "The payment has been cancelled";
                    res.ResponseCode = "400";
                    break;
                case "failed":
                    if (!string.IsNullOrEmpty(entPayment.TenantId.ToString()))
                    {
                        var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == entPayment.TenantId);

                        // Activate subscription
                        var subscription = await _publicContext.Subscriptions
                            .FirstOrDefaultAsync(x => x.TenantId == entPayment.TenantId && x.Application == entPayment.Application);

                        if (subscription is not null)
                        {
                            subscription.UpdatedAt = DateTime.UtcNow;
                            subscription.Status = PaymentStatus.Failed.ToString();
                            subscription.PaymentId = paymentId;

                            _publicContext.Subscriptions.Update(subscription);

                            BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Inactive, subscription.UserId, subscription.Id.ToString(), tenant.Id.ToString(), subscription.TenantId.ToString(), subscription.Application.ToString(), null));

                            // Schedule a subscription event to be published after 2 days from the time of the failed payment
                            BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Inactive, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));
                        }
                    }

                    entPayment.PaymentStatus = PaymentStatus.Failed;
                    entPayment.UpdatedOn = DateTime.UtcNow;

                    // TODO: Send an email to the customer that the payment failed. Ask to fund the account and try again.

                    _logger.Information($"VerifyEnterpriseSubscriptionPayment:Enterprise payment failed for payment ID: {paymentId}");
                    res.Data = true;
                    res.ResponseMessage = "The payment failed";
                    res.ResponseCode = "400";
                    break;
            }

            _publicContext.EnterprizeSubscriptionPayments.Update(entPayment);
            var dbResult = await _publicContext.SaveChangesAsync();

            if (dbResult > 0 && status)
            {
                //TODO: Send an email to the user that payment was successfull and company account will be created shortly.
                //TODO: Also send am email to the super admins of zarttech that an enterprise payment was successful and for them to commence company account creation
            }
            else if (dbResult < 1)
                throw new Exception("Enterprise payment update failed");

            return res;
        }
        #endregion

        #region Get a user or a company's current subscription
        /// <summary>
        /// This gets a user or a company's current subscription
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetSubscriptions(string userId, string subdomain = null)
        {
            var subscriptions = new List<Models.Subscription>();
            if (subdomain != null)
            {
                var tenantId = await _publicContext.Tenants.Where(x => x.Subdomain == subdomain).Select(x => x.Id).FirstOrDefaultAsync();
                subscriptions = await _publicContext.Subscriptions.Where(x => x.TenantId == tenantId).ToListAsync();
            }
            else
            {
                subscriptions = await _publicContext.Subscriptions.Where(x => x.UserId == userId).ToListAsync();
            }

            return new GenericResponse
            {
                Data = subscriptions,
                ResponseCode = "200",
                ResponseMessage = "Subscriptions retrieved successfully",
            };
        }
        #endregion

        #region Subscription for additional users
        public async Task<GenericResponse> AddUsersToCurrentSubscription(BuyMoreLicenceDto model)
        {
            PaymentResponse paymentRes = null;
            SubscriptionCreateResponse subscriptionCreateResponse = null;

            var tenant = await _publicContext.Tenants
                .Where(x => x.Subdomain == model.Subdomain)
                .FirstOrDefaultAsync();

            var subscription = await _publicContext.Subscriptions
                .FirstOrDefaultAsync(x => x.TenantId == tenant.Id && x.Application == model.App);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");
            if (subscription.ExpiresOn < DateTime.UtcNow)
                throw new Exception("Subscription is not active");

            await using var _context = new JobProDbContext(_conString, new DbContextSchema(model.Subdomain));

            // Check if the number of selected users plus the user making the subscription is more than the number of liencese
            if (model.UserIds.Count > model.NumberOfUsers)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Number of liencenses {model.NumberOfUsers} cannot be less than the number of " +
                    $"selected users - {model.UserIds.Count + 1}",
                };
            }

            // Check that user ids supplied if any belongs to the company and that they don't already have access to the application
            if (model.UserIds.Any())
            {
                var validationRes = await ValidateUserIds(model.UserIds, _context, model.App, false);
                if (validationRes.ResponseCode != "200")
                    return validationRes;
            }

            var user = await _publicContext.Users.FirstOrDefaultAsync(x => x.Id == model.LoggedInUserId);

            var plan = await _publicContext.PricingPlans
                .FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
            if (plan.Name == SubscriptionPlans.Enterprise.ToString())
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "You are on an enterprise plan, kindly contact support for more liencenses",
                };

            var regionRes = await GetRegionAsEnum(tenant.Country, tenant.Id);
            if (regionRes.ResponseCode != "200")
                return regionRes;

            var region = regionRes.Data as SubscriptionRegions?;
            var planDetails = await _publicContext.PackagePricing
                .FirstOrDefaultAsync(x => x.PricingPlanId == plan.Id && x.Currency == subscription.Currency.ToString());

            // The additional fee will be prorated based on the remaining time until the next payment cycle, and the extra cost will be due immediately.
            var remainingDays = (subscription.ExpiresOn - DateTime.UtcNow).Value.Days;
            var amountPerUserPerDay = 0.0;
            if (subscription.Interval == SubscriptionInterval.Monthly.ToString())
                amountPerUserPerDay = (double)(planDetails.PricePerMonth / 30);
            else
                amountPerUserPerDay = (double)(planDetails.PricePerMonthForYearlyOption / 30);

            var amountToPay = (double)(model.NumberOfUsers * amountPerUserPerDay * remainingDays);
            amountToPay = !model.ApplyAtTheEndOfCurrentSub ? (double)(model.NumberOfUsers * planDetails.PricePerMonth) : amountToPay;
            amountToPay = Math.Ceiling(amountToPay);
            var currency = subscription.Currency.ToString();

            if (model.FirstCall)
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "The amount below is for the additional Licenses." +
                    " Note that this amount is prorated based on the remaining days till the next billing cycle.",
                    Data = new { Amount = amountToPay, NoOfUsers = model.NumberOfUsers, planDetails.Currency }
                };

            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                if (!string.IsNullOrEmpty(subscription.MollieCustomerId))
                {
                    var customer = await _customerClient.GetCustomerAsync(subscription.MollieCustomerId);
                    if (customer is not null)
                        subscription.MollieCustomerId = customer.Id;
                }

                var paymentMethodEnum = Enum.Parse<PaymentMethods>(subscription.PaymentMethod);
                paymentRes = await CreatePaymentForCustomer(plan, amountToPay, subscription.MollieCustomerId, currency, paymentMethodEnum, true);

                // Add a new AdditionalLiecense record
                var additionalLiecense = new AdditionalLiecense
                {
                    SubscriptionId = subscription.Id,
                    PaymentId = paymentRes.Id,
                    MollieCustomerId = subscription.MollieCustomerId,
                    Amount = amountToPay,
                    SubscriptionFor = model.NumberOfUsers,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Updated = false,
                    Status = PaymentStatus.Pending.ToString(),
                    UserIds = model.UserIds
                };

                await _publicContext.AdditionalLiecenses.AddAsync(additionalLiecense);

                if (model.UserIds.Any())
                {
                    var result = await AddOrEnablePermission(model.UserIds, tenant.Id.ToString(), _context, SubscriptionStatus.Inactive, subscription.Application);
                    if (!result)
                        throw new Exception("Failed to add users to subscription");
                }

                var res = await _publicContext.SaveChangesAsync();
                return new GenericResponse
                {
                    Data = res > 0 ? paymentRes.Links.Checkout.Href : null,
                    ResponseMessage = res > 0 ? "The new number of users will be added after a successfull payment. " +
                    "Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription billing cycle" : "Additional liecense creation failed",
                    ResponseCode = res > 0 ? "200" : "400"
                };
            }
            else
            {
                // Create paymentIntent
                var paymentIntentModel = new PaymentIntentCreationDto
                {
                    Email = user.Email,
                    Currency = currency,
                    CustomerId = subscription.StripeCustomerId,
                    Amount = (long)amountToPay,
                };
                var clientSecret = await CreatePaymentIntent(paymentIntentModel);
                subscriptionCreateResponse = new SubscriptionCreateResponse
                {
                    ClientSecret = clientSecret
                };

                var additionalLiecense = new AdditionalLiecense
                {
                    SubscriptionId = subscription.Id,
                    StripeCustomerId = subscription.StripeCustomerId,
                    Amount = amountToPay,
                    SubscriptionFor = model.NumberOfUsers,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Updated = false,
                    Status = PaymentStatus.Pending.ToString(),
                    UserIds = model.UserIds
                };

                await _publicContext.AdditionalLiecenses.AddAsync(additionalLiecense);

                if (model.UserIds.Any())
                {
                    var result = await AddOrEnablePermission(model.UserIds, tenant.Id.ToString(), _context, SubscriptionStatus.Inactive, subscription.Application);
                    if (!result)
                        throw new Exception("Failed to add users to subscription");
                }

                var res = await _publicContext.SaveChangesAsync();
                return new GenericResponse
                {
                    Data = res > 0 ? subscriptionCreateResponse.ClientSecret : null,
                    ResponseMessage = res > 0 ? "The new number of users will be added after a successfull payment. Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription billing cycle" : "Additional liecense creation failed",
                    ResponseCode = res > 0 ? "200" : "400"
                };
            }
        }
        #endregion

        #region Add users to an active AI subscription
        public async Task<GenericResponse> AddUsersToAISubscription(RemoveOrAddUsersFromAISubReq model)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id == model.SubscriptionId);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");

            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
            var aiSubDetails = await _publicContext.AISubscriptionDetails
                .Where(x => x.SubscriptionId == subscription.Id && x.Agent == model.Agent).FirstOrDefaultAsync();
            if (aiSubDetails == null)
                throw new RecordNotFoundException("AI Subscription details not found");

            // Get the price per user for the AI subscription
            var planPricing = await _publicContext.AIAgents.FirstOrDefaultAsync(x => x.Agent == model.Agent.ToString());
            if (planPricing == null)
                throw new RecordNotFoundException("AI Subscription pricing not found");

            var oldAmount = aiSubDetails.Amount;
            aiSubDetails.NoOfUserSubscribedFor = aiSubDetails.NoOfUserSubscribedFor + model.NumberOfUsers;
            if (subscription.Interval == SubscriptionInterval.Monthly.ToString())
                aiSubDetails.Amount = (double)(aiSubDetails.NoOfUserSubscribedFor * planPricing.AmountPerMonthPerUser);
            else
                aiSubDetails.Amount = (double)(aiSubDetails.NoOfUserSubscribedFor * planPricing.AmountPerYearPerUser);


            _publicContext.AISubscriptionDetails.Update(aiSubDetails);

            // Calculate the new amount to pay
            var amountToPay = subscription.Amount - oldAmount + aiSubDetails.Amount;
            subscription.SubscriptionFor = subscription.SubscriptionFor + model.NumberOfUsers;
            subscription.Amount = amountToPay;

            // Update user's app permissions - disable the app permissions for the users
            var permissions = await _publicContext.AppPermissions
                .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Agent == model.Agent && x.IsEnabled && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && model.UserIds.Contains(x.UserId)).ToListAsync();

            // Update companies subscription
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                var response = await UpdateSubscription(subscription, true, subscription.PricingPlanId.ToString());
                if (response.Status == "active")
                {
                    foreach (var permission in permissions)
                    {
                        permission.IsEnabled = false;
                        permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                    }

                    _publicContext.AppPermissions.UpdateRange(permissions);
                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "The new number of users have been removed successfully. Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
            }
            else
            {
                var productId = "";
                var existingPrices = await _publicContext.StripeSubscriptionDetails
                    .Where(x => x.AIAgent && x.Plan == plan.Name).FirstOrDefaultAsync();
                if (existingPrices is null)
                    productId = await CreateStripeProduct(plan.Name);
                else
                    productId = existingPrices.ProductId;

                var priceDto = new CreateStripePriceDto
                {
                    ProductId = productId,
                    NumberOfUsersToSubFor = subscription.SubscriptionFor.Value,
                    Amount = (int)amountToPay,
                    Plan = plan.Name,
                    Interval = Enum.Parse<SubscriptionInterval>(subscription.Interval),
                    IsForAI = true
                };

                var priceId = await CreateStripePrice(priceDto);
                var response = await UpdateStripeSubscription(subscription, true, priceId);
                if (response)
                {
                    foreach (var permission in permissions)
                    {
                        permission.IsEnabled = false;
                        permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                    }

                    _publicContext.AppPermissions.UpdateRange(permissions);
                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "The new number of users have been removed successfully. " +
                        "Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
            }

            return new GenericResponse
            {
                ResponseCode = "400",
                ResponseMessage = "Failed to remove users to subscription"
            };
        }
        #endregion

        #region Remove users from a subscription
        public async Task<GenericResponse> RemoveUsersFromSubscription(int numberOfUsers, string subdomain, Applications app, List<string> userIds)
        {
            var tenant = await _publicContext.Tenants.Where(x => x.Subdomain == subdomain).FirstOrDefaultAsync();
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == tenant.Id.ToString() && x.Application == app);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");
            if (subscription.IsCancelled)
                throw new DirtyFormException("You cannot remove users from a cancelled subscription");

            // Get company subscription
            var companySubscription = await _publicContext.CompanySubscriptions
                .FirstOrDefaultAsync(x => x.TenantId == tenant.Id && x.Application == app && x.Status == SubscriptionStatus.Active);
            if (companySubscription == null)
                throw new RecordNotFoundException("No active company subscription record found");

            // Check that user ids supplied if any belongs to the company and that they don't already have access to the application
            await using var _context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            if (userIds.Any())
            {
                var validationRes = await ValidateUserIds(userIds, _context, app, true);
                if (validationRes.ResponseCode != "200")
                    return validationRes;
            }

            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
            if (plan.Name == SubscriptionPlans.Enterprise.ToString())
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "You are on an enterprise plan, kindly contact support for more liencenses",
                };

            var regionRes = await GetRegionAsEnum(tenant.Country, tenant.Id);
            if (regionRes.ResponseCode != "200")
                return regionRes;

            var region = regionRes.Data as SubscriptionRegions?;
            var planDetails = await _publicContext.PackagePricing
                .FirstOrDefaultAsync(x => x.PricingPlanId == plan.Id && x.Region == region);

            subscription.SubscriptionFor = subscription.SubscriptionFor - numberOfUsers;
            if (subscription.Interval == SubscriptionInterval.Monthly.ToString())
                subscription.Amount = (double)(subscription.SubscriptionFor * planDetails.PricePerMonth);
            else
                subscription.Amount = (double)(subscription.SubscriptionFor * planDetails.PricePerMonthForYearlyOption * 12);    

            if (numberOfUsers > subscription.SubscriptionFor)
                throw new DirtyFormException("You cannot remove more users than you have on your subscription");

            // Update user's app permissions - disable the app permissions for the users
            var permissions = await _context.AppPermissions
                .Where(x => x.TenantId == tenant.Id.ToString() && x.Application == app.ToString() && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && userIds.Contains(x.UserId)).ToListAsync();

            if (subscription.ContractEndsOn == null)
                subscription.ContractEndsOn = subscription.CreatedAt.AddYears(1);

            // Update companies subscription
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                var response = await UpdateSubscription(subscription, true, subscription.PricingPlanId.ToString());
                if (response.Status == "active")
                {
                    foreach (var permission in permissions)
                    {
                        permission.IsEnabled = false;
                        permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                    }

                    _context.AppPermissions.UpdateRange(permissions);
                    await _context.SaveChangesAsync();

                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "The users have been removed successfully. Kindly note that your subscription has been updated accordingly and the new amount will take effect at the end of your current subscription contract.";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
            }
            else
            {
                var newAmountPerUser = 0.0;
                if (subscription.Interval == SubscriptionInterval.Monthly.ToString())
                    newAmountPerUser = planDetails.PricePerMonth.Value;
                else
                    newAmountPerUser = planDetails.PricePerMonthForYearlyOption.Value;

                var productId = "";
                var existingPrices = await _publicContext.StripeSubscriptionDetails
                    .Where(x => x.Application == app.ToString() && x.Plan == plan.Name).FirstOrDefaultAsync();
                if (existingPrices is null)
                    productId = await CreateStripeProduct(plan.Name);
                else
                    productId = existingPrices.ProductId;

                var priceDto = new CreateStripePriceDto
                {
                    ProductId = productId,
                    Application = app.ToString(),
                    NumberOfUsersToSubFor = subscription.SubscriptionFor.Value,
                    Amount = (int)newAmountPerUser,
                    Plan = plan.Name,
                    Interval = Enum.Parse<SubscriptionInterval>(subscription.Interval),
                    Currency = subscription.Currency
                };

                var priceId = await CreateStripePrice(priceDto);
                var response = await UpdateStripeSubscription(subscription, true, priceId);
                if (response)
                {
                    foreach (var permission in permissions)
                    {
                        permission.IsEnabled = false;
                        permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                    }

                    _context.AppPermissions.UpdateRange(permissions);
                    await _context.SaveChangesAsync();

                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "The user(s) have been removed successfully. Kindly note that your subscription has been updated accordingly and the new amount will take effect at the end of your current subscription contract.";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
            }

            return new GenericResponse
            {
                ResponseCode = "400",
                ResponseMessage = "Failed to remove users to subscription"
            };
        }
        #endregion

        #region Remove users from an AI subscription
        public async Task<GenericResponse> RemoveUsersFromAISubscription(RemoveOrAddUsersFromAISubReq model)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id == model.SubscriptionId);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");

            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
            var aiSubDetails = await _publicContext.AISubscriptionDetails
                .Where(x => x.SubscriptionId == subscription.Id && x.Agent == model.Agent).FirstOrDefaultAsync();
            if (aiSubDetails == null)
                throw new RecordNotFoundException("AI Subscription details not found");

            // Get the price per user for the AI subscription
            var planPricing = await _publicContext.AIAgents.FirstOrDefaultAsync(x => x.Agent == model.Agent.ToString());
            if (planPricing == null)
                throw new RecordNotFoundException("AI Subscription pricing not found");

            var oldAmount = aiSubDetails.Amount;
            aiSubDetails.NoOfUserSubscribedFor = aiSubDetails.NoOfUserSubscribedFor - model.NumberOfUsers;
            if (subscription.Interval == SubscriptionInterval.Monthly.ToString())
                aiSubDetails.Amount = (double)(aiSubDetails.NoOfUserSubscribedFor * planPricing.AmountPerMonthPerUser);
            else
                aiSubDetails.Amount = (double)(aiSubDetails.NoOfUserSubscribedFor * planPricing.AmountPerYearPerUser);

            if (model.NumberOfUsers > aiSubDetails.NoOfUserSubscribedFor)
                throw new DirtyFormException("You cannot remove more users than you have on your subscription");

            _publicContext.AISubscriptionDetails.Update(aiSubDetails);

            // Calculate the new amount to pay
            var amountToPay = subscription.Amount - oldAmount + aiSubDetails.Amount;
            subscription.SubscriptionFor = subscription.SubscriptionFor - model.NumberOfUsers;
            subscription.Amount = amountToPay;

            // Update user's app permissions - disable the app permissions for the users
            var permissions = await _publicContext.AppPermissions
                .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Agent == model.Agent && x.IsEnabled && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && model.UserIds.Contains(x.UserId)).ToListAsync();

            // Update companies subscription
            if (subscription.PaymentProvider == PaymentProviders.Mollie)
            {
                var response = await UpdateSubscription(subscription, true, subscription.PricingPlanId.ToString());
                if (response.Status == "active")
                {
                    foreach (var permission in permissions)
                    {
                        permission.IsEnabled = false;
                        permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                    }

                    _publicContext.AppPermissions.UpdateRange(permissions);
                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "The new number of users have been removed successfully. Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
            }
            else
            {
                var productId = "";
                var existingPrices = await _publicContext.StripeSubscriptionDetails
                    .Where(x => x.AIAgent && x.Plan == plan.Name).FirstOrDefaultAsync();
                if (existingPrices is null)
                    productId = await CreateStripeProduct(plan.Name);
                else
                    productId = existingPrices.ProductId;

                var priceDto = new CreateStripePriceDto
                {
                    ProductId = productId,
                    NumberOfUsersToSubFor = subscription.SubscriptionFor.Value,
                    Amount = (int)amountToPay,
                    Plan = plan.Name,
                    Interval = Enum.Parse<SubscriptionInterval>(subscription.Interval),
                    IsForAI = true
                };

                var priceId = await CreateStripePrice(priceDto);
                var response = await UpdateStripeSubscription(subscription, true, priceId);
                if (response)
                {
                    foreach (var permission in permissions)
                    {
                        permission.IsEnabled = false;
                        permission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();
                    }

                    _publicContext.AppPermissions.UpdateRange(permissions);
                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "The new number of users have been removed successfully. " +
                        "Kindly note that your subscription has been updated accordingly and the new amount will take effect on your next subscription";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
            }

            return new GenericResponse
            {
                ResponseCode = "400",
                ResponseMessage = "Failed to remove users to subscription"
            };
        }
        #endregion

        #region Activate Enterprise Subscription Manually
        /// <summary>
        /// Activates enterprise subscription manually in the event that the webhook fails to activate the subscription
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="InvalidOperationException"></exception>
        /// <exception cref="OperationFailedException"></exception>
        public async Task<GenericResponse> ActivateEnterpriseSubscriptionManually(string tenantId, Applications app)
        {
            var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenantId);
            if (tenant == null)
                throw new RecordNotFoundException("No company record found");

            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == app);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");

            if (subscription.Status == PaymentStatus.Successful.ToString())
                throw new InvalidOperationException("Subscription is already active");

            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
            var planDetails = await _publicContext.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == plan.Id);

            // Get enterprise plan payment details using the subscription payment id
            var entPayment = await _publicContext.EnterprizeSubscriptionPayments.FirstOrDefaultAsync(x => x.PaymentId == subscription.PaymentId);
            if (entPayment == null)
                throw new RecordNotFoundException("No enterprise payment record found. This is likely not an enterprise subscription");

            // If payment has been used and subscription has not been reactivated, check the payment date, if is recent, reactivate the subscription
            if (entPayment.PaymentUsed)
            {
                // Check if the entPayment is recent
                if (entPayment.PaymentDate.Value.AddDays(30)! < DateTime.UtcNow)
                {
                    subscription.Status = PaymentStatus.Successful.ToString();
                    subscription.ActivatedOn = DateTime.UtcNow;
                    subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                    subscription.UpdatedAt = DateTime.UtcNow;

                    _publicContext.Subscriptions.Update(subscription);
                }
            }
            else
            {
                subscription.Status = PaymentStatus.Successful.ToString();
                subscription.ActivatedOn = DateTime.UtcNow;
                subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                subscription.UpdatedAt = DateTime.UtcNow;

                _publicContext.Subscriptions.Update(subscription);
            }

            var res = await _publicContext.SaveChangesAsync();
            if (res < 1)
                throw new OperationFailedException("Failed to activate subscription");

            BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Active, subscription.UserId, subscription.Id.ToString(), tenant.Id.ToString(), subscription.TenantId.ToString(), subscription.Application.ToString(), entPayment.Id.ToString()));

            BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Active, ReasonForSubscriptionStatus.Activation));

            // Trigger backgraound task that will update subscription status to inactive after 30 days
            BackgroundJob.Enqueue<IBackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), tenant.Subdomain, null));

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Subscription has been activated successfully",
                Data = subscription
            };

        }
        #endregion

        #region Activate subscription manually
        /// <summary>
        /// This method is used to activate subscription manually in the event that the webhook fails to activate 
        /// the subscription even after payment has been made and was successful
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<GenericResponse> ActivateSubscriptionManually(string subdomain, Applications app)
        {
            var tenantId = await _publicContext.Tenants.Where(x => x.Subdomain == subdomain).Select(x => x.Id.ToString()).FirstOrDefaultAsync();
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == app);
            if (subscription == null)
                throw new RecordNotFoundException("No subscription record found");

            if (string.IsNullOrEmpty(subscription.SubscriptionId) && subscription.Status == PaymentStatus.Successful.ToString())
            {
                var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
                var planDetails = await _publicContext.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == plan.Id);
                subscription.Amount = (double)(subscription.SubscriptionFor * planDetails.PricePerMonth);

                // Create subscription on Mollie
                var response = await CreateSubscription(subscription);
                if (response.Status == "active")
                {
                    subscription.SubscriptionId = response.Id;
                    subscription.UpdatedAt = DateTime.UtcNow;

                    _publicContext.Subscriptions.Update(subscription);
                    var res = await _publicContext.SaveChangesAsync();

                    var message = "Your subscription has been activated successfully";
                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = message,
                        Data = response
                    };
                }
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to activate subscription",
                    Data = response
                };
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Subscription already activated",
                Data = subscription
            };
        }
        #endregion

        #region Initaite enterprize subscription payment link creation
        public async Task<GenericResponse> InitaiteEnterprizePlanPaymentLinkCreation(EnterprizeSubscriptionPaymentDto model)
        {
            User user = null;
            var withEmail = false;
            var withPhoneNumber = false;

            if (model.IsExistingUser)
            {
                user = await _publicContext.Users.FirstOrDefaultAsync(x => x.Email.ToLower() == model.PersonalEmail.ToLower());
                if (user == null)
                {
                    user = await _publicContext.Users.FirstOrDefaultAsync(x => x.PhoneNumber == model.PhoneNumber);
                    if (user != null)
                        withPhoneNumber = true;
                }
                else
                    withEmail = true;
            }

            // Validate that the phone number contains the country code
            if (!string.IsNullOrEmpty(model.PhoneNumber) && !model.PhoneNumber.Contains("+"))
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Please provide a valid phone number with the country code. Eg: +2347062746869 or +31622444648",
                    Data = false
                };

            if (model.Amount < 0.5 && !model.EnterprizeFreePlan)
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Amount cannot be less than 0.5",
                    Data = false
                };

            var enterprizePaymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                  .Where(x => x.CompanyEmail.ToLower() == model.CompanyEmail.ToLower() && x.Application == model.Application && !x.PaymentUsed && x.PaymentStatus == PaymentStatus.Pending)
                  .FirstOrDefaultAsync();
            if (enterprizePaymentDetails is null)
            {
                enterprizePaymentDetails = _mapper.Map<EnterprizeSubscriptionPayment>(model);
                enterprizePaymentDetails.CreatedBy = model.LoggedInUser;
                enterprizePaymentDetails.IsExistingUser = user != null;
                enterprizePaymentDetails.PersonalEmail = model.PersonalEmail;
                await _publicContext.EnterprizeSubscriptionPayments.AddAsync(enterprizePaymentDetails);
            }
            else
            {
                enterprizePaymentDetails.IsExistingUser = user != null;
                enterprizePaymentDetails.Amount = model.Amount;
                enterprizePaymentDetails.Currency = model.Currency;
                enterprizePaymentDetails.PhoneNumber = model.PhoneNumber;
                enterprizePaymentDetails.PersonalEmail = model.PersonalEmail;
                enterprizePaymentDetails.CompanyName = model.CompanyName;
                _publicContext.EnterprizeSubscriptionPayments.Update(enterprizePaymentDetails);

                //TODO: Send email with the incoice as an attachemnet to the user and also with a link to make the oayment
                // Add payment details Id to the url

                var url = Constants.FRONTEND_PAYMENT_URL + "?id=" + enterprizePaymentDetails.Id;
                var value = withEmail ? model.PersonalEmail : model.PhoneNumber;
                if (!model.EnterprizeFreePlan)
                {
                    // Prepare email template parameters
                    //await SendOutInvoiceMail(model, user, enterprizePaymentDetails, url);
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = user == null ? "Payment validation link was generated and sent successfully" : $"Payment validation link was generated and sent successfully. Note that an account with {value} already exists and the details is part of the response",
                    Data = new
                    {
                        PaymentDetailsId = enterprizePaymentDetails.Id,
                        URL = url,
                        UserDetails = user != null ? new
                        {
                            user.Id,
                            Username = user.UserName,
                            user.FirstName,
                            user.LastName,
                            user.MiddleName,
                            user.Email,
                            user.PhoneNumber
                        } : null
                    }
                };
            }

            var dbResult = await _publicContext.SaveChangesAsync() > 0;

            if (dbResult)
            {
                var url = Constants.FRONTEND_PAYMENT_URL + "?id=" + enterprizePaymentDetails.Id;

                if (!model.EnterprizeFreePlan)
                {
                    // Prepare email template parameters
                    //await SendOutInvoiceMail(model, user, enterprizePaymentDetails, url);
                }

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Payment validation link was generated and sent successfully",
                    Data = new
                    {
                        PaymentDetailsId = enterprizePaymentDetails.Id,
                        URL = url,
                        UserDetails = user != null ? new
                        {
                            user.Id,
                            Username = user.UserName,
                            user.FirstName,
                            user.LastName,
                            user.MiddleName,
                            user.Email,
                            user.PhoneNumber
                        } : null
                    }
                };
            }

            return new GenericResponse
            {
                ResponseMessage = "Failed",
                ResponseCode = "500",
            };
        }

        private async Task SendOutInvoiceMail(EnterprizeSubscriptionPaymentDto model, User user, EnterprizeSubscriptionPayment enterprizePaymentDetails, string url)
        {
            var parameters = new Dictionary<string, string>
                    {
                        { "[User Name]", user != null ? user.FirstName : model.CompanyName },
                        { "[no_users]", model.NoOfUser.ToString() ?? "" },
                        { "#", url }  // This replaces the href in the "Proceed To Pay" button
                    };

            // Prepare invoice PDF template parameters
            var invoiceParams = new Dictionary<string, string>
                    {
                        { "{invoice_no}", enterprizePaymentDetails.Id.ToString() },
                        { "{issue_date}", DateTime.UtcNow.ToString("dd MMMM yyyy") },
                        { "{client_name}", user != null ? $"{user.FirstName} {user.LastName}" : model.CompanyName },
                        { "{client_address}", model.Address ?? "N/A" },
                        { "{companu_name}", model.CompanyName },
                        { "{company_address}", model.Address },  // TODO: Get from config
                        { "{company_email}", model.CompanyEmail },  // TODO: Get from config
                        { "{company_website}", "www.joble.com" },    // TODO: Get from config
                        { "{currency}", model.Currency.ToString() },
                        { "{plan}", "Enterprise Plan" },
                        { "{interval}", model.Frequency.ToString() },
                        { "{number_of_users}", model.NoOfUser.ToString() ?? "" },
                        { "{formated_amount}", (model.Amount / model.NoOfUser).ToString("N2") },
                        { "{total_formatted_amount}", model.Amount.ToString("N2") },
                        { "{sub_total}", model.Amount.ToString("N2") },
                        { "{tax}", "0.00" },
                        { "{total_due}", model.Amount.ToString("N2") }
                    };

            var template = Extensions.UpdateTemplateWithParams("subscription/ent_invoice", _environment, parameters);
            var invoiceTemplate = Extensions.UpdateTemplateWithParams("subscription/invoice_pdf_attachment", _environment, invoiceParams);

            // Generate PDF from invoice template
            var pdfBytes = await _pdfService.GeneratePdfAsync(invoiceTemplate);

            // Convert the byte array to a memory stream
            var pdfStream = new MemoryStream(pdfBytes);

            var attachments = new List<AttachmentDto>
                    {
                        new AttachmentDto
                        {
                            attachment = pdfStream,
                            fileName = $"Invoice_{enterprizePaymentDetails.Id}.pdf",
                            fileType = "application/pdf"
                        }
                    };

            var email = user != null ? user.Email : model.CompanyEmail;
            BackgroundJob.Enqueue(() => _emailService.SendEmailWithAttachments(template, email, "Enterprise Plan Payment Link", attachments));
        }
        #endregion

        #region Make enterprize subscription payment
        public async Task<GenericResponse> MakeEnterprizePlanPayment(string paymentDetailsId, PaymentProviders provider)
        {
            var paymentLink = "";
            var paymentId = "";
            var enterprizePaymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                  .Where(x => x.Id.ToString() == paymentDetailsId)
                  .FirstOrDefaultAsync();
            if (enterprizePaymentDetails is null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment details found", };

            if (provider == PaymentProviders.Mollie)
            {
                PaymentRequest paymentRequest = new PaymentRequest()
                {
                    Amount = new Amount(enterprizePaymentDetails.Currency.ToString(), (decimal)enterprizePaymentDetails.Amount),
                    Description = $"Payment link for enterprize subscription. Company: {enterprizePaymentDetails.CompanyName} - Application: {enterprizePaymentDetails.Application}",
                    RedirectUrl = Constants.CLIENT_ADMIN_MOLLIE_REDIRECT_URL,
                    WebhookUrl = string.Format("{0}/subscription/webhook/verifyenterprisepayment", Constants.BACKEND_BASE_URL),
                    Locale = Locale.en_US,
                    Metadata = "Payment Link"
                };

                PaymentResponse paymentResponse = await _paymentClient.CreatePaymentAsync(paymentRequest, true);
                paymentLink = paymentResponse.Links.Checkout.Href;
                paymentId = paymentResponse.Id;
            }
            else
            {
                try
                {
                    // Create a product
                    var product = new ProductCreateOptions
                    {
                        Name = "Enterprize Subscription",
                    };
                    var productService = new ProductService();
                    var stripeProduct = productService.Create(product);

                    // Create price
                    var price = new PriceCreateOptions
                    {
                        Product = stripeProduct.Id,
                        UnitAmount = (long)enterprizePaymentDetails.Amount * 100,
                        Currency = enterprizePaymentDetails.Currency.ToString()
                    };
                    var priceService = new PriceService();
                    var stripePrice = priceService.Create(price);

                    // Create payment link
                    var paymentLK = new PaymentLinkCreateOptions
                    {
                        LineItems = new List<PaymentLinkLineItemOptions>
                        {
                            new PaymentLinkLineItemOptions { Price = stripePrice.Id, Quantity = 1 },
                        },
                        AfterCompletion = new PaymentLinkAfterCompletionOptions
                        {
                            Type = "redirect",
                            Redirect = new PaymentLinkAfterCompletionRedirectOptions
                            {
                                Url = Constants.ADMIN_URL,
                            },
                        },
                    };

                    var paymentLinkService = new PaymentLinkService();
                    var stripePaymentLink = paymentLinkService.Create(paymentLK);

                    paymentLink = stripePaymentLink.Url;
                    paymentId = stripePaymentLink.Id;
                }
                catch (StripeException ex)
                {
                    Console.WriteLine($"Stripe Exception: {ex.Message}");
                    return null;
                }
            }

            enterprizePaymentDetails.PaymentLink = paymentLink;
            enterprizePaymentDetails.PaymentId = paymentId;
            enterprizePaymentDetails.PaymentProvider = provider;
            _publicContext.EnterprizeSubscriptionPayments.Update(enterprizePaymentDetails);

            var dbResult = await _publicContext.SaveChangesAsync() > 0;

            if (dbResult)
            {
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Payment link was generated and sent successfully",
                    Data = new
                    {
                        enterprizePaymentDetails.PaymentId,
                        Link = enterprizePaymentDetails.PaymentLink
                    }
                };
            }

            return new GenericResponse
            {
                ResponseMessage = "Failed",
                ResponseCode = "500",
            };
        }
        #endregion

        #region Re-Generate enterprise subscription payment link
        /// <summary>
        /// Re-Generate enterprise subscription payment link
        /// </summary>
        /// <param name="id"></param>
        /// <param name="companyEmail"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        public async Task<GenericResponse> ReGenerateEnterprisePaymentLink(string id, string companyEmail, Applications? app)
        {
            EnterprizeSubscriptionPayment enterprizePaymentDetails = null;
            if (!string.IsNullOrEmpty(id))
            {
                enterprizePaymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                .Where(x => x.Id.ToString() == id && !x.PaymentUsed && !x.EnterprizeFreePlan)
                .FirstOrDefaultAsync();
            }
            else
            {
                if (string.IsNullOrEmpty(companyEmail) || app == null)
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "Both companyEmail and application is required" };

                enterprizePaymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                    .Where(x => x.CompanyEmail.ToLower() == companyEmail.ToLower() && x.Application == app && !x.PaymentUsed)
                    .FirstOrDefaultAsync();
            }

            if (enterprizePaymentDetails is null)
            {
                _logger.Information($"ReGenerateEnterprisePaymentLink: No payment details found - {id}");
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment details found", };
            }
            else
            {
                //TODO: Send email with the incoice as an attachemnet to the user and also with a link to make the oayment
                // Add payment details Id to the url

                var url = Constants.FRONTEND_AUTH_URL + "?" + enterprizePaymentDetails.Id;

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Payment link was generated and sent successfully",
                    Data = new
                    {
                        PaymentDetailsId = enterprizePaymentDetails.Id,
                        URL = url
                    }
                };
            }
        }
        #endregion

        #region Initiate enterprise subscription payment for renewal
        public async Task<GenericResponse> InitiateEnterpriseSubPaymentForRenewal(string tenantId, Applications app)
        {
            var enterprizePaymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                  .Where(x => x.TenantId.Value.ToString() == tenantId && x.Application == app && !x.EnterprizeFreePlan)
                  .FirstOrDefaultAsync();
            if (enterprizePaymentDetails is null)
            {
                _logger.Information($"InitiateEnterpriseSubPaymentForRenewal: No payment details found - {tenantId}");
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "No previous payment details found", };
            }
            else
            {
                var newPaymentDetails = new EnterprizeSubscriptionPayment
                {
                    Amount = enterprizePaymentDetails.Amount,
                    Application = enterprizePaymentDetails.Application,
                    CompanyEmail = enterprizePaymentDetails.CompanyEmail,
                    CompanyName = enterprizePaymentDetails.CompanyName,
                    Currency = enterprizePaymentDetails.Currency,
                    PaymentLink = enterprizePaymentDetails.PaymentLink,
                    PaymentStatus = PaymentStatus.Pending,
                    PaymentUsed = false,
                    TenantId = enterprizePaymentDetails.TenantId,
                    UpdatedOn = DateTime.UtcNow,
                };

                await _publicContext.EnterprizeSubscriptionPayments.AddAsync(newPaymentDetails);
                var dbResult = await _publicContext.SaveChangesAsync() > 0;

                if (dbResult)
                {
                    //TODO: Send email with the incoice as an attachemnet to the user and also with a link to make the oayment
                    // Add payment details Id to the url

                    var url = Constants.FRONTEND_AUTH_URL + "?id=" + enterprizePaymentDetails.Id + "&isForRenewal=yes";

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Payment link was generated and sent successfully",
                        Data = new
                        {
                            PaymentDetailsId = enterprizePaymentDetails.Id,
                            URL = url
                        }
                    };
                }
            }

            return new GenericResponse
            {
                ResponseMessage = "Failed",
                ResponseCode = "500",
            };
        }
        #endregion

        #region Get Enterprise Payment Details
        public async Task<GenericResponse> GetEnterprisePaymentDetails(string companyEmail, Applications app)
        {
            User user = null;
            var paymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                .Where(x => x.CompanyEmail.ToLower() == companyEmail.ToLower() && x.Application == app && !x.PaymentUsed)
                .FirstOrDefaultAsync();

            if (paymentDetails == null)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment details found", };

            if (paymentDetails.PaymentStatus == PaymentStatus.Pending)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Payment is still pending. Please contact the customer to complete the payment.", Data = paymentDetails };

            if (paymentDetails.PaymentStatus == PaymentStatus.Failed)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Payment failed. Please contact the customer to re-try the payment.", Data = paymentDetails };

            if (paymentDetails.PaymentStatus == PaymentStatus.Cancelled)
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Payment was cancelled.", Data = paymentDetails };

            // Check if existingUser is true and get user details if its true
            if (paymentDetails.IsExistingUser)
            {
                user = await _publicContext.Users.FirstOrDefaultAsync(u => u.Email.ToLower() == paymentDetails.PersonalEmail.ToLower());
                if (user == null)
                    user = await _publicContext.Users.FirstOrDefaultAsync(u => u.PhoneNumber == paymentDetails.PhoneNumber);
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = new
                {
                    PaymentDetails = paymentDetails,
                    UserDetails = user != null ? new
                    {
                        user.Id,
                        Username = user.UserName,
                        user.FirstName,
                        user.LastName,
                        user.MiddleName,
                        user.Email,
                        user.PhoneNumber
                    } : null
                }
            };
        }
        #endregion

        #region Create an enterprise subscription for a new company
        public async Task<GenericResponse> CreateEnterpriseSubscriptionForNewCompany(EnterpriseSubscriptionDto model)
        {
            EnterprizeSubscriptionPayment? paymentDetails = null;

            // Check that payment have been made
            if (!model.EnterPriseOptions.CreateWithOutPayment)
            {
                paymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                    .FirstOrDefaultAsync(x => x.PaymentId == model.EnterPriseOptions.PaymentId && !x.PaymentUsed && x.CompanyEmail.ToLower() == model.Email.ToLower() && x.Application == model.EnterPriseOptions.Application);
                if (paymentDetails == null)
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment has been made. Either the paymentId supplied is wrong, or it has been used or is not for this company and application or the customer is yet to make payment. Contact the customer if payment id supplied is correct." };

                if (paymentDetails.PaymentStatus != PaymentStatus.Successful)
                {
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "Payment has not been made." };
                }
            }

            if (model.EnterPriseOptions.Application == Applications.JobPays || model.EnterPriseOptions.Application == Applications.JobID)
            {
                throw new OperationFailedException($"Application: {model.EnterPriseOptions.Application.ToString()} not supported !!");
            }

            // Method to Convert base64 of the logo to an IFormfile object
            IFormFile logo = null;
            if (model.Logo != null)
                logo = ConvertBase64ToFile(model.Logo);

            // Create the company and the super admin user if not already created
            var password = GenerateRandomPassword(15);
            var createCompanyDto = new RegisterTenantVM
            {
                companyAdmin = new CompanyAdminModel
                {
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    Email = model.Email,
                    PersonalEmail = model.PersonalEmail,
                    PhoneNumber = model.PhoneNumber,
                    Password = password,
                    ConfirmPassword = password,
                    MiddleName = model.MiddleName,
                    Designation = model.Designation
                },
                tenant = new TenantRegistrationVM
                {
                    CompanyName = model.CompanyName,
                    CompanyType = model.CompanyType,
                    Country = model.Country,
                    Subdomain = model.Domain,
                    ContactNo = model.PhoneNumber,
                    Application = model.EnterPriseOptions.Application,
                    Industry = model.Industry,
                    SecDomains = model.SecDomains
                },
                Logo = logo,
                ForEnterprizePlan = true
            };

            var response = await _tenantService.RegisterTenant(createCompanyDto);
            if (response.ResponseCode != "200")
                return response;

            var responseData = response.Data as CompanyCreationResponseDto;

            model.EnterPriseOptions.TenantId = responseData.TenantId;
            var enterPrizeCreationRes = await CreateEnterpriseSubscription(model.EnterPriseOptions);

            // Update the IsExistingUser field of EnterprizeSubscriptionPayments table to true
            if (!model.EnterPriseOptions.CreateWithOutPayment)
            {
                paymentDetails.IsExistingUser = true;
                _publicContext.EnterprizeSubscriptionPayments.Update(paymentDetails);
                await _publicContext.SaveChangesAsync();
            }

            return enterPrizeCreationRes;
        }
        #endregion

        #region Create an enterprise subscription for a new company with an existing user
        public async Task<GenericResponse> CreateEnterpriseSubscriptionForNewCompanyWithExistingUser(EnterpriseSubscriptionForExistingUserDto model)
        {
            // Check if user exist
            var user = await _publicContext.Users.FirstOrDefaultAsync(x => x.Id == model.UserId);
            if (user == null)
                throw new RecordNotFoundException("User does not exist");

            // Check that payment have been made
            if (!model.EnterPriseOptions.CreateWithOutPayment)
            {
                var paymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                    .FirstOrDefaultAsync(x => x.PaymentId == model.EnterPriseOptions.PaymentId && !x.PaymentUsed && x.CompanyEmail.ToLower() == model.Email.ToLower() && x.Application == model.EnterPriseOptions.Application);
                if (paymentDetails == null)
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment has been made. Either the paymentId supplied is wrong, or it has been used or is not for this company and application or the customer is yet to make oayment. Contact the customer if payment id supplied is correct." };

                if (paymentDetails.PaymentStatus != PaymentStatus.Successful)
                {
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "Payment has not been made." };
                }
            }

            //if (model.EnterPriseOptions.Application == Applications.JobPays || model.EnterPriseOptions.Application == Applications.JobID)
            //{
            //    throw new OperationFailedException($"Application: {model.EnterPriseOptions.Application} not supported !!");
            //}

            var adminPhoneNumber = await _publicContext.Users.Where(u => u.Id == model.UserId)
                .Select(x => x.PhoneNumber).FirstOrDefaultAsync();

            // Method to Convert base64 of the logo to an IFormfile object
            IFormFile logo = null;
            if (model.Logo != null)
                logo = ConvertBase64ToFile(model.Logo);

            var createCompanyDto = new CreateTenantForExistingUserVM
            {
                CompanyEmail = model.Email,
                UserId = model.UserId,
                ForEnterprizePlan = true,
                Logo = logo,
                Tenant = new TenantRegistrationVM
                {
                    CompanyName = model.CompanyName,
                    CompanyType = model.CompanyType,
                    Country = model.Country,
                    Subdomain = model.Domain,
                    ContactNo = adminPhoneNumber,
                    Application = model.EnterPriseOptions.Application,
                    Industry = model.Industry,
                    SecDomains = model.SecDomains
                }
            };

            var response = await _tenantService.CreateCompanyForExistingUser(createCompanyDto);
            if (response.ResponseCode != "200")
                throw new OperationFailedException(response.ResponseMessage);
            var responseData = response.Data as CompanyCreationResponseDto;

            model.EnterPriseOptions.TenantId = responseData.TenantId;
            return await CreateEnterpriseSubscription(model.EnterPriseOptions);
        }
        #endregion

        #region Activate, Deactivate or Reactivate an Enterprise plan
        public async Task<GenericResponse> ActivateDeactivateReactivateEntPlan(bool activationStatus, string tenantId, Applications app, string enterPriseSubId, DateTime? expiresOn)
        {
            var company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenantId);
            if (company is null)
                throw new RecordNotFoundException("Company not found");

            var getEnterprisePlan = await _publicContext.EnterpriseSubscriptions
                .FirstOrDefaultAsync(x => x.Id.ToString() == enterPriseSubId && x.TenantId.ToString() == tenantId || x.IsDeleted == false);
            if (getEnterprisePlan is null)
                throw new RecordNotFoundException("Enterprise plan not found");

            await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
            var noOfEmployees = (await _context.AppPermissions.ToListAsync()).Count;
            if (activationStatus && getEnterprisePlan.UsersLimit != 0 && getEnterprisePlan.UsersLimit < noOfEmployees)
                throw new OperationFailedException($"The number of users - {noOfEmployees} in the company is above the number of users - {getEnterprisePlan.UsersLimit} paid for under the plan. kindly increase the number of users or reduce the number of user in the company.");

            // Get subscription and update
            var subscription = await _publicContext.Subscriptions
                .FirstOrDefaultAsync(sub => sub.Id == getEnterprisePlan.SubscriptionId);
            subscription.ActivatedOn = DateTime.UtcNow;

            if (expiresOn is not null && activationStatus)
                subscription.ExpiresOn = expiresOn;
            if (activationStatus)
                subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
            if (!activationStatus)
                subscription.ExpiresOn = DateTime.UtcNow;
            subscription.Status = activationStatus ? PaymentStatus.Successful.ToString() : PaymentStatus.Failed.ToString();
            _publicContext.Subscriptions.Update(subscription);

            // Update companySubcription table
            var companySub = await _publicContext.CompanySubscriptions
                .FirstOrDefaultAsync(c => c.TenantId == company.Id && c.Application == app);
            if (companySub is not null)
            {
                companySub.Status = activationStatus ? SubscriptionStatus.Active : SubscriptionStatus.Inactive;
                companySub.UpdatedAt = subscription.UpdatedAt;
                _publicContext.CompanySubscriptions.Update(companySub);
            }

            // Update user's app permissions - disable the app permissions for the users
            var permissions = await _context.AppPermissions.Where(x => x.TenantId == company.Id.ToString() && x.Application == app.ToString() && x.IsEnabled == true).ToListAsync();
            foreach (var permission in permissions)
            {
                permission.SubscriptionStatus = activationStatus ? SubscriptionStatus.Active.ToString() : SubscriptionStatus.Inactive.ToString();
            }

            if (permissions.Count != 0)
                _context.AppPermissions.UpdateRange(permissions);
            await _context.SaveChangesAsync();
            var res = await _publicContext.SaveChangesAsync();

            var statusForEvent = activationStatus ? SubscriptionStatusForRMQ.Active : SubscriptionStatusForRMQ.Inactive;
            var reasonForEvent = activationStatus ? ReasonForSubscriptionStatus.Activation : ReasonForSubscriptionStatus.Deactivation;
            BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), statusForEvent, reasonForEvent));

            // Trigger backgraound task that will update subscription status to inactive after 30 days/ 1 year
            if (activationStatus)
                BackgroundJob.Enqueue<IBackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), company.Subdomain, null));

            return new GenericResponse
            {
                ResponseCode = res > 0 ? "200" : "500",
                ResponseMessage = res > 0 ? "Success" : "Failed",
                Data = res > 0
            };
        }
        #endregion

        #region Edit/Update an enterprise subscription
        public async Task<GenericResponse> EditEnterpriseSubscription(UpdateEnterprisePalnDto model)
        {
            var company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == model.TenantId);
            if (company is null)
                throw new RecordNotFoundException("Company not found");

            var getEnterprisePlan = await _publicContext.EnterpriseSubscriptions
                .FirstOrDefaultAsync(x => x.Id.ToString() == model.EnterPriseSubId && x.TenantId.ToString() == model.TenantId && x.IsDeleted == false);
            if (getEnterprisePlan is null)
                throw new RecordNotFoundException("Enterprise plan not found");

            getEnterprisePlan.UpdatedOn = DateTime.UtcNow;
            getEnterprisePlan.UpdatedBy = model.LoggedInUserId;
            getEnterprisePlan.ActivityLogHistoryLimit = model.ActivityLogHistoryLimit;
            getEnterprisePlan.CalenderLimit = model.CalenderLimit;

            if (model.TimeSheetManagement is not null)
                getEnterprisePlan.TimeSheetManagement = model.TimeSheetManagement.Value;
            getEnterprisePlan.InternalCommunicationHistoryLimit = model.InternalCommunicationHistoryLimit;
            getEnterprisePlan.StorageLimit = model.StorageLimit;
            getEnterprisePlan.UsersLimit = model.UsersLimit;
            getEnterprisePlan.ProjectLimit = model.ProjectLimit;

            if (model.AiAssistants is not null)
                getEnterprisePlan.AiAssistant = model.AiAssistants.Value;

            getEnterprisePlan.Application = model.Application;

            if (model.DataRetentionPeriodInMonth is not null)
                getEnterprisePlan.DataRetentionPeriodInMonth = model.DataRetentionPeriodInMonth.Value;
            if (model.ActivatedOn is not null)
                getEnterprisePlan.ActivatedOn = model.ActivatedOn.Value;
            if (model.ExpiresOn is not null)
                getEnterprisePlan.ExpiresOn = model.ExpiresOn.Value;

            getEnterprisePlan.PaymentProvider = model?.Provider ?? getEnterprisePlan.PaymentProvider;

            _publicContext.EnterpriseSubscriptions.Update(getEnterprisePlan);

            // Get subscription and update
            var subscription = await _publicContext.Subscriptions
                .FirstOrDefaultAsync(sub => sub.Id == getEnterprisePlan.SubscriptionId);
            subscription.UpdatedAt = DateTime.UtcNow;
            subscription.SubscriptionFor = model.UsersLimit;
            subscription.Amount = model.AmountPaid;
            subscription.Application = model.Application;
            subscription.PaymentProvider = model.Provider ?? subscription.PaymentProvider;

            if (model.ActivatedOn is not null)
                subscription.ActivatedOn = model.ActivatedOn.Value;
            if (model.ExpiresOn is not null)
                subscription.ExpiresOn = model.ExpiresOn.Value;

            _publicContext.Subscriptions.Update(subscription);

            //Update subscription history
            var subscriptionHistory = _publicContext.SubscriptionHistory
                .Where(sub => sub.TenantId == getEnterprisePlan.TenantId && sub.Application == getEnterprisePlan.Application)
                .OrderByDescending(x => x.CreatedAt)
                .First();

            subscriptionHistory.UpdatedAt = DateTime.UtcNow;
            subscriptionHistory.SubscriptionFor = model.UsersLimit;
            subscriptionHistory.Amount = model.AmountPaid;
            subscriptionHistory.Application = model.Application;
            subscriptionHistory.PaymentProvider = model.Provider ?? subscriptionHistory.PaymentProvider;

            if (model.ActivatedOn is not null)
                subscriptionHistory.ActivatedOn = model.ActivatedOn.Value;
            if (model.ExpiresOn is not null)
                subscriptionHistory.ExpiresOn = model.ExpiresOn.Value;

            _publicContext.SubscriptionHistory.Update(subscriptionHistory);

            var res = await _publicContext.SaveChangesAsync();
            var responseCode = res > 0 ? "200" : "500";

            // Update the hangfire job if the ExpiresOn is updated
            if (model.ExpiresOn is not null && model.ExpiresOn.Value != getEnterprisePlan.ExpiresOn)
            {
                var oldJob = await _publicContext.BackGroundJobIds
                    .FirstOrDefaultAsync(x => x.EventId == getEnterprisePlan.SubscriptionId.ToString());

                if (oldJob != null)
                {
                    BackgroundJob.Delete(oldJob.JobId);

                    // Trigger a background job that will update subscription status a day after expiration date
                    BackgroundJob.Schedule<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Inactive, null, subscription.Id.ToString(), company.Subdomain, company.Id.ToString(), model.Application.ToString(), null), model.ExpiresOn.Value.AddDays(1));
                }
            }


            var message = "Your enterprise subscription has been modified successfully.";
            var errorMessage = "Enterprise subscription was not modified successfully";
            return new GenericResponse
            {
                ResponseCode = responseCode,
                ResponseMessage = res > 0 ? message : errorMessage,
                Data = res > 0
            };
        }
        #endregion

        #region Cancel or Delete an enterprise subscription
        public async Task<GenericResponse> DeleteEnterpriseSubscription(string tenentId, string userId, string enterPriseSubId)
        {
            var company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenentId);
            if (company is null)
                throw new RecordNotFoundException("Company not found");

            var getEnterprisePlan = await _publicContext.EnterpriseSubscriptions
                .FirstOrDefaultAsync(x => x.Id.ToString() == enterPriseSubId && x.TenantId.ToString() == tenentId && x.IsDeleted == false);
            if (getEnterprisePlan is null)
                throw new RecordNotFoundException("Enterprise plan not found");

            getEnterprisePlan.DeletedOn = DateTime.UtcNow;
            getEnterprisePlan.DeletedBy = userId;
            getEnterprisePlan.IsDeleted = true;
            _publicContext.EnterpriseSubscriptions.Update(getEnterprisePlan);

            // Cancel subscription from the subscription table
            var subscription = await _publicContext.Subscriptions
                .FirstOrDefaultAsync(sub => sub.Id == getEnterprisePlan.SubscriptionId);
            subscription.UpdatedAt = DateTime.UtcNow;
            subscription.IsCancelled = true;
            _publicContext.Subscriptions.Update(subscription);

            var res = await _publicContext.SaveChangesAsync();

            var responseCode = res > 0 ? "200" : "500";

            var message = "Your enterprise subscription has been cancelled successfully.";
            var errorMessage = "Enterprise subscription was not cancelled successfully";
            return new GenericResponse
            {
                ResponseCode = res > 0 ? "200" : "500",
                ResponseMessage = res > 0 ? message : errorMessage,
                Data = res > 0
            };
        }
        #endregion

        #region Get all enterprise subscriptions

        //public async Task<Page<GetEnterpriseSubscriptionDto>> GetEnterpriseSubscriptions(PaginationParameters parameters, string? searchKeyword)
        //{
        //    var enterpriseSubscriptionsWithTenant = _publicContext.EnterpriseSubscriptions.Where(s => !s.IsDeleted)
        //    .Join(_publicContext.Tenants,
        //        subscription => subscription.TenantId,
        //        tenant => tenant.Id,
        //        (subscription, tenant) => new
        //        {
        //            Subscription = subscription,
        //            Tenant = tenant
        //        })
        //    .Select(x => new GetEnterpriseSubscriptionDto
        //    {
        //        AdminId = x.Tenant.AdminId,
        //        Application = x.Subscription.Application,
        //        EnterpriseSubId = x.Subscription.Id,
        //        TenantId = x.Subscription.TenantId,
        //        AIAssistance = x.Subscription.AiAssistant,
        //        DateRegistered = x.Subscription.CreatedOn,
        //        CommunicationHistoryInMonth = x.Subscription.InternalCommunicationHistoryLimit,
        //        StorageInGigaByte = x.Subscription.StorageLimit,
        //        MaxNumberofUser = x.Subscription.UsersLimit,
        //        ProjectCreation = x.Subscription.ProjectLimit,
        //        UnlimitedActivityLog = x.Subscription.ActivityLogHistoryLimit == 1 ? true : false,
        //        CompanyName = x.Tenant.CompanyName,
        //        CompanyType = x.Tenant.CompanyType,
        //        Location = x.Tenant.Country,
        //        TimeSheetManagement = x.Subscription.TimeSheetManagement,
        //        IsNumberOfUserUnlimited = x.Subscription.UsersLimit > 0 ? false : true,
        //        IsStorageUnlimited = x.Subscription.StorageLimit > 0 ? false : true,
        //        KeepCommunicationForever = x.Subscription.InternalCommunicationHistoryLimit > 0 ? false : true,
        //        KeepProjectCreationForever = x.Subscription.ProjectLimit > 0 ? false : true,
        //        KeepDataRetentionForever = x.Subscription.DataRetentionPeriodInMonth > 0 ? false : true,
        //        DataRetentionPeriodInMonth = x.Subscription.DataRetentionPeriodInMonth
        //    }).OrderByDescending(x => x.DateRegistered);

        //    Page<GetEnterpriseSubscriptionDto> response = null;
        //    if (!string.IsNullOrWhiteSpace(searchKeyword))
        //        response = await ApplySearchKeyword(enterpriseSubscriptionsWithTenant, searchKeyword).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        //    else
        //        response = await enterpriseSubscriptionsWithTenant.ToPageListAsync(parameters.PageNumber, parameters.PageSize);

        //    // Get the admin email from userCompany table
        //    response.Items = (GetEnterpriseSubscriptionDto[])response.Items.Distinct();
        //    foreach (var enterpriseSubscription in response.Items)
        //    {
        //        var adminEmail = await _publicContext.UserCompanies
        //            .Where(x => x.TenantId == enterpriseSubscription.TenantId && x.UserId == enterpriseSubscription.AdminId)
        //            .Select(x => x.Email).FirstOrDefaultAsync();
        //        enterpriseSubscription.AdminEmail = adminEmail;
        //    }

        //    return response;
        //}

        public async Task<Page<GetEnterpriseSubscriptionDto>> GetEnterpriseSubscriptions(PaginationParameters parameters, string searchKeyword)
        {
            var enterpriseSubscriptions = await _publicContext.EnterpriseSubscriptions
                .Join(_publicContext.Tenants,
                    subscription => subscription.TenantId,
                    tenant => tenant.Id,
                    (subscription, tenant) => new
                    {
                        Subscription = subscription,
                        Tenant = tenant
                    })
                .ToListAsync();

            var groupedSubscriptions = enterpriseSubscriptions
                .GroupBy(x => x.Subscription.TenantId)
                 .Where(g => !g.Any(x => x.Subscription.IsDeleted))
                .Select(g => g.OrderByDescending(x => x.Subscription.CreatedOn).FirstOrDefault())
                .Where(x => !x.Subscription.IsDeleted).ToList();

            var enterpriseSubscriptionsWithDto = groupedSubscriptions
                .Select(x => new GetEnterpriseSubscriptionDto
                {
                    AdminId = x.Tenant.AdminId,
                    Application = x.Subscription.Application,
                    EnterpriseSubId = x.Subscription.Id,
                    TenantId = x.Subscription.TenantId,
                    SubscriptionId = x.Subscription.SubscriptionId.ToString(),
                    AIAssistance = x.Subscription.AiAssistant,
                    DateRegistered = x.Subscription.CreatedOn,
                    CommunicationHistoryInMonth = x.Subscription.InternalCommunicationHistoryLimit,
                    StorageInGigaByte = x.Subscription.StorageLimit,
                    MaxNumberofUser = x.Subscription.UsersLimit,
                    ProjectCreation = x.Subscription.ProjectLimit,
                    UnlimitedActivityLog = x.Subscription.ActivityLogHistoryLimit == 1 ? true : false,
                    CompanyName = x.Tenant.CompanyName,
                    CompanyType = x.Tenant.CompanyType,
                    Location = x.Tenant.Country,
                    TimeSheetManagement = x.Subscription.TimeSheetManagement,
                    IsNumberOfUserUnlimited = x.Subscription.UsersLimit > 0 ? false : true,
                    IsStorageUnlimited = x.Subscription.StorageLimit > 0 ? false : true,
                    KeepCommunicationForever = x.Subscription.InternalCommunicationHistoryLimit > 0 ? false : true,
                    KeepProjectCreationForever = x.Subscription.ProjectLimit > 0 ? false : true,
                    KeepDataRetentionForever = x.Subscription.DataRetentionPeriodInMonth > 0 ? false : true,
                    DataRetentionPeriodInMonth = x.Subscription.DataRetentionPeriodInMonth,
                    Status = GetEntSubStatus(x.Subscription.SubscriptionId.ToString()).Result
                })
                .AsQueryable();


            if (!string.IsNullOrWhiteSpace(searchKeyword))
            {
                enterpriseSubscriptionsWithDto = ApplySearchKeyword(enterpriseSubscriptionsWithDto, searchKeyword);
            }

            var pagedResult = enterpriseSubscriptionsWithDto.ToPageList(parameters.PageNumber, parameters.PageSize);

            foreach (var enterpriseSubscription in pagedResult.Items)
            {
                var adminEmail = await _publicContext.UserCompanies
                    .Where(x => x.TenantId == enterpriseSubscription.TenantId && x.UserId == enterpriseSubscription.AdminId)
                    .Select(x => x.Email)
                    .FirstOrDefaultAsync();

                enterpriseSubscription.AdminEmail = adminEmail;
            }

            return pagedResult;
        }

        private async Task<string> GetEntSubStatus(string subscriptionId)
        {
            var status = "";
            var subscription = await _publicContext.Subscriptions
                .Where(x => x.Id.ToString() == subscriptionId)
                .FirstOrDefaultAsync();

            if (subscription is not null)
            {
                status = subscription.Status == PaymentStatus.Successful.ToString() ? SubscriptionStatus.Active.ToString() : SubscriptionStatus.Inactive.ToString();
            }
            else
            {
                status = SubscriptionStatus.Inactive.ToString();
            }

            return status;
        }

        #endregion

        #region Create an enterprise subscription for an existing company
        public async Task<GenericResponse> CreateEnterpriseSubForExistingCompany(EnterPriseOptionsDto model)
        {
            var company = await _publicContext.Tenants
                .Where(x => x.Id.ToString() == model.TenantId).FirstOrDefaultAsync();

            await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
            var noOfEmployees = (await _context.AppPermissions.ToListAsync()).Count;
            if (model.UsersLimit != 0 && model.UsersLimit < noOfEmployees)
                throw new OperationFailedException($"The number of users - {noOfEmployees} in the company is above the number of users - {model.UsersLimit} paid for under the plan. kindly increase the number of users or reduce the number of user in the company.");

            if (!string.IsNullOrWhiteSpace(model.SubscriptionId))
            {
                var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == model.TenantId && x.Application == model.Application);
                if (subscription is null)
                    throw new RecordNotFoundException("Subscription not found");

                var oldPlan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
                if (oldPlan.Name == PricingPlans.Enterprise.ToString())
                    throw new InvalidOperationException("You are already on Enterprise plan. Kindly update or reach out to our admin to activate/reactivate your plan");

                // Cancel the subscription on mollie/stripe
                var cancelRes = await CancelSubscription(subscription.Id.ToString(), false, company.Subdomain, true);
                if (cancelRes.ResponseCode != "200")
                    throw new OperationFailedException("Failed to cancel existing subscription");

                subscription.PricingPlanId = Guid.Parse(model.PlanId);
                subscription.SubscriptionFor = model.UsersLimit;
                subscription.Amount = model.AmountPaid;
                subscription.Status = PaymentStatus.Successful.ToString();
                subscription.ActivatedOn = model.ActivatedOn ?? DateTime.UtcNow;
                subscription.ExpiresOn = model.ExpiresOn;
                subscription.TransactionDate = model.TransactionDate;
                subscription.Currency = model.Currency;
                subscription.TransactionCode = model.TransactionCode;
                subscription.UpdatedAt = DateTime.UtcNow;
                subscription.PaymentProvider = model.Provider;

                var subscriptionHistory = _mapper.Map<SubscriptionHistory>(subscription);
                subscriptionHistory.Id = Guid.NewGuid();
                await _publicContext.SubscriptionHistory.AddAsync(subscriptionHistory);
                _publicContext.Subscriptions.Update(subscription);

                var enterpriseSub = _mapper.Map<EnterpriseSubscription>(model);
                enterpriseSub.SubscriptionId = subscription.Id;
                enterpriseSub.CreatedBy = model.LoggedInUserId;
                await _publicContext.EnterpriseSubscriptions.AddAsync(enterpriseSub);

                // Update companySubcription table
                var companySub = await _publicContext.CompanySubscriptions
                    .FirstOrDefaultAsync(c => c.TenantId == company.Id && c.Application == model.Application && c.Status != SubscriptionStatus.Active);
                if (companySub is not null)
                {
                    companySub.Status = SubscriptionStatus.Active;
                    companySub.UpdatedAt = subscription.UpdatedAt;
                    _publicContext.CompanySubscriptions.Update(companySub);
                }

                // Update user's app permissions - disable the app permissions for the users
                var permissions = await _context.AppPermissions.Where(x => x.TenantId == company.Id.ToString() && x.Application == model.Application.ToString() && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Inactive.ToString()).ToListAsync();
                foreach (var permission in permissions)
                {
                    permission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                }

                if (permissions.Count != 0)
                    _context.AppPermissions.UpdateRange(permissions);
                await _context.SaveChangesAsync();
                var res = await _publicContext.SaveChangesAsync();

                // Trigger a background job that will update subscription status a day after expiration date
                var jobId = BackgroundJob.Schedule<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Inactive, null, subscription.Id.ToString(), company.Subdomain, company.Id.ToString(), model.Application.ToString(), null), model.ExpiresOn.AddDays(1));
                if (!string.IsNullOrEmpty(jobId))
                {
                    await SaveBackgroundJobId(enterpriseSub.SubscriptionId.ToString(), jobId);
                }

                var responseCode = res > 0 ? "200" : "500";

                // Publish a subscription event
                BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.New, null));

                // Send out notification
                await SendPaidPlanNotification(responseCode, subscription);

                var message = "Your enterprise subscription has been activated successfully.";
                var errorMessage = "Enterprise subscription was not activated successfully";
                return new GenericResponse
                {
                    ResponseCode = responseCode,
                    ResponseMessage = res > 0 ? message : errorMessage,
                    Data = res > 0
                };
            }
            else
                return await CreateEnterpriseSubscription(model);
        }
        #endregion

        #region Mark an application as favorite
        public async Task<GenericResponse> MakeApplicationFavorite(Applications app, string userId, string subdomain)
        {
            var res = 0;
            var response = new GenericResponse();
            var tenantId = string.Empty;
            if (!string.IsNullOrEmpty(subdomain) && subdomain != "api")
                tenantId = await _publicContext.Tenants.Where(x => x.Subdomain == subdomain).Select(x => x.Id.ToString()).FirstOrDefaultAsync();

            await using var _context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            if (!string.IsNullOrEmpty(tenantId))
            {
                var appPermission = await _context.AppPermissions.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Application == app.ToString() && x.UserId == userId);
                if (appPermission is not null)
                {
                    appPermission.MakeFavorite = true;
                    _context.AppPermissions.Update(appPermission);
                    res = await _context.SaveChangesAsync();
                }
                else
                {
                    response.ResponseMessage = "You do not have permission for this application";
                    response.ResponseCode = "404";
                }
            }
            else
            {
                var appPermission = await _publicContext.AppPermissions.FirstOrDefaultAsync(x => x.Application == app.ToString() && x.UserId == userId);
                if (appPermission is not null)
                {
                    appPermission.MakeFavorite = true;
                    _publicContext.AppPermissions.Update(appPermission);
                    res = await _publicContext.SaveChangesAsync();
                }
                else
                {
                    response.ResponseMessage = "You do not have permission for this application";
                    response.ResponseCode = "404";
                }
            }

            if (res > 0)
            {
                response.ResponseMessage = "Application marked as favorite";
                response.ResponseCode = "200";
            }
            else
            {
                response.ResponseMessage = "Application was not marked as favorite";
                response.ResponseCode = "500";
            }

            return response;
        }
        #endregion

        #region Add a user review and rating
        public async Task<bool> AddUserReviewAndRating(AddFeedbackReviewsAndRatingsDto model)
        {
            var userReview = _mapper.Map<FeedbackReviewsAndRatings>(model);
            await _publicContext.FeedbackReviewsAndRatings.AddAsync(userReview);
            var res = await _publicContext.SaveChangesAsync();

            return res > 0;
        }
        #endregion

        #region Stripe Enterprise Webhook
        public async Task<GenericResponse> HandleEnterpriseStripeEvents(string json, string signature)
        {
            var res = new GenericResponse()
            {
                ResponseCode = "200",
                ResponseMessage = "None of the events being listened to were triggered",
                Data = true
            };

            res.Data = false;
            Session session = null;
            Models.Subscription subscription = null;
            var company = new Tenant.Model.Tenant();

            Event stripeEvent = EventUtility.ConstructEvent(
                   json,
                   signature,
                   _options.Value.WebhookSecret,
                   throwOnApiVersionMismatch: false
                );

            if (stripeEvent.Type == "checkout.session.completed" || stripeEvent.Type == "checkout.session.async_payment_succeeded")
            {
                session = stripeEvent.Data.Object as Session;
                var entPayment = await _publicContext.EnterprizeSubscriptionPayments.FirstOrDefaultAsync(x => x.PaymentId == session.PaymentLink.Id);
                if (entPayment == null)
                {
                    _logger.Error($"HandleEnterpriseStripeEvents: No payment details found for payment ID: {session.PaymentLink.Id}");
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment details found", };
                }

                if (entPayment.TenantId != null)
                {
                    var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == entPayment.TenantId);

                    // Activate subscription
                    subscription = await _publicContext.Subscriptions
                        .FirstOrDefaultAsync(x => x.TenantId == entPayment.TenantId && x.Application == entPayment.Application);

                    if (subscription is not null)
                    {
                        subscription.ActivatedOn = DateTime.UtcNow;
                        subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                        subscription.UpdatedAt = DateTime.UtcNow;
                        subscription.Status = PaymentStatus.Successful.ToString();
                        subscription.PaymentId = session.PaymentLink.Id;

                        _publicContext.Subscriptions.Update(subscription);

                        BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Active, subscription.UserId, subscription.Id.ToString(), tenant.Id.ToString(), subscription.TenantId.ToString(), subscription.Application.ToString(), entPayment.Id.ToString()));

                        // Trigger backgraound task that will update subscription status to inactive after 30 days
                        BackgroundJob.Enqueue<IBackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), tenant.Subdomain, null));

                        // Schedule a subscription event to be published after 2 days from the time of the failed payment
                        BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Active, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));
                    }
                }

                entPayment.PaymentStatus = PaymentStatus.Successful;
                entPayment.PaymentDate = DateTime.UtcNow;
                entPayment.UpdatedOn = DateTime.UtcNow;

                _logger.Information($"HandleEnterpriseStripeEvents: Enterprise payment has been updated to successful for payment ID: {session.PaymentLink.Id}");
                res.Data = true;
                res.ResponseMessage = "Enterprise payment has been updated to successful";
                res.ResponseCode = "200";
            }

            if (stripeEvent.Type == "checkout.session.async_payment_failed")
            {
                session = stripeEvent.Data.Object as Session;
                var entPayment = await _publicContext.EnterprizeSubscriptionPayments.FirstOrDefaultAsync(x => x.PaymentId == session.PaymentLink.Id);
                if (entPayment == null)
                {
                    _logger.Error($"HandleEnterpriseStripeEvents: No payment details found for payment ID: {session.PaymentLink.Id}");
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "No payment details found", };
                }

                if (entPayment.Tenant != null)
                {
                    var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == entPayment.TenantId);

                    // Update subscription
                    subscription = await _publicContext.Subscriptions
                        .FirstOrDefaultAsync(x => x.TenantId == entPayment.TenantId && x.Application == entPayment.Application);

                    if (subscription is not null)
                    {
                        subscription.UpdatedAt = DateTime.UtcNow;
                        subscription.Status = PaymentStatus.Failed.ToString();
                        subscription.PaymentId = session.PaymentLink.Id;

                        _publicContext.Subscriptions.Update(subscription);

                        BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Inactive, subscription.UserId, subscription.Id.ToString(), tenant.Id.ToString(), subscription.TenantId.ToString(), subscription.Application.ToString(), null));

                        // Schedule a subscription event to be published after 2 days from the time of the failed payment
                        BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Inactive, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));
                    }
                }

                entPayment.PaymentStatus = PaymentStatus.Failed;
                entPayment.UpdatedOn = DateTime.UtcNow;

                _logger.Information($"HandleEnterpriseStripeEvents: Enterprise payment has been updated to failed for payment ID: {session.PaymentLink.Id}");
                res.Data = true;
                res.ResponseMessage = "The payment failed";
                res.ResponseCode = "200";

                // TODO: Send out an email to the customer that the payment failed, to fund and retry again later
            }

            var result = await _publicContext.SaveChangesAsync();

            if (res.ResponseCode == "200" && !subscription.FreeTrialOptionSelected && subscription.SubscriptionCount > 1)
            {
                await SendPaidPlanNotification(res.ResponseCode, subscription);
                return res;
            }

            if (res.ResponseCode == "200" && !subscription.FreeTrialOptionSelected && subscription.SubscriptionCount! > 1)
            {
                await SendPaidPlanNotification(res.ResponseCode, subscription);

                // Publish subscription event
                BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.New, null));
                return res;
            }

            return res;
        }
        #endregion

        #region Stripe Webhook
        public async Task<GenericResponse> HandleStripeEvents(string json, string signature)
        {
            var res = new GenericResponse()
            {
                ResponseCode = "200",
                ResponseMessage = "None of the events being listened to were triggered",
                Data = true
            };

            res.Data = false;
            Invoice invoice = null;
            PaymentIntent paymentIntent = null;
            Models.Subscription subscription = null;
            AdditionalLiecense additionalLiecense = null;
            var company = new Tenant.Model.Tenant();

            Event stripeEvent = EventUtility.ConstructEvent(
                   json,
                   signature,
                   _options.Value.WebhookSecret,
                   throwOnApiVersionMismatch: false
                );

            if (stripeEvent.Type == "invoice.payment_succeeded")
            {
                invoice = stripeEvent.Data.Object as Invoice;

                if (invoice.BillingReason == "subscription_create")
                {
                    // The subscription automatically activates after successful payment
                    // Set the payment method used to pay the first invoice
                    // as the default payment method for that subscription

                    // Retrieve the payment intent used to pay the subscription
                    var service = new PaymentIntentService();
                    paymentIntent = await service.GetAsync(invoice.PaymentIntentId);

                    // Set the default payment method
                    var options = new SubscriptionUpdateOptions
                    {
                        DefaultPaymentMethod = paymentIntent.PaymentMethodId,
                    };
                    var subscriptionService = new SubscriptionService();
                    await subscriptionService.UpdateAsync(invoice.SubscriptionId, options);

                    res.Data = true;
                    res.ResponseCode = "200";
                }
            }

            if (stripeEvent.Type == "invoice.paid")
            {
                invoice = stripeEvent.Data.Object as Invoice;
                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.SubscriptionId == invoice.SubscriptionId);
                if (subscription is null)
                {
                    res.ResponseMessage = $"No subscription was found for subdcriptionId - {invoice.SubscriptionId}. " +
                        $"The subscription is likely to have been deleted on the joble db.";
                    return res;
                }

                subscription.Status = PaymentStatus.Successful.ToString();
                subscription.ActivatedOn = DateTime.UtcNow;
                subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                if (subscription.FreeTrialOptionSelected)
                    subscription.ExpiresOn = DateTime.UtcNow.AddDays(14);
                subscription.UpdatedAt = DateTime.UtcNow;
                subscription.SubscriptionCount = subscription.SubscriptionCount++;

                res = await UpdateSubscriptionStatus(subscription, SubscriptionStatus.Active);
                if (res.ResponseCode != "200")
                    return res;

                var subscriptionHistory = subscription.Map();
                subscriptionHistory.Id = Guid.NewGuid();
                await _publicContext.SubscriptionHistory.AddAsync(subscriptionHistory);

                res.Data = true;
                res.ResponseCode = "200";
            }

            if (stripeEvent.Type == "invoice.payment_failed")
            {
                invoice = stripeEvent.Data.Object as Invoice;
                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.SubscriptionId == invoice.SubscriptionId);
                if (subscription == null)
                {
                    res.ResponseMessage = $"No subscription was found for subscriptionId - {invoice.SubscriptionId}. The subscription is likely to have been deleted on the joble db.";
                    return res;
                }

                subscription.Status = PaymentStatus.Failed.ToString();
                subscription.UpdatedAt = DateTime.UtcNow;
                _publicContext.Subscriptions.Update(subscription);
                _publicContext.SaveChanges();

                // Send an email to the customer that the payment failed. Ask to fund the account and try again.
                // The system will try to make the payment again (3 times) 24 hours after each attempt

                // Update subscription status to failed
                BackgroundJob.Enqueue<BackGroundServices>(x => x.UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), company.Subdomain, 2));

                if (subscription.SubscriptionCount > 1)
                {
                    // Schedule a subscription event to be published after 2 days from the time of the failed payment
                    BackgroundJob.Schedule(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.Inactive, ReasonForSubscriptionStatus.PaymentFialed), DateTime.UtcNow.AddDays(2));
                }

                res.Data = false;
                res.ResponseMessage = "The payment has failed";
                res.ResponseCode = "400";
            }

            if (stripeEvent.Type == "invoice.payment_action_required")
            {
                invoice = stripeEvent.Data.Object as Invoice;
                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.SubscriptionId == invoice.SubscriptionId);
                if (subscription == null)
                {
                    res.ResponseMessage = $"No subscription was found for subscription id - {invoice.SubscriptionId}. The subscription is likely to have been deleted on the joble db.";
                    return res;
                }

                subscription.Status = PaymentStatus.Pending.ToString();
                subscription.UpdatedAt = DateTime.UtcNow;

                res.Data = true;
                res.ResponseMessage = "The payment is still proccessing";
                res.ResponseCode = "200";

                // Todo: Send an email to the customer that the payment is still processing.
                return res;
            }

            if (stripeEvent.Type == "customer.subscription.trial_will_end")
            {
                // Send notification to your user that the trial will end
                var stripeSubscription = (Stripe.Subscription)stripeEvent.Data.Object;
                var customerId = stripeSubscription.CustomerId;
                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.StripeCustomerId == customerId);
                
                if (subscription != null && subscription.TenantId.HasValue)
                {
                    var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == subscription.TenantId);
                    var superAdmin = await _publicContext.Users.FirstOrDefaultAsync(x => x.Id == tenant.AdminId);
                    
                    // Check if billing information exists
                    var billingInfo = await _publicContext.BillingInformations.FirstOrDefaultAsync(x => x.TenantId == tenant.Id);
                    
                    var mailDto = new SendTrialEndedMailDto
                    {
                        TenantId = tenant.Id.ToString()
                    };
                    
                    // Use billing information if available, otherwise use super admin details
                    if (billingInfo != null)
                    {
                        mailDto.Email = billingInfo.Email;
                        mailDto.Name = $"{billingInfo.FirstName} {billingInfo.LastName}";
                    }
                    else
                    {
                        mailDto.Email = superAdmin.Email;
                        mailDto.Name = superAdmin.FirstName;
                    }
                    
                    await _tenantService.SendFreeTrialEndNotification(mailDto);
                }
                
                res.Data = true;
                res.ResponseCode = "200";
                res.ResponseMessage = "The trial will end soon notification sent successfully";
                return res;
            }

            //*** PaymentIntent Events ****//
            if (stripeEvent.Type == "payment_intent.succeeded")
            {
                paymentIntent = (PaymentIntent)stripeEvent.Data.Object;
                var customerId = paymentIntent.CustomerId;
                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.StripeCustomerId == customerId);
                if (subscription is null)
                {
                    // Get liecense record using the paymentId and then use the subscriptionId to get the subscription record
                    additionalLiecense = await _publicContext.AdditionalLiecenses.FirstOrDefaultAsync(x => x.StripeCustomerId == customerId && !x.Updated);
                    if (additionalLiecense is null)
                    {
                        res.ResponseMessage = $"No subscription was found for customerId - {customerId}. The subscription is likely to have been deleted on the joble database.";
                        return res;
                    }

                    subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id == additionalLiecense.SubscriptionId);
                    if (subscription is null)
                    {
                        res.ResponseMessage = $"No subscription was found for customerId - {customerId}. The subscription is likely to have been deleted on the joble database.";
                        return res;
                    }
                }

                // Get liecense record using the paymentId and then use the subscriptionId to get the subscription record
                additionalLiecense = await _publicContext.AdditionalLiecenses.FirstOrDefaultAsync(x => x.StripeCustomerId == customerId && !x.Updated);
                if (additionalLiecense is not null)
                {
                    // Update additional liecense record
                    additionalLiecense.Status = PaymentStatus.Successful.ToString();
                    additionalLiecense.UpdatedAt = DateTime.UtcNow;
                    additionalLiecense.TransactionDate = DateTime.UtcNow;
                    additionalLiecense.Updated = true;
                    additionalLiecense.PaymentId = paymentIntent.Id;
                    _publicContext.AdditionalLiecenses.Update(additionalLiecense);

                    subscription.UpdatedAt = DateTime.UtcNow;
                    subscription.SubscriptionFor = subscription.SubscriptionFor + additionalLiecense.SubscriptionFor;
                    subscription.Amount = subscription.Amount + additionalLiecense.Amount;

                    var subdomain = await _publicContext.Tenants.Where(x => x.Id == subscription.TenantId).Select(x => x.Subdomain).FirstOrDefaultAsync();
                    await using var _context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
                    await AddOrEnablePermission(additionalLiecense.UserIds, subscription.TenantId.Value.ToString(), _context, SubscriptionStatus.Active, subscription.Application);

                    // Update subscription
                    var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);
                    var productId = "";
                    var existingPrices = await _publicContext.StripeSubscriptionDetails
                        .Where(x => x.Application == subscription.Application.ToString() && x.Plan == plan.Name).FirstOrDefaultAsync();
                    if (existingPrices is null)
                        productId = await CreateStripeProduct(plan.Name);
                    else
                        productId = existingPrices.ProductId;

                    var priceDto = new CreateStripePriceDto
                    {
                        ProductId = productId,
                        Application = subscription.Application.ToString(),
                        NumberOfUsersToSubFor = subscription.SubscriptionFor.Value,
                        Amount = (int)subscription.Amount,
                        Plan = plan.Name,
                        Interval = Enum.Parse<SubscriptionInterval>(subscription.Interval)
                    };

                    var priceId = await CreateStripePrice(priceDto);
                    var response = await UpdateStripeSubscription(subscription, true, priceId);
                    if (!response)
                    {
                        res.ResponseMessage = "Payment failed, subscription could not be updated";
                        res.ResponseCode = "400";
                        return res;
                    }
                }
                else
                {
                    // Get the plan using pricingplanId
                    var amount = 0;
                    var plan = await _publicContext.PricingPlans.Where(x => x.Id == subscription.PricingPlanId)
                            .Select(x => x.Name).FirstOrDefaultAsync();
                    if (!subscription.IsAISubscription)
                    {
                        var planDetails = await _publicContext.PackagePricing.Where(x => x.PricingPlanId == subscription.PricingPlanId)
                            .FirstOrDefaultAsync();
                        amount = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? (int)planDetails.PricePerMonth.Value : (int)planDetails.PricePerMonthForYearlyOption.Value;
                    }
                    else
                    {
                        amount = (int)subscription.Amount;
                    }

                    if (string.IsNullOrEmpty(subscription.StripePriceId) && (string.IsNullOrEmpty(subscription.SubscriptionId) || subscription.SubscriptionId?.Length < 20))
                    {
                        var productId = await CreateStripeProduct(plan);
                        Enum.TryParse(subscription.Interval, out SubscriptionInterval interval);
                        var priceModel = new CreateStripePriceDto
                        {
                            ProductId = productId,
                            Currency = subscription.Currency,
                            Amount = amount,
                            Application = subscription.Application.ToString(),
                            Interval = interval,
                            NumberOfUsersToSubFor = subscription.SubscriptionFor.Value,
                            Plan = plan,
                            IsForAI = subscription.IsAISubscription,
                        };

                        WatchLogger.Log($"Price model: {priceModel}");

                        var priceId = await CreateStripePrice(priceModel);
                        var subscriptionRequest = new CreateStripeSubscriptionDto
                        {
                            CustomerId = subscription.StripeCustomerId,
                            PriceId = priceId,
                            Currency = subscription.Currency.ToString(),
                            Application = subscription.Application.ToString(),
                            TenantId = subscription.TenantId?.ToString(),
                            FreeTrial = true,
                            NumberOfUsersSubbedFor = subscription.SubscriptionFor.Value,
                        };

                        var subscriptionResponse = await CreateStripeSubscription(subscriptionRequest);

                        // Set the default payment method
                        var options = new SubscriptionUpdateOptions
                        {
                            DefaultPaymentMethod = paymentIntent.PaymentMethodId,
                        };
                        var subscriptionService = new SubscriptionService();
                        await subscriptionService.UpdateAsync(subscriptionResponse.SubscriptionId, options);

                        subscription.StripePriceId = priceId;
                        subscription.SubscriptionId = subscriptionResponse.SubscriptionId;
                        subscription.ActivatedOn = DateTime.UtcNow;
                        subscription.Status = PaymentStatus.Successful.ToString();
                        subscription.ContractEndsOn = DateTime.UtcNow.AddYears(1);
                        if (subscription.FreeTrialOptionSelected)
                            subscription.ExpiresOn = DateTime.UtcNow.AddDays(14);
                        else
                            subscription.ExpiresOn = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? DateTime.UtcNow.AddMonths(1) : DateTime.UtcNow.AddMonths(12);
                        _publicContext.Subscriptions.Update(subscription);
                        var dbResult = await _publicContext.SaveChangesAsync();

                        if (dbResult > 0)
                        {
                            res.ResponseCode = "200";
                            res.ResponseMessage = "Subscription succeeded";
                        }
                        else
                        {
                            res.ResponseCode = "400";
                            res.ResponseMessage = "Payment failed, subcription could not be created";
                        }
                    }
                }
            }

            if (stripeEvent.Type == "payment_intent.payment_failed")
            {
                paymentIntent = (PaymentIntent)stripeEvent.Data.Object;
                var customerId = paymentIntent.CustomerId;
                subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.StripeCustomerId == customerId);
                if (subscription is null)
                {
                    // Get liecense record using the paymentId and then use the subscriptionId to get the subscription record
                    additionalLiecense = await _publicContext.AdditionalLiecenses.FirstOrDefaultAsync(x => x.StripeCustomerId == customerId && !x.Updated);
                    if (additionalLiecense is null)
                    {
                        res.ResponseMessage = $"No subscription was found for customerId - {customerId}. The subscription is likely to have been deleted on the joble database.";
                        return res;
                    }

                    subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id == additionalLiecense.SubscriptionId);
                    if (subscription is null)
                    {
                        res.ResponseMessage = $"No subscription was found for customerId - {customerId}. The subscription is likely to have been deleted on the joble database.";
                        return res;
                    }
                }

                if (additionalLiecense is not null)
                {
                    additionalLiecense.Status = PaymentStatus.Failed.ToString();
                    additionalLiecense.UpdatedAt = DateTime.UtcNow;
                    additionalLiecense.TransactionDate = DateTime.UtcNow;
                    additionalLiecense.Updated = true;
                    additionalLiecense.PaymentId = paymentIntent.Id;
                    _publicContext.AdditionalLiecenses.Update(additionalLiecense);
                }

                // Send an email to the customer that the payment failed. Ask to fund the account and try again. 

                res.ResponseMessage = "The payment intent has failed";
                res.ResponseCode = "400";
                return res;
            }

            var result = await _publicContext.SaveChangesAsync();
            if (subscription.FreeTrialOptionSelected && res.ResponseCode == "200" && subscription.SubscriptionCount <= 1)
            {
                // Schedule a background job to update free trail status
                BackgroundJob.Enqueue<IBackGroundServices>((x) => x.UpdateFreeTrialToFalse(subscription.Id.ToString()));

                var invitee = await _publicContext.Users.Where(x => x.Id == subscription.UserId)
                    .FirstOrDefaultAsync();
                var template = string.Empty;

                template = Extensions.ReadTemplateFromFile("free-plan-notification", _environment);
                template = template.Replace("{name}", invitee.FirstName + " " + invitee.LastName).Replace("{faq-url}", ApplicationFrontendURL).Replace("{contact-url}", ApplicationFrontendURL).Replace("{google}", ApplicationFrontendURL).Replace("{apple}", ApplicationFrontendURL);
                BackgroundJob.Enqueue(() => _emailService.SendEmail(template, invitee.Email, "Welcome to JobPro - Free Trial Activated"));

                // Publish subscription event
                BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.New, null));
            }

            if (res.ResponseCode == "200" && !subscription.FreeTrialOptionSelected && subscription.SubscriptionCount > 1)
            {
                await SendPaidPlanNotification(res.ResponseCode, subscription);
                return res;
            }

            if (res.ResponseCode == "200" && !subscription.FreeTrialOptionSelected && subscription.SubscriptionCount! > 1)
            {
                if (additionalLiecense is not null)
                {
                    // TODO: Send a different mail for addition liencense purchase instead of SendPaidPlanNotification
                }
                else
                    await SendPaidPlanNotification(res.ResponseCode, subscription);

                // Publish subscription event
                BackgroundJob.Enqueue(() => PublishSubscriptionEvent(subscription.Id.ToString(), SubscriptionStatusForRMQ.New, null));
                return res;
            }

            return res;
        }
        #endregion

        #region Private Mollie Methods
        public async Task CancelMollieSubscription(string mollieCustId, string mollieSubscriptionId)
        {
            await _subscriptionClient.CancelSubscriptionAsync(mollieCustId, mollieSubscriptionId);
        }

        private static async Task<bool> AddOrEnablePermission(List<string> userIds, string tenantId, JobProDbContext _context, SubscriptionStatus status, Applications app)
        {
            foreach (var userId in userIds)
            {
                var permission = await _context.AppPermissions
                    .Where(x => x.UserId == userId && x.Application == app.ToString())
                    .FirstOrDefaultAsync();

                if (permission is null)
                {
                    var appPermission = new AppPermissions(true)
                    {
                        UserId = userId,
                        Application = app.ToString(),
                        IsEnabled = true,
                        SubscriptionStatus = status.ToString(),
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                        TenantId = tenantId
                    };

                    _context.AppPermissions.Add(appPermission);
                }
                else
                {
                    permission.SubscriptionStatus = status.ToString();
                    permission.UpdatedAt = DateTime.UtcNow;
                    permission.IsEnabled = true;

                    _context.AppPermissions.Update(permission);
                }
            }

            //var dbResult = await _context.SaveChangesAsync();
            //return dbResult > 0;
            return true;
        }

        private async Task<SubscriptionResponse> CreateSubscription(Models.Subscription subscription, bool? endOfCurrentSub = null, bool freeTrial = false)
        {
            // Cheack if the client has an existing subscription on Mollie
            ListResponse<SubscriptionResponse> response = await _subscriptionClient
                .GetSubscriptionListAsync(subscription.MollieCustomerId, null, 10);
            if (response.Count > 1)
            {
                foreach (var item in response.Items)
                {
                    if (item.Metadata == subscription.Application.ToString())
                    {
                        // Update the subscription
                        var result = await UpdateSubscription(subscription, true, subscription.PricingPlanId.ToString());
                        return result;
                    }
                }
            }

            MandateResponse mandateResponse = null;
            var companyName = await _publicContext.Tenants.Where(x => x.Id == subscription.TenantId).Select(x => x.CompanyName).FirstOrDefaultAsync();

            // Get the mandate for the customer if it exists from mollie using customer id or create a new one           
            if (subscription.MandateId is not null)
                mandateResponse = await _mandateClient.GetMandateAsync(subscription.MollieCustomerId, subscription.MandateId);
            else
            {
                mandateResponse = (await _mandateClient.GetMandateListAsync(subscription.MollieCustomerId)).Items[0];
                if (subscription.PaymentMethod == PaymentMethods.PayPal.ToString())
                {
                    var mandate = new PayPalMandateRequest
                    {
                        ConsumerEmail = subscription.PayPalEmail,
                        PaypalBillingAgreementId = subscription.PaypalBillingAgreementId,
                        Method = "paypal",
                        ConsumerName = companyName,
                        SignatureDate = DateTime.UtcNow,
                        MandateReference = Guid.NewGuid().ToString(),
                    };

                    if (mandateResponse.Method != "paypal")
                    {
                        // Revoke the existing mandate and create a new one
                        await _mandateClient.RevokeMandate(subscription.MollieCustomerId, mandateResponse.Id);
                        mandateResponse = await _mandateClient.CreateMandateAsync(subscription.MollieCustomerId, mandate);
                    }
                }
                else if (subscription.PaymentMethod == PaymentMethods.DirectDebit.ToString())
                {
                    var mandate = new SepaDirectDebitMandateRequest
                    {
                        ConsumerName = companyName,
                        ConsumerAccount = subscription.ConsumerAccount,
                        ConsumerBic = "",
                        Method = "directdebit",
                        SignatureDate = DateTime.UtcNow,
                        MandateReference = Guid.NewGuid().ToString(),
                    };

                    if (mandateResponse.Method != "directdebit")
                    {
                        // Revoke the existing mandate and create a new one
                        await _mandateClient.RevokeMandate(subscription.MollieCustomerId, mandateResponse.Id);
                        mandateResponse = await _mandateClient.CreateMandateAsync(subscription.MollieCustomerId, mandate);
                    }
                }
            }

            subscription.MandateId = subscription.MandateId ?? mandateResponse.Id;

            if (mandateResponse.Status == "valid" || mandateResponse.Status == "pending")
            {
                endOfCurrentSub = endOfCurrentSub ?? false;
                SubscriptionRequest subscriptionRequest = new SubscriptionRequest()
                {
                    Amount = new Amount(subscription.Currency.ToString(), (decimal)subscription.Amount),
                    Interval = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? "1 month" : "12 month",
                    StartDate = endOfCurrentSub.Value ? subscription.ExpiresOn : DateTime.UtcNow,
                    Description = $"Subscription created for {companyName.ToUpper()} - {subscription.Application.ToString().ToUpper()} with id - {Guid.NewGuid()}",
                    WebhookUrl = string.Format("{0}/subscription/webhook/verifysubsequentpayment", Constants.BACKEND_BASE_URL),
                    Metadata = subscription.Application.ToString(),
                    MandateId = subscription.MandateId,
                };

                if (freeTrial)
                    subscriptionRequest.StartDate = DateTime.UtcNow.AddDays(14);

                SubscriptionResponse subscriptionResponse = await _subscriptionClient.CreateSubscriptionAsync(subscription.MollieCustomerId, subscriptionRequest);
                return subscriptionResponse;
            }

            return new SubscriptionResponse();
        }

        private async Task<SubscriptionResponse> UpdateSubscription(Models.Subscription subscription, bool endOfCurrentSub, string newPricingPlan)
        {
            var metaData = new CustomUpdateSubMetaData()
            {
                PricingPlanId = newPricingPlan,
            };

            SubscriptionUpdateRequest updatedSubscriptionRequest = new SubscriptionUpdateRequest()
            {
                Amount = new Amount(subscription.Currency.ToString(), (decimal)subscription.Amount),
                StartDate = endOfCurrentSub ? subscription.ContractEndsOn.Value : DateTime.UtcNow,
                Interval = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? "1 month" : "12 month",
                Description = $"Updated subscription on {DateTime.UtcNow}",
                WebhookUrl = string.Format("{0}/subscription/webhook/verifysubsequentpayment", Constants.BACKEND_BASE_URL),
                Times = 1,
            };

            updatedSubscriptionRequest.SetMetadata(metaData);

            SubscriptionResponse response = await _subscriptionClient.UpdateSubscriptionAsync(subscription.MollieCustomerId, subscription.SubscriptionId, updatedSubscriptionRequest);
            return response;
        }

        private async Task<PaymentResponse> CreatePaymentForCustomer(PricingPlan plan, double amount, string customerId, string currency, PaymentMethods paymentMethod, bool fromClientAdmin = false, bool additionalLiecense = false)
        {
            PaymentRequest paymentRequest = new PaymentRequest()
            {
                Amount = new Amount(currency, (decimal)amount),
                Description = !additionalLiecense ? "Subscription | " + plan.Name.ToUpper() + "| " + plan.Application.ToString().ToUpper() : "Additional Payment | " + plan.Name.ToUpper() + "| " + plan.Application.ToString().ToUpper(),
                RedirectUrl = !fromClientAdmin ? Constants.MOLLIE_REDIRECT_URL : Constants.CLIENT_ADMIN_MOLLIE_REDIRECT_URL,
                WebhookUrl = string.Format("{0}/subscription/webhook/verifyfirstpayment", Constants.BACKEND_BASE_URL),
                Locale = Locale.en_US,
                Metadata = "First payment for a number of users"
            };

            switch (paymentMethod)
            {
                case PaymentMethods.PayPal:
                    paymentRequest.Method = Mollie.Api.Models.Payment.PaymentMethod.PayPal;
                    break;
                case PaymentMethods.Card:
                    paymentRequest.Method = Mollie.Api.Models.Payment.PaymentMethod.CreditCard;
                    paymentRequest.SequenceType = SequenceType.First;
                    break;
                case PaymentMethods.DirectDebit:
                    paymentRequest.Method = Mollie.Api.Models.Payment.PaymentMethod.DirectDebit;
                    break;
                case PaymentMethods.Ideal:
                    paymentRequest.Method = Mollie.Api.Models.Payment.PaymentMethod.Ideal;
                    paymentRequest.SequenceType = SequenceType.First;
                    break;
                default:
                    break;
            }

            PaymentResponse paymentResponse = await _customerClient.CreateCustomerPayment(customerId, paymentRequest);
            return paymentResponse;
        }

        private async Task<string> CreateMollieCustomer(User user, string tenatId = null)
        {
            // First check if the customer already exist on mollie
            //var key = "mollie-customers";
            //ListResponse<CustomerResponse> mollieCustomers = null;
            //if (_cache.TryGetValue(key, out ListResponse<CustomerResponse> customers))
            //{
            //    mollieCustomers = customers;
            //}
            //else
            //{
            //    mollieCustomers = await _customerClient.GetCustomerListAsync();
            //    _cache.Set(key, mollieCustomers);
            //}

            //var mollieCustomers = await _customerClient.GetCustomerListAsync();
            //foreach (var customer in mollieCustomers.Items)
            //{
            //    if (customer.Email.ToLower() == user.Email.ToLower())
            //    {
            //        return customer.Id;
            //    }
            //}

            CustomerRequest customerRequest = new CustomerRequest()
            {
                Email = user.Email,
                Name = user.FirstName + " " + user.LastName,
                Locale = Locale.en_GB,
            };

            if (tenatId != null)
            {
                var metaData = new
                {
                    TenantId = tenatId,
                };
                customerRequest.SetMetadata(metaData);
            }

            CustomerResponse customerResponse = await _customerClient.CreateCustomerAsync(customerRequest);
            return customerResponse.Id;
        }
        #endregion

        #region Private Stripe Methods
        private async Task<string> CreateStripeCustomer(User user, string tenantId = null)
        {
            // Check if a customer with the email already exist on tripe
            var service = new CustomerService();
            var queryOptions = new CustomerSearchOptions
            {
                Query = $"email:'{user.Email}'"
            };
            var stripeCustomers = service.Search(queryOptions);
            foreach (var cust in stripeCustomers.Data)
            {
                if (cust.Email.ToLower() == user.Email.ToLower())
                    return cust.Id;
            }

            var options = new CustomerCreateOptions
            {
                Email = user.Email,
                Name = user.FirstName + " " + user.LastName,
                Phone = user.PhoneNumber,
                Metadata = new Dictionary<string, string>
                {
                    { "UserId", user.Id },
                    { "Application", Applications.Joble.ToString() },
                },
            };

            if (tenantId != null)
                options.Metadata.Add("TenantId", tenantId);

            Customer customer = await service.CreateAsync(options);

            return customer.Id;
        }

        private async Task<string> CreateStripeProduct(string plan)
        {
            var service = new ProductService();
            Product product = null;

            // First check if the products exist on stripe
            var existingProduct = await _publicContext.StripeSubscriptionDetails.FirstOrDefaultAsync(x => x.Plan == plan);
            if (existingProduct != null)
            {
                // Call stripe endpoint to make check if the product exists               
                product = await service.GetAsync(existingProduct.ProductId);
                if (product is not null)
                    return product.Id;
            }

            var options = new ProductCreateOptions
            {
                Name = plan,
            };

            product = await service.CreateAsync(options);
            return product.Id;
        }

        private async Task<string> CreateStripePrice(CreateStripePriceDto model)
        {
            Price price = null;
            var service = new PriceService();
            if (!model.IsForAI)
                model.Amount = model.Interval == SubscriptionInterval.Monthly ? model.Amount * 100 : model.Amount * 100 * 12; // Price is in cents

            var existingPrices = await _publicContext.StripeSubscriptionDetails
                .Where(x => x.ProductId == model.ProductId && x.Plan == model.Plan && x.Interval == model.Interval.ToString()).ToListAsync();
            if (existingPrices is not null && existingPrices.Any())
            {
                if (model.IsForAI)
                    existingPrices = existingPrices.Where(x => x.AIAgent == model.IsForAI).ToList();
                else
                    existingPrices = existingPrices.Where(x => x.Application == model.Application).ToList();
            }

            foreach (var existingPrice in existingPrices)
            {
                price = await service.GetAsync(existingPrice.PriceId);
                if (price is not null)
                {
                    if ((int)price.UnitAmount == model.Amount && price.Currency == model.Currency.ToString().ToLower())
                        return price.Id;
                }
            }

            var stripeInterval = new Dictionary<SubscriptionInterval, string> { { SubscriptionInterval.Monthly, "month" }, { SubscriptionInterval.Yearly, "year" } };
            var options = new PriceCreateOptions
            {
                UnitAmount = model.Amount,
                Currency = model.Currency.ToString().ToLower(),
                Recurring = new PriceRecurringOptions
                {
                    Interval = stripeInterval[model.Interval],
                },
                Product = model.ProductId,
                Metadata = new Dictionary<string, string>
                {
                    { "application", model.Application },
                },
            };
            price = await service.CreateAsync(options);

            if (!string.IsNullOrEmpty(price.Id) && existingPrices.Any())
            {
                foreach (var existingPrice in existingPrices)
                {
                    var stripeDetails = new StripeSubscriptionDetail
                    {
                        Plan = model.Plan,
                        ProductId = model.ProductId,
                        PriceId = price.Id,
                        Application = model.Application,
                        AIAgent = model.IsForAI,
                        Interval = model.Interval.ToString(),
                        Currency = model.Currency.ToString().ToLower(),
                    };
                    if (existingPrice.Currency != price.Currency)
                        _publicContext.StripeSubscriptionDetails.Add(stripeDetails);
                    else
                    {
                        existingPrice.PriceId = price.Id;
                        _publicContext.StripeSubscriptionDetails.Update(existingPrice);
                    }
                }

                await _publicContext.SaveChangesAsync();
            }
            else if (!string.IsNullOrEmpty(price.Id)) // If no existing prices, add new price
            {
                var stripeDetails = new StripeSubscriptionDetail
                {
                    Plan = model.Plan,
                    ProductId = model.ProductId,
                    PriceId = price.Id,
                    Application = model.Application,
                    Interval = model.Interval.ToString(),
                    Currency = model.Currency.ToString().ToLower(),
                };

                _publicContext.StripeSubscriptionDetails.Add(stripeDetails);
                await _publicContext.SaveChangesAsync();
            }

            return price.Id;
        }

        private async Task<SubscriptionCreateResponse> CreateStripeSubscription(CreateStripeSubscriptionDto model)
        {
            model.TenantId = model.TenantId ?? "";
            var subscriptionService = new SubscriptionService();
            if (model.SubscriptionId is not null)
            {
                var subscription = await subscriptionService.GetAsync(model.SubscriptionId);
                if (subscription is not null)
                {
                    // Delete or cancel the subscription on stripe
                    if (subscription.Customer.Id == model.CustomerId && subscription.Items.Data[0].Price.Id == model.PriceId)
                    {
                        if (subscription.Status == "active")
                        {
                            var cancelResponse = await subscriptionService.CancelAsync(subscription.Id);
                            if (cancelResponse.Status != "canceled")
                                throw new OperationFailedException("Retry attempt failed. Current sub could not be canceled");
                        }
                    }
                }
            }

            var paymentSettings = new SubscriptionPaymentSettingsOptions
            {
                SaveDefaultPaymentMethod = "on_subscription",
            };

            var subscriptionOptions = new SubscriptionCreateOptions
            {
                CancelAtPeriodEnd = false,
                Customer = model.CustomerId,
                Items = new List<SubscriptionItemOptions>
                {
                    new SubscriptionItemOptions
                    {
                        Price = model.PriceId,
                        Quantity = model.NumberOfUsersSubbedFor
                    },
                },
                PaymentSettings = paymentSettings,
                PaymentBehavior = "default_incomplete",
                CollectionMethod = "charge_automatically",
                Metadata = new Dictionary<string, string>
                {
                    { "Application", model.Application },
                    { "CompanyId", model.TenantId },
                }
            };

            subscriptionOptions.AddExpand("latest_invoice.payment_intent");
            if (model.FreeTrial)
                subscriptionOptions.TrialPeriodDays = 14;
            try
            {
                var subscription = await subscriptionService.CreateAsync(subscriptionOptions);
                return new SubscriptionCreateResponse
                {
                    SubscriptionId = subscription.Id,
                    ClientSecret = subscription.LatestInvoice.PaymentIntent?.ClientSecret,
                };
            }
            catch (StripeException e)
            {
                throw e;
            }
        }

        private async Task<string> CreatePaymentIntent(PaymentIntentCreationDto model)
        {
            var paymentIntentService = new PaymentIntentService();

            // Find and delete any existing payment intents
            var existingPaymentIntents = await paymentIntentService.ListAsync(new PaymentIntentListOptions
            {
                Customer = model.CustomerId,
            });

            var paymentIntent = await paymentIntentService.CreateAsync(new PaymentIntentCreateOptions
            {
                Amount = model.Amount != null ? model.Amount * 100 : 50,
                Currency = model.Currency.ToLower(),
                Customer = model.CustomerId,
                AutomaticPaymentMethods = new PaymentIntentAutomaticPaymentMethodsOptions
                {
                    Enabled = true,
                },
                ReturnUrl = model.ReturnUrl,
                ReceiptEmail = model.Email,
                Description = "Subscription Payment Intent",
                //ConfirmationMethod = "automatic",
                SetupFutureUsage = "off_session",
            });
            return paymentIntent.ClientSecret;
        }

        private async Task<bool> UpdateStripeSubscription(Models.Subscription subscription, bool applyAtTheEndOfCurrentSub, string priceId)
        {
            var scheduleService = new SubscriptionScheduleService();
            var subscriptionService = new SubscriptionService();

            var prorationBehavior = applyAtTheEndOfCurrentSub ? "none" : "always_invoice";
            SubscriptionSchedule schedule = null;

            // Fetch the subscription first to check if it has a schedule
            var stripeSubscription = await subscriptionService.GetAsync(subscription.SubscriptionId);
            var scheduleId = stripeSubscription.ScheduleId;

            if (!string.IsNullOrEmpty(scheduleId))
                schedule = await scheduleService.GetAsync(scheduleId);
            else
            {
                // No existing schedule - create one from the subscription
                var createScheduleOptions = new SubscriptionScheduleCreateOptions
                {
                    FromSubscription = subscription.SubscriptionId,
                };

                schedule = await scheduleService.CreateAsync(createScheduleOptions);
            }

            if (schedule == null) return false;

            // Get the current phase to carry over items
            var currentPhase = schedule.Phases.First();

            // Prepare the phases for the update
            var phases = new List<SubscriptionSchedulePhaseOptions>();

            if (applyAtTheEndOfCurrentSub)
            {
                // Keep current phase until contract end, then add new phase
                phases.Add(new SubscriptionSchedulePhaseOptions
                {
                    Items = currentPhase.Items.Select(i => new SubscriptionSchedulePhaseItemOptions
                    {
                        Price = i.PriceId,
                        Quantity = i.Quantity
                    }).ToList(),
                    StartDate = currentPhase.StartDate,
                    EndDate = subscription.ContractEndsOn.Value,
                    ProrationBehavior = prorationBehavior,
                });

                // Add the new phase starting from contract end date
                phases.Add(new SubscriptionSchedulePhaseOptions
                {
                    Items = new List<SubscriptionSchedulePhaseItemOptions>
                        {
                            new SubscriptionSchedulePhaseItemOptions
                            {
                                Price = priceId,
                                Quantity = subscription.SubscriptionFor
                            }
                        },
                    StartDate = subscription.ContractEndsOn.Value,
                    ProrationBehavior = prorationBehavior,
                });
            }
            else
            {
                // Apply immediately - replace current phase
                phases.Add(new SubscriptionSchedulePhaseOptions
                {
                    Items = new List<SubscriptionSchedulePhaseItemOptions>
                        {
                            new SubscriptionSchedulePhaseItemOptions
                            {
                                Price = priceId,
                                Quantity = subscription.SubscriptionFor
                            }
                        },
                    StartDate = DateTime.UtcNow, // Start immediately
                    ProrationBehavior = prorationBehavior,
                });
            }

            // Update the schedule with the new phases
            var updateScheduleOptions = new SubscriptionScheduleUpdateOptions
            {
                Phases = phases
            };

            var updatedSchedule = await scheduleService.UpdateAsync(schedule.Id, updateScheduleOptions);
            return updatedSchedule != null;
        }

        private async Task<string> CancelStripeSubscription(string subscriptionId, bool applyAtTheEndOfCurrentSub)
        {
            Stripe.Subscription response = null;
            var service = new SubscriptionService();
            if (applyAtTheEndOfCurrentSub)
            {
                var options = new SubscriptionUpdateOptions
                {
                    CancelAtPeriodEnd = true,
                };
                response = await service.UpdateAsync(subscriptionId, options);
            }
            else
                // Cancel the subscription immidiately (no refund)
                response = await service.CancelAsync(subscriptionId);

            return response.Status;
        }

        private async Task<GenericResponse> UpdateSubscriptionStatus(Models.Subscription subscription, SubscriptionStatus status)
        {
            var res = new GenericResponse();
            if (subscription.TenantId is not null)
            {
                var company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == subscription.TenantId);
                var companySub = await _publicContext.CompanySubscriptions.FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId && x.Application == subscription.Application);

                if (!subscription.IsAISubscription)
                {
                    if (companySub is null)
                    {
                        companySub = new CompanySubscription
                        {
                            TenantId = (Guid)subscription.TenantId,
                            Application = subscription.Application,
                            Status = status,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow,
                            SubscriptionId = subscription.Id
                        };
                        await _publicContext.CompanySubscriptions.AddAsync(companySub);
                    }
                    else
                    {
                        companySub.Status = status;
                        companySub.UpdatedAt = DateTime.UtcNow;
                        companySub.SubscriptionId = subscription.Id;
                        _publicContext.CompanySubscriptions.Update(companySub);
                    }
                }
                else
                {
                    var aiSubDetails = await _publicContext.AISubscriptionDetails
                        .Where(x => x.SubscriptionId == subscription.Id).ToListAsync();
                    if (aiSubDetails.Count < 1)
                        throw new RecordNotFoundException("AI Subscription details not found");

                    foreach (var detail in aiSubDetails)
                    {
                        companySub = await _publicContext.CompanySubscriptions
                            .FirstOrDefaultAsync(x => x.TenantId == subscription.TenantId && x.AIAgent == detail.Agent);
                        if (company is null)
                        {
                            companySub = new CompanySubscription
                            {
                                TenantId = (Guid)subscription.TenantId,
                                Status = SubscriptionStatus.Active,
                                CreatedAt = DateTime.UtcNow,
                                SubscriptionId = subscription.Id,
                                UpdatedAt = DateTime.UtcNow,
                                AIAgent = detail.Agent
                            };

                            await _publicContext.CompanySubscriptions.AddAsync(companySub);
                        }
                        else
                        {
                            companySub.Status = SubscriptionStatus.Active;
                            companySub.UpdatedAt = DateTime.UtcNow;
                            companySub.SubscriptionId = subscription.Id;
                            _publicContext.CompanySubscriptions.Update(companySub);
                        }
                    }
                }

                if (!subscription.IsAISubscription)
                {
                    // Update app permission of the super admin user
                    await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
                    var superAdminId = await _publicContext.Users.Where(x => x.Id == company.AdminId).Select(x => x.Id).FirstOrDefaultAsync();
                    var permissions = await _context.AppPermissions
                        .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Application == subscription.Application.ToString() && x.IsEnabled == true).ToListAsync();

                    if (permissions.Any())
                    {
                        int count = 1;
                        foreach (var permission in permissions)
                        {
                            if (count > subscription.SubscriptionFor)
                            {
                                _logger.Error($"The number of employees with permissions exceeds the number of employees covered by the current subscription. The subscription current covers {subscription.SubscriptionFor} employees");
                                res.ResponseMessage = $"Note: The number of employees with permissions exceeds the number of employees " +
                                    $"covered by your current subscription. Your sunscription current covers {subscription.SubscriptionFor} employees";
                                res.ResponseCode = "400";
                                break;
                            }

                            permission.SubscriptionStatus = status.ToString();
                            _context.AppPermissions.Update(permission);

                            count++;
                        }
                        await _context.SaveChangesAsync();
                    }
                }
                else
                {
                    var aiSubDetails = await _publicContext.AISubscriptionDetails
                        .Where(x => x.SubscriptionId == subscription.Id).ToListAsync();
                    if (aiSubDetails.Count < 1)
                        throw new RecordNotFoundException("AI Subscription details not found");

                    foreach (var aiSub in aiSubDetails)
                    {
                        await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
                        var permissions = await _context.AppPermissions
                            .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Application == subscription.Application.ToString() && x.IsEnabled == true && x.Agent == aiSub.Agent).ToListAsync();

                        if (permissions.Any())
                        {
                            int count = 1;
                            foreach (var permission in permissions)
                            {
                                if (count > aiSub.NoOfUserSubscribedFor)
                                {
                                    _logger.Error($"The number of employees with liecenses exceeds the number of employees covered by the current subscription. The subscription current covers {aiSub.NoOfUserSubscribedFor} employees");
                                    res.ResponseMessage = $"Note: The number of employees with liecenses exceeds the number of " +
                                        $"employees covered by your current subscription. Your sunscription current covers {aiSub.NoOfUserSubscribedFor} employees";
                                    break;
                                }

                                permission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                                _context.AppPermissions.Update(permission);

                                count++;
                            }

                            var schemaContextRes = await _context.SaveChangesAsync();
                            if (schemaContextRes < 1)
                            {
                                res.ResponseCode = "500";
                                res.ResponseMessage = "Not able to update user permissions";
                                return res;
                            }
                        }
                    }
                }
            }
            else
            {
                if (!subscription.IsAISubscription)
                {
                    var userId = await _publicContext.Users.Where(x => x.Id == subscription.UserId).Select(u => u.Id).FirstOrDefaultAsync();

                    // Update app permission of the user
                    var permission = await _publicContext.AppPermissions
                        .Where(x => x.UserId == userId && x.Application == subscription.Application.ToString() && x.IsEnabled == true).FirstOrDefaultAsync();

                    if (permission is not null)
                    {
                        permission.IsEnabled = true;
                        permission.SubscriptionStatus = status.ToString();
                        _publicContext.AppPermissions.Update(permission);
                    }
                }
            }

            var dbResult = await _publicContext.SaveChangesAsync() > 0;
            res.ResponseCode = dbResult ? "200" : "500";
            res.ResponseMessage = dbResult ? "Subscription status updated successfully" : "Subscription update failed";
            return res;
        }
        #endregion

        #region Get Subscription Summary Statistics
        /// <summary>
        /// This method is used to get subscription plans/or plans summary
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <returns></returns>
        public async Task<SummaryStatisticsDto> GetSummaryStatistics(Applications application, PaymentProviders? paymentProvider)
        {
            var result = (
                TotalSubscriptions: await TotalSubscription(application, paymentProvider),
                TotalCompanies: await TotalCompanies(application, paymentProvider),
                TotalRevenue: await TotalRevenue(application, paymentProvider)
            );

            return new SummaryStatisticsDto
            {
                TotalCompanies = result.TotalCompanies,
                TotalRevenue = result.TotalRevenue,
                TotalSubscriptions = result.TotalSubscriptions
            };
        }
        #endregion

        #region Get all Subscription Summary Statistics
        /// <summary>
        /// This method is used to get all subscription plans/or plans summary
        /// </summary>
        /// <returns></returns>
        public async Task<ApplicationSummaryDto> GetAllSummaryStatistics(PaymentProviders? paymentProvider)
        {
            return new ApplicationSummaryDto
            {
                Joble = new SummaryStatisticsDto
                {
                    TotalSubscriptions = await TotalSubscription(Applications.Joble, paymentProvider),
                    TotalCompanies = await TotalCompanies(Applications.Joble, paymentProvider),
                    TotalRevenue = await TotalRevenue(Applications.Joble, paymentProvider)
                },
                Echo = new SummaryStatisticsDto
                {
                    TotalSubscriptions = await TotalSubscription(Applications.Echo, paymentProvider),
                    TotalCompanies = await TotalCompanies(Applications.Echo, paymentProvider),
                    TotalRevenue = await TotalRevenue(Applications.Echo, paymentProvider)
                },
                JobPays = new SummaryStatisticsDto
                {
                    TotalSubscriptions = await TotalSubscription(Applications.JobPays, paymentProvider),
                    TotalCompanies = await TotalCompanies(Applications.JobPays, paymentProvider),
                    TotalRevenue = await TotalRevenue(Applications.JobPays, paymentProvider)
                },
                JobID = new SummaryStatisticsDto
                {
                    TotalSubscriptions = await TotalSubscription(Applications.JobID, paymentProvider),
                    TotalCompanies = await TotalCompanies(Applications.JobID, paymentProvider),
                    TotalRevenue = await TotalRevenue(Applications.JobID, paymentProvider)
                },
                JobEyes = new SummaryStatisticsDto
                {
                    TotalSubscriptions = await TotalSubscription(Applications.JobFy, paymentProvider),
                    TotalCompanies = await TotalCompanies(Applications.JobFy, paymentProvider),
                    TotalRevenue = await TotalRevenue(Applications.JobFy, paymentProvider)
                }
            };
        }
        #endregion

        #region Private Methods
        private async Task<GenericResponse> ValidateUserIds(List<string> userIds, JobProDbContext context, Applications app, bool forRemoveSub)
        {
            foreach (var userId in userIds)
            {
                var userExist = await context.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
                if (userExist is null)
                {
                    _logger.Error($"ValidateUserIds:User with ID {userId} does not exist in your company");
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = $"Something went wrong, please try again later",
                        DevResponseMessage = "User does not exist in your company. Please check the user ID and try again. If the issue persists, contact support."
                    };
                }

                var userPermission = await context.AppPermissions
                    .FirstOrDefaultAsync(x => x.UserId == userId && x.Application == app.ToString()
                    && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.IsEnabled);
                if (forRemoveSub)
                {
                    if (userPermission is null)
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"{userExist.FirstName + " " + userExist.LastName} does not have access to the application",
                        };
                }
                else
                {
                    if (userPermission is not null)
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"{userExist.FirstName + " " + userExist.LastName} already has access to the application",
                        };
                }
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "User ids are valid"
            };
        }

        private async Task<GenericResponse> GetRegionAsEnum(string countryName, Guid tenantId)
        {
            SubscriptionRegions regionToReturn;

            // Get the region/continent of the company
            if (countryName != SubscriptionRegions.Nigeria.ToString())
            {
                string region = await _publicContext.Tenants
                    .Where(x => x.Id == tenantId).Select(x => x.Region).FirstOrDefaultAsync();
                if (string.IsNullOrEmpty(region))
                {
                    // Get the country region
                    var countryRegion = await _apiCallService.GetCountryRegionAsync(countryName);
                    if (string.IsNullOrEmpty(countryRegion))
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = "Failed to get the region of the company",
                        };
                    }
                    region = countryRegion;
                }

                regionToReturn = region == SubscriptionRegions.Europe.ToString()
                    ? SubscriptionRegions.Europe
                    : SubscriptionRegions.RestOfTheWorld;
            }
            else
            {
                regionToReturn = SubscriptionRegions.Nigeria;
            }

            return new GenericResponse
            {
                Data = regionToReturn,
                ResponseCode = "200",
                ResponseMessage = "Region retrieved successfully",
            };
        }

        private async Task SaveBackgroundJobId(string subscriptionId, string jobId)
        {
            var bkJob = new BackGroundJobId
            {
                JobId = jobId,
                EventId = subscriptionId,
                JobType = JobType.Scheduled
            };

            _publicContext.BackGroundJobIds.Add(bkJob);
            await _publicContext.SaveChangesAsync();
        }

        private static IQueryable<GetEnterpriseSubscriptionDto> ApplySearchKeyword(IQueryable<GetEnterpriseSubscriptionDto> query, string searchKeyword)
        {
            searchKeyword = searchKeyword.ToLower();

            query = query.Where(t =>
                t.CompanyName != null && t.CompanyName.ToLower().Contains(searchKeyword) ||
                t.AdminEmail != null && t.AdminEmail.ToLower().Contains(searchKeyword) ||
                t.CompanyType != null && t.CompanyType.ToLower().Contains(searchKeyword) ||
                t.Location != null && t.Location.ToLower().Contains(searchKeyword)
                );
            return query;
        }

        public async Task PublishSubscriptionEvent(string subscriptionId, SubscriptionStatusForRMQ status, ReasonForSubscriptionStatus? reason)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id.ToString() == subscriptionId);
            if (subscription is not null)
            {
                if (status != SubscriptionStatusForRMQ.New && status != SubscriptionStatusForRMQ.Cancelled)
                {
                    Enum.TryParse(subscription.Status, out PaymentStatus subStatus);
                    switch (subStatus)
                    {
                        case PaymentStatus.Successful:
                            status = SubscriptionStatusForRMQ.Active;
                            break;
                        case PaymentStatus.Failed:
                            status = SubscriptionStatusForRMQ.Inactive;
                            break;
                        default:
                            break;
                    }
                }

                var subEvent = new SubscriptionEventDto
                {
                    Id = subscription.Id.ToString(),
                    TenantId = subscription.TenantId.Value.ToString(),
                    CreatedOn = DateTime.UtcNow,
                    Product = subscription.Application.ToString(),
                    Status = status.ToString(),
                    Reason = reason.ToString(),
                };

                var plan = await _publicContext.PricingPlans.Where(x => x.Id == subscription.PricingPlanId).Select(p => p.Name).FirstOrDefaultAsync();
                subEvent.Plan = plan;

                var eventModel = new PublishModel
                           (
                               RabbitMQConstants.SubscriptionEvent,
                               "",
                               ExchangeType.Fanout,
                               subEvent
                           );

                var eventRes = await _publisherService.GenericPublish(eventModel);
                if (!eventRes)
                {
                    _logger.Error("subscription event could not be published");
                    WatchLogger.Log("Subscription event could not be published");
                }
            }
        }

        private async Task<GenericResponse> CreateEnterpriseSubscription(EnterPriseOptionsDto model)
        {
            var company = await _publicContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == model.TenantId);
            if (company is null)
                throw new RecordNotFoundException("Company not found");

            await using var _context = new JobProDbContext(_conString, new DbContextSchema(company.Subdomain));
            Models.Subscription subscription = null;
            var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id.ToString() == model.PlanId);
            if (plan.Name != PricingPlans.Enterprise.ToString())
                throw new RecordNotFoundException("The plan must be Enterprise");

            subscription = new Models.Subscription();
            subscription.PricingPlanId = Guid.Parse(model.PlanId);
            subscription.SubscriptionFor = model.UsersLimit;
            subscription.Amount = model.AmountPaid;
            subscription.Status = PaymentStatus.Successful.ToString();
            subscription.ActivatedOn = model.ActivatedOn ?? DateTime.UtcNow;
            subscription.ExpiresOn = model.ExpiresOn;
            subscription.TransactionDate = model.TransactionDate;
            subscription.Currency = model.Currency;
            subscription.TransactionCode = model.TransactionCode;
            subscription.Application = model.Application;
            subscription.CreatedAt = DateTime.UtcNow;
            subscription.TenantId = company.Id;
            subscription.UpdatedAt = DateTime.UtcNow;
            subscription.UserId = model.LoggedInUserId;
            subscription.PaymentProvider = model.Provider;
            subscription.PaymentId = model.PaymentId;

            var subscriptionHistory = _mapper.Map<SubscriptionHistory>(subscription);
            subscriptionHistory.Id = Guid.NewGuid();
            await _publicContext.SubscriptionHistory.AddAsync(subscriptionHistory);
            await _publicContext.Subscriptions.AddAsync(subscription);

            model.SubscriptionId = subscription.Id.ToString();
            var enterpriseSub = _mapper.Map<EnterpriseSubscription>(model);
            enterpriseSub.SubscriptionId = subscription.Id;
            enterpriseSub.CreatedBy = model.LoggedInUserId;
            enterpriseSub.AiAssistant = model.AiAssistants;

            await _publicContext.EnterpriseSubscriptions.AddAsync(enterpriseSub);

            // Update companySubcription table
            var companySub = await _publicContext.CompanySubscriptions
                .FirstOrDefaultAsync(c => c.TenantId.ToString() == company.Id.ToString() && c.Application == model.Application);
            if (companySub is not null)
            {
                companySub.Status = SubscriptionStatus.Active;
                companySub.UpdatedAt = subscription.UpdatedAt;

                _publicContext.CompanySubscriptions.Update(companySub);
            }
            else
            {
                companySub = new CompanySubscription()
                {
                    Status = SubscriptionStatus.Active,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Application = model.Application,
                    TenantId = company.Id,
                    SubscriptionId = subscription.Id,
                };

                await _publicContext.CompanySubscriptions.AddAsync(companySub);
            }

            // Update user's app permissions - disable the app permissions for the users
            var permissions = await _context.AppPermissions
                .Where(x => x.TenantId == company.Id.ToString() && x.Application == model.Application.ToString() && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Inactive.ToString()).ToListAsync();
            if (permissions.Any())
            {
                foreach (var permission in permissions)
                {
                    permission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                }
                _context.AppPermissions.UpdateRange(permissions);
            }
            else
            {
                // Get all the users in the comapany
                permissions = new List<AppPermissions>();
                var users = await _context.UserProfiles.ToListAsync();
                if (users.Count < model.UsersLimit && model.UsersLimit != 0 || model.UsersLimit == 0)
                {
                    foreach (var user in users)
                    {
                        var permission = new AppPermissions
                        {
                            UserId = user.UserId,
                            TenantId = company.Id.ToString(),
                            Application = model.Application.ToString(),
                            IsEnabled = true,
                            SubscriptionStatus = SubscriptionStatus.Active.ToString(),
                        };
                        permissions.Add(permission);
                    }

                    await _context.AppPermissions.AddRangeAsync(permissions);
                }
                else
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "The number of users in the company exceeds the number of users covered by the subscription",
                        Data = false
                    };
                }
            }

            // Mark the payment details as used
            if (!model.CreateWithOutPayment)
            {
                var paymentDetails = await _publicContext.EnterprizeSubscriptionPayments
                    .FirstOrDefaultAsync(x => x.PaymentId == model.PaymentId);

                paymentDetails.PaymentUsed = true;
                paymentDetails.TenantId = company.Id;
                _publicContext.EnterprizeSubscriptionPayments.Update(paymentDetails);
            }

            await _context.SaveChangesAsync();
            var res = await _publicContext.SaveChangesAsync();

            if (!model.CreateWithOutPayment)
            {
                // Trigger a background job that will update subscription status a day after expiration date
                var jobId = BackgroundJob.Schedule<BackGroundServices>(x => x.UpdateSubscriptionStatus(SubscriptionStatus.Inactive, subscription.Id.ToString(), null, company.Subdomain, company.Id.ToString(), model.Application.ToString(), null), model.ExpiresOn.AddDays(1));

                if (!string.IsNullOrEmpty(jobId))
                {
                    await SaveBackgroundJobId(enterpriseSub.SubscriptionId.ToString(), jobId);
                }
            }

            var responseCode = res > 0 ? "200" : "500";
            await SendPaidPlanNotification(responseCode, subscription);

            var message = "Your enterprise subscription has been activated successfully.";
            var errorMessage = "Enterprise subscription was not activated successfully";
            return new GenericResponse
            {
                ResponseCode = res > 0 ? "200" : "500",
                ResponseMessage = res > 0 ? message : errorMessage,
                Data = res > 0
            };
        }

        private static IQueryable<Models.Subscription> ApplySorting(IQueryable<Models.Subscription> query, string sortBy)
        {
            return sortBy switch
            {
                "CreatedAt" => query.OrderBy(s => s.Tenant.DateCreated),
                "CompanyName" => query.OrderBy(s => s.Tenant.CompanyName),
                "Country" => query.OrderBy(s => s.Tenant.Country),
                "VerifiedEmailDomain" => query.OrderBy(s => s.Tenant.VerifiedEmailDomain),
                _ when string.IsNullOrEmpty(sortBy) => query.OrderBy(s => s.CreatedAt),
                _ => throw new ArgumentException("Invalid property for sorting.", nameof(sortBy)),
            };
        }

        private async Task<int> TotalSubscription(Applications application, PaymentProviders? paymentProvider)
        {
            var query = _publicContext.Subscriptions.Where(s => s.Application == application);
            if (paymentProvider != null)
                query = query.Where(subscription => subscription.PaymentProvider.Equals(paymentProvider));
            return await query.CountAsync();
        }

        private async Task<int> TotalCompanies(Applications application, PaymentProviders? paymentProvider)
        {
            var query = _publicContext.Subscriptions.Where(s => s.Application == application);
            if (paymentProvider != null)
                query = query.Where(subscription => subscription.PaymentProvider.Equals(paymentProvider));
            return await query.Select(s => s.TenantId).Distinct().CountAsync();
        }

        private async Task<double> TotalRevenue(Applications application, PaymentProviders? paymentProvider)
        {
            var query = _publicContext.Subscriptions
                .Join(
                    _publicContext.PackagePricing,
                    subscription => subscription.PricingPlanId,
                    packagePricing => packagePricing.PricingPlanId,
                    (subscription, packagePricing) => new
                    {
                        subscription.Amount,
                        packagePricing.Application,
                        subscription.PaymentProvider
                    }
                    )
                    .Where(result => result.Application == application.ToString());
            if (paymentProvider != null)
                query = query.Where(subscription => subscription.PaymentProvider.Equals(paymentProvider));
            return await query.SumAsync(result => (double?)result.Amount ?? 0);
        }

        private static double CalculatePercentageIncrement(int allCount, int currentCount)
        {
            if (allCount == 0)
                return 0.0;
            if (currentCount == 0)
                return 0.0;

            return currentCount * 100 / allCount;
        }
        #endregion

        #region Get Revenue per provider
        /// <summary>
        /// This method is used to get revenue per provider
        /// </summary>
        /// <param name="application"></param>
        /// <param name="fromDate"></param>
        /// <param name="periodFilter"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public async Task<List<ProviderRevenueDto>> GetRevenueByProvider(Applications application, TimePeriodFilter periodFilter, DateTime? fromDate, DateTime? toDate)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredSubscriptions(application, null, periodFilter, fromDate, toDate);
            return await subscriptionsQuery
                .Where(sb => sb.PaymentProvider != null)  // Ensure PaymentProvider is not null
                .GroupBy(sb => new { sb.PaymentProvider })
                .Select(group => new ProviderRevenueDto
                {
                    PaymentProvider = group.Key.PaymentProvider,
                    TotalRevenue = group.Sum(sb => sb.Amount)
                })
                .ToListAsync();

        }
        #endregion

        #region Get Total Subscription counts
        /// <summary>
        /// This method is used to get subscription count
        /// </summary>
        /// <param name="application"></param>
        /// <param name="periodFilter"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <param name="paymentProvider"></param>
        /// <returns></returns>
        public async Task<List<TotalSubscriptionCountPerPlanDto>> GetTotalSubscriptionsCount(Applications application, PaymentProviders? paymentProvider, TimePeriodFilter periodFilter, DateTime? fromDate, DateTime? toDate)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredSubscriptions(application, paymentProvider, periodFilter, fromDate, toDate);
            return await GetTotalSubscriptionCountsForPlans(application, subscriptionsQuery);
        }
        #endregion

        #region Filter Subscription By Date
        /// <summary>
        /// This method is used to add date filtering to subscription
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <param name="timePeriodFilter"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        private IQueryable<Models.Subscription> GetFilteredSubscriptions(Applications application, PaymentProviders? paymentProvider, TimePeriodFilter timePeriodFilter, DateTime? fromDate, DateTime? toDate)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = _publicContext.Subscriptions
                .Where(subscription => subscription.Application == application);

            if (paymentProvider == PaymentProviders.Mollie || paymentProvider == PaymentProviders.Stripe)
                subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.PaymentProvider.Equals(paymentProvider));

            switch (timePeriodFilter)
            {
                case TimePeriodFilter.OneMonth:
                    subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.CreatedAt >= DateTime.UtcNow.AddMonths(-1));
                    break;
                case TimePeriodFilter.ThreeMonths:
                    subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.CreatedAt >= DateTime.UtcNow.AddMonths(-3));
                    break;
                case TimePeriodFilter.OneYear:
                    subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.CreatedAt >= DateTime.UtcNow.AddYears(-1));
                    break;
                case TimePeriodFilter.AllTime:
                    break;

                case TimePeriodFilter.Custom:
                    if (fromDate.HasValue && toDate.HasValue)
                        subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.CreatedAt >= fromDate && subscription.CreatedAt <= toDate);
                    else
                        throw new ArgumentException("Invalid custom time period. Please provide valid fromDate and toDate.");
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(timePeriodFilter), "Invalid time period filter.");
            }

            return subscriptionsQuery;
        }
        #endregion

        #region Get Total Subscription counts for each plan
        /// <summary>
        /// This method is used to get subscription count
        /// </summary>
        /// <param name="application"></param>
        /// <param name="subscriptionsQuery"></param>
        /// <returns></returns>
        private async Task<List<TotalSubscriptionCountPerPlanDto>> GetTotalSubscriptionCountsForPlans(Applications application, IQueryable<Models.Subscription> subscriptionsQuery)
        {
            var totalSubscriptionCounts = await _publicContext.PackagePricing
                .Include(packagePricing => packagePricing.PricingPlan)
                .Where(packagePricing => packagePricing.Application == application.ToString())
                .Select(packagePricing => new TotalSubscriptionCountPerPlanDto
                {
                    Name = packagePricing.PricingPlan.Name,
                    PricingPlanId = packagePricing.PricingPlanId,
                    SubscriptionCount = subscriptionsQuery.Count(subscription => subscription.PricingPlanId == packagePricing.PricingPlanId)
                })
                .ToListAsync();

            return totalSubscriptionCounts;
        }
        #endregion

        #region Get Percentage increment
        /// <summary>
        /// This method is used to get subscription percentage increment
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <param name="timePeriodFilter"></param>
        /// <param name="fromDate"></param>
        /// <returns></returns>
        public async Task<List<PercentageIncrementPerPlanDto>> GetPercentageIncrementPerPlan(Applications application, PaymentProviders? paymentProvider, TimePeriodFilter timePeriodFilter, DateTime? fromDate, DateTime? toDate)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredSubscriptions(application, paymentProvider, timePeriodFilter, fromDate, toDate);
            return await CalculatePercentageIncrement(application, paymentProvider, timePeriodFilter, subscriptionsQuery);
        }
        #endregion

        #region Calculate Percentage increment
        /// <summary>
        /// This method is used to get subscription percentage increment
        /// </summary>
        /// <param name="application"></param>
        /// <param name="paymentProvider"></param>
        /// <param name="subscriptionsQuery"></param>
        /// <returns></returns>
        private async Task<List<PercentageIncrementPerPlanDto>> CalculatePercentageIncrement(Applications application, PaymentProviders? paymentProvider, TimePeriodFilter timePeriodFilter, IQueryable<Models.Subscription> subscriptionsQuery)
        {
            var percentageIncrements = new List<PercentageIncrementPerPlanDto>();

            var allTimePeriodSubscriptions = await GetFilteredSubscriptions(application, paymentProvider, timePeriodFilter, null, null).ToListAsync();
            var currentTimePeriodSubscriptions = await subscriptionsQuery.ToListAsync();

            var allTimePeriodCounts = allTimePeriodSubscriptions.GroupBy(subscription => subscription.PricingPlanId)
                .ToDictionary(group => group.Key, group => group.Count());

            var currentTimePeriodGrouped = currentTimePeriodSubscriptions
                .GroupBy(subscription => subscription.PricingPlanId)
                .ToDictionary(group => group.Key, group => new
                {
                    Count = group.Count(),
                    RevenueSum = group.Sum(sub => sub.Amount)
                });

            var allPlanIds = await _publicContext.PricingPlans
                .Where(plan => plan.Application == application).Select(p => p.Id).ToListAsync();
            foreach (var planId in allPlanIds)
            {
                int allCount = allTimePeriodCounts.ContainsKey(planId) ? allTimePeriodCounts[planId] : 0;
                int currentCount = currentTimePeriodGrouped.ContainsKey(planId) ? currentTimePeriodGrouped[planId].Count : 0;
                double percentageIncrement = CalculatePercentageIncrement(allCount, currentCount);
                var planName = _publicContext.PricingPlans.Where(plan => plan.Id == planId).Select(plan => plan.Name).FirstOrDefault();

                percentageIncrements.Add(new PercentageIncrementPerPlanDto
                {
                    PricingPlanId = planId,
                    Name = planName,
                    SubscriptionCount = currentCount,
                    PercentageIncrement = percentageIncrement,
                    Revenue = currentTimePeriodGrouped.ContainsKey(planId) ? currentTimePeriodGrouped[planId].RevenueSum : 0
                });
            }
            return percentageIncrements;
        }
        #endregion

        #region Get Company details
        /// <summary>
        /// This method is used to get subsription companies details
        /// </summary>
        /// <param name="subscriptionQueryParameters"></param>
        /// <returns></returns>
        public async Task<Page<SubscriptionCompanyDetail>> GetSubscribedCompanyDetail(SubscriptionQueryParameters subscriptionQueryParameters)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredSubscriptions(subscriptionQueryParameters.Application, subscriptionQueryParameters.Provider, subscriptionQueryParameters.PeriodFilter, subscriptionQueryParameters.FromDate, subscriptionQueryParameters.ToDate);
            subscriptionsQuery = subscriptionsQuery
                .Include(subscription => subscription.Tenant)
                .ThenInclude(t => t.Admin)
                .Include(subscription => subscription.PricingPlan);

            if (!string.IsNullOrEmpty(subscriptionQueryParameters.PlanId))
                subscriptionsQuery = subscriptionsQuery.Where(subcription => subcription.PricingPlanId.Equals(subscriptionQueryParameters.PlanId));

            subscriptionsQuery.GroupBy(sb => new { sb.Tenant });
            subscriptionsQuery = ApplySorting(subscriptionsQuery, subscriptionQueryParameters.SortBy);
            return await subscriptionsQuery
                  .Select(s => new SubscriptionCompanyDetail()
                  {
                      CompanyName = s.Tenant.CompanyName,
                      Country = s.Tenant.Country,
                      Email = (_publicContext.UserCompanies.FirstOrDefault(x => x.UserId == s.Tenant.Admin.Id)).Email,
                      PlanName = s.PricingPlan.Name,
                      RegistrationDate = s.Tenant.DateCreated,
                      StaffSize = s.Tenant.CompanySize,
                      TenantId = s.TenantId != null ? s.TenantId.ToString() : string.Empty,
                      Subdomain = s.Tenant.Subdomain,
                  })
                  .ToPageListAsync(subscriptionQueryParameters.PageNumber, subscriptionQueryParameters.PageSize);
        }
        #endregion

        #region Get Subscribed And Unsubscribed Company details
        /// <summary>
        /// This method is used to get both subscribed and unsubscribed companies details
        /// </summary>
        /// <param name="subscriptionQueryParameters"></param>
        /// <returns></returns>
        public async Task<Page<SubscriptionCompanyDetail>> GetSubscribedAndUnsubscribedCompanyDetail(SubscriptionQueryParameters subscriptionQueryParameters)
        {
            // Get all companies (tenants) filtered by application and time period
            var allTenantsQuery = _publicContext.Tenants
                .Include(t => t.Admin)
                .AsQueryable();

            // Apply time period filtering if specified
            if (subscriptionQueryParameters.PeriodFilter != TimePeriodFilter.AllTime)
            {
                switch (subscriptionQueryParameters.PeriodFilter)
                {
                    case TimePeriodFilter.OneMonth:
                        allTenantsQuery = allTenantsQuery.Where(t => t.DateCreated >= DateTime.UtcNow.AddMonths(-1));
                        break;
                    case TimePeriodFilter.ThreeMonths:
                        allTenantsQuery = allTenantsQuery.Where(t => t.DateCreated >= DateTime.UtcNow.AddMonths(-3));
                        break;

                    case TimePeriodFilter.OneYear:
                        allTenantsQuery = allTenantsQuery.Where(t => t.DateCreated >= DateTime.UtcNow.AddYears(-1));
                        break;
                    case TimePeriodFilter.Custom:
                        if (subscriptionQueryParameters.FromDate.HasValue && subscriptionQueryParameters.ToDate.HasValue)
                            allTenantsQuery = allTenantsQuery.Where(t => t.DateCreated >= subscriptionQueryParameters.FromDate && t.DateCreated <= subscriptionQueryParameters.ToDate);
                        break;
                }
            }

            // Get subscriptions to join with tenants
            IQueryable<Models.Subscription> subscriptionsQuery = _publicContext.Subscriptions
                .Where(s => s.Application == subscriptionQueryParameters.Application)
                .Include(s => s.PricingPlan);

            // Apply payment provider filter if specified
            if (subscriptionQueryParameters.Provider.HasValue)
            {
                subscriptionsQuery = subscriptionsQuery.Where(s => s.PaymentProvider.Equals(subscriptionQueryParameters.Provider));
            }

            // Apply plan filter if specified
            if (!string.IsNullOrEmpty(subscriptionQueryParameters.PlanId))
            {
                subscriptionsQuery = subscriptionsQuery.Where(s => s.PricingPlanId.ToString() == subscriptionQueryParameters.PlanId);
            }

            // Left join tenants with subscriptions to get both subscribed and unsubscribed companies
            var combinedQuery = from tenant in allTenantsQuery
                               join subscription in subscriptionsQuery on tenant.Id equals subscription.TenantId into subscriptionGroup
                               from subscription in subscriptionGroup.DefaultIfEmpty()
                               select new SubscriptionCompanyDetail
                               {
                                   CompanyName = tenant.CompanyName,
                                   Country = tenant.Country,
                                   Email = (_publicContext.UserCompanies.FirstOrDefault(x => x.UserId == tenant.Admin.Id)).Email,
                                   PlanName = subscription != null ? subscription.PricingPlan.Name : "No Subscription",
                                   RegistrationDate = tenant.DateCreated,
                                   StaffSize = tenant.CompanySize,
                                   TenantId = tenant.Id.ToString(),
                                   Subdomain = tenant.Subdomain,
                               };

            // Apply sorting
            combinedQuery = ApplySortingForCompanyDetails(combinedQuery, subscriptionQueryParameters.SortBy);

            // Return paginated results
            return await combinedQuery.ToPageListAsync(subscriptionQueryParameters.PageNumber, subscriptionQueryParameters.PageSize);
        }

        /// <summary>
        /// Apply sorting for company details query
        /// </summary>
        /// <param name="query"></param>
        /// <param name="sortBy"></param>
        /// <returns></returns>
        private static IQueryable<SubscriptionCompanyDetail> ApplySortingForCompanyDetails(IQueryable<SubscriptionCompanyDetail> query, string sortBy)
        {
            return sortBy switch
            {
                "CreatedAt" => query.OrderBy(s => s.RegistrationDate),
                "CompanyName" => query.OrderBy(s => s.CompanyName),
                "Country" => query.OrderBy(s => s.Country),
                "PlanName" => query.OrderBy(s => s.PlanName),
                _ when string.IsNullOrEmpty(sortBy) => query.OrderBy(s => s.RegistrationDate),
                _ => throw new ArgumentException("Invalid property for sorting.", nameof(sortBy)),
            };
        }
        #endregion

        #region Get Tenant Subscription History
        public async Task<Page<TenantSubscription>> GetTenantsSubscriptionHistory(SubscriptionQueryParameters parameters)
        {

            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredSubscriptions(parameters.Application, parameters.Provider, parameters.PeriodFilter, parameters.FromDate, parameters.ToDate);
            subscriptionsQuery = subscriptionsQuery
                .Include(subscription => subscription.Tenant)
                .ThenInclude(t => t.Admin)
                .Include(subscription => subscription.PricingPlan);

            if (!string.IsNullOrEmpty(parameters.PlanId))
                subscriptionsQuery = subscriptionsQuery.Where(subcription => subcription.PricingPlanId.Equals(parameters.PlanId));
            subscriptionsQuery = ApplySorting(subscriptionsQuery, parameters.SortBy);

            return await subscriptionsQuery
            .Select(s => new TenantSubscription()
            {
                CompanyName = s.Tenant.CompanyName,
                Country = s.Tenant.Country,
                Email = s.Tenant.Admin.Email,
                PlanName = s.PricingPlan.Name,
                TransactionDate = s.TransactionDate,
                UsersInPlan = s.SubscriptionFor.Value,
                Amount = s.Amount,
                TenantId = s.TenantId != null ? s.TenantId.ToString() : string.Empty,
                PaymentProvider = s.PaymentProvider.ToString()
            }).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

        }
        #endregion

        #region Get Subscription History
        public async Task<Page<TenantSubscriptionDetail>> GetTenantSubscriptionHistory(TenantSubscriptionQueryParameters parameters)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredSubscriptions(parameters.Application, parameters.Provider, parameters.PeriodFilter, parameters.FromDate, parameters.ToDate);
            subscriptionsQuery = subscriptionsQuery
                .Where(subscription => subscription.TenantId.ToString() == parameters.TenantId)
                .Include(subscription => subscription.Tenant)
                .ThenInclude(t => t.Admin)
                .Include(subscription => subscription.PricingPlan);

            return await subscriptionsQuery.Select(s => new TenantSubscriptionDetail()
            {
                CompanyName = s.Tenant.CompanyName,
                Country = s.Tenant.Country,
                Email = s.Tenant.Admin.Email,
                PlanName = s.PricingPlan.Name,
                TransactionDate = s.TransactionDate,
                UsersInPlan = s.SubscriptionFor.Value,
                Amount = s.Amount,
                TenantId = s.TenantId != null ? s.TenantId.ToString() : string.Empty,
                PaymentProvider = s.PaymentProvider.ToString(),
                ActivatedOn = s.ActivatedOn,
                ExpiresOn = s.ExpiresOn,
                Status = s.Status
            })
           .ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }
        #endregion

        #region Notifications
        private async Task SendPaidPlanNotification(string responseCode, Models.Subscription subscription)
        {
            var tenant = await _publicContext.Tenants.Where(x => x.Id == subscription.TenantId)
                .Include(x => x.Admin)
                .Select(t => new { t.Subdomain, t.Admin.Email, t.Admin.Id, TenantId = t.Id }).FirstOrDefaultAsync();

            // Check if billing information exists
            var billingInfo = await _publicContext.BillingInformations.FirstOrDefaultAsync(x => x.TenantId == tenant.TenantId);
            string recipientName;
            var emailsToSendNotificationTo = new List<string>();

            using var context = new JobProDbContext(_conString, new DbContextSchema(tenant.Subdomain));
            var userProfile = await context.UserProfiles.Where(x => x.UserId == tenant.Id)
                .Select(profile => new { profile.Email, profile.FirstName, profile.LastName }).FirstOrDefaultAsync();
            
            // Use billing information if available, otherwise use admin details
            if (billingInfo != null)
            {
                emailsToSendNotificationTo.Add(billingInfo.Email);
                recipientName = $"{billingInfo.FirstName} {billingInfo.LastName}";
            }
            else
            {
                emailsToSendNotificationTo.Add(tenant.Email);
                emailsToSendNotificationTo.Add(userProfile.Email);
                recipientName = $"{userProfile.FirstName} {userProfile.LastName}";
            }

            var plan = await _publicContext.PricingPlans.Where(x => x.Id == subscription.PricingPlanId).Select(p => p.Name).FirstOrDefaultAsync();
            var months = new List<string> { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };

            var month = months[subscription.ExpiresOn.Value.Month - 1];
            var day = subscription.ExpiresOn.Value.Day.ToString();
            var year = subscription.ExpiresOn.Value.Year.ToString();
            var dayOfTheWeek = subscription.ExpiresOn.Value.DayOfWeek.ToString();
            var amount = subscription.Amount.ToString();
            var currency = subscription.Currency.ToString();

            if (responseCode == "200")
            {
                // Send an email to the user to notify them of their plan
                // Format the ammount to currency
                var formattedAmount = "";
                if (decimal.TryParse(amount, out decimal newAmount))
                {
                    formattedAmount = string.Format(CultureInfo.CurrentCulture, "{0:C}", newAmount);
                }
                else
                {
                    _logger.Error("Amount could not be formatted to currency");
                }

                // Remove the currency symbol which is the first character
                formattedAmount = formattedAmount.Substring(1);

                var interval = subscription.Interval == SubscriptionInterval.Monthly.ToString() ? "month" : "year";
                var template = Extensions.ReadTemplateFromFile("paid-plan-notofication", _environment);
                template = template.Replace("{name}", recipientName)
                    .Replace("{plan}", plan).Replace("{month}", month)
                    .Replace("{day}", dayOfTheWeek).Replace("{year}", year).Replace("{day-figure}", day).Replace("{currency}", currency).Replace("{amount}", formattedAmount).Replace("{interval}", interval).Replace("{admin-url}", Constants.ADMIN_URL);
                BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(template, emailsToSendNotificationTo, "Welcome to JobPro - Plan Activated"));
            }
        }
        #endregion

        #region Get Transaction History
        public async Task<Page<TransactionHistoryResponse>> GetTransactionHistory(PaginationParameters parameters, Applications application, PaymentProviders? paymentProvider, string sortBy, string planId, string companyName)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = GetFilteredTransactionHistory(application, paymentProvider, parameters.StartDate, parameters.EndDate, planId, companyName);

            subscriptionsQuery = ApplySortingForTransactionHistory(subscriptionsQuery, sortBy);

            return await subscriptionsQuery
            .Select(s => new TransactionHistoryResponse()
            {
                CompanyName = s.Tenant.CompanyName,
                TransactionDate = s.TransactionDate,
                Amount = s.Amount,
                Package = application.ToString(),
                TransactionType = TransactionType.Credit.ToString(),
                PaymentProviders = s.PaymentProvider,
                Plans = s.PricingPlan.Name
            }).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

        }
        #endregion

        #region Get Corperate wallet balance
        public Task<GetCorperateWalletBalanceResponse> GetCorperateWalletBalance()
        {
            IQueryable<SubscriptionHistory> subscriptionsQuery = _publicContext.SubscriptionHistory;
            return Task.FromResult(new GetCorperateWalletBalanceResponse()
            {
                MoneyIn = subscriptionsQuery.Sum(x => x.Amount)
            });
        }
        #endregion

        #region Get All Wallet balances
        public Task<List<GetAllWalletbalancesResponse>> GetAllWalletbalances()
        {
            var response = new List<GetAllWalletbalancesResponse>();

            IQueryable<Models.Subscription> subscriptionsQuery = _publicContext.Subscriptions;

            foreach (var item in Enum.GetValues(typeof(Applications)).Cast<Applications>())
            {
                response.Add(new GetAllWalletbalancesResponse
                {
                    Amount = subscriptionsQuery.Where(x => x.Application == item).Sum(x => x.Amount),
                    Package = item
                });
            }
            return Task.FromResult(response);
        }
        #endregion

        #region Get Failed Payments
        public async Task<Page<TransactionHistoryResponse>> GetFailedPayments(PaginationParameters parameters, Applications application)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = _publicContext.Subscriptions
                          .Where(subscription => subscription.Application == application && subscription.Status != "Successful");

            return await subscriptionsQuery
            .Select(s => new TransactionHistoryResponse()
            {
                CompanyName = s.Tenant.CompanyName,
                TransactionDate = s.TransactionDate,
                Amount = s.Amount,
                Package = application.ToString(),
                TransactionType = TransactionType.Credit.ToString() //transaction type is not in the DB
            }).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

        }
        #endregion

        #region Get Total Wallet Available Balance
        public Task<GetTotalWalletBalanceResponse> GetTotalWalletbalances()
        {
            IQueryable<Models.Subscription> subscriptionsQuery = _publicContext.Subscriptions;
            return Task.FromResult(new GetTotalWalletBalanceResponse()
            {
                Balance = subscriptionsQuery.Where(subscription => subscription.Status == "Successful").Sum(x => x.Amount)
            });
        }
        #endregion

        #region Get Filtered transaction history 
        private IQueryable<Models.Subscription> GetFilteredTransactionHistory(Applications application, PaymentProviders? paymentProvider, DateTime? fromDate, DateTime? toDate, string planId, string companyName)
        {
            IQueryable<Models.Subscription> subscriptionsQuery = _publicContext.Subscriptions
                .Where(subscription => subscription.Application == application)
               .Include(subscription => subscription.Tenant)
               .ThenInclude(t => t.Admin)
               .Include(subscription => subscription.PricingPlan);

            if (!string.IsNullOrEmpty(planId))
                subscriptionsQuery = subscriptionsQuery.Where(subcription => subcription.PricingPlanId.ToString() == planId);

            if (!string.IsNullOrEmpty(companyName))
                subscriptionsQuery = subscriptionsQuery.Where(subcription => subcription.Tenant.CompanyName.ToUpper() == companyName.ToUpper());

            if (paymentProvider != null)
                subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.PaymentProvider.Equals(paymentProvider));

            if (fromDate.HasValue && toDate.HasValue)
                subscriptionsQuery = subscriptionsQuery.Where(subscription => subscription.CreatedAt >= fromDate && subscription.CreatedAt <= toDate);

            return subscriptionsQuery;
        }
        #endregion

        #region Sort transaction history 
        private static IQueryable<Models.Subscription> ApplySortingForTransactionHistory(IQueryable<Models.Subscription> query, string sortBy)
        {
            return sortBy switch
            {
                "Plan" => query.OrderByDescending(s => s.PricingPlan.Name),
                "CompanyName" => query.OrderByDescending(s => s.Tenant.CompanyName),
                "Application" => query.OrderByDescending(s => s.Application),
                "Provider" => query.OrderByDescending(s => s.PaymentProvider),
                "Amount" => query.OrderByDescending(s => s.Amount),
                _ when string.IsNullOrEmpty(sortBy) => query.OrderByDescending(s => s.CreatedAt),
                _ => throw new ArgumentException("Invalid property for sorting.", nameof(sortBy)),
            };
        }
        #endregion
    }
}