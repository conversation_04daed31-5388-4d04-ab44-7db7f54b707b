﻿using DocumentFormat.OpenXml.Spreadsheet;
using Jobid.App.ActivityLog.Model;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Helpers.Enums.Applications.Joble)]
    public class TimeSheetController : BaseController
    {
        private readonly IUnitofwork Services_Repo;

        private IEmailService _emailService;
        private readonly ILogger _logger = Log.ForContext<TimeSheetController>();
        public TimeSheetController(IUnitofwork unitofwork, IEmailService emailService)
        {
            this.Services_Repo = unitofwork;
            _emailService = emailService;
        }

        #region Add TimeSheet
        /// <summary>
        /// Add TimeSheet
        /// </summary>
        /// <param name="timeSheetVm"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpPost]
        [Route("AddTimeSheet")]
        public async Task<ActionResult> AddTImeSheet([FromBody] TimeSheetVm timeSheetVm)
        {
            try
            {
                var userId = HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier).Value;
                timeSheetVm.UserId = userId;
                if (timeSheetVm.EndTime <= timeSheetVm.StartTime)
                {
                    return BadRequest(new ApiResponse<TimeSheet>
                    {
                        ResponseMessage = "Invalid Date Range ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                var result = await Services_Repo.TimeSheetService.AddTimeSheet(timeSheetVm);

                return Ok(new ApiResponse<TimeSheet>
                {
                    ResponseMessage = "TimeSheet Created Successfully",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Record not found");
                return BadRequest(new ApiResponse<TimeSheet>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Process failed, please try again later",
                    ResponseCode = "500",
                    Data = null
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                _logger.Error(ex, "Record already exist", nameof(AddTImeSheet));
                return BadRequest(new ApiResponse<TimeSheet>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion


        #region Update TimeSheet
        /// <summary>
        /// Updates TimeSheet
        /// </summary>
        /// <param name="timeSheetVm"></param>
        /// <param name="timesheetId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet, Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("UpdateTimeSheet/{timesheetId}")]
        public async Task<ActionResult> UpdateTimeSheet([FromBody] TimeSheetVm timeSheetVm, Guid timesheetId)
        {
            try
            {
                if (timeSheetVm.EndTime <= timeSheetVm.StartTime)
                {
                    return BadRequest(new ApiResponse<TimeSheet>
                    {
                        ResponseMessage = "Invalid Date Range ",
                        ResponseCode = "400",
                        Data = null
                    });
                }
                var result = await Services_Repo.TimeSheetService.UpdateTimeSheet(timeSheetVm, timesheetId);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "TimeSheet Updated Successfully",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Record not found", nameof(UpdateTimeSheet));
                return BadRequest(new ApiResponse<TimeSheet>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    ResponseCode = "500",
                    Data = null
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                _logger.Error(ex, "Record already exist", nameof(UpdateTimeSheet));
                return BadRequest(new ApiResponse<TimeSheet>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get teamsheet records
        /// <summary>
        /// Get teamsheet records
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="filters"></param>
        /// <param name="projectIds"></param>
        /// <param name="isArchived"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("GetTimeSheetRecords")]
        public async Task<IActionResult> GetTodosByProjectId([FromQuery] PaginationParameters parameters, [FromQuery] List<string> projectIds, [FromBody] TimeSheetFilters filters, bool isArchived = false)
        {
            try
            {
                filters.UserId = CurrentUserId.ToString();
                var result = await this.Services_Repo.TimeSheetService.GetTimeSheetRecords(parameters, projectIds, filters, isArchived);
                return Ok(result);

            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Record not found", nameof(GetTodosByProjectId));
                return BadRequest(new ApiResponse<Page<TimeSheetTodoDto>>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Process failed, please try again later",
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get TimeSheet Records for project
        /// <summary>
        /// Get TimeSheet records
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="filters"></param>
        /// <param name="isArchived"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("GetTimeSheetRecordsForProjects")]
        public async Task<IActionResult> GetTimeSheetRecordsForProjects([FromQuery] PaginationParameters parameters, [FromBody] TimeSheetFilters filters, bool isArchived = false)
        {
            try
            {
                filters.UserId = CurrentUserId.ToString(); // Assuming CurrentUserId is set appropriately
                var result = await this.Services_Repo.TimeSheetService.GetTimeSheetRecordsForProjects(parameters, filters, isArchived);
                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Record not found", nameof(GetTimeSheetRecordsForProjects));
                return BadRequest(new ApiResponse<GenericResponse>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Process failed, please try again later",
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion


        #region Get All TimeSheets
        /// <summary>
        /// Get All TimeSheets
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetTimeSheet")]
        public async Task<ApiResponse<Page<TimeSheet>>> GetTimeSheet([FromQuery] PaginationParameters parameters)
        {
            var result = await this.Services_Repo.TimeSheetService.GetTimeSheets(parameters);
            if (result.Items.Length > 0)
            {
                return new ApiResponse<Page<TimeSheet>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<Page<TimeSheet>>
                {
                    ResponseMessage = "TimeSheet Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Calculate TimeSheet Hours, total todos and total revenue
        /// <summary>
        /// Calculate TimeSheet Hours, total todos and total revenue, without project id, return data for all projects
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("CalculateTimeSheetHours")]
        public async Task<GenericResponse> CalculateTimeSheetHours(string? projectId)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.CalculateBillableandNonBillableHours(projectId);
                return result;
            }
            catch (Exception ex)
            {
                return new GenericResponse
                {
                    ResponseMessage = "Something went wrong with fething records",
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Calculate TimeSheet Billable and Non Billable Hours and Total Revenue using deynamic filters
        /// <summary>
        /// Calculate TimeSheet Hours, total todos and total revenue, without project id, return data for all projects
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="projectIds"></param>
        /// <param name="filters"></param>
        /// <param name="isArchived"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CalculateTimeSheetHoursWithFilters")]
        public async Task<GenericResponse> CalculateTimeSheetHoursWithFilters([FromQuery] PaginationParameters parameters, [FromQuery] List<string> projectIds, [FromBody] TimeSheetFilters filters, [FromQuery] bool isArchived = false)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.CalculateBillableandNonBillableHours(parameters, projectIds, filters, isArchived);
                return result;
            }
            catch (Exception ex)
            {
                return new GenericResponse
                {
                    ResponseMessage = "Something went wrong with fething records",
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Get single user details
        /// <summary>
        /// Get single user details
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserDetails")]
        public async Task<IActionResult> GetUserDetails(string userId, string projectId)
        {
            try
            {
                var loggedInUserId = CurrentUserId.ToString();
                var result = await this.Services_Repo.TimeSheetService.GetUserDetailsById(userId, projectId, loggedInUserId);
                return Ok(new ApiResponse<UserDetailsForTeamSheetDto>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });

            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<UserDetailsForTeamSheetDto>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "404",
                    Data = null
                });
            }
        }
        #endregion

        #region Get todo details by todoId
        /// <summary>
        /// Get todo details by todoId
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetTodoDetailsByTodoId")]
        public async Task<IActionResult> GetTodoDetails(string todoId, string projectId = null)
        {
            try
            {
                var loggedInUserId = CurrentUserId.ToString();
                if (string.IsNullOrEmpty(todoId)) { return BadRequest("TodoId is required"); }

                var result = await this.Services_Repo.TimeSheetService.GetTodoDetailsById(todoId, loggedInUserId, projectId);

                return Ok(new ApiResponse<TimeSheetTodoDto>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });

            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<TimeSheetTodoDto>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "400",
                    Data = null
                });
            }
        }
        #endregion

        #region Add comment to todo
        /// <summary>
        /// Add comment to todo
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="comment"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("AddCommentToTodo/{todoId}")]
        public async Task<IActionResult> AddCommentToTodo(string todoId, [FromQuery] string comment)
        {
            try
            {
                if (string.IsNullOrEmpty(comment))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Cannot send an empty comment",
                        Data = false
                    });
                }

                var loggedInUserId = CurrentUserId.ToString();
                var result = await this.Services_Repo.TimeSheetService.AddCommentToTodo(todoId, comment, loggedInUserId);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "An error has occured, please try again later",
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Edit comment
        /// <summary>
        /// Edit comment
        /// </summary>
        /// <param name="commentId"></param>
        /// <param name="comment"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("EditCommentToTodo{commentId}")]
        public async Task<IActionResult> EditCommentToTodo(string commentId, string comment)
        {
            if (string.IsNullOrEmpty(comment))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Cannot send an empty comment",
                    Data = false
                });
            }

            try
            {
                var loggedInUserId = CurrentUserId.ToString();
                var result = await this.Services_Repo.TimeSheetService.EditComment(commentId, comment, loggedInUserId);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "401",
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get todo comments
        /// <summary>
        /// Get todo comments
        /// </summary>
        /// <param name="todoId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetTodoComments/{todoId}")]
        public async Task<IActionResult> GetTodoComments(string todoId)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.GetCommentsForTodoAsync(todoId);
                return Ok(new ApiResponse<List<TodoCommentDto>>
                {
                    ResponseMessage = "Comments retrieved successfully",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "An error occured while getting todo comments");
                return BadRequest(new ApiResponse<List<TodoCommentDto>>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    ResponseCode = "400",
                    Data = null
                });
            }
        }
        #endregion

        #region Update Todo
        /// <summary>
        /// Update Todo for timesheet
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("UpdateTodo/{todoId}")]
        public async Task<IActionResult> UpdateTodoDetails(string todoId, [FromBody] UpdateTodoForTHDto model)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.UpdateTodo(todoId, model);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "401",
                    Data = false
                });
            }
        }
        #endregion

        #region Lock Or Unlock Todos
        /// <summary>
        /// This locks todos
        /// </summary>
        /// <param name="todoIds"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_lock_todo)]
        [HttpPut]
        [Route("LockTodos")]
        public async Task<IActionResult> LockTodos([FromBody] List<string> todoIds)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.LockOrUnlockTodos(todoIds, CurrentUserId.ToString());
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get project details by projectId
        /// <summary>
        /// Get project details by projectId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects, Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetProjectDetailsByProjectId/{projectId}")]
        public async Task<IActionResult> GetProjectDetails(string projectId)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.GetProjectDetailsById(projectId, CurrentUserId.ToString());
                return Ok(new ApiResponse<AllProjectsForTimeSheetDto>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<AllProjectsForTimeSheetDto>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = null
                });
            }
        }
        #endregion

        #region Get Dashboard Data
        /// <summary>
        /// Get Dashboard Data
        /// </summary>
        /// <param name="filters"></param>
        /// <returns></returns>
        [Authorize]
        [HttpPost]
        [Route("GetDashboardData")]
        public async Task<IActionResult> GetDashboardData(TimeSheetDashboardFilters filters)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.CalculateDashboardData(filters);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = $"An error has occured, please try again later",
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Update recoreded time for todo
        /// <summary>
        /// Update recoreded time for todo
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpPut]
        [Route("UpdateRecordedTimeForTodo/{Id}")]
        public async Task<IActionResult> UpdateRecordedTimeForTodoDetails(string Id, string time)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.UpdateRecordedTime(Id, time, CurrentUserId.ToString());
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Archive Todos
        /// <summary>
        /// Archive Todos
        /// </summary>
        /// <param name="todoIds"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpPut]
        [Route("ArchiveTodos")]
        public async Task<IActionResult> ArchiveTodos([FromBody] List<string> todoIds)
        {
            try
            {
                if (!todoIds.Any())
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "TodoIds is required",
                        ResponseCode = "400",
                        Data = false
                    });
                }

                var result = await this.Services_Repo.TimeSheetService.ArchiveTodos(todoIds, CurrentUserId.ToString());
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region UnArchive Todos
        /// <summary>
        /// UnArchive Todos
        /// </summary>
        /// <param name="todoIds"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpPut]
        [Route("UnArchiveTodos")]
        public async Task<IActionResult> UnArchiveTodos([FromBody] List<string> todoIds)
        {
            try
            {
                if (!todoIds.Any())
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "TodoIds is required",
                        ResponseCode = "400",
                        Data = false
                    });
                }
                var result = await this.Services_Repo.TimeSheetService.UnArchiveTodos(todoIds, CurrentUserId.ToString());
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get TimeSheet By Id
        /// <summary>
        /// Get TimeSheet By Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpGet]
        [Route("GetTimeSheet/timesheetId/{id}")]
        public async Task<ApiResponse<TimeSheet>> GetTimeSheetById(string id)
        {
            Guid newid = new Guid(id);
            var result = await this.Services_Repo.TimeSheetService.GetTimeSheetById(newid);
            if (result != null)
            {
                return new ApiResponse<TimeSheet>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<TimeSheet>
                {
                    ResponseMessage = "TimeSheet Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Get TimeSheet By UserId
        /// <summary>
        /// Get TimeSheet By UserId
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpGet]
        [Route("GetTimeSheet/User/{userId}")]
        public async Task<ApiResponse<Page<TimeSheet>>> GetTimeSheetByUserId(string userId, [FromQuery] PaginationParameters parameters)
        {
            var result = await this.Services_Repo.TimeSheetService.GetTimeSheetsByUserId(parameters, userId);
            if (result != null)
            {
                return new ApiResponse<Page<TimeSheet>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<Page<TimeSheet>>
                {
                    ResponseMessage = "TimeSheet Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Get TimeSheet By ProjectId
        /// <summary>
        /// Get TimeSheet By ProjectId
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpGet]
        [Route("GetTimeSheet/Report/{projectId}")]
        public async Task<ApiResponse<TimeSheetReport>> GetTimeSheetReportByProjectId(string ProjectId)
        {
            var result = await this.Services_Repo.TimeSheetService.GetTimeSheetReport(ProjectId);
            if (result != null)
            {
                return new ApiResponse<TimeSheetReport>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<TimeSheetReport>
                {
                    ResponseMessage = "TimeSheet Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Delete TimeSheet
        /// <summary>
        /// Delete TimeSheet
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpDelete]
        [Route("TimeSheet/{id}")]
        public async Task<ApiResponse<TimeSheet>> DeleteTimeSheet(string id)
        {

            if (id == null) { return new ApiResponse<TimeSheet> 
            { 
                ResponseCode = "400", ResponseMessage = "TimeSheet Id is Required", Data = null }; 
            }

            try
            {
                Guid newid = new Guid(id);
                var timeSheet = await Services_Repo.TimeSheetService.GetTimeSheetById(newid);
                if (timeSheet == null) { return new ApiResponse<TimeSheet> { ResponseCode = "404", ResponseMessage = "TImeSheet Not Found", Data = null }; }
                else
                {
                    bool result = await Services_Repo.TimeSheetService.DeleteTimeSheet(timeSheet, CurrentUserId.ToString());
                    if (result == false) { return new ApiResponse<TimeSheet> { ResponseCode = "500", ResponseMessage = "Could Not Remove Pls Try Again", Data = null }; }
                    else { return new ApiResponse<TimeSheet> { ResponseCode = "200", ResponseMessage = "Remove Succssful", Data = null }; }

                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return new ApiResponse<TimeSheet>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "401",
                    Data = null
                };
            }
        }
        #endregion

        #region Top Recent Activitues
        /// <summary>
        /// Top Recent Activitues
        /// </summary>
        /// <param name="numberOfActivities"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("TopRecentActivities")]
        public async Task<ApiResponse<List<Activity>>> GetTopRecentActivities(int? numberOfActivities)
        {
            var result = new List<Activity>();
            if (numberOfActivities != null)
                result = await this.Services_Repo.TimeSheetService.GetTopRecentActivities(numberOfActivities.Value);
            else
                result = await this.Services_Repo.TimeSheetService.GetTopRecentActivities();

            if (result != null)
            {
                return new ApiResponse<List<Activity>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<List<Activity>>
                {
                    ResponseMessage = "No Record Found",
                    ResponseCode = "200",
                    Data = null
                };
            }
        }
        #endregion

        #region Update TimeSheet Completion Date
        /// <summary>
        /// Update the Completion Date of a TimeSheet using a DTO.
        /// </summary>
        /// <param name="timeSheetCompletionTime">DTO containing the TimeSheet ID and the new Completion Date.</param>
        /// <returns>An ApiResponse indicating success or failure.</returns>
        [CustomAuthorize(Permissions.can_access_and_modify_timesheet)]
        [HttpPut]
        [Route("UpdateTimeSheet")]
        public async Task<ApiResponse<bool>> UpdateTimeSheetCompletionDate([FromBody] TimeSheetCompletionTime timeSheetCompletionTime)
        {
            try
            {
                var result = await this.Services_Repo.TimeSheetService.UpdateCompletionDate(timeSheetCompletionTime.TimeSheetId, timeSheetCompletionTime.CompletionDate);

                if (result)
                {
                    return new ApiResponse<bool>
                    {
                        ResponseMessage = "Completion Date Updated Successfully",
                        ResponseCode = "200",
                        Data = true
                    };
                }
                else
                {
                    return new ApiResponse<bool>
                    {
                        ResponseMessage = "Update Failed",
                        ResponseCode = "400",
                        Data = false
                    };
                }
            }
            catch (RecordNotFoundException ex)
            {
                return new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "404",
                    Data = false
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<bool>
                {
                    ResponseMessage = "An error occurred while updating the completion date.",
                    ResponseCode = "500",
                    Data = false
                };
            }
        }
        #endregion

    }
}
