﻿using Jobid.App.Calender.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models
{
    public class Localization : BaseModel
    {
        [Key]
        public Guid Id { get; set; }
        public string TimeZone { get; set; }
        public string LanguagePreference { get; set; }
        public string CalenderWeekStartsOn { get; set; }
        public string DateFormat { get; set; }
        public string TimeFormat { get; set; }
        public string Currency { get; set; }
        public string AmountFormat { get; set; }
        public string UserId { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }

        public Localization()
        {
            Id = Guid.NewGuid();
        }
    }
}
