﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class AcceptOrRejectNewProposedDateAndTimeDto
    {
        public DateTime NewProposedStartDateAndTime { get; set; }
        public DateTime NewProposedEndDateAndTime { get; set; }

        [Required(ErrorMessage = "Meeting Id is required")]
        public string MeetingId { get; set; }
        public string RequesterEmail { get; set; }
        public string Reason { get; set; }
        public bool Accepted { get; set; }

        [JsonIgnore]
      
        public string Subdomain { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }

        public string Token { get; set; }
    }
}
