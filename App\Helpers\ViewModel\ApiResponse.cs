﻿using Jobid.App.Helpers.Models;
using Twilio.TwiML;

namespace Jobid.App.Helpers
{
    public class ApiResponse
    {
        public string ResponseCode { get; set; }
        public string? ErrorCode { get; set; }
        public string ResponseMessage { get; set; }
        public string DevResponseMessage { get; set; }
        public string StackTrace { get; set; }
    }

    public class ApiResponse<T> : ApiResponse
    {
        public User user { get; set; }
        public T Data { get; set; }
    }

    public class TwiMLResponse
    {
        public string ResponseCode { get; set; }
        public MessagingResponse MessagingResponse { get; set; }
    }

    public class GenericResponse
    {
        public string ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public string DevResponseMessage { get; set; }
        public object? Data { get; set; }
    }

    public class ExcelApiResponse
    {
        public string Extension { get; set; }
        public string Title { get; set; }
        public string Base64String { get; set; }

    }
}
