using Jobid.App.Helpers;
using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;

namespace Jobid.App.AdminConsole.Contract
{
    /// <summary>
    /// Contract for Campaign Service operations
    /// </summary>
    public interface ICampaignService
    {
        /// <summary>
        /// Create a new campaign
        /// </summary>
        Task<ApiResponse<CampaignDto>> CreateCampaignAsync(CreateCampaignDto campaignDto);

        /// <summary>
        /// Update an existing campaign
        /// </summary>
        Task<ApiResponse<CampaignDto>> UpdateCampaignAsync(UpdateCampaignDto campaignDto);

        /// <summary>
        /// Delete a campaign
        /// </summary>
        Task<ApiResponse<bool>> DeleteCampaignAsync(Guid campaignId, string userId);

        /// <summary>
        /// Get campaign by ID
        /// </summary>
        Task<ApiResponse<CampaignDto>> GetCampaignByIdAsync(Guid campaignId);        /// <summary>
        /// Get campaigns for a specific user with pagination
        /// </summary>
        Task<ApiResponse<PaginatedCampaignsDto>> GetUserCampaignsAsync(string userId, int pageNumber, int pageSize, CampaignStatus? status = null);

        /// <summary>
        /// Get all campaigns with pagination
        /// </summary>
        Task<ApiResponse<PaginatedCampaignsDto>> GetAllCampaignsAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Update campaign status
        /// </summary>
        Task<ApiResponse<CampaignDto>> UpdateCampaignStatusAsync(UpdateCampaignStatusDto statusDto);

        /// <summary>
        /// Get campaign statistics for a user
        /// </summary>
        Task<ApiResponse<CampaignStatsDto>> GetCampaignStatsAsync(string userId);
    }
}
