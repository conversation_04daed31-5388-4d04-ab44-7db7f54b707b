﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Jobid.App.Helpers.Enums;
using Jobid.App.JobProjectManagement.Models;
using static Jobid.App.JobProject.Enums.Enums;
using static Jobid.App.Helpers.Utils.Extensions;

namespace Jobid.App.Helpers.Models
{
    public class ProjectMgmt_Project 
    {
        [Key] 
        public Guid ProjectId { get; set; }

        [Required]
        public string Name { get; set; }
        public int Hours { get; set; }
        public Guid? CompanyId { get; set; }
        public string Summary { get; set; }
        public string Duration { get; set; }
        public string Clients { get; set; }
        public string  CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? CreatedTime { get; set; } = GetAdjustedDateTimeBasedOnTZNow();
        public DateTime? LastUpdate { get; set; }
        public ICollection<ProjectMgmt_Todo> Todos { get; set; }
        public string Description { get; set; }
        public ICollection<ProjectFile> ProjectFiles { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsBillable { get; set; }
        public string CurrencySymbol { get; set; }
        public decimal? ExpectedProjectValue { get; set; }
        public decimal? AmountPerSelectedFrequency { get; set; }
        public AmountFrequency? AmountFrequency { get; set; }
        public ICollection<ProjectMgmt_ProjectUser> projectMgmt_ProjectUsers { get; set; }
        public ICollection<ProjectTag> ProjectTags { get; set; }
        public string tenantId { get; set; }
        public ICollection<TimeSheet> TimeSheet { get; set; }
        public string SprintProjectId { get; set; }
        public ProjectStatus ProjectStatus { get; set; }
        public ICollection<ProjectTrigger> ProjectTriggers { get; set; }

        [NotMapped]
        public string PercentageCompleted { get; set; }
    }
}
