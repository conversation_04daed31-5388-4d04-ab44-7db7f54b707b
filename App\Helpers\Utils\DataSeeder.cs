﻿using Jobid.App.AdminConsole.Models;
using Jobid.App.AdminConsole.Models.Phone;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.Subscription.Enums;
using Jobid.App.Subscription.Models;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using static Jobid.App.Helpers.Utils.Utility;
using static Jobid.App.Subscription.Enums.BasicAccessFeatures;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Helpers.Utils
{
    public class DataSeeder
    {
        #region Guids for subscrition and AI
        private static readonly List<Guid> newListOfGuidsForPricingPlans = new List<Guid>()
        {
            Guid.Parse("554e5db6-4f8a-4ee1-90d0-5ee0346f4696"),
            Guid.Parse("7b9cd545-a3cc-4ca3-9107-0e622bc6ee64"),
            Guid.Parse("0b53e534-c7d9-408d-a2e8-77fb500e3088"),
            Guid.Parse("192d0e6e-330d-4e27-a43e-d1bffe00f594"),
        };

        private static readonly List<Guid> listOfGuidsForAIPricingPlans = new List<Guid>()
        {
            Guid.Parse("39562c50-f995-405e-aa77-0f06258bd38f"),
            Guid.Parse("edc1927b-5158-4645-b3bd-72af85356025"),
            Guid.Parse("025162c7-2fdc-4d1a-8e6a-c3838fcc1a4a"),
            Guid.Parse("e9a0bd2a-24a5-4e83-8b66-805439ee6bc3"),
            Guid.Parse("eea8bcc1-22a8-45c8-8a12-9c913661a03b"),
            Guid.Parse("bc641cfa-1bab-4fed-98c0-57c96207aec6"),
        };

        private static readonly List<Guid> newListOfGuidsForProjectFeatures = new List<Guid>()
        {
            Guid.Parse("951ac934-c6a1-45ad-bfc9-7a05fb11b56b"),
            Guid.Parse("07b6d236-61b3-430e-8b60-6678b66153a6"),
            Guid.Parse("18e7708a-7f98-4dd8-94bf-2e7503251dcd")
        };

        private static readonly List<Guid> newListOfGuidsForPricingFeatures = new List<Guid>()
        {
            Guid.Parse("877d1fd6-a7e3-44d1-abcf-5b4d208e06ae"),
            Guid.Parse("e8b7ee94-eb9d-46a2-8ee2-9e6ad4680b78"),
            Guid.Parse("667d9975-2fc4-4749-be67-30e85e547140"),
            Guid.Parse("9d09bd94-813b-4c87-ab52-fdbc5c7ac0a1")
        };

        private static readonly List<Guid> newListOfGuidForPackagePricing = new List<Guid>
        {
            Guid.Parse("26a189cd-2b76-4f44-a85f-030938972fe8"),
            Guid.Parse("a3c7662a-7650-4ae0-81b0-02a18b21de46"),
            Guid.Parse("db0b358e-59f3-41c7-8b00-4952cb8830bb"),
            Guid.Parse("14e4ab0a-75cf-427f-9fd6-c7aba96aef23"),
            Guid.Parse("077da85e-b03f-4096-b91e-2dfc66f0c6e4")
        };
        #endregion

        #region Seed Guid Ids - Joble
        private static readonly List<Guid> listOfGuidsForPricingPlans = new List<Guid>()
        {
            Guid.Parse("6efda347-594f-4509-ae9c-55ae6b7abc52"),
            Guid.Parse("06673769-845e-42e7-9c55-2ecd7571849d"),
            Guid.Parse("e641b3df-405f-4ada-be63-8a479c68580b"),
            Guid.Parse("13891f24-1ab6-4706-8e7d-1bebfa06815a")
        };

        private static readonly List<Guid> listOfGuidsForProjectFeatures = new List<Guid>()
        {
            Guid.Parse("d7f91029-307b-4c2b-9393-812b56654c7c"),
            Guid.Parse("b14ba297-280d-41a1-9d01-f9d4b7b516b8"),
            Guid.Parse("a9f4fb9e-3310-4f54-9095-839e9358fd1a"),
            Guid.Parse("6becbf3d-2a7c-43ab-bc65-cb270e75316e"),
            Guid.Parse("06b7e15f-35b8-453e-9ebd-43b02c905e4c"),
            Guid.Parse("1f37a4bf-b95f-49b9-a835-efff92edfd0b"),
            Guid.Parse("de79569e-184d-4365-b20a-c1081f7fe219"),
            Guid.Parse("811bbee0-31cc-47ec-b505-14607dc27fd8"),
            Guid.Parse("013d8453-3b95-492b-a775-02e370b19baf")
        };

        private static readonly List<Guid> listOfGuidsForPricingFeatures = new List<Guid>()
        {
            Guid.Parse("8b6ec1db-0fc6-43f5-aa35-de0cb8e217f8"),
            Guid.Parse("a17551b2-862e-4f64-9f7d-57a7eb7f53b7"),
            Guid.Parse("73d97a8e-cfb3-4c6e-8e79-9bca3b019dd4"),
            Guid.Parse("be0261d6-bb8b-4cae-bb35-fe027bc01886"),
            Guid.Parse("d04a7abf-3ea0-4e40-a324-6ebf0705a01f"),
            Guid.Parse("3fd6030e-b0a9-4b5c-98ca-47c5556c150b"),
            Guid.Parse("aa3cc07f-84b4-455f-b0a0-8a7c7e9af773"),
            Guid.Parse("e641b3df-405f-4ada-be63-8a479c68580b"),
            Guid.Parse("13891f24-1ab6-4706-8e7d-1bebfa06815a"),
            Guid.Parse("06673769-845e-42e7-9c55-2ecd7571849d"),
            Guid.Parse("ad64d6b9-4991-4acb-8c97-60b36e58059d"),
            Guid.Parse("b3151b6c-90b1-4d4f-8aba-cc1ab2fb89d8"),
            Guid.Parse("b14ba297-280d-41a1-9d01-f9d4b7b516b8"),
            Guid.Parse("a9f4fb9e-3310-4f54-9095-839e9358fd1a"),
            Guid.Parse("6becbf3d-2a7c-43ab-bc65-cb270e75316e"),
            Guid.Parse("06b7e15f-35b8-453e-9ebd-43b02c905e4c"),
            Guid.Parse("1f37a4bf-b95f-49b9-a835-efff92edfd0b"),
            Guid.Parse("de79569e-184d-4365-b20a-c1081f7fe219"),
            Guid.Parse("4f6692d2-2e52-478b-9a05-3e1446457d81"),
            Guid.Parse("b2a1b3b1-1b3f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("c1b3b2a1-1b3f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("b3b2c1a1-3b1f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("248d7f1f-52a9-4caf-be14-b81ee5586b57"),
            Guid.Parse("b1b3b2a1-1b3f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("91c7de84-6178-440c-a1fc-c2c22527d268"),
            Guid.Parse("b12f33fd-d324-4072-8f63-73e848733d95"),
            Guid.Parse("39562c50-f995-405e-aa77-0f06258bd38f"),
            Guid.Parse("edc1927b-5158-4645-b3bd-72af85356025"),
            Guid.Parse("025162c7-2fdc-4d1a-8e6a-c3838fcc1a4a"),
            Guid.Parse("e9a0bd2a-24a5-4e83-8b66-805439ee6bc3"),
            Guid.Parse("eea8bcc1-22a8-45c8-8a12-9c913661a03b"),
            Guid.Parse("bc641cfa-1bab-4fed-98c0-57c96207aec6"),
            Guid.Parse("4ac62bbb-1543-4659-914d-489e7377ac83"),
            Guid.Parse("67517372-9a73-493a-b4fc-d2db6cf624e6"),
            Guid.Parse("73a81b8c-6334-4ae0-85e3-d375ad49e00a"),
            Guid.Parse("2783a311-4d5d-44a2-9bc4-f9fab895bb2e"),
        };

        private static readonly List<Guid> listOfGuidsForSmsCampaignEvents = new List<Guid>()
        {
            Guid.Parse("8b6ec1db-0fc6-43f5-aa35-de0cb8e217f8"),
            Guid.Parse("a17551b2-862e-4f64-9f7d-57a7eb7f53b7"),
            Guid.Parse("73d97a8e-cfb3-4c6e-8e79-9bca3b019dd4"),
            Guid.Parse("be0261d6-bb8b-4cae-bb35-fe027bc01886"),
            Guid.Parse("d04a7abf-3ea0-4e40-a324-6ebf0705a01f"),
            Guid.Parse("3fd6030e-b0a9-4b5c-98ca-47c5556c150b"),
            Guid.Parse("aa3cc07f-84b4-455f-b0a0-8a7c7e9af773"),
            Guid.Parse("e641b3df-405f-4ada-be63-8a479c68580b"),
            Guid.Parse("13891f24-1ab6-4706-8e7d-1bebfa06815a"),
            Guid.Parse("06673769-845e-42e7-9c55-2ecd7571849d"),
            Guid.Parse("6efda347-594f-4509-ae9c-55ae6b7abc52"),
            Guid.Parse("b3151b6c-90b1-4d4f-8aba-cc1ab2fb89d8"),
            Guid.Parse("b14ba297-280d-41a1-9d01-f9d4b7b516b8"),
            Guid.Parse("4ac62bbb-1543-4659-914d-489e7377ac83"),
            Guid.Parse("67517372-9a73-493a-b4fc-d2db6cf624e6"),
            Guid.Parse("73a81b8c-6334-4ae0-85e3-d375ad49e00a"),
            Guid.Parse("2783a311-4d5d-44a2-9bc4-f9fab895bb2e"),
            Guid.Parse("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
            Guid.Parse("2a1c1e5f-8b74-4a96-a1cd-8c5b2d1f6747"),

        };

        private static readonly List<Guid> listOfGuidsForWhatsappCampaignEvents = new List<Guid>()
        {
            Guid.Parse("a9f4fb9e-3310-4f54-9095-839e9358fd1a"),
            Guid.Parse("6becbf3d-2a7c-43ab-bc65-cb270e75316e"),
            Guid.Parse("06b7e15f-35b8-453e-9ebd-43b02c905e4c"),
            Guid.Parse("1f37a4bf-b95f-49b9-a835-efff92edfd0b"),
            Guid.Parse("de79569e-184d-4365-b20a-c1081f7fe219"),
            Guid.Parse("4f6692d2-2e52-478b-9a05-3e1446457d81"),
            Guid.Parse("b2a1b3b1-1b3f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("c1b3b2a1-1b3f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("b3b2c1a1-3b1f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("248d7f1f-52a9-4caf-be14-b81ee5586b57"),
            Guid.Parse("b1b3b2a1-1b3f-4b9a-9c1a-1b3b1f4b9a9c"),
            Guid.Parse("91c7de84-6178-440c-a1fc-c2c22527d268"),
            Guid.Parse("b12f33fd-d324-4072-8f63-73e848733d95"),
            Guid.Parse("39562c50-f995-405e-aa77-0f06258bd38f"),
            Guid.Parse("edc1927b-5158-4645-b3bd-72af85356025"),
            Guid.Parse("025162c7-2fdc-4d1a-8e6a-c3838fcc1a4a"),
            Guid.Parse("e9a0bd2a-24a5-4e83-8b66-805439ee6bc3"),
            Guid.Parse("eea8bcc1-22a8-45c8-8a12-9c913661a03b"),
            Guid.Parse("bc641cfa-1bab-4fed-98c0-57c96207aec6"),
        };

        private static readonly List<Guid> listOfGuidsForEmalCampaignEvents = new List<Guid>()
        { Guid.Parse("b33e7b16-4c3c-4d69-b5d5-39bcdf5d4e8b"),
            Guid.Parse("a95dc894-167d-411f-84a1-771da6d5a3f9"),
            Guid.Parse("d18b07f8-8c41-4678-bf99-3e89b6d50177"),
            Guid.Parse("97a0fdd4-034b-4d29-8e1a-6855e992d9a6"),
            Guid.Parse("c3d4e6a2-4e57-401c-949f-2301fc3c56d4"),
            Guid.Parse("83fb2e23-0e34-4891-896b-30bfa04c5b2e"),
            Guid.Parse("5b78c8d0-5efc-4e90-a9cb-62b7a6b4b027"),
            Guid.Parse("23e4f3bc-72e5-4ed9-9a7e-1f49d35f5fdd"),
            Guid.Parse("71f2a9b8-bc9e-4bf1-9c67-d2232d6b4b38"),
            Guid.Parse("892cc6b9-2efb-4b69-8881-0fd60f8884bb"),
            Guid.Parse("9e2303fa-9e35-4ba7-b0c5-7d78cfcce605"),
            Guid.Parse("e4b4a3b2-848b-472a-b8b7-2fc83454d1a1"),
            Guid.Parse("a8b7ec5a-4f34-46a5-bb95-87c72d23db54"),
            Guid.Parse("1b2a9bfb-8c37-4a1b-8f3b-fbc3c3e55c3b"),
            Guid.Parse("5cf39c79-36e2-4d6f-a80c-e5dcb5046f37"),
            Guid.Parse("fbfe6d8c-9b44-40c5-9f4b-2c8d0f576c63"),
            Guid.Parse("6d972cfc-3b25-4e78-87d2-93d9f7c0894d"),
            Guid.Parse("c3e2b2f4-1f6e-4f7e-ae73-38f9c8b9d2a9"),

        };

        private static readonly List<Guid> listOfGuidForPackagePricing = new List<Guid>
        {
            Guid.Parse("72700ad3-8ac5-42d5-9e04-ad02f059ced5"),
            Guid.Parse("f2b2b2b2-2b2b-2b2b-2b2b-2b2b2b2b2b2b"),
            Guid.Parse("468f94d3-41bb-4472-8900-e50fdd347091"),
            Guid.Parse("fddc7e33-011c-48be-a937-300a4f1cb793"),
        };
        #endregion

        #region Seed Guid Ids - Echo
        private static readonly List<Guid> listOfGuidsForPricingPlansEcho = new List<Guid>()
        {
            Guid.Parse("fe4628f1-5e38-4805-aad4-e8dd94c21a57"),
            Guid.Parse("274abb36-f0b7-4e22-8cd6-f06eae2d86ac"),
            Guid.Parse("91ce3898-ce55-42e3-a979-9e9b6b08d225"),
           // Guid.Parse("5e449eeb-a7c4-4918-9160-f78fbd1a694d"),
        };

        private static readonly List<Guid> listOfGuidsForProjectFeaturesEcho = new List<Guid>()
        {
            Guid.Parse("84d6dce6-d9ea-4253-ae44-ed8d70db32aa"),
            Guid.Parse("5b4911f3-e88f-4017-bc38-402951402d3a"),
            Guid.Parse("88c018e7-9216-4943-a3e1-7cbeadaadff2"),
            Guid.Parse("36c26541-d8f6-4824-bae6-03cdf271cfb9"),
            Guid.Parse("ad10b28f-8ee9-4a70-97ce-7d6a3d89a6f7"),
            Guid.Parse("7ea87af7-6d2b-4bcf-a68f-d6eec31489c4"),
            Guid.Parse("40cf662e-9e9d-4b0d-b030-9d5c8d32ac82"),
            Guid.Parse("7def8340-0fc5-40f0-88a3-1bd51536a7e1"),
            Guid.Parse("30f71c26-8306-4300-8c22-fc7356c23a83"),
            Guid.Parse("18c2ab97-fdf6-4041-9937-3713d25f480b"),
            Guid.Parse("68fb66d7-e4f5-4e63-bee2-e6e4958e51be"),
            Guid.Parse("ad5d4735-36e5-4c07-b206-8327317273bd"),
            Guid.Parse("f431dc22-665e-45ea-bdba-24bb04816485"),
        };

        private static readonly List<Guid> listOfGuidsForPricingFeaturesEcho = new List<Guid>()
        {
            Guid.Parse("5ec0ee4e-2127-4fe9-8d59-6a93853cf542"),
            Guid.Parse("54138ad5-9a91-450e-9575-c4d9c5e4e26a"),
            Guid.Parse("aeed4a53-4dc6-4ff5-8b76-4be4e91f91d4"),
            Guid.Parse("bc7cf85c-4df6-4273-986f-91a0a6920269"),
            Guid.Parse("954d877b-d4fe-4fbf-9029-0775910910fe"),
            Guid.Parse("4ab4fe37-a826-4503-9ba3-314274245ba7"),
            Guid.Parse("ac6df91b-6908-41bb-a23d-507d9c1e162a"),
            Guid.Parse("b28c7c81-259b-4ded-842f-9adf48838d16"),
            Guid.Parse("cad01e7a-d7e0-456f-86b3-8c0e0d92ad5c"),
            Guid.Parse("9ff06899-2c09-4718-a650-1ab1e36a6411"),
            Guid.Parse("7fea8c04-ae2a-42b8-bf58-d55b2b5e6bed"),
            Guid.Parse("d44718c0-144e-4052-8943-e1996448bb90"),
            Guid.Parse("6d509be8-499e-4a3e-8784-0670f12f6500"),
            Guid.Parse("a87543b6-daba-44cf-b97a-46e2e55b865a"),
            Guid.Parse("73067c41-827e-491b-be53-825f6ceb61e2"),
            Guid.Parse("c6643f09-a479-4c48-b4cb-6abaa962853e"),
            Guid.Parse("3052f068-0db3-473d-8d8f-d173178b42fa"),
            Guid.Parse("d93fa190-0b8e-43cd-bc5c-4b70b9ffcd49"),
            Guid.Parse("3bd9a7f9-5b3b-4772-a540-3bd4ec21c855"),
            Guid.Parse("ccfdc51d-ff15-409f-8c18-8dba10b3622e"),
            Guid.Parse("18cfe4b2-ae14-4fe0-9d0b-4defe6e88c93"),
            Guid.Parse("ceee2e2b-40aa-4970-9777-c401fb06a67b"),
            Guid.Parse("b61c834d-798d-49d0-8b29-af7d2649b7d3"),
            Guid.Parse("2832cd13-ccb9-4d96-a69e-c23c2519efb1"),
            Guid.Parse("fac0f519-9b2d-4ceb-8cc2-7fc54c6ffe40"),
            Guid.Parse("78a5afad-3a18-4c58-b9e1-d18b229fe2a1"),
            Guid.Parse("7189b475-15e1-4f46-b43e-6336a352cb18"),
            Guid.Parse("1e8f8d64-8743-4e4f-9c15-967c64bf8e44"),
            Guid.Parse("61a0ac30-c5af-4986-8e56-6f5df4847f48"),
            Guid.Parse("48004470-6e05-45e3-bc05-9341869c080d"),
            Guid.Parse("e4b54a72-f815-47f4-a42e-b4f70ae97be1"),
            Guid.Parse("01f35b17-328f-473c-adc2-1b49c6c17af4"),
            Guid.Parse("1b8af0a2-4d5a-420a-a6d7-de58b3963709"),
            Guid.Parse("24e29608-7edd-43c7-afd1-acf9baf5efe4"),
            Guid.Parse("1f389c6d-2ad3-48fb-9624-e6d6c76d4d0d"),
            Guid.Parse("0e08aa29-a4d6-48fa-9993-8acc1ee42470"),
            Guid.Parse("57d09bb5-d3ae-4033-9d07-f52fac911f68"),
            Guid.Parse("075c371c-7225-4edd-b1ab-36bf5de4ffdb"),
            Guid.Parse("2e5dac3f-32e8-48f0-9117-04f3de4d08a5")
        };

        private static readonly List<Guid> listOfGuidForPackagePricingEcho = new List<Guid>
        {
            Guid.Parse("dec790be-224d-47d3-b127-4a5417de2923"),
            Guid.Parse("b85598fc-6e62-405a-bf9b-54bfbbb2c6d7"),
            Guid.Parse("7d84635d-3e45-4c86-a960-35e80e36da72"),
            //Guid.Parse("255cc16b-46dd-4afe-8d19-2fc68ff13019"),
        };
        #endregion

        #region Seed Guid Ids - JobID
        private static readonly List<Guid> listOfGuidsForPricingPlansJobID = new List<Guid>()
        {
            Guid.Parse("e1b1c9a7-4391-4ae6-9efc-5bfe8147c2f9"),
            Guid.Parse("3d7bbcf4-5f7c-4a28-bd21-e2c5dd64c1b5"),
            Guid.Parse("b07260da-5a98-4e17-9886-f88f1f5c58b3"),
            Guid.Parse("9e709f26-5834-4e9b-bd1b-8d3e4b8a6a4d"),
            Guid.Parse("f3d7bbcf-5f7c-4a28-bd21-e2c5dd64c1b5"),
        };

        private static readonly List<Guid> listOfGuidsForPackageFeaturesJobID = new List<Guid>()
        {
            Guid.Parse("a9f3e6c9-8e1f-4a9f-9a1c-3f4a7f5e4a8f"),
            Guid.Parse("b2e7c4f9-5d4a-4e6f-a7c2-1b8e4a9f3e6d"),
            Guid.Parse("d1f4a9e8-9c7a-4e1f-6a3f-2d7e9b1c5f4a"),
            Guid.Parse("e4c8b2f3-7a1d-4f9a-5e6c-8b9a2f3d7e1f")
        };

        private static readonly List<Guid> listOfGuidsForPricingFeaturesJobID = new List<Guid>()
        {
            Guid.Parse("92cdd813-4828-4106-bfab-1e7505b73873"),
            Guid.Parse("7aced5d1-b262-417b-a414-1cc7bee288a3"),
            Guid.Parse("f0610829-5e74-4089-b933-d10adcb1662c"),
            Guid.Parse("6013f98f-0222-42c4-a1a4-88694613bab9"),
            Guid.Parse("73fdef2f-024e-4d87-9a9b-0f362175f106"),
            Guid.Parse("fb5363d9-d3dc-4c0f-9cc7-11bc755557b4"),
            Guid.Parse("7bbcbe95-11aa-4de9-af30-d908c3a16aa3"),
            Guid.Parse("15697133-a9a0-42ca-a5ae-feab742ed396"),
            Guid.Parse("2e0e0f44-040a-4450-b75d-1c863161120d"),
            Guid.Parse("4e7f68b3-4ca1-468e-987a-672920af7241"),
            Guid.Parse("145a01b7-1430-4324-9740-15228a49798b"),
            Guid.Parse("ac10fe2e-e3b0-4b6f-8d21-ec2da2bb8bb6"),
            Guid.Parse("f3de0aa1-ff00-4213-8f1c-3c61534e4812"),
            Guid.Parse("2779407d-fa4f-481f-b4a6-473d64b9abbe"),
            Guid.Parse("54eeec3e-e754-4b35-aff7-1ebb1fe9b8a5"),
            Guid.Parse("50cb34ab-3e8a-4caa-ac31-028427e5401b"),
            Guid.Parse("c62c0a28-a101-4072-b99a-71194e4c9bef"),
            Guid.Parse("282a0571-6078-4989-bd34-41498deb6f0a"),
            Guid.Parse("c65d9a4e-5a7e-48e8-9c54-da0e20e579ef"),
            Guid.Parse("4b623402-6afe-4bd2-83f3-f243eb72d244")
        };

        private static readonly List<Guid> listOfGuidForPackagePricingJobID = new List<Guid>
        {
            Guid.Parse("6e8fd7ed-b268-43f2-8a7a-a122184ab406"),
            Guid.Parse("478b5ead-197e-4d9e-9557-2c0f94d4fe86"),
            Guid.Parse("33085984-61be-45e1-8567-dec2b9fa8290"),
            Guid.Parse("3b899d3b-75f7-4b17-8a6a-a894b753370c"),
            Guid.Parse("46188459-4ad3-4b8f-a63e-af3feb839fa9")
        };
        #endregion

        #region Seed Guid Ids - JobFy
        private static readonly List<Guid> listOfGuidsForPricingPlansJobFy = new List<Guid>()
        {
            Guid.Parse("83f1eabc-f8a6-42c8-9634-4c70e15298c3"),
            Guid.Parse("f9c7dcb1-a392-4e95-917b-05cbf07fa10d"),
            Guid.Parse("b6c7f7e3-f012-4c8d-bf2c-d73082f142c1"),
            Guid.Parse("d4b53c09-4721-4e1e-9ec6-92b158b3d9c9")
        };

        private static readonly List<Guid> listOfGuidsForPackageFeaturesJobFy = new List<Guid>()
        {
            Guid.Parse("7f6b1cda-2d84-4651-a2b5-9e54379bb3d4"),
            Guid.Parse("aa56f7e8-fd3c-4fc3-9a25-67b1d6f71c5e"),
            Guid.Parse("2398a1c4-b1a2-4e6d-8c5a-9038f7a71e9b"),
            Guid.Parse("9c2e7564-f2c4-45b4-bac9-5e8b12f36597"),
            Guid.Parse("38c27d41-f367-44de-802d-819cf5e587e6"),
            Guid.Parse("f3cae3b7-b3cd-46e8-9c87-fb2a3a5eae41"),
            Guid.Parse("2ba5e5b0-6d4f-4f58-b2b4-5d285849f2fc"),
            Guid.Parse("f581b1ad-3a51-48ed-8ff1-431759cd8d4f"),
            Guid.Parse("d58bf2c7-428d-4d85-b914-b5187db63a45"),
            Guid.Parse("b8ec6d35-4ab8-4b3b-8f42-c3a285e23dc6"),
            Guid.Parse("2c4561b7-0146-4090-a30a-8d5b6e23735b"),
            Guid.Parse("8f4d35e6-4f32-4bfa-853b-cd93be61c865"),
            Guid.Parse("3a68e4bc-5426-4f98-bcd5-cf638b42193c"),
            Guid.Parse("5e78241c-8d63-467d-9810-f8cb964d8be7"),
            Guid.Parse("bd61fe69-d82c-49d1-8f11-e66a511e4e3c"),
            Guid.Parse("e4b13c72-b77d-4d86-bb8a-48e9db30fdf8"),
            Guid.Parse("71b7a9e3-5bc2-4642-953e-6a53c87a0d60"),
            Guid.Parse("9f38a157-ef57-4a9d-9f5b-7e9a60d01918"),
            Guid.Parse("b4da9e89-62fe-421a-8ba5-76dcbf2e9b9f"),
            Guid.Parse("df6c7a23-e7a9-4e1a-84f7-1ab9c7562bcb"),
            Guid.Parse("b1e7f3c9-4b1a-4e9c-8d2f-7a3b1e9c7f3c"),
        };

        private static readonly List<Guid> listOfGuidsForPricingFeaturesJobFy = new List<Guid>()
        {
            Guid.Parse("fc52b12b-e1f3-4c2f-b9e8-a5d7b33f9d17"),
            Guid.Parse("b81d6e3a-73c9-4b8f-88ae-2e4fc6d3f9d1"),
            Guid.Parse("a9e5f2d3-7c5a-4f2e-9d3b-c7b5a2f4e9c8"),
            Guid.Parse("d5b2c4e7-4f9a-43a5-b6d9-f7e1b4c9e3a7"),
            Guid.Parse("e7f3b5d2-1a9d-48e4-9b6c-5e1f7c3a9d2b"),
            Guid.Parse("f9b4c2a7-3e1d-48a7-9f5c-b7d9c2f3e1a5"),
            Guid.Parse("a3c9d4f8-7b2e-42f5-8a1c-e2f5b3d7c9a5"),
            Guid.Parse("c2a7e1d9-5f9b-4e1c-83d7-b2e5f9a4d3b1"),
            Guid.Parse("d7e9a5b3-1f4c-42b9-9c5d-e1a3b4f7c8d2"),
            Guid.Parse("e3f9b2a1-7d6c-4f2a-85d9-a7e1b4c5d9f3"),
            Guid.Parse("b9f2a3c4-5e7d-48f5-9a6b-2d7f1a3b4e9c"),
            Guid.Parse("c7d4b2a5-3e9f-4a1b-85c2-f5e1d9a7b4c8"),
            Guid.Parse("a1e9f7b4-9d5a-43f6-b7d9-4c3b2e1a9f5c"),
            Guid.Parse("f5c2b4d9-7a6e-41f9-b2c3-e3d9a7b5f1c4"),
            Guid.Parse("e9a4b7d1-6f3c-4e8f-b2a1-3c5f1e9d7b4a"),
            Guid.Parse("d2b7c4f9-8a1f-41e9-9c3a-5e7d2a9f6b3c"),
            Guid.Parse("a5f1b9d7-3c9e-48b4-82f5-c7a9d4e2f1b3"),
            Guid.Parse("b3e9a5d7-4f8c-42a5-9b2d-7e1f3c6a9d5b"),
            Guid.Parse("c9a3d7b5-6f1e-48c2-9e5b-f1a4d9b7c3e2"),
            Guid.Parse("d5b1c9e3-7f6a-4e9d-8b2f-3a7c5e9f1b4a"),
            Guid.Parse("f1b3c7d2-8a9e-41f5-b2d7-5e3a9c7f4b2d"),
            Guid.Parse("a4e9d7b1-3c8f-42e5-b6a9-f1d7b3e2c9f5"),
            Guid.Parse("c5a1b9d3-7e6f-48d4-8a2b-9e3f5d7b1c4a"),
            Guid.Parse("d2b4c1e9-9a3f-4b7d-8e6a-5c9b2f1e3a7d"),
            Guid.Parse("e9f1b3a5-6c7e-48d2-9f4a-7b3d9c5a2f8e"),
            Guid.Parse("b7d2c3a9-5e1f-4f9b-a2d7-c6a5f1b9e3d4"),
            Guid.Parse("c9f5a3b1-7a6e-42b9-8f3d-d7e1c2b5f9a4"),
            Guid.Parse("a1b9e5d7-4c8f-4d3a-b7f9-5c6e2f1a9d3b"),
            Guid.Parse("f3d9a7b1-8e4f-42c5-9a2b-c7f5b2e1d3a9"),
            Guid.Parse("d7b4a5f9-3e9c-48f1-9c5b-1f3a7e6b2d9e"),
            Guid.Parse("a3f9d1e7-5c2b-48e4-9b7a-6d5f1c2a9b3e"),
            Guid.Parse("e5b7d3f1-6a4e-49b8-9f2d-b3c1e9a7d4f5"),
            Guid.Parse("b9d1a5f7-7c6e-4b9f-82a1-3d2f9c5e1b7d"),
            Guid.Parse("d2e9b1a7-4a6f-4c8b-9e5f-1b7c3d5a9f2e"),
            Guid.Parse("f7a5b3c9-3e9d-48e1-9b6f-d2c5f1e7a4b9"),
            Guid.Parse("c4b7f9d1-5f3e-41a8-b2d9-9a6e3f1c7b5a"),
            Guid.Parse("a9d2f1b3-6e4c-49b7-9f8a-5c3d7e1b2f9a"),
            Guid.Parse("b3e7a9f1-7f6d-4c2a-82f9-5d9e1c3a7b4f"),
            Guid.Parse("d5a7c9b3-8e6f-4a9f-b2c7-3f1b5e9d4a7c"),
            Guid.Parse("f9b5a3d7-3c9e-4e2b-91f7-a5c7d9f2e1b4"),
            Guid.Parse("a1f7e3d9-4c6b-4e5f-9d2a-b7c9a5f3e8d1"),
            Guid.Parse("d3a9f5b1-7e2f-4b8a-9c1d-6f5e7b3a2c9f"),
            Guid.Parse("c5e7b9a4-2d9f-49a8-b3e7-a1d6f4c2b8e3"),
            Guid.Parse("b4f2c9d1-8e7f-41d3-b9a2-6a5e3f7b9c2d"),
            Guid.Parse("f3b9d1a7-6c4e-43b8-9f2d-b7c5e1a4d9e8"),
            Guid.Parse("e1a3c7f9-7d2b-48a4-9f5d-c3e9b2a6d7f1"),
            Guid.Parse("d7b3a1e9-5f4c-49e8-9a3b-f1b5c2d8e7a4"),
            Guid.Parse("c9d5b1f3-6a7f-41e9-82a4-5f7c3e9b1d2f"),
            Guid.Parse("a7e9f2b1-9c3d-48b5-8e6a-3f1d2c5b7e9a"),
            Guid.Parse("b1c7a5e3-4f9b-4d6f-b2a1-7e9d3f8c5a2b"),
            Guid.Parse("e8f3d7a1-5b6c-4e2f-9c7a-3a5b1f2d9e6f"),
            Guid.Parse("d1a5f3c9-9e7d-42a8-9b2f-7c3e5a9b1d4f"),
            Guid.Parse("b9d3a5f7-8f6c-4b2a-9e1f-2d7a3e5b9f1c"),
            Guid.Parse("c1b7d9a4-6f2e-43d5-b3f9-9e5a7b1c2d4f"),
            Guid.Parse("f7e1a9d3-3c4b-42f6-8b9d-1e5c7a3b9f2d"),
            Guid.Parse("e9b5f3a1-4d7f-49a3-b2d9-6f5e3a1c7b9f"),
            Guid.Parse("a2d9e1b7-8f3c-49f5-9a7b-5c1b6e3d2f9a"),
            Guid.Parse("d7a3c9b5-2f9d-4e1f-b7a4-8e6c3f5b1a2d"),
            Guid.Parse("c5e1b9a7-3f7b-4a9c-9f2d-7a6f5b3d1e8a"),
            Guid.Parse("f1d7a5c3-9e6b-4d2f-8b3a-5f9c1a7e2d6b"),
            Guid.Parse("b1d4f9c2-8e3a-4f5b-9a6d-c5b7a2e9d4f3"),
            Guid.Parse("e7a9b1f3-9d5f-42c8-8a3e-2b5c6f7d9a1e"),
            Guid.Parse("c3e1d7b4-6f9a-49f5-8b2d-a1f9b5c7e4d6"),
            Guid.Parse("f9b1d7a4-2e3c-49f5-9a6b-4d7e2f1c9b3a"),
            Guid.Parse("a5f1b3e9-8d4a-41f7-9c6f-b2e3f7d1c4b6"),
            Guid.Parse("b7e4c9d1-5f2a-4f8e-9b3d-1a6f7c9b5e2d"),
            Guid.Parse("c1a7b5f9-3e8d-48f6-9d4c-7f1e2b3a5d9b"),
            Guid.Parse("d9f5a7b1-4c6e-49a8-b7f3-2d9e1f5b3a4c"),
            Guid.Parse("e3a9d1b7-7f2c-4e1b-9a5d-c4f2e7b9a3d6"),
            Guid.Parse("a1d9f7e5-6f4c-4b9a-82d7-f5b3a7e1c9d4"),
            Guid.Parse("b9e7c5f1-2d8f-42a4-9b3d-e7f5a9d1c3b6"),
            Guid.Parse("f3a7d9b1-8e4d-4f6a-9c1b-7a5f2d9b3e1c"),
            Guid.Parse("c7f9b1a5-5d2e-49b4-9f6c-3a9e7d1b4c2a"),
            Guid.Parse("a9d5e7b3-7f1c-48a6-9b2d-5f3e9c1b7a4f"),
            Guid.Parse("d5f1b9e7-4e6a-4f9c-82b1-c9b7a3d5f2e1"),
            Guid.Parse("b3a9d1f5-8d6f-4c7a-91e2-7b9f5a3c2d4e"),
            Guid.Parse("c1b9f5e7-3f4a-49b6-92a1-5e2d7f3a1c9b"),
            Guid.Parse("a7d9f5b3-6e4c-49f2-9b8a-3c5d7a1f9b2e"),
            Guid.Parse("d3a7e9b1-7f6d-4c2f-91b3-2f5e1d9c4b7a"),
            Guid.Parse("e1f9b3a7-5d4e-4b9c-82f5-7a3d9f1c2b6e"),
            Guid.Parse("f8f8b957-832e-4b94-8b8a-b2e3627f6e17"),
            Guid.Parse("9a6f3e39-d43e-4b91-b118-89821b9997e8"),
            Guid.Parse("b8e7c4f9-5d4a-4e6f-a7c2-1b8e4a9f3e6d"),
            Guid.Parse("3ad7b7ca-6ed0-43b8-8859-4e9f407b4c5c"),
        };

        private static readonly List<Guid> listOfGuidForPackagePricingJobFy = new List<Guid>
        {
            Guid.Parse("d9e0cb7c-3a4d-4a45-91a7-5db4d6a34d95"),
            Guid.Parse("b6a2c5b6-3b3e-4b26-b09f-1f8e78c64b2a"),
            Guid.Parse("a5c7d83f-0b1b-4b3e-81d4-c1e4c9d8f97c"),
            Guid.Parse("c3a6a4fc-5d88-4c4e-91ae-761f2c4d3b85")
        };

        #endregion

        #region Seed Guid Ids - JobPays
        private static readonly List<Guid> listOfGuidsForPricingPlansJobPays = new List<Guid>()
        {
            Guid.Parse("f3d7bbcf-5f7c-4a45-bd21-761f2c4d3b85")
        };

        private static readonly List<Guid> listOfGuidForPackagePricingJobPays = new List<Guid>
        {
            Guid.Parse("a81cbda3-1454-4d37-83cc-9882178ca042")
        };
        #endregion

        #region Constants
        //Roles
        public const string Admin = "Admin";
        public const string SuperAdmin = "Super Admin";
        public const string TeamMember = "Team Member";
        public const string EventAdmin = "Event Admin";
        public const string ContentAdmin = "Content Admin";
        #endregion

        // Seed Data
        #region Seed Data
        public static async Task SeedData(string subdomain = null, string application = null, bool? applyPublicSchemaSeeding = null)
        {
            JobProDbContext publicContext = new JobProDbContext(new DbContextSchema());
            var subdomains = await publicContext.Tenants.Select(x => x.Subdomain).ToListAsync();

            // Get Logger service

            if (!string.IsNullOrEmpty(subdomain))
            {
                subdomains = new List<string> { subdomain };
            }

            if (applyPublicSchemaSeeding is not null && applyPublicSchemaSeeding.Value)
            {
                // Seeding for Microservices
                await SeedMicroServices();

                // Seeding related to new subscriotion flow for joble
                await SeedNewPricingPlans(new List<string> { Constants.PUBLIC_SCHEMA });
                await SeedNewJobProjectFeatures(new List<string> { Constants.PUBLIC_SCHEMA });
                await SeedNewJobProjectPricingAndFeature(new List<string> { Constants.PUBLIC_SCHEMA });
                await SeedNewPackagePricing(new List<string> { Constants.PUBLIC_SCHEMA });
                await SeedAIPackagePricing(Constants.PUBLIC_SCHEMA);

                //// Seeding for Joble
                //await SeedPricingPlans(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobProjectFeatures(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobProjectPricingAndFeature(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedPackagePricing(new List<string> { Constants.PUBLIC_SCHEMA });

                // Echo seeding 
                //await SeedEchoPricingPlans(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedEchoFeatures(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedEchoPricingAndFeature(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedEchoPackagePricing(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedWhatsappSMSEmailEvents(new List<string> { Constants.PUBLIC_SCHEMA });

                //// JobID Seeding
                ////await SeedJobIdPricingPlans(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobIdFeatures(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobIdPricingAndFeature(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobIDPackagePricing(new List<string> { Constants.PUBLIC_SCHEMA });

                //// JobFy Seeding
                //await SeedJobFyPricingPlans(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobFyFeatures(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobFyPricingAndFeature(new List<string> { Constants.PUBLIC_SCHEMA });
                ////await SeedJobFyPackagePricing(new List<string> { Constants.PUBLIC_SCHEMA });

                //// JobPays Seeding
                //await SeedJobPaysPricingPlans(new List<string> { Constants.PUBLIC_SCHEMA });
                //await SeedJobPaysPackagePricing(new List<string> { Constants.PUBLIC_SCHEMA });

                // Delete pricing and features for all applications where descriptin and category is null or empty
                await DeletePricingAndFeatures(new List<string> { Constants.PUBLIC_SCHEMA });
            }

            if (!string.IsNullOrEmpty(application))
            {
                if (application == Applications.Joble.ToString())
                {
                    await SeedEmployeeAppRole(subdomains);
                    await SeedEmployeeAppPermission(subdomains);
                    await SeedEmployeeRolePermission(subdomains);
                    await SeedTeams(subdomains);
                    await SeedPhoneNumbers(subdomains);
                }
                else if (application == Applications.JobPays.ToString())
                {
                    await SeedEmployeeAppRoleForJobPays(subdomains);
                }
                else if (application == Applications.Echo.ToString())
                {
                    await SeedEmployeeAppRoleForEcho(subdomains);
                }
            }
        }
        #endregion

        #region Seed Micro Services
        public static async Task SeedMicroServices()
        {
            var context = new JobProDbContext(new DbContextSchema());
            var microServices = new List<MicroServicesDto>
            {
                new MicroServicesDto
                {
                    ServiceId = "JOBPRO:ADYKUHHSFDJJ",
                    ServiceName = "CALENDER",
                    Key = "ASGHJD767DSNDSMDNSMSMFHDOJJNGD787DFH",
                },
                new MicroServicesDto
                {
                    ServiceId = "JOBPRO:JJSSDDHSFDJJ",
                    ServiceName = "JOBID",
                    Key = "YFSHJD767DSNDSMDNSMQQQQDOJJNGD787DFH",
                },
                new MicroServicesDto
                {
                    ServiceId = "JOBPRO:QQDFHDNFDJJ",
                    ServiceName = "JOBLE",
                    Key = "XXXHJD767DSNDSMDNSMSMFHDOJJN23FG7DFH",
                },
                new MicroServicesDto
                {
                    ServiceId = "JOBPRO:KJHJHJHJHJHJ",
                    ServiceName = "SUBSCRIPTION",
                    Key = "ASGHJD767DSNDSQQQSMSMFHDOJJNGD78788H",
                },
                new MicroServicesDto
                {
                    ServiceId = "JOBPRO:NNHJUUHJHJAA",
                    ServiceName = "USER",
                    Key = "ASGHJD767DSNDSMDNSMSMFHDOJJNGD787DFH",
                },
                new MicroServicesDto
                {
                    ServiceId = "JOBPRO:CRHJWASJHJBB",
                    ServiceName = "AI",
                    Key = "JUDEJD767DSNDSMDNSMSMFNNNJJNGD787BDO",
                },
            };

            foreach (var service in microServices)
            {
                var serviceInDb = await context.MicroServices.FirstOrDefaultAsync(x => x.ServiceId == service.ServiceId);
                if (serviceInDb == null)
                {
                    context.MicroServices.Add(new MicroService
                    {
                        ServiceId = service.ServiceId,
                        ServiceName = service.ServiceName,
                        Key = service.Key,
                    });
                }
                else
                {
                    serviceInDb.ServiceName = service.ServiceName;
                    serviceInDb.Key = service.Key;

                    context.MicroServices.Update(serviceInDb);
                }
            }

            await context.SaveChangesAsync();
        }
        #endregion

        // Seeding related to new subscription flow for joble
        #region Seed AI Plan amounts
        public static async Task SeedAIPackagePricing(string subdomain)
        {
            // Get the display name of an Enum
            Type enumType = typeof(AIAgents);
            var agents = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var aiAgents = new List<AIAgent>();
            var i = 0;
            foreach (var agent in agents)
            {
                aiAgents.Add(new AIAgent
                {
                    Id = listOfGuidsForAIPricingPlans[i],
                    Agent = agent,
                    AmountPerMonthPerUser = 10,
                    AmountPerYearPerUser = 0.0,
                    CreatedOn = DateTime.UtcNow,
                });

                i++;
            }

            var context = new JobProDbContext(new DbContextSchema(subdomain));
            foreach (var a in aiAgents)
            {
                var aiAgentInDb = await context.AIAgents.FirstOrDefaultAsync(x => x.Agent == a.Agent);
                if (aiAgentInDb == null)
                {
                    context.AIAgents.Add(a);
                }
            }
            await context.SaveChangesAsync();
        }
        #endregion

        #region Seed Joble Subscription Plans
        private static async Task SeedNewPricingPlans(List<string> subdomains)
        {
            // Get the display name of an Enum
            Type enumType = typeof(SubscriptionPlans);
            var pricingPlans = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var plans = new List<PricingPlan>();

            var i = 0;
            foreach (var plan in pricingPlans)
            {
                if (plan == SubscriptionPlans.AIPackages.ToString())
                {
                    plans.Add(new PricingPlan
                    {
                        Id = newListOfGuidsForPricingPlans[i],
                        Name = plan,
                        Application = Applications.All
                    });
                    i++;
                    continue;
                }

                plans.Add(new PricingPlan
                {
                    Id = newListOfGuidsForPricingPlans[i],
                    Name = plan,
                    Application = Applications.Joble
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var plan in plans)
                {
                    var pricingPlan = await context.PricingPlans
                        .FirstOrDefaultAsync(x => (x.Name == plan.Name && x.Application == Applications.Joble) || x.Id == plan.Id);
                    if (pricingPlan == null)
                    {
                        context.PricingPlans.Add(plan);
                    }
                    else
                    {
                        pricingPlan.Name = plan.Name;
                        pricingPlan.Application = plan.Application;

                        context.PricingPlans.Update(pricingPlan);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Joble Features - All Packages
        public static async Task SeedNewJobProjectFeatures(List<string> subdoamins)
        {
            // Get the display name of an Enum
            Type enumType = typeof(JobleFeatures);
            var features = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var featuresList = new List<Feature>();

            var i = 0;
            foreach (var feature in features)
            {
                featuresList.Add(new Feature
                {
                    Id = newListOfGuidsForProjectFeatures[i],
                    FeatureName = feature,
                    Application = Applications.Joble.ToString(),
                });
                i++;
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var feature in featuresList)
                {
                    var featureInDb = await context.Features
                        .FirstOrDefaultAsync(x => (x.FeatureName == feature.FeatureName && x.Application == Applications.Joble.ToString()) || x.Id == feature.Id);
                    if (featureInDb == null)
                    {
                        context.Features.Add(feature);
                    }
                    else
                    {
                        featureInDb.FeatureName = feature.FeatureName;
                        featureInDb.Application = feature.Application;

                        context.Features.Update(featureInDb);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Joble Pricing and Features - Joble Basic Plan
        public static async Task SeedNewJobProjectPricingAndFeature(List<string> subdoamins)
        {
            var pricingAndFeatures = new List<PricingAndFeature>();

            // The order of the features should be the same as the order of the pricing plans and the order of 'CategoryForFeatures' enum
            Type enumType = typeof(JobleFeatures);
            var descriptions = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name).ToList();

            var limitedTo = new List<string>
            {
                "1", "5", "40" // Basic
            };

            var categories = new List<string>
            {
                CategoriesForFeatures.Chat.ToString(), CategoriesForFeatures.Project.ToString(), CategoriesForFeatures.Calender.ToString() // Basic
            };

            var durationType = new List<string>
            {
                DurationTypes.Month.ToString(), DurationTypes.Number.ToString(), DurationTypes.Minutes.ToString() // Basic
            };

            int i = 0;
            foreach (var featureId in newListOfGuidsForProjectFeatures)
            {
                pricingAndFeatures.Add(new PricingAndFeature
                {
                    Id = newListOfGuidsForPricingFeatures[i],
                    PricingPlanId = newListOfGuidsForPricingPlans[0], // Basic plan Id
                    FeatureId = featureId,
                    DurationType = durationType[i],
                    Category = categories[i],
                    Description = descriptions[i],
                    LimitedTo = limitedTo[i],
                    IsLimited = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                });

                i++;
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    var pricingAndFeatureInDb = await context.PricingAndFeatures.FirstOrDefaultAsync(x => x.PricingPlanId == pricingAndFeature.PricingPlanId && x.FeatureId == pricingAndFeature.FeatureId);
                    if (pricingAndFeatureInDb == null)
                    {
                        context.PricingAndFeatures.Add(pricingAndFeature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Package Pricing - Joble Full Access
        public static async Task SeedNewPackagePricing(List<string> subdomains)
        {
            var packagePricing = new List<PackagePricing>();

            // Use the regions list to create a disctionary of region and the corresponding curreny and price
            var regionCurrencyPrice = new Dictionary<SubscriptionRegions, Tuple<string, double>>()
            {
                { SubscriptionRegions.Europe, new Tuple<string, double>(Currency.EUR.ToString(), 7.99) },
                { SubscriptionRegions.Nigeria, new Tuple<string, double>(Currency.NGN.ToString(), 5000.00) },
                { SubscriptionRegions.RestOfTheWorld, new Tuple<string, double>(Currency.USD.ToString(), 8.50) },
            };

            var i = 0;
            foreach (var region in regionCurrencyPrice)
            {
                packagePricing.Add(new PackagePricing
                {
                    Id = newListOfGuidForPackagePricing[i],
                    PricingPlanId = newListOfGuidsForPricingPlans[1], // Full access plan Id
                    Application = Applications.Joble.ToString(),
                    Currency = region.Value.Item1,
                    PricePerMonth = region.Value.Item2,
                    PricePerMonthForYearlyOption = region.Value.Item2,
                    Region = region.Key,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                i++;
            }

            packagePricing.Add(new PackagePricing
            {
                Id = newListOfGuidForPackagePricing[i],
                PricingPlanId = newListOfGuidsForPricingPlans[2], // AIPackages plan Id
                Application = Applications.All.ToString(),
                Currency = Currency.EUR.ToString(),
                PricePerMonth = 0.0,
                PricePerMonthForYearlyOption = 0.0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });
            i++;

            packagePricing.Add(new PackagePricing
            {
                Id = newListOfGuidForPackagePricing[i],
                PricingPlanId = newListOfGuidsForPricingPlans[3], // Enterprise plan Id
                Application = Applications.Joble.ToString(),
                Currency = Currency.EUR.ToString(),
                PricePerMonth = 0.0,
                PricePerMonthForYearlyOption = 0.0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            });

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var package in packagePricing)
                {
                    var packageInDb = await context.PackagePricing
                        .FirstOrDefaultAsync(x => x.PricingPlanId == package.PricingPlanId && x.Application == package.Application);
                    if (packageInDb == null)
                    {
                        context.PackagePricing.Add(package);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        // Joble
        #region Seed Pricing Plans - Joble
        private static async Task SeedPricingPlans(List<string> subdomains)
        {
            // Get the display name of an Enum
            Type enumType = typeof(PricingPlans);
            var pricingPlans = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var plans = new List<PricingPlan>();

            var i = 0;
            foreach (var plan in pricingPlans)
            {
                plans.Add(new PricingPlan
                {
                    Id = listOfGuidsForPricingPlans[i],
                    Name = plan,
                    Application = Applications.Joble
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var plan in plans)
                {
                    var pricingPlan = await context.PricingPlans
                        .FirstOrDefaultAsync(x => x.Name == plan.Name && x.Application == Applications.Joble);
                    if (pricingPlan == null)
                    {
                        context.PricingPlans.Add(plan);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Joble Features - Joble
        public static async Task SeedJobProjectFeatures(List<string> subdoamins)
        {
            // Get the display name of an Enum
            Type enumType = typeof(CategoriesForFeatures);
            var features = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var featuresList = new List<Feature>();

            var i = 0;
            foreach (var feature in features)
            {
                featuresList.Add(new Feature
                {
                    Id = listOfGuidsForProjectFeatures[i],
                    FeatureName = feature,
                    Application = Applications.Joble.ToString(),
                });
                i++;
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var feature in featuresList)
                {
                    var featureInDb = await context.Features
                        .FirstOrDefaultAsync(x => x.FeatureName == feature.FeatureName && x.Application == Applications.Joble.ToString());
                    if (featureInDb == null)
                    {
                        context.Features.Add(feature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Joble Pricing and Features - Joble
        public static async Task SeedJobProjectPricingAndFeature(List<string> subdoamins)
        {
            var pricingAndFeatures = new List<PricingAndFeature>();

            // The order of the features should be the same as the order of the pricing plans and the order of 'CategoryForFeatures' enum
            var descriptions = new List<string>() {
                "AI Assistant", "", "Limited Project Creation", "Limited Communication History (1 Month)", "Unlimited Timesheet Management", "7 Days Activity Log History", "Unlimited Appointment / Calendar System", "Limited Storage", "10 Users", // Starter

                "AI Assistant", "Team Productivity Tooling", "Unlimited Project Creation", "Unlimited Communication History", "Unlimited Timesheet Management", "14 Days History Activity Log", "Unlimited Appointment / Calendar System", "Limited Storage", "25 Users", // Growth

                "AI Assistant", "Team Productivity Tooling", "Unlimited Project Creation", "Unlimited Communication History", "Unlimited Timesheet Management", "1 Month History Activity Log", "Unlimited Appointment / Calendar System", "UnLimited Storage", "50 Users", // Business Pro

                "AI Assistant", "Team Productivity Tooling", "Unlimited Project Creation", "Unlimited Communication History", "Unlimited Timesheet Management", "UnLimited History Activity Log", "Unlimited Appointment / Calendar System", "UnLimited Storage", "UnLimited Users" // Enterprise
            };

            var limitedTo = new List<string>
            {
                "", "", "100", "1", "", "7", "", "20GB", "10", // Starter
                "", "", "", "", "", "14", "", "30GB", "25", // Growth
                "", "", "", "", "", "1", "", "", "50", // Business Pro
                "", "", "", "", "", "", "", "", "" // Enterprise
            };

            var categories = new List<string>
            {
                CategoriesForFeatures.AI.ToString(), "", CategoriesForFeatures.Project.ToString(), CategoriesForFeatures.Chat.ToString(), CategoriesForFeatures.TimeSheet.ToString(), CategoriesForFeatures.ActivityLog.ToString(), CategoriesForFeatures.Calender.ToString(), CategoriesForFeatures.Storage.ToString(), CategoriesForFeatures.User.ToString(), // Starter

                CategoriesForFeatures.AI.ToString(), CategoriesForFeatures.Team.ToString(), CategoriesForFeatures.Project.ToString(), CategoriesForFeatures.Chat.ToString(), CategoriesForFeatures.TimeSheet.ToString(), CategoriesForFeatures.ActivityLog.ToString(), CategoriesForFeatures.Calender.ToString(), CategoriesForFeatures.Storage.ToString(), CategoriesForFeatures.User.ToString(), // Growth

                CategoriesForFeatures.AI.ToString(), CategoriesForFeatures.Team.ToString(), CategoriesForFeatures.Project.ToString(), CategoriesForFeatures.Chat.ToString(), CategoriesForFeatures.TimeSheet.ToString(), CategoriesForFeatures.ActivityLog.ToString(), CategoriesForFeatures.Calender.ToString(), CategoriesForFeatures.Storage.ToString(), CategoriesForFeatures.User.ToString(), // Business Pro

                CategoriesForFeatures.AI.ToString(), CategoriesForFeatures.Team.ToString(), CategoriesForFeatures.Project.ToString(), CategoriesForFeatures.Chat.ToString(), CategoriesForFeatures.TimeSheet.ToString(), CategoriesForFeatures.ActivityLog.ToString(), CategoriesForFeatures.Calender.ToString(), CategoriesForFeatures.Storage.ToString(), CategoriesForFeatures.User.ToString() // Enterprise
            };

            var durationType = new List<string>
            {
                "", "", DurationTypes.Number.ToString(), DurationTypes.Month.ToString(), "", DurationTypes.Day.ToString(), "", DurationTypes.Number.ToString(), DurationTypes.Number.ToString(), // Starter

                "", "", "", "", "", DurationTypes.Day.ToString(), "", DurationTypes.Number.ToString(), DurationTypes.Number.ToString(), // Growth

                "", "", "", "", "", DurationTypes.Month.ToString(), "", DurationTypes.Number.ToString(), DurationTypes.Number.ToString(), // Business Pro

                "", "", "", "", "", "", "", "", "" // Enterprise
            };

            var isLimited = new List<bool>
            {
                false, false, true, true, false, true, false, true, true, // Starter
                false, false, false, false, false, true, false, true, true, // Growth
                false, false, false, false, false, true, false, false, true, // Business Pro
                false, false, false, false, false, false, false, false, false // Enterprise
            };

            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlans)
            {
                foreach (var feature in listOfGuidsForProjectFeatures)
                {
                    pricingAndFeatures.Add(new PricingAndFeature
                    {
                        Id = listOfGuidsForPricingFeatures[i],
                        PricingPlanId = plan,
                        FeatureId = feature,
                        DurationType = durationType[i],
                        Category = categories[i],
                        Description = descriptions[i],
                        LimitedTo = limitedTo[i],
                        IsLimited = isLimited[i],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    });

                    i++;
                }
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    var pricingAndFeatureInDb = await context.PricingAndFeatures.FirstOrDefaultAsync(x => x.PricingPlanId == pricingAndFeature.PricingPlanId && x.FeatureId == pricingAndFeature.FeatureId);
                    if (pricingAndFeatureInDb == null)
                    {
                        context.PricingAndFeatures.Add(pricingAndFeature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Package Pricing - Joble
        public static async Task SeedPackagePricing(List<string> subdomains)
        {
            // EUR
            var pricing = new List<double> { 8.0, 15.0, 25.0, 0.0 }; // Starter, Growth, Business Pro and Enterprise
            var pricePerMonthForYearlyOption = new List<double> { 6.0, 13.0, 23.0, 0.0 };
            var packagePricing = new List<PackagePricing>();
            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlans)
            {
                packagePricing.Add(new PackagePricing
                {
                    Id = listOfGuidForPackagePricing[i],
                    PricingPlanId = plan,
                    Application = Applications.Joble.ToString(),
                    Currency = Currency.EUR.ToString(),
                    PricePerMonth = pricing[i],
                    PricePerMonthForYearlyOption = pricePerMonthForYearlyOption[i],
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var package in packagePricing)
                {
                    var packageInDb = await context.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == package.PricingPlanId && x.Application == package.Application);
                    if (packageInDb == null)
                    {
                        context.PackagePricing.Add(package);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Employee App Roles - Joble
        public static async Task SeedEmployeeAppRole(List<string> subdomains)
        {
            var employeeRoles = new List<EmployeeRoles>
            {
                new EmployeeRoles {Id = "0b6dada9-e0b5-42fc-af9f-69e39d6a3d5f", RoleName = SuperAdmin, PackageName = Applications.Joble.ToString()},
                new EmployeeRoles {Id = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", RoleName = TeamMember, PackageName = Applications.Joble.ToString()},
                new EmployeeRoles {Id = "2e17e358-b625-485b-8288-d4166d211b9b", RoleName = EventAdmin, PackageName = "JobEvent"},
                new EmployeeRoles {Id = "3a863e9b-801e-4e8e-9087-001690c93edf", RoleName = ContentAdmin, PackageName = "JobEvent"},
                new EmployeeRoles {Id = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", RoleName = Admin, PackageName = "JobEvent"}
            };

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var employeeRole in employeeRoles)
                {
                    var employeeRoleInDb = await context.EmployeeRoles.FirstOrDefaultAsync(x => x.RoleName == employeeRole.RoleName && x.PackageName == employeeRole.PackageName);
                    if (employeeRoleInDb == null)
                    {
                        context.EmployeeRoles.Add(employeeRole);
                    }
                }
                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    throw ex;
                }
            }
        }
        #endregion

        #region Seed Employee AppPermission - Joble
        private static async Task SeedEmployeeAppPermission(List<string> subdomains)
        {
            var employeePermissions = new List<EmployeePermission>
            {
                new EmployeePermission {Id = "5835f118-50ff-4eb5-b151-66d214586578", PermissionName = "can_add_users", PackageName = Applications.Joble.ToString(), PermissionCategory ="User Management"},
                new EmployeePermission {Id = "cfe21334-a215-459d-a18b-bc70d5a50310", PermissionName = "can_remove_user", PackageName = Applications.Joble.ToString(), PermissionCategory ="User Management"},
                new EmployeePermission {Id = "cbe9a0b7-1f0c-4726-8a69-c3a5f751b829", PermissionName = "can_create_event", PackageName = "JobEvent", PermissionCategory ="Package Permission"},
                 new EmployeePermission {Id = "ae53250e-23ce-425e-9da7-419db4c96ffa", PermissionName = "can_assign_roles_and_permissions", PackageName = Applications.Joble.ToString(), PermissionCategory ="User Management"},
                new EmployeePermission {Id = "ea0dcdff-d3d0-4504-8273-25b65f72a325", PermissionName = "can_create_edit_delete_projects", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "53c8d4fb-ef28-4fed-a7d1-32f59b7d81f8", PermissionName = "can_create_edit_delete_todos", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "f8b4b49c-a4b2-4397-ba3e-a77232454ad6", PermissionName = "can_assign_team's_todos", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "44fe8c5c-4036-4a71-b71f-7ef08fd9bd91", PermissionName = "can_access_and_modify_timesheet", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "cbea28da-ad1b-4141-96aa-c733334ba014", PermissionName = "can_download_timesheet_report", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "c7cb2cf7-ac0c-4826-bee2-10c13037d5ca", PermissionName = "can_adjust_timesheet_entries", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "a08bbb21-4792-4076-8e42-cc8ddabae8f6", PermissionName = "can_lock_todo", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "1f30624f-4181-44fb-965f-33245f3e1faa", PermissionName = "can_adjust_project_rate", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "8c5454c7-0b0d-4eee-b619-00a715c9988b", PermissionName = "can_toggle_activity_off_and_on", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "b7c37357-234e-4903-aace-038629222744", PermissionName = "can_view_other_team_members_activities", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "46331475-f1f0-47a2-89e8-af17322234e6", PermissionName = "can_view_other_team_members_timesheet", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "6d3e1f8b-4209-4e17-a735-e93e11b6e412", PermissionName = "can_create_meetings", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "a89bb7f9-2927-4926-abd7-05a533229213", PermissionName = "can_view_other_team_members_calender", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "08f9f351-2845-4d08-ac82-11a38c669029", PermissionName = "can_create_triggers", PackageName = Applications.Joble.ToString(), PermissionCategory ="Package Permission"},
                new EmployeePermission {Id = "600b827e-5735-4c3f-b899-a55321aa4a3b", PermissionName = "can_update_other_user's_project", PackageName = Applications.Joble.ToString(), PermissionCategory = "Package Permission"},
                new EmployeePermission {Id = "149b15c8-7c56-4da6-a505-a3d012726fd6", PermissionName = "can_view_other_users_project", PackageName = Applications.Joble.ToString(), PermissionCategory = "Package Permission"},
                new EmployeePermission {Id = "12bff828-07b7-4d91-8e5e-884ff18da527", PermissionName = "all", PackageName = Applications.Joble.ToString(), PermissionCategory = "All"}

            };

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var employeePermission in employeePermissions)
                {
                    var employeePermissionInDb = await context.EmployeePermissions.FirstOrDefaultAsync(x => x.PermissionName == employeePermission.PermissionName && x.PackageName == employeePermission.PackageName);
                    if (employeePermissionInDb == null)
                    {
                        context.EmployeePermissions.Add(employeePermission);
                    }
                }
                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    // Log the exception
                    throw ex;
                }
            }
        }
        #endregion

        #region Seed EmployeeRole Permission - Joble
        public static async Task SeedEmployeeRolePermission(List<string> subdomains)
        {
            var employeeRolePermissions = new List<EmployeeRolesPermission>
            {
                // Super Admin
                new EmployeeRolesPermission { Id = "cfe21334-a215-459d-a18b-bc70d5a50310", RoleId = "0b6dada9-e0b5-42fc-af9f-69e39d6a3d5f", PermissionId = "12bff828-07b7-4d91-8e5e-884ff18da527"},

                // Team Member
                new EmployeeRolesPermission { Id = "e6915e4c-0a34-45e6-ad73-568ae5998581", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "ea0dcdff-d3d0-4504-8273-25b65f72a325" },
                new EmployeeRolesPermission { Id = "2a2c056c-1c29-4232-872a-c057904a00a3", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "53c8d4fb-ef28-4fed-a7d1-32f59b7d81f8" },
                new EmployeeRolesPermission { Id = "b2b6b2a9-5b9a-4b9e-9e9a-9e0e2b6b2b9b", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "f8b4b49c-a4b2-4397-ba3e-a77232454ad6" },
                new EmployeeRolesPermission { Id = "8d4396b6-92c9-415c-9b8e-b2f84daf2c12", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "c7cb2cf7-ac0c-4826-bee2-10c13037d5ca" },
                new EmployeeRolesPermission { Id = "bac24477-4a96-4ee0-9ca6-2aa2da859617", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "8c5454c7-0b0d-4eee-b619-00a715c9988b" },
                new EmployeeRolesPermission { Id = "f4c2ce91-bbb7-4584-a680-4c84abece12c", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "6d3e1f8b-4209-4e17-a735-e93e11b6e412" },
                new EmployeeRolesPermission { Id = "dba32430-a0ae-4d41-b9cb-67f2301e1862", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "08f9f351-2845-4d08-ac82-11a38c669029" },
                new EmployeeRolesPermission { Id = "cc82d733-5527-4bba-8b55-904e27efa596", RoleId = "0d49e47b-6b35-4da1-8988-fcbc9bba74d0", PermissionId = "44fe8c5c-4036-4a71-b71f-7ef08fd9bd91" },

                // Admin
                new EmployeeRolesPermission { Id = "ce5f23a9-807f-4f50-b178-a70dd20a7fc8", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "5835f118-50ff-4eb5-b151-66d214586578" },
                new EmployeeRolesPermission { Id = "e2b6b2a9-5b9a-4b9e-9e9a-9e0e2b6b2b9b", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "cfe21334-a215-459d-a18b-bc70d5a50310" },
                new EmployeeRolesPermission { Id = "f2b6b2a9-5b9a-4b9e-9e9a-9e0e2b6b2b9b", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "ae53250e-23ce-425e-9da7-419db4c96ffa" },
                new EmployeeRolesPermission { Id = "556f1c25-b3bf-4e55-a216-84d9a87b3511", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "44fe8c5c-4036-4a71-b71f-7ef08fd9bd91" },
                new EmployeeRolesPermission { Id = "a9e2b2a9-5b9a-4b9e-9e9a-9e0e2b6b2b9b", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "a08bbb21-4792-4076-8e42-cc8ddabae8f6" },
                new EmployeeRolesPermission { Id = "b8270599-2b1e-4ffa-8e85-06a54f672583", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "1f30624f-4181-44fb-965f-33245f3e1faa" },
                new EmployeeRolesPermission { Id = "4db78088-1bed-460d-aced-44a9fe27b4a5", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "b7c37357-234e-4903-aace-038629222744" },
                new EmployeeRolesPermission { Id = "20ec8008-de8c-4da6-b542-4327a41e46a4", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "46331475-f1f0-47a2-89e8-af17322234e6" },
                new EmployeeRolesPermission { Id = "860ce87b-ceba-486c-a55f-9cd6e17ce4e6", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "a89bb7f9-2927-4926-abd7-05a533229213" },
                new EmployeeRolesPermission { Id = "d5ceea5a-2ce4-4395-93d5-7846db0cddd6", RoleId = "529cbc36-5b16-484f-8f55-eda9f15d7b2f", PermissionId = "cbea28da-ad1b-4141-96aa-c733334ba014" },
            };

            foreach (var subdomain in subdomains)
            {
                if (subdomain == Constants.PUBLIC_SCHEMA)
                    continue;

                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var employeeRolePermission in employeeRolePermissions)
                {
                    var employeeRolePermissionInDb = context.EmployeeRolesPermissions.FirstOrDefault(x => x.Id == employeeRolePermission.Id);
                    if (employeeRolePermissionInDb == null)
                    {
                        context.EmployeeRolesPermissions.Add(employeeRolePermission);
                    }
                }
                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    // Log the exception
                    throw ex;
                }
            }
        }
        #endregion

        // Echo
        #region Seed Echo Pricing Plans
        private static async Task SeedEchoPricingPlans(List<string> subdomains)
        {
            // Get the display name of an Enum
            Type enumType = typeof(EchoPricingPlans);
            var pricingPlans = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var plans = new List<PricingPlan>();
            var i = 0;
            foreach (var plan in pricingPlans)
            {
                plans.Add(new PricingPlan
                {
                    Id = listOfGuidsForPricingPlansEcho[i],
                    Name = plan,
                    Application = Applications.Echo
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var plan in plans)
                {
                    var pricingPlan = await context.PricingPlans.FirstOrDefaultAsync(x => x.Name == plan.Name && x.Application == Applications.Echo);
                    if (pricingPlan == null)
                    {
                        context.PricingPlans.Add(plan);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Echo Features
        public static async Task SeedEchoFeatures(List<string> subdoamins)
        {
            // Get the display name of an Enum
            Type enumType = typeof(EchoFeatures);
            var features = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var featuresList = new List<Feature>();

            var i = 0;
            foreach (var feature in features)
            {
                featuresList.Add(new Feature
                {
                    Id = listOfGuidsForProjectFeaturesEcho[i],
                    FeatureName = feature,
                    Application = Applications.Echo.ToString(),
                });
                i++;
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var feature in featuresList)
                {
                    var featureInDb = await context.Features.FirstOrDefaultAsync(x => x.FeatureName == feature.FeatureName && x.Application == Applications.Echo.ToString());
                    if (featureInDb == null)
                    {
                        context.Features.Add(feature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Echo Pricing and Features
        public static async Task SeedEchoPricingAndFeature(List<string> subdoamins)
        {
            var pricingAndFeatures = new List<PricingAndFeature>();

            // The order of the features should be the same as the order of the pricing plans and the order of 'CategoryForFeatures' enum
            var descriptions = new List<string> { "Contact and Lead Management", "Basic Sales Automation Tools", "Standard Reporting Features", "Multi-Channel Campaign Management", "Enhanced Automation", "AI-Powered Scheduling", "Advanced Analytics", "Priority Customer Support", "Integration Capabilities", "Access to New Features/Updates", "Dedicated Account Manager", "Custom Integration", "Customizable Features and Reports", "Contact and Lead Management", "Basic Sales Automation Tools", "Standard Reporting Features", "Multi-Channel Campaign Management", "Enhanced Automation", "AI-Powered Scheduling", "Advanced Analytics", "Priority Customer Support", "Integration Capabilities", "Access to New Features/Updates", "Dedicated Account Manager", "Custom Integration", "Customizable Features and Reports", "Contact and Lead Management", "Basic Sales Automation Tools", "Standard Reporting Features", "Multi-Channel Campaign Management", "Enhanced Automation", "AI-Powered Scheduling", "Advanced Analytics", "Priority Customer Support", "Integration Capabilities", "Access to New Features/Updates", "Dedicated Account Manager", "Custom Integration", "Customizable Features and Reports" };

            var limitedTo = new List<string> { "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "" };

            var categories = new List<string> { EchoFeatures.ContactAndLeadManagement.ToString(), EchoFeatures.BasicSalesAutomationTools.ToString(), EchoFeatures.StandardReportingFeatures.ToString(), EchoFeatures.MultiChannelCampaignManagement.ToString(), EchoFeatures.EnhancedAutomation.ToString(), EchoFeatures.AIPoweredScheduling.ToString(), EchoFeatures.AdvancedAnalytics.ToString(), EchoFeatures.PriorityCustomerSupport.ToString(), EchoFeatures.IntegrationCapabilities.ToString(), EchoFeatures.AccessToNewFeaturesUpdates.ToString(), EchoFeatures.DedicatedAccountManager.ToString(), EchoFeatures.CustomIntegration.ToString(), EchoFeatures.CustomizableFeaturesAndReports.ToString(), EchoFeatures.ContactAndLeadManagement.ToString(), EchoFeatures.BasicSalesAutomationTools.ToString(), EchoFeatures.StandardReportingFeatures.ToString(), EchoFeatures.MultiChannelCampaignManagement.ToString(), EchoFeatures.EnhancedAutomation.ToString(), EchoFeatures.AIPoweredScheduling.ToString(), EchoFeatures.AdvancedAnalytics.ToString(), EchoFeatures.PriorityCustomerSupport.ToString(), EchoFeatures.IntegrationCapabilities.ToString(), EchoFeatures.AccessToNewFeaturesUpdates.ToString(), EchoFeatures.DedicatedAccountManager.ToString(), EchoFeatures.CustomIntegration.ToString(), EchoFeatures.CustomizableFeaturesAndReports.ToString(), EchoFeatures.ContactAndLeadManagement.ToString(), EchoFeatures.BasicSalesAutomationTools.ToString(), EchoFeatures.StandardReportingFeatures.ToString(), EchoFeatures.MultiChannelCampaignManagement.ToString(), EchoFeatures.EnhancedAutomation.ToString(), EchoFeatures.AIPoweredScheduling.ToString(), EchoFeatures.AdvancedAnalytics.ToString(), EchoFeatures.PriorityCustomerSupport.ToString(), EchoFeatures.IntegrationCapabilities.ToString(), EchoFeatures.AccessToNewFeaturesUpdates.ToString(), EchoFeatures.DedicatedAccountManager.ToString(), EchoFeatures.CustomIntegration.ToString(), EchoFeatures.CustomizableFeaturesAndReports.ToString() };

            var durationType = new List<string> { "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "" };

            var isLimited = new List<bool> { false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false };

            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansEcho)
            {
                foreach (var feature in listOfGuidsForProjectFeaturesEcho)
                {
                    pricingAndFeatures.Add(new PricingAndFeature
                    {
                        Id = listOfGuidsForPricingFeaturesEcho[i],
                        PricingPlanId = plan,
                        FeatureId = feature,
                        DurationType = durationType[i],
                        Category = categories[i],
                        Description = descriptions[i],
                        LimitedTo = limitedTo[i],
                        IsLimited = isLimited[i],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    });

                    i++;
                }
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    var pricingAndFeatureInDb = await context.PricingAndFeatures.FirstOrDefaultAsync(x => x.PricingPlanId == pricingAndFeature.PricingPlanId && x.FeatureId == pricingAndFeature.FeatureId);
                    if (pricingAndFeatureInDb == null)
                    {
                        context.PricingAndFeatures.Add(pricingAndFeature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Package Pricing - Echo
        public static async Task SeedEchoPackagePricing(List<string> subdomains)
        {
            // EUR
            var pricing = new List<double> { 75.0, 199.0, 350.0 }; // Starter, Growth, Business Pro and Enterprise
            var pricePerMonthForYearlyOption = new List<double> { 73.5, 195.0, 343.0 };
            var packagePricing = new List<PackagePricing>();
            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansEcho)
            {
                packagePricing.Add(new PackagePricing
                {
                    Id = listOfGuidForPackagePricingEcho[i],
                    PricingPlanId = plan,
                    Application = Applications.Echo.ToString(),
                    Currency = Currency.EUR.ToString(),
                    PricePerMonth = pricing[i],
                    PricePerMonthForYearlyOption = pricePerMonthForYearlyOption[i],
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var package in packagePricing)
                {
                    var packageInDb = await context.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == package.PricingPlanId && x.Application == package.Application);
                    if (packageInDb == null)
                    {
                        context.PackagePricing.Add(package);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        // Seed Roles
        #region Seed Employee App Roles - JobPays
        public static async Task SeedEmployeeAppRoleForJobPays(List<string> subdomains)
        {
            var employeeRoles = new List<EmployeeRoles>
            {
                new EmployeeRoles {Id = "3d81b1c0-c35f-470d-9c84-25c9b17d862f", RoleName = SuperAdmin, PackageName = Applications.JobPays.ToString()},
                new EmployeeRoles {Id = "fc9fa7a9-95d4-40f2-b7de-65dd276641ad", RoleName = TeamMember, PackageName = Applications.JobPays.ToString()},
            };

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var employeeRole in employeeRoles)
                {
                    var employeeRoleInDb = await context.EmployeeRoles.FirstOrDefaultAsync(x => x.RoleName == employeeRole.RoleName && x.PackageName == employeeRole.PackageName);
                    if (employeeRoleInDb == null)
                    {
                        context.EmployeeRoles.Add(employeeRole);
                    }
                }
                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    throw ex;
                }
            }
        }
        #endregion

        #region Seed Employee App Roles - Echo
        public static async Task SeedEmployeeAppRoleForEcho(List<string> subdomains)
        {
            var employeeRoles = new List<EmployeeRoles>
            {
                new EmployeeRoles {Id = "7c2dc3d5-b59d-4a69-924f-8ece83650912", RoleName = SuperAdmin, PackageName = Applications.Echo.ToString()},
                new EmployeeRoles {Id = "0eb16434-a271-4170-95f7-fa632155c736", RoleName = TeamMember, PackageName = Applications.Echo.ToString()},
            };

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var employeeRole in employeeRoles)
                {
                    var employeeRoleInDb = await context.EmployeeRoles.FirstOrDefaultAsync(x => x.RoleName == employeeRole.RoleName && x.PackageName == employeeRole.PackageName);
                    if (employeeRoleInDb == null)
                    {
                        context.EmployeeRoles.Add(employeeRole);
                    }
                }
                try
                {
                    await context.SaveChangesAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    throw ex;
                }
            }
        }
        #endregion

        // JobID
        //#region Seed JobID Pricing Plans
        //private static async Task SeedJobIdPricingPlans(List<string> subdomains)
        //{
        //    // Get the display name of an Enum
        //    Type enumType = typeof(JobIdPricingPlans);
        //    var pricingPlans = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
        //        .Where(field => field.IsLiteral)
        //        .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

        //    var plans = new List<PricingPlan>();
        //    var i = 0;
        //    foreach (var plan in pricingPlans)
        //    {
        //        plans.Add(new PricingPlan
        //        {
        //            Id = listOfGuidsForPricingPlansJobID[i],
        //            Name = plan,
        //            Application = Applications.JobID
        //        });

        //        i++;
        //    }

        //    foreach (var subdomain in subdomains)
        //    {
        //        var context = new JobProDbContext(new DbContextSchema(subdomain));
        //        foreach (var plan in plans)
        //        {
        //            var pricingPlan = await context.PricingPlans
        //                .FirstOrDefaultAsync(x => x.Name == plan.Name && x.Application == Applications.JobID);
        //            if (pricingPlan == null)
        //            {
        //                context.PricingPlans.Add(plan);
        //            }
        //        }

        //        await context.SaveChangesAsync();
        //    }
        //}
        //#endregion

        #region Seed JobID Features
        public static async Task SeedJobIdFeatures(List<string> subdoamins)
        {
            // Get the display name of an Enum
            Type enumType = typeof(JobIdFeatures);
            var features = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var featuresList = new List<Feature>();

            var i = 0;
            foreach (var feature in features)
            {
                featuresList.Add(new Feature
                {
                    Id = listOfGuidsForPackageFeaturesJobID[i],
                    FeatureName = feature,
                    Application = Applications.JobID.ToString(),
                });
                i++;
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var feature in featuresList)
                {
                    var featureInDb = await context.Features
                        .FirstOrDefaultAsync(x => x.FeatureName == feature.FeatureName && x.Application == Applications.JobID.ToString());
                    if (featureInDb == null)
                    {
                        context.Features.Add(feature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed JobID Pricing and Features
        public static async Task SeedJobIdPricingAndFeature(List<string> subdoamins)
        {
            var pricingAndFeatures = new List<PricingAndFeature>();

            // The order of the features should be the same as the order of the pricing plans and the order of 'CategoryForFeatures' enum
            var descriptions = new List<string>
            {
                "Job Posting", "", "", "User", // On Demand
                "Job Posting", "Candidate Pool Access", "Security Check", "User", // Starter
                "Job Posting", "Candidate Pool Access", "Security Check", "User", // Growth
                "Job Posting", "Candidate Pool Access", "Security Check", "User", // Business Growth
                "Job Posting", "Candidate Pool Access", "Security Check", "User", // Enterprise
            };

            var limitedTo = new List<string>
            {
                "" , "", "", "1", // On Demand
                "1", "", "", "1", // Starter
                "3", "", "", "5", // Growth
                "5", "", "", "50", // Business Growth
                "10", "", "", "100", // Enterprise
            };

            var categories = new List<string>
            {
                JobIdFeatures.JobPosting.ToString(), "", "", JobIdFeatures.User.ToString(),
                JobIdFeatures.JobPosting.ToString(), JobIdFeatures.CandidatePoolAccess.ToString(), JobIdFeatures.SecurityCheck.ToString(), JobIdFeatures.User.ToString(),
                JobIdFeatures.JobPosting.ToString(), JobIdFeatures.CandidatePoolAccess.ToString(), JobIdFeatures.SecurityCheck.ToString(), JobIdFeatures.User.ToString(),
                JobIdFeatures.JobPosting.ToString(), JobIdFeatures.CandidatePoolAccess.ToString(), JobIdFeatures.SecurityCheck.ToString(), JobIdFeatures.User.ToString(),
                JobIdFeatures.JobPosting.ToString(), JobIdFeatures.CandidatePoolAccess.ToString(), JobIdFeatures.SecurityCheck.ToString(), JobIdFeatures.User.ToString(),
            };

            var isLimited = new List<bool>
            {
                true, false, false, true, // On Demand
                true, false, false, true, // Starter
                true, false, false, true, // Growth
                true, false, false, true, // Business Growth
                true, false, false, true, // Enterprise
            };

            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansJobID)
            {
                foreach (var feature in listOfGuidsForPackageFeaturesJobID)
                {
                    pricingAndFeatures.Add(new PricingAndFeature
                    {
                        Id = listOfGuidsForPricingFeaturesJobID[i],
                        PricingPlanId = plan,
                        FeatureId = feature,
                        Category = categories[i],
                        Description = descriptions[i],
                        LimitedTo = limitedTo[i],
                        IsLimited = isLimited[i],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    });

                    i++;
                }
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    var pricingAndFeatureInDb = await context.PricingAndFeatures.FirstOrDefaultAsync(x => x.PricingPlanId == pricingAndFeature.PricingPlanId && x.FeatureId == pricingAndFeature.FeatureId);
                    if (pricingAndFeatureInDb == null)
                    {
                        context.PricingAndFeatures.Add(pricingAndFeature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Package Pricing - JobID
        public static async Task SeedJobIDPackagePricing(List<string> subdomains)
        {
            // EUR
            var pricing = new List<double> { 0.0, 45.0, 105.0, 245.0, 0.0 }; // On Demand, Starter, Growth, Business Growth and Enterprise
            var pricePerMonthForYearlyOption = new List<double> { 0.0, 40.0, 100.0, 240.0, 0.0 };
            var packagePricing = new List<PackagePricing>();
            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansJobID)
            {
                packagePricing.Add(new PackagePricing
                {
                    Id = listOfGuidForPackagePricingJobID[i],
                    PricingPlanId = plan,
                    Application = Applications.JobID.ToString(),
                    Currency = Currency.EUR.ToString(),
                    PricePerMonth = pricing[i],
                    PricePerMonthForYearlyOption = pricePerMonthForYearlyOption[i],
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var package in packagePricing)
                {
                    var packageInDb = await context.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == package.PricingPlanId && x.Application == package.Application);
                    if (packageInDb == null)
                    {
                        context.PackagePricing.Add(package);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        // JobFy
        #region Seed Pricing Plans - JobFy
        private static async Task SeedJobFyPricingPlans(List<string> subdomains)
        {
            // Get the display name of an Enum
            Type enumType = typeof(JobFyPricingPlans);
            var pricingPlans = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var plans = new List<PricingPlan>();

            var i = 0;
            foreach (var plan in pricingPlans)
            {
                plans.Add(new PricingPlan
                {
                    Id = listOfGuidsForPricingPlansJobFy[i],
                    Name = plan,
                    Application = Applications.JobFy
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var plan in plans)
                {
                    var pricingPlan = await context.PricingPlans
                        .FirstOrDefaultAsync(x => x.Name == plan.Name && x.Application == Applications.JobFy);
                    if (pricingPlan == null)
                    {
                        context.PricingPlans.Add(plan);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed JobFy Features
        public static async Task SeedJobFyFeatures(List<string> subdoamins)
        {
            // Get the display name of an Enum
            Type enumType = typeof(JobFyFeatures);
            var features = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var featuresList = new List<Feature>();

            var i = 0;
            foreach (var feature in features)
            {
                featuresList.Add(new Feature
                {
                    Id = listOfGuidsForPackageFeaturesJobFy[i],
                    FeatureName = feature,
                    Application = Applications.JobFy.ToString(),
                });
                i++;
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var feature in featuresList)
                {
                    var featureInDb = await context.Features
                        .FirstOrDefaultAsync(x => x.FeatureName == feature.FeatureName && x.Application == Applications.JobFy.ToString());
                    if (featureInDb == null)
                    {
                        context.Features.Add(feature);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed JobFy Pricing and Features
        public static async Task SeedJobFyPricingAndFeature(List<string> subdoamins)
        {
            var pricingAndFeatures = new List<PricingAndFeature>();

            // The order of the features should be the same as the order of the pricing plans and the order of 'CategoryForFeatures' enum
            var descriptions = new List<string>
            {
                // Starter Plan Features
                "User", "Monthly Search", "KYC Monthly Checks", "Guarantor Monthly Checks", "Document Signing", "Filing And Storage", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", // Starter

                // Growth Plan Features
                "User", "Monthly Search", "KYC Monthly Checks", "Guarantor Monthly Checks", "Document Signing", "Filing And Storage", "Data Retention (Archive)", "Address Verification", "", "", "", "", "", "", "", "", "", "", "", "", "", // Growth

                // Business Pro Plan Features
                "User", "Monthly Search", "KYC Monthly Checks", "Guarantor Monthly Checks", "Document Signing", "Filing And Storage", "Data Retention (Archive)", "Address Verification", "On-Ground Verification", "GDPR Compliance Tools", "Integrations", "", "", "", "", "", "", "", "", "", "", // Business Pro

                // Enterprise Plan Features
                "User", "Monthly Search", "KYC Monthly Checks", "Guarantor Monthly Checks", "Document Signing", "Filing And Storage", "Data Retention (Archive)", "Address Verification", "On-Ground Verification", "GDPR Compliance Tools", "Integrations", "PEP Sanctions Checks", "Verification Updates", "API Session Generation", "Ongoing Monitoring", "EmbeddedSigning", "Centralized Controls", "DedicatedSupportTeam", "AdvancedBranding", "Custom Usage Limits", "Industry-Specific Modules" // Enterprise
            };

            var summaries = new List<string>
            {
                // Starter Plan Features
                "User", "Simplified search tools to streamline your process.", "Keep your client data compliant and secure.", "Confidence in every deal with monthly checks.", "Seamlessly send documents for digital signing.", "Store and manage your documents with ease.", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", // Starter

                // Growth Plan Features
                "User", "Simplified search tools to streamline your process.", "Keep your client data compliant and secure.", "Confidence in every deal with monthly checks.", "Seamlessly send documents for digital signing.", "Store and manage your documents with ease.", "Safeguard your records with secure data archiving.", "Ensure accuracy with automated address verification.", "", "", "", "", "", "", "", "", "", "", "", "", "", // Growth

                // Business Growth Plan Features
                "User", "Simplified search tools to streamline your process.", "Keep your client data compliant and secure.", "Confidence in every deal with monthly checks.", "Seamlessly send documents for digital signing.", "Store and manage your documents with ease.", "Safeguard your records with secure data archiving.", "Ensure accuracy with automated address verification.", "Real-world verification for added security.", "Stay compliant with powerful GDPR management tools.", "Sync with Google Drive, Dropbox, and more for seamless data flow.", "", "", "", "", "", "", "", "", "", "", // Business Growth

                // Enterprise Plan Features
                "User", "Simplified search tools to streamline your process.", "Keep your client data compliant and secure.", "Confidence in every deal with monthly checks.", "Seamlessly send documents for digital signing.", "Store and manage your documents with ease.", "Safeguard your records with secure data archiving.", "Ensure accuracy with automated address verification.", "Real-world verification for added security.", "Stay compliant with powerful GDPR management tools.", "Sync with Google Drive, Dropbox, and more for seamless data flow.", "Screen clients against politically exposed persons (PEP) and sanctions lists.", "Get real-time email updates, including risk labels.", "Create secure sessions and manage workflows with API integration.", "Continuous monitoring for risk and compliance, ensuring you stay on top of every change.", "Fully integrated document signing for a seamless experience.", "Streamline administration with customizable, centralized policies.", "Enjoy a designated account manager and success team for ongoing support.", "Customize your platform with advanced branding options.", "Scale and adapt with flexible usage caps.", "Get tailored features for your specific sector." // Enterprise
            };

            var limitedTo = new List<string>
            {
                "5", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", // Starter
                "5", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", // Growth
                "5", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", // Business Pro
                "5", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", // Enterprise
            };

            var categories = new List<string>
            {
                JobFyFeatures.User.ToString(), JobFyFeatures.MonthlySearch.ToString(), JobFyFeatures.KYCMonthlyChecks.ToString(), JobFyFeatures.GuarantorMonthlyChecks.ToString(), JobFyFeatures.DocumentSigning.ToString(), JobFyFeatures.FilingAndStorage.ToString(), JobFyFeatures.DataRetention.ToString(), JobFyFeatures.AddressVerification.ToString(), JobFyFeatures.OnGroundVerification.ToString(), JobFyFeatures.GDPRComplianceTools.ToString(), JobFyFeatures.Integrations.ToString(), JobFyFeatures.PEPSanctionsChecks.ToString(), JobFyFeatures.VerificationUpdates.ToString(), JobFyFeatures.APISessionGeneration.ToString(), JobFyFeatures.OngoingMonitoring.ToString(), JobFyFeatures.EmbeddedSigning.ToString(), JobFyFeatures.CentralizedControls.ToString(), JobFyFeatures.DedicatedSupportTeam.ToString(), JobFyFeatures.AdvancedBranding.ToString(), JobFyFeatures.CustomUsageLimits.ToString(), JobFyFeatures.IndustrySpecificModules.ToString(),
                // Starter
               JobFyFeatures.User.ToString(), JobFyFeatures.MonthlySearch.ToString(), JobFyFeatures.KYCMonthlyChecks.ToString(), JobFyFeatures.GuarantorMonthlyChecks.ToString(), JobFyFeatures.DocumentSigning.ToString(), JobFyFeatures.FilingAndStorage.ToString(), JobFyFeatures.DataRetention.ToString(), JobFyFeatures.AddressVerification.ToString(), JobFyFeatures.OnGroundVerification.ToString(), JobFyFeatures.GDPRComplianceTools.ToString(), JobFyFeatures.Integrations.ToString(), JobFyFeatures.PEPSanctionsChecks.ToString(), JobFyFeatures.VerificationUpdates.ToString(), JobFyFeatures.APISessionGeneration.ToString(), JobFyFeatures.OngoingMonitoring.ToString(), JobFyFeatures.EmbeddedSigning.ToString(), JobFyFeatures.CentralizedControls.ToString(), JobFyFeatures.DedicatedSupportTeam.ToString(), JobFyFeatures.AdvancedBranding.ToString(), JobFyFeatures.CustomUsageLimits.ToString(), JobFyFeatures.IndustrySpecificModules.ToString(), // Growth
               JobFyFeatures.User.ToString(), JobFyFeatures.MonthlySearch.ToString(), JobFyFeatures.KYCMonthlyChecks.ToString(), JobFyFeatures.GuarantorMonthlyChecks.ToString(), JobFyFeatures.DocumentSigning.ToString(), JobFyFeatures.FilingAndStorage.ToString(), JobFyFeatures.DataRetention.ToString(), JobFyFeatures.AddressVerification.ToString(), JobFyFeatures.OnGroundVerification.ToString(), JobFyFeatures.GDPRComplianceTools.ToString(), JobFyFeatures.Integrations.ToString(), JobFyFeatures.PEPSanctionsChecks.ToString(), JobFyFeatures.VerificationUpdates.ToString(), JobFyFeatures.APISessionGeneration.ToString(), JobFyFeatures.OngoingMonitoring.ToString(), JobFyFeatures.EmbeddedSigning.ToString(), JobFyFeatures.CentralizedControls.ToString(), JobFyFeatures.DedicatedSupportTeam.ToString(), JobFyFeatures.AdvancedBranding.ToString(), JobFyFeatures.CustomUsageLimits.ToString(), JobFyFeatures.IndustrySpecificModules.ToString(), // Business Pro
               JobFyFeatures.User.ToString(), JobFyFeatures.MonthlySearch.ToString(), JobFyFeatures.KYCMonthlyChecks.ToString(), JobFyFeatures.GuarantorMonthlyChecks.ToString(), JobFyFeatures.DocumentSigning.ToString(), JobFyFeatures.FilingAndStorage.ToString(), JobFyFeatures.DataRetention.ToString(), JobFyFeatures.AddressVerification.ToString(), JobFyFeatures.OnGroundVerification.ToString(), JobFyFeatures.GDPRComplianceTools.ToString(), JobFyFeatures.Integrations.ToString(), JobFyFeatures.PEPSanctionsChecks.ToString(), JobFyFeatures.VerificationUpdates.ToString(), JobFyFeatures.APISessionGeneration.ToString(), JobFyFeatures.OngoingMonitoring.ToString(), JobFyFeatures.EmbeddedSigning.ToString(), JobFyFeatures.CentralizedControls.ToString(), JobFyFeatures.DedicatedSupportTeam.ToString(), JobFyFeatures.AdvancedBranding.ToString(), JobFyFeatures.CustomUsageLimits.ToString(), JobFyFeatures.IndustrySpecificModules.ToString(), // Enterprise
            };

            var isLimited = new List<bool>
            {
                true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, // Starter
                true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, // Growth
                true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, // Business Pro
                true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false // Enterprise
            };

            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansJobFy)
            {
                foreach (var feature in listOfGuidsForPackageFeaturesJobFy)
                {
                    pricingAndFeatures.Add(new PricingAndFeature
                    {
                        Id = listOfGuidsForPricingFeaturesJobFy[i],
                        PricingPlanId = plan,
                        FeatureId = feature,
                        Category = categories[i],
                        Summary = summaries[i],
                        Description = descriptions[i],
                        LimitedTo = limitedTo[i],
                        IsLimited = isLimited[i],
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow,
                    });

                    i++;
                }
            }

            foreach (var subdomain in subdoamins)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    var pricingAndFeatureInDb = await context.PricingAndFeatures.FirstOrDefaultAsync(x => x.PricingPlanId == pricingAndFeature.PricingPlanId && x.FeatureId == pricingAndFeature.FeatureId);
                    if (pricingAndFeatureInDb == null)
                    {
                        context.PricingAndFeatures.Add(pricingAndFeature);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Package Pricing - JobFy
        public static async Task SeedJobFyPackagePricing(List<string> subdomains)
        {
            // EUR
            var pricing = new List<double> { 75.0, 199.0, 350.0, 0.0 }; // Starter, Growth, Business Pro and Enterprise
            var pricePerMonthForYearlyOption = new List<double> { 70.0, 194.0, 345.0, 0.0 };
            var packagePricing = new List<PackagePricing>();

            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansJobFy)
            {
                packagePricing.Add(new PackagePricing
                {
                    Id = listOfGuidForPackagePricingJobFy[i],
                    PricingPlanId = plan,
                    Application = Applications.JobFy.ToString(),
                    Currency = Currency.EUR.ToString(),
                    PricePerMonth = pricing[i],
                    PricePerMonthForYearlyOption = pricePerMonthForYearlyOption[i],
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var package in packagePricing)
                {
                    var packageInDb = await context.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == package.PricingPlanId && x.Application == package.Application);
                    if (packageInDb == null)
                    {
                        context.PackagePricing.Add(package);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        // JobPays
        #region Seed Pricing Plans - JobPays
        private static async Task SeedJobPaysPricingPlans(List<string> subdomains)
        {
            // Get the display name of an Enum
            Type enumType = typeof(JobPaysPricingPlans);
            var pricingPlans = enumType.GetFields(BindingFlags.Public | BindingFlags.Static)
                .Where(field => field.IsLiteral)
                .Select(field => field.GetCustomAttribute<DisplayAttribute>()?.Name ?? field.Name);

            var plans = new List<PricingPlan>();

            var i = 0;
            foreach (var plan in pricingPlans)
            {
                plans.Add(new PricingPlan
                {
                    Id = listOfGuidsForPricingPlansJobPays[i],
                    Name = plan,
                    Application = Applications.JobPays
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var plan in plans)
                {
                    var pricingPlan = await context.PricingPlans
                        .FirstOrDefaultAsync(x => x.Name == plan.Name && x.Application == Applications.JobPays);
                    if (pricingPlan == null)
                    {
                        context.PricingPlans.Add(plan);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Package Pricing - JobPays
        public static async Task SeedJobPaysPackagePricing(List<string> subdomains)
        {
            // EUR
            var pricing = new List<double> { 500.0 }; // Starter, Growth, Business Pro and Enterprise
            var pricePerMonthForYearlyOption = new List<double>();
            var packagePricing = new List<PackagePricing>();
            int i = 0;
            foreach (var plan in listOfGuidsForPricingPlansJobPays)
            {
                packagePricing.Add(new PackagePricing
                {
                    Id = listOfGuidForPackagePricingJobPays[i],
                    PricingPlanId = plan,
                    Application = Applications.JobPays.ToString(),
                    Currency = Currency.EUR.ToString(),
                    PricePerMonth = pricing[i],
                    PricePerMonthForYearlyOption = null,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });

                i++;
            }

            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var package in packagePricing)
                {
                    var packageInDb = await context.PackagePricing.FirstOrDefaultAsync(x => x.PricingPlanId == package.PricingPlanId && x.Application == package.Application);
                    if (packageInDb == null)
                    {
                        context.PackagePricing.Add(package);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion

        // Delete Pricing and Features for all applications where descriptin and category is null or empty
        #region Delete Pricing and Features
        public static async Task DeletePricingAndFeatures(List<string> subdomains)
        {
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                var pricingAndFeatures = await context.PricingAndFeatures.Where(x => string.IsNullOrEmpty(x.Description) || string.IsNullOrEmpty(x.Category)).ToListAsync();
                foreach (var pricingAndFeature in pricingAndFeatures)
                {
                    context.PricingAndFeatures.Remove(pricingAndFeature);
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Teams - Luna and Wiki
        public static async Task SeedTeams(List<string> subdomains)
        {
            // List of teams to be added for LUNA and WIKI
            var teams = new List<Team>
            {
                new Team
                {
                    Name = "Luna",
                    CreatedBy = "System"
                },
                new Team
                {
                    Name = "Sophia",
                    CreatedBy = "System"
                }
            };            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var team in teams)
                {
                    var existingTeam = await context.Teams.FirstOrDefaultAsync(t => t.Name == team.Name);
                    if (existingTeam == null)
                    {
                        context.Teams.Add(team);
                    }
                }

                await context.SaveChangesAsync();
            }
        }
        #endregion

        #region Seed Phone Numbers
        public static async Task SeedPhoneNumbers(List<string> subdomains = null)
        {
            var phoneNumbers = new List<Jobid.App.AdminConsole.Models.Phone.PhoneNumber>
            {
                new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
                {
                    Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                    Number = "+1234567890",
                    IsActive = true,
                    IsRegistered = true,
                    Balance = 50.00m,
                    CountryCode = "US",
                    TwilioSid = "PN1234567890abcdef1234567890abcdef12",
                    FriendlyName = "Primary Business Line",
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-1)
                },
                new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
                {
                    Id = Guid.Parse("*************-2222-2222-************"),
                    Number = "+1987654321",
                    IsActive = true,
                    IsRegistered = true,
                    Balance = 75.50m,
                    CountryCode = "US",
                    TwilioSid = "PN0987654321fedcba0987654321fedcba09",
                    FriendlyName = "Customer Support Line",
                    CreatedAt = DateTime.UtcNow.AddDays(-25),
                    UpdatedAt = DateTime.UtcNow.AddDays(-2)
                },
                new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
                {
                    Id = Guid.Parse("*************-3333-3333-************"),
                    Number = "+***********",
                    IsActive = true,
                    IsRegistered = false,
                    Balance = 25.00m,
                    CountryCode = "GB",
                    TwilioSid = "PN***********12345678901234567890123",
                    FriendlyName = "UK Office Line",
                    CreatedAt = DateTime.UtcNow.AddDays(-20),
                    UpdatedAt = null
                },
                new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
                {
                    Id = Guid.Parse("*************-4444-4444-************"),
                    Number = "+***********",
                    IsActive = false,
                    IsRegistered = true,
                    Balance = 0.00m,
                    CountryCode = "AU",
                    TwilioSid = "PN***********98765432109876543210987",
                    FriendlyName = "Australia Branch",
                    CreatedAt = DateTime.UtcNow.AddDays(-15),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5)
                },
                new Jobid.App.AdminConsole.Models.Phone.PhoneNumber
                {
                    Id = Guid.Parse("*************-5555-5555-************"),
                    Number = "+***********",
                    IsActive = true,
                    IsRegistered = true,
                    Balance = 100.00m,
                    CountryCode = "US",
                    TwilioSid = "PN***********56789012345678901234567",
                    FriendlyName = "Sales Department",
                    CreatedAt = DateTime.UtcNow.AddDays(-10),
                    UpdatedAt = DateTime.UtcNow
                }
            };

            // Seed in tenant schemas
            foreach (var subdomain in subdomains)
            {
                var context = new JobProDbContext(new DbContextSchema(subdomain));
                foreach (var phoneNumber in phoneNumbers)
                {
                    var existingPhoneNumber = await context.PhoneNumbers.FirstOrDefaultAsync(p => p.Id == phoneNumber.Id);
                    if (existingPhoneNumber == null)
                    {
                        context.PhoneNumbers.Add(phoneNumber);
                    }
                    else
                    {
                        existingPhoneNumber.Number = phoneNumber.Number;
                        existingPhoneNumber.IsActive = phoneNumber.IsActive;
                        existingPhoneNumber.IsRegistered = phoneNumber.IsRegistered;
                        existingPhoneNumber.Balance = phoneNumber.Balance;
                        existingPhoneNumber.CountryCode = phoneNumber.CountryCode;
                        existingPhoneNumber.TwilioSid = phoneNumber.TwilioSid;
                        existingPhoneNumber.FriendlyName = phoneNumber.FriendlyName;
                        existingPhoneNumber.UpdatedAt = DateTime.UtcNow;

                        context.PhoneNumbers.Update(existingPhoneNumber);
                    }
                }
                await context.SaveChangesAsync();
            }
        }
        #endregion
    }
}
