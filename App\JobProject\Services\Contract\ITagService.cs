﻿using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ITagService
    {
        Task<ProjectTag> GetTag(Guid id);
        Task<ProjectTag> UpdateTag(UpdateTagVm updateTagVm, ProjectTag projectTag);
        Task<ProjectTag> DeleteTag(ProjectTag projectTag);
        Task<List<TagDto>> GetAllTags();
        Task<List<ProjectTag>> GetTagsByProjectId(string Id);
        Task<List<ProjectTag>> GetTagsByTodoId(string Id);
        Task<bool> AddTodoTag(List<ProjectTag> tags, string todoId);
        Task<bool> AddSprintTag(List<string> tags, string sprintId);
        Task<ProjectTag> AddTag(UpdateTagVm updateTagVm);
        Task<List<ProjectTag>> GetTagsByUserId(string userId);
    }
}
