﻿using System.Runtime.Serialization;
using System;

namespace Jobid.App.Helpers.Exceptions
{
    [Serializable]
    public class RecordAlreadyExistException : Exception
    {
        public RecordAlreadyExistException()
        {         
        }

        public RecordAlreadyExistException(string message) : base(message)
        {
        }

        public RecordAlreadyExistException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected RecordAlreadyExistException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
