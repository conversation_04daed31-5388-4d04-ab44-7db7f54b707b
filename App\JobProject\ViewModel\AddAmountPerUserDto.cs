﻿using Jobid.App.Helpers.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class AddAmountPerUserDto
    {
        [Required(ErrorMessage = "ProjectId cannot be null")]
        public string ProjectId { get; set; }
        [Required(ErrorMessage = "There must be at least one userId")]
        public List<string> UserIds { get; set; }
        [Required(ErrorMessage = "AmountPerHour cannot be null")]
        public decimal AmountPerHour { get; set; }
        [Required(ErrorMessage = "CurrencySymbol cannot be null")]
        public Currency CurrencySymbol { get; set; } = Currency.USD;
    }
}
