﻿using System;

namespace Jobid.App.Helpers.ViewModel
{
    public class LogModel
    {
        public long Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public object Request { get; set; } = string.Empty;
        public DateTime? RequestTime { get; set; }
        public string LogMode { get; set; } = string.Empty;
        public object Response { get; set; } = string.Empty;
        public DateTime? ResponseTime { get; set; }
        public string? Message { get; set; }
        public DateTime? CreatedOn { get; set; }
        public TimeSpan? ElapseTime { get; set; }
    }
}
