﻿using Jobid.App.Helpers.Enums;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class UploadDocumentDto
    {
        public string MeetingId { get; set; }
        public string SubsequentMeetingId { get; set; }
        public ReoccuringDeleteOptions? ReoccuringDeleteOptions { get; set; }
        public List<IFormFile> Files { get; set; } = new List<IFormFile>();
    }
}
