﻿using Jobid.App.ActivityLog.Model;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Contract
{
    public interface IAdminService
    {
        Task<GenericResponse> GetActivitiesWithFiltersForClientAdmin(AcitivityLogFiltersDto filters);
        Task<string> GetUserRole(string userId, string sudomains = null);
        Task<GenericResponse> LogAllUsersOut();

        // Subscription
        Task<List<string>> GetUserPermissions(string userId, string subdomain = null);
        Task<GenericResponse> GiveUserFullAccess(GiveUserFullAccessDto model);
        Task<GenericResponse> DowngradeUserToBasicAccess(DowngradeUserToBasicAccessDto model);
        Task<GenericResponse> RemoveUserFromCompany(string userId, string subdomain);

        //DashBoard
        Task<string> GetAdminCompanyLogo(string subdomain);
        Task<long> GetActiveEmployeeCount();
        Task<int> GetPendingInvitationsCount(string subdomain = null, Applications? app = null);
        Task<GenericResponse> GetPendingInvitations(string subdomain, Applications? app = null, int pageNumber = 1, int pageSize = 10);
        Task<int> GetSuspendedUsersCount();
        Task<GenericResponse> RevokeUserAccess(RevokeUserAccessDto model);
        Task<ApiResponse<IEnumerable<UserProfile>>> GetEmployeesByPackage(Applications application);
        //Task<ApiResponse<PagedResult<ApplicationUserDto>>> GetEmployeesByPackages(Applications application, int pageNumber, int pageSize, AccessTypeFilter accessType = AccessTypeFilter.All);
        Task<Page<DisplayUserDto>> GetSuspendedUsers(PaginationParameters parameters, string subdomain);
        Task<IEnumerable<PackagesDto>> GetAllPackages(string subdomain);
        Task<(IEnumerable<RecentActivitiesDto>, long)> GetAllRecentActivities(int pageNumber, int pageSize);
        Task<List<PackageMonthlyUsage>> GetPackageUsageByMonths(MostUsedPackagesFrequency frequency);
        Task<Dictionary<string, int>> GetActiveEmployeesByPackages();
        Task<Dictionary<string, int>> EmployeeDistributionByFrequency(EmployeeDistributionFrequency frequency);
        Task<PackageDashBoard> GetPackagesDashboard(Applications application, string subdomain);
        Task<GenericResponse> GetUserDistributionByLocation(EmployeeDistributionFrequency frequency, Applications application = Applications.Joble);
        Task<GenericResponse> GetUsedAndRemainingLicenceCount(string tenantId, Applications app);

        //Users
        Task<Page<DisplayUserDto>> GetAndSearchAllUsers(string subdomain, int pageSize, int pageNumber, string keyword, Applications? app = null, UserStatus? status = null);
        Task<Page<DisplayUserDto>> GetAllDeactivatedEmployees(int pageSize, int pageNumber, string subdomain, string keyword);
        Task<EmployeeInformation> ViewUserById(string userId, string subdomain);
        Task<bool> DeleteUser(string userId);
        Task<bool> ChangeRole(string userId, string role, string appName);
        Task<bool> SuspendEmployee(SuspendUser suspendDto);
        Task<bool> UnSuspendEmployee(string userId);
        Task<List<RolesDto>> GetEmployeeRoles(Applications appName);
        Task<List<RolesUsers>> GetRoleUsers(string role);
        Task<List<PermissionDto>> GetEmployeeRolesAndPermission(string roleId);
        Task<bool> UpdateRole(UpdateRoleDto permissionDto);
        Task DeleteRole(List<string> roleIds);
        Task<bool> CreateRoles(string roleName, string appName);
        Task<bool> CreatePermissions(List<CreatePermission> permission);
        Task<Dictionary<string, bool>> GetPackagesWithUserPermissions(string userId);
        Task<bool> RemoveUserAppPermission(string userId, Applications appPermission);

        //Settings
        Task<bool> UpdateAdminCompanyProfile(string subdomain, UpdateAdminProfileDto profileDto);
        Task<bool> UpdateAdminCompanyLogo(string subdomain, UpdateCompanyLogoDto model);
        Task<ApiResponse<bool>> AssignUserToRole(string userId, string roleId);
        Task<ApiResponse<bool>> AssignPermissionsToRole(string roleId, List<string> permissionIds);
        Task<Page<Activity>> GetActivityLogs(int pageNumber, int pageSize, DateTime sDate, DateTime eDate);
        Task<GenericResponse> UpdateEmployee(EmployeeUpdate updateDto);

        // Company Deletion
        Task<GenericResponse> RequestForCompanyDeletion(RequestForCompanyDeletionDto model);
        Task<GenericResponse> CancelCompanyDeletion(string requestId);
        Task<GenericResponse> GetCompanyDeletionRequests(string tenantId);

        // Localization
        Task<GenericResponse> AddOrUpdateLocalization(AddOrUpdateLocalizationDto model);
        Task<GenericResponse> GetLocalization(string userId = null);

        // Password Policy
        Task<GenericResponse> AddOrUpdatePasswordPolicy(PasswordPolicyDto model);
        Task<GenericResponse> GetPasswordPolicy();

        // Two Factor Authentication
        Task<GenericResponse> AddOrUpdateTwoFactorSettings(TwoFactorSettingsDto model);
        Task<GenericResponse> GetTwoFactorSettings();

        // Billing Information
        Task<GenericResponse> AddBillingInformation(AddBillingInformationDto model, string subdomain, string userId);
        Task<GenericResponse> UpdateBillingInformation(UpdateBillingInformationDto model, string subdomain, string userId);
        Task<GenericResponse> GetBillingInformation(string subdomain);
    }
}
