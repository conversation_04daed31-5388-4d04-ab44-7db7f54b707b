﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class CreateIndividualDto
    {
        [Required]
        public Guid UserId { get; set; }
        [Required]
        public Guid DepartmentId { get; set; }
        [Required]
        public Guid PositionId { get; set; }
        [Required]
        public string EmailAddress { get; set; }
        public long BelongsTo { get; set; }  // Superior's index, 0 if no superior
        [Required]
        public string Name { get; set; }
        [Required]
        public string Title { get; set; }
        public string BranchColor { get; set; }
        public bool IsHeadOfDepartment { get; set; }
    }
}
