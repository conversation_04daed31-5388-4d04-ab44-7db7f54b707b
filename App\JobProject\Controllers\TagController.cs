﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    public class TagController : BaseController
    {
        public TagController(IUnitofwork unitofwork)
        {
            this.Services_Repo = unitofwork;
        }

        public IUnitofwork Services_Repo { get; }

        [HttpPatch]
        [Route("{tagId}")]
        public async Task<ActionResult> UpdateSprint(UpdateTagVm updateTagVm, string tagId)
        {
            var newId = new Guid(tagId);
            var tag = await Services_Repo.TagService.GetTag(newId);

            if (tag == null) { return BadRequest(new { Msg = " Sprint is not found !" }); }
            var result = await Services_Repo.TagService.UpdateTag(updateTagVm, tag);
            if (result != null)
            {
                return Ok(new { Msg = "Tag Updated  Added Successfully" });
            }
            else { return BadRequest(); }
        }


        [HttpPost]
        public async Task<ActionResult> AddTag(UpdateTagVm updateTagVm)
        {
            var result = await Services_Repo.TagService.AddTag(updateTagVm);
            if (result != null)
            {
                return Ok(new { Msg = "Tag Added Successfully" });
            }
            else { return BadRequest(); }
        }


        [HttpDelete]
        [Route("tagId")]
        public async Task<ActionResult> DeleteTag(string tagId)
        {
            var newId = new Guid(tagId);
            var tag = await Services_Repo.TagService.GetTag(newId);

            if (tag == null) { return BadRequest(new { Msg = " Sprint is not found !" }); }

            var result = await Services_Repo.TagService.DeleteTag(tag);
            if (result != null)
            {
                return Ok(new { Msg = "ProjectMgmt_Project Added Successfully" });
            }
            else
            {
                return BadRequest();
            }
        }

        [HttpGet]
        [Route("tagId")]
        public async Task<ActionResult> GetTagById(string tagId)
        {

            try
            {
                var id = new Guid(tagId);
                var tag = await Services_Repo.TagService.GetTag(id);
                if (tag != null)
                {
                    return Ok(new ApiResponse<ProjectTag> { ResponseCode = "200", ResponseMessage = "Tags Retreived ", Data = tag });
                }
                else { return BadRequest(new { Msg = " Tag not found !" }); }
            }
            catch (Exception Ex)
            {
                return BadRequest(Ex.Message);
            }
        }

        [HttpGet]
        [Route("getalltags")]
        public async Task<ActionResult> GetAllTags()
        {
            var tag = await Services_Repo.TagService.GetAllTags();
            if (tag == null) { return BadRequest(new { Msg = " Sprint is not found !" }); }

            return Ok(new ApiResponse<List<TagDto>> { ResponseCode = "200", ResponseMessage = "Tags Retreived ", Data = tag });
        }

        [HttpGet]
        [Route("tags/getbyuserid/userId")]
        public async Task<ActionResult> GetTagsByUserId(string userId)
        {
            try
            {
                var tag = await Services_Repo.TagService.GetTagsByUserId(userId);
                if (tag == null) { return BadRequest(new { Msg = " Tags not found !" }); }

                return Ok(new ApiResponse<List<ProjectTag>> { ResponseCode = "200", ResponseMessage = "Tags Retreived ", Data = tag });
            }

            catch (Exception Ex)
            {

                return BadRequest(Ex.Message);
            }

        }


        [HttpPost("sprinttag")]
        public async Task<ActionResult> AddSprintTag(AddSprintTag AddSprintTag)
        {
            var newId = new Guid(AddSprintTag.SprintId);
            var tag = await Services_Repo.TagService.GetTag(newId);

            if (tag == null) { return BadRequest(new { Msg = " Sprint is not found !" }); }

            var result = await Services_Repo.TagService.AddSprintTag(AddSprintTag.SprinttagIds, AddSprintTag.SprintId);
            if (result)
            {
                return Ok(new { Msg = "Tags Added Successfully" });
            }
            else { return BadRequest(); }
        }

        [HttpPost("Todotag")]
        public async Task<ActionResult> AddTodoTag(AddTodoTag addTodoTag)
        {
            var newId = new Guid(addTodoTag.TodoId);
            var tag = await Services_Repo.TagService.GetTag(newId);
            if (tag == null) { return BadRequest(new { Msg = " Sprint is not found !" }); }

            var result = await Services_Repo.TagService.AddSprintTag(addTodoTag.TodotagIds, addTodoTag.TodoId);
            if (result)
            {
                return Ok(new { Msg = "Tags Added Successfully" });
            }
            else { return BadRequest(); }
        }
    }
}
