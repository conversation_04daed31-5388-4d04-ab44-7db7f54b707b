﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class MeetingNote : BaseModel
    {
        [Key]
        public Guid Id { get; set; }
        public Guid MeetingId { get; set; }
        public Guid? SubsequentMeetingId { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        public string Notes { get; set; }

        // Navigation properties
        public CalenderMeeting Meeting { get; set; }
        public SubsequentMeeting SubsequentMeeting { get; set; } = null;
    }
}
