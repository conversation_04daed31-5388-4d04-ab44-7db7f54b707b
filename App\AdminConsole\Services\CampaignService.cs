using Microsoft.EntityFrameworkCore;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;

namespace Jobid.App.AdminConsole.Services
{
    /// <summary>
    /// Service for Campaign operations
    /// </summary>
    public class CampaignService : ICampaignService
    {
        private readonly JobProDbContext _context;

        public CampaignService(JobProDbContext context)
        {
            _context = context;
        }

        #region Create Campaign
        /// <summary>
        /// Create a new campaign
        /// </summary>
        public async Task<ApiResponse<CampaignDto>> CreateCampaignAsync(CreateCampaignDto campaignDto)
        {
            // Validate that the group exists and belongs to the user
            var group = await _context.Groups
                .FirstOrDefaultAsync(g => g.Id == campaignDto.GroupId && g.UserId == campaignDto.UserId && !g.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<CampaignDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found or access denied"
                };
            }

            var campaign = new Campaign
            {
                GroupId = campaignDto.GroupId,
                PhoneNumber = campaignDto.PhoneNumber,
                ExpectedOutcome = campaignDto.ExpectedOutcome,
                ExternalMeetingId = campaignDto.ExternalMeetingId,
                UserId = campaignDto.UserId,
                CreatedBy = campaignDto.UserId,
                UpdatedBy = campaignDto.UserId
            };

            _context.Campaigns.Add(campaign);
            await _context.SaveChangesAsync();

            var result = await MapToCampaignDto(campaign);

            return new ApiResponse<CampaignDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Campaign created successfully"
            };
        }
        #endregion

        #region Update Campaign
        /// <summary>
        /// Update an existing campaign
        /// </summary>
        public async Task<ApiResponse<CampaignDto>> UpdateCampaignAsync(UpdateCampaignDto campaignDto)
        {
            var campaign = await _context.Campaigns
                .FirstOrDefaultAsync(c => c.Id == campaignDto.Id && c.UserId == campaignDto.UserId && !c.IsDeleted);

            if (campaign == null)
            {
                return new ApiResponse<CampaignDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Campaign not found"
                };
            }

            // Validate that the new group exists and belongs to the user
            var group = await _context.Groups
                .FirstOrDefaultAsync(g => g.Id == campaignDto.GroupId && g.UserId == campaignDto.UserId && !g.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<CampaignDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found or access denied"
                };
            }

            campaign.GroupId = campaignDto.GroupId;
            campaign.PhoneNumber = campaignDto.PhoneNumber;
            campaign.ExpectedOutcome = campaignDto.ExpectedOutcome;
            campaign.Status = campaignDto.Status;
            campaign.ExternalMeetingId = campaignDto.ExternalMeetingId;
            campaign.UpdatedBy = campaignDto.UserId;
            campaign.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = await MapToCampaignDto(campaign);

            return new ApiResponse<CampaignDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Campaign updated successfully"
            };
        }
        #endregion

        #region Delete Campaign
        /// <summary>
        /// Delete a campaign
        /// </summary>
        public async Task<ApiResponse<bool>> DeleteCampaignAsync(Guid campaignId, string userId)
        {
            var campaign = await _context.Campaigns
                .FirstOrDefaultAsync(c => c.Id == campaignId && c.UserId == userId && !c.IsDeleted);

            if (campaign == null)
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = "Campaign not found"
                };
            }

            campaign.IsDeleted = true;
            campaign.UpdatedBy = userId;
            campaign.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return new ApiResponse<bool>
            {
                Data = true,
                ResponseCode = "200",
                ResponseMessage = "Campaign deleted successfully"
            };
        }
        #endregion

        #region Get Campaign by ID
        /// <summary>
        /// Get campaign by ID
        /// </summary>
        public async Task<ApiResponse<CampaignDto>> GetCampaignByIdAsync(Guid campaignId)
        {
            var campaign = await _context.Campaigns
                .Include(c => c.Group)
                .FirstOrDefaultAsync(c => c.Id == campaignId && !c.IsDeleted);

            if (campaign == null)
            {
                return new ApiResponse<CampaignDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Campaign not found"
                };
            }

            var result = await MapToCampaignDto(campaign);

            return new ApiResponse<CampaignDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Campaign retrieved successfully"
            };
        }
        #endregion
        
        #region Get User Campaigns
        /// <summary>
        /// Get campaigns for a specific user with pagination
        /// </summary>
        public async Task<ApiResponse<PaginatedCampaignsDto>> GetUserCampaignsAsync(string userId, int pageNumber, int pageSize, CampaignStatus? status = null)
        {
            var query = _context.Campaigns.Where(c => c.UserId == userId && !c.IsDeleted);
            
            // Apply status filter if provided
            if (status.HasValue)
            {
                query = query.Where(c => c.Status == status.Value);
            }

            var totalCount = await query.CountAsync();

            var campaigns = await query
                .Include(c => c.Group)
                .OrderByDescending(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var campaignDtos = new List<CampaignDto>();
            foreach (var campaign in campaigns)
            {
                campaignDtos.Add(await MapToCampaignDto(campaign));
            }

            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var result = new PaginatedCampaignsDto
            {
                Campaigns = campaignDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = totalPages            };
            
            var statusMessage = status.HasValue ? $" with status {status.Value}" : "";
            return new ApiResponse<PaginatedCampaignsDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{campaigns.Count} campaigns{statusMessage} retrieved successfully"
            };
        }
        #endregion

        #region Get All Campaigns
        /// <summary>
        /// Get all campaigns with pagination
        /// </summary>
        public async Task<ApiResponse<PaginatedCampaignsDto>> GetAllCampaignsAsync(int pageNumber, int pageSize)
        {
            var totalCount = await _context.Campaigns
                .CountAsync(c => !c.IsDeleted);

            var campaigns = await _context.Campaigns
                .Where(c => !c.IsDeleted)
                .Include(c => c.Group)
                .OrderByDescending(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var campaignDtos = new List<CampaignDto>();
            foreach (var campaign in campaigns)
            {
                campaignDtos.Add(await MapToCampaignDto(campaign));
            }

            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var result = new PaginatedCampaignsDto
            {
                Campaigns = campaignDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = totalPages
            };

            return new ApiResponse<PaginatedCampaignsDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{campaigns.Count} campaigns retrieved successfully"
            };
        }
        #endregion

        #region Update Campaign Status
        /// <summary>
        /// Update campaign status
        /// </summary>
        public async Task<ApiResponse<CampaignDto>> UpdateCampaignStatusAsync(UpdateCampaignStatusDto statusDto)
        {
            var campaign = await _context.Campaigns
                .FirstOrDefaultAsync(c => c.Id == statusDto.CampaignId && c.UserId == statusDto.UserId && !c.IsDeleted);

            if (campaign == null)
            {
                return new ApiResponse<CampaignDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Campaign not found"
                };
            }

            campaign.Status = statusDto.Status;
            campaign.UpdatedBy = statusDto.UserId;
            campaign.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = await MapToCampaignDto(campaign);

            return new ApiResponse<CampaignDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Campaign status updated successfully"
            };
        }
        #endregion

        #region Get Campaign Stats
        /// <summary>
        /// Get campaign statistics for a user
        /// </summary>
        public async Task<ApiResponse<CampaignStatsDto>> GetCampaignStatsAsync(string userId)
        {
            var totalCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted);

            var activeCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == CampaignStatus.Active);

            var inactiveCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == CampaignStatus.Inactive);

            var completedCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == CampaignStatus.Completed);

            var cancelledCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == CampaignStatus.Cancelled);

            var bookInPersonMeetingCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.ExpectedOutcome == CampaignExpectedOutcome.BookInPersonMeeting);

            var bookOnlineMeetingCampaigns = await _context.Campaigns
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.ExpectedOutcome == CampaignExpectedOutcome.BookOnlineMeeting);

            var stats = new CampaignStatsDto
            {
                TotalCampaigns = totalCampaigns,
                ActiveCampaigns = activeCampaigns,
                InactiveCampaigns = inactiveCampaigns,
                CompletedCampaigns = completedCampaigns,
                CancelledCampaigns = cancelledCampaigns,
                BookInPersonMeetingCampaigns = bookInPersonMeetingCampaigns,
                BookOnlineMeetingCampaigns = bookOnlineMeetingCampaigns
            };

            return new ApiResponse<CampaignStatsDto>
            {
                Data = stats,
                ResponseCode = "200",
                ResponseMessage = "Campaign statistics retrieved successfully"
            };
        }
        #endregion

        #region Private Helper Methods
        /// <summary>
        /// Map Campaign entity to CampaignDto
        /// </summary>
        private async Task<CampaignDto> MapToCampaignDto(Campaign campaign)
        {
            ContactGroupDto groupDto = null;

            // If group is not loaded, load it
            if (campaign.Group == null)
            {
                var group = await _context.Groups
                    .FirstOrDefaultAsync(g => g.Id == campaign.GroupId);
                
                if (group != null)
                {
                    // Get group contacts for the group
                    var groupContacts = await _context.GroupContacts
                        .Where(gc => gc.GroupId == group.Id)
                        .Include(gc => gc.Contact)
                        .ToListAsync();

                    var contactIds = groupContacts.Select(gc => gc.ContactId).ToList();
                    var contacts = groupContacts.Select(gc => MapToContactDto(gc.Contact)).ToList();

                    groupDto = new ContactGroupDto
                    {
                        Id = group.Id,
                        Name = group.Name,
                        SalePitch = group.SalePitch,
                        ContactIds = contactIds,
                        Contacts = contacts,
                        Status = group.Status,
                        UserId = group.UserId,
                        CreatedAt = group.CreatedAt,
                        UpdatedAt = group.UpdatedAt,
                        CreatedBy = group.CreatedBy,
                        UpdatedBy = group.UpdatedBy
                    };
                }
            }
            else
            {
                // Group is already loaded
                var groupContacts = await _context.GroupContacts
                    .Where(gc => gc.GroupId == campaign.Group.Id)
                    .Include(gc => gc.Contact)
                    .ToListAsync();

                var contactIds = groupContacts.Select(gc => gc.ContactId).ToList();
                var contacts = groupContacts.Select(gc => MapToContactDto(gc.Contact)).ToList();

                groupDto = new ContactGroupDto
                {
                    Id = campaign.Group.Id,
                    Name = campaign.Group.Name,
                    SalePitch = campaign.Group.SalePitch,
                    ContactIds = contactIds,
                    Contacts = contacts,
                    Status = campaign.Group.Status,
                    UserId = campaign.Group.UserId,
                    CreatedAt = campaign.Group.CreatedAt,
                    UpdatedAt = campaign.Group.UpdatedAt,
                    CreatedBy = campaign.Group.CreatedBy,
                    UpdatedBy = campaign.Group.UpdatedBy
                };
            }

            return new CampaignDto
            {
                Id = campaign.Id,
                GroupId = campaign.GroupId,
                PhoneNumber = campaign.PhoneNumber,
                ExpectedOutcome = campaign.ExpectedOutcome,
                Status = campaign.Status,
                UserId = campaign.UserId,
                ExternalMeetingId = campaign.ExternalMeetingId,
                Group = groupDto,
                CreatedAt = campaign.CreatedAt,
                UpdatedAt = campaign.UpdatedAt,
                CreatedBy = campaign.CreatedBy,
                UpdatedBy = campaign.UpdatedBy
            };
        }

        /// <summary>
        /// Map UserContact entity to UserContactDto
        /// </summary>
        private UserContactDto MapToContactDto(UserContact contact)
        {
            return new UserContactDto
            {
                Id = contact.Id,
                Name = contact.Name,
                Email = contact.Email,
                PhoneNumber = contact.PhoneNumber,
                Industry = contact.Industry,
                UserId = contact.UserId,
                CreatedAt = contact.CreatedAt,
                UpdatedAt = contact.UpdatedAt,
                CreatedBy = contact.CreatedBy,
                UpdatedBy = contact.UpdatedBy
            };
        }
        #endregion
    }
}
