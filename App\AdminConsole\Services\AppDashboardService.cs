﻿using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers.Context;

namespace Jobid.App.AdminConsole.Services
{
    public class AppDashboardService : IAppDashboardService
    {
        private JobProDbContext Db;
        private JobProDbContext subdomainSchemaContext;
        public AppDashboardService(JobProDbContext subdomainSchemaContext, JobProDbContext publicSchemaContext)
        {
            Db = publicSchemaContext;
            this.subdomainSchemaContext = subdomainSchemaContext;
        }
    }
}
