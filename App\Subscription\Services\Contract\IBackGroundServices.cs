﻿using Jobid.App.Helpers.Enums;
using System;
using System.Threading.Tasks;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Services.Contract
{
    public interface IBackGroundServices
    {
        Task UpdateSubscriptionStatus(SubscriptionStatus status, string userId, string subscriptionId, string subdomain = null, string tenantId = null, string app = null, string entPaymentDetailsId = null);
        Task UpdateSubscriptionStatusAfter30Or14daysOr1Year(string subscriptionId, string subdomian = null, int? count = null);
        Task RetrySubscriptionPayment(Guid subscriptionId, string subdomain = null);
        Task UpdateSubscriptionStatusAfter14Days(SubscriptionStatus status, string userId, Applications app, string tenantId = null, string subdomain = null);
        Task UpdateFreeTrialToFalse(string subscriptionId);
    }
}
