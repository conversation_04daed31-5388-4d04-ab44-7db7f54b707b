﻿using Hangfire;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.Subscription.Services.Contract;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Mollie.Api.Client.Abstract;
using Mollie.Api.Models.Subscription;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.Services.Implementations
{
    /// <summary>
    /// 
    /// </summary>
    public class BackGroundServices : IBackGroundServices
    {
        /// <summary>
        /// Provate readonly properties
        /// </summary>
        private readonly JobProDbContext _publicContext;
        private readonly ISubscriptionClient _subscriptionClient;
        private readonly IConfiguration _config;
        private readonly string _conString;
        private ILogger _logger = Log.ForContext<BackGroundServices>();

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="publicContext"></param>
        /// <param name="subscriptionClient"></param>
        /// <param name="config"></param>
        public BackGroundServices(JobProDbContext publicContext, ISubscriptionClient subscriptionClient, IConfiguration config)
        {
            _publicContext = publicContext;
            _subscriptionClient = subscriptionClient;
            _config = config;
            _conString = GlobalVariables.ConnectionString;
        }

        #region Update free trail to false
        /// <summary>
        /// Update free trial to false
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("subscription")]
        public async Task UpdateFreeTrialToFalse(string subscriptionId)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(sub => sub.Id.ToString() == subscriptionId);
            if (subscription.ActivatedOn == null && subscription.Status == PaymentStatus.Successful.ToString())
            {
                subscription.FreeTrialOptionSelected = false;
                _publicContext.SaveChanges();
            }
        }
        #endregion

        #region Retry Subscription Payment
        /// <summary>
        /// This method retries a failed subscription payment
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("subscription")]
        public async Task RetrySubscriptionPayment(Guid subscriptionId, string subdomain = null)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id == subscriptionId);
            var count = 0;
            if (subscription.SubscriptionId is not null && subscription.Status != PaymentStatus.Successful.ToString())
            {
                SubscriptionUpdateRequest subscriptionUpdateRequest = new SubscriptionUpdateRequest()
                {
                    StartDate = DateTime.UtcNow,
                };

                SubscriptionResponse subscriptionResponse = await _subscriptionClient.UpdateSubscriptionAsync(subscription.MollieCustomerId, subscription.SubscriptionId, subscriptionUpdateRequest);

                // Update attempt count
                count = subscription.RetrySubAttempt;
                if (count == 2)
                    subscription.RetrySubAttempt = 0;
                _publicContext.Subscriptions.Update(subscription);
                await _publicContext.SaveChangesAsync();
            }

            if (count == 2)
            {
                await UpdateSubscriptionStatusAfter30Or14daysOr1Year(subscription.Id.ToString(), subdomain, count);

                // Remove the reoccuring job after after the 3rd retry
                RecurringJob.RemoveIfExists(subscription.Id.ToString());
            }
        }
        #endregion

        #region Update Subscription Status
        /// <summary>
        /// This method updates the user's/company's subscription status to inactive
        /// </summary>
        /// <param name="status"></param>
        /// <param name="userId"></param>
        /// <param name="tenantId"></param>
        /// <param name="subdomain"></param>
        /// <param name="subscriptionId"></param>
        /// <param name="app"></param>
        /// <param name="entPaymentDetailsId"></param>
        /// <returns></returns>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("subscription")]
        public async Task UpdateSubscriptionStatus(Enums.Enums.SubscriptionStatus status, string userId, string subscriptionId, string subdomain = null, string tenantId = null, string app = null, string entPaymentDetailsId = null)
        {
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id.ToString() == subscriptionId);
            if (tenantId is not null)
            {
                Guid tenantGuid = Guid.Parse(tenantId);
                var appEnum = (Applications)Enum.Parse(typeof(Applications), app, true);
                if (!subscription.IsAISubscription)
                {
                    var companySub = await _publicContext.CompanySubscriptions.FirstOrDefaultAsync(x => x.TenantId == tenantGuid && x.Application == appEnum);
                    if (companySub is null)
                    {
                        _logger.Information($"UpdateSubscriptionStatus:Compamny subscription is null for {tenantGuid}");
                        throw new Exception($"Compamny subscription is null for {tenantGuid}");
                    }

                    companySub.Status = status;
                    companySub.UpdatedAt = DateTime.UtcNow;
                    _publicContext.CompanySubscriptions.Update(companySub);

                    var permissions = await context.AppPermissions.Where(x => x.TenantId == tenantId && x.Application == app && x.IsEnabled == true).ToListAsync();
                    if (status == Enums.Enums.SubscriptionStatus.Active)
                    {
                        var count = 1;
                        foreach (var permission in permissions)
                        {
                            if (count > subscription.SubscriptionFor)
                                break;

                            permission.SubscriptionStatus = status.ToString();
                            permission.UpdatedAt = DateTime.UtcNow;

                            count++;
                        }

                        context.AppPermissions.UpdateRange(permissions);
                    }
                    else
                    {
                        foreach (var permission in permissions)
                        {
                            permission.SubscriptionStatus = status.ToString();
                            permission.UpdatedAt = DateTime.UtcNow;
                        }

                        context.AppPermissions.UpdateRange(permissions);
                    }
                }
                else
                {
                    var aiSubDetails = await _publicContext.AISubscriptionDetails
                        .Where(x => x.SubscriptionId.ToString() == subscriptionId).ToListAsync();
                    if (aiSubDetails.Count < 1)
                        throw new Exception("AI Subscription details not found");

                    var companySubs = await _publicContext.CompanySubscriptions
                        .Where(x => x.TenantId == tenantGuid && x.AIAgent != null).ToListAsync();

                    if (aiSubDetails.Count > 0)
                    {
                        foreach (var aiSubDetail in aiSubDetails)
                        {
                            var companySub = companySubs.FirstOrDefault(x => x.AIAgent == aiSubDetail.Agent);
                            if (companySub is null)
                            {
                                _logger.Information($"UpdateSubscriptionStatus:Compamny subscription is null for {tenantGuid}");
                                throw new Exception($"Compamny subscription is null for {tenantGuid}");
                            }

                            companySub.Status = status;
                            companySub.UpdatedAt = DateTime.UtcNow;
                            _publicContext.CompanySubscriptions.Update(companySub);
                        }

                        foreach (var aiSub in aiSubDetails)
                        {
                            var permissions = await context.AppPermissions
                                .Where(x => x.TenantId == subscription.TenantId.ToString() && x.Application == subscription.Application.ToString() && x.IsEnabled == true && x.Agent == aiSub.Agent).ToListAsync();

                            if (permissions.Any())
                            {
                                if (status == Enums.Enums.SubscriptionStatus.Active)
                                {
                                    int count = 1;
                                    foreach (var permission in permissions)
                                    {
                                        if (count > aiSub.NoOfUserSubscribedFor)
                                        {
                                            continue;
                                        }

                                        permission.SubscriptionStatus = status.ToString();
                                        context.AppPermissions.Update(permission);

                                        count++;
                                    }
                                }
                                else
                                {
                                    foreach (var permission in permissions)
                                    {
                                        permission.SubscriptionStatus = status.ToString();
                                        permission.UpdatedAt = DateTime.UtcNow;
                                    }

                                    context.AppPermissions.UpdateRange(permissions);
                                }
                            }
                        }
                    }
                }

                // Update enterprise payment as used
                if (entPaymentDetailsId is not null && status == Enums.Enums.SubscriptionStatus.Active)
                {
                    var entPaymentDetails = await _publicContext.EnterprizeSubscriptionPayments.FirstOrDefaultAsync(x => x.Id.ToString() == entPaymentDetailsId);
                    entPaymentDetails.PaymentUsed = true;
                    _publicContext.EnterprizeSubscriptionPayments.Update(entPaymentDetails);
                }
            }
            else
            {
                if (!subscription.IsAISubscription && !string.IsNullOrEmpty(userId))
                {
                    var permission = await _publicContext.AppPermissions.FirstOrDefaultAsync(x => x.UserId == userId && x.Application == app && x.IsEnabled == true);
                    permission.SubscriptionStatus = status.ToString();
                    _publicContext.AppPermissions.Update(permission);
                }
            }
            await Task.WhenAll(
                 context.SaveChangesAsync(),
                _publicContext.SaveChangesAsync()
            );
        }
        #endregion

        #region Update Subscription Status after 14 days
        /// <summary>
        /// This method updates the user's/company's subscription status to inactive
        /// </summary>
        /// <param name="status"></param>
        /// <param name="userId"></param>
        /// <param name="tenantId"></param>
        /// <param name="app"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("subscription")]
        public async Task UpdateSubscriptionStatusAfter14Days(Enums.Enums.SubscriptionStatus status, string userId, Applications app, string tenantId = null, string subdomain = null)
        {
            if (tenantId is not null)
            {
                var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == app);
                var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);

                if (plan.Name == "Free")
                {
                    await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
                    var companySub = await _publicContext.CompanySubscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application.ToString() == app.ToString());
                    var permissions = await context.AppPermissions.Where(x => x.TenantId == tenantId && x.Application == app.ToString() && x.IsEnabled == true).ToListAsync();
                    companySub.Status = status;
                    _publicContext.CompanySubscriptions.Update(companySub);

                    foreach (var permission in permissions)
                        permission.SubscriptionStatus = status.ToString();

                    context.AppPermissions.UpdateRange(permissions);
                    await context.SaveChangesAsync();
                }
            }
            else
            {
                var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.UserId.ToString() == userId && x.Application == app);
                var plan = await _publicContext.PricingPlans.FirstOrDefaultAsync(x => x.Id == subscription.PricingPlanId);

                if (plan.Name == "Free")
                {
                    var permission = await _publicContext.AppPermissions.FirstOrDefaultAsync(x => x.UserId.ToString() == userId && x.Application == app.ToString() && x.IsEnabled == true);
                    permission.SubscriptionStatus = status.ToString();
                    _publicContext.AppPermissions.Update(permission);
                }
            }

            await _publicContext.SaveChangesAsync();
        }
        #endregion

        #region Update Subscription Status after 30 days
        /// <summary>
        /// This metthods updates the user's/company's subscription status to inactive after 30 days counting from the last sub activation date
        /// </summary>
        /// <param name="subscriptionId"></param>
        /// <param name="subdomain"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("subscription")]
        public async Task UpdateSubscriptionStatusAfter30Or14daysOr1Year(string subscriptionId, string subdomain = null, int? count = null)
        {
            var subscription = await _publicContext.Subscriptions.FirstOrDefaultAsync(x => x.Id.ToString() == subscriptionId);
            if (subscription is not null)
            {
                if (count != null && count == 2)
                {
                    // If the subscription has tenantId, the update the company's subscription status otherwsie update user's subscription status
                    if (subscription.TenantId is not null)
                        await UpdateSubscriptionStatus(Enums.Enums.SubscriptionStatus.Inactive, subscription.UserId, subscriptionId, subdomain, subscription.TenantId.ToString(), subscription.Application.ToString());
                    else
                        await UpdateSubscriptionStatus(Enums.Enums.SubscriptionStatus.Inactive, subscription.UserId, subscriptionId, null, null, subscription.Application.ToString());
                }
                else
                {
                    // Trigger a re-ocurring backgroud job that will run every 24hours
                    RecurringJob.AddOrUpdate<IBackGroundServices>(subscription.Id.ToString(), x => x.RetrySubscriptionPayment(subscription.Id, subdomain), Cron.Daily);
                    RecurringJob.TriggerJob(subscription.Id.ToString());
                }
            }
        }
        #endregion      
    }
}
