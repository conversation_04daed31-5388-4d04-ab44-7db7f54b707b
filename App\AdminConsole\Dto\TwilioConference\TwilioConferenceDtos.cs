using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Dto.TwilioConference
{
    /// <summary>
    /// Request to create a new Twilio conference
    /// </summary>
    public class CreateConferenceDto
    {
        [Required]
        public string ConferenceName { get; set; }
        
        public int MaxParticipants { get; set; } = 10;
        
        public bool EnableRecording { get; set; } = false;
    }

    /// <summary>
    /// Response after creating a conference
    /// </summary>
    public class CreateConferenceResponseDto
    {
        public string ConferenceSid { get; set; }
        public string ConferenceName { get; set; }
        public DateTime CreatedAt { get; set; }
        public string WebAccessToken { get; set; }
        public int MaxParticipants { get; set; }
    }

    /// <summary>
    /// Request to add PSTN participant to conference
    /// </summary>
    public class AddPstnParticipantDto
    {
        [Required]
        public string ConferenceName { get; set; }
        
        [Required]
        public string PhoneNumber { get; set; }
        
        [Required]
        public string FromNumber { get; set; }
    }

    /// <summary>
    /// Request to add web participant to conference
    /// </summary>
    public class AddWebParticipantDto
    {
        [Required]
        public string ConferenceName { get; set; }
        
        [Required]
        public string Identity { get; set; }
        
        public string DisplayName { get; set; }
    }

    /// <summary>
    /// Response after adding participant
    /// </summary>
    public class AddParticipantResponseDto
    {
        public string ParticipantId { get; set; }
        public string ConferenceName { get; set; }
        public string ParticipantType { get; set; }
        public DateTime JoinedAt { get; set; }
        public string WebAccessToken { get; set; } // Only for web participants
    }

    /// <summary>
    /// Request to generate web access token
    /// </summary>
    public class GenerateTokenDto
    {
        [Required]
        public string Identity { get; set; }
        
        [Required]
        public string ConferenceName { get; set; }
        
        public int TtlMinutes { get; set; } = 60;
    }

    /// <summary>
    /// Response with access token
    /// </summary>
    public class AccessTokenResponseDto
    {
        public string AccessToken { get; set; }
        public string Identity { get; set; }
        public string ConferenceName { get; set; }
        public DateTime ExpiresAt { get; set; }
    }

    /// <summary>
    /// Conference status response
    /// </summary>
    public class ConferenceStatusDto
    {
        public string ConferenceSid { get; set; }
        public string ConferenceName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public bool IsActive { get; set; }
        public int MaxParticipants { get; set; }
        public int ParticipantCount { get; set; }
        public object[] Participants { get; set; }
    }

    /// <summary>
    /// Response after initiating call session
    /// </summary>
    public class InitiateCallSessionResponseDto
    {
        public string ConferenceName { get; set; }
        public string ConferenceSid { get; set; }
        public string CallSid { get; set; }
        public string WebAccessToken { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
