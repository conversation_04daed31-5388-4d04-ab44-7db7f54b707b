﻿﻿using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Filters
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false, Inherited = true)]
    public class EndpointTrackerFilter : Attribute, IAsyncActionFilter
    {
        private readonly Applications _application;
        private readonly ApplicationSection _section;

        public EndpointTrackerFilter(Applications application, ApplicationSection section)
        {
            _application = application;
            _section = section;
        }

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // Execute the action first
            var resultContext = await next();

            // Only track successful requests
            if (resultContext.Exception == null)
            {
                try
                {
                    // Get the UnitOfWork
                    var unitOfWork = (IUnitofwork)context.HttpContext.RequestServices
                        .GetService(typeof(IUnitofwork));

                    if (unitOfWork != null)
                    {
                        var endpointTrackerService = unitOfWork.EndpointTrackerService;
                        // Get the controller and action names
                        var controllerName = context.RouteData.Values["controller"]?.ToString();
                        var actionName = context.RouteData.Values["action"]?.ToString();
                        var httpMethod = context.HttpContext.Request.Method;

                        // Get the user ID from claims
                        var userId = context.HttpContext.User.Claims
                            .FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;

                        // If user is not authenticated, don't track
                        // Get 'aaplication' header value from the header and allow if its 'Echo'
                        var application = context.HttpContext.Request.Headers["application"].ToString();
                        if (application != "Echo" || application != "echo")
                        {
                            if (string.IsNullOrEmpty(userId))
                            {
                                return;
                            }
                        }

                        // Get the tenant ID
                        var tenantSchema = new TenantSchema();
                        var tenantId = tenantSchema.ExtractSubdomainFromRequest(context.HttpContext);

                        // Track the endpoint call
                        await endpointTrackerService.TrackEndpointCallAsync(
                            _application,
                            _section,
                            controllerName,
                            actionName,
                            httpMethod);
                    }
                }
                catch (Exception)
                {
                    // Ignore any errors in tracking to not affect the main request
                }
            }
        }
    }
}
