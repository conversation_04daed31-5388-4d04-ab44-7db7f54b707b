﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel;

namespace Jobid.App.Calender.Models.RTC.Requests
{
    public class UpdateMeetingRequest
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [Json<PERSON>roperty("hostJobAuthId")]
        public string HostJobAuthId { get; set; }

        [Json<PERSON>roperty("coHostJobAuthIds")]
        public List<string> CoHostJobAuthIds { get; set; }

        [JsonProperty("passcode", NullValueHandling = NullValueHandling.Ignore, DefaultValueHandling = DefaultValueHandling.Ignore)]
        [DefaultValue("")]
        public string Passcode { get; set; }

        [JsonProperty("isPrivate")]
        public bool IsPrivate { get; set; }

        [JsonProperty("guestJobAuthIds")]
        public List<string> GuestJobAuthIds { get; set; }

        [JsonProperty("externalGuestEmails")]
        public List<string> ExternalGuestEmails { get; set; }
    }
}
