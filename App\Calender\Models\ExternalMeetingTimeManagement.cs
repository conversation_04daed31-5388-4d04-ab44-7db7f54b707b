﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class ExternalMeetingTimeManagement
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public Guid ExternalMeetingId { get; set; }
        public string UserId { get; set; }
        public string ScheduleName { get; set; }
        public Guid PersonalScheduleId { get; set; }
        public string DayOfTheWeek { get; set; }
        public string TimeBreakDown { get; set; }
        public string SelectedTimeSlots { get; set; }
        public DateTime? Date { get; set; }

        // Locked for 10 minutes
        public DateTime? IsLockedTill { get; set; }

        [NotMapped]
        public bool IsLocked
        {
            get
            {
                if (IsLockedTill.HasValue)
                {
                    return DateTime.UtcNow < IsLockedTill.Value;
                }
                return false;
            }
        }

        // Navigational Properties
        public ExternalMeeting ExternalMeeting { get; set; }
        public PersonalSchedule PersonalSchedule { get; set; }
    }
}
