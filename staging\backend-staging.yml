apiVersion: apps/v1
kind: Deployment
metadata:
  name: jobpro-backend
  labels:
    app: jobpro-backend
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: jobpro-backend
  template:
    metadata:
      labels:
        app: jobpro-backend
    spec:
      containers:
        - name: jobpro-backend
          image: zarttechjobpro/jobpro:api-v1
          resources:
            requests:
              memory: "2Gi"
              ephemeral-storage: "1Gi"
          envFrom:
            - secretRef:
                name: jobpro-gcp-secret
            - secretRef:
                name: jobpro-secret
            - secretRef:
                name: jopro-bucket-secret
            - secretRef:
                name: jobpro-twilio-secret 
            - secretRef:
                name: jobprostaging-backend-secret
            - secretRef:
                name: jobpro-shared-secrets
          imagePullPolicy: Always
          ports:
            - containerPort: 80
         
      imagePullSecrets:
        - name: regcred


---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
spec:
  selector:
    app: jobpro-backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      port: 443
      targetPort: 80
  type: NodePort
