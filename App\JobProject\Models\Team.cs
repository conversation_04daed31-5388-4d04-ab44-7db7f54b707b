﻿using System;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Helpers.Utils.Extensions;

namespace Jobid.App.JobProjectManagement.Models
{
    public class Team
    {
        public Guid Id { get; set; } = new Guid();

        [Required]
        public string Name { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = GetAdjustedDateTimeBasedOnTZNow();
        public DateTime UpdatedAt { get; set; } = GetAdjustedDateTimeBasedOnTZNow();
        public string UpdatedBy { get; set; }
    }
}
