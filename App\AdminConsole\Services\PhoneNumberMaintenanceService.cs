using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Hangfire;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Models;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models.Phone;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Notification.Contracts;
using Jobid.App.Notification.Repository;
using Serilog;

namespace Jobid.App.AdminConsole.Services
{
    public class PhoneNumberMaintenanceService : IPhoneNumberMaintenanceService
    {
        private readonly JobProDbContext _publicContext;
        private readonly IWalletService _walletService;
        private readonly IEmailService _emailService;
        private readonly INotificationsService _notificationService;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;
        private readonly decimal _maintenanceFeePerNumber;
        private readonly int _maxRetryAttempts;
        private readonly int _retryIntervalDays;
        private Serilog.ILogger _logger = Log.ForContext<PhoneNumberMaintenanceService>();

        public PhoneNumberMaintenanceService(
            JobProDbContext publicContext,
            IWalletService walletService,
            IEmailService emailService,
            INotificationsService notificationService,
            IConfiguration configuration)
        {
            _publicContext = publicContext;
            _walletService = walletService;
            _emailService = emailService;
            _notificationService = notificationService;
            _configuration = configuration;
            _connectionString = GlobalVariables.ConnectionString;
            
            // Get maintenance fee configuration from settings
            _maintenanceFeePerNumber = _configuration.GetValue<decimal>("PhoneNumberMaintenance:FeePerNumber", 2.00m);
            _maxRetryAttempts = _configuration.GetValue<int>("PhoneNumberMaintenance:MaxRetryAttempts", 3);
            _retryIntervalDays = _configuration.GetValue<int>("PhoneNumberMaintenance:RetryIntervalDays", 7);
        }

        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("admin")]
        public async Task ProcessMonthlyMaintenanceCharges(List<string> subdomains)
        {
            if (subdomains == null || !subdomains.Any())
            {
                _logger.Information("No subdomains provided for maintenance charge processing");
                return;
            }

            _logger.Information($"Starting monthly maintenance charge processing for {subdomains.Count} companies");

            foreach (var subdomain in subdomains)
            {
                try
                {
                    // Get tenant information
                    var tenant = await _publicContext.Tenants
                        .FirstOrDefaultAsync(t => t.Subdomain.ToLower() == subdomain.ToLower());

                    if (tenant == null)
                    {
                        _logger.Warning($"Tenant not found for subdomain: {subdomain}");
                        continue;
                    }

                    // Get phone number count for this tenant
                    var phoneNumberCount = await _publicContext.PhoneNoToCompanyMappings
                        .CountAsync(p => p.TenantId == tenant.Id);

                    if (phoneNumberCount == 0)
                    {
                        _logger.Information($"No phone numbers found for tenant {subdomain}, skipping maintenance charge");
                        continue;
                    }

                    // Check if charge already exists for current month
                    var currentMonth = DateTime.UtcNow.Date.AddDays(1 - DateTime.UtcNow.Day);
                    var existingCharge = await _publicContext.PhoneNumberMaintenanceCharges
                        .FirstOrDefaultAsync(c => c.TenantId == tenant.Id && 
                                           c.ChargeDate.Date >= currentMonth && 
                                           c.ChargeDate.Date < currentMonth.AddMonths(1));

                    if (existingCharge != null)
                    {
                        _logger.Information($"Maintenance charge already exists for tenant {subdomain} for current month");
                        continue;
                    }

                    // Process the charge
                    await ChargeMaintenanceFee(tenant.Id, subdomain);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"Error processing maintenance charge for subdomain: {subdomain}");
                }
            }

            _logger.Information("Monthly maintenance charge processing completed");
        }

        public async Task<GenericResponse> ChargeMaintenanceFee(Guid tenantId, string subdomain)
        {
            try
            {
                _logger.Information($"Processing maintenance fee charge for tenant {tenantId}");

                // Get phone number count
                var phoneNumberCount = await _publicContext.PhoneNoToCompanyMappings
                    .CountAsync(p => p.TenantId == tenantId);

                if (phoneNumberCount == 0)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "No phone numbers found, no charge required"
                    };
                }

                var totalChargeAmount = phoneNumberCount * _maintenanceFeePerNumber;

                // Create maintenance charge record
                var maintenanceCharge = new PhoneNumberMaintenanceCharge
                {
                    TenantId = tenantId,
                    TotalPhoneNumbers = phoneNumberCount,
                    ChargeAmount = totalChargeAmount,
                    PerNumberFee = _maintenanceFeePerNumber,
                    ChargeDate = DateTime.UtcNow,
                    TransactionReference = Guid.NewGuid().ToString()
                };

                _publicContext.PhoneNumberMaintenanceCharges.Add(maintenanceCharge);
                await _publicContext.SaveChangesAsync();

                // Try to deduct from wallet
                await using var tenantContext = new JobProDbContext(_connectionString, new DbContextSchema(subdomain));
                var walletService = new WalletService(tenantContext, new PaymentService(_configuration, tenantContext, null, null, _publicContext));

                try
                {
                    var walletTransaction = await walletService.DeductFundsAsync(
                        totalChargeAmount, 
                        $"Phone number maintenance fee - {phoneNumberCount} numbers @ ${_maintenanceFeePerNumber} each");

                    // Update charge status to paid
                    maintenanceCharge.Status = MaintenanceChargeStatus.Paid;
                    maintenanceCharge.PaidDate = DateTime.UtcNow;
                    maintenanceCharge.WalletTransactionId = walletTransaction.Id.ToString();
                    maintenanceCharge.UpdatedAt = DateTime.UtcNow;

                    _publicContext.PhoneNumberMaintenanceCharges.Update(maintenanceCharge);
                    await _publicContext.SaveChangesAsync();

                    // Send success notification
                    await SendMaintenanceChargeNotification(tenantId, MaintenanceNotificationType.ChargeSuccess, totalChargeAmount, phoneNumberCount);

                    _logger.Information($"Successfully charged maintenance fee of ${totalChargeAmount} for tenant {tenantId}");

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Maintenance fee charged successfully",
                        Data = new
                        {
                            ChargeId = maintenanceCharge.Id,
                            Amount = totalChargeAmount,
                            PhoneNumberCount = phoneNumberCount,
                            TransactionId = walletTransaction.Id
                        }
                    };
                }
                catch (Exception walletEx)
                {
                    _logger.Warning(walletEx, $"Failed to deduct maintenance fee from wallet for tenant {tenantId}");

                    // Check if it's insufficient funds
                    if (walletEx.Message.Contains("Insufficient funds"))
                    {
                        maintenanceCharge.Status = MaintenanceChargeStatus.InsufficientFunds;
                        maintenanceCharge.FailureReason = "Insufficient wallet balance";
                        maintenanceCharge.RetryDate = DateTime.UtcNow.AddDays(_retryIntervalDays);
                        
                        // Send insufficient funds notification
                        await SendMaintenanceChargeNotification(tenantId, MaintenanceNotificationType.InsufficientFunds, totalChargeAmount, phoneNumberCount);
                    }
                    else
                    {
                        maintenanceCharge.Status = MaintenanceChargeStatus.Failed;
                        maintenanceCharge.FailureReason = walletEx.Message;
                        
                        // Send failure notification
                        await SendMaintenanceChargeNotification(tenantId, MaintenanceNotificationType.ChargeFailed, totalChargeAmount, phoneNumberCount);
                    }

                    maintenanceCharge.UpdatedAt = DateTime.UtcNow;
                    _publicContext.PhoneNumberMaintenanceCharges.Update(maintenanceCharge);
                    await _publicContext.SaveChangesAsync();

                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Failed to charge maintenance fee",
                        Data = new
                        {
                            ChargeId = maintenanceCharge.Id,
                            Amount = totalChargeAmount,
                            PhoneNumberCount = phoneNumberCount,
                            Reason = walletEx.Message
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error charging maintenance fee for tenant {tenantId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while processing maintenance fee",
                    Data = ex.Message
                };
            }
        }

        [AutomaticRetry(Attempts = 2, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("admin")]
        public async Task ProcessMaintenanceChargeRetries(List<string> subdomains)
        {
            _logger.Information("Starting maintenance charge retry processing");

            var retryDate = DateTime.UtcNow.Date;
            var pendingCharges = await _publicContext.PhoneNumberMaintenanceCharges
                .Include(c => c.Tenant)
                .Where(c => (c.Status == MaintenanceChargeStatus.InsufficientFunds || 
                           c.Status == MaintenanceChargeStatus.Failed) &&
                           c.RetryDate.HasValue &&
                           c.RetryDate.Value.Date <= retryDate &&
                           c.RetryCount < _maxRetryAttempts &&
                           subdomains.Contains(c.Tenant.Subdomain))
                .ToListAsync();

            foreach (var charge in pendingCharges)
            {
                try
                {
                    _logger.Information($"Retrying maintenance charge {charge.Id} for tenant {charge.TenantId}");

                    charge.RetryCount++;
                    charge.Status = MaintenanceChargeStatus.RetryScheduled;
                    charge.UpdatedAt = DateTime.UtcNow;

                    // Try to charge again
                    var result = await ChargeMaintenanceFee(charge.TenantId, charge.Tenant.Subdomain);
                    
                    if (result.ResponseCode == "200")
                    {
                        _logger.Information($"Retry successful for maintenance charge {charge.Id}");
                    }
                    else
                    {
                        // If still failing and max retries reached, mark as failed
                        if (charge.RetryCount >= _maxRetryAttempts)
                        {
                            charge.Status = MaintenanceChargeStatus.Failed;
                            charge.FailureReason = "Maximum retry attempts exceeded";
                        }
                        else
                        {
                            // Schedule next retry
                            charge.RetryDate = DateTime.UtcNow.AddDays(_retryIntervalDays);
                            charge.Status = MaintenanceChargeStatus.InsufficientFunds;
                        }
                    }

                    _publicContext.PhoneNumberMaintenanceCharges.Update(charge);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, $"Error during retry for maintenance charge {charge.Id}");
                    
                    charge.RetryCount++;
                    if (charge.RetryCount >= _maxRetryAttempts)
                    {
                        charge.Status = MaintenanceChargeStatus.Failed;
                        charge.FailureReason = "Retry failed: " + ex.Message;
                    }
                    else
                    {
                        charge.RetryDate = DateTime.UtcNow.AddDays(_retryIntervalDays);
                    }
                    
                    charge.UpdatedAt = DateTime.UtcNow;
                    _publicContext.PhoneNumberMaintenanceCharges.Update(charge);
                }
            }

            await _publicContext.SaveChangesAsync();
            _logger.Information($"Maintenance charge retry processing completed. Processed {pendingCharges.Count} charges");
        }

        public async Task<bool> SendMaintenanceChargeNotification(Guid tenantId, MaintenanceNotificationType notificationType, decimal chargeAmount, int phoneNumberCount)
        {
            try
            {
                // Get tenant and admin user details
                var tenant = await _publicContext.Tenants.FirstOrDefaultAsync(t => t.Id == tenantId);
                if (tenant == null) return false;

                var adminUser = await _publicContext.UserProfiles
                    .FirstOrDefaultAsync(u => u.SubDomain.ToLower() == tenant.Subdomain.ToLower());

                if (adminUser == null) return false;

                var subject = "";
                var templateName = "";
                var notificationMessage = "";

                switch (notificationType)
                {
                    case MaintenanceNotificationType.ChargeSuccess:
                        subject = "Phone Number Maintenance Fee Charged";
                        templateName = "phone-maintenance-success";
                        notificationMessage = $"Maintenance fee of ${chargeAmount:F2} charged for {phoneNumberCount} phone numbers";
                        break;

                    case MaintenanceNotificationType.ChargeFailed:
                        subject = "Phone Number Maintenance Fee Charge Failed";
                        templateName = "phone-maintenance-failed";
                        notificationMessage = $"Failed to charge maintenance fee of ${chargeAmount:F2} for {phoneNumberCount} phone numbers";
                        break;

                    case MaintenanceNotificationType.InsufficientFunds:
                        subject = "Insufficient Funds - Phone Number Maintenance Fee";
                        templateName = "phone-maintenance-insufficient-funds";
                        notificationMessage = $"Insufficient wallet balance to charge maintenance fee of ${chargeAmount:F2} for {phoneNumberCount} phone numbers";
                        break;

                    case MaintenanceNotificationType.FundWalletReminder:
                        subject = "Fund Your Wallet - Phone Number Maintenance Due";
                        templateName = "phone-maintenance-fund-wallet";
                        notificationMessage = $"Please fund your wallet to pay maintenance fee of ${chargeAmount:F2} for {phoneNumberCount} phone numbers";
                        break;

                    case MaintenanceNotificationType.RetryNotification:
                        subject = "Phone Number Maintenance Fee Retry";
                        templateName = "phone-maintenance-retry";
                        notificationMessage = $"Retrying maintenance fee charge of ${chargeAmount:F2} for {phoneNumberCount} phone numbers";
                        break;

                    default:
                        return false;
                }

                // Create email content
                var emailTemplate = GetEmailTemplate(templateName);
                if (!string.IsNullOrEmpty(emailTemplate))
                {
                    emailTemplate = emailTemplate
                        .Replace("{CompanyName}", tenant.CompanyName)
                        .Replace("{AdminName}", $"{adminUser.FirstName} {adminUser.LastName}")
                        .Replace("{ChargeAmount}", chargeAmount.ToString("F2"))
                        .Replace("{PhoneNumberCount}", phoneNumberCount.ToString())
                        .Replace("{PerNumberFee}", _maintenanceFeePerNumber.ToString("F2"))
                        .Replace("{ChargeDate}", DateTime.UtcNow.ToString("MMMM dd, yyyy"));

                    // Send email notification
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(emailTemplate, adminUser.Email, subject));
                }

                // Send in-app notification using NotificationsService
                try
                {
                    await using var tenantContext = new JobProDbContext(_connectionString, new DbContextSchema(tenant.Subdomain));
                    var notificationService = new NotificationsService(tenantContext, null);

                    var notificationId = await notificationService.AddNotification(new Notification.ViewModel.AddNotificationDto
                    {
                        Message = notificationMessage,
                        Event = JobProject.Enums.Enums.EventCategory.Admin,
                        EventId = tenantId.ToString(),
                        CreatedBy = adminUser.UserId
                    });

                    if (!string.IsNullOrEmpty(notificationId))
                    {
                        await notificationService.AddUserNotification(new List<string> { adminUser.UserId }, Guid.Parse(notificationId));
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warning(ex, "Failed to send in-app notification, but email was sent");
                }

                _logger.Information($"Sent {notificationType} notification to tenant {tenantId}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error sending notification {notificationType} to tenant {tenantId}");
                return false;
            }
        }

        public async Task<GenericResponse> GetMaintenanceChargeHistory(Guid tenantId, int pageSize = 10, int pageNumber = 1)
        {
            try
            {
                var query = _publicContext.PhoneNumberMaintenanceCharges
                    .Where(c => c.TenantId == tenantId)
                    .OrderByDescending(c => c.ChargeDate);

                var totalCount = await query.CountAsync();
                var charges = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(c => new
                    {
                        c.Id,
                        c.TotalPhoneNumbers,
                        c.ChargeAmount,
                        c.PerNumberFee,
                        c.Status,
                        c.ChargeDate,
                        c.PaidDate,
                        c.RetryDate,
                        c.RetryCount,
                        c.FailureReason,
                        c.TransactionReference,
                        c.WalletTransactionId
                    })
                    .ToListAsync();

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Maintenance charge history retrieved successfully",
                    Data = new
                    {
                        Charges = charges,
                        TotalCount = totalCount,
                        PageSize = pageSize,
                        PageNumber = pageNumber,
                        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error retrieving maintenance charge history for tenant {tenantId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Error retrieving maintenance charge history",
                    Data = ex.Message
                };
            }
        }

        public async Task<GenericResponse> GetPendingMaintenanceCharges(Guid? tenantId = null)
        {
            try
            {
                var query = _publicContext.PhoneNumberMaintenanceCharges
                    .Include(c => c.Tenant)
                    .Where(c => c.Status == MaintenanceChargeStatus.Pending ||
                               c.Status == MaintenanceChargeStatus.InsufficientFunds ||
                               c.Status == MaintenanceChargeStatus.RetryScheduled);

                if (tenantId.HasValue)
                {
                    query = query.Where(c => c.TenantId == tenantId.Value);
                }

                var pendingCharges = await query
                    .OrderBy(c => c.ChargeDate)
                    .Select(c => new
                    {
                        c.Id,
                        c.TenantId,
                        CompanyName = c.Tenant.CompanyName,
                        Subdomain = c.Tenant.Subdomain,
                        c.TotalPhoneNumbers,
                        c.ChargeAmount,
                        c.Status,
                        c.ChargeDate,
                        c.RetryDate,
                        c.RetryCount,
                        c.FailureReason
                    })
                    .ToListAsync();

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Pending maintenance charges retrieved successfully",
                    Data = pendingCharges
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error retrieving pending maintenance charges");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Error retrieving pending maintenance charges",
                    Data = ex.Message
                };
            }
        }

        private string GetEmailTemplate(string templateName)
        {
            try
            {
                var templatePath = System.IO.Path.Combine(System.IO.Directory.GetCurrentDirectory(), "wwwroot", "EmailTemplates", $"{templateName}.html");
                if (System.IO.File.Exists(templatePath))
                {
                    return System.IO.File.ReadAllText(templatePath);
                }
                
                // Return a basic template if file doesn't exist
                return GetBasicEmailTemplate(templateName);
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, $"Error loading email template {templateName}");
                return GetBasicEmailTemplate(templateName);
            }
        }

        private string GetBasicEmailTemplate(string templateName)
        {
            return templateName switch
            {
                "phone-maintenance-success" => @"
                    <h2>Phone Number Maintenance Fee Charged</h2>
                    <p>Dear {AdminName},</p>
                    <p>Your monthly phone number maintenance fee has been successfully charged.</p>
                    <p><strong>Details:</strong></p>
                    <ul>
                        <li>Company: {CompanyName}</li>
                        <li>Phone Numbers: {PhoneNumberCount}</li>
                        <li>Fee per Number: ${PerNumberFee}</li>
                        <li>Total Amount: ${ChargeAmount}</li>
                        <li>Charge Date: {ChargeDate}</li>
                    </ul>
                    <p>Thank you for using our services.</p>",
                
                "phone-maintenance-insufficient-funds" => @"
                    <h2>Insufficient Funds - Phone Number Maintenance Fee</h2>
                    <p>Dear {AdminName},</p>
                    <p>We were unable to charge your monthly phone number maintenance fee due to insufficient wallet balance.</p>
                    <p><strong>Details:</strong></p>
                    <ul>
                        <li>Company: {CompanyName}</li>
                        <li>Phone Numbers: {PhoneNumberCount}</li>
                        <li>Required Amount: ${ChargeAmount}</li>
                    </ul>
                    <p>Please fund your wallet to avoid service interruption. We will retry the charge in a few days.</p>",
                
                _ => @"
                    <h2>Phone Number Maintenance Notification</h2>
                    <p>Dear {AdminName},</p>
                    <p>This is a notification regarding your phone number maintenance fee.</p>
                    <p><strong>Details:</strong></p>
                    <ul>
                        <li>Company: {CompanyName}</li>
                        <li>Phone Numbers: {PhoneNumberCount}</li>
                        <li>Amount: ${ChargeAmount}</li>
                        <li>Date: {ChargeDate}</li>
                    </ul>"
            };
        }
    }
}
