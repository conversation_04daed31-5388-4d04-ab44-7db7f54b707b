﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class AllowedTimeFormatAttribute : ValidationAttribute
    {
        public AllowedTimeFormatAttribute()
        {
            
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var time = value as string;
            if (time != null)
            {
                TimeSpan suppliedTime;
                if (!TimeSpan.TryParse(time, CultureInfo.InvariantCulture, out suppliedTime))
                {
                    return new ValidationResult($"{time} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }
            }

            return ValidationResult.Success;
        }
    }
}
