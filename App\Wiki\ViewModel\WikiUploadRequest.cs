using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System;
using Jobid.App.Wiki.Enums;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Wiki.ViewModel
{
    public class WikiUploadRequest
    {
        public IFormFile File { get; set; }

        public string Description { get; set; }

        public List<Guid> DepartmentIds { get; set; }

        [Required(ErrorMessage = "Batch number is required for file uploads")]
        public string BatchId { get; set; }

        public WikiFileType WikiFileType { get; set; }

        /// <summary>
        /// Optional text content that can be included alongside file uploads
        /// </summary>
        public string TextContent { get; set; }
    }
}
