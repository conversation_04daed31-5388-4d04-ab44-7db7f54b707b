﻿using Hangfire;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.Tenant.Repository
{
    public class BackgroundJobService : IBackgroundJobService
    {
        private readonly string _conString;

        public BackgroundJobService()
        {
            _conString = GlobalVariables.ConnectionString;
        }

        public async Task CompleteOnboarding()
        {
            await using var publicContext = new JobProDbContext();
            var strategy = publicContext.Database.CreateExecutionStrategy();

            await strategy.ExecuteAsync(async () =>
            {
                await using var transaction = await publicContext.Database.BeginTransactionAsync();

                try
                {
                    var incompleteOnboardings = publicContext.UserCompanies
                        .Where(u => u.StartDate > u.LastUpdate)
                        .ToList();

                    if (incompleteOnboardings.Count > 0)
                    {
                        foreach (var incompleteOnboarding in incompleteOnboardings)
                        {
                            var tenantId = incompleteOnboarding.TenantId;
                            var tenant = publicContext.Tenants.FirstOrDefault(t => t.Id == tenantId);

                            if (tenant is null)
                                throw new Exception("Tenant not found");

                            await using var context = new JobProDbContext(_conString, new DbContextSchema(tenant.Subdomain));

                            var userProfile = context.UserProfiles.FirstOrDefault(u => u.UserId == incompleteOnboarding.UserId);
                            if (userProfile != null)
                            {
                                incompleteOnboarding.LastUpdate = DateTime.UtcNow;
                                publicContext.UserCompanies.Update(incompleteOnboarding);
                            }
                            else
                            {
                                var user = publicContext.Users.FirstOrDefault(u => u.Id == incompleteOnboarding.UserId);
                                if (user != null)
                                {
                                    userProfile = new UserProfile()
                                    {
                                        DateCreated = DateTime.UtcNow,
                                        Email = incompleteOnboarding.Email,
                                        FirstName = user.FirstName,
                                        LastName = user.LastName,
                                        MiddleName = user.MiddleName,
                                        PhoneNumber = user.PhoneNumber,
                                        UserId = user.Id,
                                        Id = Guid.NewGuid().ToString(),
                                        Country = user.Country,
                                        EraseAcitivity = EraseAcitivity.After1Month,
                                        EventCategory = EventCategory.All.ToString(),
                                    };
                                    context.UserProfiles.Add(userProfile);

                                    incompleteOnboarding.LastUpdate = DateTime.UtcNow;
                                    publicContext.UserCompanies.Update(incompleteOnboarding);
                                }
                            }

                            await publicContext.SaveChangesAsync();
                            await context.SaveChangesAsync();
                        }

                        await transaction.CommitAsync();
                    }

                    RecurringJob.RemoveIfExists("complete-onbaording");
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });
        }
    }
}
