﻿using System;
using System.Collections.Generic;

namespace Jobid.App.AdminConsole.Dto.External
{
    public class Data
    {
        public string Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public List<object> TicketActionLogs { get; set; }
        public string Status { get; set; }
        public DateTime DueDate { get; set; }
        public string ReferenceId { get; set; }
        public object CategoryName { get; set; }
        public bool IsRead { get; set; }
        public List<object> TicketReplies { get; set; }
        public List<object> Attachments { get; set; }
        public bool IsChatExist { get; set; }
        public object ChatId { get; set; }
        public string CustomerName { get; set; }
        public string CustomerEmail { get; set; }
        public string CategoryId { get; set; }
        public object AssignedUserId { get; set; }
        public string Priority { get; set; }
        public string Subject { get; set; }
        public string Message { get; set; }
        public object Comment { get; set; }
        public object Files { get; set; }
    }

    public class ExternalCreateTicketResponse
    {
        public string ResponseCode { get; set; }
        public string ResponseMessage { get; set; }
        public Data Data { get; set; }
    }
}
