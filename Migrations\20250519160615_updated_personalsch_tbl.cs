﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class updated_personalsch_tbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updated_personalsch_tbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ExternalMeetingId",
                schema: _Schema,
                table: "PersonalSchedule",
                type: "uuid",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId1",
                schema: _Schema,
                table: "ExternalMeeting");

            migrationBuilder.DropIndex(
                name: "IX_ExternalMeeting_PersonalScheduleId1",
                schema: _Schema,
                table: "ExternalMeeting");

            migrationBuilder.DropColumn(
                name: "ExternalMeetingId",
                schema: _Schema,
                table: "PersonalSchedule");

            migrationBuilder.DropColumn(
                name: "PersonalScheduleId1",
                schema: _Schema,
                table: "ExternalMeeting");

            migrationBuilder.CreateIndex(
                name: "IX_ExternalMeeting_PersonalScheduleId",
                schema: _Schema,
                table: "ExternalMeeting",
                column: "PersonalScheduleId");

            migrationBuilder.AddForeignKey(
                name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId",
                schema: _Schema,
                table: "ExternalMeeting",
                column: "PersonalScheduleId",
                principalSchema: _Schema,
                principalTable: "PersonalSchedule",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
