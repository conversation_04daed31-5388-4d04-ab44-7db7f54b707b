﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel;

namespace Jobid.App.Helpers.Enums
{
    [JsonConverter(typeof(StringEnumConverter))]
    public enum Channel
    {
        [Description("LINKEDIN")]
        LinkedIn = 1,
        [Description("WHATSAPP")]
        WhatsApp = 2,
        [Description("EMAIL")]
        Email = 3,
        [Description("SMS")]
        Sms = 4,
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum SmsAction
    {
        [Description("Send a Message")]
        SendMessage = 1,
        [Description("Unsubscribe Contact")]
        UnsubscribeContact = 2,
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum WhatsappAction
    {
        [Description("Send a Connection Request")]
        SendConnectionRequest = 1,
        [Description("Send an Email Invite")]
        SendEmailInvite = 2,
        [Description("View Profile")]
        ViewProfile = 3,
        [Description("Send Message")]
        SendMessage = 4,
        [Description("Send Inmail")]
        SendInMail = 5,
        [Description("Endorse Skills")]
        EndorseSkills = 6,
        [Description("Follow")]
        Follow = 7, 
        [Description("Like a Posr")]
        LikePost = 8,
        [Description("Withdraw Invite")]
        WithdrawInvite = 9,
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum EmailAction
    {
        [Description("Send an Email Invite")]
        SendEmailInvite = 1,
        [Description("Send Newslwtter")]
        SendNewslwtter = 2,
        [Description("Send Product Demo")]
        SendProductDemo = 3,
        [Description("Unsubscrribe Contact")]
        UnsubscribeContact = 4,
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum LinkedInAction
    {
        [Description("Send a Connection Request")]
        SendConnectionRequest = 1,
        [Description("Send an Email Invite")]
        SendEmailInvite = 2,
        [Description("View Profile")]
        ViewProfile = 3,
        [Description("Send Message")]
        SendMessage = 4,
        [Description("Send Inmail")]
        SendInMail = 5,
        [Description("Endorse Skills")]
        EndorseSkills = 6,
        [Description("Follow")]
        Follow = 7,
        [Description("Like a Posr")]
        LikePost = 8,
        [Description("Withdraw Invite")]
        WithdrawInvite = 9,
    }
}
