﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    public class RoundRobinHostingOrder
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public Guid ExternalMeetingId { get; set; }

        [Required]
        public string HostId { get; set; }
        public DateTime MeetingDateAndTime { get; set; }
        public int HostCount { get; set; }

        // Navigational Properties
        public ExternalMeeting ExternalMeeting { get; set; }
    }
}
