<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="https://fonts.cdnfonts.com/css/hk-groteks" rel="stylesheet" />

    <title>Meeting Invitation</title>
    <script>
      var show = false;
      var count = 1;
      var lists = [
        "{1}",
        "{2}",
        "{3}",
        "{4}",
        "{5}",
        "{6}",
        "{7}",
        "{8}",
        "{9}",
        "{10}",
      ];

      function full() {
        let listContainer = document.getElementById("guest");
        if (count > 1) listContainer.innerHTML = "";

        let listElement = document.createElement("div");
        let listItem = document.createElement("div");

        listContainer.appendChild(listElement);

        if (show) {
          for (let i = 0; i < lists.length; i++) {
            listItem.textContent = lists[i];
            listElement.appendChild(listItem);
            listItem = document.createElement("div");
          }
          document.getElementById("link").innerHTML = "View less guest list";
          show = false;
        } else {
          for (let i = 0; i < 2; i++) {
            listItem.textContent = lists[i];
            listElement.appendChild(listItem);
            listItem = document.createElement("div");
          }
          show = true;
          document.getElementById("link").innerHTML = "View full guest list";
          count++;
        }
      }
    </script>
    <style>
      body {
        font-family: "HK Grotesk", sans-serif;
        font-size: 16px;
        color: #90a3bf;
        max-width: 70em;
        margin: auto;
        word-break: break-all;
      }

      a {
        text-decoration: none;
        color: black;
      }

      .top-section {
        width: 100%;
        padding: 2em;
        border-radius: 15px 15px 0 0;
        background-color: #0075d4;
        display: table;
      }

      .body {
        width: 100%;
        padding: 30px;
        line-height: 2;
      }

      .title {
        color: white;
        font-size: 25px;
        line-height: 2;
        font-weight: 500;
      }

      .sub-title {
        color: #cce8ff;
      }

      .topic {
        color: #002f55;
        padding-top: 10px;
      }

      .link {
        color: #005da9;
        cursor: pointer;
      }

      .select-sec {
        display: inline-flex;
      }

      .select {
        width: fit-content;
        border-radius: 15px;
      }

      .yes {
        border: 1px solid;
        border-radius: 20px 0 0 20px;
      }

      .no {
        border: 1px solid;
      }

      .maybe {
        border: 1px solid;
        border-radius: 0 20px 20px 0;
      }

      span {
        padding: 10px;
        line-height: 3;
      }

      span:hover {
        background-color: #eee;
        cursor: pointer;
      }

      .propose {
        margin-left: 20px;
        width: fit-content;
        border: 1px solid;
        padding: 5px 15px;
        border-radius: 15px;
        cursor: pointer;
      }

      a:hover {
        text-decoration: underline;
        cursor: pointer;
      }

      .button {
        padding: 15px 20px;
        border-radius: 20px;
        background: #008cfe;
        border: none;
        color: white;
        font-size: 16px;
        font-weight: 600;
        font-family: "HK Grotesk", sans-serif;
        display: block;
        text-align: center;
        min-width: 400px;
      }

      .footer {
        padding: 40px;
      }

      @media screen and (max-width: 600px) {
        .select-sec {
          display: block;
        }

        .propose {
          margin-top: 10px;
          margin-left: 0;
        }

        .button {
          min-width: fit-content;
        }
      }
    </style>
  </head>

  <body onload="full()">
    <section class="top-section">
      <table>
        <td style="padding-right: 40px">
          <img
            width="120"
            src="https://jobpro.app/assets/icons/logos/jobpro.png"
            alt="logo"
          />
        </td>
        <td>
          <div class="title">Meeting Invitation</div>
          <div class="sub-title">Hello {name}! {message}</div>
        </td>
      </table>
    </section>

    <section class="body">
      <table>
        <td style="width: 50%; vertical-align: baseline">
          <div>
            <div class="topic">Date & Time:</div>
            <div class="txt">{date} {time}</div>
            <div class="txt">Duration: {duration}</div>
            <div class="txt">Location: {location}</div>
          </div>

          <div>
            <div class="topic">Organiser</div>
            <div class="txt">{organizer}</div>
          </div>

          <div>
            <div class="topic">Guest List</div>
            <div id="guest"></div>
          </div>
          <div id="link" class="link" onclick="full()"></div>
        </td>

        <td style="width: 50%; vertical-align: baseline">
          <div class="select-sec">
            <div class="select">
              <span class="yes"><a href="{yesurl}">Yes</a></span>
              <span class="no"><a href="{nourl}">No</a></span>
              <span class="maybe"><a href="{maybeurl}">Maybe</a></span>
            </div>
            <div class="propose"><a href="{ppnturl}">Propose new time</a></div>
          </div>
          <div>
            <div class="topic">Meeting Name</div>
            <a class="txt">{meeting name}</a>
          </div>
          <div>
            <div class="topic">Meeting Link</div>
            <a class="txt">{link}</a>
          </div>

          <hr />

          <div>
            <div class="topic">Join by Phone</div>
            <div class="txt">(NL) +31 20 256 2703</div>
            <div class="txt">Pin 1256270</div>
          </div>

          <div style="margin-top: 10px">
            <a class="button" href="{link}">
              <span class="button-txt">Join Meeting</span>
            </a>
          </div>
        </td>
      </table>
    </section>

    <section class="footer">
      <table style="width: 100%">
        <td>
          <img
            width="120"
            src="https://jobpro.app/assets/icons/logos/jobpro.png"
            alt="logo"
          />
        </td>
        <td>
          You are receiving this email because you are part of the jobPro
          mailing list
        </td>
      </table>
    </section>
  </body>
</html>
