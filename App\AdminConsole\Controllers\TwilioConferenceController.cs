using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto.TwilioConference;
using Jobid.App.AdminConsole.Dto;

namespace Jobid.App.AdminConsole.Controllers
{
    /// <summary>
    /// Controller for Twilio-only conference management
    /// Handles audio conferences with PSTN and web participants
    /// </summary>
    [ApiController]
    [Route("api/twilio-conference")]
    public class TwilioConferenceController : ControllerBase
    {
        private readonly ILogger<TwilioConferenceController> _logger;
        private readonly ITwilioConferenceService _conferenceService;

        public TwilioConferenceController(
            ILogger<TwilioConferenceController> logger,
            ITwilioConferenceService conferenceService)
        {
            _logger = logger;
            _conferenceService = conferenceService;
        }

        /// <summary>
        /// Create a new Twilio conference room
        /// </summary>
        [HttpPost("create")]
        public async Task<IActionResult> CreateConference([FromBody] CreateConferenceDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Creating conference {ConferenceName} for max {MaxParticipants} participants", 
                    request.ConferenceName, request.MaxParticipants);

                var conferenceSid = await _conferenceService.CreateConference(
                    request.ConferenceName, 
                    request.MaxParticipants);

                var response = new CreateConferenceResponseDto
                {
                    ConferenceSid = conferenceSid,
                    ConferenceName = request.ConferenceName,
                    CreatedAt = DateTime.UtcNow,
                    MaxParticipants = request.MaxParticipants
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating conference {ConferenceName}", request?.ConferenceName);
                return StatusCode(500, new { error = "Failed to create conference", message = ex.Message });
            }
        }

        /// <summary>
        /// Add PSTN participant to conference
        /// </summary>
        [HttpPost("add-pstn-participant")]
        public async Task<IActionResult> AddPstnParticipant([FromBody] AddPstnParticipantDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Adding PSTN participant {PhoneNumber} to conference {ConferenceName}", 
                    request.PhoneNumber, request.ConferenceName);

                var callSid = await _conferenceService.AddPstnParticipant(
                    request.ConferenceName,
                    request.PhoneNumber,
                    request.FromNumber);

                var response = new AddParticipantResponseDto
                {
                    ParticipantId = callSid,
                    ConferenceName = request.ConferenceName,
                    ParticipantType = "PSTN",
                    JoinedAt = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding PSTN participant {PhoneNumber} to conference {ConferenceName}", 
                    request?.PhoneNumber, request?.ConferenceName);
                return StatusCode(500, new { error = "Failed to add PSTN participant", message = ex.Message });
            }
        }

        /// <summary>
        /// Add web participant to conference
        /// </summary>
        [HttpPost("add-web-participant")]
        public async Task<IActionResult> AddWebParticipant([FromBody] AddWebParticipantDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Adding web participant {Identity} to conference {ConferenceName}", 
                    request.Identity, request.ConferenceName);

                var identity = await _conferenceService.AddWebParticipant(
                    request.ConferenceName,
                    request.Identity,
                    request.DisplayName);

                var accessToken = _conferenceService.GenerateWebAccessToken(
                    request.Identity,
                    request.ConferenceName);

                var response = new AddParticipantResponseDto
                {
                    ParticipantId = identity,
                    ConferenceName = request.ConferenceName,
                    ParticipantType = "Web",
                    JoinedAt = DateTime.UtcNow,
                    WebAccessToken = accessToken
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding web participant {Identity} to conference {ConferenceName}", 
                    request?.Identity, request?.ConferenceName);
                return StatusCode(500, new { error = "Failed to add web participant", message = ex.Message });
            }
        }

        /// <summary>
        /// Generate access token for web participant
        /// </summary>
        [HttpPost("generate-token")]
        public IActionResult GenerateAccessToken([FromBody] GenerateTokenDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogDebug("Generating access token for identity {Identity} in conference {ConferenceName}", 
                    request.Identity, request.ConferenceName);

                var accessToken = _conferenceService.GenerateWebAccessToken(
                    request.Identity,
                    request.ConferenceName,
                    request.TtlMinutes);

                var response = new AccessTokenResponseDto
                {
                    AccessToken = accessToken,
                    Identity = request.Identity,
                    ConferenceName = request.ConferenceName,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(request.TtlMinutes)
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating access token for identity {Identity}", request?.Identity);
                return StatusCode(500, new { error = "Failed to generate access token", message = ex.Message });
            }
        }

        /// <summary>
        /// Remove participant from conference
        /// </summary>
        [HttpDelete("{conferenceName}/participants/{participantId}")]
        public async Task<IActionResult> RemoveParticipant(string conferenceName, string participantId)
        {
            try
            {
                _logger.LogInformation("Removing participant {ParticipantId} from conference {ConferenceName}", 
                    participantId, conferenceName);

                await _conferenceService.RemoveParticipant(conferenceName, participantId);

                return Ok(new { message = "Participant removed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing participant {ParticipantId} from conference {ConferenceName}", 
                    participantId, conferenceName);
                return StatusCode(500, new { error = "Failed to remove participant", message = ex.Message });
            }
        }

        /// <summary>
        /// End conference
        /// </summary>
        [HttpPost("{conferenceName}/end")]
        public async Task<IActionResult> EndConference(string conferenceName)
        {
            try
            {
                _logger.LogInformation("Ending conference {ConferenceName}", conferenceName);

                await _conferenceService.EndConference(conferenceName);

                return Ok(new { message = "Conference ended successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ending conference {ConferenceName}", conferenceName);
                return StatusCode(500, new { error = "Failed to end conference", message = ex.Message });
            }
        }

        /// <summary>
        /// Get conference status
        /// </summary>
        [HttpGet("{conferenceName}/status")]
        public async Task<IActionResult> GetConferenceStatus(string conferenceName)
        {
            try
            {
                var conference = await _conferenceService.GetConference(conferenceName);
                
                if (conference == null)
                {
                    return NotFound(new { error = "Conference not found" });
                }

                var response = new ConferenceStatusDto
                {
                    ConferenceSid = conference.ConferenceSid,
                    ConferenceName = conference.ConferenceName,
                    StartTime = conference.StartTime,
                    EndTime = conference.EndTime,
                    Duration = conference.EndTime?.Subtract(conference.StartTime) ?? DateTime.UtcNow.Subtract(conference.StartTime),
                    IsActive = conference.IsActive,
                    MaxParticipants = conference.MaxParticipants,
                    ParticipantCount = conference.Participants.Count(p => p.Status == Services.ParticipantStatus.Connected),
                    Participants = conference.Participants.Select(p => new
                    {
                        p.Identity,
                        p.DisplayName,
                        p.PhoneNumber,
                        ParticipantType = p.ParticipantType.ToString(),
                        Status = p.Status.ToString(),
                        p.JoinedAt,
                        p.LeftAt
                    }).ToArray()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting conference status for {ConferenceName}", conferenceName);
                return StatusCode(500, new { error = "Failed to get conference status", message = ex.Message });
            }
        }

        /// <summary>
        /// Get all active conferences
        /// </summary>
        [HttpGet("active")]
        public async Task<IActionResult> GetActiveConferences()
        {
            try
            {
                var conferences = await _conferenceService.GetActiveConferences();
                return Ok(conferences);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active conferences");
                return StatusCode(500, new { error = "Failed to get active conferences", message = ex.Message });
            }
        }

        /// <summary>
        /// Handle Twilio conference status callbacks
        /// </summary>
        [HttpPost("status")]
        public async Task<IActionResult> HandleConferenceStatus()
        {
            try
            {
                var parameters = new Dictionary<string, string>();
                foreach (var key in Request.Form.Keys)
                {
                    parameters[key] = Request.Form[key];
                }

                var conferenceSid = parameters.GetValueOrDefault("ConferenceSid");
                var statusCallbackEvent = parameters.GetValueOrDefault("StatusCallbackEvent");

                _logger.LogDebug("Received conference status callback: {Event} for {ConferenceSid}", 
                    statusCallbackEvent, conferenceSid);

                await _conferenceService.HandleConferenceStatusCallback(conferenceSid, statusCallbackEvent, parameters);

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling conference status callback");
                return StatusCode(500, new { error = "Failed to handle status callback" });
            }
        }

        /// <summary>
        /// Handle Twilio call status callbacks
        /// </summary>
        [HttpPost("call-status")]
        public async Task<IActionResult> HandleCallStatus()
        {
            try
            {
                var parameters = new Dictionary<string, string>();
                foreach (var key in Request.Form.Keys)
                {
                    parameters[key] = Request.Form[key];
                }

                var callSid = parameters.GetValueOrDefault("CallSid");
                var callStatus = parameters.GetValueOrDefault("CallStatus");

                _logger.LogDebug("Received call status callback: {Status} for call {CallSid}", 
                    callStatus, callSid);

                // Handle call status updates here if needed
                
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling call status callback");
                return StatusCode(500, new { error = "Failed to handle call status callback" });
            }
        }

        /// <summary>
        /// Initiate a call session (simplified Twilio-only version)
        /// </summary>
        [HttpPost("initiate-call")]
        public async Task<IActionResult> InitiateCall([FromBody] InitiateCallSessionDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Initiating call session from user {UserId} to {ToNumber}", 
                    request.UserId, request.ToNumber);

                // Generate unique conference name
                var conferenceName = $"call-{Guid.NewGuid().ToString()[..8]}";

                // Create conference
                var conferenceSid = await _conferenceService.CreateConference(conferenceName, request.MaxParticipants);

                // Add web participant (the user initiating the call)
                await _conferenceService.AddWebParticipant(conferenceName, request.UserId, request.UserDisplayName);

                // Generate access token for web participant
                var webAccessToken = _conferenceService.GenerateWebAccessToken(request.UserId, conferenceName);

                // Add PSTN participant
                var callSid = await _conferenceService.AddPstnParticipant(conferenceName, request.ToNumber, request.FromNumberId.ToString());

                var response = new InitiateCallSessionResponseDto
                {
                    ConferenceName = conferenceName,
                    ConferenceSid = conferenceSid,
                    CallSid = callSid,
                    WebAccessToken = webAccessToken,
                    CreatedAt = DateTime.UtcNow
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initiating call session for user {UserId}", request?.UserId);
                return StatusCode(500, new { error = "Failed to initiate call session", message = ex.Message });
            }
        }
    }
}
