﻿using Jobid.App.Subscription.ViewModels;

namespace Jobid.App.Subscription.Models
{
    public class ApplicationSummaryDto
    {
        public ApplicationSummaryDto()
        {
            Joble = new SummaryStatisticsDto();
            Echo = new SummaryStatisticsDto();
            JobPays = new SummaryStatisticsDto();
            JobID = new SummaryStatisticsDto();
            JobEvent = new SummaryStatisticsDto();
            JobEyes = new SummaryStatisticsDto();
            CaringBoss = new SummaryStatisticsDto();
        }

        public SummaryStatisticsDto Joble { get; set; }
        public SummaryStatisticsDto Echo { get; set; }
        public SummaryStatisticsDto JobPays { get; set; }
        public SummaryStatisticsDto JobID { get; set; }
        public SummaryStatisticsDto JobEvent { get; set; }
        public SummaryStatisticsDto JobEyes { get; set; }
        public SummaryStatisticsDto CaringBoss { get; set; }
    }
}
