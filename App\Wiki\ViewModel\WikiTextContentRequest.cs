using System;
using System.Collections.Generic;
using Jobid.App.Wiki.Enums;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Wiki.ViewModel
{
    /// <summary>
    /// Request model for adding text content to the Wiki without file upload
    /// </summary>
    public class WikiTextContentRequest
    {
        /// <summary>
        /// The text content to add to the Wiki
        /// </summary>
        public string TextContent { get; set; }

        /// <summary>
        /// Optional description for the text content
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Optional list of department IDs to associate with the text content
        /// </summary>
        public List<Guid> DepartmentIds { get; set; }

        /// <summary>
        /// Required batch number to identify the content group
        /// </summary>
        [Required(ErrorMessage = "Batch number is required")]
        public string BatchId { get; set; }

        /// <summary>
        /// Type of Wiki content (e.g., Training, Documentation)
        /// </summary>
        public WikiFileType WikiFileType { get; set; }
    }
}
