﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Jobid.App.AdminConsole.Models
{
    public class BasicInfo
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public Guid UserId { get; set; }

        public DateTime? DateOfHire { get; set; }

        public DateTime? LastDayOfContract { get; set; }

        public int LengthOfContract { get; set; }  // In months

        public string Location { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal GrossSalary { get; set; }

        public int AvailableNumberOfLeaveDays { get; set; }

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public string CreatedBy { get; set; }

        public DateTime? UpdatedOn { get; set; }

        public string UpdatedBy { get; set; }
        public string Subdomain { get; set; }
    }



    public class EmergencyInfo
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public Guid UserId { get; set; }

        public string EmergencyContactName { get; set; }

        public string EmergencyContactAddress { get; set; }

        public string EmergencyContactRelationship { get; set; }

        public string EmergencyContactPhoneNumber { get; set; }

        public string EmergencyContactAlternativePhone { get; set; }

        [EmailAddress]
        public string EmergencyContactEmailAddress { get; set; }

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;

        public string CreatedBy { get; set; }

        public DateTime? UpdatedOn { get; set; }

        public string UpdatedBy { get; set; }
        public string Subdomain { get; set; }

    }
}
