﻿using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class ValidCompanyEmailDomian : ValidationAttribute
    {
        public ValidCompanyEmailDomian() { }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var email = value as string;
            if (email != null)
            {
                string[] publicDomains = { "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "aol.com", "icloud.com", "ymail.com", "sharklasers.com", "grr.la", "yopmail.com" };
                string[] parts = email.Split('@');
                if (parts.Length != 2)
                {
                    return new ValidationResult("Email must be a valid email address");
                }

                string domain = parts[1].ToLower();
                if (publicDomains.Any(d => d.Equals(domain)))
                    return new ValidationResult("Email must be a custom company email address");

                return ValidationResult.Success;
            }

            return new ValidationResult("Company email address is required");
        }
    }
}
