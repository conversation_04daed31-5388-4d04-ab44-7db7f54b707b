﻿using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.JobProject.Models
{
    public class UserPerformanceActivityScoreView
    {
        public Guid Id { get; set; }
        public  string UserId { get; set; }
        public  DateTime StartDateForTheWeek { get; set; }
        public DateTime EndDateForTheWeek { get; set; }
        public double Monday { get; set; }
        public double Tuesday { get; set; }
        public double Wednesday { get; set; }
        public double Thursday { get; set; }
        public double Friday { get; set; }  
        public double TotalPercentage { get; set; }
        public DateTime CreatedDate { get; set; }

        [Column(TypeName = "varchar(24)")]
        public Industries Industries { get; set; }
    }
}