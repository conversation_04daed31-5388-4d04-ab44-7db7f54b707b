﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class UpdateCalenderDto
    {
        public Guid Id { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        public string Name { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndTime { get; set; }

        public DateTime EndDate { get; set; }

        [Required]
        public string Location { get; set; }

        public string MeetingDuration { get; set; }

        [Required]
        public bool MakeSchdulePrivate { get; set; }

        public NotifyMeVia NotifyMe { get; set; }

        public string Frequency { get; set; }

        [Required(ErrorMessage = "Invited Users cannot be null")]
        public List<string> InvitedUsers { get; set; }

        public MeetingStatus MeetingStatus { get; set; }

        [Required]
        public int NotifyMeInMinutes { get; set; }

        public List<string> ExternalTeamMemberEmails { get; set; }

        public int RescheduleCount { get; set; }

        [Required]
        public bool HasCustomFrequency { get; set; } = false;

        public ReoccuringDeleteOptions? ReoccuringDeleteOptions { get; set; }

        public CustomFrequencyDto CustomFrequency { get; set; }

        [JsonIgnore]
        public Guid UpdatedBy { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }

        public List<Base64VM>? AttachmentBase64 { get; set; }

        [JsonIgnore]
        public bool AIResheduleForMeetingsThatDidntHappen { get; set; }

        [JsonIgnore]
        public bool AIReschedule { get; set; }

        public string Token { get; set; }
    }
}
