﻿using Jobid.App.Helpers.Models;
using RestSharp;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface IApiCallService
    {
        Task<string> GetCountryRegionAsync(string countryName);
        Task<R> MakeApiCallAsync<P, R>(string baseUrl, string url, Method method, object body = null);
        Task<R> MakeApiCallGenericAsync<P, R>(string baseUrl, string path, Method method, object body = null, Dictionary<string, string> headers = null);
        Task<IpGeolocationResponse> GetCountryFromIpAsync(string ipAddress);
    }
}
