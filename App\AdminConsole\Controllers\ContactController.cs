using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Filters;
using Jobid.App.JobProjectManagement.Controllers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Dto.Contact;

namespace Jobid.App.AdminConsole.Controllers
{
    [CustomAuthorize]
    [EndpointTrackerFilter(Applications.Joble, ApplicationSection.Other)]
    [Produces("Application/json")]
    [ApiController]
    [Route("api/[controller]")]    
    public class ContactController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private string CurrentUserIdString => CurrentUserId?.ToString() ?? string.Empty;

        public ContactController(IUnitofwork unitofwork)
        {
            Services_Repo = unitofwork;
        }

        #region Create Contact
        /// <summary>
        /// Create a new contact
        /// </summary>
        /// <param name="contactDto">Contact creation data</param>
        /// <returns>Created contact</returns>
        [HttpPost]
        [Route("CreateContact")]
        public async Task<IActionResult> CreateContact([FromBody] CreateUserContactDto contactDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserContactDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            // Set userId from current user context
            contactDto.UserId = CurrentUserIdString;

            var result = await Services_Repo.ContactService.CreateContactAsync(contactDto);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Create Multiple Contacts
        /// <summary>
        /// Create multiple contacts
        /// </summary>
        /// <param name="contactsDto">List of contacts to create</param>
        /// <returns>List of created contacts</returns>
        [HttpPost]
        [Route("CreateContacts")]
        public async Task<IActionResult> CreateContacts([FromBody] BulkCreateContactsDto contactsDto)
        {
            if (!ModelState.IsValid || contactsDto.Contacts == null || !contactsDto.Contacts.Any())
            {
                return BadRequest(new ApiResponse<List<UserContactDto>>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state or empty contacts list",
                    Data = null
                });
            }

            // Set userId for all contacts from current user context
            foreach (var contact in contactsDto.Contacts)
            {
                contact.UserId = CurrentUserIdString;
            }

            var result = await Services_Repo.ContactService.CreateContactsAsync(contactsDto.Contacts);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Get Contacts - User
        /// <summary>
        /// Get contacts for the current user
        /// </summary>
        /// <returns>List of user contacts</returns>
        [HttpGet]
        [Route("GetUserContacts")]
        public async Task<IActionResult> GetUserContacts()
        {
            var result = await Services_Repo.ContactService.GetUserContactsAsync(CurrentUserIdString);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Have User Uploaded Contacts
        /// <summary>
        /// Check if user has uploaded contacts
        /// </summary>
        /// <returns>Boolean indicating if user has contacts</returns>
        [HttpGet]
        [Route("HasUserUploadedContacts")]
        public async Task<IActionResult> HasUserUploadedContacts()
        {
            var result = await Services_Repo.ContactService.HasUserUploadedContactsAsync(CurrentUserIdString);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Update Contact
        /// <summary>
        /// Update an existing contact
        /// </summary>
        /// <param name="contactDto">Contact update data</param>
        /// <returns>Updated contact</returns>
        [HttpPut]
        [Route("UpdateContact")]
        public async Task<IActionResult> UpdateContact([FromBody] UpdateUserContactDto contactDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<UserContactDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            // Set userId from current user context
            contactDto.UserId = CurrentUserIdString;

            var result = await Services_Repo.ContactService.UpdateContactAsync(contactDto);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Delete Contact
        /// <summary>
        /// Delete a contact
        /// </summary>
        /// <param name="contactId">Contact ID to delete</param>
        /// <returns>Deletion result</returns>
        [HttpDelete]
        [Route("DeleteContact/{contactId}")]
        public async Task<IActionResult> DeleteContact(Guid contactId)
        {
            var result = await Services_Repo.ContactService.DeleteContactAsync(contactId, CurrentUserIdString);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Get All Contacts
        /// <summary>
        /// Get all contacts with pagination
        /// </summary>
        /// <param name="model">Page number</param>
        /// <returns>Paginated contacts</returns>
        [HttpPost]
        [Route("GetAllContacts")]
        public async Task<IActionResult> GetAllContacts(GetAllContactsDto model)
        {
            if (model.PageNumber < 1 || model.PageSize < 1)
            {
                return BadRequest(new ApiResponse<PaginatedContactsDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Page number and page size must be greater than 0",
                    Data = null
                });
            }

            var result = model.UserIds != null && model.UserIds.Any() 
                ? await Services_Repo.ContactService.GetAllContactsAsync(model.PageNumber, model.PageSize, model.UserIds)
                : await Services_Repo.ContactService.GetAllContactsAsync(model.PageNumber, model.PageSize);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Get Contact by ID
        /// <summary>
        /// Get contact by ID
        /// </summary>
        /// <param name="contactId">Contact ID</param>
        /// <returns>Contact details</returns>
        [HttpGet]
        [Route("GetContactById/{contactId}")]
        public async Task<IActionResult> GetContactById(Guid contactId)
        {
            var result = await Services_Repo.ContactService.GetContactByIdAsync(contactId);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Upload Contacts
        /// <summary>
        /// Upload contacts from CSV or Excel file
        /// </summary>
        /// <param name="file">CSV or Excel file containing contacts</param>
        /// <returns>Upload result</returns>
        [HttpPost]
        [Route("UploadContacts")]
        public async Task<IActionResult> UploadContacts(IFormFile file)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new ApiResponse<ContactUploadResponseDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "No file provided",
                    Data = null
                });
            }

            var result = await Services_Repo.ContactService.UploadContactsAsync(file, CurrentUserIdString);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion        // Contact Group Operations        #region Create Contact Group

        #region Create Contact Group
        /// <summary>
        /// Create a new contact group
        /// </summary>
        /// <param name="groupDto">Group creation data</param>
        /// <returns>Created group</returns>
        [HttpPost]
        [Route("CreateGroup")]
        public async Task<IActionResult> CreateContactGroup([FromForm] CreateContactGroupDto groupDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<ContactGroupDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }            groupDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.ContactService.CreateContactGroupAsync(groupDto);
              if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Update Contact Group
        /// <summary>
        /// Update an existing contact group
        /// </summary>
        /// <param name="groupDto">Group update data</param>
        /// <returns>Updated group</returns>
        [HttpPut]
        [Route("UpdateGroup")]
        public async Task<IActionResult> UpdateContactGroup([FromBody] UpdateContactGroupDto groupDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<ContactGroupDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            groupDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.ContactService.UpdateContactGroupAsync(groupDto);
              if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Delete Contact Group
        /// <summary>
        /// Delete a contact group
        /// </summary>
        /// <param name="groupId">Group ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete]
        [Route("DeleteGroup/{groupId}")]
        public async Task<IActionResult> DeleteContactGroup(Guid groupId)
        {
            var result = await Services_Repo.ContactService.DeleteContactGroupAsync(groupId, CurrentUserIdString);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Get Contact Group by ID
        /// <summary>
        /// Get contact group by ID
        /// </summary>
        /// <param name="groupId">Group ID</param>
        /// <returns>Group details</returns>
        [HttpGet]
        [Route("GetGroupById/{groupId}")]
        public async Task<IActionResult> GetContactGroupById(Guid groupId)
        {
            var result = await Services_Repo.ContactService.GetContactGroupByIdAsync(groupId);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Get User Contact Groups
        /// <summary>
        /// Get contact groups for the current user with pagination
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated group list</returns>
        [HttpGet]
        [Route("GetUserGroups")]
        public async Task<IActionResult> GetUserContactGroups([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            if (pageNumber < 1 || pageSize < 1)
            {
                return BadRequest(new ApiResponse<PaginatedContactGroupsDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Page number and page size must be greater than 0",
                    Data = null
                });
            }

            var result = await Services_Repo.ContactService.GetUserContactGroupsAsync(CurrentUserIdString, pageNumber, pageSize);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Get All Contact Groups
        /// <summary>
        /// Get all contact groups with pagination
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Paginated group list</returns>
        [HttpGet]
        [Route("GetAllGroups")]
        public async Task<IActionResult> GetAllContactGroups([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            if (pageNumber < 1 || pageSize < 1)
            {
                return BadRequest(new ApiResponse<PaginatedContactGroupsDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Page number and page size must be greater than 0",
                    Data = null
                });
            }

            var result = await Services_Repo.ContactService.GetAllContactGroupsAsync(pageNumber, pageSize);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Add Contacts to Group
        /// <summary>
        /// Add contacts to a contact group
        /// </summary>
        /// <param name="updateDto">Group contacts update data</param>
        /// <returns>Updated group</returns>
        [HttpPost]
        [Route("AddContactsToGroup")]
        public async Task<IActionResult> AddContactsToContactGroup([FromBody] UpdateContactGroupContactsDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<ContactGroupDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            updateDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.ContactService.AddContactsToContactGroupAsync(updateDto);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Remove Contacts from Group
        /// <summary>
        /// Remove contacts from a contact group
        /// </summary>
        /// <param name="updateDto">Group contacts update data</param>
        /// <returns>Updated group</returns>
        [HttpPost]
        [Route("RemoveContactsFromGroup")]
        public async Task<IActionResult> RemoveContactsFromContactGroup([FromBody] UpdateContactGroupContactsDto updateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<ContactGroupDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            updateDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.ContactService.RemoveContactsFromContactGroupAsync(updateDto);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Update Group Status
        /// <summary>
        /// Update contact group status
        /// </summary>
        /// <param name="statusDto">Group status update data</param>
        /// <returns>Updated group</returns>
        [HttpPut]
        [Route("UpdateGroupStatus")]
        public async Task<IActionResult> UpdateContactGroupStatus([FromBody] UpdateContactGroupStatusDto statusDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<ContactGroupDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            statusDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.ContactService.UpdateContactGroupStatusAsync(statusDto);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);        }
        #endregion

        #region Get Group Statistics
        /// <summary>
        /// Get contact group statistics for the current user
        /// </summary>
        /// <returns>Group statistics</returns>
        [HttpGet]
        [Route("GetGroupStats")]
        public async Task<IActionResult> GetContactGroupStats()
        {
            var result = await Services_Repo.ContactService.GetContactGroupStatsAsync(CurrentUserIdString);
            
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Upload and Delete Group Files
        /// <summary>
        /// Upload a file to an existing contact group
        /// </summary>
        /// <param name="uploadDto">File upload data</param>
        /// <returns>Updated contact group</returns>
        [HttpPost]
        [Route("UploadGroupFile")]
        public async Task<IActionResult> UploadContactGroupFile([FromForm] UploadContactGroupFileDto uploadDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiResponse<ContactGroupDto>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid model state",
                    Data = null
                });
            }

            uploadDto.UserId = CurrentUserIdString;
            var result = await Services_Repo.ContactService.UploadContactGroupFileAsync(uploadDto);

            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);
        }

        /// <summary>
        /// Delete a file from an existing contact group
        /// </summary>
        /// <param name="groupId">Group ID</param>
        /// <returns>Updated contact group</returns>
        [HttpDelete]
        [Route("DeleteGroupFile/{groupId}")]
        public async Task<IActionResult> DeleteContactGroupFile(Guid groupId)
        {
            var deleteDto = new DeleteContactGroupFileDto
            {
                GroupId = groupId,
                UserId = CurrentUserIdString
            };

            var result = await Services_Repo.ContactService.DeleteContactGroupFileAsync(deleteDto);

            if (result.ResponseCode == "200")
                return Ok(result);
            else if (result.ResponseCode == "404")
                return NotFound(result);
            else
                return BadRequest(result);
        }

        #endregion
    }
}
