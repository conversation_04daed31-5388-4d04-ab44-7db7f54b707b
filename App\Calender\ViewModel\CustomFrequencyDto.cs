﻿using Jobid.App.Calender.Models;
using System;

namespace Jobid.App.Calender.ViewModel
{
    public class CustomFrequencyDto
    {
        public string RepeatEvery { get; set; }
        public int RepeatCount { get; set; }
        public string RepeatOn { get; set; }
        public EndStatus EndStatus { get; set; }
        public DateTime? EndsOn { get; set; }
        public int? EndsAfter { get; set; }
        public Guid CalenderMeetingId { get; set; }
    }
}
