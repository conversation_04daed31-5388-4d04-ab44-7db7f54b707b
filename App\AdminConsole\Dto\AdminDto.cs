﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;

namespace Jobid.App.AdminConsole.Dto
{
    public class AdminDto
    {
    }
    public class PackageMonthlyUsage
    {
        public string PackageName { get; set; }
        public Dictionary<string, int> MonthlyUsage { get; set; }
    }
    public class PackagesDto
    {
        public string Status { get; set; }
        public Applications PackageName { get; set; }
        public string PackagePlan { get; set; }
    }

    public class RecentActivitiesDto
    {
        public string Activities { get; set; }
        public string Date { get; set; }
        public string Time { get; set; }
    }

    public class DisplayUserDto
    {
        public string UserProfileId { get; set; }
        public string UserId { get; set; }
        public string ProfilePicture { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Role { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string LastSeen { get; set; }
        public DateTime DateRegistered { get; set; }
        public bool? FullAccess { get; set; }
    }

    public class EmployeeInformation
    {
        public string UserProfileId { get; set; }
        public string UserId { get; set; }
        public string ProfilePicture { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string Role { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string PhoneNo { get; set; }
        public string Country { get; set; }
        public string JobTitle { get; set; }
        public bool isTwoFactorEnabled { get; set; }

        //tenant
        public string TenantWorkSpace { get; set; }
        public string TenantCompanyAddress { get; set; }
        public string TenantBusinessRegistrationNumber { get; set; }
    }

    public class MostUsedPackages
    {
        public string Package { get; set; }
        public int NumberOfTimesUsed { get; set; }
        public string Month { get; set; }
    }
    public class SuspendUser
    {
        public string UserId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsIndefinite { get; set; }
        public string Message { get; set; }
    }
    public class RolesDto
    {
        public string RoleId { get; set; }
        public string RoleName { get; set; }
        public string PackageName { get; set; }
    }
    public class CreatePermission
    {
        public string PermissionName { get; set; }
        public string appName { get; set; }
        public string Category { get; set; }
    }
    public class RolesUsers
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string UsersEmail { get; set; }
        public string RoleName { get; set; }
    }
    public class UpdateRoleDto
    {
        public string RoleId { get; set; }
        public string NewName { get; set; }
    }
    public class UpdateAdminProfileDto
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string WorkSpace { get; set; }
        public string Subdomain { get; set; }
    }
    public class UpdateEmployeeRole
    {
        public string UserId { get; set; }
        public string RoleName { get; set; }
        public string appName { get; set; }
    }
    public class PermissionDto
    {
        public string PermissionId { get; set; }
        public string PermissionName { get; set; }
        public string PermissionCategory { get; set; }
        public bool IsAssociatedWithRole { get; set; }
        //public Dictionary<string,string> Permissions { get; set; }
    }

    public class PayRollVendors
    {
        public string Package { get; set; }
        public int PayRollVendorsCount { get; set; }
        public int PercentageOfVendors { get; set; }
        public int PayRollVendorsPerWeek { get; set; }
        public Dictionary<string, int> LocationDistribution { get; set; } = new Dictionary<string, int>();
    }
    public class PackageDashBoard
    {
        public Applications Package { get; set; }
        public int TotalActiveEmployees { get; set; }
        public int TotalActiveEmployeesThisWeek { get; set; }
        public int PercentageActiveEmployees { get; set; }
        public int TotalInActiveEmployees { get; set; }
        public int TotalInActiveEmployeesThisWeek { get; set; }
        public int PercentageInActiveEmployees { get; set; }
        public int TotalPendingInvitations { get; set; }
        public int TotalPendingInvitationsThisWeek { get; set; }
        public int PercentagePendingInvitations { get; set; }
    }
}
