using System;
using System.Threading.Tasks;
using Npgsql;
using Microsoft.Extensions.Logging;

namespace Jobid.App.Helpers.Utils
{
    public static class RetryHelper
    {
        /// <summary>
        /// Retries an operation when PostgreSQL serialization errors occur
        /// </summary>
        /// <param name="operation">The operation to retry</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="delayMs">Delay between retries in milliseconds</param>
        /// <param name="logger">Optional logger for retry attempts</param>
        /// <returns>True if operation succeeded, false otherwise</returns>
        public static async Task<bool> RetryOnSerializationError(
            Func<Task> operation, 
            int maxRetries = 3, 
            int delayMs = 1000,
            ILogger logger = null)
        {
            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                try
                {
                    await operation();
                    return true;
                }
                catch (PostgresException ex) when (ex.SqlState == "40001")
                {
                    if (attempt == maxRetries - 1)
                    {
                        logger?.LogError(ex, "Failed to complete operation after {MaxRetries} attempts due to serialization error", maxRetries);
                        throw;
                    }

                    logger?.LogWarning("Serialization error occurred on attempt {Attempt}/{MaxRetries}. Retrying in {DelayMs}ms...", 
                        attempt + 1, maxRetries, delayMs);
                    
                    await Task.Delay(delayMs + (attempt * 500)); // Exponential backoff
                }
            }
            return false;
        }

        /// <summary>
        /// Retries a synchronous operation when PostgreSQL serialization errors occur
        /// </summary>
        /// <param name="operation">The operation to retry</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="delayMs">Delay between retries in milliseconds</param>
        /// <param name="logger">Optional logger for retry attempts</param>
        /// <returns>True if operation succeeded, false otherwise</returns>
        public static bool RetryOnSerializationError(
            Action operation, 
            int maxRetries = 3, 
            int delayMs = 1000,
            ILogger logger = null)
        {
            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                try
                {
                    operation();
                    return true;
                }
                catch (PostgresException ex) when (ex.SqlState == "40001")
                {
                    if (attempt == maxRetries - 1)
                    {
                        logger?.LogError(ex, "Failed to complete operation after {MaxRetries} attempts due to serialization error", maxRetries);
                        throw;
                    }

                    logger?.LogWarning("Serialization error occurred on attempt {Attempt}/{MaxRetries}. Retrying in {DelayMs}ms...", 
                        attempt + 1, maxRetries, delayMs);
                    
                    Task.Delay(delayMs + (attempt * 500)).Wait(); // Exponential backoff
                }
            }
            return false;
        }

        /// <summary>
        /// Retries an operation with a return value when PostgreSQL serialization errors occur
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="operation">The operation to retry</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="delayMs">Delay between retries in milliseconds</param>
        /// <param name="logger">Optional logger for retry attempts</param>
        /// <returns>Result of the operation</returns>
        public static async Task<T> RetryOnSerializationError<T>(
            Func<Task<T>> operation, 
            int maxRetries = 3, 
            int delayMs = 1000,
            ILogger logger = null)
        {
            for (int attempt = 0; attempt < maxRetries; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (PostgresException ex) when (ex.SqlState == "40001")
                {
                    if (attempt == maxRetries - 1)
                    {
                        logger?.LogError(ex, "Failed to complete operation after {MaxRetries} attempts due to serialization error", maxRetries);
                        throw;
                    }

                    logger?.LogWarning("Serialization error occurred on attempt {Attempt}/{MaxRetries}. Retrying in {DelayMs}ms...", 
                        attempt + 1, maxRetries, delayMs);
                    
                    await Task.Delay(delayMs + (attempt * 500)); // Exponential backoff
                }
            }
            // This should never be reached due to throw above, but compiler requires it
            throw new InvalidOperationException("Retry loop completed without success or final exception");
        }
    }
}
