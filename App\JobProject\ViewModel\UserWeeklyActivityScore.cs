﻿namespace Jobid.App.JobProject.ViewModel
{
    public class UserWeeklyActivityScore
    {
        public string UserId { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public string ProfilePictureUrl { get; set; }
        public string Email { get; set; }
        public string Grade { get; set; }
        public double WeeklyPercentage { get; set; }
        public string Role { get; set; }
        public int Rank { get; set; }
    }

    public class CompanyWeeklyAnalytics
    {
        public string UserId { get; set; }
        public string CompanyLogo { get; set; }
        public string CompanyName { get; set; }
        public string Grade { get; set; }
        public double WeeklyPercentage { get; set; }
        public int Rank { get; set; }
    }
}