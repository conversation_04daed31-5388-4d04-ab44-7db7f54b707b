﻿using Jobid.App.JobProjectManagement.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Models
{
    public class ProjectFile
    {
        [Key]
        public Guid Id { get; set; }
        public string FileName { get; set; }
        public Guid? ProjectMgmt_ProjectId { get; set; }
        public ProjectMgmt_Project? projectMgmt_Project { get; set; }
        public Guid? ProjectMgmt_TodoId { get; set; }
        public ProjectMgmt_Todo? projectMgmt_Todo { get; set; }
        public Guid? SprintProjectId { get; set; }
        public SprintProject? SprintProject { get; set; }

        public Guid? TimeSheetId { get; set; }
        public TimeSheet? TimeSheet  { get; set; }


    }
}
