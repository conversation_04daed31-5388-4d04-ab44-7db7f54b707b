﻿using Elastic.CommonSchema;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.Contract;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class _2FAService : I2FAService
    {
        private readonly JobProDbContext _publicSchemaContext;
        private readonly JobProDbContext _subdomainSchemaContext;
        private readonly IConfiguration _config;
        private readonly IOTPServices _oTPServices;
        private readonly IAWSS3Sevices _awsS3Service;

        public _2FAService(
            JobProDbContext publicSchemaContext,
            JobProDbContext subdomainSchemaContext,
            IConfiguration config,
            IOTPServices oTPServices,
            IAWSS3Sevices awsS3Service)
        {
            _publicSchemaContext = publicSchemaContext;
            _subdomainSchemaContext = subdomainSchemaContext;
            _config = config;
            _oTPServices = oTPServices;
            _awsS3Service = awsS3Service;
        }

        #region Set up 2FA
        public async Task<GenericResponse> Setup2FA(TwoFactorSetupRequest model)
        {
            // Check of the email is a personal email or a company email
            if (model.Email.IsPersonalEmail())
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = "Personal email not allowed"
                };
            }

            var userProfile = await _subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(u => u.Email == model.Email);
            if (userProfile == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = "User profile not found"
                };
            }

            if (model.options == AdminConsole.Enums._2FAOptions.AuthenticatorApp)
            {
                // Get company name using subdomain
                var companyProfile = await _publicSchemaContext.Tenants
                    .FirstOrDefaultAsync(c => c.Subdomain == model.Subdomain.ToLower());

                var issuer = _config.GetValue<string>("2FAIssuer") + ": " + companyProfile.CompanyName;
                var secretKey = TwoFactorHelper.GenerateSecretKey();

                // Get company logo URL if available
                string logoUrl = null;
                if (!string.IsNullOrEmpty(companyProfile.LogoUrl))
                {
                    logoUrl = await _awsS3Service.GetSignedUrlAsync(companyProfile.LogoUrl);
                }

                var qrCodeImage = TwoFactorHelper.GenerateQrCodeImage(model.Email, secretKey, issuer, logoUrl);

                // Save temporarily (or return to client for confirmation step)
                userProfile.TwoFactorSecretKey = secretKey;
                _subdomainSchemaContext.Update(userProfile);
                await _subdomainSchemaContext.SaveChangesAsync();

                var data = new TwoFactorSetupResponse
                {
                    QrCodeImageUrl = qrCodeImage,
                    ManualEntryKey = secretKey,
                    SetupInstructions = GenerateManualSetupInstructions(secretKey, issuer)
                };

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "2FA setup successful",
                    Data = data
                };
            }
            else if (model.options == AdminConsole.Enums._2FAOptions.SMS)
            {
                var result = await _oTPServices.GenerateToken(new NewOTPVM
                {
                    Identifier = userProfile.PhoneNumber,
                    IdentifierType = OTPIdentifierType.Phone,
                    TokenType = OTPTokenType._2FA,
                }, 4, OTPTokenType._2FA);

                return new GenericResponse
                {
                    ResponseCode = result ? "200" : "400",
                    ResponseMessage = result ? "2FA setup initiated successful. An SMS has been sent to your phone number" : "Could not setup 2FA, please try again later",
                };
            }
            else if (model.options == AdminConsole.Enums._2FAOptions.Email)
            {
                var result = await _oTPServices.GenerateToken(new NewOTPVM
                {
                    Identifier = model.Email,
                    IdentifierType = OTPIdentifierType.Email,
                    TokenType = OTPTokenType._2FA,
                }, 4, OTPTokenType._2FA);

                return new GenericResponse
                {
                    ResponseCode = result ? "200" : "400",
                    ResponseMessage = result ? "2FA setup initiated successful. An email has been sent to your email address" : "Could not setup 2FA, please try again later",
                };
            }
            else
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid 2FA option"
                };
            }
        }
        #endregion

        #region Enable 2FA
        public async Task<GenericResponse> Enable2FA(TwoFactorVerificationRequest model)
        {
            var userProfile = await _subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == model.UserId);
            if (userProfile == null)
                return new GenericResponse { ResponseCode = "400",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE, DevResponseMessage = "User profile not found" };

            if (model.options == AdminConsole.Enums._2FAOptions.AuthenticatorApp)
            {
                if (!TwoFactorHelper.ValidateCode(userProfile.TwoFactorSecretKey, model.Code))
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "Could not validate code, please try again later" };

                userProfile._2FAOptions = AdminConsole.Enums._2FAOptions.AuthenticatorApp;
            }
            else if (model.options == AdminConsole.Enums._2FAOptions.SMS)
            {
                var phoneToken = _oTPServices.GetToken(userProfile.PhoneNumber, OTPIdentifierType.Phone, OTPTokenType._2FA, "verified");
                if (phoneToken == null)
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "Could not verify otp, please try again later" };

                userProfile._2FAOptions = AdminConsole.Enums._2FAOptions.SMS;
            }
            else if (model.options == AdminConsole.Enums._2FAOptions.Email)
            {
                var emailToken = _oTPServices.GetToken(userProfile.Email, OTPIdentifierType.Email, OTPTokenType._2FA, "verified");
                if (emailToken == null)
                    return new GenericResponse { ResponseCode = "400", ResponseMessage = "Could not verify otp, please try again later" };

                userProfile._2FAOptions = AdminConsole.Enums._2FAOptions.Email;
            }
            else
            {
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "Invalid 2FA option" };
            }

            _subdomainSchemaContext.UserProfiles.Update(userProfile);
            var dbResult = await _subdomainSchemaContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = dbResult > 0 ? "200" : "400",
                ResponseMessage = dbResult > 0 ? "2FA enabled successfully" : "Could not enable 2FA, please try again later",
            };
        }
        #endregion

        #region Disable 2FA
        public async Task<GenericResponse> Disable2FA(string userId)
        {
            var userProfile = await _subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (userProfile == null)
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = "User profile not found"
                };

            userProfile._2FAOptions = AdminConsole.Enums._2FAOptions.None;
            _subdomainSchemaContext.UserProfiles.Update(userProfile);
            var dbResult = await _subdomainSchemaContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = dbResult > 0 ? "200" : "400",
                ResponseMessage = dbResult > 0 ? "2FA disabled successfully" : "Could not disabled 2FA, please try again later",
            };
        }
        #endregion

        #region Verify 2FA
        public async Task<bool> Verify2FA(string userId, string code)
        {
            var userProfile = await _subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(x => x.UserId == userId);
            if (userProfile == null)
                return false;

            if (!TwoFactorHelper.ValidateCode(userProfile.TwoFactorSecretKey, code))
                return false;

            return true;
        }
        #endregion

        #region Private Methods
        private string GenerateManualSetupInstructions(string secretKey, string issuer)
        {
            return $"To set up 2FA without scanning the QR code:\n" +
                   $"1. Open your authenticator app (Google Authenticator, Microsoft Authenticator, Authy etc.)\n" +
                   $"2. Select the option to add a new account manually\n" +
                   $"3. Enter the following details:\n" +
                   $"   - Key: {secretKey}\n" +
                   $"   - Issuer: {issuer}\n" +
                   $"   - Type: Time-based (TOTP)\n" +
                   $"   - Digits (Optional): 6\n";
        }
        #endregion
    }
}
