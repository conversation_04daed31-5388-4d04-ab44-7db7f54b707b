using Hangfire;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Extensions
{
    /// <summary>
    /// Extension methods for Hangfire to improve database connection management
    /// </summary>
    public static class HangfireExtensions
    {
        /// <summary>
        /// Enqueues a job that uses a database connection with proper disposal
        /// </summary>
        /// <param name="client">The background job client</param>
        /// <param name="action">The action to execute</param>
        /// <param name="connectionString">The connection string to use</param>
        /// <param name="subdomain">The tenant subdomain (if applicable)</param>
        /// <returns>The job ID</returns>
        public static string EnqueueDatabaseJob(
            this IBackgroundJobClient client,
            Action<JobProDbContext> action,
            string connectionString,
            string subdomain = null)
        {
            return BackgroundJob.Enqueue<DatabaseJobExecutor>(x => 
                x.ExecuteDbJob(action, connectionString, subdomain));
        }

        /// <summary>
        /// Enqueues an async job that uses a database connection with proper disposal
        /// </summary>
        /// <param name="client">The background job client</param>
        /// <param name="action">The async action to execute</param>
        /// <param name="connectionString">The connection string to use</param>
        /// <param name="subdomain">The tenant subdomain (if applicable)</param>
        /// <returns>The job ID</returns>
        public static string EnqueueDatabaseJobAsync(
            this IBackgroundJobClient client,
            Func<JobProDbContext, Task> action,
            string connectionString,
            string subdomain = null)
        {
            return BackgroundJob.Enqueue<DatabaseJobExecutor>(x => 
                x.ExecuteDbJobAsync(action, connectionString, subdomain));
        }
    }

    /// <summary>
    /// Helper service for executing database jobs
    /// </summary>
    public class DatabaseJobExecutor
    {
        private readonly ILogger<DatabaseJobExecutor> _logger;

        public DatabaseJobExecutor(ILogger<DatabaseJobExecutor> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Executes a database job with proper connection handling
        /// </summary>
        public void ExecuteDbJob(Action<JobProDbContext> action, string connectionString, string subdomain)
        {
            var schema = subdomain != null ? new DbContextSchema(subdomain) : null;
            using var context = schema != null 
                ? new JobProDbContext(connectionString, schema) 
                : new JobProDbContext(connectionString);
            
            try
            {
                action(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing database job");
                throw;
            }
        }

        /// <summary>
        /// Executes an async database job with proper connection handling
        /// </summary>
        public async Task ExecuteDbJobAsync(Func<JobProDbContext, Task> action, string connectionString, string subdomain)
        {
            var schema = subdomain != null ? new DbContextSchema(subdomain) : null;
            await using var context = schema != null 
                ? new JobProDbContext(connectionString, schema) 
                : new JobProDbContext(connectionString);
            
            try
            {
                await action(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing async database job");
                throw;
            }
        }
    }
}
