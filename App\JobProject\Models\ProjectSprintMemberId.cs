﻿using Jobid.App.Helpers.Enums;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.Models
{
    public class ProjectSprintMemberId
    {
        [Key]
        public Guid Id { get; set; }
        public string SprintId { get; set; }
        public string MemberId { get; set; }
        public string ExternalMemberEmail { get; set; }
        [Column(TypeName = "decimal(18,4)")]
        public decimal? AmountPerHour { get; set; }
        public string CurrencySymbol { get; set; } = Currency.USD.ToString();
    }
}
