﻿using Jobid.App.Helpers.Services.Contract;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using System.IO;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    // Using QuestPdf for PDF generation
    public class PdfGeneratorServices : IPdfGeneratorServices
    {
        public PdfGeneratorServices()
        {
            QuestPDF.Settings.License = QuestPDF.Infrastructure.LicenseType.Community;
        }

        public async Task<byte[]> GenerateMeetingNotesPdfAsBytes()
        {
            using var stream = new MemoryStream();

            Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Margin(50);

                    page.Header().Text("QuestPDF Byte[] Example")
                        .FontSize(20).Bold().FontColor(Colors.Blue.Medium);

                    page.Content().Column(col =>
                    {
                        col.Item().Text("This PDF was generated into memory and returned as byte[].");
                    });

                    page.Footer().AlignCenter().Text("Generated dynamically - QuestPDF");
                });
            }).GeneratePdf(stream);

            return stream.ToArray();
        }
    }
}
