﻿using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Jobid.App.Calender.Services.Implementations.CalenderService;

namespace Jobid.App.Calender.Contracts
{
    public interface IBackGroundService
    {
        Task EmailReminderBefore(Guid bookedExternalMeetId, DateTime bookedTime, string subdomain, SendMailRecord record);
        Task EmailReminderAfter(string email, ExternalMeeting externalMeeting, BookedExternalMeeting bookedExternalMeet, DateTime bookedTime, string subdoamin, bool isReschduled);
        Task SendNotificationsForReoccurringMeetings(string template, string email, string meetingId, string subdomain);
        Task AddMeetingSubsequentDates(CalenderVm calenderVm, CalenderMeeting meeting);
        Task UpdateExternalMeetingTimeBreakDown(List<PersonalSchedule> personalSchedules, string subdomain, DateTime canBookEndDate);
        Task ResheduleMeetingsThatDidntHappen(List<string> subdomains);
    }
}
