using System.Collections.Generic;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Services;

namespace Jobid.App.AdminConsole.Contract
{
    /// <summary>
    /// Interface for Twilio-only conference service
    /// </summary>
    public interface ITwilioConferenceService
    {
        /// <summary>
        /// Create a new Twilio conference room
        /// </summary>
        Task<string> CreateConference(string conferenceName, int maxParticipants = 10);

        /// <summary>
        /// Add PSTN participant to conference
        /// </summary>
        Task<string> AddPstnParticipant(string conferenceName, string phoneNumber, string fromNumber);

        /// <summary>
        /// Generate access token for web participants
        /// </summary>
        string GenerateWebAccessToken(string identity, string conferenceName, int ttlMinutes = 60);

        /// <summary>
        /// Add web participant to conference
        /// </summary>
        Task<string> AddWebParticipant(string conferenceName, string identity, string displayName);

        /// <summary>
        /// Remove participant from conference
        /// </summary>
        Task RemoveParticipant(string conferenceName, string participantId);

        /// <summary>
        /// End conference and disconnect all participants
        /// </summary>
        Task EndConference(string conferenceName);

        /// <summary>
        /// Get active conference sessions for monitoring
        /// </summary>
        Task<object> GetActiveConferences();

        /// <summary>
        /// Get conference details
        /// </summary>
        Task<ConferenceSession> GetConference(string conferenceName);

        /// <summary>
        /// Handle Twilio conference status callbacks
        /// </summary>
        Task HandleConferenceStatusCallback(string conferenceSid, string statusCallbackEvent, Dictionary<string, string> parameters);
    }
}
