﻿#region Using Statement
using Hangfire;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant;
using Jobid.App.Tenant.Contract;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using static Jobid.App.JobProject.Enums.Enums;
using static Jobid.App.Helpers.Utils.Extensions;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.Subscription.Enums;
using Jobid.App.Helpers.Services.Contract;
#endregion

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class ProjectServices : IProjectServices
    {
        #region Properties and Constructor
        private JobProDbContext Db;
        public JobProDbContext Dbo;
        private IEmailService _emailService;
        private readonly ITenantService _tenantService;
        private readonly ICompanyUserInvite _companyUserInvite;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IWebHostEnvironment _environment;
        private readonly IAdminService _adminService;

        public ProjectServices(JobProDbContext _db, JobProDbContext publicSchemaContext, IEmailService emailService, ITenantService tenantService, ICompanyUserInvite companyUserInvite, IAWSS3Sevices aWSS3Sevices, IWebHostEnvironment environment, IAdminService adminService)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _emailService = emailService;
            _tenantService = tenantService;
            _companyUserInvite = companyUserInvite;
            _aWSS3Sevices = aWSS3Sevices;
            _environment = environment;
            _adminService = adminService;
        }
        #endregion

        #region Add Project
        /// <summary>
        /// Add Project
        /// </summary>
        /// <param name="modelDto"></param>
        /// <param name="userId"></param>
        /// <returns></returns>

        public async Task<ProjectMgmt_Project> Add_Project(AddProjectDto modelDto, string userId, string subDomain)
        {
            if (modelDto.IsBillable && (modelDto.AmountPerSelectedFrequency == null || modelDto.AmountPerSelectedFrequency <= 0
                 || modelDto.ExpectedProjectValue == null || modelDto.ExpectedProjectValue <= 0))
                throw new DirtyFormException("Amount per selected frequency and project value are required, rate and value should not be less than or equal to zero");
            if (modelDto.StartDate == null || modelDto.EndDate == null)
                throw new DirtyFormException("Project Startdate or EndDate cannot be null.");
            modelDto.Name = modelDto.Name.ToTitleCase();

            using var transaction = await Db.Database.BeginTransactionAsync();
            using var publicTransaction = await Dbo.Database.BeginTransactionAsync();
            try
            {
                // Check if a project with the project name already exists
                var projectExists = await Db.ProjectMgmt_Projects.AnyAsync(p => p.Name.ToLower() == modelDto.Name.ToLower());
                if (projectExists)
                    throw new RecordAlreadyExistException("Project with the supplied project name already exists");

                var days = (int)(modelDto.EndDate - modelDto.StartDate).TotalDays;
                var existingTenantClients = await Db.TenantClients.Select(x => x.ClientName).ToListAsync();
                var clients = "";
                foreach (var c in modelDto.Clients)
                {
                    if (!existingTenantClients.Contains(c, StringComparer.OrdinalIgnoreCase))
                        Db.TenantClients.Add(new TenantClient() { ClientName = c });
                    clients += c + ",";
                }
                clients = clients.Remove(clients.Length - 1); //Removing the last comma in the string
                var createdTime = GetAdjustedDateTimeBasedOnTZNow();
                ProjectMgmt_Project pro = new()
                {
                    CreatedBy = userId,
                    CreatedTime = createdTime,
                    LastUpdate = GetAdjustedDateTimeBasedOnTZNow(),
                    Name = modelDto.Name,
                    StartDate = modelDto.StartDate,
                    EndDate = modelDto.EndDate,
                    CurrencySymbol = modelDto.CurrencySymbol.ToString(),
                    AmountFrequency = modelDto.AmountFrequency,
                    Duration = Utility.DateDurationCalculator(days),
                    ProjectStatus = modelDto.ProjectStatus,
                    IsBillable = modelDto.IsBillable,
                    AmountPerSelectedFrequency = modelDto.AmountPerSelectedFrequency,
                    ExpectedProjectValue = modelDto.ExpectedProjectValue,
                    Clients = clients
                };

                await Db.ProjectMgmt_Projects.AddAsync(pro);
                var res = await Db.SaveChangesAsync();

                TenantProjectView projectView = new()
                {
                    Subdomain = Db.Schema,
                    CreatedTime = createdTime,
                    ProjectId = pro.ProjectId
                };

                await Dbo.TenantProjectViews.AddAsync(projectView);
                await Dbo.SaveChangesAsync();

                await transaction.CommitAsync();
                await publicTransaction.CommitAsync();

                return res > 0 ? pro : null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                await transaction.RollbackAsync();
                await publicTransaction.RollbackAsync();
                throw;
            }
        }

        #endregion

        #region Activate Project
        public async Task<bool> MakeProjectActive(string projectId)
        {
            var project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId);
            if (project == null)
                throw new RecordNotFoundException("Project not found");

            project.ProjectStatus = ProjectStatus.Active;
            project.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
            Db.ProjectMgmt_Projects.Update(project);
            var res = await Db.SaveChangesAsync();

            return res > 0;
        }
        #endregion  

        #region Project Analytic Data
        /// <summary>
        /// This method gets the project analytic data and last project details for company dashboard
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetProjectAnalyticData(string projectId, DateTime startDate, DateTime endDate)
        {
            List<string> projectIds = null;
            var proId = "";
            List<ProjectMgmt_Todo> todos = null;

            if (string.IsNullOrEmpty(projectId))
            {
                projectIds = await Db.ProjectMgmt_Projects
                    .Where(x => x.CreatedTime >= startDate && x.CreatedTime <= endDate)
                    .Select(x => x.ProjectId.ToString()).ToListAsync();
                todos = await Db.ProjectMgmt_Todo.ToListAsync();
            }
            else
            {
                proId = await Db.ProjectMgmt_Projects
                    .Where(x => x.ProjectId.ToString() == projectId)
                    .Select(x => x.ProjectId.ToString())
                    .FirstOrDefaultAsync();
                todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == proId).ToListAsync();
            }

            var response = new Dictionary<string, Dictionary<string, int>>();
            var months = new List<string> { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
            var monthsInt = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };
            var stages = new List<string> { ProjectManagementStatus.InProgress.ToString(), ProjectManagementStatus.Completed.ToString(), ProjectManagementStatus.OverDue.ToString() };

            foreach (var stage in stages)
            {
                foreach (var month in months)
                {
                    var todoCount = 0;
                    var monthInt = monthsInt[months.IndexOf(month)];
                    if (stage == ProjectManagementStatus.InProgress.ToString())
                        todoCount = todos.Count(x => x.CreatedDate.Month == monthInt && x.TodoStatus == ProjectManagementStatus.InProgress.ToString());
                    if (stage == ProjectManagementStatus.Completed.ToString())
                        todoCount = todos.Count(x => x.CompletedAt.HasValue ? x.CompletedAt.Value.Month == monthInt : x.Id.ToString() == "" && x.TodoStatus == ProjectManagementStatus.Completed.ToString());
                    if (stage == ProjectManagementStatus.OverDue.ToString())
                        todoCount = todos.Count(x => x.EndTime.AddDays(1).Month == monthInt && x.TodoStatus == ProjectManagementStatus.OverDue.ToString());

                    if (!response.ContainsKey(stage))
                        response.Add(stage, new Dictionary<string, int>
                            {
                                { month, todoCount }
                            });
                    else
                    {
                        response[stage][month] = todoCount;
                    }
                }
            }

            // Sprint count under the project
            var sprintCount = await Db.SprintProjects.CountAsync(x => x.ProjectMgmt_ProjectId.ToString() == projectId);

            // Get the last project and calculate the percentage of completion
            var lastProjectPercentageCompleted = 0;
            var lastProject = await Db.ProjectMgmt_Projects.OrderByDescending(x => x.CreatedTime).FirstOrDefaultAsync();
            var projectMembers = await GetProjectMembers(lastProject.ProjectId);
            var lastProjectTodosCount = await Db.ProjectMgmt_Todo.CountAsync(x => x.ProjectMgmt_ProjectId == lastProject.ProjectId);
            var lastProjectCompletedTodosCount = await Db.ProjectMgmt_Todo.CountAsync(x => x.ProjectMgmt_ProjectId == lastProject.ProjectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString());
            if (lastProjectTodosCount > 0 && lastProjectCompletedTodosCount > 0)
                lastProjectPercentageCompleted = lastProjectCompletedTodosCount * 100 / lastProjectTodosCount;

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Analytic data retrieved successfully",
                Data = new
                {
                    ProjectAnalyticsData = response,
                    LastProjectDetails = new
                    {
                        Project = lastProject,
                        ProjectMembers = projectMembers,
                        SprintCount = sprintCount,
                        PercentageCompleted = lastProjectPercentageCompleted.ToString() + "%"
                    }
                }
            };
        }
        #endregion

        #region Edit Project - Not used for now
        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userid"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public async Task<bool> Edit_Project(ProjectMgmt_ProjectVM model, string userid, string projectId)
        {
            if (model.IsBillable && (model.AmountPerSelectedFrequency == null || model.AmountPerSelectedFrequency <= 0
                || model.ExpectedProjectValue == null || model.ExpectedProjectValue <= 0))
                throw new DirtyFormException("Amount per selected frequency and project value are required");
            // Get the tenantId
            var tenantId = (await _tenantService.GetTenantBySubdomain(model.SubDomain)).Id;

            // Get the project to be edited
            var project = await Db.ProjectMgmt_Projects
                .Where(x => x.ProjectId.ToString() == projectId).FirstOrDefaultAsync();

            var days = (int)(model.EndDate - model.StartDate).TotalDays;

            // Get the invitee name
            var invitee = Db.UserProfiles.Where(x => x.UserId == userid)
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            project.UpdatedBy = userid;
            project.CreatedTime = GetAdjustedDateTimeBasedOnTZNow();
            project.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
            project.Name = model.Name;
            project.StartDate = model.StartDate;
            project.IsBillable = model.IsBillable;
            project.AmountPerSelectedFrequency = model.AmountPerSelectedFrequency;
            project.EndDate = model.EndDate;
            project.Description = model.Description;
            project.Duration = Utility.DateDurationCalculator(days);
            project.Summary = model.Summary;
            project.CreatedBy = userid;
            project.ProjectStatus = model.EndDate > GetAdjustedDateTimeBasedOnTZNow() ? ProjectStatus.Active : project.ProjectStatus;


            var users = new List<User>();
            var userEmailList = new List<string>();
            var projUser = new List<ProjectMgmt_ProjectUser>();
            if (model.Members.Any() && !string.IsNullOrEmpty(model.Members[0]))
            {
                foreach (var member in model.Members)
                {
                    var user = await Db.Users.Where(x => x.Id == member).FirstOrDefaultAsync();

                    if (user != null && !users.Any(x => x.Id == user.Id))
                    {
                        userEmailList.Add(user.Email);
                        var proj = new ProjectMgmt_ProjectUser()
                        {
                            ProjectMgmt_ProjectId = project.ProjectId,
                            UserId = member
                        };
                        projUser.Add(proj);
                    }
                }

                Db.projectMgmt_ProjectUsers.AddRange(projUser);
            }

            var tagList = new List<ProjectTag>() { };
            if (model.Tags.Any() && !string.IsNullOrEmpty(model.Tags[0]))
            {
                var tags = model.Tags.Select(x => new ProjectTag()
                {
                    TagName = x,
                    ProjectMgmt_ProjectId = project.ProjectId,
                }).ToList();

                tagList.AddRange(tags);
            }

            Db.ProjectTag.AddRange(tagList);
            //project.projectMgmt_ProjectUsers = projUser;

            Db.ProjectMgmt_Projects.Update(project);
            int result = await Db.SaveChangesAsync();

            if (result > 0)
            {
                // Send email notification to added member
                var inviteUrl = string.Format("https://{0}.jobpro.app/auth/invited-user?invitee={1}&tenantId={2}", model.SubDomain, invitee, tenantId);

                var template = await _emailService.GetMailTemplate();
                template = template.Replace("{company}", invitee).Replace("{proj}", project.Name).Replace("{name}", invitee)
                    .Replace("{url}", inviteUrl);

                BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(template, userEmailList, "Project Addition"));

                // Send mail to external memebers
                if (model.ExternalMembersEmails.Any())
                {
                    // Add an invite to the database                 
                    foreach (var mail in model.ExternalMembersEmails)
                    {
                        var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                        {
                            Email = mail,
                            Application = Applications.Joble
                        }, tenantId.ToString());
                    }

                    string subject = "Invitation to JobPro - New Project Alert";
                    var taskId = BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(template, model.ExternalMembersEmails, subject));
                }

                // Upload file to S3
                if (model.UploadFile.Count > 0)
                {
                    var projectFiles = new List<ProjectFile>();
                    foreach (var file in model.UploadFile)
                    {
                        Guid guid = Guid.NewGuid();
                        var fileTrimmed = file.FileName.Replace(" ", "");
                        var fileName = guid.ToString()
                                                .Replace('-', '0')
                                                .Replace('_', '0')
                                                .ToUpper() + "-" + fileTrimmed;

                        var imageUrl = await _aWSS3Sevices.UploadFileAsync(file, fileName);

                        var projectFile = new ProjectFile()
                        {
                            FileName = fileName,
                            ProjectMgmt_ProjectId = project.ProjectId
                        };
                        projectFiles.Add(projectFile);

                        if (string.IsNullOrEmpty(imageUrl))
                            throw new FileUploadException("Project edited successfully but File upload failed");
                    }

                    Db.ProjectFile.AddRange(projectFiles);
                    var res = await Db.SaveChangesAsync();

                    if (res <= 0)
                        throw new FileUploadException("Project edited successfully but File upload failed");
                }

                return true;
            }

            return false;
        }
        #endregion

        #region Get Uploaded files for a project using projectId
        /// <summary>
        /// Get uploaded files for a project sing projectId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<List<ProjectFile>> GetUplaodedProjectFiles(string projectId)
        {
            // Check if the project exists
            var pro = Db.ProjectMgmt_Projects.FirstOrDefault(x => x.ProjectId.Equals(projectId));
            if (pro == null)
                throw new RecordNotFoundException("Project not found");

            // Get the uploaeded files
            return await Db.ProjectFile.Where(x => x.ProjectMgmt_ProjectId.Equals(projectId)).ToListAsync();
        }
        #endregion

        #region Super Search for Project, Sprint and Todos
        /// <summary>
        /// Super Search for Project and Todos
        /// </summary>
        /// <param name="nameParam"></param>
        /// <returns></returns>
        public async Task<GenericResponse> SearchAllProjectAndTodoAndSprint(string nameParam)
        {
            var projectSearchResult = await Db.ProjectMgmt_Projects
                .Where(x => x.Name.ToLower().Contains(nameParam.ToLower())).ToListAsync();
            var todoSearchResult = await Db.ProjectMgmt_Todo
                .Where(x => x.TodoDescription.ToLower().Contains(nameParam.ToLower()) || x.TodoSummary.ToLower().Contains(nameParam.ToLower())).ToListAsync();

            var sprints = await Db.SprintProjects.Where(x => x.Name.ToLower().Contains(nameParam.ToLower())).ToListAsync();

            var response = new GenericResponse()
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = new
                {
                    Projects = projectSearchResult,
                    Todos = todoSearchResult,
                    Sprints = sprints
                }
            };

            return response;
        }
        #endregion

        #region Get all projects
        /// <summary>
        /// Get All Projects
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public async Task<Page<ProjectMgmt_Project>> GetAllProjectMgmt_Projects(PaginationParameters parameters)
        {
            parameters.StartDate = parameters.StartDate ?? new DateTime(2023, 01, 01);
            parameters.EndDate = parameters.EndDate ?? new DateTime(2050, 12, 30);
            var projects = await Db.ProjectMgmt_Projects
                .Where(p => p.CreatedTime >= parameters.StartDate && p.CreatedTime <= parameters.EndDate)
                .OrderByDescending((x) => x.CreatedTime).Include(x => x.ProjectTags).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

            foreach (var project in projects.Items)
            {
                // Calcaulate the percentage of completion for the project
                var sprintIds = await Db.SprintProjects
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).Select(x => x.Id.ToString()).ToListAsync();
                var todos = await Db.ProjectMgmt_Todo
                    .Where(x => sprintIds.Contains(x.SprintProjectId.Value.ToString())).ToListAsync();
                var totalTodosCount = todos.Count;
                var totalCompletedTodosCount = todos.Count(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString());
                var percentageCompletedInt = totalTodosCount > 0 ? totalCompletedTodosCount * 100 / totalTodosCount : 0;

                project.PercentageCompleted = percentageCompletedInt.ToString() + "%";
            }

            return projects;
        }
        #endregion

        #region Get all projects with their sprints
        /// <summary>
        /// Get all projects with their sprints
        /// </summary>
        /// <returns></returns>
        public async Task<GenericResponse> GetAllProjectsWithSprints()
        {
            var projects = await Db.ProjectMgmt_Projects.ToListAsync();
            var projectDtos = new List<ProjectDto>();
            foreach (var project in projects)
            {
                var sprints = await Db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();
                var sprintDtos = new List<SprintDto>();
                foreach (var sprint in sprints)
                {
                    sprintDtos.Add(new SprintDto
                    {
                        Id = sprint.Id.ToString(),
                        Name = sprint.Name
                    });
                }

                projectDtos.Add(new ProjectDto
                {
                    Id = project.ProjectId.ToString(),
                    Name = project.Name,
                    Sprints = sprintDtos
                });
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = projectDtos
            };
        }
        #endregion

        #region Get projects by userId
        /// <summary>
        /// Get projects by userId
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="paginationParameters"></param>
        /// <returns></returns>
        public async Task<Page<ProjectMgmt_Project>> GetProjectByUserId(string userId, PaginationParameters paginationParameters)
        {
            var projectIds = new List<string>();
            var userEmail = await Db.UserProfiles.Where(x => x.UserId.Equals(userId)).Select(u => u.Email).FirstOrDefaultAsync();

            // Get the todos for the user using the email address
            var todoIds = await Db.projectMgmt_TodoUsers.Where(x => x.ExternalMemberEmail.Equals(userEmail) || x.UserId == userId)
                .Select(u => u.ProjectMgmt_TodoId).ToListAsync();
            if (todoIds.Any())
                projectIds = await Db.ProjectMgmt_Todo.Where(x => todoIds.Contains(x.Id)).Select(u => u.ProjectMgmt_ProjectId.ToString()).ToListAsync();

            // Get the sprints for the user using the email address and userId
            var sprintIds = await Db.ProjectSprintMemberIds.Where(x => x.MemberId.Equals(userId) || x.ExternalMemberEmail.Equals(userEmail))
                .Select(u => u.SprintId).ToListAsync();
            if (sprintIds.Any())
                projectIds.AddRange(await Db.SprintProjects.Where(x => sprintIds.Contains(x.Id.ToString())).Select(u => u.ProjectMgmt_ProjectId.ToString()).ToListAsync());

            // Get projectids of projects created by the user
            var createdProjectIds = await Db.ProjectMgmt_Projects
                .Where(x => x.CreatedBy == userId).Select(u => u.ProjectId.ToString()).ToListAsync();
            projectIds.AddRange(createdProjectIds);

            // Add ther user to the projects if not already added
            HashSet<string> hashSetOfProjects = new HashSet<string>(projectIds);
            foreach (var projectId in hashSetOfProjects)
            {
                if (projectId is not null)
                {
                    // Get all the userIds assigned to sprints and todos under the project with the projectId
                    todoIds = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId)
                        .Select(u => u.Id).ToListAsync();
                    var todoUserIds = await Db.projectMgmt_TodoUsers.Where(x => todoIds.Contains(x.ProjectMgmt_TodoId))
                        .Select(u => u.UserId).ToListAsync();

                    sprintIds = await Db.SprintProjects
                        .Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId).Select(u => u.Id.ToString()).ToListAsync();
                    var sprintUserIds = await Db.ProjectSprintMemberIds.Where(x => sprintIds.Contains(x.SprintId)).Select(u => u.MemberId).ToListAsync();

                    var totalUserIds = todoUserIds.Concat(sprintUserIds).ToList();
                    foreach (var id in totalUserIds.Distinct())
                    {
                        var projectUser = await Db.projectMgmt_ProjectUsers.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && x.UserId == id).FirstOrDefaultAsync();
                        if (projectUser == null)
                        {
                            var projectUserToAdd = new ProjectMgmt_ProjectUser()
                            {
                                ProjectMgmt_ProjectId = Guid.Parse(projectId),
                                UserId = id
                            };
                            Db.projectMgmt_ProjectUsers.Add(projectUserToAdd);
                            await Db.SaveChangesAsync();
                        }
                    }
                }
            }

            var projectsToReturn = await Db.ProjectMgmt_Projects
                .Include(x => x.projectMgmt_ProjectUsers)
                .Where(x => x.projectMgmt_ProjectUsers.Any(u => u.UserId == userId || u.Email == userEmail) || x.CreatedBy == userId || projectIds.Contains(x.ProjectId.ToString()))
                .OrderByDescending(x => x.CreatedTime).ToPageListAsync(paginationParameters.PageNumber, paginationParameters.PageSize);

            foreach (var project in projectsToReturn.Items)
            {
                // Calcaulate the percentage of completion for the project
                sprintIds = await Db.SprintProjects
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).Select(x => x.Id.ToString()).ToListAsync();
                var todos = await Db.ProjectMgmt_Todo
                    .Where(x => sprintIds.Contains(x.SprintProjectId.Value.ToString())).ToListAsync();
                var totalTodosCount = todos.Count;
                var totalCompletedTodosCount = todos.Count(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString());
                var percentageCompletedInt = totalTodosCount > 0 ? totalCompletedTodosCount * 100 / totalTodosCount : 0;

                project.PercentageCompleted = percentageCompletedInt.ToString() + "%";
            }

            return projectsToReturn;
        }
        #endregion

        #region Add Both external and internal members to a project
        /// <summary>
        /// Add Both external and internal members to a project
        /// </summary>
        /// <param name="model"></param>
        /// <param name="project"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> AddMembersToProject(ProjectMembersVm model, ProjectMgmt_Project project)
        {
            // Get the tenantId
            var tenant = await _tenantService.GetTenantBySubdomain(model.SubDomain);
            var tenantId = tenant.Id;

            // Get the invitee name
            var invitee = Db.UserProfiles.Where(x => x.UserId == model.UserId)
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            int result = 0;
            var projUser = new List<ProjectMgmt_ProjectUser>();
            var emails = new List<string>();
            if (model.TeamMembers.Any())
            {
                foreach (var member in model.TeamMembers)
                {
                    var user = await Dbo.Users.Where(X => X.Id == member).FirstOrDefaultAsync();
                    if (user != null)
                    {
                        var proj = new ProjectMgmt_ProjectUser()
                        {
                            ProjectMgmt_ProjectId = project.ProjectId,
                            UserId = member

                        };
                        emails.Add(user.Email);
                        projUser.Add(proj);
                    }
                }

                Db.projectMgmt_ProjectUsers.AddRange(projUser);
            }

            if (model.ExternalMemberEmails.Any())
            {
                foreach (var email in model.ExternalMemberEmails)
                {
                    // Add an invite to the database
                    var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                    {
                        Email = email,
                        Application = Applications.Joble,
                    }, tenantId.ToString());
                    if (!inviteCreated)
                        return false;

                    var proj = new ProjectMgmt_ProjectUser()
                    {
                        ProjectMgmt_ProjectId = project.ProjectId,
                        Email = email,
                        UserId = null
                    };
                    projUser.Add(proj);
                }

                Db.projectMgmt_ProjectUsers.AddRange(projUser);
            }

            result = await Db.SaveChangesAsync();
            if (result > 0)
            {
                //var template = await _emailService.GetMailTemplate();
                var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/zarttech-invited-email.html");
                var template = File.ReadAllText(templatePath);

                // Send mails to added internal members
                if (model.TeamMembers.Any())
                {
                    foreach (var email in emails)
                    {
                        // Send email notification to added members
                        var html = $"<p>Hi {invitee},</p><p>You have been added to {project.Name} project.</p><p>Click <a href='https://{model.SubDomain}.jobpro.app/auth/login'>here</a> to login to your account.</p>";

                        BackgroundJob.Enqueue(() => _emailService.SendEmail(html, email, "Project Addition"));
                    }
                }

                // Send invite to added external members
                if (model.ExternalMemberEmails.Any())
                {
                    foreach (var email in model.ExternalMemberEmails)
                    {
                        // Send email notification to added members
                        var encodedEmail = HttpUtility.UrlEncode(email);
                        var inviteUrl = string.Format(Utility.Constants.INVITE_URL, model.SubDomain, invitee, tenantId, Applications.Joble, encodedEmail) + $"&type={TypeForInviteUrl.Project.ToString()}&id={project.ProjectId}";

                        template = template.Replace("{Jobpro}", tenant.CompanyName).Replace("{proj}", project.Name).Replace("{name}", invitee).Replace("{url}", inviteUrl);
                        BackgroundJob.Enqueue(() => _emailService.SendEmail(template, email, "Project Addition"));
                    }
                }

                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion

        #region Update Project
        /// <summary>
        /// Update Project
        /// </summary>
        /// <param name="model"></param>
        /// <param name="pro"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<bool> UpdateProjectMgmt_Project(ProjectMgmt_ProjectUpdateVM model, ProjectMgmt_Project pro, string loggedInUserId)
        {
            try
            {
                var allowedPermissions = new List<Permissions> { Permissions.can_update_other_users_project };
                await CheckPermission(pro, loggedInUserId, allowedPermissions);

                if (model.IsBillable && (model.AmountPerSelectedFrequency == null || model.AmountPerSelectedFrequency <= 0
                 || model.ExpectedProjectValue == null || model.ExpectedProjectValue <= 0))
                    throw new DirtyFormException("Amount per selected frequency and project value is required, rate and value should not be less than or equal to zero");

                model.ProjectName = model.ProjectName.ToTitleCase();

                // Check if a project with the project name already exists
                var projectExists = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(p => p.Name.ToLower() == model.ProjectName.ToLower());
                if (projectExists != null && projectExists.ProjectId != pro.ProjectId)
                    throw new RecordAlreadyExistException("Project with the supplied project name already exists");

                pro.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
                var days = (int)(model.EndDate - model.StartDate).TotalDays;
                pro.Name = model.ProjectName;
                pro.ProjectStatus = model.ProjectStatus;
                pro.Duration = Utility.DateDurationCalculator(days);
                pro.StartDate = model.StartDate;
                pro.CurrencySymbol = model.CurrencySymbol.ToString();
                pro.AmountFrequency = model.AmountFrequency;
                pro.IsBillable = model.IsBillable;
                pro.EndDate = model.EndDate;
                pro.AmountPerSelectedFrequency = model.AmountPerSelectedFrequency;
                pro.ExpectedProjectValue = model.ExpectedProjectValue;
                pro.ProjectStatus = model.EndDate > GetAdjustedDateTimeBasedOnTZNow() ? ProjectStatus.Active : pro.ProjectStatus;

                // Update Project Members both internal and external
                Db.ProjectMgmt_Projects.Update(pro);
                int result = await Db.SaveChangesAsync();
                if (result > 0) { return true; } else { return false; }
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Update Project Status
        /// <summary>
        /// Update Project Status
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="projectStatus"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<ProjectMgmt_Project> UpdateProjectStatus(Guid Id, ProjectStatus projectStatus, string loggedInUserId)
        {
            var project = Db.ProjectMgmt_Projects.Where(x => x.ProjectId == Id).FirstOrDefault();
            if (project == null)
                throw new RecordNotFoundException("Project not found");

            var allowedPermissions = new List<Permissions> { Permissions.can_update_other_users_project };
            await CheckPermission(project, loggedInUserId, allowedPermissions);

            project.ProjectStatus = projectStatus;

            //if (projectStatus == ProjectStatus.Overdue)
            //{
            //    var todos = this.Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId == Id && x.TodoStatus == ProjectManagementStatus.OnHold.ToString());

            //    if (todos.Any())
            //    {
            //        foreach (var todo in todos)
            //        {
            //            todo.TodoStatus = ProjectManagementStatus.Backlog.ToString();
            //        }

            //        this.Db.ProjectMgmt_Todo.UpdateRange(todos);
            //        await this.Db.SaveChangesAsync();

            //    }

            //}

            Db.ProjectMgmt_Projects.Update(project);
            var result = await Db.SaveChangesAsync();
            return result > 0 ? project : null;
        }
        #endregion

        #region Get Project Members
        /// <summary>
        /// Get Project Members
        /// </summary>
        /// <param name="ProjectId"></param>
        /// <returns></returns>
        public async Task<List<UserDto>> GetProjectMembers(Guid ProjectId)
        {
            var membersIds = new HashSet<List<string>>();
            var membersList = new List<UserDto>();
            var members = Db.projectMgmt_ProjectUsers.Where(x => x.ProjectMgmt_ProjectId == ProjectId).ToList();
            if (members.Any())
            {
                foreach (var member in members)
                {
                    var user = await Db.UserProfiles.Where(x => x.UserId == member.UserId).FirstOrDefaultAsync();
                    membersList.Add(new UserDto()
                    {
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        MiddleName = user.MiddleName,
                        Id = user.UserId,
                        Email = user.Email,
                        ProfileUrl = user.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl) : null,
                        AmountPerHour = member.AmountPerHour,
                        Currency = member.CurrencySymbol
                    });
                }
            }

            // Get members from sprints under the project
            var sprints = await Db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId == ProjectId)
                .Select(x => x.Id).ToListAsync();
            foreach (var sprint in sprints)
            {
                var sprintMembers = Db.ProjectSprintMemberIds.Where(x => x.Id == sprint).ToList();
                if (sprintMembers.Any())
                {
                    foreach (var member in sprintMembers)
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == member.MemberId).FirstOrDefaultAsync();
                        membersList.Add(new UserDto()
                        {
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            MiddleName = user.MiddleName,
                            Id = user.UserId,
                            Email = user.Email,
                            ProfileUrl = user.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl) : null,
                            AmountPerHour = member.AmountPerHour,
                            Currency = member.CurrencySymbol
                        });
                    }
                }

                // Get members from todos under the sprint
                var todos = Db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprint)
                    .Select(x => x.Id).ToList();
                var todoMmembers = Db.projectMgmt_TodoUsers.Where(x => todos.Contains(x.ProjectMgmt_TodoId)).ToList();
                if (todoMmembers.Any())
                {
                    foreach (var member in todoMmembers)
                    {
                        var user = await Db.UserProfiles.Where(x => x.UserId == member.UserId).FirstOrDefaultAsync();
                        membersList.Add(new UserDto()
                        {
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            MiddleName = user.MiddleName,
                            Id = user.UserId,
                            Email = user.Email,
                            ProfileUrl = user.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl) : null,
                            AmountPerHour = member.AmountPerHour,
                            Currency = member.CurrencySymbol
                        });
                    }
                }
            }

            return membersList;
        }
        #endregion

        #region Add amount per user for a specific project
        /// <summary>
        /// Add amount per user for a specific project
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="userIds"></param>
        /// <param name="amount"></param>
        /// <param name="currency"></param>
        /// <returns></returns>
        public async Task<bool> AddAmountPerUser(string projectId, List<string> userIds, decimal amount, string currency)
        {
            var project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId == Guid.Parse(projectId)).FirstOrDefaultAsync();
            if (project == null)
                new RecordNotFoundException("Project not found");

            // Check if the user was added to projectmember table
            var projectMembers = await Db.projectMgmt_ProjectUsers
                .Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && userIds.Contains(x.UserId)).ToListAsync();
            if (projectMembers.Count > 0)
            {
                foreach (var member in projectMembers)
                {
                    member.AmountPerHour = amount;
                    member.CurrencySymbol = currency;
                }

                Db.projectMgmt_ProjectUsers.UpdateRange(projectMembers);
                goto savechanges;
            }

            // Check if the user was added to sprint under this project
            var sprints = await Db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId)
                .Select(x => x.Id).ToListAsync();
            foreach (var sprint in sprints)
            {
                var sprintMembers = await Db.ProjectSprintMemberIds.Where(x => x.SprintId == sprint.ToString() && userIds.Contains(x.Id.ToString())).ToListAsync();
                if (sprintMembers.Count > 0)
                {
                    foreach (var member in sprintMembers)
                    {
                        member.AmountPerHour = amount;
                        member.CurrencySymbol = currency;
                    }

                    Db.ProjectSprintMemberIds.UpdateRange(sprintMembers);
                    goto savechanges;
                }

                // Check if the user was added to todo under this sprint
                var todos = await Db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprint)
                    .Select(x => x.Id).ToListAsync();
                foreach (var todo in todos)
                {
                    var todoMembers = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo && userIds.Contains(x.UserId)).ToListAsync();
                    if (todoMembers.Count > 0)
                    {
                        foreach (var todoMember in todoMembers)
                        {
                            todoMember.AmountPerHour = amount;
                            todoMember.CurrencySymbol = currency;
                        }

                        Db.projectMgmt_TodoUsers.UpdateRange(todoMembers);
                        goto savechanges;
                    }
                }
            }

        // Label
        savechanges:
            var result = await Db.SaveChangesAsync();
            return result > 0 ? true : false;
        }
        #endregion

        #region Get project by sprint id
        /// <summary>
        /// Get project by sprint id
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        public async Task<ProjectMgmt_Project> GetProjectMgmt_SprintId(Guid sprintId)
        {
            Guid projectId = await Db.SprintProjects.Where(x => x.Id == sprintId).Select(x => x.ProjectMgmt_ProjectId).FirstOrDefaultAsync();
            ProjectMgmt_Project data = await Db.ProjectMgmt_Projects.Where(z => z.ProjectId == projectId).FirstOrDefaultAsync();
            return data;
        }
        #endregion

        #region Get project by id
        public async Task<ProjectMgmt_Project> GetProjectMgmt_ProjectId(Guid projectId)
        {
            return await Db.ProjectMgmt_Projects.Where(z => z.ProjectId == projectId).FirstOrDefaultAsync();
        }
        #endregion

        #region Get Actual Project Value using projectId
        /// <summary>
        /// This gets the actual value of the project at any point in time
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<ActualProjectValueResponse> GetActualProjectValue(string projectId)
        {
            var project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId.ToString() == projectId).FirstOrDefaultAsync();
            if (project is null) { throw new RecordNotFoundException("Project not found"); }

            // Get the total timespent on the all the todos under the project and the project value
            var totalTime = new TimeSpan();
            decimal projectValue = 0.0M;

            var todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();
            var todosFromTh = await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId.ToString())
                .Select(x => new ProjectMgmt_Todo()
                {
                    Id = x.Id,
                    TimeSpent = x.TimeSpent,
                    IsBillable = x.IsBillable,
                })
                .ToListAsync();
            todos.AddRange(todosFromTh);

            foreach (var todo in todos)
            {
                if (todo.ActualTimeSpent == "0.00:00:00")
                    todo.ActualTimeSpent = null;

                TimeSpan time;
                if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTime += time;

                // Get project value
                if (project.IsBillable)
                {
                    var amountPerMin = 0.0M;
                    switch (project.AmountFrequency)
                    {
                        case AmountFrequency.Hourly:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 60;
                            break;
                        case AmountFrequency.Daily:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 1440;
                            break;
                        case AmountFrequency.Weekly:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 10080;
                            break;
                        case AmountFrequency.Monthly:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 43800;
                            break;
                        default:
                            break;
                    }

                    if (todo.IsBillable)
                    {
                        var todoAmount = amountPerMin * (decimal)time.TotalMinutes;
                        projectValue += todoAmount;
                    }
                }
            }

            return new ActualProjectValueResponse
            {
                Currency = project.CurrencySymbol,
                Value = projectValue,
            };

        }
        #endregion

        #region Get all project report summary
        /// <summary>
        /// Get all project report summary
        /// </summary>
        /// <returns></returns>
        public async Task<List<ProjectReportSummaryDto>> GetProjectReportSummary()
        {
            var projects = await Db.ProjectMgmt_Projects
                .Select(x => new ProjectReportSummaryDto
                {
                    Id = x.ProjectId.ToString(),
                    ProjectName = x.Name,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                }).ToListAsync();

            // Get project members
            foreach (var project in projects)
                project.Members.AddRange(await GetProjectMembers(Guid.Parse(project.Id)));

            return projects;
        }
        #endregion

        #region Get project report details by projectid
        /// <summary>
        /// Get project report details by projectid
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<ProjectReportDetailsDto> GetProjectReportDetails(Guid projectId)
        {
            var project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId == projectId).FirstOrDefaultAsync();
            if (project == null)
                throw new RecordNotFoundException("Project not found");

            var projectReport = new ProjectReportDetailsDto()
            {
                Id = project.ProjectId.ToString(),
                ProjectName = project.Name,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                Summary = project.Summary,
                Description = project.Description,
                TeamMembers = await GetProjectMembers(projectId)
            };

            // Get project uploaded files
            var files = await Db.ProjectFile.Where(x => x.ProjectMgmt_ProjectId == projectId).Select(x => x.FileName).ToListAsync();
            var fileUrls = new List<string>();
            foreach (var file in files)
            {
                var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(file);
                fileUrls.Add(signedUrl);
            }
            projectReport.ProjectFilesUrls = fileUrls;

            // Calculate total time spent on project
            TimeSpan totalTime = await CalculateTotalTimeSpentOnAProject(project);

            projectReport.totalTimeTracked = totalTime.ToString();

            // Calculate for project analytics
            var months = new List<string> { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
            var monthsInt = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };

            var projectAnalytics = new ProjectAnalyticsDto();
            foreach (var month in monthsInt)
            {
                try
                {
                    var totalSprints = await Db.SprintProjects.Where(x => x.StartDate.Month == month && x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();
                    var currentMonth = months[month - 1];
                    var totalSprintCount = totalSprints.Count();
                    var completedSprintCount = totalSprints.Where(x => x.Status == SprintStatus.Completed).Count();
                    var overdueSprintCount = totalSprints.Where(x => x.Status == SprintStatus.LogJam).Count();

                    // Calcualte % of completed projects and overdue projects
                    if (totalSprintCount == 0)
                    {
                        projectAnalytics.CompletedSprint.Add(currentMonth, "0%");
                        projectAnalytics.OverDueSprint.Add(currentMonth, "0%");
                        continue;
                    }

                    if (completedSprintCount == 0)
                        projectAnalytics.CompletedSprint.Add(currentMonth, "0%");
                    else
                    {
                        double percentage = completedSprintCount / (double)totalSprintCount * 100;
                        projectAnalytics.CompletedSprint.Add(currentMonth, percentage.ToString() + "%");
                    }

                    if (overdueSprintCount == 0)
                        projectAnalytics.OverDueSprint.Add(currentMonth, "0%");
                    else
                    {
                        double percentage = overdueSprintCount / (double)totalSprintCount * 100;
                        projectAnalytics.OverDueSprint.Add(currentMonth, percentage.ToString() + "%");
                    }
                }
                catch
                {
                    throw;
                }

            }

            projectReport.ProjectAnalytics = projectAnalytics;

            return projectReport;
        }
        #endregion

        #region Get project PDF export report summary
        /// <summary>
        /// This method is used to get project PDF export report summary
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<ProjectPDFExportReportSummaryDto> GetProjectPDFExportReportSummary(Guid projectId)
        {
            var project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId == projectId).FirstOrDefaultAsync();
            if (project == null)
                throw new RecordNotFoundException("Project not found");

            var projectReport = new ProjectPDFExportReportSummaryDto()
            {
                ProjectName = project.Name,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                Summary = project.Summary,
                Description = project.Description,
                TeamMembers = await GetProjectMembers(projectId)
            };

            // Calculate total time spent on project
            TimeSpan totalTime = await CalculateTotalTimeSpentOnAProject(project);
            projectReport.totalTimeTracked = totalTime.ToString();

            // Get project uploaded files
            var files = await Db.ProjectFile.Where(x => x.ProjectMgmt_ProjectId == projectId).Select(x => x.FileName).ToListAsync();
            var fileUrls = new List<string>();
            foreach (var file in files)
            {
                var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(file);
                fileUrls.Add(signedUrl);
            }
            projectReport.ProjectFilesUrls = fileUrls;

            // Get sprint reports for the projectId
            var sprints = await Db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId == projectId).ToListAsync();

            var sprintDFExportReportSummaryDtos = new List<SprintDFExportReportSummaryDto>();
            var todoDFExportReportSummaryDtos = new List<TodoDFExportReportSummaryDto>();
            foreach (var sprint in sprints)
            {
                var sprintDFExportReportSummaryDto = new SprintDFExportReportSummaryDto()
                {
                    SprintName = sprint.Name,
                    Status = sprint.Status.ToString(),
                    StartDate = sprint.StartDate,
                    EndDate = sprint.EndDate,
                    Duration = sprint.Duration,
                    TotalTimeTracked = (await CalculateTotalTimeSpentOnASprint(sprint)).ToString()
                };

                var sprintTodos = await Db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprint.Id).ToListAsync();
                foreach (var todo in sprintTodos)
                {
                    var todoDFExportReportSummaryDto = new TodoDFExportReportSummaryDto()
                    {
                        Name = todo.TodoName,
                        Description = todo.TodoDescription,
                        Billable = todo.IsBillable,
                        Status = todo.TodoStatus,
                        TotalTimeTracked = todo.TimeSpent
                    };
                    todoDFExportReportSummaryDtos.Add(todoDFExportReportSummaryDto);
                }

                sprintDFExportReportSummaryDto.TodoSummary = todoDFExportReportSummaryDtos;
                sprintDFExportReportSummaryDtos.Add(sprintDFExportReportSummaryDto);
            }

            projectReport.SprintSummary = sprintDFExportReportSummaryDtos;

            return projectReport;
        }
        #endregion

        #region Get Project Count Report
        /// <summary>
        /// Get Project Count Report
        /// </summary>
        /// <returns></returns>
        public async Task<ProjectReportVm> GetTotalProjectCountReport()
        {
            var totalProjectCount = await Db.ProjectMgmt_Projects.CountAsync();
            var totalTodoCount = Db.ProjectMgmt_Todo.Count();
            var inProgressProjectCount = Db.ProjectMgmt_Projects.Where(x => x.ProjectStatus == ProjectStatus.Active).Count();
            var todoInprogressCount = Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == ProjectManagementStatus.InProgress.ToString()).Count();
            var todoCompletedCount = Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Count();
            var activeProjectCount = Db.ProjectMgmt_Projects.Where(x => x.ProjectStatus == ProjectStatus.Active).Count();

            return new ProjectReportVm()
            {
                TotalProject = totalProjectCount,
                ProjectInProgress = inProgressProjectCount,
                TodoCompleted = todoCompletedCount,
                TodoInProgress = todoInprogressCount,
                TodoOnHoldCount = 0,
                ActiveProject = activeProjectCount,
                inActiveProject = totalProjectCount - activeProjectCount,
                PercentageCompleted = Utility.CalculatePercentage(todoCompletedCount, totalProjectCount),
                PercentageActive = Utility.CalculatePercentage(activeProjectCount, totalProjectCount)
            };
        }
        #endregion

        #region Get Project Count Report By Id
        /// <summary>
        /// Get Project Count Report By Id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        public async Task<ProjectReportVm> GetTotalProjectReportById(string projectId)
        {

            var totalCount = Db.ProjectMgmt_Projects.Where(x => x.ProjectId.ToString() == projectId).Count();
            var totalTodoCount = Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId).Count();
            var inProgressProjectCount = Db.ProjectMgmt_Projects.Where(x => x.EndDate < GetAdjustedDateTimeBasedOnTZNow()).Count();
            var todoInprogressCount = Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == ProjectManagementStatus.InProgress.ToString() && x.ProjectMgmt_ProjectId.ToString() == projectId).Count();
            var todoCompletedCount = Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString() && x.ProjectMgmt_ProjectId.ToString() == projectId).Count();
            var activeProjectCount = Db.ProjectMgmt_Projects.Where(x => x.ProjectStatus == ProjectStatus.Active && x.ProjectId.ToString() == projectId).Count();

            return new ProjectReportVm()
            {
                TotalProject = totalCount,
                ProjectInProgress = inProgressProjectCount,
                TodoCompleted = todoCompletedCount,
                TodoInProgress = todoInprogressCount,
                TodoOnHoldCount = 0,
                ActiveProject = activeProjectCount,
                inActiveProject = totalCount - activeProjectCount,
                PercentageCompleted = Utility.CalculatePercentage(todoCompletedCount, totalCount),
                PercentageActive = Utility.CalculatePercentage(activeProjectCount, totalCount)
            };
        }
        #endregion

        #region Delete Project
        /// <summary>
        /// Delete Project By Id
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> DeleteProjectById(string Id, string loggedInUserId)
        {
            var project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == Id);
            if (project != null)
            {
                //await CheckPermission(project, loggedInUserId);

                // Delete Project Member
                var projectMembers = await Db.projectMgmt_ProjectUsers.Where(x => x.ProjectMgmt_ProjectId.ToString() == Id).ToListAsync();
                if (projectMembers.Any())
                {
                    Db.projectMgmt_ProjectUsers.RemoveRange(projectMembers);
                }

                // Delete Project Tag
                var projectTags = await Db.ProjectTag.Where(x => x.ProjectMgmt_ProjectId.ToString() == Id).ToListAsync();
                if (projectTags.Any())
                {
                    Db.ProjectTag.RemoveRange(projectTags);
                }

                // Delete uploaded files
                var projectFiles = await Db.ProjectFile.Where(x => x.ProjectMgmt_ProjectId.ToString() == Id).ToListAsync();
                if (projectFiles.Any())
                {
                    Db.ProjectFile.RemoveRange(projectFiles);
                }

                // Delete Project Todo
                var projectTodos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == Id).ToListAsync();
                if (projectTodos.Any())
                {
                    // Delete Todo Tags from TagId
                    var todoTagList = new List<TagId>();
                    foreach (var todo in projectTodos)
                    {
                        var todoTags = await Db.TagId.Where(x => x.TodoId == todo.Id.ToString()).ToListAsync();
                        if (todoTags.Any())
                        {
                            todoTagList.AddRange(todoTags);
                        }
                    }

                    Db.TagId.RemoveRange(todoTagList);
                    Db.ProjectMgmt_Todo.RemoveRange(projectTodos);
                }

                // Delete Project Sprint
                var projectSprints = await Db.SprintProjects.Where(x => x.ProjectMgmt_ProjectId.ToString() == Id).ToListAsync();
                if (projectSprints.Any())
                {
                    // Delete sprint tags from TagId
                    var sprintTagList = new List<TagId>();
                    foreach (var sprint in projectSprints)
                    {
                        var sprintTags = await Db.TagId.Where(x => x.SprintId == sprint.Id.ToString()).ToListAsync();
                        if (sprintTags.Any())
                        {
                            sprintTagList.AddRange(sprintTags);
                        }

                        // Delete TodoStatus for a sprint
                        var todoStatus = await Db.TodoStatus.Where(x => x.SprintId == sprint.Id).ToListAsync();
                        if (todoStatus.Any())
                        {
                            Db.TodoStatus.RemoveRange(todoStatus);
                        }
                    }

                    Db.TagId.RemoveRange(sprintTagList);
                    Db.SprintProjects.RemoveRange(projectSprints);
                }

                Db.ProjectMgmt_Projects.Remove(project);
                var result = await Db.SaveChangesAsync();
                return result > 0 ? true : false;
            }

            throw new RecordNotFoundException("Project not found");
        }
        #endregion

        #region Project Count Statistic
        /// <summary>
        /// Project count statistic
        /// </summary>
        /// <returns></returns>
        public async Task<List<ProjectCountStatisticDto>> GetProjectCount()
        {
            var tenants = Dbo.CompanySubscriptions
                .Include(x => x.Tenant)
                .Where(x => x.Application == Applications.Joble && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active)
                .Select(x => x.Tenant.Subdomain)
                .ToList();

            DateTime currentDate = GetAdjustedDateTimeBasedOnTZNow();
            DateTime startDate = currentDate.AddMonths(-4);
            return await Dbo.TenantProjectViews
                .Where(project => tenants.Contains(project.Subdomain) && project.CreatedTime >= startDate && project.CreatedTime <= currentDate)
                .GroupBy(project => new { project.CreatedTime.Month })
                .Select(group => new ProjectCountStatisticDto
                {
                    Month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(group.Key.Month),
                    Count = group.Count()
                })
                .ToListAsync();
        }
        #endregion

        #region Project Top Company Performance
        /// <summary>
        /// Company performance by project count
        /// </summary>
        /// <returns></returns>
        public async Task<List<TopPerformingCompanyDto>> GetTopPerformingCompaniesPercentage()
        {
            DateTime currentDate = GetAdjustedDateTimeBasedOnTZNow();
            var topPerformingProjects = await GetTopPerformingCompany(6);
            var results = new List<TopPerformingCompanyDto>();

            foreach (var result in topPerformingProjects)
            {
                int companyProjectCountEver = await Dbo.TenantProjectViews.CountAsync(project => project.Subdomain == result.SubDomain);
                int companyProjectCountInMonth = await Dbo.TenantProjectViews.CountAsync(project => project.Subdomain == result.SubDomain && project.CreatedTime.Month == currentDate.Month && project.CreatedTime.Year == currentDate.Year);

                double percentageIncrement = CalculatePercentageIncrement(companyProjectCountEver, companyProjectCountInMonth);
                results.Add(new TopPerformingCompanyDto
                {
                    CompanyName = result.Tenant.CompanyName,
                    TotalProjectCount = companyProjectCountEver,
                    PercentageIncrement = percentageIncrement
                });
            }
            return results;
        }
        #endregion

        #region Project Company detail
        /// <summary>
        /// Project company detail
        /// </summary>
        /// <returns></returns>
        public async Task<Page<ProjectCreationCompanyDetail>> GetProjectCompanyDetail(CompanyProjectFilter filter, int pageSize, int pageNumber)
        {
            IQueryable<ProjectCreationCompanyDetail> query = filter switch
            {
                CompanyProjectFilter.HighestFirst => GetProjectsOrderedByProjectCount(true),
                CompanyProjectFilter.LowestFirst => GetProjectsOrderedByProjectCount(false),
                CompanyProjectFilter.MostRecent => GetMostRecentProjects(),
                CompanyProjectFilter.CompanySize => GetProjectsOrderedByCompanySize(),
                _ => throw new ArgumentOutOfRangeException(nameof(filter), "Invalid CompanyProjectFilter value."),
            };
            return await query.ToPageListAsync(pageNumber, pageSize);
        }
        #endregion

        #region Get project metrics
        /// <summary>
        /// Get  project metrics
        /// </summary>
        /// <returns></returns>
        public async Task<Page<CompanyProjectMetrics>> GetProjectMetrics(ProjectMetricsQueryParameters queryParameters)
        {
            IQueryable<CompanyProjectMetrics> query = queryParameters.PeriodFilter switch
            {
                TimePeriodFilter.OneMonth => GetMetricsForOneMonth(),
                TimePeriodFilter.ThreeMonths => GetMetricsForThreeMonths(),
                TimePeriodFilter.OneYear => GetMetricsForOneYear(),
                TimePeriodFilter.AllTime => GetMetricsForAllTime(),
                TimePeriodFilter.Custom => GetMetricsForCustom(queryParameters.FromDate, queryParameters.ToDate),
                _ => throw new ArgumentOutOfRangeException(nameof(queryParameters.PeriodFilter), "Invalid TimePeriodFilter value."),
            };
            return await query.ToPageListAsync(queryParameters.PageNumber, queryParameters.PageSize);
        }
        #endregion

        #region Get project metrics
        /// <summary>
        /// Get  project metrics
        /// </summary>
        /// <returns></returns>
        public async Task<ProjectMetricPercentage> GetProjectMetricsSummary(ProjectMetricsQueryParameters queryParameters)
        {
            var projectMetricsViews = GetProjectMetricsViews(queryParameters.PeriodFilter, queryParameters.FromDate, queryParameters.ToDate);
            var currentYear = GetAdjustedDateTimeBasedOnTZNow().Year;
            var currentMonth = GetAdjustedDateTimeBasedOnTZNow().Month;

            return queryParameters.PeriodFilter switch
            {
                TimePeriodFilter.OneMonth => GetMetricsForOneMonthSummary(projectMetricsViews, currentYear, currentMonth),
                TimePeriodFilter.ThreeMonths => GetMetricsForThreeMonthsSummary(projectMetricsViews, currentYear, currentMonth),
                TimePeriodFilter.OneYear => GetMetricsForOneYearSummary(projectMetricsViews, currentYear),
                TimePeriodFilter.AllTime => GetMetricsForAllTimeSummary(projectMetricsViews),
                TimePeriodFilter.Custom => throw new ArgumentException("Custom time period is not supported for summary metrics."),
                _ => throw new ArgumentOutOfRangeException(nameof(queryParameters.PeriodFilter), "Invalid TimePeriodFilter value."),
            };
        }
        #endregion

        #region Get Tenant Clients
        /// <summary>
        /// Get  project metrics
        /// </summary>
        /// <returns></returns>
        //   public async Task<HashSet<string>> GetTenantClients(String subDomain)
        //   {
        //      var clients = await Db.TenantClients.FirstOrDefault(x => x.SubDomain == subDomain);  
        //      return clients as HashSet<string>;
        //  }
        #endregion

        #region Private Methods
        /// <summary>
        /// This method checks if the logged in user has permission to perform the action
        /// </summary>
        /// <param name="pro"></param>
        /// <param name="loggedInUserId"></param>
        /// <param name="permissions"></param>
        /// <returns></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        private async Task CheckPermission(ProjectMgmt_Project pro, string loggedInUserId, List<Permissions> permissions = null)
        {
            var hasPermission = false;
            if (permissions != null)
            {
                // Get Logged In User Permissions
                var userPermissions = (await _adminService.GetUserPermissions(loggedInUserId))
                    .Select(p => p.Replace("'", ""));
                foreach (var permission in permissions)
                {
                    if (userPermissions.Contains(permission.ToString()))
                    {
                        hasPermission = true;
                        break;
                    }
                }
            }

            // Check if the logged in user is a super admin
            var userRole = await _adminService.GetUserRole(loggedInUserId);
            if (pro.CreatedBy != loggedInUserId && userRole != DataSeeder.SuperAdmin && !hasPermission)
                throw new UnauthorizedAccessException("You are not authorized to perform this action");
        }

        /// <summary>
        /// This private method calculates the total time spent on a project
        /// </summary>
        /// <param name="project"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<TimeSpan> CalculateTotalTimeSpentOnAProject(ProjectMgmt_Project project)
        {
            var totalTime = new TimeSpan();

            var todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();
            var todosFromTh = await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId.ToString())
                .Select(x => new ProjectMgmt_Todo()
                {
                    Id = x.Id,
                    TimeSpent = x.TimeSpent,
                    IsBillable = x.IsBillable,
                })
                .ToListAsync();
            todos.AddRange(todosFromTh);

            foreach (var todo in todos)
            {
                if (todo.ActualTimeSpent == "0.00:00:00")
                    todo.ActualTimeSpent = null;

                TimeSpan time;
                if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTime += time;
            }

            return totalTime;
        }

        /// <summary>
        /// This private method calculates the total time spent on a sprint
        /// </summary>
        /// <param name="sprint"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<TimeSpan> CalculateTotalTimeSpentOnASprint(SprintProject sprint)
        {
            var totalTime = new TimeSpan();

            var todos = await Db.ProjectMgmt_Todo.Where(x => x.SprintProjectId == sprint.Id).ToListAsync();
            foreach (var todo in todos)
            {
                if (todo.ActualTimeSpent == "0.00:00:00")
                    todo.ActualTimeSpent = null;

                TimeSpan time;
                if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTime += time;
            }

            return totalTime;
        }

        /// <summary>
        /// This private method calculates the total time spent on a todo
        /// </summary>
        /// <param name="todo"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<TimeSpan> CalculateTotalTimeSpentOnTodo(ProjectMgmt_Todo todo)
        {
            if (todo.ActualTimeSpent == "0.00:00:00")
                todo.ActualTimeSpent = null;

            var totalTime = new TimeSpan();
            TimeSpan time;
            if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
            {
                throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
            }

            return await Task.FromResult(totalTime);
        }

        /// <summary>
        /// This private method get top performing companies 
        /// </summary>
        /// <param name="numberOfCompanies"></param>
        /// <returns></returns>
        private async Task<List<PerformingCompany>> GetTopPerformingCompany(int numberOfCompanies)
        {
            var topPerformingCompaniesData = await Dbo.Tenants
                    .Select(t => new
                    {
                        Tenant = t,
                        ProjectCount = Dbo.TenantProjectViews.Count(tpv => tpv.Subdomain == t.Subdomain)
                    })
                    .OrderByDescending(x => x.ProjectCount)
                    .Take(numberOfCompanies)
                    .ToListAsync();

            var topPerformingCompanies = topPerformingCompaniesData
                .Select((t, index) => new PerformingCompany
                {
                    SubDomain = t.Tenant.Subdomain,
                    Tenant = t.Tenant,
                    Rank = index + 1,
                    ProjectCount = t.ProjectCount
                })
                .ToList();

            return topPerformingCompanies;
        }

        /// <summary>
        /// This private method calculates percentage increment
        /// </summary>
        /// <param name="companyProjectCountEver"></param>
        /// <param name="companyProjectCountInMonth"></param>
        /// <returns></returns>
        private static double CalculatePercentageIncrement(int companyProjectCountEver, int companyProjectCountInMonth)
        {
            if (companyProjectCountInMonth == 0)
                return 0.0;
            return ((double)companyProjectCountEver / companyProjectCountInMonth - 1) * 100.0;
        }

        /// <summary>
        /// This private method calculates percentage increment
        /// </summary>
        /// <param name="highestFirst"></param>
        /// <returns></returns>
        private IQueryable<ProjectCreationCompanyDetail> GetProjectsOrderedByProjectCount(bool highestFirst)
        {
            var currentMonth = GetAdjustedDateTimeBasedOnTZNow().Month;
            var currentYear = GetAdjustedDateTimeBasedOnTZNow().Year;

            var query = from tenant in Dbo.Tenants
                        where Dbo.TenantProjectViews.Any(tpv =>
                                  tpv.Subdomain == tenant.Subdomain)
                        let staffSize = Dbo.UserCompanies.Count(uc => uc.tenant.Subdomain == tenant.Subdomain)
                        let projectCount = Dbo.TenantProjectViews
                            .Count(tpv => tpv.Subdomain == tenant.Subdomain)
                        let totalProjectCount = Dbo.TenantProjectViews.Count(tpv => tpv.Subdomain == tenant.Subdomain)
                        let creationDate = Dbo.TenantProjectViews
                            .Where(tpv => tpv.Subdomain == tenant.Subdomain)
                            .OrderByDescending(tpv => tpv.CreatedTime)
                            .Select(tpv => (DateTime?)tpv.CreatedTime)
                            .FirstOrDefault()
                        select new ProjectCreationCompanyDetail
                        {
                            CompanyName = tenant.CompanyName,
                            StaffSize = staffSize,
                            ProjectCount = projectCount,
                            CreationDate = creationDate,
                            TotalProjectCount = totalProjectCount
                        };

            query = highestFirst ? query.OrderByDescending(x => x.TotalProjectCount) : query.OrderBy(x => x.TotalProjectCount);
            return query;
        }

        /// <summary>
        /// This private method calculates percentage increment
        /// </summary>
        /// <returns></returns>
        private IQueryable<ProjectCreationCompanyDetail> GetMostRecentProjects()
        {
            var currentMonth = GetAdjustedDateTimeBasedOnTZNow().Month;
            var currentYear = GetAdjustedDateTimeBasedOnTZNow().Year;

            var query = from tenant in Dbo.Tenants
                        let projectCount = Dbo.TenantProjectViews
                            .Count(tpv => tpv.Subdomain == tenant.Subdomain)
                        let creationDate = Dbo.TenantProjectViews
                            .Where(tpv => tpv.Subdomain == tenant.Subdomain)
                            .OrderByDescending(tpv => tpv.CreatedTime)
                            .Select(tpv => (DateTime?)tpv.CreatedTime)
                            .FirstOrDefault()
                        let staffSize = Dbo.UserCompanies.Count(uc => uc.tenant.Subdomain == tenant.Subdomain)
                        let totalProjectCount = Dbo.TenantProjectViews.Count(tpv => tpv.Subdomain == tenant.Subdomain)
                        where Dbo.TenantProjectViews.Any(tpv =>
                                  tpv.Subdomain == tenant.Subdomain)
                        select new ProjectCreationCompanyDetail
                        {
                            CompanyName = tenant.CompanyName,
                            StaffSize = staffSize,
                            ProjectCount = projectCount,
                            CreationDate = creationDate,
                            TotalProjectCount = totalProjectCount
                        };

            return query.OrderByDescending(x => x.CreationDate);
        }

        /// <summary>
        /// This private method fetches projects and order by the company size
        /// </summary>
        /// <returns></returns>
        private IQueryable<ProjectCreationCompanyDetail> GetProjectsOrderedByCompanySize()
        {
            var currentMonth = GetAdjustedDateTimeBasedOnTZNow().Month;
            var currentYear = GetAdjustedDateTimeBasedOnTZNow().Year;

            var query = from tenant in Dbo.Tenants
                        where Dbo.TenantProjectViews.Any(tpv =>
                                  tpv.Subdomain == tenant.Subdomain)
                        let staffSize = Dbo.UserCompanies.Count(uc => uc.tenant.Subdomain == tenant.Subdomain)
                        let projectCount = Dbo.TenantProjectViews
                            .Count(tpv => tpv.Subdomain == tenant.Subdomain)
                        let totalProjectCount = Dbo.TenantProjectViews.Count(tpv => tpv.Subdomain == tenant.Subdomain)
                        let creationDate = Dbo.TenantProjectViews
                            .Where(tpv => tpv.Subdomain == tenant.Subdomain)
                            .OrderByDescending(tpv => tpv.CreatedTime)
                            .Select(tpv => (DateTime?)tpv.CreatedTime)
                            .FirstOrDefault()
                        select new ProjectCreationCompanyDetail
                        {
                            CompanyName = tenant.CompanyName,
                            StaffSize = staffSize,
                            ProjectCount = projectCount,
                            CreationDate = creationDate,
                            TotalProjectCount = totalProjectCount
                        } into result
                        orderby result.StaffSize descending
                        select result;

            return query;
        }

        private IQueryable<CompanyProjectMetrics> GetMetricsForOneMonth()
        {
            DateTime startDate = GetAdjustedDateTimeBasedOnTZNow().AddMonths(-1);
            return Dbo.ProjectMetricsViews
                .Where(pmv => pmv.Year == startDate.Year && pmv.Month == startDate.Month)
                .GroupBy(pmv => new { pmv.CompanyName })
                .Select(group => new CompanyProjectMetrics
                {
                    CompanyName = group.Key.CompanyName,
                    TotalSprints = group.Sum(p => p.TotalSprints),
                    TotalTodo = group.Sum(p => p.TotalTodo),
                    TotalHours = group.Sum(p => p.TotalHours),
                    Year = startDate.Year,
                    Month = startDate.Month
                });
        }

        private IQueryable<CompanyProjectMetrics> GetMetricsForThreeMonths()
        {
            DateTime startDate = GetAdjustedDateTimeBasedOnTZNow().AddMonths(-3);
            return Dbo.ProjectMetricsViews
                .Where(pmv => pmv.Year == startDate.Year && pmv.Month >= startDate.Month)
                .GroupBy(pmv => new { pmv.CompanyName })
                .Select(group => new CompanyProjectMetrics
                {
                    CompanyName = group.Key.CompanyName,
                    TotalSprints = group.Sum(p => p.TotalSprints),
                    TotalTodo = group.Sum(p => p.TotalTodo),
                    TotalHours = group.Sum(p => p.TotalHours),
                    Year = startDate.Year,
                    Month = startDate.Month
                });
        }
        private IQueryable<CompanyProjectMetrics> GetMetricsForOneYear()
        {
            DateTime startDate = GetAdjustedDateTimeBasedOnTZNow().AddYears(-1);
            return Dbo.ProjectMetricsViews
                 .Where(pmv => pmv.Year >= startDate.Year)
                 .GroupBy(pmv => new { pmv.CompanyName })
                 .Select(group => new CompanyProjectMetrics
                 {
                     CompanyName = group.Key.CompanyName,
                     TotalSprints = group.Sum(p => p.TotalSprints),
                     TotalTodo = group.Sum(p => p.TotalTodo),
                     TotalHours = group.Sum(p => p.TotalHours),
                     Year = startDate.Year,
                     Month = startDate.Month
                 });
        }

        private IQueryable<CompanyProjectMetrics> GetMetricsForAllTime()
        {
            return Dbo.ProjectMetricsViews
                .GroupBy(pmv => new { pmv.CompanyName })
                .Select(group => new CompanyProjectMetrics
                {
                    CompanyName = group.Key.CompanyName,
                    TotalSprints = group.Sum(p => p.TotalSprints),
                    TotalTodo = group.Sum(p => p.TotalTodo),
                    TotalHours = group.Sum(p => p.TotalHours),
                    Year = GetAdjustedDateTimeBasedOnTZNow().Year,
                    Month = GetAdjustedDateTimeBasedOnTZNow().Month
                });
        }

        private IQueryable<CompanyProjectMetrics> GetMetricsForCustom(DateTime? startDate, DateTime? endDate)
        {
            if (startDate == null || endDate == null)
                throw new ArgumentException("Custom time period requires both startDate and endDate.");

            return Dbo.ProjectMetricsViews
                .Where(pmv => pmv.Year >= startDate.Value.Year && pmv.Year <= endDate.Value.Year && pmv.Month >= startDate.Value.Month && pmv.Month <= endDate.Value.Month)
                .GroupBy(pmv => new { pmv.CompanyName })
                .Select(group => new CompanyProjectMetrics
                {
                    CompanyName = group.Key.CompanyName,
                    TotalSprints = group.Sum(p => p.TotalSprints),
                    TotalTodo = group.Sum(p => p.TotalTodo),
                    TotalHours = group.Sum(p => p.TotalHours),
                    Year = startDate.Value.Year,
                    Month = startDate.Value.Month
                });
        }

        public IQueryable<ProjectMetricsView> GetProjectMetricsViews(TimePeriodFilter timePeriod, DateTime? startDate, DateTime? endDate)
        {
            var currentYear = GetAdjustedDateTimeBasedOnTZNow().Year;
            var currentMonth = GetAdjustedDateTimeBasedOnTZNow().Month;

            IQueryable<ProjectMetricsView> projectMetricsViews = Dbo.ProjectMetricsViews;
            switch (timePeriod)
            {
                case TimePeriodFilter.OneMonth:
                    projectMetricsViews = projectMetricsViews
                        .Where(pmv => pmv.Year == currentYear && pmv.Month == currentMonth);
                    break;

                case TimePeriodFilter.ThreeMonths:
                    projectMetricsViews = projectMetricsViews
                        .Where(pmv => pmv.Year == currentYear && pmv.Month >= currentMonth - 2 && pmv.Month <= currentMonth ||
                                      pmv.Year == currentYear - 1 && pmv.Month >= currentMonth + 10 && pmv.Month <= currentMonth + 11);
                    break;

                case TimePeriodFilter.OneYear:
                    projectMetricsViews = projectMetricsViews
                        .Where(pmv => pmv.Year == currentYear);
                    break;

                case TimePeriodFilter.AllTime:
                    break;
                case TimePeriodFilter.Custom:
                    projectMetricsViews = projectMetricsViews
                    .Where(pmv => pmv.Year >= startDate.Value.Year && pmv.Month >= startDate.Value.Month &&
                                  pmv.Year <= endDate.Value.Year && pmv.Month <= endDate.Value.Month);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(timePeriod), "Invalid TimePeriodFilter value.");
            }
            return projectMetricsViews;
        }


        private ProjectMetricPercentage GetMetricsForOneMonthSummary(IQueryable<ProjectMetricsView> projectMetricsViews, int currentYear, int currentMonth)
        {
            var lastMonthMetrics = projectMetricsViews
                .Where(pmv => pmv.Year == currentYear && pmv.Month == currentMonth - 1)
                .FirstOrDefault();

            return new ProjectMetricPercentage
            {
                ProjectPercentage = new ProjectPercentage
                {
                    ProjectCount = projectMetricsViews.Sum(pmv => pmv.TotalSprints),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalHours), lastMonthMetrics?.TotalHours ?? 0)
                },
                SprintPercentage = new SprintPercentage
                {
                    SprintHour = projectMetricsViews.Sum(pmv => pmv.TotalHours),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalHours), lastMonthMetrics?.TotalHours ?? 0)
                },
                TodoPercentage = new TodoPercentage
                {
                    TodoCount = projectMetricsViews.Sum(pmv => pmv.TotalTodo),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalTodo), lastMonthMetrics?.TotalTodo ?? 0)
                }
            };
        }

        private ProjectMetricPercentage GetMetricsForThreeMonthsSummary(IQueryable<ProjectMetricsView> projectMetricsViews, int currentYear, int currentMonth)
        {
            var lastThreeMonthsMetrics = projectMetricsViews
                .Where(pmv => pmv.Year == currentYear && pmv.Month >= currentMonth - 3 ||
                              pmv.Year == currentYear - 1 && pmv.Month >= currentMonth + 9)
                .ToList();

            return new ProjectMetricPercentage
            {
                ProjectPercentage = new ProjectPercentage
                {
                    ProjectCount = projectMetricsViews.Sum(pmv => pmv.TotalSprints),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalHours), lastThreeMonthsMetrics.Sum(pmv => pmv.TotalHours))
                },
                SprintPercentage = new SprintPercentage
                {
                    SprintHour = projectMetricsViews.Sum(pmv => pmv.TotalHours),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalHours), lastThreeMonthsMetrics.Sum(pmv => pmv.TotalHours))
                },
                TodoPercentage = new TodoPercentage
                {
                    TodoCount = projectMetricsViews.Sum(pmv => pmv.TotalTodo),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalTodo), lastThreeMonthsMetrics.Sum(pmv => pmv.TotalTodo))
                }
            };
        }

        private ProjectMetricPercentage GetMetricsForOneYearSummary(IQueryable<ProjectMetricsView> projectMetricsViews, int currentYear)
        {
            var lastYearMetrics = projectMetricsViews
                .Where(pmv => pmv.Year == currentYear - 1)
                .FirstOrDefault();

            return new ProjectMetricPercentage
            {
                ProjectPercentage = new ProjectPercentage
                {
                    ProjectCount = projectMetricsViews.Sum(pmv => pmv.TotalSprints),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalHours), lastYearMetrics?.TotalHours ?? 0)
                },
                SprintPercentage = new SprintPercentage
                {
                    SprintHour = projectMetricsViews.Sum(pmv => pmv.TotalHours),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalHours), lastYearMetrics?.TotalHours ?? 0)
                },
                TodoPercentage = new TodoPercentage
                {
                    TodoCount = projectMetricsViews.Sum(pmv => pmv.TotalTodo),
                    PercentageIncrement = CalculatePercentage(projectMetricsViews.Sum(pmv => pmv.TotalTodo), lastYearMetrics?.TotalTodo ?? 0)
                }
            };
        }

        private static ProjectMetricPercentage GetMetricsForAllTimeSummary(IQueryable<ProjectMetricsView> projectMetricsViews)
        {
            return new ProjectMetricPercentage
            {
                ProjectPercentage = new ProjectPercentage
                {
                    ProjectCount = projectMetricsViews.Sum(pmv => pmv.TotalSprints),
                    PercentageIncrement = 0 // No percentage increment for AllTime
                },
                SprintPercentage = new SprintPercentage
                {
                    SprintHour = projectMetricsViews.Sum(pmv => pmv.TotalHours),
                    PercentageIncrement = 0 // No percentage increment for AllTime
                },
                TodoPercentage = new TodoPercentage
                {
                    TodoCount = projectMetricsViews.Sum(pmv => pmv.TotalTodo),
                    PercentageIncrement = 0 // No percentage increment for AllTime
                }
            };
        }

        private static double CalculatePercentage(double current, double previous)
        {
            if (previous == 0)
                return 0;
            return (current - previous) / previous * 100;
        }
        #endregion
    }
}
