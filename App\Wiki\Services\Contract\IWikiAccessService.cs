using Jobid.App.Helpers;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Wiki.ViewModel;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Wiki.Services.Contract
{
    public interface IWikiAccessService
    {
        /// <summary>
        /// Update wiki access for multiple users
        /// </summary>
        /// <param name="accessUpdates">List of user IDs and their access status</param>
        /// <returns>Success status</returns>
        Task<GenericResponse> UpdateWikiAccess(List<WikiAccessDto> accessUpdates);
        
        /// <summary>
        /// Get all team members with their wiki access status
        /// </summary>
        /// <param name="filter">Filter by access status (All, Access, NoAccess)</param>
        /// <param name="paginationParameters">Pagination parameters</param>
        /// <returns>List of team members with their wiki access status</returns>
        Task<GenericResponse> GetAllTeamMembersWithWikiAccess(string filter, PaginationParameters paginationParameters);

        /// <summary>
        /// Check if a user has access to the wiki
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<bool> HasAccessToWiki(string userId);
    }
}
