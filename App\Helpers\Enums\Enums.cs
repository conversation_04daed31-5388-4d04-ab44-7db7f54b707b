using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.Enums
{
    public enum Industries
    {
        Technology = 1,
        Healthcare,
        Finance,
        Retail,
        Education,
        Manufacturing,
        Transportation,
        Hospitality,
        Entertainment,
        Construction
    }

    public enum MolliePaymentStatus
    {
        open,
        pending,
        canceled,
        expired,
        failed,
        paid,
        paidout,
        refunded,
        charged_back
    }

    public enum Permissions
    {
        [Description("can_add_users")]
        can_add_users,

        [Description("can_remove_user")]
        can_remove_user,

        [Description("can_create_event")]
        can_create_event,

        [Description("can_assign_roles_and_permissions")]
        can_assign_roles_and_permissions,

        [Description("can_create_edit_delete_projects")]
        can_create_edit_delete_projects,

        [Description("can_create_edit_delete_todos")]
        can_create_edit_delete_todos,

        [Description("can_create_edit_delete_sprints")]
        can_create_edit_delete_sprints,

        [Description("can_assign_team's_todos")]
        can_assign_teams_todos,

        [Description("can_access_and_modify_timesheet")]
        can_access_and_modify_timesheet,

        [Description("can_download_timesheet_report")]
        can_download_timesheet_report,

        [Description("can_adjust_timesheet_entries")]
        can_adjust_timesheet_entries,

        [Description("can_lock_todo")]
        can_lock_todo,

        [Description("can_adjust_project_rate")]
        can_adjust_project_rate,

        [Description("can_view_other_users_project")]
        can_view_other_users_project,

        [Description("can_update_other_user's_project")]
        can_update_other_users_project,

        [Description("can_toggle_activity_off_and_on")]
        can_toggle_activity_off_and_on,

        [Description("can_view_other_team_members_activities")]
        can_view_other_team_members_activities,

        [Description("can_view_other_team_members_timesheet")]
        can_view_other_team_members_timesheet,

        [Description("can_create_meetings")]
        can_create_meetings,

        [Description("can_view_other_team_members_calender")]
        can_view_other_team_members_calender,

        [Description("can_create_triggers")]
        can_create_triggers,

        [Description("all")]
        all
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum UserTypes
    {
        IndividualUser,
        CompanyUser,
        CompanyAdmin
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum MeetingOwnerTypes
    {
        Admin,
        TeamMember
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ApprovalStatus
    {
        Pending,
        Approved,
        Declined
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum Applications
    {
        Joble = 1,
        JobPays,
        JobFy,
        Echo,
        JobID,
        All
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum UserOnlineStatusOptions
    {
        Active = 1,
        Offline,
        Away,
        OutOfOffice
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum Status
    {
        Active,InActive,Converted,Archived
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum DealStage
    {
        Discovery,
        Handover,
        Interview,
        Sold,
        Lost,
        Archived,
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ExternalMeetingTypePlan
    {
        Free,
        Paid
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum PersonalScheduleType
    {
        Work,
        Emergency,
        Custom
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ExternalMeetingType
    {
        [Display(Name = "One on One")]
        OneOnOne,
        [Display(Name = "Round Robin")]
        RoundRobin,
        [Display(Name = "Group Meeting")]
        Group
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ComfirmationPageOptions
    {
        Default,
        ExternalWebsite,
        JobProjectComfirmationPage
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum NotifyMeVia
    {
        Email,
        Calender,
        Both
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum InternalOrExternal
    {
        Internal,
        External
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum CalenderScheduleType
    {
        Meeting,
        Event
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum Region
    {
        Africa,
        EU
    }

    public enum FinancialService
    {
        VFD,
        Weavr
    }
    public enum EmploymentType
    {
        FullTime,
        Contract,
        PartTime,
        FreeLance
    }
    public enum MostUsedPackagesFrequency
    {
        FrequentlyUsed,
        Onboarded,
        TimeTracking,
        TodoCreation
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ApplicationSection
    {
        Calendar,
        Todo,
        Project,
        Chat,
        Onboarding,
        Other
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum TimeRangeFilter
    {
        ThisMonth,
        LastMonth,
        ThreeMonthsAgo,
        SixMonthsAgo,
        ThisYear,
        LastYear
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum EmployeeDistributionFrequency
    {
       ThisMonth,
       ThisWeek,
       ThisYear,
       LastMonth,
       LastWeek,
       LastYear,
       ThreeMonthsAgo,
       SixMonthsAgo,
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum EmployeeStatus
    {
        NotVerified,
        Pending,
        Verified
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum SigningPower
    {
        Dependent,
        InDependent
    }
    public enum LocationType
    {
        Remote,
        OnSite,
        Hybrid
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum JobVacancyStatus
    {
       Ongoing,
       New,
       Rejected,
       OnHold,
       Closed,
       Rollover,
       ProfileSubmittedAwaitingFeedBack
    }

    public enum InterviewProcessType
    {
        InternalHiring,
        ExternalHiring
    }

    public enum ContactStatus
    {
        Active,InActive
    }

    public enum ProjectManagementStatus
    {
        Todo,
        InProgress,
        Review,
        Completed,
        OverDue
    }

    public enum ProjectManagementPriority
    {
        Low,
        Medium,
        High,
    }

    public enum SignatoriesPower
    {
        Dependent,
        Independent
    }
    public enum SignatoriesLevel
    {
        Primary,
        Secondary
    }
    public enum CompanyStatus
    {
        Active, InActive, Archived
    }

    public enum ContactType
    {
        company = 0,
        deal = 1,

        lead = 2,
    }
    public enum KycFieldType
    {
        Text,
        Number,
        Date,
        Checkbox,
        Upload
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum KYCTrustLevels {
        HighTrust,
        MidTrust,
        LowTrust,
        ZeroTrust,
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum Tags
    {
        Internal,
        External,
        EntryLevel,
        Management
    }
    public enum CompanyUserInviteStatus {
        PendingFinalization,
        Expired,
    }

    public enum KYCFields {
        Passport,
        DriversLicence,
        NationalID,
        Phone,
        Email,
        Designation,
        DOB,
        FaceRecognition,
        FirstGuarantor,
        SecondGuarantor,
        UserId,
        PEP,
        AML,
    }

    public enum ProjectManagementTodoStatus
    {
        InProgress,
        Completed,
        Overdue
    }

    public enum OTPIdentifierType {
        Phone,
        Email
    }

    public enum TimeSheetStatus
    {
        InProgress,
        Completed,
        Overdue
    }

    public enum TimeSheetPriority
    {
        InProgress,
        Completed,
        Overdue
    }

    public enum OTPTokenType {
        TenantRegistration,
        Generic,
        ForgotPassword,
        UserSignup,
        _2FA
    }

    public enum CRMTodoStatus
    {
        Active,InActive
    }

    public enum CRMNotificationStatus
    {
        Unread,Read
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum TriggerReason
    {
        PaymentDue,
        ProjectDueDate,
        ProjectOverDue,
        TodoOverdue,
        Personal,
        Generic
    }

    public enum TriggerAction
    {
        Email,
        Alarm
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ProjectStatus
    {
        Active,
        Overdue,
        Completed
    }
    [JsonConverter(typeof(StringEnumConverter))]
    public enum DealType {
        Outsourcing,
        Outstaffing
    }

    public enum CommentStatus {
        ToDo,
        InProgress,
        Completed,
        Archived,
    }


    [JsonConverter(typeof(StringEnumConverter))]
    public enum SprintStatus
    {
        Active = 1,
        Completed,
        EndSprint,
        LogJam
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum MeetingFrequency
    {
        OneOff = 1,
        Daily,
        Weekly,
        Monthly,
        Yearly
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum ExternalMeetingFrequency
    {
        OneOff,
        ReOccuring
    }


    [JsonConverter(typeof(StringEnumConverter))]
    public enum MeetingStatus
    {
        Active,
        InActive
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum Dispute
    {
        NoDispute,
        Dispute
    }

    public enum KpiAnalysisFrequency
    {
        Daily, Weekly, Monthly
    }
    public enum DocumentType
    {
        DriversLicense,
        NIN,
        Passport,
        Grauntee1,
        Grauntee2
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum Currency
    {
        EUR,
        USD,
        GBP,
        NGN
    }

   public enum OverviewRangeEnum
    {
        [Display(Name = "All")]
        All,
        [Display(Name = "Last 30 days")]
        LastThirtyDays,
    }

    [JsonConverter(typeof(StringEnumConverter))]
    public enum AccessTypeFilter
    {
        All,
        Basic,
        Full
    }
}
