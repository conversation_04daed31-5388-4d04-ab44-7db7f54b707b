﻿using Jobid.App.AdminConsole.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models.Calls
{
    public class CallTranscription
    {
        public Guid Id { get; set; }
        public string CompanyId { get; set; }
        public Actions Action { get; set; }

        [Required]
        public string FromNumber { get; set; }

        [Required]
        public string ToNumber { get; set; }

        [Required]
        public string Transcription { get; set; }
        public string CustomerName { get; set; }

        [Required]
        public double CallDuration { get; set; }

        public CallDirection CallDirection { get; set; }

        // Meeting details
        public string CustomerEmail { get; set; }
        public string EmployeeName { get; set; }
        public DateTime? MeetingDate { get; set; }
        public TimeSpan? MeetingTime { get; set; }
        public double? DurationInMinutes { get; set; }

        // Action Status
        public ActionStatus ActionStatus { get; set; }

        // Audit properties
        public DateTime CallDate { get; set; }
        public DateTime? UpdatedOn { get; set; }

        public CallTranscription()
        {
            Id = Guid.NewGuid();
            CallDate = DateTime.UtcNow;
            ActionStatus = ActionStatus.NoActionTaken; // Default status
            Action = Actions.NoAction; // Default action
            UpdatedOn = null; // Initially null, will be set on update
        }
    }
}
