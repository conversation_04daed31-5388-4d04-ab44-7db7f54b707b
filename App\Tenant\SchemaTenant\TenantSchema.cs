using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Tenant.SchemaTenant;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.Tenant
{

    public interface ITenantSchema
    {
        JobProDbContext getRequestContext();
        JobProDbContext getRequestContext(HttpContext context);
        Task<bool> DoesCurrentSubdomainExist();
        Task<bool> RunMigrations(string schemaName);
        Task<bool> NewSchema(string schemaName);
        string ExtractSubdomainFromRequest(HttpContext httpContext);
    }

    public class TenantSchema : ITenantSchema
    {
        public string _schema;
        private string conString;
        public JobProDbContext context;
        private readonly CustomAppSettings _appSettings;
        private readonly IConfiguration _config;

        public TenantSchema()
        {
            _config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            conString = GlobalVariables.ConnectionString;
            this.context = new JobProDbContext(conString, new DbContextSchema());
            _appSettings = GlobalVariables.CustomAppSettings;
        }

        public TenantSchema(HttpContext context)
        {
            _config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            conString = GlobalVariables.ConnectionString;

            // create new schema if not exist
            _schema = getSubdomainName(context);
            this.context = new JobProDbContext(conString, new DbContextSchema(_schema));
            _appSettings = GlobalVariables.CustomAppSettings;
        }

        public async Task<bool> NewSchema(string schemaName)
        {
            try
            {
                schemaName = schemaName.prepareSubdomainName();

                // Check if schema exists
                var sql = $"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{schemaName}'";
                var schemaExists = context.Database.ExecuteScalar(sql);

                if (schemaExists == null)
                {
                    // Create schema if it doesn't exist
                    await context.Database
                        .ExecuteSqlRawAsync($"CREATE SCHEMA {schemaName}");

                    // Verify schema was created
                    sql = $"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{schemaName}'";
                    schemaExists = context.Database.ExecuteScalar(sql);
                }

                return schemaExists != null;
            }
            catch (Exception e)
            {
                throw;
            }
        }

        private async Task<int> ExecSQL(string sql)
        {
            return (int)await this.context.Database.ExecuteSqlRawAsync(sql);
        }

        private T ExecScalar<T>(string sql, List<DbParameter> parameters)
        {
            return (T)context.ExecuteScalar(sql, parameters);
        }

        public string getSubdomainName(HttpContext context)
        {
            var _schemaName = string.Empty;
            if (context.Request.Headers.TryGetValue("subdomain", out var subdomain))
            {
                _schemaName = subdomain;
            }

            _schemaName = _schemaName.prepareSubdomainName();
            return _schemaName;
        }        public async Task<bool> RunMigrations(string schemaName)
        {
            try
            {
                schemaName = schemaName.prepareSubdomainName();
                var schema = await this.NewSchema(schemaName);
                if (!schema) throw new Exception("Schema not created.");
                this.context = new JobProDbContext(conString, new DbContextSchema(schemaName));

                // Apply migrations with retry logic for serialization errors
                await RetryHelper.RetryOnSerializationError(
                    async () => 
                    {
                        // check if pending migrations first
                        await Task.Run(() => this.context.Database.Migrate());
                    },
                    maxRetries: 5,
                    delayMs: 1500
                );
                
                return true;
            }            catch (Exception ex)
            {
                Console.WriteLine(ex);
                throw;
            }
        }

        public JobProDbContext getRequestContext()
        {
            return this.context;
        }

        public JobProDbContext getRequestContext(HttpContext context)
        {
            return new JobProDbContext(conString, new DbContextSchema(getSubdomainName(context)));
        }

        public async Task<bool> DoesCurrentSubdomainExist()
        {
            string rootDomain = _appSettings.RootDomain;
            var tenant = await this.context.Tenants.FirstOrDefaultAsync(x => x.Subdomain == _schema);
            if (_schema == "api" || _schema == "admin" || _schema == rootDomain)
            {
                return true;
            }
            if (tenant == null) return false;
            return true;
        }

        public string ExtractSubdomainFromRequest(HttpContext httpContext)
        {
            var _host = httpContext.Request.Headers["Referer"].ToString();
            Console.WriteLine($"Refer Host {_host}");
            if (string.IsNullOrEmpty(_host))
            {
                _host = httpContext.Request.Host.ToString();
            }

            _host = getSubdomainName(httpContext);
            return _host;
        }
    }
}