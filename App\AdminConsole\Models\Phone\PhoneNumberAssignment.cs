using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models.Phone
{
    public class PhoneNumberAssignment
    {
        [Key]
        public Guid Id { get; set; }
        public Guid PhoneNumberId { get; set; }
        public string UserId { get; set; }
        public string AssignedBy { get; set; }
        public DateTime AssignedAt { get; set; }

        // Navigation properties
        public virtual PhoneNumber PhoneNumber { get; set; }

        [NotMapped]
        public UserProfile UserProfile { get; set; }

        public PhoneNumberAssignment()
        {
            Id = Guid.NewGuid();
            AssignedAt = DateTime.UtcNow;
        }
    }
}
