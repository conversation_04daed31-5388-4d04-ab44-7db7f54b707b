﻿using Jobid.App.Helpers.Models;
using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class UserDetailsForTeamSheetDto
    {
        public string FullName { get; set; }
        public string Email { get; set; }
        public string UserId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public int AssignedTodosCount { get; set; }
        public int HoursWorked { get; set; }
        public string WorkStatus { get; set; }
        public Decimal TotalAmountEarned { get; set; }
        public int CompletedTodoCount { get; set; }
        public List<ProjectMgmt_Todo> Todos { get; set; } = new List<ProjectMgmt_Todo>();
    }
}
