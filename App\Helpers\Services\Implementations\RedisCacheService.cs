﻿using Jobid.App.Helpers.Configurations;
using Jobid.App.Helpers.Services.Contract;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Linq;
using System.Threading.Tasks;
using WatchDog;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class RedisCacheService : IRedisCacheService
    {
        private IDatabase _db;
        public RedisCacheService(IConfiguration configuration)
        {
            ConfigureRedis(configuration);
        }

        #region Configure Redis
        private void ConfigureRedis(IConfiguration configuration)
        {
            var host = configuration["Redis"];
            var awsOptions = configuration.GetSection("AWSConfigOptions").Get<AWSConfigOptions>();

            if (!string.IsNullOrEmpty(host))
            {
                try
                {
                    // Don't add the user is its default; it will cause an error - This is for local Redis
                    var configurationOptions = new ConfigurationOptions();
                    configurationOptions.AbortOnConnectFail = false;
                    configurationOptions.IncludeDetailInExceptions = true;

                    var uri = new Uri(host);
                    configurationOptions.Password = uri.UserInfo.Split(':').Length > 1 ? uri.UserInfo.Split(':')[1] : null;
                    configurationOptions.EndPoints.Add(uri.Host, uri.Port);
                    configurationOptions.DefaultDatabase = uri.LocalPath.Length > 1 ? int.Parse(uri.LocalPath.Substring(1)) : 0;

                    // Set connection timeout
                    configurationOptions.ConnectTimeout = 90000;

                    // This is for AWS ElastiCache
                    awsOptions.ElasticCache = Environment.GetEnvironmentVariable("AWS_ELASTIC_CACHE") ?? awsOptions.ElasticCache;
                    awsOptions.ElasticCachePassword = Environment.GetEnvironmentVariable("AWS_ELASTIC_CACHE_PASSWORD") ?? awsOptions.ElasticCachePassword;
                    var options = new ConfigurationOptions
                    {
                        EndPoints = { awsOptions.ElasticCache },
                        Ssl = true
                    };
                    options.AbortOnConnectFail = false;
                    options.IncludeDetailInExceptions = true;
                    options.Password = awsOptions.ElasticCachePassword;
                    options.DefaultDatabase = 0;

                    var redis = ConnectionMultiplexer.Connect(configurationOptions);
                    _db = redis.GetDatabase();
                }
                catch (Exception ex)
                {
                    // Log the error with details (e.g., using a logger)
                    Console.WriteLine($"Error connecting to Redis: {ex.Message}");  // For demonstration purposes, replace with proper logging
                    _db = null;
                }
            }
        }
        #endregion

        #region Get Data From Redis Cache
        /// <summary>
        /// Get Data using key
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        public async Task<T> GetDataAsync<T>(string key)
        {
            if (_db is null)
                return default;

            try
            {
                var value = await _db.StringGetAsync(key);
                if (!value.IsNull)
                {
                    return JsonConvert.DeserializeObject<T>(value);
                }
                return default;
            }
            catch (Exception ex)
            {
                WatchLogger.LogError($"Error getting data from Redis: {ex.Message}");
                return default;
            }
        }
        #endregion

        #region Set Data To Redis Cache
        /// <summary>
        /// Set Data with Value and Expiration Time of Key
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expirationTime"></param>
        /// <returns></returns>
        public async Task<bool> SetDataAsync<T>(string key, T value, DateTimeOffset expirationTime)
        {
            if (_db is null)
                return false;

            try
            {
                TimeSpan expiryTime = expirationTime.DateTime.Subtract(DateTime.UtcNow);
                var isSet = await _db.StringSetAsync(key, JsonConvert.SerializeObject(value, Formatting.Indented,
                                                    new JsonSerializerSettings()
                                                    {
                                                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                                                    }), expiryTime);
                return isSet;
            }
            catch (Exception ex)
            {
                WatchLogger.LogError($"Error setting data in Redis: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region Remove Data From Redis Cache
        /// <summary>
        /// Remove Data
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public async Task<bool> RemoveDataAsync(string key)
        {
            if (_db is null)
                return false;

            try
            {
                bool _isKeyExist = await _db.KeyExistsAsync(key);
                if (_isKeyExist == true)
                {
                    return await _db.KeyDeleteAsync(key);
                }
                return true;
            }
            catch (Exception ex)
            {
                WatchLogger.LogError($"Error removing data from Redis: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region Get all keys from Redis Cache
        public async Task<string[]> GetAllKeysAsync()
        {
            if (_db is null)
                return null;

            try
            {
                var server = _db.Multiplexer.GetServer(_db.Multiplexer.GetEndPoints().First());
                var keysToReturn = server.Keys().Select(k => k.ToString()).ToArray();

                return await Task.FromResult(keysToReturn);
            }
            catch (Exception ex)
            {
                WatchLogger.LogError($"Error getting all keys from Redis: {ex.Message}");
                return null;
            }
        }
        #endregion
    }
}
