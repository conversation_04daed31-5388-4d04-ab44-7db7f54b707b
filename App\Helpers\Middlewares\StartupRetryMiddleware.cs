using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using Npgsql;
using Jobid.App.Helpers.Utils;
using Microsoft.AspNetCore.Hosting;

namespace Jobid.App.Helpers.Middlewares
{
    /// <summary>
    /// Helper to handle PostgreSQL serialization errors during application startup
    /// </summary>
    public static class StartupRetryMiddleware
    {
        /// <summary>
        /// Safely executes startup operations that may encounter PostgreSQL serialization errors
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <param name="operation">The startup operation to execute</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="maxRetries">Maximum retry attempts</param>
        /// <param name="baseDelayMs">Base delay between retries in milliseconds</param>
        /// <param name="isRequired">Whether the operation is required for startup to continue</param>
        /// <returns>True if operation succeeded, false if it failed but wasn't required</returns>
        public static async Task<bool> ExecuteStartupOperation(
            IServiceProvider serviceProvider,
            Func<Task> operation,
            string operationName,
            int maxRetries = 5,
            int baseDelayMs = 2000,
            bool isRequired = true)
        {
            var logger = serviceProvider.GetService<ILogger<object>>();
            var environment = serviceProvider.GetService<IWebHostEnvironment>();
            
            try
            {
                logger?.LogInformation("Starting startup operation: {OperationName}", operationName);
                
                await RetryHelper.RetryOnSerializationError(
                    operation,
                    maxRetries: maxRetries,
                    delayMs: baseDelayMs,
                    logger: logger
                );
                
                logger?.LogInformation("Successfully completed startup operation: {OperationName}", operationName);
                return true;
            }
            catch (PostgresException ex) when (ex.SqlState == "40001")
            {
                logger?.LogError(ex, "Failed to complete startup operation '{OperationName}' after {MaxRetries} attempts due to serialization errors", 
                    operationName, maxRetries);
                
                if (isRequired)
                {
                    logger?.LogCritical("Required startup operation '{OperationName}' failed - application startup cannot continue", operationName);
                    throw;
                }
                
                logger?.LogWarning("Optional startup operation '{OperationName}' failed - continuing with application startup", operationName);
                return false;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Unexpected error during startup operation: {OperationName}", operationName);
                
                if (isRequired)
                {
                    logger?.LogCritical("Required startup operation '{OperationName}' failed with unexpected error - application startup cannot continue", operationName);
                    throw;
                }
                
                logger?.LogWarning("Optional startup operation '{OperationName}' failed with unexpected error - continuing with application startup", operationName);
                return false;
            }
        }

        /// <summary>
        /// Safely executes synchronous startup operations that may encounter PostgreSQL serialization errors
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <param name="operation">The startup operation to execute</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="maxRetries">Maximum retry attempts</param>
        /// <param name="baseDelayMs">Base delay between retries in milliseconds</param>
        /// <param name="isRequired">Whether the operation is required for startup to continue</param>
        /// <returns>True if operation succeeded, false if it failed but wasn't required</returns>
        public static bool ExecuteStartupOperation(
            IServiceProvider serviceProvider,
            Action operation,
            string operationName,
            int maxRetries = 5,
            int baseDelayMs = 2000,
            bool isRequired = true)
        {
            var logger = serviceProvider.GetService<ILogger<object>>();
            var environment = serviceProvider.GetService<IWebHostEnvironment>();
            
            try
            {
                logger?.LogInformation("Starting startup operation: {OperationName}", operationName);
                
                RetryHelper.RetryOnSerializationError(
                    operation,
                    maxRetries: maxRetries,
                    delayMs: baseDelayMs,
                    logger: logger
                );
                
                logger?.LogInformation("Successfully completed startup operation: {OperationName}", operationName);
                return true;
            }
            catch (PostgresException ex) when (ex.SqlState == "40001")
            {
                logger?.LogError(ex, "Failed to complete startup operation '{OperationName}' after {MaxRetries} attempts due to serialization errors", 
                    operationName, maxRetries);
                
                if (isRequired)
                {
                    logger?.LogCritical("Required startup operation '{OperationName}' failed - application startup cannot continue", operationName);
                    throw;
                }
                
                logger?.LogWarning("Optional startup operation '{OperationName}' failed - continuing with application startup", operationName);
                return false;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Unexpected error during startup operation: {OperationName}", operationName);
                
                if (isRequired)
                {
                    logger?.LogCritical("Required startup operation '{OperationName}' failed with unexpected error - application startup cannot continue", operationName);
                    throw;
                }
                
                logger?.LogWarning("Optional startup operation '{OperationName}' failed with unexpected error - continuing with application startup", operationName);
                return false;
            }
        }
    }
}
