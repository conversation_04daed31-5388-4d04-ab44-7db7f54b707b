﻿syntax = "proto3";

option csharp_namespace = "Jobid";

service GrpcAuthService {
    rpc Authenticate(GrpcAuthenticateRequest) returns (GrpcAuthenticationResponse) {}
}

//Request
message GrpcAuthenticateRequest {
    string ServiceId = 1;
    string SecretKey = 2;
}

message GrpcAuthenticationResponse {
    string ServiceId = 1;
    string ServiceName = 2;
    string Token = 3;
    string Message = 4;
    int32 Code = 5;
}