﻿using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    public class SprintController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private readonly ITenantSchema _tenantSchema;
        private readonly ILogger _logger = Log.ForContext<SprintController>();

        public SprintController(IUnitofwork unitofwork, ITenantSchema tenantSchema)
        {
            this.Services_Repo = unitofwork;
            _tenantSchema = tenantSchema;
        }

        #region Add Sprint
        /// <summary>
        /// Create a sprint under a project
        /// </summary>
        /// <param name="model"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPost]
        [Route("AddSprint/{projectId}")]
        public async Task<ActionResult> AddSprint(AddSprintDto model, Guid projectId)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.SubDomian = subdomain;
                model.ProjectMgmt_ProjectId = projectId;
                var project = await this.Services_Repo.ProjectService.GetProjectMgmt_ProjectId(projectId);
                if (project == null)
                {
                    return BadRequest(new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "No Project Found   ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                // Check if the sprint enddate is greater than project enddate
                if (model.EndDate > project.EndDate)
                {
                    return BadRequest(new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "Sprint End Date cannot be greater than Project End Date ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                // Check if the sprint startdate is less than project startdate
                if (model.StartDate < project.StartDate)
                {
                    return BadRequest(new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "Sprint Start Date cannot be less than Project Start Date ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                if (model.EndDate <= model.StartDate)
                {
                    return BadRequest(new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "Invalid Date Range ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                var result = await Services_Repo.SprintProjectService.AddSprint_ToProject(model, project);
                if (result != null)
                {
                    var description = $"Sprint Created by {CurrentUser}";
                    var summary = $"Sprint Created";
                    await LogActivity(description, summary, result.Id);

                    return Ok(new ApiResponse<AddSprintDto>
                    {
                        ResponseMessage = "Sprint added Successfully ",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "Cannot add project ",
                        ResponseCode = "400",
                        Data = null
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<ProjectMgmt_Project>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = null
                });
            }
        }
        #endregion

        #region Update Sprint
        /// <summary>
        /// Update Sprint
        /// </summary>
        /// <param name="model"></param>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPut]
        [Route("UpdateSprint/{sprintId}")]
        public async Task<IActionResult> Put(SprintProjectVm model, string sprintId)
        {
            if (sprintId == null) { return BadRequest(new ApiResponse<bool> { ResponseCode = "400", ResponseMessage = "ProjectMgmt_Project Id is Required", Data = false }); }

            if (model.EndDate <= model.StartDate)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Invalid Date Range ",
                    ResponseCode = "400",
                    Data = false
                });
            }

            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            model.SubDomian = subdomain;
            var project = await this.Services_Repo.ProjectService.GetProjectMgmt_SprintId(Guid.Parse(sprintId));
            model.ProjectMgmt_ProjectId = project.ProjectId;

            if (project == null)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "No Project Found   ",
                    ResponseCode = "400",
                    Data = false
                });
            }

            // Check if the sprint enddate is greater than project enddate
            if (model.EndDate > project.EndDate)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Sprint End Date cannot be greater than Project End Date ",
                    ResponseCode = "400",
                    Data = false
                });
            }

            try
            {
                var sprint = await this.Services_Repo.SprintProjectService.GetSprintById(sprintId);
                if (sprint == null)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "No Sprint Found   ",
                        ResponseCode = "400",
                        Data = false
                    });
                }

                model.LoggedInUserId = CurrentUserId.ToString();
                var result = await this.Services_Repo.SprintProjectService.UpdateSprintProject(model, sprint);
                if (result)
                {
                    // Log Activity
                    var description = $"Sprint Updated By {CurrentUser}";
                    var summary = $"Sprint Updated";
                    await LogActivity(description, summary, sprintId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Sprint Updated Successfully ",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Cannot Update Sprint ",
                        ResponseCode = "400",
                        Data = false
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Update Sprint Status
        /// <summary>
        /// Update Sprint Status - this endpoint can be used to complete and re-open a sprint
        /// </summary>
        /// <param name="sprintId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPut]
        [Route("UpdateSprintStatus/{sprintId}")]
        public async Task<IActionResult> UpdateSprintStatus(string sprintId, SprintStatus status)
        {
            try
            {
                var sprint = await this.Services_Repo.SprintProjectService.GetSprintById(sprintId);
                if (sprint == null)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "No Sprint Found   ",
                        ResponseCode = "400",
                        Data = false
                    });
                }

                // Update Sprint
                var result = await this.Services_Repo.SprintProjectService.UpdateSprintStatus(sprintId, status, CurrentUserId.ToString());
                if (result)
                {
                    // Log Activity
                    var description = $"Sprint Status Updated By {CurrentUser}";
                    var summary = $"Sprint Status Updated";
                    await LogActivity(description, summary, sprintId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Sprint Status Updated Successfully ",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Cannot Update Sprint Status ",
                        ResponseCode = "400",
                        Data = false
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get All Sprints
        /// <summary>
        /// Gets all sprints
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.all)]
        [HttpGet]
        [Route("GetAllSprints")]
        public async Task<ApiResponse<Page<SprintProject>>> GetAllSprints([FromQuery] PaginationParameters parameters)
        {
            var result = await this.Services_Repo.SprintProjectService.GetAllSprints(parameters);
            if (result.Items.Length > 0)
            {
                // Log Activity
                var description = $"All Sprints Retrieved By {CurrentUser}";
                var summary = $"Sprint Retrived";
                await LogActivity(description, summary);

                return new ApiResponse<Page<SprintProject>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<Page<SprintProject>>
                {
                    ResponseMessage = "No Sprint Found",
                    ResponseCode = "200",
                    Data = null
                };
            }
        }
        #endregion

        #region Add todo status
        /// <summary>
        /// Add todo status
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPost]
        [Route("AddTodoStatus")]
        public async Task<ActionResult> AddTodoStatus(AddTodoStatusVm model)
        {
            if (string.IsNullOrEmpty(model.SprintId.ToString()))
            {
                return BadRequest(new ApiResponse<SprintProject>
                {
                    ResponseMessage = "Sprint Id cannot be null ",
                    ResponseCode = "400",
                    Data = null
                });
            }

            var result = await Services_Repo.SprintProjectService.AddTodoStatus(model);
            if (result is not null)
            {
                // Log Activity
                var description = $"Todo Status Createds By {CurrentUser}";
                var summary = $"Todo Status Created";
                await LogActivity(description, summary);
            }

            return Ok(new ApiResponse<TodoStatus>
            {
                ResponseMessage = "Todo Status added Successfully ",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Get TodoStatus by sprintId
        /// <summary>
        /// Get TodoStatus by sprintId
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTodoStatusById/{sprintId}")]
        public async Task<ApiResponse<List<TodoStatus>>> GetTodoStatusById(string sprintId)
        {
            var result = await this.Services_Repo.SprintProjectService.GetTodoStatus(sprintId);

            // Log Activity
            var description = $"Todo Status Retrieved By {CurrentUser}";
            var summary = $"Todo Status Retrieved";
            await LogActivity(description, summary);

            return new ApiResponse<List<TodoStatus>>
            {
                ResponseMessage = "Todo Status Found",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Update TodoStatus
        /// <summary>
        /// Update TodoStatus
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPut]
        [Route("UpdateTodoStatus/{Id}")]
        public async Task<ActionResult> UpdateTodoStatus(string Id, string status)
        {
            if (string.IsNullOrEmpty(Id))
            {
                return BadRequest(new ApiResponse<SprintProject>
                {
                    ResponseMessage = "Sprint Id cannot be null ",
                    ResponseCode = "400",
                    Data = null
                });
            }

            var result = await Services_Repo.SprintProjectService.UpdateTodoStatus(Id, status);

            if (!result)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Cannot update todo status ",
                    ResponseCode = "400",
                    Data = result
                });
            }

            // Log Activity
            var description = $"Todo Status Updated By {CurrentUser}";
            var summary = $"Todo Status Updated";
            await LogActivity(description, summary, Id);

            return Ok(new ApiResponse<bool>
            {
                ResponseMessage = "Todo Status updated Successfully ",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Delete TodoStatus
        /// <summary>
        /// Delete TodoStatus
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpDelete]
        [Route("DeleteTodoStatus/{Id}")]
        public async Task<ActionResult> DeleteTodoStatus(string Id)
        {
            try
            {
                if (string.IsNullOrEmpty(Id))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Sprint Id cannot be null ",
                        ResponseCode = "400",
                        Data = false
                    });
                }

                var result = await Services_Repo.SprintProjectService.DeleteTodoStatus(Id, CurrentUserId.ToString());

                if (!result)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Todo status could not be deleted",
                        ResponseCode = "400",
                        Data = result
                    });
                }

                // Log Activity
                var description = $"Todo Status Deleted By {CurrentUser}";
                var summary = $"Todo Status Deleted";
                await LogActivity(description, summary, Id);

                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Todo Status deleted Successfully ",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex.Message, "DeleteTodoStatus");
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region Get Sprint By projectId
        /// <summary>
        /// Get sprint by project id
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSprintByProjectIdPaginated/{projectId}")]
        public async Task<ApiResponse<Page<SprintProject>>> GetSprintsByProjectId([FromQuery] PaginationParameters parameters, string projectId)
        {
            var result = await this.Services_Repo.SprintProjectService.GetSprintProjectByIdPaginated(projectId, parameters);
            if (result.Items.Length > 0)
            {
                // Log Activity
                var description = $"Sprints For a Project Retrieved By {CurrentUser}";
                var summary = $"Sprints Retrieved";
                await LogActivity(description, summary);

                return new ApiResponse<Page<SprintProject>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<Page<SprintProject>>
                {
                    ResponseMessage = "No Sprint Found",
                    ResponseCode = "200",
                    Data = null
                };
            }
        }
        #endregion

        #region Get Sprint By Id
        /// <summary>
        /// Gets sprint by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        // [Authorize(Roles = "Administrator")]
        [Route("GetSprintById/{id}")]
        public async Task<ApiResponse<SprintProject>> GetSprintById(string id)
        {
            var result = await this.Services_Repo.SprintProjectService.GetSprintById(id);
            if (result != null)
            {
                // Log Activity
                var description = $"Sprint Retrieved By {CurrentUser}";
                var summary = $"Sprint Retrieved";
                await LogActivity(description, summary, id);

                return new ApiResponse<SprintProject>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<SprintProject>
                {
                    ResponseMessage = "ProjectMgmt_Project Not Found",
                    ResponseCode = "200",
                    Data = null
                };
            }
        }
        #endregion

        #region Delete Sprint
        /// <summary>
        /// Delete Sprint
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpDelete]
        [Route("RemoveSprint/{sprintId}")]
        public async Task<ApiResponse<SprintProject>> Delete(string sprintId)
        {
            if (sprintId == null) { return new ApiResponse<SprintProject> { ResponseCode = "400", ResponseMessage = "Sprint Id is Required", Data = null }; }

            try
            {
                SprintProject sprint = await Services_Repo.SprintProjectService.GetSprintById(sprintId);
                if (sprint == null) { return new ApiResponse<SprintProject> { ResponseCode = "404", ResponseMessage = "ProjectMgmt_Project Not Found", Data = null }; }

                else
                {
                    bool result = await Services_Repo.SprintProjectService.RemoveSprintProject(sprint, CurrentUserId.ToString());
                    if (result == false) { return new ApiResponse<SprintProject> { ResponseCode = "400", ResponseMessage = "Could Not Remove Sprint. Pls Try Again", Data = null }; }
                    else
                    {
                        // Log Activity
                        var description = $"Sprint Deleted By {CurrentUser}";
                        var summary = $"Sprint Deleted";
                        await LogActivity(description, summary, sprintId);

                        return new ApiResponse<SprintProject> { ResponseCode = "200", ResponseMessage = "Removed Succssfully", Data = sprint };
                    }

                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return new ApiResponse<SprintProject>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "403",
                    Data = null
                };
            }
        }
        #endregion

        #region Add internal members to sprint
        /// <summary>
        /// Add internal members to sprint
        /// </summary>
        /// <param name="members"></param>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPost]
        [Route("AddInternalMembers/{sprintId}")]
        public async Task<ActionResult> AddInternalMembersToSprint(InternalMember members, string sprintId)
        {
            try
            {
                // Check if sprint exist
                var sprint = await Services_Repo.SprintProjectService.GetSprintById(sprintId);
                if (sprint == null) { return BadRequest(new { Msg = " Sprint is not found !" }); }
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                members.Subdomain = subdomain;

                // Add internal members to sprint
                var result = Services_Repo.SprintProjectService.AddInternalMemberSprintProject(members, sprintId);
                if (result.Result == true)
                {
                    // Log Activity
                    var description = $"Internal Members {members} Added to Sprint By {CurrentUser}";
                    var summary = $"Members Added";
                    await LogActivity(description, summary, sprintId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Users added succeefully",
                        Data = true
                    });
                }
                else { return BadRequest(new { Msg = "" }); }
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region Add external members to sprint
        /// <summary>
        /// Add external members to sprint
        /// </summary>
        /// <param name="members"></param>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPost]
        [Route("AddExternalMembers/{sprintId}")]
        public async Task<ActionResult> AddExternalMembersToSprint(ExternalMember members, string sprintId)
        {
            try
            {
                members.SprintId = sprintId;
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                members.SubDomain = subdomain;
                members.UserId = CurrentUserId.ToString();

                // Add external members to sprint
                var result = await Services_Repo.SprintProjectService.AddExternalMemberSprintProject(members);
                if (result)
                {
                    // Log Activity
                    var description = $"External Members {members} Added to Sprint By {CurrentUser}";
                    var summary = $"Members Added";
                    await LogActivity(description, summary, sprintId);

                    return Ok(new { Message = "User Added Successfully" });
                }
                else { return BadRequest(new { Msg = "" }); }
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(ex.Message);
            }
        }
        #endregion

        #region Remove members from sprint
        /// <summary>
        /// Remove members from sprint
        /// </summary>
        /// <param name="memberIds"></param>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("RemoveMembersFromSprint/{sprintId}")]
        public async Task<ActionResult> RemoveMemberFromSprint(List<string> memberIds, string sprintId)
        {
            try
            {
                var userId = CurrentUserId.ToString();
                var result = await Services_Repo.SprintProjectService.RemoveMemberFromSprint(memberIds, sprintId, userId);
                if (result)
                {
                    // Log Activity
                    var description = $"Member {memberIds} Removed from Sprint By {CurrentUser}";
                    var summary = $"Member Removed";
                    await LogActivity(description, summary, sprintId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Member Removed Successfully",
                        Data = true
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Could not remove member from sprint",
                        Data = false
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "403",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Get Sprint Reports Summary
        /// <summary>
        /// Get Sprint Reports Summary
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Report/GetSprintReportsSummary")]
        public async Task<ApiResponse<List<SprintReportSummaryDto>>> GetSprintReportsSummary()
        {
            var result = await Services_Repo.SprintProjectService.GetSprintReportSummary();

            // Log Activity
            var description = $"Sprint Reports Summary Fetched By {CurrentUser}";
            var summary = $"Sprint Reports Summary Fetched";
            await LogActivity(description, summary);

            return new ApiResponse<List<SprintReportSummaryDto>>
            {
                ResponseCode = "200",
                ResponseMessage = "Sprint Reports Found",
                Data = result
            };
        }
        #endregion

        #region Get Sprint Reports Details By Id
        /// <summary>
        /// Get Sprint Reports Details By Id
        /// </summary>
        /// <param name="sprintId"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("Report/GetSprintReportsDetails/{sprintId}")]
        public async Task<ApiResponse<SprintReportDetailsDto>> GetSprintReportsDetails([FromQuery] PaginationParameters parameters, string sprintId)
        {
            try
            {
                var newGuid = Guid.Parse(sprintId);
                var result = await Services_Repo.SprintProjectService.GetSprintReportDetails(sprintId);
                result.TodoSummary = await Services_Repo.TodoService.GetAllProjectMgmt_TodobysprintId(parameters, newGuid, CurrentUserId.ToString(), null);

                // Log Activity
                var description = $"Sprint Reports Details Fetched By {CurrentUser}";
                var summary = $"Sprint Reports Details Fetched";
                await LogActivity(description, summary, sprintId);

                return new ApiResponse<SprintReportDetailsDto>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Sprint Reports Details Found",
                    Data = result
                };
            }
            catch (RecordNotFoundException ex)
            {
                return new ApiResponse<SprintReportDetailsDto>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Move Todos to a new/different sprint
        /// <summary>
        /// Move Todos to a new/different sprint
        /// </summary>
        /// <param name="sprintId"></param>
        /// <param name="todoIds"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_sprints)]
        [HttpPut]
        [Route("MoveTodosToSprint/{sprintId}")]
        public async Task<ActionResult> MoveTodosToSprint(string sprintId, [FromBody] List<string> todoIds)
        {
            var result = await Services_Repo.SprintProjectService.MoveTodosToDifferentOrNewSprint(todoIds, sprintId);
            if (result == true)
            {
                // Log Activity
                var description = $"Todos {todoIds} Moved to Sprint By {CurrentUser}";
                var summary = $"Todos Moved";
                await LogActivity(description, summary, sprintId);

                return Ok(new { Msg = "Todos Moved Successfully" });
            }

            else { return BadRequest(new { Msg = "" }); }
        }
        #endregion

        #region Private Method
        private async Task LogActivity(string description, string summary, string? eventId = null)
        {
            var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), EventCategory.Sprint);
            if (canLogActivity)
            {
                var activityModel = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Sprint,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                    EventId = eventId,
                    Application = Applications.Joble
                };
                await Services_Repo.ActivityService.CreateLog(activityModel);
            }
        }
        #endregion
    }
}
