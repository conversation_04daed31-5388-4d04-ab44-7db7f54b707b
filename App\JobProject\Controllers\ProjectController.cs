﻿#region using statements
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using Serilog;
using ILogger = Serilog.ILogger;
using Jobid.App.Helpers.Attributes;
using static Jobid.App.Subscription.Enums.Enums;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Filters;
#endregion

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    [EndpointTrackerFilter(Applications.Joble, ApplicationSection.Project)]
    public class ProjectController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private readonly ITenantSchema _tenantSchema;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly ILogger _logger = Log.ForContext<ProjectController>();

        public ProjectController(IUnitofwork unitofwork, ITenantSchema tenantSchema, IAWSS3Sevices aWSS3Sevices)
        {
            this.Services_Repo = unitofwork;
            _tenantSchema = tenantSchema;
            _aWSS3Sevices = aWSS3Sevices;
        }

        #region Add Project
        /// <summary>
        /// Add Project
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [SubscriptionFeaturesLimitFilter(CategoriesForFeatures.Project)]
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPost]
        [Route("AddProject")]
        public async Task<IActionResult> AddProjects(AddProjectDto model, string userId)
        {
            try
            {
                var subDomain = this._tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                if (model.EndDate <= model.StartDate)
                {
                    return BadRequest(new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "Invalid Date Range ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                var result = await this.Services_Repo.ProjectService.Add_Project(model, userId, subDomain);

                var description = $"Project Created by {CurrentUser}";
                var summary = $"Project Created";
                await LogActivity(description, summary, result.ProjectId.ToString());

                return Ok(new ApiResponse<ProjectMgmt_Project>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (DirtyFormException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                _logger.Error(ex.ToString());
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Activate Project
        /// <summary>
        /// Activate Project
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPost]
        [Route("ActivateProject/{projectId}")]
        public async Task<IActionResult> MakeProjectActive(string projectId)
        {
            try
            {
                var result = await this.Services_Repo.ProjectService.MakeProjectActive(projectId);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.ToString());
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "404",
                    Data = false
                });
            }
        }
        #endregion

        #region Edit project
        /// <summary>
        /// Edits project - not in use
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPost]
        [Route("EditProject")]
        public async Task<IActionResult> Edit_Project(ProjectMgmt_ProjectVM model, string userId)
        {
            var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            model.SubDomain = subdomain;

            if (model.UploadFile.Any())
            {

                var allowedExtensions = new string[] { ".jpg", ".jpeg", ".png", ".pdf" };
                foreach (var file in model.UploadFile)
                {
                    var extension = Path.GetExtension(file.FileName);

                    if (!allowedExtensions.Contains(extension))
                    {
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "File extension not allowed",
                            Data = false
                        });
                    }
                }
            }

            if (model.EndDate <= model.StartDate)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Invalid Date Range ",
                    ResponseCode = "400",
                    Data = false
                });
            }


            var result = await Services_Repo.ProjectService.Edit_Project(model, userId, model.ProjectId);
            if (result)
            {
                // Log activity
                var description = $"Project Updated by {CurrentUser}";
                var summary = $"Project Updated";
                await LogActivity(description, summary, model.ProjectId);

                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Project updated Successfully ",
                    ResponseCode = "200",
                    Data = result
                });
            }
            else
            {
                return BadRequest(new ApiResponse<SprintProject>
                {
                    ResponseMessage = "Cannot add project ",
                    ResponseCode = "400",
                    Data = null
                });
            }
        }
        #endregion

        #region Get All Project
        /// <summary>
        /// Gets all projects
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_view_other_users_project)]
        [HttpGet]
        [Route("GetProjects")]
        public async Task<ApiResponse<Page<ProjectMgmt_Project>>> GetAllProjects([FromQuery] PaginationParameters parameters)
        {
            var result = await this.Services_Repo.ProjectService.GetAllProjectMgmt_Projects(parameters);

            // Log Activity
            var description = $"Projects Retrieved by {CurrentUser}";
            var summary = $"Projects Retrieved";
            await LogActivity(description, summary, null);

            return new ApiResponse<Page<ProjectMgmt_Project>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Get All Project With Sprints
        /// <summary>
        /// Get All Project With Sprints
        /// </summary>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("GetProjectsWithSprints")]
        public async Task<IActionResult> GetProjectsWithSprints()
        {
            var result = await this.Services_Repo.ProjectService.GetAllProjectsWithSprints();

            // Log Activity
            var description = $"Projects With Sprints Retrieved by {CurrentUser}";
            var summary = $"Projects With Sprints Retrieved";
            await LogActivity(description, summary, null);

            return Ok(result);
        }
        #endregion

        #region Get Project By Id
        /// <summary>
        /// Gets a project by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("GetProjectById/{id}")]
        public async Task<ApiResponse<ProjectMgmt_Project>> GetProjectbyId(string id)
        {
            Guid newid = new Guid(id);
            var result = await this.Services_Repo.ProjectService.GetProjectMgmt_ProjectId(newid);

            if (result != null)
            {
                // Log Activity
                var description = $"Project Retrieved by {CurrentUser}";
                var summary = $"Project Retrieved";
                await LogActivity(description, summary, id);

                return new ApiResponse<ProjectMgmt_Project>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<ProjectMgmt_Project>
                {
                    ResponseMessage = "Project Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Get Projects By userId
        /// <summary>
        /// Get Projects By userId
        /// </summary>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("GetProjectsByUserId")]
        public async Task<ApiResponse<Page<ProjectMgmt_Project>>> GetProjectsByUserId([FromQuery] PaginationParameters parameters)
        {
            var userId = CurrentUserId.ToString();
            var result = await this.Services_Repo.ProjectService.GetProjectByUserId(userId, parameters);

            // Log Activity
            var description = $"Projects Retrieved by {CurrentUser}";
            var summary = $"Projects Retrieved";
            await LogActivity(description, summary, null);

            return new ApiResponse<Page<ProjectMgmt_Project>>
            {
                ResponseMessage = "Projects retrieved successfully",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Get actual project value
        /// <summary>
        /// Gets the actual project value
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("GetActualProjectValue/{projectId}")]
        public async Task<IActionResult> GetActualProjectValue(string projectId)
        {
            try
            {
                var result = await this.Services_Repo.ProjectService.GetActualProjectValue(projectId);
                return Ok(new ApiResponse<ActualProjectValueResponse>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Get uplaoded files for a project
        /// <summary>
        /// Gets uploaded files for a project using projectId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("GetProjectFiles/{projectId}")]
        public async Task<ApiResponse<List<ProjectFile>>> GetProjectFiles(string projectId)
        {
            var result = await this.Services_Repo.ProjectService.GetUplaodedProjectFiles(projectId);

            // Log Activity
            var description = $"Project Files Retrieved by {CurrentUser}";
            var summary = $"Project Files Retrieved";
            await LogActivity(description, summary, null);

            return new ApiResponse<List<ProjectFile>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Super search for project and todo
        /// <summary>
        /// Super search for project and todo
        /// </summary>
        /// <param name="searchParam"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("SuperSearchProjectOrTodo/{searchParam}")]
        public async Task<IActionResult> SuperSearchForProjectAndTodo(string searchParam)
        {
            var result = await this.Services_Repo.ProjectService.SearchAllProjectAndTodoAndSprint(searchParam);
            // Log Activity
            var description = $"{searchParam} searched by {CurrentUser}";
            var summary = $"Search Operation";
            await LogActivity(description, summary, null);

            return Ok(result);
        }
        #endregion

        #region Upload Project File
        /// <summary>
        /// Upload Project file
        /// </summary>
        /// <param name="asset"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPost]
        [Route("UploadProjectFile/{projectId}")]
        public async Task<ActionResult> UpLoadProjectDocument([FromForm] UploadVM asset, string projectId)
        {
            if (projectId == null) { return BadRequest(new { Msg = " Project Id Is Required !" }); }

            try
            {
                var newId = new Guid(projectId);
                var Result = await this.Services_Repo.ProjectService.GetProjectMgmt_ProjectId(newId);

                if (Result == null) { return NotFound(new { Mgs = " Project Not Found !" }); }

                Guid guid = Guid.NewGuid();
                var fileTrimmed = asset.UploadFile.FileName.Replace(" ", "");
                var fileName = guid.ToString()
                                        .Replace('-', '0')
                                        .Replace('_', '0')
                                        .ToUpper() + "-" + fileTrimmed;

                var imageUrl = await _aWSS3Sevices.UploadFileAsync(asset.UploadFile, fileName);
                var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(fileName);

                await Services_Repo.ProjectFileService.AddNewProjectFileAsync(new ProjectFile()
                {
                    FileName = fileName,
                    ProjectMgmt_ProjectId = new Guid(projectId)
                });

                if (string.IsNullOrEmpty(imageUrl))
                {
                    return Ok(new { Msg = "File Not Uploaded Successfully" });
                }

                // Log Activity
                var description = $"Project File Uploaded by {CurrentUser}";
                var summary = $"Project File Uploaded";
                await LogActivity(description, summary, projectId);

                return Ok(new { Msg = "File Added Successfully" });

            }

            catch (Exception Ex)
            {
                return BadRequest(Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE);
            }

        }
        #endregion

        #region Update Project
        /// <summary>
        /// Update Project
        /// </summary>
        /// <param name="model"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPatch]
        [Route("UpdateProject/{projectId}")]
        public async Task<IActionResult> Put(ProjectMgmt_ProjectUpdateVM model, string projectId)
        {
            if (model.EndDate <= model.StartDate)
            {
                return BadRequest(new ApiResponse<SprintProject>
                {
                    ResponseMessage = "Invalid Date Range ",
                    ResponseCode = "400",
                    Data = null
                });
            }

            if (projectId == null) { return BadRequest(new ApiResponse<SprintProject> { ResponseCode = "400", ResponseMessage = "Project Id is Required", Data = null }); }

            try
            {
                Guid newid = new Guid(projectId);
                var pro = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(newid);
                if (pro == null) { return Ok(new ApiResponse<SprintProject> { ResponseCode = "200", ResponseMessage = "Project Not Found", Data = null }); }

                else
                {

                    var result = await Services_Repo.ProjectService.UpdateProjectMgmt_Project(model, pro, CurrentUserId.ToString());
                    if (!result) { return BadRequest(new ApiResponse<SprintProject> { ResponseCode = "400", ResponseMessage = "Could Not Update. Pls Try Again", Data = null }); }
                    else
                    {
                        // Log Activity
                        var description = $"Project Updated by {CurrentUser}";
                        var summary = $"Project Updated";
                        await LogActivity(description, summary, projectId);

                        return Ok(new ApiResponse<SprintProject> { ResponseCode = "200", ResponseMessage = "Update Successful", Data = null });
                    }
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<SprintProject> { ResponseCode = "401", ResponseMessage = ex.Message, Data = null });
            }
            catch (DirtyFormException ex)
            {
                return BadRequest(new ApiResponse<SprintProject>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = null
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<SprintProject>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = null
                });
            }
        }
        #endregion

        #region Delete Project
        /// <summary>
        /// Delete Project
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpDelete]
        [Route("RemoveProject/{id}")]
        public async Task<ApiResponse<bool>> DeleteProjectById(string id)
        {
            if (id == null) { return new ApiResponse<bool> { ResponseCode = "400", ResponseMessage = "Project Id is Required", Data = false }; }

            try
            {
                Guid newid = new Guid(id);
                var pro = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(newid);
                if (pro == null) { return new ApiResponse<bool> { ResponseCode = "404", ResponseMessage = "Project Not Found", Data = false }; }

                else
                {
                    bool result = await Services_Repo.ProjectService.DeleteProjectById(id, CurrentUserId.ToString());
                    if (result == false) { return new ApiResponse<bool> { ResponseCode = "500", ResponseMessage = "Could Not Remove project Pls Try Again", Data = false }; }
                    else
                    {
                        // Log Activity
                        var description = $"Project Deleted by {CurrentUser}";
                        var summary = $"Project Deleted";
                        await LogActivity(description, summary, id);

                        return new ApiResponse<bool> { ResponseCode = "200", ResponseMessage = "Deleted Succssfully", Data = true };
                    }

                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return new ApiResponse<bool> { ResponseCode = "401", ResponseMessage = ex.Message, Data = false };
            }
        }
        #endregion

        #region Add both External and Internal Members to a project
        /// <summary>
        /// Add both External and Internal Members to a project
        /// </summary>
        /// <param name="externalMember"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPost]
        [Route("AddBothExternalAndInternalMembers/{projectId}")]
        public async Task<ActionResult> AddBothExternalAndInternalMembers([FromBody] ProjectMembersVm externalMember, string projectId)
        {

            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                externalMember.SubDomain = subdomain;

                var newId = new Guid(projectId);
                var project = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(newId);

                if (project == null)
                {
                    return BadRequest("Project does not exist");
                }

                var result = await Services_Repo.ProjectService.AddMembersToProject(externalMember, project);
                if (result == true)
                {
                    // Log Activity
                    var description = $"Project Members Added by {CurrentUser}";
                    var summary = $"Project Members Added";
                    await LogActivity(description, summary, projectId);

                    return Ok(new ApiResponse<bool>
                    {
                        Data = true,
                        ResponseCode = "200",
                        ResponseMessage = "Members Added Successfully"
                    });

                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Members Not Added Successfully"
                    });
                }
            }

            catch (Exception Ex)
            {
                return BadRequest(Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE);
            }
        }
        #endregion

        #region Get Project Members
        /// <summary>
        /// Gets project memebers by Id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("GetProjectMembers/{projectId}")]
        public async Task<ApiResponse<List<UserDto>>> GetProjectMembers(Guid projectId)
        {
            // Check if project exists
            var project = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(projectId);
            if (project == null)
            {
                return new ApiResponse<List<UserDto>>
                {
                    ResponseMessage = "Project does not exist",
                    ResponseCode = "400",
                    Data = null
                };
            }

            var result = await this.Services_Repo.ProjectService.GetProjectMembers(projectId);
            if (result != null)
            {
                return new ApiResponse<List<UserDto>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                // Log Activity
                var description = $"Project Members Retrieved by {CurrentUser}";
                var summary = $"Project Members Retrieved";
                await LogActivity(description, summary, projectId.ToString());

                return new ApiResponse<List<UserDto>>
                {
                    ResponseMessage = "Members retrieved",
                    ResponseCode = "400",
                    Data = null
                };
            }
        }
        #endregion

        #region Delete Project Files by Id
        /// <summary>
        /// Delete Project Files by Id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpDelete]
        [Route("DeleteProjectFile/{projectId}")]
        public async Task<ApiResponse<ProjectFile>> DeleteProjectfilebyId(string projectId)
        {
            Guid newid = new Guid(projectId);
            var result = await this.Services_Repo.ProjectFileService.DeleteFilesAsync(newid);
            if (result)
            {
                // Log Activity
                var description = $"Project File Deleted by {CurrentUser}";
                var summary = $"Project File Deleted";
                await LogActivity(description, summary, projectId);

                return new ApiResponse<ProjectFile>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200"
                };
            }
            else
            {
                return new ApiResponse<ProjectFile>
                {
                    ResponseMessage = "project File Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Update Project Status
        /// <summary>
        /// Update Project Status
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpPut]
        [Route("UpdateProjectStatus/{projectId}")]
        public async Task<ApiResponse<ProjectMgmt_Project>> UpdateProjectStatus(string projectId, ProjectStatus status)
        {
            try
            {
                Guid newid = new Guid(projectId);
                var result = await this.Services_Repo.ProjectService.UpdateProjectStatus(newid, status, CurrentUserId.ToString());
                if (result != null)
                {
                    // Log Activity
                    var description = $"Project Status Updated by {CurrentUser}";
                    var summary = $"Project Status Updated";
                    await LogActivity(description, summary, projectId);

                    return new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "Successful",
                        ResponseCode = "200",
                        Data = result
                    };
                }
                else
                {
                    return new ApiResponse<ProjectMgmt_Project>
                    {
                        ResponseMessage = "project Not Found",
                        ResponseCode = "404",
                        Data = null
                    };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return new ApiResponse<ProjectMgmt_Project>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "401",
                    Data = null
                };
            }
            catch (RecordNotFoundException ex)
            {
                return new ApiResponse<ProjectMgmt_Project>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Add amount per user for a project
        /// <summary>
        /// Add amount per user for a project
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_adjust_project_rate)]
        [HttpPut]
        [Route("AddAmountPerUser")]
        public async Task<ApiResponse<bool>> AddAmountPerUser(AddAmountPerUserDto model)
        {
            try
            {
                var result = await this.Services_Repo.ProjectService.AddAmountPerUser(model.ProjectId, model.UserIds, model.AmountPerHour, model.CurrencySymbol.ToString());

                // Log Activity
                var description = $"Amount Per User Added by {CurrentUser}";
                var summary = $"Amount Per User Added";
                await LogActivity(description, summary, null);

                return new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            catch (RecordNotFoundException ex)
            {
                return new ApiResponse<bool>
                {
                    ResponseMessage = "Record not found",
                    ResponseCode = "404",
                    Data = false
                };
            }
        }
        #endregion

        #region Get Project Count Reports
        /// <summary>
        /// Get Project Count Reports
        /// </summary>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("Report/GetProjectCountReport")]
        public async Task<ApiResponse<ProjectReportVm>> GetProjectCountReport()
        {
            var result = await this.Services_Repo.ProjectService.GetTotalProjectCountReport();
            if (result != null)
            {
                // Log Activity
                var description = $"Project Count Report Retrieved by {CurrentUser}";
                var summary = $"Project Count Report Retrieved";
                await LogActivity(description, summary, null);

                return new ApiResponse<ProjectReportVm>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<ProjectReportVm>
                {
                    ResponseMessage = "Project Report not found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Get all project report summary
        /// <summary>
        /// Get all project report summary
        /// </summary>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("Report/GetProjectReportSummary")]
        public async Task<ApiResponse<List<ProjectReportSummaryDto>>> GetProjectReportSummary()
        {
            var result = await this.Services_Repo.ProjectService.GetProjectReportSummary();

            // Log Activity
            var description = $"Project Report Summary Retrieved by {CurrentUser}";
            var summary = $"Project Report Summary Retrieved";
            await LogActivity(description, summary, null);

            return new ApiResponse<List<ProjectReportSummaryDto>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Get Project Report Details by project id
        /// <summary>
        /// Get Project Report Details by project id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("Report/GetProjectReportDetails/{projectId}")]
        public async Task<ApiResponse<ProjectReportDetailsDto>> GetProjectReportDetails(string projectId)
        {
            Guid newid = new Guid(projectId);
            var result = await this.Services_Repo.ProjectService.GetProjectReportDetails(newid);
            result.SprintSummary = await this.Services_Repo.SprintProjectService.GetSprintSummaryReportByProjectId(projectId);

            // Log Activity
            var description = $"Project Report Details Retrieved by {CurrentUser}";
            var summary = $"Project Report Details Retrieved";
            await LogActivity(description, summary, projectId);

            return new ApiResponse<ProjectReportDetailsDto>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Get PDF Export Project Report Details by project id
        /// <summary>
        /// This method is used to get PDF Export Project Report Details by project id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("Report/GetPDFExportProjectReportDetails/{projectId}")]
        public async Task<IActionResult> GetPDFExportProjectReportDetails(string projectId)
        {
            Guid newid = new Guid(projectId);
            var result = await this.Services_Repo.ProjectService.GetProjectPDFExportReportSummary(newid);

            var description = $"PDF Export Project Report Details Retrieved by {CurrentUser}";
            var summary = $"PDF Export Project Report Details Retrieved";
            await LogActivity(description, summary, projectId);

            return Ok(new ApiResponse<ProjectPDFExportReportSummaryDto>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Get project analytics data
        /// <summary>
        /// This gets the project analytics data
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_projects)]
        [HttpGet]
        [Route("Report/GetProjectAnalyticsData/{projectId}")]
        public async Task<IActionResult> GetProjectAnalyticsData(string projectId, DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.Information("GetProjectAnalyticsData", $"{projectId}");
                var result = await this.Services_Repo.ProjectService.GetProjectAnalyticData(projectId, startDate, endDate);

                // Log Activity
                var description = $"Project Analytics Data Retrieved by {CurrentUser}";
                var summary = $"Project Analytics Data Retrieved";
                await LogActivity(description, summary, projectId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.Error("ProjectController", ex.ToString());
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get project counts
        /// <summary>
        /// This gets project counts
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetProjectCount")]
        public async Task<IActionResult> GetProjectCount()
        {
            try
            {
                _logger.Information("GetProjectCount");
                var projectCounts = await Services_Repo.ProjectService.GetProjectCount();
                return Ok(new GenericResponse()
                {
                    ResponseMessage = "Project count fetched successfully",
                    ResponseCode = "200",
                    Data = projectCounts
                });
            }
            catch (Exception ex)
            {
                _logger.Error("ProjectController", ex.ToString());
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get top performing tenants
        /// <summary>
        /// This gets top performig tenants
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetTopPerformingCompaniesPercentage")]
        public async Task<IActionResult> GetTopPerformingCompaniesPercentage()
        {
            try
            {
                _logger.Information("GetTopPerformingCompaniesPercentage");
                var projectCompanies = await Services_Repo.ProjectService.GetTopPerformingCompaniesPercentage();
                return Ok(new GenericResponse()
                {
                    ResponseMessage = "Top performing company fetched successfully",
                    ResponseCode = "200",
                    Data = projectCompanies
                });
            }
            catch (Exception ex)
            {
                _logger.Error("ProjectController", ex.ToString());
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get projects tenant details
        /// <summary>
        /// This gets projects tenant details
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetProjectCompanyDetail")]
        public async Task<IActionResult> GetProjectCompanyDetail([FromQuery] CompanyProjectFilter filter, [FromQuery][Required] int pageSize, [FromQuery][Required] int pageNumber)
        {
            try
            {
                _logger.Information("GetProjectCompanyDetail", $"{filter}");
                var projectCompanies = await Services_Repo.ProjectService.GetProjectCompanyDetail(filter, pageSize, pageNumber);
                return Ok(new GenericResponse()
                {
                    ResponseMessage = "Company details company fetched successfully",
                    ResponseCode = "200",
                    Data = projectCompanies
                });
            }
            catch (Exception ex)
            {
                _logger.Error("ProjectController", ex.ToString());
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get projects metrics
        /// <summary>
        /// This gets projects metrics
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetProjectMetrics")]
        public async Task<IActionResult> GetProjectMetrics([FromQuery][Required] ProjectMetricsQueryParameters parameters)
        {
            try
            {
                _logger.Information("GetProjectMetrics", $"{parameters}");
                var projectCompanies = await Services_Repo.ProjectService.GetProjectMetrics(parameters);
                return Ok(new GenericResponse()
                {
                    ResponseMessage = "Projects metrics fetched successfully",
                    ResponseCode = "200",
                    Data = projectCompanies
                });
            }
            catch (Exception ex)
            {
                _logger.Error("ProjectController", ex.ToString());
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get projects metrics
        /// <summary>
        /// This gets projects metrics
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetProjectMetricsSummary")]
        public async Task<IActionResult> GetProjectMetricsSummary([FromQuery][Required] ProjectMetricsQueryParameters parameters)
        {
            try
            {
                _logger.Information("GetProjectMetrics", $"{parameters}");
                var projectCompanies = await Services_Repo.ProjectService.GetProjectMetricsSummary(parameters);
                return Ok(new GenericResponse()
                {
                    ResponseMessage = "Projects metrics fetched successfully",
                    ResponseCode = "200",
                    Data = projectCompanies
                });
            }
            catch (Exception ex)
            {
                _logger.Error("ProjectController", ex.ToString());
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Private Method
        private async Task LogActivity(string description, string summary, string eventId = null)
        {
            var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), EventCategory.Project);
            if (canLogActivity)
            {
                var activityModel = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Project,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                    EventId = eventId,
                    Application = Applications.Joble
                };
                await Services_Repo.ActivityService.CreateLog(activityModel);
            }
        }
        #endregion
    }
}
