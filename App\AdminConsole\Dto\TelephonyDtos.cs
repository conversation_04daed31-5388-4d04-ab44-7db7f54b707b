using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.AdminConsole.Dto
{
    /// <summary>
    /// Request to initiate a new call session
    /// </summary>
    public class InitiateCallSessionDto
    {
        [Required]
        public string UserId { get; set; }

        [Required]
        public Guid FromNumberId { get; set; }

        [Required]
        public string ToNumber { get; set; }

        public string UserDisplayName { get; set; }

        public int MaxParticipants { get; set; } = 10;

        public bool? EnableRecording { get; set; } = true;

        public bool? EnableAI { get; set; } = true;

        [JsonIgnore]
        public string Subdomain { get; set; }
    }

    /// <summary>
    /// Call session information returned after initiation
    /// </summary>
    public class CallSessionDto
    {
        public string SessionId { get; set; }

        public string LiveKitToken { get; set; }

        public string LiveKitUrl { get; set; }

        public string TwilioCallSid { get; set; }

        public string FromNumber { get; set; }

        public string ToNumber { get; set; }

        public string Status { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public CallParticipantDto[] Participants { get; set; }
    }

    /// <summary>
    /// Call participant information
    /// </summary>
    public class CallParticipantDto
    {
        public string UserId { get; set; }

        public string DisplayName { get; set; }

        public string PhoneNumber { get; set; }

        public string Role { get; set; } // "initiator", "participant", "agent", "ai"

        public DateTime JoinedAt { get; set; }

        public DateTime? LeftAt { get; set; }

        public bool IsConnected { get; set; } = true;

        public bool IsMuted { get; set; } = false;
    }

    /// <summary>
    /// Request to answer an incoming call
    /// </summary>
    public class AnswerCallDto
    {
        [Required]
        public string UserId { get; set; }

        [Required]
        public string CallSid { get; set; }

        [Required]
        public string LiveKitRoomName { get; set; }

        public string UserDisplayName { get; set; }
    }

    /// <summary>
    /// Request to end a call session
    /// </summary>
    public class EndCallDto
    {
        [Required]
        public string SessionId { get; set; }

        [Required]
        public string UserId { get; set; }

        public string CallSid { get; set; }

        public string LiveKitRoomName { get; set; }

        public string Reason { get; set; } // "user_ended", "timeout", "error"
    }    /// <summary>
    /// Request to add a participant to an ongoing call
    /// </summary>
    public class TelephonyAddParticipantDto
    {
        [Required]
        public string SessionId { get; set; }

        [Required]
        public string ParticipantId { get; set; }

        public string ParticipantName { get; set; }

        public string PhoneNumber { get; set; }

        public bool IsPhoneNumber { get; set; }

        public string FromNumber { get; set; }

        public string AddedBy { get; set; }

        public string Role { get; set; } = "participant";
    }

    /// <summary>
    /// Request to remove a participant from an ongoing call
    /// </summary>
    public class TelephonyRemoveParticipantDto
    {
        [Required]
        public string SessionId { get; set; }

        [Required]
        public string ParticipantId { get; set; }

        public string RemovedBy { get; set; }

        public string Reason { get; set; }
    }

    /// <summary>
    /// Request to forward a call to another number/user
    /// </summary>
    public class ForwardCallDto
    {
        [Required]
        public string SessionId { get; set; }

        [Required]
        public string CallSid { get; set; }

        [Required]
        public string ForwardedBy { get; set; }

        public string ToNumber { get; set; }

        public string ToUserId { get; set; }

        public string ToDisplayName { get; set; }

        public string FromNumber { get; set; }

        public bool IsTransfer { get; set; } = false; // true = transfer, false = conference

        public string Reason { get; set; }
    }    
    
    /// <summary>
    /// Call status update from Twilio webhook
    /// </summary>
    public class TwilioCallStatusDto
    {
        public string CallSid { get; set; }

        public string CallStatus { get; set; }

        public string Direction { get; set; }

        public string From { get; set; }

        public string To { get; set; }

        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// AI service integration request
    /// </summary>
    public class AICallInterceptionDto
    {
        public string SessionId { get; set; }

        public string CallSid { get; set; }

        public string AudioData { get; set; } // Base64 encoded

        public string AudioFormat { get; set; } // "opus", "pcm", "mulaw"

        public int SampleRate { get; set; } = 48000;

        public int Channels { get; set; } = 1;

        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        public string Direction { get; set; } // "inbound", "outbound"

        public string FromParticipant { get; set; }

        public string ToParticipant { get; set; }
    }

    /// <summary>
    /// AI service response for call processing
    /// </summary>
    public class AICallResponseDto
    {
        public string SessionId { get; set; }

        public string Action { get; set; } // "continue", "interrupt", "redirect", "record", "transcribe"

        public string TranscriptionText { get; set; }

        public string RedirectTo { get; set; }

        public string ResponseAudio { get; set; } // Base64 encoded AI response

        public double Confidence { get; set; }

        public string Intent { get; set; }

        public object Metadata { get; set; }
    }
}
