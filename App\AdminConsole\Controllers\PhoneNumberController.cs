using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Dto.AI;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils._Helper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Twilio.TwiML;
using Twilio.TwiML.Voice;

namespace Jobid.App.AdminConsole.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class PhoneNumberController : ControllerBase
    {
        private readonly IUnitofwork _unitofwork;

        public PhoneNumberController(IUnitofwork unitofwork)
        {
            _unitofwork = unitofwork;
        }

        /// <summary>
        /// Get Available Phone Numbers
        /// </summary>
        /// <param name="countryCode"></param>
        /// <param name="areaCode"></param>
        /// <returns></returns>
        [HttpGet("available")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetAvailablePhoneNumbers([FromQuery] string countryCode, [FromQuery] string areaCode = null)
        {
            var response = await _unitofwork.PhoneNumberService.GetAvailablePhoneNumbers(countryCode, areaCode);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// Purchase a phone number from Twilio
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("purchase")]
        public async Task<IActionResult> PurchasePhoneNumber([FromBody] PurchasePhoneNumberDto model)
        {
            // Get subdomain from the headers
            model.Subdomain = Request.Headers["Subdomain"].ToString() ?? Request.Headers["subdomain"].ToString();
            model.Subdomain = model.Subdomain.ToLower();
            var response = await _unitofwork.PhoneNumberService.PurchasePhoneNumber(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// This registers a number that you have already purchased from twilio
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("register")]
        public async Task<IActionResult> RegisterExistingNumber([FromBody] RegisterPhoneNumberDto model)
        {
            var response = await _unitofwork.PhoneNumberService.RegisterExistingNumber(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// Assign Users To Phone Number
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("assign-users")]
        public async Task<IActionResult> AssignUsers([FromBody] AssignPhoneNumberUsersDto model)
        {
            model.CreatedBy = User.GetUserId();
            var response = await _unitofwork.PhoneNumberService.AssignUsers(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// Get Phone Number by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPhoneNumber(Guid id)
        {
            var response = await _unitofwork.PhoneNumberService.GetPhoneNumber(id);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// Get Phone Numbers by Tenant Id
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        [HttpGet("tenant/{tenantId}")]
        public async Task<IActionResult> GetPhoneNumbers(string tenantId)
        {
            var response = await _unitofwork.PhoneNumberService.GetPhoneNumbers(tenantId);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// Purge Phone Number Data
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("purge")]
        public async Task<IActionResult> PurgePhoneNumberData([FromBody] PurgePhoneNumberDataDto model)
        {
            var response = await _unitofwork.PhoneNumberService.PurgePhoneNumberData(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        #region Get company details a phone numnber belongs to
        /// <summary>
        /// Get company details by phone number
        /// </summary>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpGet("company/{phoneNumber}")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetCompanyByPhoneNumber(string phoneNumber)
        {
            var response = await _unitofwork.PhoneNumberService.GetCompanyByPhoneNumber(phoneNumber);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Get phone numbers assigned to a user
        /// <summary>
        /// Get phone numbers assigned to a user
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("user/phonenumbers")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetUserPhoneNumbers(string userId)
        {
            if (string.IsNullOrEmpty(userId))
                userId = User.GetUserId();

            var response = await _unitofwork.PhoneNumberService.GetAssignedPhoneNumbersForUser(userId);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Add Call Transaction
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPost("add-call-transaction")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetCallTransaction([FromBody] CallTransactionDto model)
        {
            var response = await _unitofwork.PhoneNumberService.AddCallTransaction(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Add Or Update Company Summarized Info
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPost("company/add-summarized-info")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> AddOrUpdateCompanySummarizedInfo([FromBody] CompanySummarizedInfoDto model)
        {
            var response = await _unitofwork.PhoneNumberService.AddOrUpdateCompanySummarizedInfo(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Get Company Summarized Info
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpGet("company/summarized-info/{companyId}")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetCompanySummarizedInfo(Guid companyId)
        {
            var response = await _unitofwork.PhoneNumberService.GetCompanySummarizedInfo(companyId);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Add Or Update Company Files Summary
        /// <summary>
        /// Add or Update Company Files Summary
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPost("company/add-files-summary")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> AddOrUpdateCompanyFilesSummary([FromBody] CompanyFileSummaryDto model)
        {
            var response = await _unitofwork.PhoneNumberService.AddOrUpdateCompanyFilesSummary(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region Get Company Files Summary
        /// <summary>
        /// Get Company Files Summary
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpGet("company/files-summary/{companyId}")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> GetCompanyFilesSummary(Guid companyId)
        {
            var response = await _unitofwork.PhoneNumberService.GetCompanyFilesSummary(companyId);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }
        #endregion

        #region TwiML Endpoints for Call Flow

        /// <summary>
        /// Webhook endpoint for handling inbound calls from Twilio
        /// This is called when someone dials a Twilio phone number
        /// </summary>
        [HttpPost("webhook/handle-inbound-call")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleInboundCall([FromForm] string From, [FromForm] string To, [FromForm] string CallSid, [FromQuery] string subdomain)
        {
            try
            {
                // Process the inbound call through the service
                var response = await _unitofwork.PhoneNumberService.HandleInboundCall(From, To, subdomain, CallSid);
                
                // Generate TwiML response for call routing
                var twimlResponse = await _unitofwork.PhoneNumberService.GenerateInboundCallTwiML(CallSid, From, To, subdomain);
                
                return Content(twimlResponse, "text/xml");
            }
            catch (Exception)
            {
                // Fallback TwiML for errors
                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
                <Response>
                    <Say voice=""alice"">We're sorry, but we're unable to take your call right now. Please try again later.</Say>
                    <Hangup/>
                </Response>";
                return Content(errorTwiml, "text/xml");
            }
        }

        /// <summary>
        /// Fallback webhook for inbound calls when primary routing fails
        /// </summary>
        [HttpPost("webhook/handle-inbound-call-fallback")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleInboundCallFallback([FromForm] string CallSid, [FromForm] string From, [FromForm] string To, [FromQuery] string subdomain)
        {
            try
            {
                var twilResponse = await _unitofwork.PhoneNumberService.GenerateInboundCallTallBackTwiML(CallSid, From, To, subdomain);
                return Content(twilResponse, "text/xml");
            }
            catch (Exception)
            {
                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
                <Response>
                    <Say voice=""alice"">We're experiencing technical difficulties. Please try again later.</Say>
                    <Hangup/>
                </Response>";
                return Content(errorTwiml, "text/xml");
            }
        }

        /// <summary>
        /// Webhook for call status updates from Twilio
        /// </summary>
        [HttpPost("webhook/phone-status")]
        [AllowAnonymous]
        public async Task<IActionResult> PhoneStatusCallback([FromQuery] string subdomain, [FromForm] string CallSid, [FromForm] string CallStatus, [FromForm] string CallDuration = null)
        {
            try
            {
                await _unitofwork.PhoneNumberService.UpdateCallStatus(CallSid, CallStatus, subdomain);
                return Ok();
            }
            catch (Exception)
            {
                // Don't fail the webhook, just log and return success
                return Ok();
            }
        }

        /// <summary>
        /// Endpoint for agents to accept inbound calls
        /// </summary>
        [HttpPost("accept-call")]
        public async Task<IActionResult> AcceptCall([FromBody] AcceptInboundCallDto model)
        {
            var response = await _unitofwork.PhoneNumberService.AcceptInboundCall(model);
            return StatusCode(Convert.ToInt32(response.ResponseCode), response);
        }

        /// <summary>
        /// Provides hold music for callers in queue
        /// </summary>
        [HttpGet("webhook/queue-wait-music")]
        [AllowAnonymous]
        public IActionResult QueueWaitMusic()
        {
            var holdMusicTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
            <Response>
                <Play loop=""0"">https://demo.twilio.com/docs/classic.mp3</Play>
            </Response>";
            
            return Content(holdMusicTwiml, "text/xml");
        }

        /// <summary>
        /// Handles voicemail recording completion
        /// </summary>
        [HttpPost("webhook/voicemail")]
        [AllowAnonymous]
        public IActionResult Voicemail([FromQuery] string callSid, [FromForm] string RecordingUrl = null, [FromForm] string RecordingSid = null)
        {
            try
            {
                // Here you would typically save the voicemail recording details
                // For now, just acknowledge receipt
                
                var thankYouTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
                <Response>
                    <Say voice=""alice"">Thank you for your message. We will get back to you soon. Goodbye.</Say>
                    <Hangup/>
                </Response>";

                return Content(thankYouTwiml, "text/xml");
            }
            catch (Exception)
            {
                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
                <Response>
                    <Say voice=""alice"">Thank you. Goodbye.</Say>
                    <Hangup/>
                </Response>";
                return Content(errorTwiml, "text/xml");
            }
        }

        /// <summary>
        /// Handle dial result - THIS IS THE MISSING BRIDGE!
        /// This endpoint returns TwiML with Stream verb to establish WebSocket connection
        /// </summary>
        [HttpPost("webhook/handle-dial-result")]
        [AllowAnonymous]
        public IActionResult HandleDialResult([FromQuery] Guid callId, [FromQuery] string roomName, [FromForm] string CallSid, [FromForm] string DialCallStatus)
        {
            try
            {
                var actualRoomName = !string.IsNullOrEmpty(roomName) ? roomName : $"call-{callId}";
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                var redirectUrl = $"{baseUrl}/api/phonenumber/webhook/connect-stream?roomName={actualRoomName}&callSid={CallSid}";

                var response = new VoiceResponse();
                response.Say("Connecting your call. Please hold.");
                response.Pause(length: 1);
                response.Redirect(new Uri(redirectUrl));

                return Content(response.ToString(), "text/xml");
            }
            catch (Exception)
            {
                var errorResponse = new VoiceResponse();
                errorResponse.Say("We're experiencing technical difficulties. Please try again later.");
                errorResponse.Hangup();

                return Content(errorResponse.ToString(), "text/xml");
            }
        }

        [HttpPost("webhook/connect-stream")]
        [AllowAnonymous]
        public IActionResult ConnectStream([FromQuery] string roomName, [FromQuery] string callSid)
        {
            var baseUrl = $"{Request.Scheme}://{Request.Host}";
            var streamUrl = $"{baseUrl.Replace("http://", "wss://").Replace("https://", "wss://")}/api/livekit-pbx/media-bridge?room={roomName}&callSid={callSid}";

            var response = new VoiceResponse();
            var connect = new Connect();
            connect.Stream(streamUrl);
            response.Append(connect);

            return Content(response.ToString(), "text/xml");
        }

        #endregion
    }
}