﻿using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    public class ProjectTriggerController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        public ProjectTriggerController(IUnitofwork unitofwork)
        {
            this.Services_Repo = unitofwork;
        }

        #region Add Trigger
        /// <summary>
        /// Add Trigger
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("addtrigger")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        [ProducesResponseType(typeof(string), 500)]
        public async Task<ActionResult> Post([FromBody] TriggerVm model)
        {
            try
            {
                model.UserId = CurrentUserId.ToString();
                var project = new Helpers.Models.ProjectMgmt_Project();
                if (model?.ProjectId is not null)
                {
                    project = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(Guid.Parse(model.ProjectId));
                    if (project == null)
                    {
                        return BadRequest(new ApiResponse<ProjectTrigger>
                        {
                            ResponseMessage = "Invalid Project Id",
                            ResponseCode = "400",
                            Data = null
                        });
                    }

                }
                else project = null;
                

                if (model.TriggerDateAndTime.Any(x => x < DateTime.UtcNow))
                {
                    return BadRequest(new ApiResponse<ProjectTrigger>
                    {
                        ResponseMessage = "Invalid Date Range ",
                        ResponseCode = "400",
                        Data = null
                    });
                }

                var result = await Services_Repo.TriggerService.AddTrigger(model, project);
                if (result != null)
                {
                    // Log Activity
                    var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), EventCategory.ProjectTrigger);
                    if (canLogActivity)
                    {
                        var activity = new ActivityDto
                        {
                            Description = $"Project Trigger created by {CurrentUser}",
                            ActivitySummary = $"Trigger created",
                            EventCategory = EventCategory.ProjectTrigger,
                            UserId = CurrentUserId?.ToString(),
                            By = CurrentUser,
                            EventId = result.Id.ToString(),
                            Application = Applications.Joble
                        };
                        await Services_Repo.ActivityService.CreateLog(activity);
                    }

                    return Ok(new ApiResponse<ProjectTrigger>
                    {
                        ResponseMessage = "Trigger created successfully and will be displayed on your calendar",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else { return BadRequest(); }
            }
            catch (Exception Ex)
            {
                return BadRequest(Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE);
            }
        }
        #endregion

        #region Get Triggers By Project Id
        /// <summary>
        /// Get Triggers By Project Id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTriggersByProjectId/{projectId}")]
        public async Task<ApiResponse<List<ProjectTriggerDto>>> GetTriggeerByProjectId(string projectId)
        {
            try
            {
                var result = await this.Services_Repo.TriggerService.GetTriggersById(projectId);
                if (result.Count > 0)
                {
                    // Log Activity
                    var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), JobProject.Enums.Enums.EventCategory.ProjectTrigger);
                    if (canLogActivity)
                    {
                        var activity = new ActivityDto
                        {
                            Description = $"Project Triggers viewed by {CurrentUser}",
                            ActivitySummary = $"Triggers viewed",
                            EventCategory = EventCategory.ProjectTrigger,
                            UserId = CurrentUserId?.ToString(),
                            By = CurrentUser,
                            EventId = projectId,
                            Application = Applications.Joble
                        };
                        await Services_Repo.ActivityService.CreateLog(activity);
                    }
                    return new ApiResponse<List<ProjectTriggerDto>>
                    {
                        ResponseMessage = "Successful",
                        ResponseCode = "200",
                        Data = result
                    };
                }
                else
                {
                    return new ApiResponse<List<ProjectTriggerDto>>
                    {
                        ResponseMessage = "Trigger Not Found",
                        ResponseCode = "200",
                        Data = null
                    };
                }
            }
            catch (Exception ex)
            {
                return new ApiResponse<List<ProjectTriggerDto>>
                {
                    ResponseMessage = "An error occured, please contact support",
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Get Trigger By Id
        /// <summary>
        /// Get Trigger By Id
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTriggerById/{Id}")]
        public async Task<ApiResponse<ProjectTriggerDto>> GetTriggerById(string Id)
        {
            try
            {
                var newId = new Guid(Id);
                var result = await this.Services_Repo.TriggerService.GetTriggerById(newId);
                if (result != null)
                {
                    // Log Activity
                    var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), JobProject.Enums.Enums.EventCategory.ProjectTrigger);
                    if (canLogActivity)
                    {
                        var activity = new ActivityDto
                        {
                            Description = $"Project Trigger viewed by {CurrentUser}",
                            ActivitySummary = $"Trigger viewed",
                            EventCategory = EventCategory.ProjectTrigger,
                            UserId = CurrentUserId?.ToString(),
                            By = CurrentUser,
                            EventId = Id,
                            Application = Applications.Joble
                        };
                        await Services_Repo.ActivityService.CreateLog(activity);
                    }

                    return new ApiResponse<ProjectTriggerDto>
                    {
                        ResponseMessage = "Successful",
                        ResponseCode = "200",
                        Data = result
                    };
                }
                else
                {
                    return new ApiResponse<ProjectTriggerDto>
                    {
                        ResponseMessage = "Trigger Not Found",
                        ResponseCode = "200",
                        Data = null
                    };
                }
            }
            catch (Exception ex)
            {
                return new ApiResponse<ProjectTriggerDto>
                {
                    ResponseMessage = "Could not retrive records, try again later or contact support",
                    ResponseCode = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = null
                };
            }
        }
        #endregion

        #region Get Triggers By user Id
        /// <summary>
        /// Get Triggers By user Id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTriggersByUserId/{userId}")]
        public async Task<ApiResponse<List<ProjectTriggerDto>>> GetTriggersByUserId(string userId)
        {
            var result = await this.Services_Repo.TriggerService.GetTriggersByUserId(userId);

            // Log Activity
            var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), EventCategory.ProjectTrigger);
            if (canLogActivity)
            {
                var activity = new ActivityDto
                {
                    Description = $"Project Triggers viewed by {CurrentUser}",
                    ActivitySummary = $"Triggers viewed",
                    EventCategory = EventCategory.ProjectTrigger,
                    UserId = CurrentUserId?.ToString(),
                    By = CurrentUser,
                    EventId = userId,
                    Application = Applications.Joble
                };
                await Services_Repo.ActivityService.CreateLog(activity);
            }

            return new ApiResponse<List<ProjectTriggerDto>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Get Upcoming Triggers By Id
        /// <summary>
        /// Get Upcoming triggers by projetId
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [HttpGet("upcoming/{projectId}")]
        public async Task<ApiResponse<List<ProjectTrigger>>> GetUpcomingTriggeerByProjectId([FromQuery] string projectId)
        {
            var newId = new Guid(projectId);
            var result = await this.Services_Repo.TriggerService.GetUpcomingTriggerById(newId);
            if (result.Count > 0)
            {
                return new ApiResponse<List<ProjectTrigger>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                };
            }
            else
            {
                return new ApiResponse<List<ProjectTrigger>>
                {
                    ResponseMessage = "Trigger Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Delete Trigger
        /// <summary>
        /// Delete Trigger
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete("delete/{id}")]
        public async Task<IActionResult> DeleteTrigger(string id)
        {
            try
            {
                var newId = new Guid(id);
                var result = await this.Services_Repo.TriggerService.DeleteTrigger(newId);
                if (result)
                {
                    // Log Activity
                    var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), JobProject.Enums.Enums.EventCategory.ProjectTrigger);
                    if (canLogActivity)
                    {
                        var activity = new ActivityDto
                        {
                            Description = $"Project Trigger deleted by {CurrentUser}",
                            ActivitySummary = $"Trigger deleted",
                            EventCategory = EventCategory.ProjectTrigger,
                            UserId = CurrentUserId?.ToString(),
                            By = CurrentUser,
                            EventId = id,
                            Application = Applications.Joble
                        };
                        await Services_Repo.ActivityService.CreateLog(activity);
                    }

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Successful",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Trigger not deleted successfully",
                        ResponseCode = "400",
                        Data = result
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Could not delete record, try again later or contact support",
                    ResponseCode = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
        }
        #endregion
    }
}
