﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using static Jobid.App.Helpers.Utils.Utility;

namespace Jobid.App.Helpers.Middlewares
{
    public class TokenValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly string _conString;

        public TokenValidationMiddleware(RequestDelegate next)
        {
            _next = next;
            _conString = GlobalVariables.ConnectionString;
        }

        public async Task Invoke(HttpContext context)
        {
            var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();

            if (!string.IsNullOrEmpty(token))
            {
                // Check if the token is blacklisted
                await using var publicDbContext = new JobProDbContext(_conString, new DbContextSchema(Constants.PUBLIC_SCHEMA));
                var isBlacklisted = await publicDbContext.BlacklistedTokens.AnyAsync(t => t.Token == token);

                if (isBlacklisted)
                {
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    var response = new GenericResponse
                    {
                        ResponseCode = StatusCodes.Status401Unauthorized.ToString(),
                        ResponseMessage = "You were logged out. kindly re-log in"
                    };

                    var options = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
                    var json = JsonSerializer.Serialize(response, options);
                    await context.Response.WriteAsync(json);

                    return;
                }
                else
                {
                    if (GlobalVariables.Subdomain != "api")
                    {
                        await using var dbContext = new JobProDbContext(_conString, new DbContextSchema(GlobalVariables.Subdomain));
                        var claims = context.User.Claims.ToList();
                        var userId = claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
                        if (!string.IsNullOrEmpty(userId))
                        {
                            var userProfile = await dbContext.UserProfiles
                                .Where(u => u.UserId == userId)
                                .FirstOrDefaultAsync();

                            if (userProfile.UserLoggedOut)
                            {
                                // Blacklist the token and return 401
                                var blacklistedToken = new BlacklistedToken
                                {
                                    Token = token
                                };
                                await publicDbContext.BlacklistedTokens.AddAsync(blacklistedToken);

                                userProfile.UserLoggedOut = false;
                                dbContext.UserProfiles.Update(userProfile);

                                await dbContext.SaveChangesAsync();
                                await publicDbContext.SaveChangesAsync();

                                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                                var response = new GenericResponse
                                {
                                    ResponseCode = StatusCodes.Status401Unauthorized.ToString(),
                                    ResponseMessage = "You were logged out by an admin. kindly re-log in"
                                };

                                var options = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
                                var json = JsonSerializer.Serialize(response, options);
                                await context.Response.WriteAsync(json);

                                return;
                            }
                        }
                    }
                }
            }

            await _next(context);
        }
    }

}
