﻿using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Notification.Models
{
    public class UserNotification
    {
        [ForeignKey("UserProfile")]
        public string UserProfileId { get; set; }
        public Guid NotificationId { get; set; }
        public bool IsRead { get; set; }
        public bool Disappear { get; set; }
        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
        public DateTime ReadOn { get; set; }

        // Navigation properties
        public UserProfile UserProfile { get; set; }
        public Notification Notification { get; set; }
    }
}
