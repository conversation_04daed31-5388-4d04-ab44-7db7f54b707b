﻿using Jobid.App.Calender.Models;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Tenant.ViewModel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly IUnitofwork _unitofwork;
        private readonly IApiCallService _apiCallService;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IRedisCacheService _redisCacheService;

        public TestController(IUnitofwork unitofwork, IApiCallService apiCallService, IAWSS3Sevices aWSS3Sevices, IRedisCacheService redisCacheService)
        {
            _unitofwork = unitofwork;
            _apiCallService = apiCallService;
            _aWSS3Sevices = aWSS3Sevices;
            _redisCacheService = redisCacheService;
        }

        #region Test Reschedule Meeting
        [HttpPost("TestRescheduleMeeting")]
        public async Task<IActionResult> TestRescheduleMeeting()
        {
            try
            {
                // "2024-07-18T10:00:00" to DateTime
                var date = DateTime.Parse("2024-10-01T10:00:00");
                await _unitofwork.CalenderService.TestRescheduleMeetingBK();
                return Ok("Success");
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Delete Activities
        [HttpDelete("DeleteActivities")]
        public async Task<IActionResult> DeleteActivities()
        {
            await this._unitofwork.ActivityBackgroundService.DeleteUserActivities(new List<string> { "localhost" });
            return Ok("Success");
        }
        #endregion

        #region Get Country Region
        [HttpGet("GetCountryRegion")]
        public async Task<IActionResult> GetCountryRegion(string countryName)
        {
            //var region = await _apiCallService.GetCountryRegionAsync(countryName);
            return Ok("Successful");
        }
        #endregion

        #region Test Upload File To S3
        [HttpPost("UploadFileToS3")]
        public async Task<IActionResult> TestUploadFileToS3(IFormFile file)
        {
            var fileName = file.FileName;
            var result = await _aWSS3Sevices.UploadFileAsync(file, fileName);
            return Ok(result);
        }
        #endregion

        #region Test Get File From S3
        [HttpGet("GetFileFromS3")]
        public async Task<IActionResult> TestGetFileFromS3(string fileName)
        {
            var result = await _aWSS3Sevices.GetSignedUrlAsync(fileName);
            return Ok(result);
        }
        #endregion

        #region Test Save to Redis
        [HttpPost("SaveToRedis")]
        public async Task<IActionResult> SaveToRedis(string key, string value)
        {
            var result = await _redisCacheService.SetDataAsync(key, value, DateTimeOffset.UtcNow.AddMinutes(5));
            return Ok(result);
        }
        #endregion

        #region Migrate to Postgres
        [HttpPost("MigrateToPostgres")]
        public async Task<IActionResult> MigrateToPostgres(MigrateFromSqlToPostgresDto model)
        {
            await _unitofwork.TenantService.MigrateFromSQLServerToPostgres(model);
            return Ok("Success");
        }
        #endregion

        #region Get AI Name And Image
        [HttpGet("GetAINameAndImage")]
        public async Task<IActionResult> GetAINameAndImage()
        {
            var token = this.HttpContext.Request.Headers["Authorization"].ToString();
            var result = await _unitofwork.TeamSheetService.GetAiDetails(token);
            return Ok(result);
        }
        #endregion

        #region Test Upload Large File To S3
        [HttpPost("UploadLargeFileToS3")]
        public async Task<IActionResult> TestUploadLargeFileToS3(IFormFile file)
        {
            if (file == null)
            {
                return BadRequest("No file uploaded");
            }

            var fileName = file.FileName;
            var result = await _aWSS3Sevices.UploadLargeFileAsync(file, fileName);
            return Ok(new {
                FileUrl = result,
                FileSize = file.Length,
                FileSizeInMB = Math.Round((double)file.Length / (1024 * 1024), 2),
                Message = "File uploaded successfully using multipart upload"
            });
        }
        #endregion

        #region Test SignalR IncomingCall/IncomingWebRTCCall via CallHub
        /// <summary>
        /// Test endpoint to trigger SignalR IncomingCall and IncomingWebRTCCall events via CallHub.
        /// </summary>
        [HttpPost("TestSignalRCallHubEvent")]
        public async Task<IActionResult> TestSignalRCallHubEvent([FromBody] SignalRTestEventDto dto)
        {
            // Use the PhoneNumberService to send events, as it has access to the hub contexts
            // If you want to test both events, call both
            await _unitofwork.PhoneNumberService.SendTestIncomingCallEvents(dto.UserId, dto.Payload);
            return Ok("SignalR CallHub events triggered");
        }
        #endregion

        #region Test SignalR IncomingCall/IncomingWebRTCCall via NotificationHub
        /// <summary>
        /// Test endpoint to trigger SignalR IncomingCall and IncomingWebRTCCall events via NotificationHub.
        /// </summary>
        [HttpPost("TestSignalRNotificationHubEvent")]
        public async Task<IActionResult> TestSignalRNotificationHubEvent([FromBody] SignalRTestEventDto dto)
        {
            // Use the PhoneNumberService to send events, as it has access to the hub contexts
            await _unitofwork.PhoneNumberService.SendTestIncomingCallEventsViaNotificationHub(dto.UserId, dto.Payload);
            return Ok("SignalR NotificationHub events triggered");
        }
        #endregion
    }
}

// DTO for SignalR test endpoints
public class SignalRTestEventDto
{
    public string UserId { get; set; }
    public object Payload { get; set; }
}
