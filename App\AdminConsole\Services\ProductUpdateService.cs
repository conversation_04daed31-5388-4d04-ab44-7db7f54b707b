﻿using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Services
{
    public class ProductUpdateService : IProductUpdateService
    {
        private readonly JobProDbContext _publicSchemaContext;
        private readonly ILogger _logger = Log.ForContext<ProductUpdateService>();

        public ProductUpdateService()
        {
            _publicSchemaContext = new JobProDbContext(new DbContextSchema());
        }

        public async Task<Page<SentProductUpdateDto>> GetSentProductUpdates(string subdomain, int page, int pageSize)
        {
            var schemaContext = new JobProDbContext(new DbContextSchema(subdomain));
            var productUpdates = schemaContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Sent)
                                .Skip((page - 1) * pageSize)
                                .Take(pageSize)
                                .Select(x => new SentProductUpdateDto
                                {
                                    Id = x.Id,
                                    Package = x.Package,
                                    CategoryName = x.Category,
                                    Body = x.Body,
                                    Subject = x.Subject,
                                    ImageAttachmentUrl = x.ImageUrl,
                                    DateSent = x.CreatedAt
                                });

            return await Task.FromResult(productUpdates.ToPageList(page, pageSize));
        }

        public async Task<Page<DeletedProductUpdateDto>> GetDeletedProductUpdates(string subdomain, int page, int pageSize)
        {
            var schemaContext = new JobProDbContext(new DbContextSchema(subdomain));
            var productUpdates = schemaContext.ProductUpdates.Where(x => x.PublishStatus == PublishStatus.Deleted)
                                .Skip((page - 1) * pageSize)
                                .Take(pageSize)
                                .Select(x => new DeletedProductUpdateDto
                                {
                                    Id = x.Id,
                                    Package = x.Package,
                                    CategoryName = x.Category,
                                    Body = x.Body,
                                    Subject = x.Subject,
                                    ImageAttachmentUrl = x.ImageUrl,
                                    DateDeleted = x.UpdatedAt
                                });

            return await Task.FromResult(productUpdates.ToPageList(page, pageSize));
        }


        public async Task ProcessProductUpdateMessage(ProductUpdateMessage message)
        {
            List<string> subDomains = GetSubDomains(message.Package);

            switch (message.PublishStatus)
            {
                case PublishStatus.Sent:
                    await AddProductUpdate(subDomains, message);
                    break;

                case PublishStatus.Deleted:
                    await UpdateProductUpdate(subDomains, message);
                    break;

                default:
                    throw new ArgumentOutOfRangeException($"Invalid publish status - {message.PublishStatus}");

            }
        }

        private async Task AddProductUpdate(List<string> subDomains, ProductUpdateMessage message)
        {
            _publicSchemaContext.ProductUpdates.Add(new ProductUpdate
            {
                Id = message.Id,
                PublishStatus = message.PublishStatus,
                Body = message.Body,
                Subject = message.Subject,
                Package = message.Package,
                Category = message.Category,
                ImageUrl = message.ImageUrl
            });

            await _publicSchemaContext.SaveChangesAsync();

            foreach (var subDomain in subDomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subDomain));
                context.ProductUpdates.Add(new ProductUpdate
                {
                    Id = message.Id,
                    PublishStatus = message.PublishStatus,
                    Body = message.Body,
                    Subject = message.Subject,
                    Package = message.Package,
                    Category = message.Category,
                    ImageUrl = message.ImageUrl
                });

                await context.SaveChangesAsync();
            }
        }

        private async Task UpdateProductUpdate(List<string> subDomains, ProductUpdateMessage message)
        {
            var productUpdate = await _publicSchemaContext.ProductUpdates.FindAsync(message.Id);
            if (productUpdate is not null)
            {
                productUpdate.PublishStatus = message.PublishStatus;
                productUpdate.UpdatedAt = DateTime.UtcNow;

                _publicSchemaContext.ProductUpdates.Update(productUpdate);
                await _publicSchemaContext.SaveChangesAsync();
            }

            foreach (var subDomain in subDomains)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subDomain));
                var tenantProductUpdate = await context.ProductUpdates.FindAsync(message.Id);

                if (tenantProductUpdate is not null)
                {
                    tenantProductUpdate.PublishStatus = message.PublishStatus;
                    tenantProductUpdate.UpdatedAt = DateTime.UtcNow;

                    context.ProductUpdates.Update(tenantProductUpdate);
                    await context.SaveChangesAsync();
                }
            }
        }

        private List<string> GetSubDomains(string package)
        {
            if (string.IsNullOrWhiteSpace(package))
            {
                _logger.Error("Package cannot be null");
                throw new ArgumentNullException(nameof(package));
            }

            List<string> subDomains;

            if (package.Equals("All", StringComparison.InvariantCultureIgnoreCase))
            {
                subDomains = _publicSchemaContext.Tenants.Where(x => x.isSchemaCreated).Select(x => x.Subdomain).ToList();
            }
            else
            {
                var application = Enum.Parse<Applications>(package);
                subDomains = _publicSchemaContext.CompanySubscriptions
                            .Include(x => x.Tenant)
                            .Where(x => x.Application == application && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active)
                            .Select(x => x.Tenant.Subdomain)
                            .ToList();
            }

            return subDomains;
        }
    }
}
