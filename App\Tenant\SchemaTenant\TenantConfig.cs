using System;
using System.Linq;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using static Jobid.App.Helpers.Utils.Utility;

namespace Jobid.App.SchemaTenant
{
    public interface ITenantConfig<T>
    {
        T getRequestContext(HttpContext context);
        public string getSubdomainName(HttpContext context);
    }

    public class TenantConfig : ITenantConfig<JobProDbContext>
    {
        JobProDbContext context;
        private readonly CustomAppSettings _appSettings;
        string conString;
        private readonly IConfiguration _config;

        public TenantConfig(IConfiguration config)
        {
            _config = config;
            conString = GlobalVariables.ConnectionString;
            this.context = new JobProDbContext(conString, new DbContextSchema());
            _appSettings = GlobalVariables.CustomAppSettings;
        }

        public string getSubdomainName(HttpContext context)
        {
            var _schemaName = string.Empty;
            if (context.Request.Headers.TryGetValue("subdomain", out var subdomain))
            {
                _schemaName = subdomain;
            }
            _schemaName = _schemaName.prepareSubdomainName();

            return _schemaName;
        }

        public JobProDbContext getRequestContext(HttpContext context)
        {
            // Get the url from context
            var url = context.Request.Path.Value;
            if (!string.IsNullOrEmpty(url) && url.Contains("webhook"))
                return null;

            string rootSubDomain = _appSettings.RootDomain;
            var subdomain = this.getSubdomainName(context);
            Tenant.Model.Tenant tenant = null;

            if (subdomain == "api" || subdomain == "admin")
            {
                subdomain = Constants.PUBLIC_SCHEMA;
                tenant = new Tenant.Model.Tenant();
            }
            else
            {
                // verify schema exists in subdomain
                tenant = this.context.Tenants.FirstOrDefault(x => x.Subdomain == subdomain);
            }

            if (tenant != null)
            {
                return new JobProDbContext(conString, new DbContextSchema(subdomain));
            }
            else
            {
                if (subdomain == rootSubDomain) return null;

                throw new Exception("Subdomain not found.");
            }
        }
    }
}