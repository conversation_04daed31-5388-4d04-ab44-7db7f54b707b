﻿using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class AddMemberToMeeting
    {
        public List<string> InternalMemberIds { get; set; }
        public string CalenderId { get; set; }
        public string LoggedInUserId { get; set; }
        public List<string> ExternalTeamMembers { get; set; }
        public string Token { get; set; }
        public string Subdomain { get; set; }

        public AddMemberToMeeting()
        {
            InternalMemberIds = new List<string>();
            ExternalTeamMembers = new List<string>();
        }
    }
}
