﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class AllProjectsForTimeSheetDto
    {
        public string ProjectId { get; set; }
        public string ProjectName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int ToDosCount { get; set; }
        public string AssignedTo { get; set; }
        public string AssignedBy { get; set; }
        public ProjectStatus Status { get; set; }
        public decimal ProjectValue { get; set; }
        public string TimeSpent { get; set; }
        public List<string> Tags { get; set; } = new List<string>();
        public int BillableHours { get; set; }
        public bool IsBillable { get; set; }
        public string Description { get; set; }
        public string CurrencySymbol { get; set; }
        public List<string> Todos { get; set; } = new List<string>();
        public List<ProjectFile> Files { get; set; } = new List<ProjectFile>();
    }
}
