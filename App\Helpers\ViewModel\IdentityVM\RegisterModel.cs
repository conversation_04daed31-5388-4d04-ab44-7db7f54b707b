﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.ViewModel.IdentityVM
{
    public class RegisterModel
    {
        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        public string Email { get; set; }
        public string Password { get; set; } = "";
        public bool SendMail { get; set; }
        public string ConfirmPassword { get; set; }  = "";

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string Gender { get; set; }

        [Required]
        public string PhoneNumber { get; set; }
        public string DateOfBirth { get; set; }
        public string Status { get; set; }
        public string InvitedBy { get; set; }
        public bool PasswordCreatedByAdmin { get; set; } = false;
        public Region Region { get; set; } = Region.Africa;
        public KYCTrustLevels KycStatus { get; set; }
        public Guid TenantId { get; set; }
        public string GoogleAuthToken { get; set; }
        public string GoogleAuthId { get; set; }
        public string MicrosoftAccessToken { get; set; }
        public string MicrosoftAuthId { get; set; }
        public string CountryCode { get; set; }
        public string Country { get; set; }
        public string PhoneCountryCode { get; set; }
        public Applications Application { get; set; } = Applications.Joble;
        public string RoleId { get; set; }

        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        public string CompanyEmail { get; set; }


    }

    public class CompanyAdminModel
    {
        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        [ValidCompanyEmailDomian]
        public string Email { get; set; }

        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        public string PersonalEmail { get; set; }

        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Compare(nameof(Password))]
        public string ConfirmPassword { get; set; } = "";

        [Required]
        public string FirstName { get; set; }
        public string Gender { get; set; }

        [Required]
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string PhoneCountryCode { get; set; }

        [Required]
        public string PhoneNumber { get; set; }
        public string GoogleAuthToken { get; set; }
        public string GoogleAuthId { get; set; }
        public string MicrosoftAccessToken { get; set; }
        public string MicrosoftAuthId { get; set; }
        public string IpAddress { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string State { get; set; }
        public string DateOfBirth { get; set; }
        public string BaseCurrency { get; set; }
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public string Designation { get; set; }
    }

    public class CompanyInviteModel
    {
        [DataType(DataType.EmailAddress)]
        public string Email { get; set; }
        public bool SendMail { get; set; }
        public KYCTrustLevels TrustLevel { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
    }

    public class UserSignupModel
    {
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        public string Email { get; set; }
        public string Password { get; set; } = "";
        public bool SendMail { get; set; }
        public string ConfirmPassword { get; set; }  = "";

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string Role { get; set; }
        public string Profession { get; set; }
        public string PhoneNumber { get; set; } 
        public Region Region { get; set; } = Region.Africa;
        public string GoogleAuthToken { get; set; }
        public string GoogleAuthId { get; set; }
        public string MicrosoftAccessToken { get; set; }
        public string MicrosoftAuthId { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string Gender { get; set; }
        public string IpAddress { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string BaseCurrency { get; set; }
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public string State { get; set; }
        public Applications? Application { get; set; }
        public string ReferralCode { get; set; }
    }

}
