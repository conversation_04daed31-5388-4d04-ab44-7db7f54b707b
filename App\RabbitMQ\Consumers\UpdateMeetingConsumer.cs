﻿using AutoMapper;
using Calendar.Service.RabbitMQ.Consumers;
using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.ViewModel;
using Microsoft.Extensions.DependencyInjection;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using static Jobid.App.RabbitMQ.Records;
using System.Collections.Generic;
using System.Threading;
using System;
using Serilog;
using System.Threading.Tasks;
using System.Text;
using WatchDog;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;

namespace Jobid.App.RabbitMQ.Consumers
{
    public class UpdateMeetingConsumer : BackgroundService
    {
        private readonly IMapper _mapper;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private Serilog.ILogger _logger = Log.ForContext<UpdateMeetingConsumer>();
        private readonly Dictionary<string, object> args;
        private readonly ICalenderService _calenderService;

        public UpdateMeetingConsumer(IMapper mapper, IServiceScopeFactory serviceScopeFactory)
        {
            _mapper = mapper;
            _serviceScopeFactory = serviceScopeFactory;

            // Get the generic consumer from the service scope factory
            using var scope = serviceScopeFactory.CreateScope();
            _calenderService = scope.ServiceProvider.GetRequiredService<ICalenderService>();

            // Set how long the message should live on the queue i.e ttl
            var expiration = **********; // 100 hours
            args = new Dictionary<string, object>
            {
                {"x-message-ttl", expiration}
            };
        }

        public async virtual Task StartConsumer(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                stoppingToken.ThrowIfCancellationRequested();
                var model = new ConsumeModel(RabbitMQConstants.AIUpdateMeetingQueue, RabbitMQConstants.AIUpdateMeetingEvent, "", ExchangeType.Fanout);

                using var scope = _serviceScopeFactory.CreateScope();
                var rabbitMQConnectionService = scope.ServiceProvider.GetRequiredService<IRabbitMQConnectionService>();
                var channel = rabbitMQConnectionService._channel;

                if (channel is not null)
                {
                    channel.QueueDeclare(queue: model.QueueName, durable: true, exclusive: false, autoDelete: false, arguments: args);
                    channel.QueueBind(queue: model.QueueName, exchange: model.ExchangeName, routingKey: "");
                    //channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);
                    var consumer = new AsyncEventingBasicConsumer(channel);

                    consumer.Received += async (ch, ea) =>
                    {
                        var message = Encoding.UTF8.GetString(ea.Body.ToArray());
                        await CreateMeeting(message);

                        _logger.Information($"{model.QueueName} message received: {message}");
                        WatchLogger.Log($"{model.QueueName} message received: {message}");
                        //channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                    };

                    channel.BasicConsume(queue: model.QueueName, autoAck: true, consumer: consumer);
                    await Task.Delay(100);
                }
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            await Task.Run(() => StartConsumer(stoppingToken));
        }

        private async Task CreateMeeting(string message)
        {
            var meetingPayload = JsonConvert.DeserializeObject<UpdateCalenderDto>(message);
            if (meetingPayload != null)
            {
                var response = await _calenderService.UpdateMeetingOrEvents(meetingPayload);
                if (response == null)
                    _logger.Error("UpdateMeetingConsumer: Meeting update failed");
            }
        }
    }
}
