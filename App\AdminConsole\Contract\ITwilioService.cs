﻿using Jobid.App.AdminConsole.Dto;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Contract
{
    public interface ITwilioService
    {
        /// <summary>
        /// Get details of a specific call by its SID
        /// </summary>
        /// <param name="callSid"></param>
        /// <returns></returns>
        Task<CallDetailsDto> GetCallDetailsAsync(string callSid);

        /// <summary>
        /// Get Call recordings for a specific call by its SID
        /// </summary>
        /// <param name="callSid"></param>
        /// <returns></returns>
        Task<List<TranscriptionDto>> GetCallTranscriptionsAsync(string callSid);
    }
}
