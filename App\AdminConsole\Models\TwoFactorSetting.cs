﻿using Jobid.App.AdminConsole.Enums;
using Jobid.App.Calender.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models
{
    public class TwoFactorSetting : BaseModel
    {
        [Key]
        public Guid Id { get; set; }
        public _2FAOptions options { get; set; }
        public bool Require2FA { get; set; }
        public bool ImidiatelyRequire2FA { get; set; }
        public bool Require2FAOnThirdLogin { get; set; }
        public bool Require2FAOnSecondFailedLoginAttempt { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }

        public TwoFactorSetting()
        {
            Id = Guid.NewGuid();
            options = _2FAOptions.None;
            Require2FA = false;
            ImidiatelyRequire2FA = false;
            Require2FAOnThirdLogin = false;
            Require2FAOnSecondFailedLoginAttempt = false;
        }
    }
}
