using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Linq;

namespace Jobid.App.AdminConsole.Hubs
{
    // Temporarily allow anonymous access for testing - remove [Authorize] until JWT tokens are properly configured
    // [Authorize]
    public class CallHub : Hub
    {
        private readonly ILogger<CallHub> _logger;
        
        public CallHub(ILogger<CallHub> logger)
        {
            _logger = logger;
        }
   
        private string GetUserId()
        {
            return Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                   Context.User?.FindFirst("sub")?.Value ??
                   Context.User?.FindFirst("userId")?.Value ??
                   throw new UnauthorizedAccessException();
        }
        
        public override async Task OnConnectedAsync()
        {
            var userId = GetUserId();
            _logger.LogInformation("SignalR connection established for user: {UserId}, Connection ID: {ConnectionId}", userId, Context.ConnectionId);
            
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var userId = GetUserId();
            _logger.LogInformation("SignalR connection disconnected for user: {UserId}, Connection ID: {ConnectionId}", userId, Context.ConnectionId);
            
            // Clean up user connection mapping
            CustomUserIdProvider.RemoveUserConnection(Context.ConnectionId);
            
            await base.OnDisconnectedAsync(exception);
        }
        
        public async Task AssociateUserConnection(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Cannot associate connection with empty user ID");
                return;
            }
            
            // Associate user with connection
            CustomUserIdProvider.AssociateUserConnection(userId, Context.ConnectionId);
            _logger.LogInformation("Associated user {UserId} with connection {ConnectionId}", userId, Context.ConnectionId);
            
            await Task.CompletedTask;
        }

        public async Task JoinCallGroup(string phoneNumberId)
        {
            var userId = GetUserId();
            _logger.LogInformation("User {UserId} joining call group: {PhoneNumberId}", userId, phoneNumberId);
            await Groups.AddToGroupAsync(Context.ConnectionId, phoneNumberId);
        }

        public async Task LeaveCallGroup(string phoneNumberId)
        {
            var userId = GetUserId();
            _logger.LogInformation("User {UserId} leaving call group: {PhoneNumberId}", userId, phoneNumberId);
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, phoneNumberId);
        }

        public async Task UpdateCallStatus(string phoneNumberId, string status, string callId)
        {
            var userId = GetUserId();
            _logger.LogInformation("User {UserId} updating call status for group {PhoneNumberId}: {Status}", userId, phoneNumberId, status);
            await Clients.Group(phoneNumberId).SendAsync("CallStatusUpdated", new { CallId = callId, Status = status });
        }
    }
}
