﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.ViewModel
{
    public class AddOrUpdateSkillsDto
    {
        [Required(ErrorMessage = "UserId is required")]
        public string UserId { get; set; }

        public List<CategoryAndkillDto> CategoryAndkills { get; set; } = new List<CategoryAndkillDto>();
    }

    public class CategoryAndkillDto
    {
        public SkillCategory Category { get; set; }

        public List<string> Skills { get; set; }
    }
}
