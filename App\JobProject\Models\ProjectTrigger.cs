﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.Helpers.Utils.Extensions;

namespace Jobid.App.JobProjectManagement.Models
{
    public class ProjectTrigger
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public string TriggerName { get; set; }
        public TriggerReason TriggerReason { get; set; } = TriggerReason.PaymentDue;
        public string Reasons { get; set; }

        [NotMapped]
        public List<string> TriggerReasons { get; set; }
        public DateTime TriggerTime { get; set; }
        public string ParticipantsIds { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; } = GetAdjustedDateTimeBasedOnTZNow();
        public string ProjectMgmt_ProjectId { get; set; }
        public ProjectMgmt_Project projectMgmt_Project { get; set; }
        public bool Notification { get; set; }
        public bool SMS { get; set; }
        public bool Email { get; set; }
    }
}
