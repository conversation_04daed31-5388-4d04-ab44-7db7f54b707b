﻿using Jobid.App.Calender.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{
    public class PasswordPolicy : BaseModel
    {
        [Key]
        public Guid Id { get; set; }
        public int MinimumPasswordLength { get; set; }
        public bool RequireAtLeastOneUppercase { get; set; }
        public bool RequireAtLeastOneLowercase { get; set; }
        public bool RequireAtLeastOneNumber { get; set; }
        public bool RequireAtLeastOneSpecialCharacter { get; set; }
        public bool ProhibitUserNameAsPassword { get; set; }
        public bool RequirePasswordExpiration { get; set; }
        public int PasswordExpirationDays { get; set; }

        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }

        public PasswordPolicy()
        {
            Id = Guid.NewGuid();
            MinimumPasswordLength = 8; // Default minimum password length
            RequireAtLeastOneUppercase = true;
            RequireAtLeastOneLowercase = true;
            RequireAtLeastOneNumber = true;
            RequireAtLeastOneSpecialCharacter = true;
            ProhibitUserNameAsPassword = true;
            RequirePasswordExpiration = true;
            PasswordExpirationDays = 90; // Default password expiration days
        }
    }
}
