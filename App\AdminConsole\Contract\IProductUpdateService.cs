﻿using Jobid.App.AdminConsole.Dto;
using Jobid.App.Helpers.ViewModel;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Contract
{
    public interface IProductUpdateService
    {
        Task ProcessProductUpdateMessage(ProductUpdateMessage model);

        Task<Page<SentProductUpdateDto>> GetSentProductUpdates(string subdomain, int page, int pageSize);
        Task<Page<DeletedProductUpdateDto>> GetDeletedProductUpdates(string subdomain, int page, int pageSize);
    }
}
