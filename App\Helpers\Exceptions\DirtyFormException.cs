﻿using System.Runtime.Serialization;
using System;

namespace Jobid.App.Helpers.Exceptions
{
    [Serializable]
    public class DirtyFormException : Exception
    {
        public DirtyFormException()
        {
        }

        public DirtyFormException(string message) : base(message)
        {
        }

        public DirtyFormException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected DirtyFormException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
