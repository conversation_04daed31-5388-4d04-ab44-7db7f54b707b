using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers.Enums;
using Jobid.App.Notification.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.Helpers.Models
{
    public class UserProfile
    {
        [Key]
        public string Id { get; set; }
        public string JobProId { get; set; }

        [Required]
        public string UserId { get; set; }
        public string Gender { get; set; }

        [Required]
        public string Email { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string JobPaysPin { get; set; }
        public string MobileLoginPin { get; set; }
        public string WeaverCompanyIndustry { get; set; }
        public string DOB { get; set; }
        public DateTime DateCreated { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
        public bool IsVerified { get; set; } = false;
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Country { get; set; }
        public string TimeZone { get; set; }
        public string State { get; set; }
        public string Bio { get; set; }
        public string ProfilePictureUrl { get; set; }
        public string EmployerUserID { get; set; }
        public string SubDomain { get; set; }

        [Required]
        public string PhoneNumber { get; set; }
        public string SecondaryEmail { get; set; }
        public string SecondaryPhoneNumber { get; set; }
        public bool IsSuspended { get; set; }
        public string SuspendedEmployeeId { get; set; }
        public bool IsDeleted { get; set; }
        public byte[] QrCode { get; set; }
        public bool AuthUserCreated { get; set; }
        public bool AuthUserPasswordCreated { get; set; }
        public string Profession { get; set; }

        [Column(TypeName = "varchar(24)")]
        public Currency Currency { get; set; }
        public string WeavrId { get; set; }
        public string SignatoryId { get; set; }
        public bool IsSignatory { get; set; }
        public string EventCategory { get; set; }
        public EraseAcitivity EraseAcitivity { get; set; } = EraseAcitivity.Never;
        public InternalOrExternal InternalOrExternal { get; set; } = InternalOrExternal.Internal;
        public bool LogActivity { get; set; } = true;
        public string Designation { get; set; }
        public UserOnlineStatusOptions OnlineStatus { get; set; } = UserOnlineStatusOptions.Active;

        // This is used to track if the user has been logged out from the system by an admin, default is false
        public bool UserLoggedOut { get; set; }

        public _2FAOptions _2FAOptions { get; set; } = _2FAOptions.None;

        [NotMapped]
        public bool IsTwoFactorEnabled
        { 
            get => _2FAOptions != _2FAOptions.None;
        }
        public string TwoFactorSecretKey { get; set; }

        // Navigation properties
        [NotMapped]
        public ICollection<UserNotification> UserNotifications { get; set; }

        [NotMapped]
        public string EmployeeRole { get; set; }

        [NotMapped]
        public Tenant.Model.Tenant CompanyDetails { get; set; }

        [NotMapped]
        public string Status { get; set; }

        [NotMapped]
        public bool? HasAccess { get; set; }
    }

    public class UserProfilesForAdminDto
    {
        public UserProfile UserProfile { get; set; }
        public string TenantWorkSpace { get; set; }
        public string TenantBusinessRegistrationNumber { get; set; }
        public string TenantCompanyAddress { get; set; }
    }

    public class ApplicationUserDto
    {
        public string UserId { get; set; }
        public string ProfilePictureUrl { get; set; }
        public string Name { get; set; }
        public string AccessType { get; set; }
        public string Role { get; set; }
        public string Email { get; set; }
        public string Status { get; set; }
        public string LastSeen { get; set; }
        public DateTime DateCreated { get; set; }
        public string AIAgents {  get; set; }
    }
}