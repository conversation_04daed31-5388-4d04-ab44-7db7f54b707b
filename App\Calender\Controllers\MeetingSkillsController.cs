using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Jobid.App.Calender.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MeetingSkillsController : ControllerBase
    {
        private readonly IMeetingSkillsService _meetingSkillsService;
        private readonly ITenantSchema _tenantSchema;

        public MeetingSkillsController(IMeetingSkillsService meetingSkillsService, ITenantSchema tenantSchema)
        {
            _meetingSkillsService = meetingSkillsService;
            _tenantSchema = tenantSchema;
        }

        /// <summary>
        /// Suggest skills for a meeting
        /// </summary>
        /// <param name="model">Meeting skill suggestion data</param>
        /// <returns>Response with the created meeting skill suggestion</returns>
        [HttpPost]
        [Route("suggestions")]
        public async Task<IActionResult> SuggestMeetingSkills([FromBody] MeetingSkillSuggestionDto model)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var result = await _meetingSkillsService.SuggestMeetingSkills(model, userId);

            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }

        /// <summary>
        /// Get skill suggestions for a meeting
        /// </summary>
        /// <param name="meetingId">Meeting ID</param>
        /// <returns>Response with list of meeting skill suggestions</returns>
        [HttpGet]
        [Route("suggestions/{meetingId}")]
        public async Task<IActionResult> GetMeetingSkillSuggestions(Guid meetingId)
        {
            var model = new GetMeetingSkillSuggestionsDto { MeetingId = meetingId };
            var result = await _meetingSkillsService.GetMeetingSkillSuggestions(model);

            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }

        /// <summary>
        /// Delete skill suggestions for a meeting
        /// </summary>
        /// <param name="meetingId">Meeting ID</param>
        /// <returns>Response with success status</returns>
        [HttpDelete]
        [Route("suggestions/{meetingId}")]
        public async Task<IActionResult> DeleteMeetingSkillSuggestions(Guid meetingId)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var result = await _meetingSkillsService.DeleteMeetingSkillSuggestions(meetingId, userId);

            return result.ResponseCode == "200"
                ? Ok(result)
                : BadRequest(result);
        }
    }
}
