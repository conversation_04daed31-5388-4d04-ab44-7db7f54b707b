﻿using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.RabbitMQ;
using Microsoft.Extensions.Configuration;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Serilog;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ILogger = Serilog.ILogger;

namespace Jobid.App.AdminConsole.Services
{
    public class ProductUpdateReceiverService : IProductUpdateReceiverService
    {
        #region Properties and Constructors
        private readonly ILogger _logger = Log.ForContext<ProductUpdateReceiverService>();
        private readonly IConnection _connection;
        private readonly IModel _channel;
        private readonly IProductUpdateService _productUpdateService;

        public ProductUpdateReceiverService(IConfiguration config, IProductUpdateService productUpdateService, IRabbitMQConnectionService rabbitMQConnectionService)
        {
            _connection = rabbitMQConnectionService._connection;
            if (_connection is not null)
            {
                _channel = _connection.CreateModel();
            }

            _productUpdateService = productUpdateService;
        }

        #endregion

        #region Receive Published Product Updates from RabbitMQ
        public void ReceiveMessage()
        {
            if (_channel is null)
            {
                _logger.Error("RabbitMQ channel is null");
                return;
            }

            _channel.ExchangeDeclare(RabbitMQConstants.PublishProductUpdateEvent, ExchangeType.Direct);
            _channel.QueueDeclare(RabbitMQConstants.ProductUpdateQueue, true, false, false, null);
            _channel.QueueBind(RabbitMQConstants.ProductUpdateQueue, RabbitMQConstants.PublishProductUpdateEvent, RabbitMQConstants.ProductUpdateKey, null);
            _channel.BasicQos(0, 1, false);

            if (_connection is not null)
            {
                if (_connection.IsOpen)
                {
                    var consumer = new EventingBasicConsumer(_channel);
                    consumer.Received += async (model, eventArgs) =>
                    {
                        var body = eventArgs.Body.ToArray();
                        var message = Encoding.UTF8.GetString(body);

                        await ProcessMessage(message);
                        _channel.BasicAck(eventArgs.DeliveryTag, false);
                    };
                    _channel.BasicConsume(RabbitMQConstants.ProductUpdateQueue, false, consumer);
                }
                else
                {
                    _connection?.Close();
                    _connection?.Dispose();
                }
            }

        }
        #endregion

        private async Task ProcessMessage(string message)
        {
            ProductUpdateMessage data = JsonSerializer.Deserialize<ProductUpdateMessage>(message);
            await _productUpdateService.ProcessProductUpdateMessage(data);
        }
    }
}
