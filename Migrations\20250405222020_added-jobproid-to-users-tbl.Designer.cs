﻿// <auto-generated />
using System;
using Jobid.App.Helpers.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Jobid.Migrations
{
    [DbContext(typeof(JobProDbContext))]
    [Migration("20250405222020_added-jobproid-to-users-tbl")]
    partial class addedjobproidtouserstbl
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("public")
                .HasAnnotation("Relational:MaxIdentifierLength", 63)
                .HasAnnotation("ProductVersion", "5.0.11")
                .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            modelBuilder.Entity("CRMCompanyCRMCompanyCollaborator", b =>
                {
                    b.Property<long>("CRMCompaniesId")
                        .HasColumnType("bigint");

                    b.Property<long>("CRMCompanyCollaboratorsId")
                        .HasColumnType("bigint");

                    b.HasKey("CRMCompaniesId", "CRMCompanyCollaboratorsId");

                    b.HasIndex("CRMCompanyCollaboratorsId");

                    b.ToTable("CRMCompanyCRMCompanyCollaborator");
                });

            modelBuilder.Entity("CRMCompanyCRMCompanyTag", b =>
                {
                    b.Property<long>("CRMCompaniesId")
                        .HasColumnType("bigint");

                    b.Property<long>("CompanyTagsId")
                        .HasColumnType("bigint");

                    b.HasKey("CRMCompaniesId", "CompanyTagsId");

                    b.HasIndex("CompanyTagsId");

                    b.ToTable("CRMCompanyCRMCompanyTag");
                });

            modelBuilder.Entity("CRMContactCRMContactCollaborator", b =>
                {
                    b.Property<long>("CRMContactCollaboratorsId")
                        .HasColumnType("bigint");

                    b.Property<long>("CRMContactsId")
                        .HasColumnType("bigint");

                    b.HasKey("CRMContactCollaboratorsId", "CRMContactsId");

                    b.HasIndex("CRMContactsId");

                    b.ToTable("CRMContactCRMContactCollaborator");
                });

            modelBuilder.Entity("CRMContactCRMContactTag", b =>
                {
                    b.Property<long>("CRMContactsId")
                        .HasColumnType("bigint");

                    b.Property<long>("TagsId")
                        .HasColumnType("bigint");

                    b.HasKey("CRMContactsId", "TagsId");

                    b.HasIndex("TagsId");

                    b.ToTable("CRMContactCRMContactTag");
                });

            modelBuilder.Entity("CRMDealCRMDealCollaborator", b =>
                {
                    b.Property<long>("CRMDealCollaboratorsId")
                        .HasColumnType("bigint");

                    b.Property<long>("CRMDealsDealId")
                        .HasColumnType("bigint");

                    b.HasKey("CRMDealCollaboratorsId", "CRMDealsDealId");

                    b.HasIndex("CRMDealsDealId");

                    b.ToTable("CRMDealCRMDealCollaborator");
                });

            modelBuilder.Entity("CRMLeadCRMLeadCollaborator", b =>
                {
                    b.Property<long>("CRMLeadCollaboratorsId")
                        .HasColumnType("bigint");

                    b.Property<long>("CRMLeadsId")
                        .HasColumnType("bigint");

                    b.HasKey("CRMLeadCollaboratorsId", "CRMLeadsId");

                    b.HasIndex("CRMLeadsId");

                    b.ToTable("CRMLeadCRMLeadCollaborator");
                });

            modelBuilder.Entity("CRMLeadCRMTag", b =>
                {
                    b.Property<long>("LeadsId")
                        .HasColumnType("bigint");

                    b.Property<long>("TagsId")
                        .HasColumnType("bigint");

                    b.HasKey("LeadsId", "TagsId");

                    b.HasIndex("TagsId");

                    b.ToTable("CRMLeadCRMTag");
                });

            modelBuilder.Entity("CRMTagCRMTodo", b =>
                {
                    b.Property<long>("TagsId")
                        .HasColumnType("bigint");

                    b.Property<long>("TodosId")
                        .HasColumnType("bigint");

                    b.HasKey("TagsId", "TodosId");

                    b.HasIndex("TodosId");

                    b.ToTable("CRMTagCRMTodo");
                });

            modelBuilder.Entity("ContactNoteNoteOwner", b =>
                {
                    b.Property<long>("ContactNotesId")
                        .HasColumnType("bigint");

                    b.Property<long>("NoteOwnersId")
                        .HasColumnType("bigint");

                    b.HasKey("ContactNotesId", "NoteOwnersId");

                    b.HasIndex("NoteOwnersId");

                    b.ToTable("ContactNoteNoteOwner");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActivitySummary")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("By")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("EventCategory")
                        .HasColumnType("integer");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<string>("GenericUrl")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Activities");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ActivityRequestedPermisssions", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("EventCategories")
                        .HasColumnType("text");

                    b.Property<DateTime>("FromDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("RequesterId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ToDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ActivityRequestedPermisssions");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ActivityView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActivitySummary")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("EventCategory")
                        .HasColumnType("integer");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ActivityViews");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.LogAttachment", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ActivityId");

                    b.ToTable("LogAttachments");
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.ShareActivityParams", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AnyOneCanView")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("EventCategory")
                        .HasColumnType("text");

                    b.Property<string>("MemberId")
                        .HasColumnType("text");

                    b.Property<string>("ShareId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("ShareActivityParams");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.BasicInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AvailableNumberOfLeaveDays")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateOfHire")
                        .HasColumnType("timestamp");

                    b.Property<decimal>("GrossSalary")
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime?>("LastDayOfContract")
                        .HasColumnType("timestamp");

                    b.Property<int>("LengthOfContract")
                        .HasColumnType("integer");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("BasicInfos");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchColor")
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("DepartmentName")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("Departments");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmergencyInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmergencyContactAddress")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactAlternativePhone")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactEmailAddress")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactName")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("EmergencyContactRelationship")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("EmergencyInfos");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeeComplaint", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("ActionWithdrawn")
                        .HasColumnType("boolean");

                    b.Property<int>("Category")
                        .HasColumnType("integer");

                    b.Property<string>("ComplaintRemark")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateIssued")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("IssuedBy")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmployeeComplaints");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeePermission", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("PackageName")
                        .HasColumnType("text");

                    b.Property<string>("PermissionCategory")
                        .HasColumnType("text");

                    b.Property<string>("PermissionName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmployeePermissions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeePosition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("PositionName")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("EmployeePositions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeeRoles", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("PackageName")
                        .HasColumnType("text");

                    b.Property<string>("RoleName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmployeeRoles");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeeRolesPermission", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("PermissionId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId");

                    b.ToTable("EmployeeRolesPermissions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.Individual", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchColor")
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsHeadofDepartment")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("PositionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("Individuals");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.OrganogramCompany", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<long>("BelongsTo")
                        .HasColumnType("bigint");

                    b.Property<string>("BranchColor")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int>("CompanyType")
                        .HasColumnType("integer");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<int>("EntityType")
                        .HasColumnType("integer");

                    b.Property<string>("FullAddress")
                        .HasColumnType("text");

                    b.Property<int>("Industry")
                        .HasColumnType("integer");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<long>("index")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.HasKey("Id");

                    b.ToTable("OrganogramCompanies");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.ProductUpdate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("text");

                    b.Property<string>("Package")
                        .HasColumnType("text");

                    b.Property<int>("PublishStatus")
                        .HasColumnType("integer");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("ProductUpdates");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.SuspendedEmployee", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsIndefinite")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("SuspendedOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("UnSuspendedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SuspendedEmployees");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BackGroundJobId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("JobId")
                        .HasColumnType("text");

                    b.Property<int>("JobType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("BackGroundJobIds");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedExternalMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdditionalInfo")
                        .HasColumnType("text");

                    b.Property<DateTime>("BookedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("CancelMeetingLink")
                        .HasColumnType("text");

                    b.Property<int>("DurationInMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GuestEmails")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("PersonalScheduleId")
                        .HasColumnType("text");

                    b.Property<string>("ReScheduleMeetingLink")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ReScheduledOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("ReasonForCancelling")
                        .HasColumnType("text");

                    b.Property<string>("ReasonForReSchedulling")
                        .HasColumnType("text");

                    b.Property<DateTime>("SelectedDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.ToTable("BookedExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedMeetingMember", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BookedMeetingId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<int>("InviteResponse")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("NotifyMeInMinutes")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BookedMeetingMembers");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderExternalIntegration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("IcalenderEventId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MeetingId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CalenderExternalIntegrations");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Attachment")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("CreatedByAI")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("Frequency")
                        .HasColumnType("text");

                    b.Property<bool>("HasCustomFrequency")
                        .HasColumnType("boolean");

                    b.Property<bool>("HasThisMeetingHappened")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<bool>("MakeSchdulePrivate")
                        .HasColumnType("boolean");

                    b.Property<int?>("MeetLength")
                        .HasColumnType("integer");

                    b.Property<string>("MeetingDuration")
                        .HasColumnType("text");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("MeetingLink")
                        .HasColumnType("text");

                    b.Property<int>("MeetingOwnerType")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("NotifyMe")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMembersIn")
                        .HasColumnType("integer");

                    b.Property<int>("RescheduleCount")
                        .HasColumnType("integer");

                    b.Property<int>("ScheduleType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("CalenderMeetings");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderMeetingRecording", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("MeetingRecordingUrl")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CalenderMeetingRecordings");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderUpload", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("MeetingId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SubsequentMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("MeetingId");

                    b.HasIndex("SubsequentMeetingId");

                    b.ToTable("CalenderUploads");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomFrequency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CalenderMeetingId")
                        .HasColumnType("uuid");

                    b.Property<int>("EndStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("EndsAfter")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndsOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("RepeatCount")
                        .HasColumnType("integer");

                    b.Property<string>("RepeatEvery")
                        .HasColumnType("text");

                    b.Property<string>("RepeatOn")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CalenderMeetingId")
                        .IsUnique();

                    b.ToTable("CustomFrequency");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("Options")
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .HasColumnType("text");

                    b.Property<int>("QuestionType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.ToTable("CustomQuestions");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestionAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Answer")
                        .HasColumnType("text");

                    b.Property<Guid>("BookedExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CustomQuestionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Question")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BookedExternalMeetingId");

                    b.ToTable("CustomQuestionAnswers");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BookingLink")
                        .HasColumnType("text");

                    b.Property<bool>("CanBeChosenBasedOnAvailability")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("CanBookEndDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("CanBookStartDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("CancellationPolicy")
                        .HasColumnType("boolean");

                    b.Property<int>("ComfirmationPageOptions")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("EmailComfirmation")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailFollowUpAfter")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailRemindersBefore")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ExternalMeetingQuestionId")
                        .HasColumnType("uuid");

                    b.Property<int>("ExternalMeetingType")
                        .HasColumnType("integer");

                    b.Property<int>("ExternalMeetingTypePlan")
                        .HasColumnType("integer");

                    b.Property<string>("Guests")
                        .HasColumnType("text");

                    b.Property<string>("InternalNote")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("MakeMeetingPrivate")
                        .HasColumnType("boolean");

                    b.Property<int?>("MaxInvitePerMeeting")
                        .HasColumnType("integer");

                    b.Property<int>("MeetingDuration")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("MeetingEndDateRange")
                        .HasColumnType("timestamp");

                    b.Property<int>("MeetingFrequency")
                        .HasColumnType("integer");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("MeetingLink")
                        .HasColumnType("text");

                    b.Property<string>("MeetingName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MeetingOwnerId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("MeetingStartDateRange")
                        .HasColumnType("timestamp");

                    b.Property<string>("OtherMeetingHostsIds")
                        .HasColumnType("text");

                    b.Property<Guid>("PersonalScheduleId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Price")
                        .HasColumnType("numeric");

                    b.Property<bool>("PushMeetingToWebsite")
                        .HasColumnType("boolean");

                    b.Property<string>("SiteUrl")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingQuestionId");

                    b.HasIndex("PersonalScheduleId");

                    b.ToTable("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingMembers", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("ExternalMeetingId")
                        .HasColumnType("text");

                    b.Property<int>("InviteResponse")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMeInMinutes")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("SelectedDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ExternalMeetingMembers");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AddGuests")
                        .HasColumnType("boolean");

                    b.Property<bool>("AdditionalInfo")
                        .HasColumnType("boolean");

                    b.Property<bool>("AdditionalInfoRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("AskEmail")
                        .HasColumnType("boolean");

                    b.Property<bool>("AskFullName")
                        .HasColumnType("boolean");

                    b.Property<bool>("AskLocation")
                        .HasColumnType("boolean");

                    b.Property<string>("CustomQuestion")
                        .HasColumnType("text");

                    b.Property<bool>("CustomQuestionRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("EmailRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("FullNameRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("LocationRequired")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("ExternalMeetingQuestion");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingTimeManagement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("DayOfTheWeek")
                        .HasColumnType("text");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("PersonalScheduleId")
                        .HasColumnType("uuid");

                    b.Property<string>("ScheduleName")
                        .HasColumnType("text");

                    b.Property<string>("SelectedTimeSlots")
                        .HasColumnType("text");

                    b.Property<string>("TimeBreakDown")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.HasIndex("PersonalScheduleId");

                    b.ToTable("ExternalMeetingTimeManagements");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.MeetingNote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<Guid>("MeetingId")
                        .HasColumnType("uuid");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("SubsequentMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MeetingId");

                    b.HasIndex("SubsequentMeetingId");

                    b.ToTable("MeetingNotes");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.PersonalSchedule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Available")
                        .HasColumnType("boolean");

                    b.Property<bool>("AvailableForMeetingsEmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<bool>("AvailableForSprintsEmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<bool>("AvailableForTasksEmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<string>("Day")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("ScheduleIsPublic")
                        .HasColumnType("boolean");

                    b.Property<int>("ScheduleName")
                        .HasColumnType("integer");

                    b.Property<string>("SelectedTimeSlots")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("TeamMatesCanSee")
                        .HasColumnType("boolean");

                    b.Property<string>("TimeBreakDown")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId1")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId1");

                    b.ToTable("PersonalSchedule");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.RoundRobinHostingOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ExternalMeetingId")
                        .HasColumnType("uuid");

                    b.Property<int>("HostCount")
                        .HasColumnType("integer");

                    b.Property<string>("HostId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("MeetingDateAndTime")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("ExternalMeetingId");

                    b.ToTable("RoundRobinHostingOrders");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.SubsequentMeeting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CalenderMeetingId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("HasMeetingHappeed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCanceled")
                        .HasColumnType("boolean");

                    b.Property<bool>("MakeSchdulePrivate")
                        .HasColumnType("boolean");

                    b.Property<int?>("MeetLength")
                        .HasColumnType("integer");

                    b.Property<string>("MeetingDuration")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("NotificationSent")
                        .HasColumnType("boolean");

                    b.Property<int>("NotifyMe")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMembersIn")
                        .HasColumnType("integer");

                    b.Property<int>("RescheduleCount")
                        .HasColumnType("integer");

                    b.Property<DateTime>("SubsequentMeetingDateTime")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("CalenderMeetingId");

                    b.ToTable("SubsequentMeetings");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.UserIdCalenderId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CalenderId")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<int>("InviteResponse")
                        .HasColumnType("integer");

                    b.Property<int>("NotifyMeInMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("SubsequentMeetingId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("UserIdMeetingIds");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.AppPermissions", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("Agent")
                        .HasColumnType("integer");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("boolean");

                    b.Property<bool>("MakeFavorite")
                        .HasColumnType("boolean");

                    b.Property<string>("SubscriptionStatus")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AppPermissions");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.BlacklistedToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BlacklistedTokens");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.BrowserInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BrowserId")
                        .HasColumnType("text");

                    b.Property<bool>("IsBot")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMobile")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Platform")
                        .HasColumnType("text");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BrowserInfos");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CRMNotification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("CRMNotification");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CRMTasks", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("LeadId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("TaskName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("LeadId");

                    b.HasIndex("UserId");

                    b.ToTable("CRMTasks");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("RoleName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ClientRoles");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRoleRoleModule", b =>
                {
                    b.Property<Guid>("ClientRoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleModuleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.HasKey("ClientRoleId", "RoleModuleId");

                    b.HasIndex("RoleModuleId");

                    b.ToTable("ClientRoleRoleModules");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CompanySettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("WorkSpaceName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CompanySettings");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CompanyUserInvite", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("InviteCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.ToTable("CompanyUserInvites");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Country", b =>
                {
                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("financialService")
                        .HasColumnType("integer");

                    b.HasKey("Code");

                    b.ToTable("Countries");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.DefaultCompany", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("DefaultCompanies");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Location", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Location");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.LoginLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("LastLoginDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("LoginLogs");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.MicroService", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Key")
                        .HasColumnType("text");

                    b.Property<string>("ServiceId")
                        .HasColumnType("text");

                    b.Property<string>("ServiceName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("MicroServices");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.NationalLaguage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("NationalLaguages");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.OTP", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .HasColumnType("text");

                    b.Property<string>("IdentifierType")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<string>("TokenType")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("OTP");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Create")
                        .HasColumnType("boolean");

                    b.Property<bool>("Delete")
                        .HasColumnType("boolean");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.Property<Guid>("RoleModuleId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Update")
                        .HasColumnType("boolean");

                    b.Property<bool>("View")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("RoleModuleId");

                    b.ToTable("Permissions");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.PricingAndFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DurationType")
                        .HasColumnType("text");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLimited")
                        .HasColumnType("boolean");

                    b.Property<string>("LimitedTo")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("PricingAndFeatures");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SprintProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("TimeSheetId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.HasIndex("SprintProjectId");

                    b.HasIndex("TimeSheetId");

                    b.ToTable("ProjectFile");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Project", b =>
                {
                    b.Property<Guid>("ProjectId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("AmountFrequency")
                        .HasColumnType("integer");

                    b.Property<decimal?>("AmountPerSelectedFrequency")
                        .HasColumnType("numeric");

                    b.Property<string>("Clients")
                        .HasColumnType("text");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreatedTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<decimal?>("ExpectedProjectValue")
                        .HasColumnType("numeric");

                    b.Property<int>("Hours")
                        .HasColumnType("integer");

                    b.Property<bool>("IsBillable")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProjectStatus")
                        .HasColumnType("integer");

                    b.Property<string>("SprintProjectId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("tenantId")
                        .HasColumnType("text");

                    b.HasKey("ProjectId");

                    b.HasIndex("CompanyId");

                    b.ToTable("ProjectMgmt_Projects");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_ProjectUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.ToTable("projectMgmt_ProjectUsers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Todo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActualTimeSpent")
                        .HasColumnType("text");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("ApprovalStatus")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<string>("ClientName")
                        .HasColumnType("text");

                    b.Property<long?>("CompanyReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp");

                    b.Property<long?>("ContactReferenceId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("CreatedByAI")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<long?>("DealReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("ExistingTodoLink")
                        .HasColumnType("text");

                    b.Property<bool>("HasCustomFrequency")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBillable")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMeasurable")
                        .HasColumnType("boolean");

                    b.Property<long?>("KpiReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<long?>("LeadReferenceId")
                        .HasColumnType("bigint");

                    b.Property<bool>("LockTodo")
                        .HasColumnType("boolean");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<Guid?>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SprintProjectId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartDateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("TempCreationId")
                        .HasColumnType("text");

                    b.Property<string>("TimeLeft")
                        .HasColumnType("text");

                    b.Property<string>("TimeSpent")
                        .HasColumnType("text");

                    b.Property<string>("TodoDescription")
                        .HasColumnType("text");

                    b.Property<string>("TodoId")
                        .HasColumnType("text");

                    b.Property<string>("TodoName")
                        .HasColumnType("text");

                    b.Property<string>("TodoStatus")
                        .HasColumnType("text");

                    b.Property<string>("TodoSummary")
                        .HasColumnType("text");

                    b.Property<string>("WillBeDueIn")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.HasIndex("SprintProjectId");

                    b.ToTable("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_TodoUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("ExternalMemberEmail")
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("projectMgmt_TodoUsers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.RoleModule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("ModuleName")
                        .HasColumnType("text");

                    b.Property<string>("RegionName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("RoleModules");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.SecDomain", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Domain")
                        .HasColumnType("text");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SecDomains");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.TenantProjectView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TenantProjectViews");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.TodoTimeSequence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("TodoStatus")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("TodoTimeSequence");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("BaseCurrency")
                        .HasColumnType("text");

                    b.Property<string>("CV_URL")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<Guid?>("ClientRoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyId")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("Created_At")
                        .HasColumnType("timestamp");

                    b.Property<string>("DateOfBirth")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("EmailVerificationToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EmailVerificationTokenExpiry")
                        .HasColumnType("timestamp");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ForgotPasswordToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ForgotPasswordTokenExpiry")
                        .HasColumnType("timestamp");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<string>("GoogleAuthId")
                        .HasColumnType("text");

                    b.Property<string>("GovernmentId")
                        .HasColumnType("text");

                    b.Property<string>("HouseNo")
                        .HasColumnType("text");

                    b.Property<int>("IndividualUserAccountStatus")
                        .HasColumnType("integer");

                    b.Property<string>("InvitedBy")
                        .HasColumnType("text");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPhoneNumberVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("JobPaysPin")
                        .HasColumnType("text");

                    b.Property<string>("JobProId")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MicrosoftAuthId")
                        .HasColumnType("text");

                    b.Property<string>("MiddleName")
                        .HasColumnType("text");

                    b.Property<DateTime>("Modified_At")
                        .HasColumnType("timestamp");

                    b.Property<string>("NewReference")
                        .HasColumnType("text");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("OldReference")
                        .HasColumnType("text");

                    b.Property<bool>("PasswordCreatedByAdmin")
                        .HasColumnType("boolean");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneCountryCode")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("PhoneNumberVerificationToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("PhoneNumberVerificationTokenExpiry")
                        .HasColumnType("timestamp");

                    b.Property<string>("Profession")
                        .HasColumnType("text");

                    b.Property<string>("ProfileImageUrl")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProofOfResidence")
                        .HasColumnType("text");

                    b.Property<string>("ReferralCode")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryEmail")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("SendMail")
                        .HasColumnType("boolean");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StatusComment")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserInCompanyRole")
                        .HasColumnType("text");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("UserType")
                        .HasColumnType("integer");

                    b.Property<string>("WeavrId")
                        .HasColumnType("text");

                    b.Property<string>("WeavrPasscode")
                        .HasColumnType("text");

                    b.Property<bool>("WeavrPasscodeForPublic")
                        .HasColumnType("boolean");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.Property<bool>("lockIpAddress")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("ClientRoleId");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("AspNetUsers");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserAndRoleId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserProId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.ToTable("UserAndRoleIds");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserCompanies", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Active")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<int>("EmployementType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<int>("LocationType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("UserCompanies");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AccountType")
                        .HasColumnType("integer");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<bool>("AuthUserCreated")
                        .HasColumnType("boolean");

                    b.Property<bool>("AuthUserPasswordCreated")
                        .HasColumnType("boolean");

                    b.Property<string>("Bio")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("varchar(24)");

                    b.Property<string>("DOB")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Designation")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EmployerUserID")
                        .HasColumnType("text");

                    b.Property<int>("EraseAcitivity")
                        .HasColumnType("integer");

                    b.Property<string>("EventCategory")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<int>("InternalOrExternal")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSignatory")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("JobPaysPin")
                        .HasColumnType("text");

                    b.Property<string>("JobProId")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("LogActivity")
                        .HasColumnType("boolean");

                    b.Property<string>("MiddleName")
                        .HasColumnType("text");

                    b.Property<string>("MobileLoginPin")
                        .HasColumnType("text");

                    b.Property<int>("OnlineStatus")
                        .HasColumnType("integer");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Profession")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePictureUrl")
                        .HasColumnType("text");

                    b.Property<byte[]>("QrCode")
                        .HasColumnType("bytea");

                    b.Property<string>("SecondaryEmail")
                        .HasColumnType("text");

                    b.Property<string>("SecondaryPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("SignatoryId")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.Property<string>("SuspendedEmployeeId")
                        .HasColumnType("text");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("WeaverCompanyIndustry")
                        .HasColumnType("text");

                    b.Property<string>("WeavrId")
                        .HasColumnType("text");

                    b.Property<string>("ZipCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("UserProfiles");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserRefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedByIp")
                        .HasColumnType("text");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("timestamp");

                    b.Property<string>("ReplacedByToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("Revoked")
                        .HasColumnType("timestamp");

                    b.Property<string>("RevokedByIp")
                        .HasColumnType("text");

                    b.Property<string>("Token")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.WaitingEmailList", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Applications")
                        .HasColumnType("text");

                    b.Property<int>("CompanySize")
                        .HasColumnType("integer");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("TimeToLaunch")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("WaitingEmailLists");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.CRMComments", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("LeadId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("LeadId");

                    b.HasIndex("UserId");

                    b.ToTable("CRMComments");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Companies", b =>
                {
                    b.Property<Guid>("CompanyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("Size")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("VATReistration")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("CompanyId");

                    b.HasIndex("UserId");

                    b.ToTable("Companies");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.CompanyKycSignatory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CompanyKycId")
                        .HasColumnType("text");

                    b.Property<string>("DateUploaded")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryDate")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("InviteeName")
                        .HasColumnType("text");

                    b.Property<Guid>("KycId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("Role")
                        .HasColumnType("text");

                    b.Property<int>("SignatoryLevel")
                        .HasColumnType("integer");

                    b.Property<string>("SignatoryPicture")
                        .HasColumnType("text");

                    b.Property<int>("SignatoryPower")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CompanyKycId");

                    b.HasIndex("KycId");

                    b.ToTable("CompanyKycSignatories");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AnnualRevenue")
                        .HasColumnType("numeric");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("Identifier")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<int>("status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Deal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("text");

                    b.Property<string>("ContactFirstName")
                        .HasColumnType("text");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<string>("ContactLastName")
                        .HasColumnType("text");

                    b.Property<string>("ContactPhoneNumber")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("DealSize")
                        .HasColumnType("text");

                    b.Property<int>("DealStage")
                        .HasColumnType("integer");

                    b.Property<int?>("DealType")
                        .HasColumnType("integer");

                    b.Property<Guid?>("JobApplicationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("LeadId")
                        .HasColumnType("uuid");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("JobApplicationId");

                    b.HasIndex("LeadId");

                    b.HasIndex("UserId");

                    b.ToTable("Deals");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.DealActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DealId")
                        .HasColumnType("uuid");

                    b.Property<string>("Identifier")
                        .HasColumnType("text");

                    b.Property<string>("IdentifierTitle")
                        .HasColumnType("text");

                    b.Property<string>("PayloadBody")
                        .HasColumnType("text");

                    b.Property<string>("PayloadCompany")
                        .HasColumnType("text");

                    b.Property<string>("PayloadName")
                        .HasColumnType("text");

                    b.Property<string>("PayloadPhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("PayloadTitle")
                        .HasColumnType("text");

                    b.Property<string>("PayloadWebsite")
                        .HasColumnType("text");

                    b.Property<string>("body")
                        .HasColumnType("text");

                    b.Property<string>("email")
                        .HasColumnType("text");

                    b.Property<string>("title")
                        .HasColumnType("text");

                    b.Property<string>("type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DealId");

                    b.ToTable("DealActivity");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.DealEmail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DealId")
                        .HasColumnType("uuid");

                    b.Property<string>("Destination")
                        .HasColumnType("text");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DealId");

                    b.ToTable("DealEmail");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.DealsContact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("DealId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ContactId");

                    b.HasIndex("DealId");

                    b.ToTable("DealsContacts");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Kpi", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AnalysisFrequency")
                        .HasColumnType("text");

                    b.Property<bool>("CanMeasureKpi")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("KpiDepartmentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("KpiSourceId")
                        .HasColumnType("uuid");

                    b.Property<int>("Measurement")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<Guid?>("ProcessId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("KpiDepartmentId");

                    b.HasIndex("KpiSourceId");

                    b.HasIndex("ProcessId");

                    b.ToTable("Kpis");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiCollaborators", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("KpiId");

                    b.ToTable("KpiCollaborators");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiDepartment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("KpiDepartment");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiExpectedOutcome", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("KpiId")
                        .HasColumnType("uuid");

                    b.Property<double>("maximum")
                        .HasColumnType("double precision");

                    b.Property<double>("minimum")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("KpiId");

                    b.ToTable("KpiExpectedOutcome");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiProcess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("KpiProcess");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiSource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("KpiSource");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Lead", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccountName")
                        .HasColumnType("text");

                    b.Property<Guid?>("ContactId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<Guid?>("Lead")
                        .HasColumnType("uuid");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Lead");

                    b.HasIndex("UserId");

                    b.ToTable("Leads");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.LeadNTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("LeadId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TagId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("LeadId");

                    b.HasIndex("TagId");

                    b.ToTable("LeadNTag");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.LeadTransferHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("FromUserId")
                        .HasColumnType("text");

                    b.Property<Guid>("LeadId")
                        .HasColumnType("uuid");

                    b.Property<string>("ToUserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("LeadId");

                    b.ToTable("LeadTransferHistory");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Tag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Tags");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Actors", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("DateAdded")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<long?>("PhoneNumberId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProfilePicture")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PhoneNumberId");

                    b.ToTable("Actors");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMAudioRecording", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long>("ContactId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMAudioRecordings");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMBuisinessCard", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("BookingLink")
                        .HasColumnType("text");

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMBuisinessCards");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaign", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMCampaigns");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaignOnwer", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMCampaignId")
                        .HasColumnType("bigint");

                    b.Property<string>("OwnerName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMCampaignId")
                        .IsUnique();

                    b.ToTable("CRMCampaignOnwer");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaignSequence", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<long?>("CRMCampaignSequenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsCondition")
                        .HasColumnType("boolean");

                    b.Property<long?>("PreviousActionId")
                        .HasColumnType("bigint");

                    b.Property<bool>("PreviousActionRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMCampaignSequenceId");

                    b.ToTable("CRMCampaignSequences");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCollaborator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMMeetingId")
                        .HasColumnType("bigint");

                    b.Property<long?>("CRMTodoId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("TeamMemberId")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberName")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberProfilePic")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMMeetingId");

                    b.HasIndex("CRMTodoId");

                    b.ToTable("CRMCollaborator");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCompany", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<int?>("CompanySize")
                        .HasColumnType("integer");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<int?>("DealSize")
                        .HasColumnType("integer");

                    b.Property<string>("DealType")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsArchive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsConvertedToDeals")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("VATRegistration")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMCompanies");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCompanyCollaborator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMCompanyCollaborators");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCompanyTag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMCompanyTags");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMContact", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<long?>("CompanyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<long?>("DealReferenceId")
                        .HasColumnType("bigint");

                    b.Property<string>("Details")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<long?>("LeadReferenceId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMContacts");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMContactCollaborator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMContactCollaborator");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMContactOwner", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long>("ContactId")
                        .HasColumnType("bigint");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ContactId")
                        .IsUnique();

                    b.ToTable("CRMContactOwners");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMContactTag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMContactTags");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMDeal", b =>
                {
                    b.Property<long>("DealId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateConverted")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsConverted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<long?>("ReferenceId")
                        .HasColumnType("bigint");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("DealId");

                    b.ToTable("CRMDeals");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMDealCollaborator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMDealCollaborators");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMDealStatus", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMDealId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMDealId")
                        .IsUnique();

                    b.ToTable("CRMDealStatuses");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMEmail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<Guid?>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("RecipientEmail")
                        .HasColumnType("text");

                    b.Property<string>("SenderEmail")
                        .HasColumnType("text");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.ToTable("CRMEmail");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMEmailRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMCampaignId")
                        .HasColumnType("bigint");

                    b.Property<long>("CampaignId")
                        .HasColumnType("bigint");

                    b.Property<double>("Clicks")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<int>("EmailSent")
                        .HasColumnType("integer");

                    b.Property<double>("Interested")
                        .HasColumnType("double precision");

                    b.Property<double>("Replies")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("CRMCampaignId")
                        .IsUnique();

                    b.ToTable("CRMEmailRecords");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMEmailSequence", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Action")
                        .HasColumnType("text");

                    b.Property<string>("Condition")
                        .HasColumnType("text");

                    b.Property<string>("ConditionType")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCondition")
                        .HasColumnType("boolean");

                    b.Property<long?>("PreviousActionId")
                        .HasColumnType("bigint");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMEmailSequences");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpi", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("AnalysisFrequency")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Monitor")
                        .HasColumnType("boolean");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMKpis");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiActivities", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberId")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberProfilePic")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId");

                    b.ToTable("CRMKpiActivities");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiCollaborator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("TeamMemberEmail")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberId")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberName")
                        .HasColumnType("text");

                    b.Property<string>("TeamMemberProfilePic")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId");

                    b.ToTable("CRMKpiCollaborators");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiExpectedOutCome", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<double>("Max")
                        .HasColumnType("double precision");

                    b.Property<double>("Min")
                        .HasColumnType("double precision");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId");

                    b.ToTable("CRMKpiExpectedOutComes");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiIncentive", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<decimal>("Value")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId")
                        .IsUnique();

                    b.ToTable("CRMKpiIncentives");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiMeasurement", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<double>("Unit")
                        .HasColumnType("double precision");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId")
                        .IsUnique();

                    b.ToTable("CRMKpiMeasurements");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiProcess", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<int>("Achievement")
                        .HasColumnType("integer");

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<int>("Quantity")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId")
                        .IsUnique();

                    b.ToTable("CRMKpiProcesses");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiSource", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMKpiId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMKpiId")
                        .IsUnique();

                    b.ToTable("CRMKpiSources");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMLead", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMCampaignId")
                        .HasColumnType("bigint");

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<long?>("ContactReferenceId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DateConverted")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("EmployeeRole")
                        .HasColumnType("text");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsConvertedToDeals")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTransfered")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastContacted")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMCampaignId");

                    b.ToTable("CRMLeads");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMLeadCollaborator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMLeadCollaborators");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMLeadOwner", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long>("CRMLeadId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMLeadId")
                        .IsUnique();

                    b.ToTable("CRMLeadOwners");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMMeeting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Author")
                        .HasColumnType("text");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EndTime")
                        .HasColumnType("text");

                    b.Property<string>("ImageUrl")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("boolean");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("MeeetingLink")
                        .HasColumnType("text");

                    b.Property<string>("MeetingId")
                        .HasColumnType("text");

                    b.Property<string>("StartTime")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMMeetings");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMSequenceMail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<long>("CRMEmailSequenceId")
                        .HasColumnType("bigint");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMEmailSequenceId")
                        .IsUnique();

                    b.ToTable("CRMSequenceMails");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMSocialMedia", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMBuisinessCardId")
                        .HasColumnType("bigint");

                    b.Property<string>("Link")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMBuisinessCardId");

                    b.ToTable("CRMSocialMedias");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMSource", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("DatedCreated")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("URL")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMSources");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMTag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("DatedCreated")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMTags");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMTodo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("EndTime")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("StartTime")
                        .HasColumnType("text");

                    b.Property<string>("TaskPriority")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<DateTime>("TodoDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMTodos");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMWorkSchedule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMWorkSchedules");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Campaign", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Channels")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("NewOwnerId")
                        .HasColumnType("uuid");

                    b.Property<string>("OwnerId")
                        .HasColumnType("text");

                    b.Property<bool>("PremiumRequired")
                        .HasColumnType("boolean");

                    b.Property<bool>("ProfilePictureRequired")
                        .HasColumnType("boolean");

                    b.Property<Guid>("SequenceId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("TemplateId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("OwnerId");

                    b.HasIndex("SequenceId");

                    b.HasIndex("TemplateId");

                    b.ToTable("Campaigns");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CampaignsTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("SequenceId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SequenceId");

                    b.ToTable("CampaignsTemplates");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Capabilities", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<bool>("MMS")
                        .HasColumnType("boolean");

                    b.Property<bool>("SMS")
                        .HasColumnType("boolean");

                    b.Property<bool>("Voice")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("Capabilities");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.ContactNote", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMContactId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Detail")
                        .HasColumnType("text");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMContactId");

                    b.ToTable("ContactNotes");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CreditTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("PurchasedBy")
                        .HasColumnType("text");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("text");

                    b.Property<long?>("SimWalletId")
                        .HasColumnType("bigint");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Tenant")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<DateTime>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("TransactionId")
                        .HasColumnType("text");

                    b.Property<string>("TransactionType")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("SimWalletId");

                    b.ToTable("CreditTransactions");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.EmailSignature", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<string>("JobTitle")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("Website")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmailSignatures");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.EmailTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Template")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmailTemplates");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Event", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Channel")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Events");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.EventNode", b =>
                {
                    b.Property<Guid>("ParentNodeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("NodeName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("ParentNodeId");

                    b.ToTable("EventNodes");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.IncomingSms", b =>
                {
                    b.Property<string>("MessageSid")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateSent")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("IncomingMessage")
                        .HasColumnType("text");

                    b.Property<string>("MessageStatus")
                        .HasColumnType("text");

                    b.Property<string>("ResposeMessage")
                        .HasColumnType("text");

                    b.Property<string>("SenderNumber")
                        .HasColumnType("text");

                    b.Property<string>("TwilioPhoneNumber")
                        .HasColumnType("text");

                    b.HasKey("MessageSid");

                    b.ToTable("IncomingSms");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.NoteOwner", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("NoteOwners");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.NoteUpload", b =>
                {
                    b.Property<long>("NoteUploadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long>("CRMContactId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ContactNoteId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.HasKey("NoteUploadId");

                    b.HasIndex("ContactNoteId");

                    b.ToTable("NoteUploads");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.OutgoingSms", b =>
                {
                    b.Property<string>("MessageSid")
                        .HasColumnType("text");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateResponded")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateSent")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsWhatsapp")
                        .HasColumnType("boolean");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("MessageResponse")
                        .HasColumnType("text");

                    b.Property<string>("MessageStatus")
                        .HasColumnType("text");

                    b.Property<string>("RecieverNumber")
                        .HasColumnType("text");

                    b.Property<Guid>("SequenceId")
                        .HasColumnType("uuid");

                    b.Property<string>("TwilioPhoneNumber")
                        .HasColumnType("text");

                    b.HasKey("MessageSid");

                    b.HasIndex("CampaignId");

                    b.HasIndex("SequenceId");

                    b.ToTable("OutgoingSms");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.PhoneNumber", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CapabilitiesId")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CountryName")
                        .HasColumnType("text");

                    b.Property<DateTime>("DatePurchased")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExpired")
                        .HasColumnType("boolean");

                    b.Property<string>("PhoneNo")
                        .HasColumnType("text");

                    b.Property<decimal>("Price")
                        .HasColumnType("numeric");

                    b.Property<string>("PurchaseBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CapabilitiesId");

                    b.ToTable("CRMPhoneNumbers");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.PhoneNumberTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<DateTime>("DatePurchased")
                        .HasColumnType("timestamp");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("PurchasedBy")
                        .HasColumnType("text");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("PhoneNumberTransactions");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.ScheduledAction", b =>
                {
                    b.Property<Guid>("NodeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("CurrentlyExecuting")
                        .HasColumnType("boolean");

                    b.Property<string>("Delay")
                        .HasColumnType("text");

                    b.Property<int>("ExecutionNo")
                        .HasColumnType("integer");

                    b.Property<int>("Node")
                        .HasColumnType("integer");

                    b.Property<int>("ParentNode")
                        .HasColumnType("integer");

                    b.Property<bool>("Response")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ScheduleActionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<bool>("TiedToOutcome")
                        .HasColumnType("boolean");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("NodeId");

                    b.ToTable("ScheduledActions");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.ScheduledActionNodes", b =>
                {
                    b.Property<Guid>("NodeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("ScheduleActionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("NodeId");

                    b.ToTable("ScheduledActionNodes");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Sequence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<bool>("SaveHasTemplate")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ScheduledActionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("Sequences");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.SimSettings", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<decimal>("CallCharges")
                        .HasColumnType("numeric");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<decimal>("SMSCost")
                        .HasColumnType("numeric");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("SimSettings");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.SimWallet", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("WalletId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("SimWallets");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Subscriber", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CampaignId")
                        .HasColumnType("uuid");

                    b.Property<int>("Channels")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastUpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CampaignId");

                    b.ToTable("Subscriber");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.VoiceNoteUpload", b =>
                {
                    b.Property<long>("VoiceNoteUploadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long>("CRMContactId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ContactNoteId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("FilePath")
                        .HasColumnType("text");

                    b.HasKey("VoiceNoteUploadId");

                    b.HasIndex("ContactNoteId");

                    b.ToTable("VoiceNoteUploads");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.WorkingDay", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<long?>("CRMWorkScheduleId")
                        .HasColumnType("bigint");

                    b.Property<string>("DayOfTheWeek")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CRMWorkScheduleId");

                    b.ToTable("WorkingDays");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.WorkingHour", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("EndTime")
                        .HasColumnType("text");

                    b.Property<string>("StartTime")
                        .HasColumnType("text");

                    b.Property<long?>("WorkingDayId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("WorkingDayId");

                    b.ToTable("WorkingHours");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMBillingAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("StreetName")
                        .HasColumnType("text");

                    b.Property<string>("StreetNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMBillingAddresses");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMPackagePricing", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<double?>("PricePerMonth")
                        .HasColumnType("double precision");

                    b.Property<double?>("PricePerMonthForYearlyOption")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("CRMPackagePricings");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMPricingAndFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DurationType")
                        .HasColumnType("text");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLimited")
                        .HasColumnType("boolean");

                    b.Property<string>("LimitedTo")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("CRMPricingAndFeatures");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMPricingPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMPricingPlans");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMStripeSubscriptionDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("Plan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PriceId")
                        .HasColumnType("text");

                    b.Property<string>("ProductId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CRMStripeSubscriptionDetails");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RetrySubAttempt")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("CRMSubscriptions");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CandidateSummary", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("CandidateSummary");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.Categories", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<bool>("IsPredefined")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKYC", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("BusinessName")
                        .HasColumnType("text");

                    b.Property<string>("CompanyDetailsDynamicFieldsJson")
                        .HasColumnType("text");

                    b.Property<string>("CompanyEmail")
                        .HasColumnType("text");

                    b.Property<string>("CompanyLogo")
                        .HasColumnType("text");

                    b.Property<string>("DateCreated")
                        .HasColumnType("text");

                    b.Property<int>("Dispute")
                        .HasColumnType("integer");

                    b.Property<string>("ExpiredDate")
                        .HasColumnType("text");

                    b.Property<string>("HighTrustDynamicFieldsJson")
                        .HasColumnType("text");

                    b.Property<string>("IndustryType")
                        .HasColumnType("text");

                    b.Property<string>("InviteeName")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsInvited")
                        .HasColumnType("boolean");

                    b.Property<string>("LastUpdate")
                        .HasColumnType("text");

                    b.Property<string>("LegalFormEntity")
                        .HasColumnType("text");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("RegisteredName")
                        .HasColumnType("text");

                    b.Property<string>("RegistrationNumber")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Tag")
                        .HasColumnType("integer");

                    b.Property<string>("TradeName")
                        .HasColumnType("text");

                    b.Property<string>("VATNumber")
                        .HasColumnType("text");

                    b.Property<int>("VerificationLevel")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("CompaniesKYCs");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycAddress", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CompanyBranchAddress")
                        .HasColumnType("text");

                    b.Property<string>("CompanyBranchCity")
                        .HasColumnType("text");

                    b.Property<string>("CompanyBranchCountry")
                        .HasColumnType("text");

                    b.Property<string>("CompanyBranchPostalCode")
                        .HasColumnType("text");

                    b.Property<string>("CompanyBranchState")
                        .HasColumnType("text");

                    b.Property<string>("CompanyHeadquarterAddress")
                        .HasColumnType("text");

                    b.Property<string>("CompanyHeadquarterCity")
                        .HasColumnType("text");

                    b.Property<string>("CompanyHeadquarterCountry")
                        .HasColumnType("text");

                    b.Property<string>("CompanyHeadquarterPostalCode")
                        .HasColumnType("text");

                    b.Property<string>("CompanyHeadquarterState")
                        .HasColumnType("text");

                    b.Property<string>("CompanyId")
                        .HasColumnType("text");

                    b.Property<string>("CompanyKycId")
                        .HasColumnType("text");

                    b.Property<bool>("IsCompanyTheHeadQuarter")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("CompanyKycId")
                        .IsUnique();

                    b.ToTable("CompanyKycAddresses");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycCategories", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CategoryId")
                        .HasColumnType("text");

                    b.Property<string>("CompanyKycId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CompanyKycId");

                    b.ToTable("CompanyKycCategories");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycDataClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CompanyKycId")
                        .HasColumnType("text");

                    b.Property<string>("DataClassId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyKycId");

                    b.HasIndex("DataClassId");

                    b.ToTable("CompanyKycDataClasses");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycDocument", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CompanyKycId")
                        .HasColumnType("text");

                    b.Property<string>("DocumentFilePath")
                        .HasColumnType("text");

                    b.Property<string>("DocumentName")
                        .HasColumnType("text");

                    b.Property<string>("DocumentUploadDate")
                        .HasColumnType("text");

                    b.Property<bool>("IsDocumentSubmitted")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("CompanyKycId");

                    b.ToTable("CompanyKycDocuments");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycDynamicField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("DataType")
                        .HasColumnType("text");

                    b.Property<string>("FieldName")
                        .HasColumnType("text");

                    b.Property<int>("FieldType")
                        .HasColumnType("integer");

                    b.Property<int>("TrustLevel")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("CompanyKycDynamicFields");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycUBO", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CompanyKycId")
                        .HasColumnType("text");

                    b.Property<string>("DateUploaded")
                        .HasColumnType("text");

                    b.Property<string>("Equity")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryDate")
                        .HasColumnType("text");

                    b.Property<string>("Location")
                        .HasColumnType("text");

                    b.Property<string>("Role")
                        .HasColumnType("text");

                    b.Property<int>("SignatoryLevel")
                        .HasColumnType("integer");

                    b.Property<string>("UBODynamicFieldsJson")
                        .HasColumnType("text");

                    b.Property<string>("UboDateOfBirth")
                        .HasColumnType("text");

                    b.Property<string>("UboFirstName")
                        .HasColumnType("text");

                    b.Property<string>("UboLastName")
                        .HasColumnType("text");

                    b.Property<string>("UboMiddleName")
                        .HasColumnType("text");

                    b.Property<string>("UboNationality")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyKycId");

                    b.ToTable("CompanyKycUBOs");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.DataClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("DataClasses");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.DataClassCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CategoryId")
                        .HasColumnType("text");

                    b.Property<string>("DataClassId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("DataClassId");

                    b.ToTable("DataClassCategories");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.EmployeeKycDynamicField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("DataType")
                        .HasColumnType("text");

                    b.Property<string>("FieldName")
                        .HasColumnType("text");

                    b.Property<int>("FieldType")
                        .HasColumnType("integer");

                    b.Property<int>("TrustLevel")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("EmployeeKycDynamicFields");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.InterviewStage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("InterviewDate")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("JobVacancyId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobVacancyId");

                    b.ToTable("InterviewStage");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobApplication", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApplicationDate")
                        .HasColumnType("text");

                    b.Property<string>("AverageScore")
                        .HasColumnType("text");

                    b.Property<string>("CandidateId")
                        .HasColumnType("text");

                    b.Property<string>("InternalVacancyProcessId")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("ScoreCardAssessorName")
                        .HasColumnType("text");

                    b.Property<string>("ScoreCardAssessorSignature")
                        .HasColumnType("text");

                    b.Property<string>("ScoreCardAverageScore")
                        .HasColumnType("text");

                    b.Property<string>("ScoreCardFinalComment")
                        .HasColumnType("text");

                    b.Property<bool>("ScoreCardRecommended")
                        .HasColumnType("boolean");

                    b.Property<string>("ScoreCardUrl")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("VacancyId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CandidateId");

                    b.HasIndex("VacancyId");

                    b.ToTable("JobApplication");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("InputType")
                        .HasColumnType("text");

                    b.Property<Guid>("JobVacancyId")
                        .HasColumnType("uuid");

                    b.Property<string>("Question")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobVacancyId");

                    b.ToTable("JobQuestion");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobSkill", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("JobVacancyId")
                        .HasColumnType("uuid");

                    b.Property<string>("SkillName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobVacancyId");

                    b.ToTable("JobSkill");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobVacancy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .HasColumnType("text");

                    b.Property<string>("Certification")
                        .HasColumnType("text");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid");

                    b.Property<string>("ContactDetail")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("DealId")
                        .HasColumnType("text");

                    b.Property<string>("EndDate")
                        .HasColumnType("text");

                    b.Property<string>("HourlyRate")
                        .HasColumnType("text");

                    b.Property<string>("HoursAWeek")
                        .HasColumnType("text");

                    b.Property<int>("InterviewStageType")
                        .HasColumnType("integer");

                    b.Property<bool>("IsClosed")
                        .HasColumnType("boolean");

                    b.Property<string>("JobCategory")
                        .HasColumnType("text");

                    b.Property<string>("JobDescription")
                        .HasColumnType("text");

                    b.Property<string>("JobEffective")
                        .HasColumnType("text");

                    b.Property<string>("JobExperience")
                        .HasColumnType("text");

                    b.Property<string>("JobExpire")
                        .HasColumnType("text");

                    b.Property<string>("JobLocation")
                        .HasColumnType("text");

                    b.Property<string>("JobTitle")
                        .HasColumnType("text");

                    b.Property<string>("JobType")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("LocationId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("NationalLaguageId")
                        .HasColumnType("uuid");

                    b.Property<bool>("PublishJob")
                        .HasColumnType("boolean");

                    b.Property<string>("RecruisitionStatus")
                        .HasColumnType("text");

                    b.Property<string>("Recruiter")
                        .HasColumnType("text");

                    b.Property<string>("SalaryRange")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("LocationId");

                    b.HasIndex("NationalLaguageId");

                    b.HasIndex("UserId");

                    b.ToTable("JobVacancy");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KYC", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AMLVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("AMLVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("AddressFile")
                        .HasColumnType("text");

                    b.Property<bool>("AddressVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("AddressVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("BVN")
                        .HasColumnType("text");

                    b.Property<bool>("BVNVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("BVNVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Contract")
                        .HasColumnType("text");

                    b.Property<bool>("DOBVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("DOBVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("DateCreated")
                        .HasColumnType("text");

                    b.Property<int>("Disputes")
                        .HasColumnType("integer");

                    b.Property<string>("DriversLicenceFile")
                        .HasColumnType("text");

                    b.Property<string>("DriversLicenceFileBack")
                        .HasColumnType("text");

                    b.Property<string>("DriversLicenceIdValue")
                        .HasColumnType("text");

                    b.Property<bool>("DriversLicenceVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("DriversLicenceVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("EmailVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("EmailVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmployeeCompanyName")
                        .HasColumnType("text");

                    b.Property<string>("EmployeeEmail")
                        .HasColumnType("text");

                    b.Property<int>("EmployeeKycStatus")
                        .HasColumnType("integer");

                    b.Property<string>("ExpiringDate")
                        .HasColumnType("text");

                    b.Property<string>("FaceRecognitionFile")
                        .HasColumnType("text");

                    b.Property<bool>("FaceRecognitionVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("FaceRecognitionVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("HighTrustDynamicFieldsJson")
                        .HasColumnType("text");

                    b.Property<bool>("InviteEmployee")
                        .HasColumnType("boolean");

                    b.Property<string>("JobRole")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("MidTrustDynamicFieldsJson")
                        .HasColumnType("text");

                    b.Property<bool>("NFCVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("NFCVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("NINFile")
                        .HasColumnType("text");

                    b.Property<string>("NINFileBack")
                        .HasColumnType("text");

                    b.Property<bool>("NINVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("NINVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Nationality")
                        .HasColumnType("text");

                    b.Property<string>("NinIdValue")
                        .HasColumnType("text");

                    b.Property<bool>("PEPVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PEPVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("PassportFile")
                        .HasColumnType("text");

                    b.Property<string>("PassportIdValue")
                        .HasColumnType("text");

                    b.Property<bool>("PassportVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PassportVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("PhoneVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PhoneVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("text");

                    b.Property<string>("Tag")
                        .HasColumnType("text");

                    b.Property<int>("TrustLevel")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.Property<bool>("WatchListVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("WatchListVerifiedDate")
                        .HasColumnType("text");

                    b.Property<string>("kycStatus")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("UserProfileId")
                        .IsUnique();

                    b.ToTable("KYCs");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KycCategories", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CategoryId")
                        .HasColumnType("text");

                    b.Property<Guid>("KycId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("KycId");

                    b.ToTable("KycCategories");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KycDataClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("DataClassId")
                        .HasColumnType("text");

                    b.Property<Guid>("KycId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DataClassId");

                    b.HasIndex("KycId");

                    b.ToTable("KycDataClasses");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KycEmployeeGuarantor", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("AddressFile")
                        .HasColumnType("text");

                    b.Property<bool>("AddressVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("AddressVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("DateOfBirth")
                        .HasColumnType("text");

                    b.Property<string>("DriversLicenceFile")
                        .HasColumnType("text");

                    b.Property<bool>("DriversLicenceVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("DriversLicenceVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("ExpiringDate")
                        .HasColumnType("text");

                    b.Property<string>("FaceRecognitionFile")
                        .HasColumnType("text");

                    b.Property<bool>("FaceRecognitionVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("FaceRecognitionVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("Gender")
                        .HasColumnType("text");

                    b.Property<string>("GuarantorEmail")
                        .HasColumnType("text");

                    b.Property<int>("GuarantorStatus")
                        .HasColumnType("integer");

                    b.Property<bool>("IsInvited")
                        .HasColumnType("boolean");

                    b.Property<Guid>("KycId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("NINFile")
                        .HasColumnType("text");

                    b.Property<bool>("NINVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("NINVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Nationality")
                        .HasColumnType("text");

                    b.Property<string>("OtherName")
                        .HasColumnType("text");

                    b.Property<string>("PassportFile")
                        .HasColumnType("text");

                    b.Property<bool>("PassportVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PassportVerifiedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("PhoneNo")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("Relationship")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("ZeroTrustDynamicFieldsJson")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("KycId");

                    b.ToTable("KycEmployeeGuarantors");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.Profile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AcademicAndProfessionalCertificateUrls")
                        .HasColumnType("text");

                    b.Property<string>("AddressDocumentUrl")
                        .HasColumnType("text");

                    b.Property<string>("CV_URL")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("GithubPlatform")
                        .HasColumnType("text");

                    b.Property<string>("Guarantor1_FullName")
                        .HasColumnType("text");

                    b.Property<string>("Guarantor2_EmailAddress")
                        .HasColumnType("text");

                    b.Property<string>("Guarantor2_Fullname")
                        .HasColumnType("text");

                    b.Property<string>("Guarantor2_PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("Guarantor2_RelationshipToKin")
                        .HasColumnType("text");

                    b.Property<string>("GuarantorI_EmailAddress")
                        .HasColumnType("text");

                    b.Property<string>("GuarantorI_Phone")
                        .HasColumnType("text");

                    b.Property<string>("GuarantorI_RelationshipToKin")
                        .HasColumnType("text");

                    b.Property<string>("IdDocumentType")
                        .HasColumnType("text");

                    b.Property<string>("IdDocumentUrl")
                        .HasColumnType("text");

                    b.Property<bool>("IsAddressVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGuarantorsVerified")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("LinkedinPlatform")
                        .HasColumnType("text");

                    b.Property<string>("PortfolioPlatform")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("Profession")
                        .HasColumnType("text");

                    b.Property<string>("SalaryExpectationPerHour")
                        .HasColumnType("text");

                    b.Property<string>("SelfietUrl")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Street")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("YearsOfExperience")
                        .HasColumnType("text");

                    b.Property<bool>("isGovernmentIdDocumentVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("isPoofOfExpertiseVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("isSelfieVerified")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Profiles");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.QuestionOption", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("JobQuestionId")
                        .HasColumnType("uuid");

                    b.Property<string>("OptionName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobQuestionId");

                    b.ToTable("QuestionOption");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.SalePerson", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<Guid>("JobVacancyId")
                        .HasColumnType("uuid");

                    b.Property<string>("Lastname")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobVacancyId")
                        .IsUnique();

                    b.ToTable("SalePerson");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.AutoSaveSetting", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<bool>("All")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("DateStarted")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Deposit")
                        .HasColumnType("boolean");

                    b.Property<double>("InterestRatePA")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("MaturityDate")
                        .HasColumnType("timestamp");

                    b.Property<int>("PercentageToSave")
                        .HasColumnType("integer");

                    b.Property<bool>("Salary")
                        .HasColumnType("boolean");

                    b.Property<bool>("Transfer")
                        .HasColumnType("boolean");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AutoSaveSettings");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.AutoSaveTransaction", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<long>("Amount")
                        .HasColumnType("bigint");

                    b.Property<string>("AutoSaveSettingId")
                        .HasColumnType("text");

                    b.Property<DateTime>("TransDate")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("AutoSaveSettingId");

                    b.ToTable("AutoSaveTransactions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Beneficiary", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("BatchId")
                        .HasColumnType("text");

                    b.Property<string>("BenJobPaysAccountId")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<string>("InstrumentId")
                        .HasColumnType("text");

                    b.Property<string>("WeavrBeneficiaryId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BatchId");

                    b.ToTable("Beneficiaries");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.BeneficiaryBatch", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("JobPaysAcctId")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("WeavrBatchId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobPaysAcctId");

                    b.ToTable("BeneficiaryBatches");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.BridgeCardTransactionHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("WalletId")
                        .HasColumnType("text");

                    b.Property<string>("bridgecard_transaction_reference")
                        .HasColumnType("text");

                    b.Property<string>("card_id")
                        .HasColumnType("text");

                    b.Property<string>("card_transaction_type")
                        .HasColumnType("text");

                    b.Property<string>("cardholder_id")
                        .HasColumnType("text");

                    b.Property<string>("client_transaction_reference")
                        .HasColumnType("text");

                    b.Property<string>("currency")
                        .HasColumnType("text");

                    b.Property<string>("description")
                        .HasColumnType("text");

                    b.Property<string>("interchange_revenue")
                        .HasColumnType("text");

                    b.Property<string>("interchange_revenue_refund")
                        .HasColumnType("text");

                    b.Property<bool>("is_recurring")
                        .HasColumnType("boolean");

                    b.Property<string>("issuing_app_id")
                        .HasColumnType("text");

                    b.Property<bool>("livemode")
                        .HasColumnType("boolean");

                    b.Property<string>("merchant_category_code")
                        .HasColumnType("text");

                    b.Property<string>("merchant_city")
                        .HasColumnType("text");

                    b.Property<string>("merchant_code")
                        .HasColumnType("text");

                    b.Property<string>("merchant_logo")
                        .HasColumnType("text");

                    b.Property<string>("merchant_name")
                        .HasColumnType("text");

                    b.Property<string>("merchant_website")
                        .HasColumnType("text");

                    b.Property<string>("partner_interchange_fee")
                        .HasColumnType("text");

                    b.Property<string>("partner_interchange_fee_refund")
                        .HasColumnType("text");

                    b.Property<string>("transaction_category")
                        .HasColumnType("text");

                    b.Property<DateTime>("transaction_date")
                        .HasColumnType("timestamp");

                    b.Property<string>("transaction_group")
                        .HasColumnType("text");

                    b.Property<long>("transaction_timestamp")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("WalletId");

                    b.ToTable("BridgeCardTransactionHistories");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Card", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<long>("AvailableToSpend")
                        .HasColumnType("bigint");

                    b.Property<string>("CardBrand")
                        .HasColumnType("text");

                    b.Property<string>("CardLevelClassification")
                        .HasColumnType("text");

                    b.Property<string>("CardNumber")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberFirstSix")
                        .HasColumnType("text");

                    b.Property<string>("CardNumberLastFour")
                        .HasColumnType("text");

                    b.Property<string>("CardholderMobileNumber")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Cvv")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryMmyy")
                        .HasColumnType("text");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("ManagedAccountId")
                        .HasColumnType("text");

                    b.Property<string>("Mode")
                        .HasColumnType("text");

                    b.Property<string>("NameOnCard")
                        .HasColumnType("text");

                    b.Property<string>("NameOnCardLine2")
                        .HasColumnType("text");

                    b.Property<bool>("PendingActivation")
                        .HasColumnType("boolean");

                    b.Property<bool>("PinBlocked")
                        .HasColumnType("boolean");

                    b.Property<int>("PrepaidAactualBalance")
                        .HasColumnType("integer");

                    b.Property<int>("PrepaidAvailableBalance")
                        .HasColumnType("integer");

                    b.Property<string>("RenewalType")
                        .HasColumnType("text");

                    b.Property<string>("StartMmyy")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("WeavrCardId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ManagedAccountId");

                    b.ToTable("Cards");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysAccount", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccountClassId")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<long?>("GrossPay")
                        .HasColumnType("bigint");

                    b.Property<string>("LoginPin")
                        .HasColumnType("text");

                    b.Property<long?>("NetPay")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysAccounts");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysAccountClass", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysAccountClasses");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysAppSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("AppName")
                        .HasColumnType("text");

                    b.Property<string>("ContentType")
                        .HasColumnType("text");

                    b.Property<string>("FileName")
                        .HasColumnType("text");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.Property<string>("PropertyName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysAppSettings");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysBillingAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("StreetName")
                        .HasColumnType("text");

                    b.Property<string>("StreetNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysBillingAddresses");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysCard", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal>("AvailableBalance")
                        .HasColumnType("numeric(18,2)");

                    b.Property<decimal>("Balance")
                        .HasColumnType("numeric(18,2)");

                    b.Property<bool>("BlockedDueToFraud")
                        .HasColumnType("boolean");

                    b.Property<decimal>("BookBalance")
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("Brand")
                        .HasColumnType("text");

                    b.Property<string>("CardCurrency")
                        .HasColumnType("text");

                    b.Property<string>("CardId")
                        .HasColumnType("text");

                    b.Property<string>("CardNumber")
                        .HasColumnType("text");

                    b.Property<string>("CardType")
                        .HasColumnType("text");

                    b.Property<string>("CardholderIdd")
                        .HasColumnType("text");

                    b.Property<long>("CreatedAt")
                        .HasColumnType("bigint");

                    b.Property<string>("Cvv")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryMonth")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryYear")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("IssuingAppId")
                        .HasColumnType("text");

                    b.Property<string>("Last4")
                        .HasColumnType("text");

                    b.Property<bool>("LiveMode")
                        .HasColumnType("boolean");

                    b.Property<string>("NardName")
                        .HasColumnType("text");

                    b.Property<bool>("Pin3dsActivated")
                        .HasColumnType("boolean");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("WalletId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("WalletId");

                    b.ToTable("JobPaysCards");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysClientCompany", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("BusinessName")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("ClientId")
                        .HasColumnType("text");

                    b.Property<int>("ClientType")
                        .HasColumnType("integer");

                    b.Property<string>("ContactFirstName")
                        .HasColumnType("text");

                    b.Property<string>("ContactLastName")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<string>("RcNumber")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.Property<string>("Vat")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysClientCompanies");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysClientCompanyAddress", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("ClientCompanyId")
                        .HasColumnType("text");

                    b.Property<string>("CompanyClientId")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Street")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ClientCompanyId");

                    b.ToTable("JobPaysClientCompanyAddresses");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysCompanySubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TenantId");

                    b.ToTable("JobPaysCompanySubscriptions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysEnterpriseSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ActivityLogHistoryLimit")
                        .HasColumnType("integer");

                    b.Property<double>("AmountPaid")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<int>("CalenderLimit")
                        .HasColumnType("integer");

                    b.Property<int>("InternalCommunicationHistoryLimit")
                        .HasColumnType("integer");

                    b.Property<string>("PlanId")
                        .HasColumnType("text");

                    b.Property<int>("ProjectLimit")
                        .HasColumnType("integer");

                    b.Property<int>("StorageLimit")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TimeSheetManagement")
                        .HasColumnType("text");

                    b.Property<int>("UsersLimit")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TenantId");

                    b.ToTable("JobPaysEnterpriseSubscriptions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("FeatureName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysFeature");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysInvite", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccountClassId")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<decimal>("GrossPay")
                        .HasColumnType("numeric");

                    b.Property<string>("InviteCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<decimal>("NetPay")
                        .HasColumnType("numeric");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysInvite");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysPackagePricing", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<double?>("PricePerMonth")
                        .HasColumnType("double precision");

                    b.Property<double?>("PricePerMonthForYearlyOption")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("JobPaysPackagePricing");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysPricingAndFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DurationType")
                        .HasColumnType("text");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsLimited")
                        .HasColumnType("boolean");

                    b.Property<string>("LimitedTo")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("JobPaysPricingAndFeatures");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysPricingPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysPricingPlans");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysStripeSubscriptionDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("Plan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PriceId")
                        .HasColumnType("text");

                    b.Property<string>("ProductId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobPaysStripeSubscriptionDetails");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("FreeTrialOptionSelected")
                        .HasColumnType("boolean");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RetrySubAttempt")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int>("SubscriptionCount")
                        .HasColumnType("integer");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("JobPaysSubscriptions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysSubscriptionHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("JobPaysSubscriptionHistory");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Module", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ClassId")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ClassId");

                    b.ToTable("Module");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.NFC", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<Guid?>("KycId")
                        .HasColumnType("uuid");

                    b.Property<string>("com")
                        .HasColumnType("text");

                    b.Property<string>("cvca")
                        .HasColumnType("text");

                    b.Property<string>("dg1")
                        .HasColumnType("text");

                    b.Property<string>("dg14")
                        .HasColumnType("text");

                    b.Property<string>("dg15")
                        .HasColumnType("text");

                    b.Property<string>("dg2")
                        .HasColumnType("text");

                    b.Property<string>("dg5")
                        .HasColumnType("text");

                    b.Property<string>("dg7")
                        .HasColumnType("text");

                    b.Property<bool>("isAAPassed")
                        .HasColumnType("boolean");

                    b.Property<bool>("isAASupported")
                        .HasColumnType("boolean");

                    b.Property<bool>("isBACPassed")
                        .HasColumnType("boolean");

                    b.Property<bool>("isBACSupported")
                        .HasColumnType("boolean");

                    b.Property<bool>("isCAPassed")
                        .HasColumnType("boolean");

                    b.Property<bool>("isCASupported")
                        .HasColumnType("boolean");

                    b.Property<bool>("isSACPassed")
                        .HasColumnType("boolean");

                    b.Property<bool>("isSACSupported")
                        .HasColumnType("boolean");

                    b.Property<string>("mrz")
                        .HasColumnType("text");

                    b.Property<string>("sod")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("KycId")
                        .IsUnique();

                    b.ToTable("NFCs");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollRequest", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("ChargePercentage")
                        .HasColumnType("integer");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateAccepted")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("DateRequested")
                        .HasColumnType("timestamp");

                    b.Property<string>("EmployeeEmail")
                        .HasColumnType("text");

                    b.Property<string>("EmployeeFullName")
                        .HasColumnType("text");

                    b.Property<decimal>("Gross")
                        .HasColumnType("numeric");

                    b.Property<int>("Interval")
                        .HasColumnType("integer");

                    b.Property<decimal>("NetPay")
                        .HasColumnType("numeric");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.Property<string>("VendorId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("VendorId");

                    b.ToTable("PayrollRequests");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollService", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("PayrollVendorId")
                        .HasColumnType("text");

                    b.Property<double>("Percentage")
                        .HasColumnType("double precision");

                    b.Property<string>("ServiceName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PayrollVendorId");

                    b.ToTable("PayrollServices");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollTransaction", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<long>("Amount")
                        .HasColumnType("bigint");

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("PayrollRequestId")
                        .HasColumnType("text");

                    b.Property<int>("TransactionType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PayrollRequestId");

                    b.ToTable("PayrollTransactions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollVendor", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Bio")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("Logo")
                        .HasColumnType("text");

                    b.Property<string>("User")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("VendorWalletId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("VendorWalletId");

                    b.ToTable("PayrollVendors");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.TempUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<bool>("IsEmailVerified")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPhoneNumberVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("Phone")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TempUsers");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Transaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("Amount")
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("BankAddress")
                        .HasColumnType("text");

                    b.Property<string>("BankCountry")
                        .HasColumnType("text");

                    b.Property<string>("BankIdentifierCode")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("BeneficiaryAccount")
                        .HasColumnType("text");

                    b.Property<string>("BeneficiaryBank")
                        .HasColumnType("text");

                    b.Property<string>("BeneficiaryBankCode")
                        .HasColumnType("text");

                    b.Property<string>("BeneficiaryName")
                        .HasColumnType("text");

                    b.Property<string>("CancellationReason")
                        .HasColumnType("text");

                    b.Property<string>("ChallengeExemptionReason")
                        .HasColumnType("text");

                    b.Property<long>("CreationTimestamp")
                        .HasColumnType("bigint");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("ExecutionTimestamp")
                        .HasColumnType("text");

                    b.Property<string>("InitiatorId")
                        .HasColumnType("text");

                    b.Property<bool>("InvoiceUploaded")
                        .HasColumnType("boolean");

                    b.Property<string>("Narration")
                        .HasColumnType("text");

                    b.Property<string>("OutWireTranferType")
                        .HasColumnType("text");

                    b.Property<string>("ProviderType")
                        .HasColumnType("text");

                    b.Property<string>("Range")
                        .HasColumnType("text");

                    b.Property<string>("Reference")
                        .HasColumnType("text");

                    b.Property<string>("ScheduledTimestamp")
                        .HasColumnType("text");

                    b.Property<string>("SessionId")
                        .HasColumnType("text");

                    b.Property<string>("Signature")
                        .HasColumnType("text");

                    b.Property<string>("SortCode")
                        .HasColumnType("text");

                    b.Property<string>("SourceAccount")
                        .HasColumnType("text");

                    b.Property<string>("SourceAccountName")
                        .HasColumnType("text");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<DateTime>("TransDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<string>("TransactionType")
                        .HasColumnType("text");

                    b.Property<string>("VFDTxnId")
                        .HasColumnType("text");

                    b.Property<string>("WalletID")
                        .HasColumnType("text");

                    b.Property<string>("WeavrTransactionId")
                        .HasColumnType("text");

                    b.Property<string>("beneficiaryAddress")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("WalletID");

                    b.ToTable("Transactions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.UnregisteredUserCompany", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<string>("Industry")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UnregisteredUserCompanies");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.VendorWallet", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<decimal>("CurrentBalance")
                        .HasColumnType("numeric");

                    b.Property<string>("PayrollVendorId")
                        .HasColumnType("text");

                    b.Property<decimal>("TotalEarning")
                        .HasColumnType("numeric");

                    b.Property<decimal>("TotalTaxRemitance")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.HasIndex("PayrollVendorId");

                    b.ToTable("VendorWallets");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Wallet", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccountName")
                        .HasColumnType("text");

                    b.Property<string>("AcountNumber")
                        .HasColumnType("text");

                    b.Property<decimal>("Balance")
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("BankAddress")
                        .HasColumnType("text");

                    b.Property<string>("BankCode")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<string>("Bvn")
                        .HasColumnType("text");

                    b.Property<string>("CardHolderId")
                        .HasColumnType("text");

                    b.Property<string>("CbaCustomerID")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("DateUpdated")
                        .HasColumnType("timestamp");

                    b.Property<string>("IBanState")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIBANRequested")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsIdVerified")
                        .HasColumnType("boolean");

                    b.Property<string>("IssuingAppId")
                        .HasColumnType("text");

                    b.Property<string>("JobPaysAccountID")
                        .HasColumnType("text");

                    b.Property<string>("PaymentReference")
                        .HasColumnType("text");

                    b.Property<int>("Provider")
                        .HasColumnType("integer");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<int>("WalletType")
                        .HasColumnType("integer");

                    b.Property<string>("WeavrAccountId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobPaysAccountID");

                    b.ToTable("AccountWallets");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.WalletBeneficiary", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AccountName")
                        .HasColumnType("text");

                    b.Property<string>("AccountNumber")
                        .HasColumnType("text");

                    b.Property<string>("BankCode")
                        .HasColumnType("text");

                    b.Property<string>("BankName")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("WalletId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("WalletId");

                    b.ToTable("AccountWalletBeneficiaries");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.WeavrCorpKyc", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("CorporateEmail")
                        .HasColumnType("text");

                    b.Property<string>("CorporateId")
                        .HasColumnType("text");

                    b.Property<string>("Details")
                        .HasColumnType("text");

                    b.Property<string>("JobPaysAccountId")
                        .HasColumnType("text");

                    b.Property<string>("OngoingStatus")
                        .HasColumnType("text");

                    b.Property<string>("RejectionComment")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("JobPaysAccountId");

                    b.ToTable("weavrCorpKycs");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectPermission", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobProjectPermission");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectRoles", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("JobProjectRoles");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectRolesPermissions", b =>
                {
                    b.Property<string>("PermissionsId")
                        .HasColumnType("text");

                    b.Property<string>("RolesId")
                        .HasColumnType("text");

                    b.ToTable("JobProjectRolesPermissions");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CannotResceduleWithin")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("EmergencyHours")
                        .HasColumnType("boolean");

                    b.Property<bool>("OutOfOffice")
                        .HasColumnType("boolean");

                    b.Property<string>("OverDueWaitingPeriod")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<bool>("WorkingHours")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("JobProjectSettings");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectUserPermissions", b =>
                {
                    b.Property<string>("PermissionsId")
                        .HasColumnType("text");

                    b.Property<string>("UsersId")
                        .HasColumnType("text");

                    b.ToTable("JobProjectUserPermissions");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.JobProjectUserRoles", b =>
                {
                    b.Property<string>("RolesId")
                        .HasColumnType("text");

                    b.Property<string>("UsersId")
                        .HasColumnType("text");

                    b.ToTable("JobProjectUserRoles");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.TenantClient", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ClientName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TenantClients");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.TodoCustomFrequency", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("EndStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("EndsAfter")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EndsOn")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<int?>("RepeatCount")
                        .HasColumnType("integer");

                    b.Property<string>("RepeatEvery")
                        .HasColumnType("text");

                    b.Property<string>("RepeatOn")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.ToTable("TodoCustomFrequency");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.UserPerformanceActivityScoreView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndDateForTheWeek")
                        .HasColumnType("timestamp");

                    b.Property<double>("Friday")
                        .HasColumnType("double precision");

                    b.Property<string>("Industries")
                        .IsRequired()
                        .HasColumnType("varchar(24)");

                    b.Property<double>("Monday")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("StartDateForTheWeek")
                        .HasColumnType("timestamp");

                    b.Property<double>("Thursday")
                        .HasColumnType("double precision");

                    b.Property<double>("TotalPercentage")
                        .HasColumnType("double precision");

                    b.Property<double>("Tuesday")
                        .HasColumnType("double precision");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<double>("Wednesday")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.ToTable("PerformanceMetricAnalyses");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectMetricsView", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int>("Month")
                        .HasColumnType("integer");

                    b.Property<string>("SubDomain")
                        .HasColumnType("text");

                    b.Property<double>("TotalHours")
                        .HasColumnType("double precision");

                    b.Property<int>("TotalSprints")
                        .HasColumnType("integer");

                    b.Property<int>("TotalTodo")
                        .HasColumnType("integer");

                    b.Property<int>("Year")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("ProjectMetricsViews");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectSprintMemberId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric(18,4)");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<string>("ExternalMemberEmail")
                        .HasColumnType("text");

                    b.Property<string>("MemberId")
                        .HasColumnType("text");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ProjectSprintMemberIds");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectMgmt_TodoId")
                        .HasColumnType("uuid");

                    b.Property<string>("TagName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TenantId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TimeSheetId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectMgmt_ProjectId");

                    b.HasIndex("ProjectMgmt_TodoId");

                    b.HasIndex("TimeSheetId");

                    b.ToTable("ProjectTag");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTrigger", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("Email")
                        .HasColumnType("boolean");

                    b.Property<bool>("Notification")
                        .HasColumnType("boolean");

                    b.Property<string>("ParticipantsIds")
                        .HasColumnType("text");

                    b.Property<string>("ProjectMgmt_ProjectId")
                        .HasColumnType("text");

                    b.Property<string>("Reasons")
                        .HasColumnType("text");

                    b.Property<bool>("SMS")
                        .HasColumnType("boolean");

                    b.Property<string>("TriggerName")
                        .HasColumnType("text");

                    b.Property<int>("TriggerReason")
                        .HasColumnType("integer");

                    b.Property<DateTime>("TriggerTime")
                        .HasColumnType("timestamp");

                    b.Property<Guid?>("projectMgmt_ProjectProjectId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("projectMgmt_ProjectProjectId");

                    b.ToTable("ProjectTriggers");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.SprintProject", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<Guid>("ProjectMgmt_ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("SprintProjects");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TagId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("PrjectTagId")
                        .HasColumnType("text");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.Property<string>("TodoId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TagId");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.Team", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Teams");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TeamMembers", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("Role")
                        .HasColumnType("text");

                    b.Property<Guid>("TeamId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TeamId");

                    b.ToTable("TeamMembers");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ActualTimeSpent")
                        .HasColumnType("text");

                    b.Property<decimal?>("AmountPerHour")
                        .HasColumnType("numeric(18,4)");

                    b.Property<string>("AssignedTo")
                        .HasColumnType("text");

                    b.Property<string>("ClientName")
                        .HasColumnType("text");

                    b.Property<string>("Comments")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CompletionDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DateLogged")
                        .HasColumnType("timestamp");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Duration")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("IsArchived")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBillable")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<int?>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("ProjectMgmt_ProjectId")
                        .HasColumnType("text");

                    b.Property<string>("SprintId")
                        .HasColumnType("text");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Summary")
                        .HasColumnType("text");

                    b.Property<string>("TimeSpent")
                        .HasColumnType("text");

                    b.Property<string>("TodoName")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<Guid?>("projectMgmt_ProjectProjectId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("projectMgmt_ProjectProjectId");

                    b.ToTable("TimeSheet");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheetMemberId", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("MemberId")
                        .HasColumnType("text");

                    b.Property<string>("TimesheetId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("TimeSheetMemberIds");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoComments", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .HasColumnType("text");

                    b.Property<string>("CommentedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<Guid>("TodoId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("TodoId");

                    b.ToTable("TodoComments");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoOrder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("SprintId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<Guid>("TodoId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SprintId");

                    b.HasIndex("TodoId")
                        .IsUnique();

                    b.ToTable("TodoOrder");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SprintId")
                        .HasColumnType("uuid");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("SprintId");

                    b.ToTable("TodoStatus");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TriggerSequence", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<DateTime>("DateAndTime")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Email")
                        .HasColumnType("boolean");

                    b.Property<bool>("Notification")
                        .HasColumnType("boolean");

                    b.Property<Guid>("ProjectTriggerId")
                        .HasColumnType("uuid");

                    b.Property<bool>("SMS")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("ProjectTriggerId");

                    b.ToTable("TriggerSequence");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Event")
                        .HasColumnType("integer");

                    b.Property<string>("EventId")
                        .HasColumnType("text");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.UserNotification", b =>
                {
                    b.Property<string>("UserProfileId")
                        .HasColumnType("text");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Disappear")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRead")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("ReadOn")
                        .HasColumnType("timestamp");

                    b.HasKey("UserProfileId", "NotificationId");

                    b.HasIndex("NotificationId");

                    b.ToTable("UserNotifications");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AIAgent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Agent")
                        .HasColumnType("text");

                    b.Property<double>("AmountPerMonthPerUser")
                        .HasColumnType("double precision");

                    b.Property<double>("AmountPerYearPerUser")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.ToTable("AIAgents");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AISubscriptionDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Agent")
                        .HasColumnType("integer");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("NoOfUserSubscribedFor")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("AISubscriptionDetails");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AdditionalLiecense", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<bool>("Updated")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserIdsJson")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("AdditionalLiecenses");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.BillingAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<string>("PostalCode")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("StreetName")
                        .HasColumnType("text");

                    b.Property<string>("StreetNumber")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("BillingAddresses");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.ClientCards", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<string>("CardNumber")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CardNumberFirstSix")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CardNumberLastFour")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("CardType")
                        .HasColumnType("integer");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<bool>("Country")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Cvv")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<string>("ExpiryMmyy")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("State")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<bool>("ZipCode")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("ClientCards");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.CompanySubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int?>("AIAgent")
                        .HasColumnType("integer");

                    b.Property<int?>("Application")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TenantId");

                    b.ToTable("CompanySubscriptions");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterpriseSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("ActivityLogHistoryLimit")
                        .HasColumnType("integer");

                    b.Property<bool>("AiAssistant")
                        .HasColumnType("boolean");

                    b.Property<double>("AmountPaid")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<int>("CalenderLimit")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<int>("DataRetentionPeriodInMonth")
                        .HasColumnType("integer");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedOn")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("InternalCommunicationHistoryLimit")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PlanId")
                        .HasColumnType("text");

                    b.Property<int>("ProjectLimit")
                        .HasColumnType("integer");

                    b.Property<int>("StorageLimit")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<bool>("TimeSheetManagement")
                        .HasColumnType("boolean");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("UsersLimit")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TenantId");

                    b.ToTable("EnterpriseSubscriptions");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterprizeSubscriptionPayment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("CompanyEmail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<int>("Frequency")
                        .HasColumnType("integer");

                    b.Property<bool>("IsExistingUser")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("timestamp");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentLink")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("integer");

                    b.Property<bool>("PaymentUsed")
                        .HasColumnType("boolean");

                    b.Property<string>("PersonalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("EnterprizeSubscriptionPayments");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.Feature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("FeatureName")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Features");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.FeedbackReviewsAndRatings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("FeedBack")
                        .HasColumnType("text");

                    b.Property<int>("StarRating")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("FeedbackReviewsAndRatings");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.PackagePricing", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<double?>("PricePerMonth")
                        .HasColumnType("double precision");

                    b.Property<double?>("PricePerMonthForYearlyOption")
                        .HasColumnType("double precision");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int?>("Region")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.HasKey("Id");

                    b.HasIndex("PricingPlanId");

                    b.ToTable("PackagePricing");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.PricingPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("PricingPlans");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.StripeSubscriptionDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AIAgent")
                        .HasColumnType("boolean");

                    b.Property<string>("Application")
                        .HasColumnType("text");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<string>("Plan")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PriceId")
                        .HasColumnType("text");

                    b.Property<string>("ProductId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("StripeSubscriptionDetails");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("FreeTrialOptionSelected")
                        .HasColumnType("boolean");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<bool>("IsAISubscription")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RetrySubAttempt")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int>("SubscriptionCount")
                        .HasColumnType("integer");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.ToTable("Subscriptions");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.SubscriptionHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ActivatedOn")
                        .HasColumnType("timestamp");

                    b.Property<double>("Amount")
                        .HasColumnType("double precision");

                    b.Property<int>("Application")
                        .HasColumnType("integer");

                    b.Property<Guid?>("BillingAddressId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConsumerAccount")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp");

                    b.Property<int>("Currency")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpiresOn")
                        .HasColumnType("timestamp");

                    b.Property<bool>("FreeTrialOptionSelected")
                        .HasColumnType("boolean");

                    b.Property<string>("Interval")
                        .HasColumnType("text");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("boolean");

                    b.Property<string>("MandateId")
                        .HasColumnType("text");

                    b.Property<string>("MandateReference")
                        .HasColumnType("text");

                    b.Property<string>("MollieCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("PayPalEmail")
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<int?>("PaymentProvider")
                        .HasColumnType("integer");

                    b.Property<string>("PaypalBillingAgreementId")
                        .HasColumnType("text");

                    b.Property<Guid>("PricingPlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("RetrySubAttempt")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("StripeCustomerId")
                        .HasColumnType("text");

                    b.Property<string>("StripePriceId")
                        .HasColumnType("text");

                    b.Property<int>("SubscriptionCount")
                        .HasColumnType("integer");

                    b.Property<int?>("SubscriptionFor")
                        .HasColumnType("integer");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("text");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionCode")
                        .HasColumnType("text");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp");

                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BillingAddressId");

                    b.HasIndex("PricingPlanId");

                    b.HasIndex("TenantId");

                    b.ToTable("SubscriptionHistory");
                });

            modelBuilder.Entity("Jobid.App.Tenant.Model.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AdminId")
                        .HasColumnType("text");

                    b.Property<string>("CompanyAddress")
                        .HasColumnType("text");

                    b.Property<string>("CompanyName")
                        .HasColumnType("text");

                    b.Property<int>("CompanySize")
                        .HasColumnType("integer");

                    b.Property<string>("CompanyType")
                        .HasColumnType("text");

                    b.Property<string>("ContactNo")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("timestamp");

                    b.Property<string>("Industry")
                        .IsRequired()
                        .HasColumnType("varchar(24)");

                    b.Property<DateTime>("LastMigration")
                        .HasColumnType("timestamp");

                    b.Property<DateTime>("LastUpdate")
                        .HasColumnType("timestamp");

                    b.Property<string>("LogoUrl")
                        .HasColumnType("text");

                    b.Property<string>("RegNumber")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasColumnType("text");

                    b.Property<string>("Subdomain")
                        .HasColumnType("text");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<string>("VerifiedEmailDomain")
                        .HasColumnType("text");

                    b.Property<string>("WorkSpace")
                        .HasColumnType("text");

                    b.Property<bool>("isSchemaCreated")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("AdminId");

                    b.ToTable("Tenants");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("RoleId")
                        .HasColumnType("text");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("text");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens");
                });

            modelBuilder.Entity("CRMCompanyCRMCompanyCollaborator", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCompany", null)
                        .WithMany()
                        .HasForeignKey("CRMCompaniesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCompanyCollaborator", null)
                        .WithMany()
                        .HasForeignKey("CRMCompanyCollaboratorsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMCompanyCRMCompanyTag", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCompany", null)
                        .WithMany()
                        .HasForeignKey("CRMCompaniesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCompanyTag", null)
                        .WithMany()
                        .HasForeignKey("CompanyTagsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMContactCRMContactCollaborator", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMContactCollaborator", null)
                        .WithMany()
                        .HasForeignKey("CRMContactCollaboratorsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMContact", null)
                        .WithMany()
                        .HasForeignKey("CRMContactsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMContactCRMContactTag", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMContact", null)
                        .WithMany()
                        .HasForeignKey("CRMContactsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMContactTag", null)
                        .WithMany()
                        .HasForeignKey("TagsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMDealCRMDealCollaborator", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMDealCollaborator", null)
                        .WithMany()
                        .HasForeignKey("CRMDealCollaboratorsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMDeal", null)
                        .WithMany()
                        .HasForeignKey("CRMDealsDealId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMLeadCRMLeadCollaborator", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMLeadCollaborator", null)
                        .WithMany()
                        .HasForeignKey("CRMLeadCollaboratorsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMLead", null)
                        .WithMany()
                        .HasForeignKey("CRMLeadsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMLeadCRMTag", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMLead", null)
                        .WithMany()
                        .HasForeignKey("LeadsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMTag", null)
                        .WithMany()
                        .HasForeignKey("TagsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CRMTagCRMTodo", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMTag", null)
                        .WithMany()
                        .HasForeignKey("TagsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMTodo", null)
                        .WithMany()
                        .HasForeignKey("TodosId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ContactNoteNoteOwner", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.ContactNote", null)
                        .WithMany()
                        .HasForeignKey("ContactNotesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.NoteOwner", null)
                        .WithMany()
                        .HasForeignKey("NoteOwnersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.LogAttachment", b =>
                {
                    b.HasOne("Jobid.App.ActivityLog.Model.Activity", "Activity")
                        .WithMany("LogAttachments")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeeRolesPermission", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Model.EmployeePermission", "EmployeePermission")
                        .WithMany("EmployeeRolesPermissions")
                        .HasForeignKey("PermissionId");

                    b.HasOne("Jobid.App.AdminConsole.Model.EmployeeRoles", "EmployeeRoles")
                        .WithMany("EmployeeRolesPermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("EmployeePermission");

                    b.Navigation("EmployeeRoles");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedExternalMeeting", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderUpload", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "CalenderMeeting")
                        .WithMany()
                        .HasForeignKey("MeetingId");

                    b.HasOne("Jobid.App.Calender.Models.SubsequentMeeting", "SubsequentMeeting")
                        .WithMany()
                        .HasForeignKey("SubsequentMeetingId");

                    b.Navigation("CalenderMeeting");

                    b.Navigation("SubsequentMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomFrequency", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "CalenderMeeting")
                        .WithOne("CustomFrequency")
                        .HasForeignKey("Jobid.App.Calender.Models.CustomFrequency", "CalenderMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CalenderMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestion", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany("CustomQuestion")
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CustomQuestionAnswer", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.BookedExternalMeeting", "BookedExternalMeeting")
                        .WithMany("CustomQuestionAnswer")
                        .HasForeignKey("BookedExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BookedExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeeting", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeetingQuestion", "ExternalMeetingQuestion")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Calender.Models.PersonalSchedule", "PersonalSchedule")
                        .WithMany()
                        .HasForeignKey("PersonalScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeetingQuestion");

                    b.Navigation("PersonalSchedule");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeetingTimeManagement", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Calender.Models.PersonalSchedule", "PersonalSchedule")
                        .WithMany()
                        .HasForeignKey("PersonalScheduleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");

                    b.Navigation("PersonalSchedule");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.MeetingNote", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "Meeting")
                        .WithMany()
                        .HasForeignKey("MeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Calender.Models.SubsequentMeeting", "SubsequentMeeting")
                        .WithMany()
                        .HasForeignKey("SubsequentMeetingId");

                    b.Navigation("Meeting");

                    b.Navigation("SubsequentMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.PersonalSchedule", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId1");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.RoundRobinHostingOrder", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.ExternalMeeting", "ExternalMeeting")
                        .WithMany()
                        .HasForeignKey("ExternalMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExternalMeeting");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.SubsequentMeeting", b =>
                {
                    b.HasOne("Jobid.App.Calender.Models.CalenderMeeting", "CalenderMeeting")
                        .WithMany()
                        .HasForeignKey("CalenderMeetingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CalenderMeeting");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CRMNotification", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.CRMTasks", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Lead", "lead")
                        .WithMany()
                        .HasForeignKey("LeadId");

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("lead");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRoleRoleModule", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ClientRole", "ClientRole")
                        .WithMany("ClientRoleRoleModules")
                        .HasForeignKey("ClientRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.RoleModule", "RoleModule")
                        .WithMany("ClientRoleRoleModules")
                        .HasForeignKey("RoleModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClientRole");

                    b.Navigation("RoleModule");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Permission", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.RoleModule", "Rolemodule")
                        .WithMany("Permission")
                        .HasForeignKey("RoleModuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Rolemodule");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.PricingAndFeature", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Feature", "Feature")
                        .WithMany()
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Feature");

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectFile", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("ProjectMgmt_ProjectId");

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "projectMgmt_Todo")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "SprintProject")
                        .WithMany()
                        .HasForeignKey("SprintProjectId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.TimeSheet", "TimeSheet")
                        .WithMany("ProjectFiles")
                        .HasForeignKey("TimeSheetId");

                    b.Navigation("projectMgmt_Project");

                    b.Navigation("projectMgmt_Todo");

                    b.Navigation("SprintProject");

                    b.Navigation("TimeSheet");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Project", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Companies", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_ProjectUser", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("projectMgmt_ProjectUsers")
                        .HasForeignKey("ProjectMgmt_ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("projectMgmt_Project");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Todo", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("Todos")
                        .HasForeignKey("ProjectMgmt_ProjectId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "SprintProject")
                        .WithMany()
                        .HasForeignKey("SprintProjectId");

                    b.Navigation("projectMgmt_Project");

                    b.Navigation("SprintProject");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_TodoUser", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "ProjectMgmt_Todo")
                        .WithMany("ProjectMgmt_TodoUsers")
                        .HasForeignKey("ProjectMgmt_TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.SecDomain", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.TodoTimeSequence", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "ProjectMgmt_Todo")
                        .WithMany("TodoTimeSequence")
                        .HasForeignKey("ProjectMgmt_TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.User", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ClientRole", "ClientRole")
                        .WithMany("user")
                        .HasForeignKey("ClientRoleId");

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", null)
                        .WithMany("Members")
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.Navigation("ClientRole");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserAndRoleId", b =>
                {
                    b.HasOne("Jobid.App.AdminConsole.Model.EmployeeRoles", "EmployeeRoles")
                        .WithMany("UserEmployeeAppRole")
                        .HasForeignKey("RoleId");

                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany("UserEmployeeAppRole")
                        .HasForeignKey("UserId");

                    b.Navigation("EmployeeRoles");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserCompanies", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.User", "user")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("tenant");

                    b.Navigation("user");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserRefreshToken", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.CRMComments", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Lead", "lead")
                        .WithMany()
                        .HasForeignKey("LeadId");

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("lead");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Companies", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.CompanyKycSignatory", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.CompanyKYC", "CompanyKYC")
                        .WithMany("CompanyKycSignatory")
                        .HasForeignKey("CompanyKycId");

                    b.HasOne("Jobid.App.JobID.Models.KYC", "Kyc")
                        .WithMany()
                        .HasForeignKey("KycId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompanyKYC");

                    b.Navigation("Kyc");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Deal", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Companies", "Companies")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobID.Models.JobApplication", "JobApplication")
                        .WithMany()
                        .HasForeignKey("JobApplicationId");

                    b.HasOne("Jobid.App.JobCRM.Models.Lead", "Lead")
                        .WithMany()
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "user")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("Companies");

                    b.Navigation("JobApplication");

                    b.Navigation("Lead");

                    b.Navigation("user");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.DealActivity", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Deal", "deal")
                        .WithMany()
                        .HasForeignKey("DealId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("deal");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.DealEmail", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Deal", "deal")
                        .WithMany()
                        .HasForeignKey("DealId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("deal");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.DealsContact", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Contact", "Contact")
                        .WithMany()
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCRM.Models.Deal", "Deal")
                        .WithMany("Contacts")
                        .HasForeignKey("DealId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contact");

                    b.Navigation("Deal");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Kpi", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "userProfile")
                        .WithMany()
                        .HasForeignKey("CreatedBy");

                    b.HasOne("Jobid.App.JobCRM.Models.KpiDepartment", "KpiDepartment")
                        .WithMany()
                        .HasForeignKey("KpiDepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCRM.Models.KpiSource", "kpiSource")
                        .WithMany()
                        .HasForeignKey("KpiSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCRM.Models.KpiProcess", "Process")
                        .WithMany()
                        .HasForeignKey("ProcessId");

                    b.Navigation("KpiDepartment");

                    b.Navigation("kpiSource");

                    b.Navigation("Process");

                    b.Navigation("userProfile");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiCollaborators", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Kpi", "kpi")
                        .WithMany("kpiCollaborators")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("kpi");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.KpiExpectedOutcome", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Kpi", null)
                        .WithMany("kpiExpectedOutcomes")
                        .HasForeignKey("KpiId");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Lead", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.Campaign", null)
                        .WithMany("Leads")
                        .HasForeignKey("Lead");

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "user")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("user");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.LeadNTag", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Lead", "Lead")
                        .WithMany("Tags")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCRM.Models.Tag", "Tag")
                        .WithMany()
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Lead");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.LeadTransferHistory", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Lead", "lead")
                        .WithMany()
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("lead");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Actors", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.PhoneNumber", null)
                        .WithMany("PhoneNumberUsers")
                        .HasForeignKey("PhoneNumberId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaignOnwer", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCampaign", "CRMCampaign")
                        .WithOne("CRMCampaignOnwer")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMCampaignOnwer", "CRMCampaignId");

                    b.Navigation("CRMCampaign");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaignSequence", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCampaignSequence", null)
                        .WithMany("NextActions")
                        .HasForeignKey("CRMCampaignSequenceId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCollaborator", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMMeeting", null)
                        .WithMany("Participants")
                        .HasForeignKey("CRMMeetingId");

                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMTodo", null)
                        .WithMany("Collaborators")
                        .HasForeignKey("CRMTodoId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMContactOwner", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMContact", "CRMContact")
                        .WithOne("CRMContactOwner")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMContactOwner", "ContactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CRMContact");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMDealStatus", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMDeal", "CRMDeals")
                        .WithOne("CRMDealStatus")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMDealStatus", "CRMDealId")
                        .OnDelete(DeleteBehavior.ClientCascade);

                    b.Navigation("CRMDeals");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMEmail", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId");

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMEmailRecord", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCampaign", "CRMCampaign")
                        .WithOne("CRMEmailRecord")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMEmailRecord", "CRMCampaignId");

                    b.Navigation("CRMCampaign");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiActivities", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", null)
                        .WithMany("KpiActivities")
                        .HasForeignKey("CRMKpiId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiCollaborator", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", null)
                        .WithMany("Collaborators")
                        .HasForeignKey("CRMKpiId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiExpectedOutCome", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", "CRMKpi")
                        .WithMany("ExpectedOutComes")
                        .HasForeignKey("CRMKpiId");

                    b.Navigation("CRMKpi");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiIncentive", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", "CRMKpi")
                        .WithOne("KpiIncentive")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMKpiIncentive", "CRMKpiId");

                    b.Navigation("CRMKpi");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiMeasurement", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", "CRMKpi")
                        .WithOne("Measurement")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMKpiMeasurement", "CRMKpiId");

                    b.Navigation("CRMKpi");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiProcess", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", "CRMKpi")
                        .WithOne("KpiProcess")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMKpiProcess", "CRMKpiId");

                    b.Navigation("CRMKpi");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpiSource", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMKpi", "CRMKpi")
                        .WithOne("Source")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMKpiSource", "CRMKpiId");

                    b.Navigation("CRMKpi");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMLead", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMCampaign", null)
                        .WithMany("CRMLeads")
                        .HasForeignKey("CRMCampaignId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMLeadOwner", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMLead", "CRMLead")
                        .WithOne("LeadOwner")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMLeadOwner", "CRMLeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CRMLead");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMSequenceMail", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMEmailSequence", "CRMEmailSequence")
                        .WithOne("CRMSequenceMail")
                        .HasForeignKey("Jobid.App.JobCrmProject.Model.CRMSequenceMail", "CRMEmailSequenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CRMEmailSequence");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMSocialMedia", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMBuisinessCard", "CRMBuisinessCard")
                        .WithMany("CRMSocialMedias")
                        .HasForeignKey("CRMBuisinessCardId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CRMBuisinessCard");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Campaign", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId");

                    b.HasOne("Jobid.App.JobCrmProject.Model.Sequence", "Sequence")
                        .WithMany()
                        .HasForeignKey("SequenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.CampaignsTemplate", "Template")
                        .WithMany()
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");

                    b.Navigation("Sequence");

                    b.Navigation("Template");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CampaignsTemplate", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.Sequence", "Sequence")
                        .WithMany()
                        .HasForeignKey("SequenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sequence");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.ContactNote", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMContact", null)
                        .WithMany("Notes")
                        .HasForeignKey("CRMContactId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CreditTransaction", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.SimWallet", null)
                        .WithMany("CreditHistories")
                        .HasForeignKey("SimWalletId");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.NoteUpload", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.ContactNote", "ContactNote")
                        .WithMany("FileUploadUrl")
                        .HasForeignKey("ContactNoteId");

                    b.Navigation("ContactNote");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.OutgoingSms", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Model.Sequence", "Sequence")
                        .WithMany()
                        .HasForeignKey("SequenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");

                    b.Navigation("Sequence");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.PhoneNumber", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.Capabilities", "Capabilities")
                        .WithMany()
                        .HasForeignKey("CapabilitiesId");

                    b.Navigation("Capabilities");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Subscriber", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.Campaign", "Campaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Campaign");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.VoiceNoteUpload", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.ContactNote", "ContactNote")
                        .WithMany("VoiceNoteUploadUrl")
                        .HasForeignKey("ContactNoteId");

                    b.Navigation("ContactNote");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.WorkingDay", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.CRMWorkSchedule", "CRMWorkSchedule")
                        .WithMany("Workdays")
                        .HasForeignKey("CRMWorkScheduleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("CRMWorkSchedule");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.WorkingHour", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Model.WorkingDay", "WorkingDay")
                        .WithMany("WorkingHours")
                        .HasForeignKey("WorkingDayId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("WorkingDay");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMPackagePricing", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Services.PaymentService.CRMPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMPricingAndFeature", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Feature", "Feature")
                        .WithMany()
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobCrmProject.Services.PaymentService.CRMPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Feature");

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Services.PaymentService.CRMSubscription", b =>
                {
                    b.HasOne("Jobid.App.JobCrmProject.Services.PaymentService.CRMBillingAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("Jobid.App.JobCrmProject.Services.PaymentService.CRMPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("BillingAddress");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CandidateSummary", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycAddress", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.CompanyKYC", "CompanyKYC")
                        .WithOne("CompanyKycAddress")
                        .HasForeignKey("Jobid.App.JobID.Models.CompanyKycAddress", "CompanyKycId");

                    b.Navigation("CompanyKYC");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycCategories", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.Categories", "Category")
                        .WithMany("CompanyKycCategories")
                        .HasForeignKey("CategoryId");

                    b.HasOne("Jobid.App.JobID.Models.CompanyKYC", "CompanyKYC")
                        .WithMany("CompanyKycCategories")
                        .HasForeignKey("CompanyKycId");

                    b.Navigation("Category");

                    b.Navigation("CompanyKYC");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycDataClass", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.CompanyKYC", "CompanyKYC")
                        .WithMany("CompanyKycDataclasses")
                        .HasForeignKey("CompanyKycId");

                    b.HasOne("Jobid.App.JobID.Models.DataClass", "DataClass")
                        .WithMany("CompanyKycDataClasses")
                        .HasForeignKey("DataClassId");

                    b.Navigation("CompanyKYC");

                    b.Navigation("DataClass");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycDocument", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.CompanyKYC", "CompanyKYC")
                        .WithMany("CompanyKycDocument")
                        .HasForeignKey("CompanyKycId");

                    b.Navigation("CompanyKYC");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKycUBO", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.CompanyKYC", "CompanyKYC")
                        .WithMany("CompanyKycUBO")
                        .HasForeignKey("CompanyKycId");

                    b.Navigation("CompanyKYC");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.DataClassCategory", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.Categories", "Category")
                        .WithMany("DataClassCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Jobid.App.JobID.Models.DataClass", "DataClass")
                        .WithMany("DataClassCategories")
                        .HasForeignKey("DataClassId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Category");

                    b.Navigation("DataClass");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.InterviewStage", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.JobVacancy", "JobVacancy")
                        .WithMany("interviewStages")
                        .HasForeignKey("JobVacancyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobVacancy");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobApplication", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "Candidate")
                        .WithMany()
                        .HasForeignKey("CandidateId");

                    b.HasOne("Jobid.App.JobID.Models.JobVacancy", "Vacancies")
                        .WithMany()
                        .HasForeignKey("VacancyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Candidate");

                    b.Navigation("Vacancies");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobQuestion", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.JobVacancy", "JobVacancy")
                        .WithMany("jobQuestion")
                        .HasForeignKey("JobVacancyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobVacancy");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobSkill", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.JobVacancy", "JobVacancy")
                        .WithMany("jobSkills")
                        .HasForeignKey("JobVacancyId");

                    b.Navigation("JobVacancy");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobVacancy", b =>
                {
                    b.HasOne("Jobid.App.JobCRM.Models.Companies", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.Location", "Location")
                        .WithMany("jobVacancies")
                        .HasForeignKey("LocationId");

                    b.HasOne("Jobid.App.Helpers.Models.NationalLaguage", "NationalLaguage")
                        .WithMany("jobVacancies")
                        .HasForeignKey("NationalLaguageId");

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "JobPoster")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("Company");

                    b.Navigation("JobPoster");

                    b.Navigation("Location");

                    b.Navigation("NationalLaguage");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KYC", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "UserProfile")
                        .WithOne("Kyc")
                        .HasForeignKey("Jobid.App.JobID.Models.KYC", "UserProfileId");

                    b.Navigation("User");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KycCategories", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.Categories", "Category")
                        .WithMany("KycCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Jobid.App.JobID.Models.KYC", "KYC")
                        .WithMany("KycCategories")
                        .HasForeignKey("KycId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("KYC");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KycDataClass", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.DataClass", "DataClass")
                        .WithMany("KycDataClasses")
                        .HasForeignKey("DataClassId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Jobid.App.JobID.Models.KYC", "KYC")
                        .WithMany("KycDataclasses")
                        .HasForeignKey("KycId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataClass");

                    b.Navigation("KYC");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KycEmployeeGuarantor", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.KYC", "Kyc")
                        .WithMany("EmployeeGuarantors")
                        .HasForeignKey("KycId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kyc");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.Profile", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.QuestionOption", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.JobQuestion", "JobQuestion")
                        .WithMany("questionOptions")
                        .HasForeignKey("JobQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobQuestion");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.SalePerson", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.JobVacancy", "JobVacancy")
                        .WithOne("SalePerson")
                        .HasForeignKey("Jobid.App.JobID.Models.SalePerson", "JobVacancyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("JobVacancy");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.AutoSaveSetting", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.AutoSaveTransaction", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.AutoSaveSetting", "User")
                        .WithMany()
                        .HasForeignKey("AutoSaveSettingId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Beneficiary", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.BeneficiaryBatch", "Batch")
                        .WithMany("Beneficiaries")
                        .HasForeignKey("BatchId");

                    b.Navigation("Batch");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.BeneficiaryBatch", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysAccount", "JobPaysAccount")
                        .WithMany("BeneficiaryBatches")
                        .HasForeignKey("JobPaysAcctId");

                    b.Navigation("JobPaysAccount");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.BridgeCardTransactionHistory", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.Wallet", "ManagedAccount")
                        .WithMany("CardTransactions")
                        .HasForeignKey("WalletId");

                    b.Navigation("ManagedAccount");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Card", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.Wallet", "ManagedAccount")
                        .WithMany("Cards")
                        .HasForeignKey("ManagedAccountId");

                    b.Navigation("ManagedAccount");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysCard", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.Wallet", "ManagedAccount")
                        .WithMany("JobPaysCards")
                        .HasForeignKey("WalletId");

                    b.Navigation("ManagedAccount");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysClientCompanyAddress", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysClientCompany", "ClientCompany")
                        .WithMany("Addresses")
                        .HasForeignKey("ClientCompanyId");

                    b.Navigation("ClientCompany");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysCompanySubscription", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysSubscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId");

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysEnterpriseSubscription", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysSubscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysPackagePricing", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysPricingAndFeature", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysFeature", "Feature")
                        .WithMany()
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobPays.Models.JobPaysPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Feature");

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysSubscription", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysBillingAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("Jobid.App.JobPays.Models.JobPaysPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("BillingAddress");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysSubscriptionHistory", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysPricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Module", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysAccountClass", "AccountClass")
                        .WithMany("Modules")
                        .HasForeignKey("ClassId");

                    b.Navigation("AccountClass");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.NFC", b =>
                {
                    b.HasOne("Jobid.App.JobID.Models.KYC", "Kyc")
                        .WithOne("NFC")
                        .HasForeignKey("Jobid.App.JobPays.Models.NFC", "KycId");

                    b.Navigation("Kyc");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollRequest", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.JobPays.Models.PayrollVendor", "Vendor")
                        .WithMany()
                        .HasForeignKey("VendorId");

                    b.Navigation("Tenant");

                    b.Navigation("Vendor");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollService", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.PayrollVendor", "Payroll")
                        .WithMany("Services")
                        .HasForeignKey("PayrollVendorId");

                    b.Navigation("Payroll");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollTransaction", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.PayrollRequest", "PayrollRequest")
                        .WithMany("Transactions")
                        .HasForeignKey("PayrollRequestId");

                    b.Navigation("PayrollRequest");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollVendor", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.VendorWallet", "VendorWallet")
                        .WithMany()
                        .HasForeignKey("VendorWalletId");

                    b.Navigation("VendorWallet");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Transaction", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.Wallet", "Wallet")
                        .WithMany("Transactions")
                        .HasForeignKey("WalletID");

                    b.Navigation("Wallet");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.UnregisteredUserCompany", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.VendorWallet", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.PayrollVendor", "PayrollVendor")
                        .WithMany()
                        .HasForeignKey("PayrollVendorId");

                    b.Navigation("PayrollVendor");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Wallet", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysAccount", "Account")
                        .WithMany("Wallets")
                        .HasForeignKey("JobPaysAccountID");

                    b.Navigation("Account");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.WalletBeneficiary", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.Wallet", "Wallet")
                        .WithMany()
                        .HasForeignKey("WalletId");

                    b.Navigation("Wallet");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.WeavrCorpKyc", b =>
                {
                    b.HasOne("Jobid.App.JobPays.Models.JobPaysAccount", "JobPaysAccount")
                        .WithMany()
                        .HasForeignKey("JobPaysAccountId");

                    b.Navigation("JobPaysAccount");
                });

            modelBuilder.Entity("Jobid.App.JobProject.Models.TodoCustomFrequency", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "ProjectMgmt_Todo")
                        .WithMany()
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.Navigation("ProjectMgmt_Todo");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTag", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("ProjectTags")
                        .HasForeignKey("ProjectMgmt_ProjectId");

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "projectMgmt_Todo")
                        .WithMany("ProjectTags")
                        .HasForeignKey("ProjectMgmt_TodoId");

                    b.HasOne("Jobid.App.JobProjectManagement.Models.TimeSheet", "TimeSheet")
                        .WithMany("ProjectTags")
                        .HasForeignKey("TimeSheetId");

                    b.Navigation("projectMgmt_Project");

                    b.Navigation("projectMgmt_Todo");

                    b.Navigation("TimeSheet");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.ProjectTrigger", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("ProjectTriggers")
                        .HasForeignKey("projectMgmt_ProjectProjectId");

                    b.Navigation("projectMgmt_Project");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TeamMembers", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.Team", "Team")
                        .WithMany()
                        .HasForeignKey("TeamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Team");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheet", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Project", "projectMgmt_Project")
                        .WithMany("TimeSheet")
                        .HasForeignKey("projectMgmt_ProjectProjectId");

                    b.Navigation("projectMgmt_Project");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoComments", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "Todo")
                        .WithMany("TodoComments")
                        .HasForeignKey("TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Todo");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoOrder", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "Sprint")
                        .WithMany()
                        .HasForeignKey("SprintId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.ProjectMgmt_Todo", "Todo")
                        .WithOne("TodoOrder")
                        .HasForeignKey("Jobid.App.JobProjectManagement.Models.TodoOrder", "TodoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sprint");

                    b.Navigation("Todo");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TodoStatus", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.SprintProject", "Sprint")
                        .WithMany()
                        .HasForeignKey("SprintId");

                    b.Navigation("Sprint");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TriggerSequence", b =>
                {
                    b.HasOne("Jobid.App.JobProjectManagement.Models.ProjectTrigger", "ProjectTrigger")
                        .WithMany()
                        .HasForeignKey("ProjectTriggerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProjectTrigger");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.UserNotification", b =>
                {
                    b.HasOne("Jobid.App.Notification.Models.Notification", "Notification")
                        .WithMany("UserNotification")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.UserProfile", "UserProfile")
                        .WithMany()
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AISubscriptionDetail", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.AdditionalLiecense", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.ClientCards", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("Tenant");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.CompanySubscription", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId");

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterpriseSubscription", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.EnterprizeSubscriptionPayment", b =>
                {
                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.FeedbackReviewsAndRatings", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.PackagePricing", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PricingPlan");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.Subscription", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.BillingAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.Navigation("BillingAddress");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Subscription.Models.SubscriptionHistory", b =>
                {
                    b.HasOne("Jobid.App.Subscription.Models.BillingAddress", "BillingAddress")
                        .WithMany()
                        .HasForeignKey("BillingAddressId");

                    b.HasOne("Jobid.App.Subscription.Models.PricingPlan", "PricingPlan")
                        .WithMany()
                        .HasForeignKey("PricingPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Tenant.Model.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId");

                    b.Navigation("BillingAddress");

                    b.Navigation("PricingPlan");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Jobid.App.Tenant.Model.Tenant", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", "Admin")
                        .WithMany()
                        .HasForeignKey("AdminId");

                    b.Navigation("Admin");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Jobid.App.Helpers.Models.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Jobid.App.ActivityLog.Model.Activity", b =>
                {
                    b.Navigation("LogAttachments");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeePermission", b =>
                {
                    b.Navigation("EmployeeRolesPermissions");
                });

            modelBuilder.Entity("Jobid.App.AdminConsole.Model.EmployeeRoles", b =>
                {
                    b.Navigation("EmployeeRolesPermissions");

                    b.Navigation("UserEmployeeAppRole");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.BookedExternalMeeting", b =>
                {
                    b.Navigation("CustomQuestionAnswer");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.CalenderMeeting", b =>
                {
                    b.Navigation("CustomFrequency");
                });

            modelBuilder.Entity("Jobid.App.Calender.Models.ExternalMeeting", b =>
                {
                    b.Navigation("CustomQuestion");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ClientRole", b =>
                {
                    b.Navigation("ClientRoleRoleModules");

                    b.Navigation("user");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.Location", b =>
                {
                    b.Navigation("jobVacancies");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.NationalLaguage", b =>
                {
                    b.Navigation("jobVacancies");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Project", b =>
                {
                    b.Navigation("ProjectFiles");

                    b.Navigation("projectMgmt_ProjectUsers");

                    b.Navigation("ProjectTags");

                    b.Navigation("ProjectTriggers");

                    b.Navigation("TimeSheet");

                    b.Navigation("Todos");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.ProjectMgmt_Todo", b =>
                {
                    b.Navigation("Members");

                    b.Navigation("ProjectFiles");

                    b.Navigation("ProjectMgmt_TodoUsers");

                    b.Navigation("ProjectTags");

                    b.Navigation("TodoComments");

                    b.Navigation("TodoOrder");

                    b.Navigation("TodoTimeSequence");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.RoleModule", b =>
                {
                    b.Navigation("ClientRoleRoleModules");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.User", b =>
                {
                    b.Navigation("UserEmployeeAppRole");
                });

            modelBuilder.Entity("Jobid.App.Helpers.Models.UserProfile", b =>
                {
                    b.Navigation("Kyc");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Deal", b =>
                {
                    b.Navigation("Contacts");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Kpi", b =>
                {
                    b.Navigation("kpiCollaborators");

                    b.Navigation("kpiExpectedOutcomes");
                });

            modelBuilder.Entity("Jobid.App.JobCRM.Models.Lead", b =>
                {
                    b.Navigation("Tags");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMBuisinessCard", b =>
                {
                    b.Navigation("CRMSocialMedias");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaign", b =>
                {
                    b.Navigation("CRMCampaignOnwer");

                    b.Navigation("CRMEmailRecord");

                    b.Navigation("CRMLeads");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMCampaignSequence", b =>
                {
                    b.Navigation("NextActions");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMContact", b =>
                {
                    b.Navigation("CRMContactOwner");

                    b.Navigation("Notes");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMDeal", b =>
                {
                    b.Navigation("CRMDealStatus");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMEmailSequence", b =>
                {
                    b.Navigation("CRMSequenceMail");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMKpi", b =>
                {
                    b.Navigation("Collaborators");

                    b.Navigation("ExpectedOutComes");

                    b.Navigation("KpiActivities");

                    b.Navigation("KpiIncentive");

                    b.Navigation("KpiProcess");

                    b.Navigation("Measurement");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMLead", b =>
                {
                    b.Navigation("LeadOwner");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMMeeting", b =>
                {
                    b.Navigation("Participants");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMTodo", b =>
                {
                    b.Navigation("Collaborators");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.CRMWorkSchedule", b =>
                {
                    b.Navigation("Workdays");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.Campaign", b =>
                {
                    b.Navigation("Leads");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.ContactNote", b =>
                {
                    b.Navigation("FileUploadUrl");

                    b.Navigation("VoiceNoteUploadUrl");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.PhoneNumber", b =>
                {
                    b.Navigation("PhoneNumberUsers");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.SimWallet", b =>
                {
                    b.Navigation("CreditHistories");
                });

            modelBuilder.Entity("Jobid.App.JobCrmProject.Model.WorkingDay", b =>
                {
                    b.Navigation("WorkingHours");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.Categories", b =>
                {
                    b.Navigation("CompanyKycCategories");

                    b.Navigation("DataClassCategories");

                    b.Navigation("KycCategories");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.CompanyKYC", b =>
                {
                    b.Navigation("CompanyKycAddress");

                    b.Navigation("CompanyKycCategories");

                    b.Navigation("CompanyKycDataclasses");

                    b.Navigation("CompanyKycDocument");

                    b.Navigation("CompanyKycSignatory");

                    b.Navigation("CompanyKycUBO");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.DataClass", b =>
                {
                    b.Navigation("CompanyKycDataClasses");

                    b.Navigation("DataClassCategories");

                    b.Navigation("KycDataClasses");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobQuestion", b =>
                {
                    b.Navigation("questionOptions");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.JobVacancy", b =>
                {
                    b.Navigation("interviewStages");

                    b.Navigation("jobQuestion");

                    b.Navigation("jobSkills");

                    b.Navigation("SalePerson");
                });

            modelBuilder.Entity("Jobid.App.JobID.Models.KYC", b =>
                {
                    b.Navigation("EmployeeGuarantors");

                    b.Navigation("KycCategories");

                    b.Navigation("KycDataclasses");

                    b.Navigation("NFC");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.BeneficiaryBatch", b =>
                {
                    b.Navigation("Beneficiaries");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysAccount", b =>
                {
                    b.Navigation("BeneficiaryBatches");

                    b.Navigation("Wallets");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysAccountClass", b =>
                {
                    b.Navigation("Modules");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.JobPaysClientCompany", b =>
                {
                    b.Navigation("Addresses");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollRequest", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.PayrollVendor", b =>
                {
                    b.Navigation("Services");
                });

            modelBuilder.Entity("Jobid.App.JobPays.Models.Wallet", b =>
                {
                    b.Navigation("Cards");

                    b.Navigation("CardTransactions");

                    b.Navigation("JobPaysCards");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("Jobid.App.JobProjectManagement.Models.TimeSheet", b =>
                {
                    b.Navigation("ProjectFiles");

                    b.Navigation("ProjectTags");
                });

            modelBuilder.Entity("Jobid.App.Notification.Models.Notification", b =>
                {
                    b.Navigation("UserNotification");
                });
#pragma warning restore 612, 618
        }
    }
}
