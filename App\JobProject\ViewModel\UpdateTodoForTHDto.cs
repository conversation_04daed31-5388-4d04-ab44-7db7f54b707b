﻿using Jobid.App.Helpers.Enums;
using Microsoft.Graph.Models;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class UpdateTodoForTHDto
    {
        public bool LockTodo { get; set; }
        public ApprovalStatus ApprovalStatus { get; set; }
        public bool IsBillable { get; set; }
        public string Comment { get; set; }

        [Required]
        public string UserId { get; set; }
    }
}
