﻿using System;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.Model
{
    public class ActivitySettings
    {
        public Guid Id { get; set; } = new Guid();
        public string UserId { get; set; }
        public bool LogActivity { get; set; }
        public bool? TurnOnOrOffForAllUsers { get; set; }
        public EraseAcitivity EraseAcitivity { get; set; } = EraseAcitivity.After3Months;
    }
}
