﻿using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Tenant.Repository;
using Jobid.App.Tenant.ViewModel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using NLog.Targets;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using WatchDog;
using Jobid.App.Helpers.Services.Implementations;

namespace Jobid.App.Helpers.Controllers
{
    [ApiController]
    [Route("api")]
    public class HealthCheckController : ControllerBase
    {
        private readonly TenantService _tenantService;
        private readonly HealthCheckService _healthCheckService;
        private ILogger _logger = Log.ForContext<HealthCheckController>();

        public HealthCheckController(TenantService tenantService, HealthCheckService healthCheckService)
        {
            _tenantService = tenantService;
            _healthCheckService = healthCheckService;
        }

        #region Health Check
        /// <summary>
        /// Health Check
        /// </summary>
        /// <returns></returns>        
        [HttpGet("GetHealthCheck")]
        public IActionResult CheckHealth()
        {
            try
            {
                WatchDogServiceWrapper.LogWithRetry("Checking health status");
                
                var result = new
                {
                    OldTime = DateTime.UtcNow,
                    NewTime = Utils.Extensions.GetAdjustedDateTimeBasedOnTZNow(),
                    AllTimeZones = TimeZoneInfo.GetSystemTimeZones(),
                    Timezone = TimeZoneInfo.FindSystemTimeZoneById("W. Central Africa Standard Time"),
                    Message = "Success"
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                WatchDogServiceWrapper.LogErrorWithRetry($"Health check failed: {ex.Message}", "HealthCheck", "HealthCheckController", ex.StackTrace);
                return StatusCode(500, new { Message = "Health check failed", Error = ex.Message });
            }

            // Alternative comprehensive health check (commented for now)
            //var healthReport = await _healthCheckService.CheckHealthAsync();
            //var result = new
            //{
            //    status = healthReport.Status.ToString(),
            //    message = healthReport.Status == HealthStatus.Healthy ? "All systems are cool. You can rock and role!" : "Opps! We have some issues.",
            //    duration = healthReport.TotalDuration.TotalMilliseconds.ToString(),
            //    checks = healthReport.Entries
            //        .Select(entry => new
            //        {
            //            name = entry.Key,
            //            status = entry.Value.Status.ToString(),
            //            description = entry.Value.Description
            //        }),
            //    customAppSettings = GlobalVariables.CustomAppSettings,
            //    testStatus = GlobalVariables.Test
            //};
            //return Ok(result);
        }
        #endregion

        #region Run Migration
        /// <summary>
        /// Run Migration - For development specifically
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("RunMigration")]
        public async Task<IActionResult> RunMigration([FromQuery] string publicSchema)
        {
            try
            {
                var res = await _tenantService.MigrateTenants();
                return Ok(new ApiResponse<TenantMigrationResultVM>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Migration Successful",
                    Data = res
                });
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<string>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.ToString(),
                    Data = null
                });
            }
           
        }
        #endregion

        #region Seed Data
        [HttpPost]
        [Route("SeedData")]
        public async Task<IActionResult> SeedData([FromQuery] string subdomain, [FromQuery] string application)
        {
            try
            {
                await DataSeeder.SeedData(subdomain, application, true);
                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Seed Data Successful - Test",
                    Data = true
                });
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Get all values of environment variables
        /// <summary>
        /// This gets all set environment vaiables
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetAllEnvironmentVariables")]
        public IActionResult GetEnvironmentVariables(ViewEnvVariablesDto request)
        {
            var envKey = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT_VARIABLE_KEY");
            var username = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT_VARIABLE_USERNAME");
            var password = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT_VARIABLE_PASSWORD");

            if (request.Key != envKey)
            {
                return BadRequest(new ApiResponse<string>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid Key",
                    Data = null
                });
            }

            // Check that the username and password match
            if (username != request.Username || password != request.Password)
            {
                return Unauthorized(new ApiResponse<string>
                {
                    ResponseCode = "401",
                    ResponseMessage = "Wrong username or password",
                    Data = null
                });
            }

            var databaseSource = Environment.GetEnvironmentVariable("JOBPRO_DATA_SOURCE");
            var database = Environment.GetEnvironmentVariable("JOBPRO_DATABASE");
            var databaseUsername = Environment.GetEnvironmentVariable("JOBPRO_DATABASE_USERNAME");
            var databasePassword = Environment.GetEnvironmentVariable("JOBPRO_DATABASE_PASSWORD");
            var twilioAccountSID = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID");
            var twilioAuthToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN");
            var elasticCloudId = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_CLOUD_ID");
            var elasticUsername = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_USERNAME");
            var elasticPassword = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_PASSWORD");
            var elasticUri = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_URI");
            var hangfireUsername = Environment.GetEnvironmentVariable("JOBPRO_HANGFIRE_USERNAME");
            var hangfirePassword = Environment.GetEnvironmentVariable("JOBPRO_HANGFIRE_PASSWORD");
            var rabbitMQUrl = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_URL");
            var rabbitMQPort = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PORT");
            var rabbitMQUsername = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_USERNAME");
            var rabbitMQPassword = Environment.GetEnvironmentVariable("JOBPRO_RABBITMQ_BROKER_PASSWORD");
            var mollieTestApiKey = Environment.GetEnvironmentVariable("JOBPRO_MOLLIE_API_KEY_TEST");
            var mollieLiveApiKey = Environment.GetEnvironmentVariable("JOBPRO_MOLLIE_API_KEY_LIVE");
            var stripeTestPubKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_PUB_KEY_TEST");
            var stripeTestSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_SECRET_KEY_TEST");
            var stripeTestWebHookSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_WEBHOOK_SECRET_TEST");
            var stripeLivePubKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_PUB_KEY_LIVE");
            var stripeLiveSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_SECRET_KEY_LIVE");
            var stripeLiveWebHookSecretKey = Environment.GetEnvironmentVariable("JOBPRO_STRIPE_WEBHOOK_SECRET_LIVE");
            var watchDogUsername = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_USERNAME");
            var watcgDogPassword = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_PASSWORD");
            var redisHost = Environment.GetEnvironmentVariable("JOBPRO_REDIS_LOCATION");
            var mailgunApiKey = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_APIKEY");
            var mailgunDomain = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_DOMAIN");
            var jwtKey = Environment.GetEnvironmentVariable("JOBPRO_JWT_SHARED_SECRET");
            var connectionString = Environment.GetEnvironmentVariable("JOBPRO_CONNECTIONSTRING");
            var environment = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT");
            var rtcNotificationKey = Environment.GetEnvironmentVariable("JOBPRO_RTC_NOTIFICATION_KEY");
            var rtcApiKey = Environment.GetEnvironmentVariable("JOBPRO_RTC_KEY");

            return Ok(new
            {
                databaseSource,
                database,
                databaseUsername,
                databasePassword,
                twilioAccountSID,
                twilioAuthToken,
                elasticCloudId,
                elasticUsername,
                elasticPassword,
                elasticUri,
                hangfireUsername,
                hangfirePassword,
                rabbitMQUrl,
                rabbitMQPort,
                rabbitMQUsername,
                rabbitMQPassword,
                mollieTestApiKey,
                mollieLiveApiKey,
                stripeTestPubKey,
                stripeTestSecretKey,
                stripeTestWebHookSecretKey,
                stripeLivePubKey,
                stripeLiveSecretKey,
                stripeLiveWebHookSecretKey,
                watchDogUsername,
                watcgDogPassword,
                redisHost,
                mailgunApiKey,
                mailgunDomain,
                jwtKey,
                connectionString,
                username,
                password,
                envKey,
                environment,
                rtcNotificationKey,
                rtcApiKey
            });
        }
        #endregion
    }
}
