﻿using Microsoft.Extensions.DependencyInjection;

namespace Jobid.App.Helpers.Extensions
{
    /// <summary>
    /// Custom cors policy service extension
    /// </summary>
    public static class CustomCorsPolicyServiceExtension
    {
        /// <summary>
        /// Cors policy
        /// </summary>
        /// <param name="services"></param>
        public static void AddCustomCors(this IServiceCollection services)
        {
            services.AddCors(o => o.AddPolicy("MyPolicy", builder =>
            {
                builder.SetIsOriginAllowed(_ => true) // Allow all origins for development
                       .AllowAnyMethod()
                       .AllowAnyHeader()
                       .AllowCredentials(); // Required for SignalR
            }));
        }
    }
}
