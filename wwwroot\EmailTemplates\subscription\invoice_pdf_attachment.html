﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Template</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .invoice-header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
            padding: 40px 50px 30px 50px;
            position: relative;
        }

            .invoice-header::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 6px;
                background: #1565C0;
            }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .invoice-title {
            font-size: 42px;
            font-weight: 300;
            letter-spacing: 3px;
            margin: 0;
        }

        .job-pro-logo {
            background: white;
            color: #2196F3;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .invoice-meta {
            display: flex;
            gap: 80px;
            font-size: 14px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .meta-value {
            font-weight: 500;
        }

        .invoice-body {
            padding: 40px;
        }

        .billing-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            margin-bottom: 50px;
        }

        .billing-block h3 {
            color: #2196F3;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }

        .billing-block p {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 4px;
        }

            .billing-block p:first-of-type {
                color: #212529;
                font-weight: 500;
            }

        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

            .services-table thead {
                background: #f8f9fa;
            }

            .services-table th {
                padding: 16px 20px;
                text-align: left;
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                border-bottom: 1px solid #dee2e6;
            }

                .services-table th:last-child {
                    text-align: right;
                }

            .services-table td {
                padding: 20px;
                border-bottom: 1px solid #f1f3f4;
                font-size: 14px;
            }

        .service-name {
            color: #212529;
            font-weight: 500;
        }

        .service-description {
            color: #6c757d;
            font-size: 13px;
            margin-top: 2px;
        }

        .user-count {
            color: #495057;
        }

        .amount {
            text-align: right;
            color: #212529;
            font-weight: 500;
        }

        .totals-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-top: 40px;
        }

        .invoice-total-box {
            background: #f8f9fa;
            padding: 24px;
            border-radius: 6px;
            text-align: left;
            min-width: 200px;
        }

        .invoice-total-label {
            color: #2196F3;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .invoice-total-amount {
            color: #212529;
            font-size: 32px;
            font-weight: 600;
        }

        .summary-totals {
            text-align: right;
            min-width: 250px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 14px;
        }

            .total-row .label {
                color: #6c757d;
            }

            .total-row .value {
                color: #495057;
                font-weight: 500;
            }

            .total-row.final {
                border-top: 1px solid #dee2e6;
                padding-top: 12px;
                margin-top: 8px;
                font-weight: 600;
            }

                .total-row.final .label,
                .total-row.final .value {
                    color: #212529;
                    font-size: 16px;
                }

        @media (max-width: 768px) {
            .invoice-container {
                margin: 10px;
            }

            .invoice-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .invoice-meta {
                justify-content: center;
            }

            .billing-section {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .invoice-body {
                padding: 20px;
            }

            .totals-wrapper {
                flex-direction: column;
                gap: 20px;
            }

            .summary-totals {
                width: 100%;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .invoice-container {
                box-shadow: none;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div class="header-top">
                <h1 class="invoice-title">INVOICE</h1>
                <div class="job-pro-logo">Joble</div>
            </div>
            <div class="invoice-meta">
                <div class="meta-item">
                    <span class="meta-label">INVOICE NUMBER</span>
                    <span class="meta-value">{invoice_no}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">DATE OF ISSUE</span>
                    <span class="meta-value">{issue_date}</span>
                </div>
            </div>
        </div>

        <div class="invoice-body">
            <div class="billing-section">
                <div class="billing-block">
                    <h3>Billed To</h3>
                    <p>{client_name}</p>
                    <p>{client_address}</p>
                    <p>{country}</p>
                </div>
                <div class="billing-block">
                    <h3>{companu_name}</h3>
                    <p>{company_address}</p>
                    <p>{company_email}</p>
                    <p>{company_website}</p>
                </div>
            </div>

            <table class="services-table">
                <thead>
                    <tr>
                        <th>Name of Plan</th>
                        <th>No of Users</th>
                        <th>Amount Per User({currency})</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="service-name">{plan} ({interval})</div>
                            <div class="service-description">{interval} Payment</div>
                        </td>
                        <td class="user-count">{number_of_users}</td>
                        <td class="amount">{formated_amount}</td>
                    </tr>
                </tbody>
            </table>

            <div class="totals-wrapper">
                <div class="invoice-total-box">
                    <div class="invoice-total-label">Invoice Total</div>
                    <div class="invoice-total-amount">{total_formatted_amount}</div>
                </div>

                <div class="summary-totals">
                    <div class="total-row">
                        <span class="label">Subtotal</span>
                        <span class="value">{sub_total}</span>
                    </div>
                    <div class="total-row">
                        <span class="label">Tax</span>
                        <span class="value">{tax}</span>
                    </div>
                    <div class="total-row final">
                        <span class="label">Total Due</span>
                        <span class="value">{total_due}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>