using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models.Phone
{
    public class PhoneNumberUser
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public Guid PhoneNumberId { get; set; }

        [Required]
        public string UserId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        public DateTime? DeletedAt { get; set; }

        public string CreatedBy { get; set; }

        public string UpdatedBy { get; set; }

        public string DeletedBy { get; set; }

        public bool IsDeleted { get; set; }

        public virtual PhoneNumber PhoneNumber { get; set; }
    }
}