﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.JobProjectManagement.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class SprintProjectVm
    {
        public string Name { get; set; }
        public string Summary { get; set; }
        public string Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        [Required]
        public DateTime StartTime { get; set; }
        [Required]
        public DateTime EndTime { get; set; }
        public string Duration { get; set; }
        public SprintStatus Status { get; set; }
        public List<string> TagIds { get; set; }
        public List<string> memberIds { get; set; }
        public Guid UserId { get; set; }

        [CanInviteExternalUser(Applications.Joble)]
        public List<string> ExternalMembersEmails { get; set; } = new List<string>();

        [JsonIgnore]
        public Guid ProjectMgmt_ProjectId { get; set; }
        [JsonIgnore]
        public string SprintId { get; set; }
        [JsonIgnore]
        public ICollection<ProjectTag> Tags { get; set; }
        [JsonIgnore]
        public string LoggedInUserId { get; set; }
        [JsonIgnore]
        public string SubDomian { get; set; }
    }
}
