using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Stripe;
using System.IO;
using Serilog;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Contract;

namespace Jobid.App.AdminConsole.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class WalletController : ControllerBase
    {
        private readonly ILogger _logger = Log.ForContext<WalletController>();
        private readonly IUnitofwork _unitofwork;

        public WalletController(IUnitofwork unitofwork)
        {
            _unitofwork = unitofwork;
        }

        [HttpGet("get-balance")]
        public async Task<IActionResult> GetWalletBalance()
        {
            var res = await _unitofwork.WalletService.GetWalletBalance();
            return StatusCode(Convert.ToInt32(res.ResponseCode), res);
        }

        [HttpPost("fund")]
        public async Task<IActionResult> FundWallet([FromBody] FundWalletDto model)
        {
            // Get subdomain from hearder
            if (string.IsNullOrEmpty(model.Subdomain))
            {
                model.Subdomain = HttpContext.Request.Headers["Subdomain"].ToString();
            }
            var res = await _unitofwork.WalletService.FundWallet(model);
            return StatusCode(Convert.ToInt32(res.ResponseCode), res);
        }

        [HttpGet("get-transactions")]
        public async Task<IActionResult> GetTransactions([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            var res = await _unitofwork.WalletService.GetTransactions(startDate, endDate);
            return StatusCode(Convert.ToInt32(res.ResponseCode), res);
        }

        [HttpGet("get-transaction/{transactionId}")]
        public async Task<IActionResult> GetTransaction(Guid transactionId)
        {
            var res = await _unitofwork.WalletService.GetTransaction(transactionId);
            return StatusCode(Convert.ToInt32(res.ResponseCode), res);
        }

        #region Webhook Endpoint - Verify First Subscription Payment - Mollie
        /// <summary>
        /// Webhook Endpoint - Verify First Subscription Payment
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("webhook/mollie")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> VerifyFirstSubscriptionPayment([FromForm] string id)
        {
            try
            {
                //var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                //var subscription = JsonConvert.DeserializeObject<Subscription>(json);

                var res = await _unitofwork.PaymentService.VerifyMolliePaymentAsync(id); ;
                return StatusCode(Convert.ToInt32(res.ResponseCode), res);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "Error verifying first subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying first subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex}");
            }
        }
        #endregion

        #region Webhook Endpoint - Stripe Webhook
        /// <summary>
        /// Webhook Endpoint - Stripe Webhook
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("webhook/stripe")]
        [ProducesResponseType(200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> StripeWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();                
                var signature = Request.Headers["Stripe-Signature"];
                var response = await _unitofwork.PaymentService.VerifyStripePaymentAsync(json, signature);
                
                return StatusCode(Convert.ToInt32(response.ResponseCode), response);
            }
            catch (StripeException ex)
            {
                _logger.Error(ex.Message, "Error handling stripe webhook");
                return BadRequest($"Something went wrong, please try again later - {ex.ToString()}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, "Error verifying subscription payment");
                return BadRequest($"Something went wrong, please try again later - {ex.ToString()}");
            }        }
        
        #endregion

        [HttpGet("transactions-by-payment-method")]
        public async Task<IActionResult> GetTransactionsByPaymentMethod([FromQuery] AdminConsole.Enums.PaymentMethod paymentMethod, [FromQuery] int pageSize = 10, [FromQuery] int pageNumber = 1)
        {
            var res = await _unitofwork.PaymentService.GetWalletTransactionsByPaymentMethodAsync(paymentMethod, pageSize, pageNumber);
            return StatusCode(Convert.ToInt32(res.ResponseCode), res);
        }
    }
}