﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Models
{
    public class RoleModule
    {
        [Key]
        public Guid Id { get; set; }
        public string RegionName { get; set; }
        public string ModuleName { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public virtual ICollection<Permission> Permission { get; set; }
        public ICollection<ClientRoleRoleModule> ClientRoleRoleModules { get; set; }
     //   public Guid? ClientRoleId { get; set; }
    }
}
