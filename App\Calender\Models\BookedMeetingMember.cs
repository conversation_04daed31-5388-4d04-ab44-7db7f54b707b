﻿using System;

namespace Jobid.App.Calender.Models
{
    public class BookedMeetingMember : BaseModel
    {
        public Guid Id { get; set; }
        public string BookedMeetingId { get; set; }
        public string UserId { get; set; }
        public string Email { get; set; }
        public int NotifyMeInMinutes { get; set; } = 10;
        public InviteResponse InviteResponse { get; set; } = InviteResponse.Pending;
    }
}
