﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.JobProject.ViewModel;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class CreateTodoDto
    {
        public string TodoDescription { get; set; }

        [Required(ErrorMessage = "Todo summary cannot be null")]
        public string TodoSummary { get; set; }

        [Required(ErrorMessage = "Invalid Status")]
        public ProjectManagementStatus Status { get; set; }

        [Required(ErrorMessage = "Invalid Priority")]
        public ProjectManagementPriority Priority { get; set; }

        [CanInviteExternalUser(Applications.Joble)]
        [ValidEmailChecks]
        public List<string> ExternalTeamMembers { get; set; } = new List<string>();
        public List<string> MemberIds { get; set; } = new List<string> { };
        public List<string> TagIds { get; set; } = new List<string>();
        public List<IFormFile> UploadFile { get; set; } = new List<IFormFile>();
        public string TeamId { get; set; }
        public DateTime StartDateAndTime { get; set; }
        public DateTime EndTime { get; set; }
        public DateTime? DueDate { get; set; }
        public int? DurationInMinutes { get; set; }
        public Guid UserId { get; set; }
        public string ProjectId { get; set; }
        public string SprintId { get; set; }
        public Applications Application { get; set; }
        public string ClientName { get; set; }
        public long? KpiReferenceId { get; set; }
        public long? CompanyReferenceId { get; set; }
        public long? LeadReferenceId { get; set; }
        public long? DealReferenceId { get; set; }
        public long? ContactReferenceId { get; set; }
        public bool IsMeasurable { get; set; }
        public bool CreatedByAI { get; set; } = false;
        public TodoCustomFrequencyDto? TodoCustomFrequency { get; set; }
        public bool CreatedByAdmin { get; set; } = false;


        [JsonIgnore]
        public string SubDomain { get; set; }

        [JsonIgnore]
        public string TenantId { get; set; }

        [JsonIgnore]
        public bool IsBulkTodoUpload { get; set; } = false;
        public string TimeEstimate { get; set; }
    }
}
