﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Calender.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class ExternalMeetingQuestionDto
    {
        public bool AskFullName { get; set; }
        public bool FullNameRequired { get; set; }
        public bool AskEmail { get; set; }
        public bool EmailRequired { get; set; }
        public bool AskLocation { get; set; }
        public bool LocationRequired { get; set; }
        public bool AddGuests { get; set; }
        public bool AdditionalInfo { get; set; }
        public bool AdditionalInfoRequired { get; set; }
        public List<CustomQuestionDto> CustomQuestionDtos { get; set; }

        public bool CustomQuestionRequired { get; set; }
    }

    public class CustomQuestionDto
    {
        public string CustomQuestion { get; set; }
        public QuestionType QuestionType { get; set; }
        public string Options { get; set; }
    }
}
