﻿using Jobid.App.AdminConsole.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models
{
    public class EmployeeComplaint
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public string UserId { get; set; }
        public string IssuedBy { get; set; }
        public string Subdomain { get; set; }
        public ComplaintCategory Category { get; set; }
        public string ComplaintRemark { get; set; }
        public DateTime DateIssued { get; set; }
        public DateTime EndDate { get; set; }
        public bool ActionWithdrawn { get; set; }
    }
}
