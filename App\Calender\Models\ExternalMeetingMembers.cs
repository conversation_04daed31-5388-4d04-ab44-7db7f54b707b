﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.Models
{
    /// <summary>
    /// This class represents the members of an external meeting. This is specifically for Group meetings.
    /// </summary>
    public class ExternalMeetingMembers
    {
        [Key]
        public Guid Id { get; set; }
        public string UserId { get; set; }
        public string ExternalMeetingId { get; set; }
        public string Email { get; set; }
        public int NotifyMeInMinutes { get; set; } = 10;
        public DateTime? SelectedDateAndTime { get; set; }
        public InviteResponse InviteResponse { get; set; } = InviteResponse.Pending;
    }
}
