using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using System;
using System.Threading.Tasks;

namespace Jobid.App.Calender.Contracts
{
    /// <summary>
    /// Interface for meeting skills functionality
    /// </summary>
    public interface IMeetingSkillsService
    {
        /// <summary>
        /// Suggest skills for a meeting
        /// </summary>
        /// <param name="model">Meeting skill suggestion data</param>
        /// <param name="userId">User ID</param>
        /// <returns>GenericResponse with the created meeting skill suggestion</returns>
        Task<GenericResponse> SuggestMeetingSkills(MeetingSkillSuggestionDto model, string userId);

        /// <summary>
        /// Get skill suggestions for a meeting
        /// </summary>
        /// <param name="model">Meeting ID</param>
        /// <returns>GenericResponse with list of meeting skill suggestions</returns>
        Task<GenericResponse> GetMeetingSkillSuggestions(GetMeetingSkillSuggestionsDto model);

        /// <summary>
        /// Delete skill suggestions for a meeting
        /// </summary>
        /// <param name="meetingId">Meeting ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>GenericResponse with success status</returns>
        Task<GenericResponse> DeleteMeetingSkillSuggestions(Guid meetingId, string userId);
    }
}
