using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Calender.Services.Implementations
{
    /// <summary>
    /// Implementation of meeting skills functionality
    /// </summary>
    public class MeetingSkillsService : IMeetingSkillsService
    {
        private readonly IUnitofwork _unitOfWork;
        private readonly JobProDbContext _dbContext;
        private readonly ILogger _logger = Log.ForContext<MeetingSkillsService>();

        /// <summary>
        /// Constructor for MeetingSkillsService
        /// </summary>
        /// <param name="unitOfWork">Unit of work</param>
        /// <param name="dbContext">Database context</param>
        public MeetingSkillsService(IUnitofwork unitOfWork, JobProDbContext dbContext)
        {
            _unitOfWork = unitOfWork;
            _dbContext = dbContext;
        }

        /// <summary>
        /// Suggest skills for a meeting
        /// </summary>
        /// <param name="model">Meeting skill suggestion data</param>
        /// <param name="userId">User ID</param>
        /// <returns>GenericResponse with the created meeting skill suggestion</returns>
        public async Task<GenericResponse> SuggestMeetingSkills(MeetingSkillSuggestionDto model, string userId)
        {
            // Validate meeting exists
            var meeting = await _unitOfWork.CalenderService.GetMeetingById(model.MeetingId.ToString());
            if (meeting == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Meeting not found",
                    Data = null
                };
            }

            var responseList = new List<MeetingSkillSuggestionResponseDto>();

            // Process each user skill suggestion
            foreach (var userSkill in model.UserSkills)
            {
                // Check if this user already has skill suggestions for this meeting
                var existingSuggestion = await _dbContext.MeetingSkillSuggestions
                    .FirstOrDefaultAsync(ms => ms.MeetingId == model.MeetingId && ms.UserId == userSkill.UserId);

                if (existingSuggestion != null)
                {
                    // Update existing suggestion
                    existingSuggestion.Skills = string.Join(",", userSkill.Skills);
                    existingSuggestion.UpdatedAt = DateTime.UtcNow;

                    _dbContext.MeetingSkillSuggestions.Update(existingSuggestion);

                    // Map to response DTO
                    var updateResponseDto = new MeetingSkillSuggestionResponseDto
                    {
                        Id = existingSuggestion.Id,
                        UserId = existingSuggestion.UserId,
                        MeetingId = existingSuggestion.MeetingId,
                        Skills = existingSuggestion.SkillsList,
                        CreatedAt = existingSuggestion.CreatedAt,
                        UpdatedAt = existingSuggestion.UpdatedAt
                    };

                    responseList.Add(updateResponseDto);
                }
                else
                {
                    // Create new meeting skill suggestion
                    var meetingSkillSuggestion = new MeetingSkillSuggestion
                    {
                        UserId = userSkill.UserId,
                        MeetingId = model.MeetingId,
                        Skills = string.Join(",", userSkill.Skills)
                    };

                    await _dbContext.MeetingSkillSuggestions.AddAsync(meetingSkillSuggestion);

                    // Map to response DTO
                    var responseDto = new MeetingSkillSuggestionResponseDto
                    {
                        Id = meetingSkillSuggestion.Id,
                        UserId = meetingSkillSuggestion.UserId,
                        MeetingId = meetingSkillSuggestion.MeetingId,
                        Skills = meetingSkillSuggestion.SkillsList,
                        CreatedAt = meetingSkillSuggestion.CreatedAt,
                        UpdatedAt = meetingSkillSuggestion.UpdatedAt
                    };

                    responseList.Add(responseDto);
                }
            }

            await _dbContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Meeting skill suggestions processed successfully",
                Data = responseList
            };
        }

        /// <summary>
        /// Get skill suggestions for a meeting
        /// </summary>
        /// <param name="model">Meeting ID</param>
        /// <returns>GenericResponse with list of meeting skill suggestions</returns>
        public async Task<GenericResponse> GetMeetingSkillSuggestions(GetMeetingSkillSuggestionsDto model)
        {
            // Validate meeting exists
            var meeting = await _unitOfWork.CalenderService.GetMeetingById(model.MeetingId.ToString());
            if (meeting == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Meeting not found",
                    Data = null
                };
            }

            // Get all skill suggestions for the meeting
            var meetingSkillSuggestions = await _dbContext.MeetingSkillSuggestions
                .Where(ms => ms.MeetingId == model.MeetingId)
                .ToListAsync();

            // Map to user skill response DTOs
            var userSkillResponses = meetingSkillSuggestions.Select(ms => new UserSkillSuggestionResponseDto
            {
                Id = ms.Id,
                UserId = ms.UserId,
                Skills = ms.SkillsList,
                CreatedAt = ms.CreatedAt,
                UpdatedAt = ms.UpdatedAt
            }).ToList();

            // Create grouped response
            var groupedResponse = new GroupedMeetingSkillSuggestionResponseDto
            {
                MeetingId = model.MeetingId,
                UserSkills = userSkillResponses
            };

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Meeting skill suggestions retrieved successfully",
                Data = groupedResponse
            };
        }

        /// <summary>
        /// Delete skill suggestions for a meeting
        /// </summary>
        /// <param name="meetingId">Meeting ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>GenericResponse with success status</returns>
        public async Task<GenericResponse> DeleteMeetingSkillSuggestions(Guid meetingId, string userId)
        {
            // Validate meeting exists
            var meeting = await _unitOfWork.CalenderService.GetMeetingById(meetingId.ToString());
            if (meeting == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Meeting not found",
                    Data = null
                };
            }

            // Find existing meeting skill suggestion
            var meetingSkillSuggestion = await _dbContext.MeetingSkillSuggestions
                .FirstOrDefaultAsync(ms => ms.MeetingId == meetingId && ms.UserId == userId);

            if (meetingSkillSuggestion == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Meeting skill suggestions not found",
                    Data = null
                };
            }

            // Delete meeting skill suggestion
            _dbContext.MeetingSkillSuggestions.Remove(meetingSkillSuggestion);
            await _dbContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Meeting skill suggestions deleted successfully",
                Data = true
            };
        }
    }
}
