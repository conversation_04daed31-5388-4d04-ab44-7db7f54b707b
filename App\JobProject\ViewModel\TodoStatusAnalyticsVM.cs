﻿namespace Jobid.App.JobProject.ViewModel
{
    public class TodoStatusAnalyticsVM
    {
        public int Todo {  get; set; }
        public int InProgress { get; set; }
        public int Completed { get; set; }  
        public int Overdue { get; set; }
        public int Pending { get; set; }
    }

    public class TodoStatusAnalyticsDictionary
    {
        public TodoStatusAnalyticsVM Personal { get; set; }
        public TodoStatusAnalyticsVM PersonalToday { get; set; }
        public TodoStatusAnalyticsVM General { get; set; }
        public TodoStatusAnalyticsVM GeneralToday { get; set; }
    }
}
