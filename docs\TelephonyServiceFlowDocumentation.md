# Telephony Service Implementation Flow Documentation

## Overview

The telephony service implementation in the AdminConsole folder provides a comprehensive solution for managing voice communications by orchestrating three main components:

- **Twilio** (PSTN/Phone calls)
- **LiveKit** (WebRTC media)
- **Media Bridge Service** (Audio stream bridging)

This document details the complete flow architecture, service interactions, and implementation patterns.

## Architecture Components

### Core Services

1. **TelephonyService** - Main orchestration service
2. **PhoneNumberService** - Twilio PSTN operations
3. **LiveKitPbxService** - WebRTC room management
4. **MediaBridgeService** - Audio stream bridging
5. **CallHub** - SignalR real-time notifications

### Supporting Components

- **TelephonyController** - REST API endpoints
- **CallHub** - SignalR hub for real-time updates
- **DTOs** - Data transfer objects for API contracts
- **Interfaces** - Service contracts and abstractions

## Detailed Flow Documentation

### 1. Outbound Call Flow (UI -> Backend -> Twilio/LiveKit)

#### 1.1 Call Initiation Request

```
Client/UI → TelephonyController.InitiateCall() → TelephonyService.InitiateCallAsync()
```

**Step-by-Step Process:**

1. **Request Reception** (`TelephonyController.InitiateCall`)

   - Receives `InitiateCallSessionDto` from frontend
   - Contains: UserId, FromNumberId, ToNumber, UserDisplayName, MaxParticipants, EnableRecording

2. **LiveKit Room Creation** (`TelephonyService.InitiateCallAsync`)

   ```csharp
   var roomRequest = new CreateLiveKitRoomDto
   {
       RoomName = $"call-{Guid.NewGuid()}",
       MaxParticipants = request.MaxParticipants ?? 10,
       EnableRecording = request.EnableRecording ?? true
   };
   var roomResponse = await _liveKitPbxService.CreateRoom(roomRequest);
   ```

3. **LiveKit Access Token Generation**

   ```csharp
   var accessToken = await _liveKitPbxService.GenerateAccessToken(
       roomRequest.RoomName,
       request.UserId,
       canPublish: true,
       canSubscribe: true,
       audioOnly: true); // Audio-only mode for telephony
   ```

4. **Phone Number Lookup and Twilio Call Initiation**

   ```csharp
   var phoneNumberRes = await _phoneNumberService.GetPhoneNumber(request.FromNumberId);
   var twilioCallRequest = new InitiateCallDto
   {
       PhoneNumberId = phoneNumber.Id,
       ToNumber = request.ToNumber,
       FromNumber = phoneNumber.Number,
       RecordCall = request.EnableRecording ?? true,
       EnableTranscription = true,
       CallerUserId = request.UserId
   };
   var callResponse = await _phoneNumberService.InitiateCall(twilioCallRequest);
   ```

5. **Media Bridge Session Setup**

   ```csharp
   var bridgeSessionId = await _mediaBridgeService.StartBridgeSession(
       roomRequest.RoomName,
       twilioCallSid,
       participantIdentity);
   ```

6. **Call Session Response Creation**

   ```csharp
   var callSession = new CallSessionDto
   {
       SessionId = roomRequest.RoomName,
       LiveKitToken = accessToken,
       LiveKitUrl = (roomResponse.Data as dynamic)?.WebSocketUrl,
       TwilioCallSid = (callResponse.Data as dynamic)?.CallSid,
       FromNumber = phoneNumber.Number,
       ToNumber = request.ToNumber,
       Status = "initiating",
       Participants = new[] { new CallParticipantDto { ... } }
   };
   ```

7. **SignalR Notification**
   ```csharp
   await _callHubContext.Clients.User(request.UserId)
       .SendAsync("CallSessionCreated", callSession);
   ```

#### 1.2 Error Handling

- **LiveKit Room Creation Failure**: Room cleanup, error response
- **Token Generation Failure**: Error response without room cleanup
- **Twilio Call Failure**: LiveKit room cleanup, error response
- **Media Bridge Failure**: Logged but doesn't fail the call

### 2. Inbound Call Flow (Twilio -> Backend -> LiveKit)

#### 2.1 Webhook Reception

```
Twilio Webhook → TelephonyController.HandleInboundCall() → TelephonyService.HandleInboundCallAsync()
```

**Step-by-Step Process:**

1. **Webhook Processing** (`TelephonyController.HandleInboundCall`)

   - Receives From, To, CallSid from Twilio webhook
   - Endpoint: `POST /api/telephony/webhook/twilio/inbound-call`
   - Anonymous access (no authorization required)

2. **Call Record Creation** (`TelephonyService.HandleInboundCallAsync`)

   ```csharp
   var response = await _phoneNumberService.HandleInboundCall(from, to);
   ```

3. **TwiML Response Generation**

   ```csharp
   var twimlResponse = await _phoneNumberService.GenerateInboundCallTwiML(callSid, from, to);
   ```

4. **TwiML Response Delivery**
   - Returns XML TwiML response to Twilio
   - Instructs Twilio on call handling (routing, recording, etc.)

#### 2.2 Agent Assignment and LiveKit Integration

The inbound call flow integrates with agent availability and LiveKit room creation through the PhoneNumberService, which handles:

- Agent availability checking
- Queue management
- LiveKit room creation for accepted calls
- Media bridge setup

### 3. Call Answer Flow (Agent -> Backend -> Twilio/LiveKit)

#### 3.1 Answer Request Processing

```
Agent UI → TelephonyController.AnswerCall() → TelephonyService.AnswerCallAsync()
```

**Step-by-Step Process:**

1. **Answer Request Reception** (`TelephonyController.AnswerCall`)

   - Receives `AnswerCallDto` from agent interface
   - Contains: UserId, CallSid, LiveKitRoomName, UserDisplayName

2. **Twilio Call Acceptance** (`TelephonyService.AnswerCallAsync`)

   ```csharp
   var acceptRequest = new AcceptInboundCallDto
   {
       CallId = Guid.Parse(request.CallSid),
       UserId = request.UserId,
       UserName = request.UserDisplayName
   };
   var acceptResponse = await _phoneNumberService.AcceptInboundCall(acceptRequest);
   ```

3. **LiveKit Access Token Generation**

   ```csharp
   var accessToken = await _liveKitPbxService.GenerateAccessToken(
       request.LiveKitRoomName,
       request.UserId,
       canPublish: true,
       canSubscribe: true,
       audioOnly: true);
   ```

4. **Call Status Update**

   ```csharp
   await _phoneNumberService.UpdateCallStatus(request.CallSid, "in-progress");
   ```

5. **SignalR Notification**
   ```csharp
   await _callHubContext.Clients.Group(request.LiveKitRoomName)
       .SendAsync("ParticipantJoined", new { ... });
   ```

### 4. Call Termination Flow

#### 4.1 End Call Request

```
UI → TelephonyController.EndCall() → TelephonyService.EndCallAsync()
```

**Step-by-Step Process:**

1. **End Request Processing** (`TelephonyService.EndCallAsync`)

   - Receives `EndCallDto` with SessionId, UserId, CallSid, Reason

2. **Twilio Call Termination**

   ```csharp
   if (!string.IsNullOrEmpty(request.CallSid))
   {
       await _phoneNumberService.UpdateCallStatus(request.CallSid, "completed");
   }
   ```

3. **LiveKit Room Closure**

   ```csharp
   if (Guid.TryParse(request.SessionId, out var roomId))
   {
       await _liveKitPbxService.CloseRoom(roomId);
   }
   ```

4. **Participant Notification**
   ```csharp
   await _callHubContext.Clients.Group(request.SessionId)
       .SendAsync("CallEnded", new { ... });
   ```

### 5. Participant Management Flow

#### 5.1 Adding Participants

```
UI → TelephonyController.AddParticipant() → TelephonyService.AddParticipantAsync()
```

**Process:**

1. Validates participant request
2. Adds participant to LiveKit room
3. Generates access token for new participant
4. Notifies existing participants via SignalR

#### 5.2 Removing Participants

```
UI → TelephonyController.RemoveParticipant() → TelephonyService.RemoveParticipantAsync()
```

**Process:**

1. Validates removal request
2. Removes participant from LiveKit room
3. Notifies remaining participants via SignalR

### 6. Call Forwarding Flow

#### 6.1 Forward Call Request

```
UI → TelephonyController.ForwardCall() → TelephonyService.ForwardCallAsync()
```

**Process:**

1. Validates current call session
2. Initiates new call to forward destination
3. Bridges audio between original caller and new destination
4. Optionally ends original agent connection

### 7. Media Bridge Flow

#### 7.1 Bridge Session Management

The MediaBridgeService handles the critical task of bridging PSTN audio streams with WebRTC:

**Components:**

- **WebSocket Handler**: Receives real-time audio from Twilio
- **Audio Buffer Management**: Stores and processes audio data
- **LiveKit Integration**: Streams audio to LiveKit participants

**Flow:**

1. **Session Initiation**: `StartBridgeSession(roomName, callSid, participantIdentity)`
2. **Audio Stream Processing**: Real-time audio bridging between PSTN and WebRTC
3. **Session Monitoring**: Track active bridge sessions
4. **Session Termination**: Clean up resources when call ends

### 8. Webhook Processing Flow

#### 8.1 Twilio Webhooks

The system handles various Twilio webhooks:

**Call Status Webhooks:**

```
Twilio → TelephonyController.HandleTwilioCallStatus() → TelephonyService.HandleTwilioCallStatusAsync()
```

**Dial Result Webhooks:**

```
Twilio → TelephonyController.HandleDialResult() → TelephonyService.HandleDialResult()
```

**Voicemail Webhooks:**

```
Twilio → TelephonyController.HandleVoicemail() → TelephonyService.HandleVoicemail()
```

#### 8.2 LiveKit Webhooks

```
LiveKit → TelephonyController.HandleLiveKitWebhook() → TelephonyService.HandleLiveKitWebhookAsync()
```

Handles participant events, room status changes, and recording notifications.

### 9. SignalR Real-time Communication

#### 9.1 CallHub Implementation

The `CallHub` provides real-time updates to connected clients:

**Methods:**

- `JoinCallGroup(phoneNumberId)`: Subscribe to call updates
- `LeaveCallGroup(phoneNumberId)`: Unsubscribe from updates
- `UpdateCallStatus(phoneNumberId, status, callId)`: Broadcast status updates

**Events Broadcasted:**

- `CallSessionCreated`: New call session initiated
- `ParticipantJoined`: Participant joined call
- `ParticipantLeft`: Participant left call
- `CallEnded`: Call session terminated
- `CallStatusUpdated`: Call status changed

### 10. Error Handling and Resilience

#### 10.1 Error Handling Patterns

1. **Service-Level Errors**: Logged with context, return structured error responses
2. **Integration Failures**: Graceful degradation, fallback mechanisms
3. **Webhook Failures**: Return appropriate TwiML error responses

#### 10.2 Fallback Mechanisms

- **Inbound Call Fallback**: When WebRTC agents unavailable
- **Queue Wait Music**: When agents busy
- **Voicemail Recording**: When no agents available

### 11. API Endpoints Summary

#### 11.1 Call Management Endpoints

- `POST /api/telephony/initiate-call`: Initiate outbound call
- `POST /api/telephony/answer-call`: Answer incoming call
- `POST /api/telephony/end-call`: End call session
- `GET /api/telephony/session/{sessionId}`: Get call session details

#### 11.2 Participant Management Endpoints

- `POST /api/telephony/add-participant`: Add participant to call
- `POST /api/telephony/remove-participant`: Remove participant from call
- `POST /api/telephony/forward-call`: Forward active call

#### 11.3 Media Bridge Endpoints

- `GET /api/telephony/bridge-sessions`: Get active bridge sessions
- `POST /api/telephony/bridge-sessions/{sessionId}/stop`: Stop bridge session

#### 11.4 Webhook Endpoints

- `POST /api/telephony/webhook/twilio/call-status`: Twilio call status
- `POST /api/telephony/webhook/twilio/inbound-call`: Twilio inbound call
- `POST /api/telephony/webhook/twilio/dial-result`: Twilio dial result
- `POST /api/telephony/webhook/twilio/voicemail`: Twilio voicemail
- `POST /api/telephony/webhook/twilio/inbound-fallback`: Inbound call fallback
- `POST /api/telephony/webhook/livekit`: LiveKit webhooks

#### 11.5 Analytics Endpoints

- `GET /api/telephony/call-history/{phoneNumberId}`: Call history
- `GET /api/telephony/recording/{callId}`: Call recording
- `GET /api/telephony/transcription/{callId}`: Call transcription
- `GET /api/telephony/analytics/{phoneNumberId}`: Call analytics

### 12. Configuration and Dependencies

#### 12.1 Service Registration

```csharp
// From ServiceExtensions.cs
services.AddScoped<IPhoneNumberService, PhoneNumberService>();
services.AddScoped<ILiveKitPbxService, LiveKitPbxService>();
services.AddScoped<IMediaBridgeService, MediaBridgeService>();
services.AddScoped<ITelephonyService, TelephonyService>();
```

#### 12.2 Dependency Injection

The TelephonyService depends on:

- `IPhoneNumberService`: Twilio operations
- `ILiveKitPbxService`: WebRTC room management
- `IHubContext<CallHub>`: SignalR notifications
- `IMediaBridgeService`: Audio stream bridging

### 13. Data Flow Diagrams

#### 13.1 Outbound Call Data Flow

```
[UI] → [TelephonyController] → [TelephonyService] → [LiveKitPbxService]
                                     ↓
[SignalR Hub] ← [MediaBridgeService] ← [PhoneNumberService] → [Twilio API]
```

#### 13.2 Inbound Call Data Flow

```
[Twilio Webhook] → [TelephonyController] → [TelephonyService] → [PhoneNumberService]
                                                ↓
[TwiML Response] ← [Agent Assignment] ← [LiveKit Room Creation]
```

### 14. Security Considerations

#### 14.1 Authentication and Authorization

- Most endpoints require `[Authorize]` attribute
- Webhook endpoints use `[AllowAnonymous]` for Twilio/LiveKit callbacks
- User context validation in service methods

#### 14.2 Webhook Security

- Twilio webhook signature validation (implemented in PhoneNumberService)
- LiveKit webhook authentication
- Request origin validation

### 15. Monitoring and Logging

#### 15.1 Logging Implementation

- Comprehensive logging using Serilog
- Structured logging with correlation IDs
- Error context preservation

#### 15.2 Key Metrics

- Call initiation success/failure rates
- Media bridge session health
- LiveKit room utilization
- Webhook processing latency

### 16. Frontend (FE) Flow Documentation

#### 16.1 Frontend Architecture Overview

The frontend telephony implementation follows a modern reactive architecture with real-time communication:

**Key Components:**

- **Call Management UI**: Main interface for call operations
- **SignalR Client**: Real-time communication with backend
- **LiveKit SDK**: WebRTC media handling
- **State Management**: Call session state management
- **Audio Controls**: Mute, hold, transfer controls

#### 16.2 Outbound Call Frontend Flow

**User Interaction Flow:**

```
[Dialer UI] → [Call Button Click] → [API Request] → [LiveKit Connection] → [Audio Stream]
```

**Step-by-Step Frontend Process:**

1. **Call Initiation UI**

   ```javascript
   // User enters phone number and clicks call
   const initiateCall = async (toNumber, fromNumberId) => {
     const callRequest = {
       userId: currentUser.id,
       fromNumberId: fromNumberId,
       toNumber: toNumber,
       userDisplayName: currentUser.displayName,
       maxParticipants: 10,
       enableRecording: true,
       enableAI: true,
     };

     // Call backend API
     const response = await fetch("/api/telephony/initiate-call", {
       method: "POST",
       headers: { "Content-Type": "application/json" },
       body: JSON.stringify(callRequest),
     });
   };
   ```

2. **Backend Response Handling**

   ```javascript
   // Handle successful call initiation
   if (response.ok) {
     const callSession = await response.json();

     // Store call session data
     setCallSession(callSession.data);

     // Connect to LiveKit room
     await connectToLiveKitRoom(callSession.data);

     // Update UI to show call in progress
     setCallState("connecting");
   }
   ```

3. **LiveKit Connection Setup**

   ```javascript
   const connectToLiveKitRoom = async (sessionData) => {
     const room = new Room({
       audioCaptureDefaults: {
         echoCancellation: true,
         noiseSuppression: true,
         autoGainControl: true,
       },
     });

     // Connect to room with provided token
     await room.connect(sessionData.liveKitUrl, sessionData.liveKitToken);

     // Set up event listeners
     room.on("participantConnected", handleParticipantConnected);
     room.on("participantDisconnected", handleParticipantDisconnected);
     room.on("trackSubscribed", handleTrackSubscribed);
   };
   ```

4. **UI State Updates**
   ```javascript
   // Update call status UI
   const updateCallUI = (status) => {
     switch (status) {
       case "connecting":
         showCallDialog();
         setCallStatus("Connecting...");
         enableHangupButton();
         break;
       case "connected":
         setCallStatus("Connected");
         enableCallControls();
         break;
       case "ended":
         hideCallDialog();
         resetCallState();
         break;
     }
   };
   ```

#### 16.3 Inbound Call Frontend Flow

**Real-time Notification Flow:**

```
[SignalR Event] → [Call Notification] → [Accept/Reject UI] → [LiveKit Connection]
```

**Step-by-Step Process:**

1. **SignalR Event Reception**

   ```javascript
   // SignalR connection setup
   const signalRConnection = new HubConnectionBuilder()
     .withUrl("/callHub")
     .build();

   // Listen for incoming calls
   signalRConnection.on("IncomingCall", (callData) => {
     handleIncomingCall(callData);
   });

   // Join phone number group for notifications
   signalRConnection.invoke("JoinCallGroup", userPhoneNumberId);
   ```

2. **Incoming Call UI Display**

   ```javascript
   const handleIncomingCall = (callData) => {
     // Show incoming call notification
     showIncomingCallModal({
       callerNumber: callData.fromNumber,
       callSid: callData.callSid,
       roomName: callData.liveKitRoomName,
     });

     // Play ringtone
     playRingtone();

     // Set timeout for auto-reject
     setTimeout(() => {
       if (callState === "ringing") {
         rejectCall(callData.callSid);
       }
     }, 30000); // 30 second timeout
   };
   ```

3. **Call Accept Flow**

   ```javascript
   const acceptCall = async (callSid, roomName) => {
     try {
       // Stop ringtone
       stopRingtone();

       // Call backend to accept call
       const response = await fetch("/api/telephony/answer-call", {
         method: "POST",
         headers: { "Content-Type": "application/json" },
         body: JSON.stringify({
           userId: currentUser.id,
           callSid: callSid,
           liveKitRoomName: roomName,
           userDisplayName: currentUser.displayName,
         }),
       });

       if (response.ok) {
         const result = await response.json();

         // Connect to LiveKit room
         await connectToLiveKitRoom({
           liveKitToken: result.data.liveKitToken,
           liveKitUrl: liveKitServerUrl,
           roomName: roomName,
         });

         // Update UI
         hideIncomingCallModal();
         showActiveCallUI();
         setCallState("connected");
       }
     } catch (error) {
       showError("Failed to accept call");
     }
   };
   ```

4. **Call Reject Flow**
   ```javascript
   const rejectCall = async (callSid) => {
     // Update UI immediately
     hideIncomingCallModal();
     stopRingtone();

     // Notify backend (optional - call will timeout)
     try {
       await fetch("/api/telephony/reject-call", {
         method: "POST",
         headers: { "Content-Type": "application/json" },
         body: JSON.stringify({ callSid: callSid }),
       });
     } catch (error) {
       console.error("Error rejecting call:", error);
     }
   };
   ```

#### 16.4 Call Control Frontend Flow

**Active Call Controls:**

```
[Mute Button] → [Hold Button] → [Transfer Button] → [Hangup Button]
```

1. **Mute/Unmute Control**

   ```javascript
   const toggleMute = async () => {
     if (localAudioTrack) {
       const isMuted = localAudioTrack.isMuted;

       if (isMuted) {
         await localAudioTrack.unmute();
         setMuteButtonState("unmuted");
       } else {
         await localAudioTrack.mute();
         setMuteButtonState("muted");
       }

       // Update UI
       updateMuteButton(!isMuted);
     }
   };
   ```

2. **Hold Functionality**

   ```javascript
   const toggleHold = async () => {
     const isOnHold = callState === "on-hold";

     if (isOnHold) {
       // Resume call
       await localAudioTrack.unmute();
       setCallState("connected");
       setHoldButtonState("active");
     } else {
       // Put on hold
       await localAudioTrack.mute();
       setCallState("on-hold");
       setHoldButtonState("holding");
     }
   };
   ```

3. **Call Transfer UI**

   ```javascript
   const initiateTransfer = async (targetNumber) => {
     try {
       const response = await fetch("/api/telephony/forward-call", {
         method: "POST",
         headers: { "Content-Type": "application/json" },
         body: JSON.stringify({
           sessionId: currentCallSession.sessionId,
           targetNumber: targetNumber,
           transferType: "blind", // or 'attended'
           userId: currentUser.id,
         }),
       });

       if (response.ok) {
         showTransferSuccessMessage();
         // Call will be ended automatically after transfer
       }
     } catch (error) {
       showError("Transfer failed");
     }
   };
   ```

4. **Hangup Call**
   ```javascript
   const hangupCall = async () => {
     try {
       // End call on backend
       await fetch("/api/telephony/end-call", {
         method: "POST",
         headers: { "Content-Type": "application/json" },
         body: JSON.stringify({
           sessionId: currentCallSession.sessionId,
           userId: currentUser.id,
           callSid: currentCallSession.twilioCallSid,
           reason: "User initiated hangup",
         }),
       });

       // Disconnect from LiveKit
       if (liveKitRoom) {
         await liveKitRoom.disconnect();
       }

       // Reset UI
       resetCallUI();
     } catch (error) {
       console.error("Error hanging up:", error);
       // Force UI reset even if backend call fails
       resetCallUI();
     }
   };
   ```

#### 16.5 Multi-participant Call Frontend Flow

**Conference Call Management:**

```
[Add Participant Button] → [Participant List] → [Remove Participant] → [Participant Controls]
```

1. **Adding Participants**

   ```javascript
   const addParticipant = async (phoneNumber) => {
     try {
       const response = await fetch("/api/telephony/add-participant", {
         method: "POST",
         headers: { "Content-Type": "application/json" },
         body: JSON.stringify({
           sessionId: currentCallSession.sessionId,
           phoneNumber: phoneNumber,
           userId: currentUser.id,
         }),
       });

       if (response.ok) {
         showSuccess("Participant added successfully");
         // Participant will appear via SignalR event
       }
     } catch (error) {
       showError("Failed to add participant");
     }
   };
   ```

2. **Participant Management UI**

   ```javascript
   // Handle participant events from SignalR
   signalRConnection.on("ParticipantJoined", (participantData) => {
     updateParticipantList(participantData, "joined");
     showNotification(`${participantData.displayName} joined the call`);
   });

   signalRConnection.on("ParticipantLeft", (participantData) => {
     updateParticipantList(participantData, "left");
     showNotification(`${participantData.displayName} left the call`);
   });

   const updateParticipantList = (participant, action) => {
     if (action === "joined") {
       setParticipants((prev) => [...prev, participant]);
     } else {
       setParticipants((prev) =>
         prev.filter((p) => p.userId !== participant.userId)
       );
     }
   };
   ```

#### 16.6 Real-time State Management

**SignalR Event Handling:**

```javascript
// Complete SignalR event setup
const setupSignalREvents = () => {
  // Call session events
  signalRConnection.on("CallSessionCreated", handleCallSessionCreated);
  signalRConnection.on("CallStatusUpdated", handleCallStatusUpdate);
  signalRConnection.on("CallEnded", handleCallEnded);

  // Participant events
  signalRConnection.on("ParticipantJoined", handleParticipantJoined);
  signalRConnection.on("ParticipantLeft", handleParticipantLeft);

  // Call quality events
  signalRConnection.on("CallQualityUpdate", handleCallQualityUpdate);

  // Error events
  signalRConnection.on("CallError", handleCallError);
};

const handleCallStatusUpdate = (statusData) => {
  switch (statusData.status) {
    case "ringing":
      setCallStatus("Ringing...");
      break;
    case "connected":
      setCallStatus("Connected");
      startCallTimer();
      break;
    case "on-hold":
      setCallStatus("On Hold");
      pauseCallTimer();
      break;
    case "ended":
      setCallStatus("Call Ended");
      resetCallState();
      break;
  }
};
```

#### 16.7 Error Handling and User Experience

**Frontend Error Handling:**

```javascript
const handleCallErrors = {
  connectionFailed: () => {
    showError(
      "Failed to connect to call. Please check your internet connection."
    );
    resetCallState();
  },

  microphonePermission: () => {
    showWarning(
      "Microphone permission required for calls. Please enable in browser settings."
    );
  },

  liveKitConnectionFailed: () => {
    showError("Media connection failed. Retrying...");
    // Attempt reconnection
    setTimeout(retryLiveKitConnection, 2000);
  },

  twilioCallFailed: () => {
    showError("Call could not be completed. Please try again.");
    resetCallState();
  },
};

// Retry mechanism for LiveKit connection
const retryLiveKitConnection = async (attempt = 1) => {
  if (attempt > 3) {
    showError("Unable to establish media connection after multiple attempts.");
    return;
  }

  try {
    await connectToLiveKitRoom(currentCallSession);
    showSuccess("Connection restored");
  } catch (error) {
    setTimeout(() => retryLiveKitConnection(attempt + 1), 2000 * attempt);
  }
};
```

#### 16.8 UI State Management

**Call State Management:**

```javascript
// Call state enum
const CallState = {
  IDLE: "idle",
  DIALING: "dialing",
  RINGING: "ringing",
  CONNECTING: "connecting",
  CONNECTED: "connected",
  ON_HOLD: "on-hold",
  TRANSFERRING: "transferring",
  ENDING: "ending",
  ENDED: "ended",
  ERROR: "error",
};

// State management hook
const useCallState = () => {
  const [callState, setCallState] = useState(CallState.IDLE);
  const [callSession, setCallSession] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [callDuration, setCallDuration] = useState(0);
  const [isRecording, setIsRecording] = useState(false);

  const resetCallState = () => {
    setCallState(CallState.IDLE);
    setCallSession(null);
    setParticipants([]);
    setCallDuration(0);
    setIsRecording(false);
  };

  return {
    callState,
    setCallState,
    callSession,
    setCallSession,
    participants,
    setParticipants,
    callDuration,
    setCallDuration,
    isRecording,
    setIsRecording,
    resetCallState,
  };
};
```

#### 16.9 Audio Device Management

**Device Selection and Control:**

```javascript
const useAudioDevices = () => {
  const [audioDevices, setAudioDevices] = useState({
    microphones: [],
    speakers: [],
  });
  const [selectedDevices, setSelectedDevices] = useState({
    microphone: null,
    speaker: null,
  });

  // Get available audio devices
  const getAudioDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const microphones = devices.filter(
        (device) => device.kind === "audioinput"
      );
      const speakers = devices.filter(
        (device) => device.kind === "audiooutput"
      );

      setAudioDevices({ microphones, speakers });
    } catch (error) {
      console.error("Error getting audio devices:", error);
    }
  };

  // Switch microphone during call
  const switchMicrophone = async (deviceId) => {
    if (liveKitRoom && localAudioTrack) {
      try {
        await localAudioTrack.restartTrack({ deviceId });
        setSelectedDevices((prev) => ({ ...prev, microphone: deviceId }));
      } catch (error) {
        showError("Failed to switch microphone");
      }
    }
  };

  return {
    audioDevices,
    selectedDevices,
    getAudioDevices,
    switchMicrophone,
  };
};
```

#### 16.10 Performance Optimization

**Frontend Performance Best Practices:**

```javascript
// Memoized components for call UI
const CallControls = React.memo(
  ({ onMute, onHold, onHangup, isMuted, isOnHold }) => {
    return (
      <div className="call-controls">
        <button onClick={onMute} className={isMuted ? "muted" : ""}>
          {isMuted ? "Unmute" : "Mute"}
        </button>
        <button onClick={onHold} className={isOnHold ? "holding" : ""}>
          {isOnHold ? "Resume" : "Hold"}
        </button>
        <button onClick={onHangup} className="hangup">
          Hangup
        </button>
      </div>
    );
  }
);

// Debounced audio level updates
const useAudioLevels = () => {
  const [audioLevel, setAudioLevel] = useState(0);

  const updateAudioLevel = useMemo(
    () => debounce((level) => setAudioLevel(level), 100),
    []
  );

  return { audioLevel, updateAudioLevel };
};

// Cleanup on component unmount
useEffect(() => {
  return () => {
    // Cleanup LiveKit connection
    if (liveKitRoom) {
      liveKitRoom.disconnect();
    }

    // Cleanup SignalR connection
    if (signalRConnection) {
      signalRConnection.stop();
    }

    // Clear timers
    clearInterval(callTimerInterval);
  };
}, []);
```

This comprehensive flow documentation provides a detailed understanding of the telephony service implementation, covering all aspects from API endpoints to internal service orchestration, error handling, and real-time communication patterns.
