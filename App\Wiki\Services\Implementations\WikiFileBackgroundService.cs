using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Wiki.Enums;
using Jobid.App.Wiki.Services.Contract;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Hangfire;
using Jobid.App.AdminConsole.Services;

namespace Jobid.App.Wiki.Services.Implementations
{
    public class WikiFileBackgroundService : IWikiFileBackgroundService
    {
        private readonly IAWSS3Sevices _s3Service;
        private readonly JobProDbContext _dbContext;
        private ILogger _logger = Log.ForContext<WikiFileBackgroundService>();

        // Size of each part in multipart upload (loaded from appsettings.json)
        private readonly int _partSize;
        
        public WikiFileBackgroundService(IAWSS3Sevices s3Service, JobProDbContext dbContext, IConfiguration configuration)
        {
            _s3Service = s3Service;
            _dbContext = dbContext;
            
            // Load part size from configuration
            _partSize = configuration.GetValue<int>("WikiFileUploadSettings:MultipartChunkSize");
        }

        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]        
        public async Task ProcessLargeFileUploadAsync(Guid fileId, string filePath, string awsKey)
        {
            try
            {
                // Update file status to InProgress
                var file = await _dbContext.WikiFiles.FindAsync(fileId);
                if (file == null)
                {
                    _logger.Error("File with ID {FileId} not found", fileId);
                    return;
                }

                file.UploadStatus = WikiFileUploadStatus.InProgress;
                await _dbContext.SaveChangesAsync();

                // Process multipart upload
                string contentType = "application/octet-stream";
                var result = await _s3Service.InitiateMultipartUploadAsync(awsKey, contentType);
                string uploadId = result.uploadId;
                var partETags = new Dictionary<int, string>();

                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    long fileSize = fileStream.Length;
                    long position = 0;
                    int partNumber = 1;

                    while (position < fileSize)
                    {
                        int partSize = (int)Math.Min(_partSize, fileSize - position);
                        byte[] buffer = new byte[partSize];
                        int bytesRead = await fileStream.ReadAsync(buffer, 0, partSize);
                        var partData = new byte[bytesRead];
                        Array.Copy(buffer, partData, bytesRead);
                        
                        using (var partStream = new MemoryStream(partData))
                        {
                            string etag = await _s3Service.UploadPartAsync(awsKey, uploadId, partNumber, partStream, partSize);
                            partETags.Add(partNumber, etag);
                        }

                        position += bytesRead;
                        partNumber++;
                    }
                }

                // Complete multipart upload
                bool completed = await _s3Service.CompleteMultipartUploadAsync(uploadId, awsKey, partETags);

                // Update file status to Completed
                file.UploadStatus = completed ? WikiFileUploadStatus.Completed : WikiFileUploadStatus.Failed;
                await _dbContext.SaveChangesAsync();

                // Delete temporary file
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                
                _logger.Information("Successfully completed multipart upload for file ID {FileId}", fileId);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error processing large file upload for file ID {FileId}", fileId);
                
                // Update file status to Failed
                var file = await _dbContext.WikiFiles.FindAsync(fileId);
                if (file != null)
                {
                    file.UploadStatus = WikiFileUploadStatus.Failed;
                    await _dbContext.SaveChangesAsync();
                }
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
                
                // Rethrow the exception to trigger Hangfire retry
                throw;
            }
        }
        
        public async Task<bool> ProcessLargeFileUploadStreamAsync(Guid fileId, Stream stream, long fileSize, string awsKey)
        {
            try
            {
                // Update file status to InProgress
                var file = await _dbContext.WikiFiles.FindAsync(fileId);
                if (file == null)
                {
                    _logger.Error("File with ID {FileId} not found", fileId);
                    return false;
                }

                file.UploadStatus = WikiFileUploadStatus.InProgress;
                await _dbContext.SaveChangesAsync();

                // Start multipart upload
                string contentType = "application/octet-stream";
                var result = await _s3Service.InitiateMultipartUploadAsync(awsKey, contentType);
                string uploadId = result.uploadId;
                var partETags = new Dictionary<int, string>();

                long position = 0;
                int partNumber = 1;

                while (position < fileSize)
                {
                    int partSize = (int)Math.Min(_partSize, fileSize - position);
                    byte[] buffer = new byte[partSize];
                    int bytesRead = await stream.ReadAsync(buffer, 0, partSize);
                    var partData = new byte[bytesRead];
                    Array.Copy(buffer, partData, bytesRead);
                    
                    using (var partStream = new MemoryStream(partData))
                    {
                        string etag = await _s3Service.UploadPartAsync(awsKey, uploadId, partNumber, partStream, partSize);
                        partETags.Add(partNumber, etag);
                    }

                    position += bytesRead;
                    partNumber++;
                }

                // Complete multipart upload
                bool completed = await _s3Service.CompleteMultipartUploadAsync(uploadId, awsKey, partETags);

                // Update file status
                file.UploadStatus = completed ? WikiFileUploadStatus.Completed : WikiFileUploadStatus.Failed;
                await _dbContext.SaveChangesAsync();

                return completed;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error processing large file upload stream for file ID {FileId}", fileId);
                
                // Update file status to Failed
                var file = await _dbContext.WikiFiles.FindAsync(fileId);
                if (file != null)
                {
                    file.UploadStatus = WikiFileUploadStatus.Failed;
                    await _dbContext.SaveChangesAsync();
                }
                
                return false;
            }
        }

        public async Task<bool> CheckUploadStatusAsync(Guid fileId)
        {
            try
            {
                var file = await _dbContext.WikiFiles.FindAsync(fileId);
                if (file == null)
                {
                    return false;
                }

                return file.UploadStatus == WikiFileUploadStatus.Completed;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error checking upload status for file ID {FileId}", fileId);
                return false;
            }
        }
    }
}
