﻿using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProject.Models
{
    public class TodoCustomFrequency
    {
        [Key]
        public Guid Id { get; set; }
        public string RepeatEvery { get; set; }
        public int? RepeatCount { get; set; }
        public string RepeatOn { get; set; }
        public TodoEndStatus? EndStatus { get; set; }
        public DateTime? EndsOn { get; set; }
        public int? EndsAfter { get; set; }
        public Guid? ProjectMgmt_TodoId { get; set; }
        public ProjectMgmt_Todo ProjectMgmt_Todo { get; set; }
    }

    public enum TodoEndStatus
    {
        Never,
        On,
        After
    }
}
