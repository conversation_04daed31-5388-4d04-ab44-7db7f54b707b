﻿#region Using Statements
using DocumentFormat.OpenXml.Spreadsheet;
using Hangfire;
using Jobid.App.ActivityLog.BackGroundJobs;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto.External;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Models;
using Jobid.App.AdminConsole.Services;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Controllers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.Model;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using QRCoder;
using Serilog;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Imaging;
using System.DrawingCore;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Reflection;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using Utility = Jobid.App.Helpers.Utils.Utility;
using System.Net.Http;
#endregion

namespace Jobid.App.Helpers.Services.Implementations
{
    public class UserServices : IUserServices
    {
        #region Properties And Constructor
        private readonly JobProDbContext publicSchemaContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly UserManager<User> UserManager;
        private readonly JobProDbContext subdomainSchemaContext;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IEmailService _emailService;
        private readonly IWebHostEnvironment _environment;
        private readonly IConfiguration _configuration;
        private readonly IAdminService _adminService;
        private readonly IUserCompaniesServices _userCompaniesServices;
        private ILogger _logger = Log.ForContext<UserServices>();

        public UserServices(JobProDbContext _db, IHttpContextAccessor httpContextAccessor, UserManager<User> _usermanager, JobProDbContext subdomainSchemaContext, IAWSS3Sevices aWSS3Sevices, IEmailService emailService, IWebHostEnvironment environment, IConfiguration configuration, IAdminService adminService, IUserCompaniesServices userCompaniesServices)
        {
            publicSchemaContext = _db;
            _httpContextAccessor = httpContextAccessor;
            UserManager = _usermanager;
            this.subdomainSchemaContext = subdomainSchemaContext;
            _aWSS3Sevices = aWSS3Sevices;
            _emailService = emailService;
            _environment = environment;
            _configuration = configuration;
            _adminService = adminService;
            _userCompaniesServices = userCompaniesServices;
        }
        #endregion

        #region Check if a user with the jobProId exists
        //public async Task<bool> CheckIfUserWithProfileIdExists(string jobProId)
        //{
        //    var user = await this.publicSchemaContext.Users.FirstOrDefaultAsync(x => x.JobProId == jobProId);
        //    return user != null;
        //}
        #endregion

        #region Get Free plan Id
        public async Task<string> GetFreePlanId()
        {
            var plan = await publicSchemaContext.PricingPlans.FirstOrDefaultAsync(x => x.Name == "Free");
            return plan.Id.ToString();
        }
        #endregion

        #region Add email to wait list
        /// <summary>
        /// Add email to wait list
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<bool> AddEmailToWaitList(WaitListDto model)
        {
            if (await publicSchemaContext.WaitingEmailLists.AnyAsync(x => x.Email.ToLower().Equals(model.Email.ToLower())))
                throw new RecordAlreadyExistException("Email already part of our wait-list");

            TimeSpan time;
            if (!TimeSpan.TryParse(model.TimeToLaunch, CultureInfo.InvariantCulture, out time))
            {
                throw new Exception($"{model.TimeToLaunch} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
            }

            var list = new WaitingEmailList()
            {
                Email = model.Email,
                TimeToLaunch = time.ToString(),
                Applications = string.Join(" - ", model.Applications),
                CompanySize = model.CompanySize
            };

            publicSchemaContext.WaitingEmailLists.Add(list);
            var res = await publicSchemaContext.SaveChangesAsync();

            var apps = "";
            if (res > 0)
            {
                // Send mail to the supplied email
                var template = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/waiting-list-template.html"));
                var temp = model.Applications;
                if (temp.Count > 1)
                {
                    temp.Remove(model.Applications[model.Applications.Count - 1]);
                    apps = string.Join(", ", temp) + " and " + model.Applications[model.Applications.Count - 1];
                    template = template.Replace("{apps}", apps);
                }
                else
                {
                    template = template.Replace("{apps}", model.Applications[0]);
                    apps = model.Applications[0];
                }

                await _emailService.SendEmail(template, model.Email, "Exciting News! You're on the Waitlist for JobPro's Grand Launch");

                // Send mail to the admin
                var adminTemplate = File.ReadAllText(Path.Combine(_environment.WebRootPath, @"EmailTemplates/waitlist-admin-template.html"));
                adminTemplate = adminTemplate.Replace("{email}", model.Email).Replace("{apps}", apps);
                var emails = new List<string>() { "<EMAIL>", "<EMAIL>", "<EMAIL>" };
                emails.ForEach(email =>
                {
                    _emailService.SendEmail(adminTemplate, email, "Exciting News! Email Added To Jobpro WaitList");
                });
            }

            return res > 0;
        }
        #endregion

        #region Get Wait List
        public async Task<List<WaitingEmailList>> GetUserFromWaitList()
        {
            return await publicSchemaContext.WaitingEmailLists.ToListAsync();
        }
        #endregion

        #region Log Last Login
        public async Task<bool> LogLastLogin(string userId)
        {
            var lastLogin = new LoginLog()
            {
                UserId = userId,
                LastLoginDate = DateTime.UtcNow
            };
            await publicSchemaContext.LoginLogs.AddAsync(lastLogin);
            var result = await publicSchemaContext.SaveChangesAsync();
            return result > 0;
        }
        #endregion

        #region Generate QR Code for smart login
        /// <summary>
        /// Geeenrate QR Code for smart login
        /// </summary>
        /// <param name="browserId"></param>
        /// <param name="httpRequest"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GenerateQRCodeForSmartLogin(string browserId, HttpRequest httpRequest)
        {
            if (browserId.Length != 40)
            {
                _logger.Error("Invalid browser ID. Must be 40 characters.");
                return new GenericResponse
                {
                    ResponseCode = HttpStatusCode.BadRequest.ToString(),
                    ResponseMessage = "Invalid browser ID. Must be 40 characters."
                };
            }

            // Try to get the company logo from the subdomain
            string logoUrl = null;
            try
            {
                var subdomain = httpRequest.Headers["subdomain"].ToString();
                if (!string.IsNullOrEmpty(subdomain))
                {
                    var tenant = await publicSchemaContext.Tenants.FirstOrDefaultAsync(t => t.Subdomain == subdomain);
                    if (tenant != null && !string.IsNullOrEmpty(tenant.LogoUrl))
                    {
                        logoUrl = await _aWSS3Sevices.GetSignedUrlAsync(tenant.LogoUrl);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting company logo for QR code");
                // Continue without the logo if there's an error
            }

            using var qrGenerator = new QRCodeGenerator();
            var qrCodeData = qrGenerator.CreateQrCode(browserId, QRCodeGenerator.ECCLevel.Q);

            var qrCode = new PipelineMatrix(qrCodeData);
            var matrix = qrCode.Matrix;

            int moduleSize = 20;
            int margin = moduleSize * 2;
            int size = matrix.GetLength(0) * moduleSize + margin * 2;

            using var bitmap = new SKBitmap(size, size);
            using var canvas = new SKCanvas(bitmap);

            canvas.Clear(SKColors.White);

            // Create paint object for QR code modules
            using var paint = new SKPaint
            {
                Color = SKColors.Black,
                IsAntialias = true
            };

            // Draw QR code modules
            for (int x = 0; x < matrix.GetLength(0); x++)
            {
                for (int y = 0; y < matrix.GetLength(1); y++)
                {
                    if (matrix[x, y])
                    {
                        float xPos = x * moduleSize + margin;
                        float yPos = y * moduleSize + margin;
                        canvas.DrawRect(xPos, yPos, moduleSize, moduleSize, paint);
                    }
                }
            }

            // Add company logo at the center if available
            if (!string.IsNullOrEmpty(logoUrl))
            {
                try
                {
                    // Download the logo image
                    using var httpClient = new HttpClient();
                    var logoBytes = await httpClient.GetByteArrayAsync(logoUrl);

                    using var logoStream = new MemoryStream(logoBytes);
                    using var logoData = SKData.Create(logoStream);
                    using var logoImage = SKImage.FromEncodedData(logoData);

                    if (logoImage != null)
                    {
                        // Calculate logo size (20% of QR code size)
                        int logoSize = size / 5;

                        // Calculate center position
                        float centerX = size / 2 - logoSize / 2;
                        float centerY = size / 2 - logoSize / 2;

                        // Create a white background circle
                        using var bgPaint = new SKPaint
                        {
                            Color = SKColors.White,
                            IsAntialias = true
                        };

                        // Draw white background circle
                        float circleRadius = logoSize / 2 + (logoSize * 0.1f);
                        canvas.DrawCircle(size / 2, size / 2, circleRadius, bgPaint);

                        // Create a circular clip path for the logo
                        using var clipPath = new SKPath();
                        clipPath.AddCircle(size / 2, size / 2, logoSize / 2);

                        // Save the canvas state before applying the clip
                        canvas.Save();
                        canvas.ClipPath(clipPath);

                        // Create a high-quality paint for the logo
                        using var logoPaint = new SKPaint
                        {
                            FilterQuality = SKFilterQuality.High,
                            IsAntialias = true
                        };

                        // Draw the logo within the circular clip
                        var destRect = new SKRect(centerX, centerY, centerX + logoSize, centerY + logoSize);
                        canvas.DrawImage(logoImage, destRect, logoPaint);

                        // Restore the canvas state
                        canvas.Restore();
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error adding logo to QR code");
                    // If there's any error loading the logo, just continue without it
                }
            }

            // Convert to PNG bytes
            using var image = SKImage.FromBitmap(bitmap);
            using var data = image.Encode(SKEncodedImageFormat.Png, 100);
            var bytes = data.ToArray();

            // Convert to base64 string
            var base64String = Convert.ToBase64String(bytes);

            // Get browser info and add to the database
            var browserDetection = new BrowserDetection(httpRequest);
            var browserInfo = browserDetection.GetBrowserInfo();
            if (browserInfo.IsMobile || browserInfo.IsBot)
            {
                return await Task.FromResult(new GenericResponse
                {
                    ResponseMessage = "Invalid browser, request must come from a web browser on a PC",
                    ResponseCode = HttpStatusCode.BadRequest.ToString()
                });
            }

            var browserInfoAlreadyExist = await publicSchemaContext.BrowserInfos.AnyAsync(x => x.BrowserId == browserId);
            if (!browserInfoAlreadyExist)
            {
                var newBrowserInfo = new BrowserInfo
                {
                    BrowserId = browserId,
                    Name = browserInfo.Name,
                    Version = browserInfo.Version,
                    IsMobile = browserInfo.IsMobile,
                    IsBot = browserInfo.IsBot,
                    Platform = browserInfo.Platform
                };
                await publicSchemaContext.BrowserInfos.AddAsync(newBrowserInfo);
                await publicSchemaContext.SaveChangesAsync();
            }

            return await Task.FromResult(new GenericResponse
            {
                ResponseMessage = "QR Code generated successfully",
                Data = new
                {
                    QRCodeImage = $"data:image/png;base64,{base64String}",
                    Token = browserId
                },
                ResponseCode = HttpStatusCode.OK.ToString()
            });
        }
        #endregion

        #region Add or Update user status
        public async Task<ApiResponse<bool>> AddOrUpdateUserOnlineStatus(string userId, UserOnlineStatusOptions status)
        {
            var userProfile = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (userProfile is null)
            {
                return new ApiResponse<bool>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found",
                    Data = false
                };
            }

            userProfile.OnlineStatus = status;
            subdomainSchemaContext.UserProfiles.Update(userProfile);
            var result = await subdomainSchemaContext.SaveChangesAsync();

            return new ApiResponse<bool>
            {
                ResponseCode = "200",
                ResponseMessage = "User status updated successfully",
                Data = result > 0
            };
        }
        #endregion

        #region Get User Online Status
        public async Task<ApiResponse<string>> GetUserOnlineStatus(string userId)
        {
            var userProfile = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (userProfile is null)
            {
                return new ApiResponse<string>
                {
                    ResponseCode = "404",
                    ResponseMessage = "User not found",
                    Data = null
                };
            }

            return new ApiResponse<string>
            {
                ResponseCode = "200",
                ResponseMessage = "User status retrieved successfully",
                Data = userProfile.OnlineStatus.ToString()
            };
        }
        #endregion

        #region Add role to a user
        public async Task<bool> AddUserToRole(string userId, string role, string appName, string subdomain, string connectionStr)
        {
            var roleID = await subdomainSchemaContext.EmployeeRoles.Where(r => r.RoleName == role && r.PackageName == appName)
                .Select(x => x.Id).FirstOrDefaultAsync();
            if (roleID is not null)
            {
                // Check if the user has already been assigned to that role
                if (await subdomainSchemaContext.UserAndRoleIds.AnyAsync(x => x.UserProId == userId && x.RoleId == roleID))
                {
                    return true;
                }

                await subdomainSchemaContext.AddAsync(new UserAndRoleId
                {
                    UserProId = userId,
                    RoleId = roleID
                });

                return await subdomainSchemaContext.SaveChangesAsync() > 0;
            }

            return true;
        }
        #endregion

        #region Get role name by Id
        /// <summary>
        /// This method gets the role name using the role Id
        /// </summary>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public async Task<string> GetUserRoleName(string roleId)
        {
            return await subdomainSchemaContext.EmployeeRoles
                .Where(x => x.Id == roleId)
                .Select(role => role.RoleName).FirstOrDefaultAsync();
        }
        #endregion

        #region Delete user role
        public async Task<bool> DeleteUserRole(string userId, string role, string appName)
        {
            var roleID = await subdomainSchemaContext.EmployeeRoles.Where(r => r.RoleName == role && r.PackageName == appName)
                .Select(x => x.Id).FirstOrDefaultAsync();
            if (roleID is not null)
            {
                var userRole = await subdomainSchemaContext.UserAndRoleIds.FirstOrDefaultAsync(x => x.UserProId == userId && x.RoleId == roleID);
                if (userRole is not null)
                {
                    subdomainSchemaContext.UserAndRoleIds.Remove(userRole);
                    return await subdomainSchemaContext.SaveChangesAsync() > 0;
                }
            }
            return true;
        }
        #endregion

        #region Get user roles and permissions
        public async Task<GenericResponse> GetUserRolesAndPermissions(string userId, string appName)
        {
            var userRoleIds = await subdomainSchemaContext.UserAndRoleIds
                .Where(r => r.UserProId == userId).Select(x => x.RoleId).ToListAsync();
            var userRoleId = await subdomainSchemaContext.EmployeeRoles.FirstOrDefaultAsync(x => userRoleIds.Contains(x.Id) && x.PackageName == appName);

            var userPermissionIds = await subdomainSchemaContext.EmployeeRolesPermissions
                .Where(r => r.RoleId == userRoleId.Id).Select(x => x.PermissionId).ToListAsync();
            var roleName = await subdomainSchemaContext.EmployeeRoles
                .Where(role => role.Id == userRoleId.Id).FirstOrDefaultAsync();
            var userPermissions = await subdomainSchemaContext.EmployeePermissions.Where(x => userPermissionIds.Contains(x.Id))
                .ToListAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Roles and permission retrived successfully",
                Data = new
                {
                    Role = roleName,
                    Permissions = userPermissions
                }
            };
        }
        #endregion

        #region Get All Users from all companies
        /// <summary>
        /// Gets all the users from all the companies
        /// </summary>
        /// <param name="region"></param>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="searchparam"></param>
        /// <returns></returns>
        public async Task<Page<User>> GetAllUsers(Enums.Region region, int pageNumber, int pageSize, string searchparam = null)
        {

            var role = publicSchemaContext.Roles.FirstOrDefault(x => x.Name == "Member");//.Where(z => z.Name == "Member").Select(z => z.Id).FirstOrDefault();
            var userid = publicSchemaContext.UserRoles.Where(z => z.RoleId == role.Id)
                                          .Select(z => z.UserId)
                                          .ToList();

            var user = publicSchemaContext.Users.Where(x => userid.Contains(x.Id) && x.Region == region.ToString());

            if (searchparam != null)
            {
                //query = query.Where(x => EF.Functions.Like(x.Advice.MerchantRef, $"%{searxh}%"));
                user = user.Where(x => EF.Functions.Like(x.UserName, $"%{searchparam}%") ||
                                       EF.Functions.Like(x.FirstName, $"%{searchparam}%") ||
                                       EF.Functions.Like(x.FirstName, $"%{searchparam}%"));

            };

            var res = await user.ToPageListAsync(pageNumber, pageSize);
            return res;
        }
        #endregion

        #region Update user type
        /// <summary>
        /// Update user type
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="usertype"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<bool> UpdateUserType(string userid, UserTypes usertype)
        {
            var user = await publicSchemaContext.Users.FirstOrDefaultAsync(x => x.Id == userid);
            if (user == null)
            {
                throw new Exception("User not found");
            }

            user.UserType = usertype;
            publicSchemaContext.Users.Update(user);
            var res = await publicSchemaContext.SaveChangesAsync();
            return res > 0;
        }
        #endregion

        #region Get User by email
        /// <summary>
        /// Get User by email
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        public async Task<UserProfile> GetUserByEmail(string email)
        {
            var userProfile = await subdomainSchemaContext.UserProfiles
                .FirstOrDefaultAsync(x => x.Email.ToLower() == email.ToLower());

            if (!string.IsNullOrEmpty(userProfile?.ProfilePictureUrl))
                userProfile.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(userProfile.ProfilePictureUrl);

            return userProfile;
        }
        #endregion

        #region Activate/Deactivate or Delete an Individual User Account
        public async Task<GenericResponse> ActivateDeactivateOrDeleteIndUserAcct(string userId, IndividualUserAccountStatus status)
        {
            var user = await publicSchemaContext.Users.FirstOrDefaultAsync(x => x.Id == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found. Wrong user Id");

            user.IndividualUserAccountStatus = status;
            publicSchemaContext.Users.Update(user);
            var dbResult = await publicSchemaContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = dbResult > 0 ? "200" : "500",
                ResponseMessage = dbResult > 0 ? "Status updated successfully" : "Failed to update user",
                Data = null
            };
        }
        #endregion

        #region Get User Profile by id
        /// <summary>
        /// Get User Profile by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<UserProfile> GetUserProfileById(string id, JobProDbContext context = null)
        {
            UserProfile userProfile = null;
            if (context is not null)
                userProfile = await context.UserProfiles
                    .FirstOrDefaultAsync(x => x.UserId == id);
            else
                userProfile = await subdomainSchemaContext.UserProfiles
                    .FirstOrDefaultAsync(x => x.UserId == id);

            if (!string.IsNullOrEmpty(userProfile?.ProfilePictureUrl))
                userProfile.ProfilePictureUrl = await _aWSS3Sevices.GetSignedUrlAsync(userProfile.ProfilePictureUrl);

            return userProfile;
        }
        #endregion

        #region Get User Details by Id
        /// <summary>
        /// Get User Details by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<User> GetUserById(string id)
        {
            return await publicSchemaContext.Users.FindAsync(id);
        }
        #endregion

        #region Get User by phone number
        public async Task<bool> CheckIfUserExistByPhoneNumber(string phoneNumber)
        {
            return await publicSchemaContext.Users
                .AnyAsync(x => x.PhoneNumber == phoneNumber);
        }
        #endregion

        #region Assign role and permissions to a jobproject User during onboarding
        /// <summary>
        /// Assign role and permissions to a jobproject User during onboarding
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="role"></param>
        /// <param name="subdomain"</param>
        /// <returns></returns>
        public async Task<bool> AssignRoleAndPermissionsToUser(string userid, string role, string subdomain)
        {
            var roleid = subdomainSchemaContext.JobProjectRoles.Where(z => z.Name.ToLower() == role.ToLower())
                .Select(z => z.Id).FirstOrDefault();

            var table = subdomain + ".JobProjectUserRoles";
            await subdomainSchemaContext.Database.ExecuteSqlRawAsync($"INSERT INTO {table} VALUES(@rolesId, @userId)",
                               new SqlParameter("rolesId", roleid),
                               new SqlParameter("userId", userid));

            // Get the permissions attached to the role and assign to the user
            var tableRolesPermission = subdomain + ".JobProjectRolesPermissions";
            var permissions = await subdomainSchemaContext.JobProjectUserPermissions
                .FromSqlRaw($"SELECT * FROM {table} WHERE UsersId = @userId ",
                    new SqlParameter("userId", userid)).ToListAsync();

            foreach (var permission in permissions)
            {
                var tableUserPermissions = subdomain + ".JobProjectUserPermissions";
                await subdomainSchemaContext.Database.ExecuteSqlRawAsync($"INSERT INTO {table} VALUES(@permissionsId, @userId)",
                               new SqlParameter("permissionsId", permission.PermissionsId),
                               new SqlParameter("userId", userid));
            }

            return true;
        }
        #endregion

        #region Generate Reference
        public string GenerateRef()
        {
            Random rand = new Random();
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            var firstchars = new string(Enumerable.Repeat(chars, 2)
                .Select(s => s[rand.Next(s.Length)]).ToArray());
            var secondchars = new string(Enumerable.Repeat(chars, 2)
                .Select(s => s[rand.Next(s.Length)]).ToArray());
            var firstints = rand.Next(0, 999);
            var secondints = rand.Next(0, 99);
            var newRef = firstchars + firstints.ToString() + secondchars + secondints;
            if (CheckRefExists(newRef)) return GenerateRef();
            else return newRef;
        }
        #endregion

        #region Check Reference Exists
        public bool CheckRefExists(string refe)
        {
            var user = publicSchemaContext.Users.Where(z => z.NewReference == refe).FirstOrDefault();
            if (user != null) return true;
            else return false;
        }
        #endregion

        #region Save User
        public async Task<bool> SaveUser(User user)
        {
            await publicSchemaContext.AddAsync(user);
            int result = await publicSchemaContext.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }
        #endregion

        #region Update User
        public async Task<bool> UpdateUser(User user)
        {
            publicSchemaContext.Users.Update(user);
            int result = await publicSchemaContext.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }
        #endregion

        #region Authenticate Refresh Token
        public async Task<UserRefreshToken> AuthenticateRefreshToken(string token, string ipAddress)
        {
            var refreshToken = await publicSchemaContext.RefreshTokens
                .Where(x => x.Token == token && x.CreatedByIp == ipAddress).FirstOrDefaultAsync();
            if (refreshToken is not null)
            {
                if (!refreshToken.IsActive) { return null; } else { return refreshToken; }
            }

            return null;
        }
        #endregion

        #region Revoke RefreshToken
        public async Task<bool> RevokeToken(string ipAddress)
        {
            var tokenToRevoke = await publicSchemaContext.RefreshTokens.SingleOrDefaultAsync(x => x.CreatedByIp == ipAddress);
            if (tokenToRevoke is not null)
            {
                tokenToRevoke.Revoked = DateTime.UtcNow;
                tokenToRevoke.RevokedByIp = ipAddress;
                publicSchemaContext.RefreshTokens.Update(tokenToRevoke);
                await publicSchemaContext.SaveChangesAsync();

                return true;
            }

            return false;
        }
        #endregion

        #region Black list a token
        public async Task<bool> BlacklistJwtToken(string token)
        {
            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            var jwtHandler = new JwtSecurityTokenHandler();
            var jwtToken = jwtHandler.ReadToken(token) as JwtSecurityToken;

            if (jwtToken == null)
            {
                return false;
            }

            var expirationDate = jwtToken.ValidTo;
            if (expirationDate <= DateTime.UtcNow)
            {
                return false;
            }

            var blacklistedToken = new BlacklistedToken
            {
                Token = token
            };

            publicSchemaContext.BlacklistedTokens.Add(blacklistedToken);
            await publicSchemaContext.SaveChangesAsync();

            return true;
        }
        #endregion

        #region Get User By RefreshToken
        public async Task<User> GetUserByRefreshToken(string refreshToken)
        {
            var userId = await publicSchemaContext.RefreshTokens.Where(x => x.Token == refreshToken).Select(x => x.UserId).FirstOrDefaultAsync();
            return await publicSchemaContext.Users.FindAsync(userId);
        }
        #endregion

        #region Add or Update RefreshToken
        public async Task AddOrUpdateRefreshToken(UserRefreshToken refreshToken)
        {
            var existingToken = publicSchemaContext.RefreshTokens
                .SingleOrDefault(x => x.UserId == refreshToken.UserId && x.CreatedByIp == refreshToken.CreatedByIp);
            if (existingToken is not null)
            {
                existingToken.Revoked = null;
                existingToken.RevokedByIp = null;
                existingToken.Token = refreshToken.Token;
                existingToken.Created = refreshToken.Created;
                existingToken.CreatedByIp = refreshToken.CreatedByIp;
                existingToken.Expires = refreshToken.Expires;
                publicSchemaContext.RefreshTokens.Update(existingToken);
            }
            else
                publicSchemaContext.RefreshTokens.Add(refreshToken);

            await publicSchemaContext.SaveChangesAsync();
        }
        #endregion

        #region Get User Ip Address
        [Obsolete]
        public string GetIp(IPAddress remoteIpAddress)
        {
            // Get host name with out using Dns
            string hostName = System.Net.NetworkInformation.IPGlobalProperties.GetIPGlobalProperties().HostName;
            IPHostEntry host = Dns.GetHostByName(hostName);

            return host
                .AddressList
                .FirstOrDefault(ip => ip.AddressFamily == AddressFamily.InterNetwork).ToString();
        }
        #endregion

        #region Validate User Ip Address
        public async Task<bool> ValidateUserIp(User user, string ipAdress)
        {
            if (ipAdress != user.IpAddress && user.lockIpAddress == true)
            {
                return false;
            }
            return true;
        }
        #endregion

        #region Get User Companies
        public async Task<IEnumerable<UserCompanies>> GetUserCompanies(string userId)
        {
            var userWorkSpaces = await publicSchemaContext.UserCompanies.Where(x => x.Active && x.UserId == userId)
                .OrderByDescending(x => x.DateCreated)
                .Include(x => x.tenant)
                .ToListAsync();

            // Get company's logo
            foreach (var userCompany in userWorkSpaces)
            {
                userCompany.tenant.LogoUrl = await _aWSS3Sevices.GetSignedUrlAsync(userCompany.tenant.LogoUrl);
                userCompany.LastVisited = await _userCompaniesServices.GetDefaultCompany(userId);
            }

            return userWorkSpaces;
        }
        #endregion

        #region Lock User Ip Address
        public async Task<bool> lockUserIp(string userId, bool ipUpdate = true)
        {
            var user = await publicSchemaContext.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();
            user.lockIpAddress = ipUpdate;
            publicSchemaContext.Users.Update(user);
            var result = await publicSchemaContext.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }
        #endregion

        #region Save User Profile
        public async Task<bool> SaveNewUserProfile(User user)
        {
            // First check if a profile already exists for the user
            var userProfile = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == user.Id);
            if (userProfile != null)
                return true;

            userProfile = new UserProfile()
            {
                DateCreated = DateTime.UtcNow,
                Email = user.Email,
                FirstName = user.FirstName,
                LastName = user.LastName,
                MiddleName = user.MiddleName,
                PhoneNumber = user.PhoneNumber,
                UserId = user.Id,
                Id = Guid.NewGuid().ToString(),
                Country = user.Country,
                EraseAcitivity = EraseAcitivity.After1Month,
                EventCategory = EventCategory.All.ToString(),
            };
            await subdomainSchemaContext.AddAsync(userProfile);
            int result = await subdomainSchemaContext.SaveChangesAsync();
            if (result > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion

        #region Get User By Microsoft Graph Access Token
        public async Task<User> GetUserByMicrosoftGraphAccessToken(MicrosoftAuthModel model)
        {
            var microsoftUser = await Utils.Utility.GetMicrosoftUserModelAsync(model.MicrosoftAccessToken);
            if (microsoftUser != null && microsoftUser.Sub != model.MicrosoftAuthId)
            {
                throw new Exception("Invalid action, Account mismatched.");
            }
            // verify user account exists or not
            var user = publicSchemaContext.Users.FirstOrDefault(x => x.Email == model.Email);
            return user;
        }
        #endregion

        #region Get Team Members
        /// <summary>
        /// Get Team Members
        /// </summary>
        /// <param name="nameParam"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<List<UserMDVm>> GetTeamMembers(string nameParam, string subdomain)
        {
            var users = new List<UserProfile>();
            var userVm = new List<UserMDVm>();
            if (nameParam == null)
            {
                users = await subdomainSchemaContext.UserProfiles.ToListAsync();
            }
            else
                users = await subdomainSchemaContext.UserProfiles.Where(x => x.FirstName.ToLower().Contains(nameParam.ToLower()) || x.LastName.ToLower().Contains(nameParam.ToLower())).ToListAsync();

            // Get company industry from tenant details
            var tenant = await publicSchemaContext.Tenants
                .Where(x => x.Subdomain == subdomain)
                .Select(x => new { x.Industry, x.Id }).FirstOrDefaultAsync();

            userVm = users.Select(x => new UserMDVm()
            {
                Id = x.UserId,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Bio = x.Bio,
                Country = x.Country,
                PhoneNumber = x.PhoneNumber,
                Address = x.Address,
                State = x.State,
                Email = x.Email,
                ProfilePictureUrl = x.ProfilePictureUrl,
                TimeZone = x.TimeZone,
                IsVerified = x.IsVerified,
                Designation = x.Designation,
                Industry = tenant.Industry.ToString()
            }).ToList();

            if (userVm.Count > 0)
            {
                foreach (var user in userVm)
                {
                    user.ProfilePictureUrl = user.ProfilePictureUrl is not null ? await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl) : null;

                    // Get user role
                    user.RoleName = await _adminService.GetUserRole(user.Id, subdomain);

                    // Get user skills
                    user.Skills = publicSchemaContext.UserSkills
                        .Where(x => x.UserId == user.Id)
                        .AsEnumerable()
                        .GroupBy(x => x.Category)
                        .ToDictionary(g => g.Key, g => g.ToList());
                }
            }

            return userVm.OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName).ToList(); ;
        }
        #endregion

        #region Get Team Member Details
        public async Task<GenericResponse> GetTeamMemberDetails(List<string> userIds, string subdomain)
        {
            var userVm = new List<UserMDVm>();
            var users = await subdomainSchemaContext.UserProfiles.Where(x => userIds.Contains(x.UserId)).ToListAsync();

            userVm = users.Select(x => new UserMDVm()
            {
                Id = x.UserId,
                FirstName = x.FirstName,
                LastName = x.LastName,
                Bio = x.Bio,
                Country = x.Country,
                PhoneNumber = x.PhoneNumber,
                Address = x.Address,
                State = x.State,
                Email = x.Email,
                ProfilePictureUrl = x.ProfilePictureUrl,
                TimeZone = x.TimeZone,
                IsVerified = x.IsVerified,
                Designation = x.Designation
            }).ToList();

            if (userVm.Count > 0)
            {
                foreach (var user in userVm)
                {
                    user.ProfilePictureUrl = user.ProfilePictureUrl is not null ? await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl) : null;

                    // Get user role
                    user.RoleName = await _adminService.GetUserRole(user.Id, subdomain);

                    // Get user skills
                    user.Skills = publicSchemaContext.UserSkills
                        .Where(x => x.UserId == user.Id)
                        .AsEnumerable()
                        .GroupBy(x => x.Category)
                        .ToDictionary(g => g.Key, g => g.ToList());
                }
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Team members retrieved successfully",
                Data = userVm.OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName).ToList()
            };
        }
        #endregion

        #region Get Countries
        public async Task<List<Country>> GetCountries()
        {
            // Get countries from countries enum
            var countriesEnnum = Enum.GetValues(typeof(Countries)).Cast<Countries>().ToList();
            var countries = new List<Country>();
            foreach (var country in countriesEnnum)
            {
                FieldInfo fieldInfo = country.GetType().GetField(country.ToString());

                if (fieldInfo != null)
                {
                    DescriptionAttribute[] attributes = fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false) as DescriptionAttribute[];
                    if (attributes != null && attributes.Length > 0)
                    {
                        countries.Add(new Country()
                        {
                            Name = attributes[0].Description,
                            Code = country.ToString()
                        });
                    }
                }
            }

            return countries;
        }
        #endregion

        #region Get User's Subdomain Using Email Address
        public async Task<ApiResponse<string>> GetUserSubdomain(string email)
        {
            string subdomain = null;
            var userCompany = await publicSchemaContext.UserCompanies
                .Include(x => x.tenant)
                .FirstOrDefaultAsync(x => x.Email == email);

            subdomain = userCompany?.tenant?.Subdomain;

            if (subdomain is null)
            {
                var tenantId = await publicSchemaContext.CompanyUserInvites.Where(x => x.Email == email)
                    .Select(x => x.TenantId).FirstOrDefaultAsync();
                tenantId = tenantId ?? userCompany?.TenantId.ToString();

                subdomain = await publicSchemaContext.Tenants.Where(x => x.Id.ToString() == tenantId)
                    .Select(x => x.Subdomain).FirstOrDefaultAsync();
            }

            return new ApiResponse<string>
            {
                Data = subdomain,
                ResponseMessage = "Subdomain retrieved successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Get all users
        public async Task<List<UserMDVm>> GetAllUsers(string personalEmail = null)
        {
            var userVms = new List<UserMDVm>();
            var users = new List<User>();

            if (!string.IsNullOrEmpty(personalEmail))
                users = await publicSchemaContext.Users
                    .Where(x => x.Email.ToLower() == personalEmail.ToLower()).ToListAsync();
            else
                users = await publicSchemaContext.Users.ToListAsync();

            foreach (var user in users)
            {
                var userVm = user.MapToUserMDVm();
                var userCompanies = await publicSchemaContext.UserCompanies.Where(x => x.UserId == user.Id).FirstOrDefaultAsync();
                if (userCompanies is not null)
                {
                    var subdomain = await publicSchemaContext.Tenants.Where(x => x.Id == userCompanies.TenantId).Select(x => x.Subdomain).FirstOrDefaultAsync();
                    userVm.CompanyEmail = userCompanies.Email;
                    userVm.TenantId = userCompanies.TenantId.ToString();
                    userVm.Subdomain = subdomain;
                }

                userVms.Add(userVm);
            }

            return userVms;
        }
        #endregion

        #region Service To Service Auth Method
        public async Task<GenericResponse> InternalServiceLogin(GrpcAuthenticateRequest request)
        {
            // Check if the service exists and that the key is correct
            var service = await publicSchemaContext.MicroServices
                .Where(x => x.ServiceId.ToLower() == request.ServiceId.ToLower()).FirstOrDefaultAsync();
            if (service == null)
            {
                _logger.Error($"Service not found- {request.ServiceId}");
                return new GenericResponse
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "Service not found",
                };
            }

            if (string.IsNullOrEmpty(request.SecretKey) || service.Key != request.SecretKey)
            {
                _logger.Error($"Invalid key- {request.ServiceId}");
                return new GenericResponse
                {
                    Data = null,
                    ResponseCode = "401",
                    ResponseMessage = "Invalid key",
                };
            }

            var token = GenerateToken(_configuration, request.ServiceId);
            return new GenericResponse
            {
                Data = new GrpcAuthenticationResponse
                {
                    Code = 200,
                    Message = "Success",
                    Token = token.ToString(),
                    ServiceId = request.ServiceId,
                    ServiceName = service.ServiceName,
                },
                ResponseCode = "200",
                ResponseMessage = "Success",
            };
        }
        #endregion

        #region Generate Token for service to service auth
        private object GenerateToken(IConfiguration configuration, string serviceId)
        {
            var issuer = Environment.GetEnvironmentVariable("JOBPRO_GRPC_JWT_ISSSUER") ?? configuration["Jwt:Grpc:Issuer"];
            var audience = Environment.GetEnvironmentVariable("JOBPRO_GRPC_JWT_AUDIENCE") ?? configuration["Jwt:Grpc:Audience"];
            var secretKey = Environment.GetEnvironmentVariable("JOBPRO_GRPC_JWT_KEY") ?? configuration["Jwt:Grpc:Key"];
            var expiresIn = Environment.GetEnvironmentVariable("JOBPRO_GRPC_JWT_EXPIRESIN") ?? configuration["Jwt:Grpc:ExpiryInMinutes"];

            var claims = new List<Claim>
            {
                new Claim("serviceId", serviceId),
            };

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.UtcNow.AddDays(24);

            var token = new JwtSecurityToken(
                issuer,
                audience,
                claims,
                expires: expires,
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
        #endregion

        #region Get Micro services
        public async Task<GenericResponse> GetAllServices()
        {
            var response = await publicSchemaContext.MicroServices.ToListAsync();

            return new GenericResponse
            {
                Data = response,
                ResponseCode = "200",
                ResponseMessage = "Success",
            };
        }
        #endregion

        #region Add Or Update User Skills
        /// <summary>
        /// Add or Update User Skills
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> AddOrUpdateUserSkills(AddOrUpdateSkillsDto model)
        {
            foreach (var item in model.CategoryAndkills)
            {
                foreach (var skill in item.Skills)
                {
                    var userSkills = await publicSchemaContext.UserSkills
                        .Where(x => x.UserId == model.UserId && x.Skill == skill && x.Category == item.Category.ToString()).FirstOrDefaultAsync();

                    if (userSkills is null)
                    {
                        userSkills = new UserSkill()
                        {
                            UserId = model.UserId,
                            Category = item.Category.ToString(),
                            Skill = skill,
                        };
                        await publicSchemaContext.AddAsync(userSkills);
                    }
                    else
                    {
                        userSkills.Category = item.Category.ToString();
                        userSkills.Skill = skill;
                        userSkills.UpdatedOn = DateTime.UtcNow;
                        publicSchemaContext.Update(userSkills);
                    }
                }
            }

            var result = await publicSchemaContext.SaveChangesAsync();

            var addedSkills = await publicSchemaContext.UserSkills
                .Where(x => x.UserId == model.UserId)
                .ToListAsync();

            return new GenericResponse
            {
                ResponseCode = result > 0 ? "200" : "500",
                ResponseMessage = result > 0 ? "Success" : "Operation failed",
                Data = result > 0 ? addedSkills : null
            };
        }
        #endregion

        #region Get User Skills
        /// <summary>
        /// Get User Skills
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetUserSkills(string userId)
        {
            var userSkills = await publicSchemaContext.UserSkills
                .Where(x => x.UserId == userId)
                .ToListAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = userSkills
            };
        }
        #endregion

        #region Delete User Skills
        /// <summary>
        /// Delete User Skills
        /// </summary>
        /// <param name="skillIds"></param>
        /// <returns></returns>
        public async Task<GenericResponse> DeleteUserSkills(List<string> skillIds)
        {
            var userSkills = await publicSchemaContext.UserSkills
                .Where(x => skillIds.Contains(x.Id.ToString()))
                .ToListAsync();

            var result = 0;
            if (userSkills.Any())
            {
                publicSchemaContext.UserSkills.RemoveRange(userSkills);
                result = await publicSchemaContext.SaveChangesAsync();
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = userSkills
            };
        }
        #endregion

        #region Check if jobProId exists
        public async Task<bool> CheckIfJobProIdExist(string jobproId)
        {
            var user = await publicSchemaContext.Users.FirstOrDefaultAsync(x => x.JobProId.ToLower() == jobproId.ToLower());
            return user != null;
        }
        #endregion

        #region Request For Account Deletion
        public async Task<GenericResponse> RequestForAccountDeletion(RequestForAccountDeletionDto model)
        {
            // Check if the company have an existing request
            var existingRequest = await subdomainSchemaContext.AccountDeletionRequests
                .FirstOrDefaultAsync(x => x.UserId == model.RequestedBy && x.Status == AccountDeletionStatus.Pending);
            if (existingRequest != null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request already exists", ResponseMessage = "You have an existing request. Please wait for it to be processed." };

            var loggedInUser = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == model.RequestedBy); if (loggedInUser == null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "User not found", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE };

            var tenant = await subdomainSchemaContext.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == model.TenantId);
            if (tenant == null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Tenant not found", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE};

            var reqId = Utility.RandomString(15);
            var request = new AccountDeletionRequest
            {
                UserId = model.RequestedBy,
                Reason = model.Reason,
                RequestId = reqId
            };

            await this.subdomainSchemaContext.AccountDeletionRequests.AddAsync(request);
            var result = await this.subdomainSchemaContext.SaveChangesAsync() > 0;

            if (result)
            {
                // Get admin details
                var admin = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == tenant.AdminId);

                // Send an email notification to the user
                var parameters = new Dictionary<string, string>
                {
                    { "{companyName}", tenant.CompanyName.CapitalizeFirstLetterOfEachWord() },
                    { "{requestId}", reqId },
                    { "{requestDate}", DateTime.UtcNow.ToShortDateString() },
                    { "{reason}", model.Reason ?? "No reason was provided" },
                    { "{name}", loggedInUser.FirstName + " " + loggedInUser.LastName },
                    { "{cancelLink}", Utility.Constants.ADMIN_URL }
                };

                var template = Utils.Extensions.UpdateTemplateWithParams("account_deletion_req_user", _environment, parameters);
                var subject = "Account Deletion Request Confirmation";
                await _emailService.SendEmail(template, loggedInUser.Email, subject);

                // Send an email notification to the admin
                var templateAdmin = Utils.Extensions.UpdateTemplateWithParams("account_deletion_req_admin", _environment, parameters);
                var subjectAdmin = "Account Deletion Request";
                await _emailService.SendEmail(templateAdmin, "<EMAIL>", subjectAdmin);
                await _emailService.SendEmail(templateAdmin, admin.Email, subjectAdmin);

                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Request for account deletion has been received. We will get back to you shortly." };
            }

            return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request not saved", ResponseMessage = "Something went wrong, please try again later" };
        }
        #endregion

        #region Cancel Account Deletion Request
        public async Task<GenericResponse> CancelAccountDeletion(string requestId)
        {
            var request = await subdomainSchemaContext.AccountDeletionRequests.FirstOrDefaultAsync(x => x.RequestId == requestId);
            if (request == null)
                return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request not found", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE };

            request.Status = AccountDeletionStatus.Cancelled;
            request.UpdatedOn = DateTime.UtcNow;
            subdomainSchemaContext.AccountDeletionRequests.Update(request);
            var result = await subdomainSchemaContext.SaveChangesAsync() > 0;

            if (result)
            {
                return new GenericResponse { ResponseCode = "200", ResponseMessage = "Request for account deletion has been cancelled." };
            }

            return new GenericResponse { ResponseCode = "400", DevResponseMessage = "Request not saved",
                ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE };
        }
        #endregion

        #region Get Company Deletion Requests
        public async Task<GenericResponse> GetCompanyDeletionRequests(string userId)
        {
            var requests = await subdomainSchemaContext.AccountDeletionRequests
                .Where(x => x.UserId == userId && x.Status == AccountDeletionStatus.Pending).ToListAsync();
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Account deletion request retrieved successfully.",
                Data = requests
            };
        }
        #endregion
    }
}

