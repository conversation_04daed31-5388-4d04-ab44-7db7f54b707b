using Jobid.App.Helpers;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Contract
{
    public interface IPaymentService
    {
        Task<string> InitiateStripePaymentAsync(decimal amount, string currency, string subdomain);
        Task<string> InitiateMolliePaymentAsync(decimal amount, string currency, string subdomain);
        Task<GenericResponse> VerifyStripePaymentAsync(string json, string signature);
        Task<GenericResponse> VerifyMolliePaymentAsync(string paymentId);
        Task<GenericResponse> GetPaymentStatusAsync(string paymentIntentId);
        Task<GenericResponse> GetWalletTransactionsByPaymentMethodAsync(AdminConsole.Enums.PaymentMethod paymentMethod, int pageSize = 10, int pageNumber = 1);
        Task<GenericResponse> InitiateWalletFundingAsync(decimal amount, string currency, AdminConsole.Enums.PaymentProvider provider, string subdomain);
    }
}
