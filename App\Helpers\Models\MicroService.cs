﻿using System;

namespace Jobid.App.Helpers.Models
{
    public class MicroService
    {
        public string Id { get; set; }
        public string ServiceId { get; set; }
        public string ServiceName { get; set; }
        public string Key { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }

        public MicroService()
        {
            Id = Guid.NewGuid().ToString();
            CreatedOn = DateTime.UtcNow;
        }
    }
}
