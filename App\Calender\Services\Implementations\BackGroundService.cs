﻿using Hangfire;
using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.Models;
using Jobid.App.Calender.Models.AI.Requests;
using Jobid.App.Calender.Models.AI.Responses;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using RestSharp;
using Serilog;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.Calender.Services.Implementations.CalenderService;

namespace Jobid.App.Calender.Services.Implementations
{
    public class BackGroundService : IBackGroundService
    {
        private readonly IEmailService _emailService;
        private readonly IConfiguration _config;
        private readonly JobProDbContext Db;
        private readonly string _conString;
        private readonly IApiCallService _apiCallService;
        private readonly IServiceScopeFactory _serviceProvider;
        private ILogger _logger = Log.ForContext<BackGroundService>();

        public BackGroundService(IEmailService emailService, JobProDbContext _db, IConfiguration config, IApiCallService apiCallService, IServiceScopeFactory serviceProvider)
        {
            _emailService = emailService;
            _config = config;
            Db = _db;
            _conString = GlobalVariables.ConnectionString;
            _apiCallService = apiCallService;
            _serviceProvider = serviceProvider;
        }

        #region Email Reminder Before
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task EmailReminderBefore(Guid bookedExternalMeetId, DateTime bookedTime, string subdomain, SendMailRecord record)
        {
            // Get the current selected date and time from the database
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var bookedMeet = await context.BookedExternalMeeting.FirstOrDefaultAsync(x => x.Id == bookedExternalMeetId);

            if (bookedMeet != null && bookedTime == bookedMeet.SelectedDateAndTime && !bookedMeet.IsCancelled)
            {
                SendBookingComfirmationEmail(record);
            }
        }
        #endregion

        #region Update ExternalMeeting TimeBreakDown
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task UpdateExternalMeetingTimeBreakDown(List<PersonalSchedule> personalSchedules, string subdomain, DateTime canBookEndDate)
        {
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            foreach (var personalSch in personalSchedules)
            {
                personalSch.TimeBreakDown = null;
                personalSch.SelectedTimeSlots = null;
                context.PersonalSchedule.Update(personalSch);
            }

            await context.SaveChangesAsync();
        }
        #endregion

        #region Email Reminder After
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task EmailReminderAfter(string email, ExternalMeeting externalMeeting, BookedExternalMeeting bookedExternalMeet, DateTime bookedTime, string subdomain, bool isReschduled = false)
        {
            // Get the current selected date and time from the database
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var bookedMeet = await context.BookedExternalMeeting.FirstOrDefaultAsync(x => x.Id == bookedExternalMeet.Id);

            if (bookedMeet != null && bookedTime == bookedMeet.SelectedDateAndTime && !bookedMeet.IsCancelled)
            {
                var html = $"Thank you for attending, we are really glad that you joined us";

                await _emailService.SendEmail(html, email, $"External {externalMeeting.ExternalMeetingType} Meeting Follow Up");
            }
        }
        #endregion

        #region Send Notifications For Reoccurring Meetings
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task SendNotificationsForReoccurringMeetings(string template, string email, string meetingId, string subdomain)
        {
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var meeting = await context.CalenderMeetings.Where(x => x.Id.ToString() == meetingId).FirstOrDefaultAsync();
            var meetingDates = await context.SubsequentMeetings.Where(x => x.CalenderMeetingId.ToString() == meetingId && x.NotificationSent == false && !x.IsCanceled && x.SubsequentMeetingDateTime.Date >= DateTime.UtcNow.Date).Take(7).OrderBy(d => d.SubsequentMeetingDateTime).ToListAsync();
            if (meeting == null || !meetingDates.Any())
            {
                RecurringJob.RemoveIfExists(meetingId);
                return;
            }

            var remindMeIn = meetingDates[0].NotifyMembersIn;
            var jobId = "";
            var title = $"Reminder - Meeting Invitation: {meeting.Name}";
            if (meeting.CustomFrequency is null)
            {
                if (!meeting.IsCancelled)
                {

                    jobId = BackgroundJob.Schedule(() => _emailService.SendEmail(template, email, title), meetingDates[0].SubsequentMeetingDateTime.AddMinutes(-remindMeIn));
                    meetingDates[0].NotificationSent = true;
                    context.SubsequentMeetings.Update(meetingDates[0]);
                }
                else
                    RecurringJob.RemoveIfExists(meetingId);
            }
            else
            {
                var customFrequency = meeting.CustomFrequency;
                var repeatOn = customFrequency.RepeatOn.Split(',');
                var repeatEvery = customFrequency.RepeatEvery;
                var repeatCount = customFrequency.RepeatCount;
                var endsOn = customFrequency.EndsOn;
                var endStatus = customFrequency.EndStatus;
                var endsAfter = customFrequency.EndsAfter;
                if (!meeting.IsCancelled)
                {
                    template = template.Replace("{date}", meetingDates[0].SubsequentMeetingDateTime.ToShortDateString()).Replace("{time}", meetingDates[0].SubsequentMeetingDateTime.ToShortTimeString());
                    if (endsOn != null)
                    {
                        if (endsOn >= DateTime.UtcNow)
                        {
                            jobId = BackgroundJob.Schedule(() => _emailService.SendEmail(template, email, title), meetingDates[0].SubsequentMeetingDateTime.AddMinutes(-remindMeIn));
                            meetingDates[0].NotificationSent = true;
                            context.SubsequentMeetings.Update(meetingDates[0]);
                        }
                        else
                        {
                            RecurringJob.RemoveIfExists(meetingId);
                        }
                    }
                    else if (endsAfter is not null)
                    {
                        if (endsAfter >= repeatCount)
                        {
                            jobId = BackgroundJob.Schedule(() => _emailService.SendEmail(template, email, title), meetingDates[0].SubsequentMeetingDateTime.AddMinutes(-remindMeIn));
                            meetingDates[0].NotificationSent = true;
                            context.SubsequentMeetings.Update(meetingDates[0]);
                        }
                        else
                        {
                            RecurringJob.RemoveIfExists(meetingId);
                        }
                    }
                    else
                    {
                        jobId = BackgroundJob.Schedule(() => _emailService.SendEmail(template, email, title), meetingDates[0].SubsequentMeetingDateTime.AddMinutes(-remindMeIn));
                        meetingDates[0].NotificationSent = true;
                        context.SubsequentMeetings.Update(meetingDates[0]);
                    }
                }
                else
                    RecurringJob.RemoveIfExists(meetingId);
            }

            await LogJobId(meetingDates[0].Id.ToString(), jobId, context);
            await context.SaveChangesAsync();
        }
        #endregion

        #region Add Subsequent Meeting Days
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task AddMeetingSubsequentDates(CalenderVm calenderVm, CalenderMeeting meeting)
        {
            var maxOccurrance = 20;
            var res = 0;
            var dates = new List<DateTime>();
            var days = new Dictionary<string, int>
                {
                    { "Sunday", 0 },
                    { "Monday", 1 },
                    { "Tuesday", 2 },
                    { "Wednesday", 3 },
                    { "Thursday", 4 },
                    { "Friday", 5 },
                    { "Saturday", 6 }
                };
            await using var context = new JobProDbContext(_conString, new DbContextSchema(calenderVm.SubDomain));
            if (string.IsNullOrEmpty(calenderVm.Frequency) && calenderVm.CustomFrequency is not null)
            {
                var tempEndsAfter = meeting.CustomFrequency.EndsAfter;
                switch (calenderVm.CustomFrequency.RepeatEvery)
                {
                    case "Day":
                        var date = meeting.StartDate.AddDays(1);
                        if (meeting.CustomFrequency.EndsOn is not null)
                        {
                            while (date <= meeting.CustomFrequency.EndsOn)
                            {
                                dates.Add(date);
                                date = date.AddDays(1);
                            }
                        }
                        else if (meeting.CustomFrequency.EndsAfter is not null)
                        {
                            while (meeting.CustomFrequency.EndsAfter > 0)
                            {
                                dates.Add(date);
                                date = date.AddDays(1);
                                meeting.CustomFrequency.EndsAfter--;
                            }
                        }
                        else
                        {
                            var count = 0;
                            while (count < maxOccurrance)
                            {
                                dates.Add(date);
                                date = date.AddDays(1);
                                count++;
                            }
                        }
                        break;
                    case "Week":
                        var repeatOn = calenderVm.CustomFrequency.RepeatOn?.Split(',');
                        foreach (var day in repeatOn)
                        {
                            meeting.CustomFrequency.EndsAfter = tempEndsAfter;
                            var dayOfWeek = days[day];
                            var startDate = meeting.StartDate;

                            int offset = ((int)startDate.DayOfWeek - 1 + 7) % 7;
                            var currentWeekStart = startDate.Date.AddDays(-offset);

                            var weekDate = currentWeekStart.AddDays(dayOfWeek - 1);

                            // If that day has already passed, move to next week's same day
                            if (weekDate < startDate)
                            {
                                weekDate = weekDate.AddDays(7);
                            }

                            if (meeting.CustomFrequency.EndsOn is not null)
                            {
                                while (weekDate <= meeting.CustomFrequency.EndsOn)
                                {
                                    if (weekDate >= startDate)
                                        dates.Add(weekDate);

                                    weekDate = weekDate.AddDays(7);
                                }
                            }
                            else if (meeting.CustomFrequency.EndsAfter is not null)
                            {
                                while (meeting.CustomFrequency.EndsAfter > 0)
                                {
                                    if (weekDate >= startDate)
                                    {
                                        dates.Add(weekDate);
                                        meeting.CustomFrequency.EndsAfter--;
                                    }
                                    weekDate = weekDate.AddDays(7);
                                }
                            }
                            else
                            {
                                var weekCount = 0;
                                while (weekCount < maxOccurrance)
                                {
                                    if (weekDate >= startDate)
                                    {
                                        dates.Add(weekDate);
                                        weekCount++;
                                    }

                                    weekDate = weekDate.AddDays(7);
                                }
                            }
                        }
                        break;
                    case "Month":
                        var monthDate = meeting.StartDate.AddMonths(1);
                        if (meeting.CustomFrequency.EndsOn is not null)
                        {
                            while (monthDate <= meeting.CustomFrequency.EndsOn)
                            {
                                dates.Add(monthDate);
                                monthDate = monthDate.AddMonths(1);
                            }
                        }
                        else if (meeting.CustomFrequency.EndsAfter is not null)
                        {
                            while (meeting.CustomFrequency.EndsAfter > 0)
                            {
                                dates.Add(monthDate);
                                monthDate = monthDate.AddMonths(1);
                                meeting.CustomFrequency.EndsAfter--;
                            }
                        }
                        else
                        {
                            var count = 0;
                            while (count < maxOccurrance)
                            {
                                dates.Add(monthDate);
                                monthDate = monthDate.AddMonths(1);
                                count++;
                            }
                        }
                        break;

                    case "Year":
                        var yearDate = meeting.StartDate.AddYears(1);
                        if (meeting.CustomFrequency.EndsOn is not null)
                        {
                            while (yearDate <= meeting.CustomFrequency.EndsOn)
                            {
                                dates.Add(yearDate);
                                yearDate = yearDate.AddYears(1);
                            }
                        }
                        else if (meeting.CustomFrequency.EndsAfter is not null)
                        {
                            while (meeting.CustomFrequency.EndsAfter > 0)
                            {
                                dates.Add(yearDate);
                                yearDate = yearDate.AddYears(1);
                                meeting.CustomFrequency.EndsAfter--;
                            }
                        }
                        else
                        {
                            var count = 0;
                            while (count < maxOccurrance)
                            {
                                dates.Add(yearDate);
                                yearDate = yearDate.AddYears(1);
                                count++;
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
            else
            {
                switch (calenderVm.Frequency)
                {
                    case "Daily":
                        var date = meeting.StartDate.AddDays(1);
                        var count = 0;
                        while (count < maxOccurrance)
                        {
                            dates.Add(date);
                            date = date.AddDays(1);
                            count++;
                        }
                        break;
                    case "Weekly":
                        var weekDate = meeting.StartDate.AddDays(7);
                        var weekCount = 0;
                        while (weekCount < maxOccurrance)
                        {
                            dates.Add(weekDate);
                            weekDate = weekDate.AddDays(7);
                            weekCount++;
                        }
                        break;
                    case "Monthly":
                        var monthDate = meeting.StartDate.AddMonths(1);
                        var monthCount = 0;
                        while (monthCount < maxOccurrance)
                        {
                            dates.Add(monthDate);
                            monthDate = monthDate.AddMonths(1);
                            monthCount++;
                        }
                        break;
                    case "Yearly":
                        var yearDate = meeting.StartDate.AddYears(1);
                        var yearCount = 0;
                        while (yearCount < maxOccurrance)
                        {
                            dates.Add(yearDate);
                            yearDate = yearDate.AddYears(1);
                            yearCount++;
                        }
                        break;
                    default:
                        break;
                }
            }

            IEnumerable<SubsequentMeeting> subsequentMeetings = new List<SubsequentMeeting>();
            // Ensure that the dates not less than the meeting start date
            dates = dates.Where(x => x.Date >= meeting.StartDate.Date).ToList();
            if (dates.Any())
            {
                if (calenderVm.ReoccuringDeleteOptions == null || calenderVm.ReoccuringDeleteOptions == Helpers.Enums.ReoccuringDeleteOptions.All)
                {
                    // Delete all previous subsequent meeting dates
                    var previousSubsequentMeetings = context.SubsequentMeetings.Where(s => s.CalenderMeetingId == meeting.Id);
                    if (previousSubsequentMeetings.Any())
                    {
                        var existingRecords = new List<BackGroundJobId>();
                        foreach (var row in previousSubsequentMeetings)
                        {
                            var existingJobRecord = await context.BackGroundJobIds.FirstOrDefaultAsync(x => x.EventId == row.Id.ToString());
                            if (existingJobRecord != null)
                            {
                                existingRecords.Add(existingJobRecord);
                                BackgroundJob.Delete(existingJobRecord.JobId);
                            }
                        }
                        context.BackGroundJobIds.RemoveRange(existingRecords);

                        context.SubsequentMeetings.RemoveRange(previousSubsequentMeetings);
                        RecurringJob.RemoveIfExists(meeting.Id.ToString());
                    }

                    subsequentMeetings = dates.Select(d => new SubsequentMeeting()
                    {
                        Id = Guid.NewGuid(),
                        CalenderMeetingId = meeting.Id,
                        SubsequentMeetingDateTime = d,
                        Name = meeting.Name,
                        NotifyMe = meeting.NotifyMe,
                        NotifyMembersIn = meeting.NotifyMembersIn,
                        EndTime = d.AddMinutes(double.Parse(meeting.MeetLength.ToString())),
                        MeetingDuration = meeting.MeetingDuration,
                        MeetLength = meeting.MeetLength,
                        MakeSchdulePrivate = meeting.MakeSchdulePrivate,
                        MeetingRecordingLink = meeting.MeetingRecordingLink,
                        HasMeetingHappeed = meeting.HasThisMeetingHappened
                    });

                    meeting.SubsequentMeetingDates = subsequentMeetings.ToList();
                    context.SubsequentMeetings.AddRange(subsequentMeetings);
                }
                else
                {
                    if (calenderVm.ReoccuringDeleteOptions == Helpers.Enums.ReoccuringDeleteOptions.ThisAndFuture)
                    {
                        subsequentMeetings = await context.SubsequentMeetings.Where(x => x.CalenderMeetingId == meeting.Id && x.SubsequentMeetingDateTime.Date >= DateTime.UtcNow.Date).ToListAsync();
                        foreach (var subsequentMeeting in subsequentMeetings)
                        {
                            var existingJobRecord = await context.BackGroundJobIds.FirstOrDefaultAsync(x => x.EventId == subsequentMeeting.Id.ToString());
                            if (existingJobRecord != null)
                            {
                                BackgroundJob.Delete(existingJobRecord.JobId);
                                context.BackGroundJobIds.Remove(existingJobRecord);
                            }
                        }

                        context.SubsequentMeetings.RemoveRange(subsequentMeetings);
                        subsequentMeetings = dates.Where(x => x.Date > DateTime.UtcNow.Date).Select(d => new SubsequentMeeting()
                        {
                            Id = Guid.NewGuid(),
                            CalenderMeetingId = meeting.Id,
                            SubsequentMeetingDateTime = d,
                            Name = meeting.Name,
                            NotifyMe = meeting.NotifyMe,
                            NotifyMembersIn = meeting.NotifyMembersIn,
                            EndTime = d.AddMinutes(double.Parse(meeting.MeetLength.ToString())),
                            MeetingDuration = meeting.MeetingDuration,
                            MeetLength = meeting.MeetLength,
                            MakeSchdulePrivate = meeting.MakeSchdulePrivate,
                            MeetingRecordingLink = meeting.MeetingRecordingLink,
                            HasMeetingHappeed = meeting.HasThisMeetingHappened
                        });
                        meeting.SubsequentMeetingDates = subsequentMeetings.ToList();
                        context.SubsequentMeetings.AddRange(subsequentMeetings);
                    }
                }

                res = await context.SaveChangesAsync();
                if (res > 0)
                {
                    // Get uploaded documents for the main meeting and assign to subsequent meetings
                    BackgroundJob.Enqueue(() => AddUploadsToSubsequentMeetings(meeting, calenderVm.SubDomain, subsequentMeetings));

                    // Get meeting members and assign to subsequent meetings
                    BackgroundJob.Enqueue(() => AddMembersToSubsequentMeetings(meeting, calenderVm.SubDomain, subsequentMeetings));
                }
            }
        }
        #endregion

        #region Send Booking Comfirmation Email - Private Method
        private void SendBookingComfirmationEmail(SendMailRecord record)
        {
            var template = record.template.Replace("{name}", record.fullName).Replace("{message}", record.message).Replace("{meeting name}", record.externalMeeting.MeetingName).Replace("{location}", record.externalMeeting.Location).Replace("{duration}", record.externalMeeting.MeetingDuration.ToString()).Replace("{link}", record.externalMeeting.MeetingLink).Replace("{organizer}", record.invitee).Replace("{reshedule}", record.rescheduleLink).Replace("{cancel}", record.cancelLink);

            template = template.Replace("{date}", record.meetingDate.ToShortDateString()).Replace("{time}", record.meetingDate.ToShortTimeString());
            BackgroundJob.Enqueue(() => _emailService.SendEmail(template, record.email, record.subject));
        }
        #endregion

        #region BK - Reshedule meetings that didnt happen
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task ResheduleMeetingsThatDidntHappen(List<string> subdomains)
        {
            if (subdomains.Any())
            {
                foreach (var subdomain in subdomains)
                {
                    try
                    {
                        await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
                        var meetings = await context.CalenderMeetings
                            .Where(x => DateTime.UtcNow > x.StartDate && !x.HasThisMeetingHappened && !x.HasCustomFrequency && !x.IsCancelled).ToListAsync();
                        foreach (var meeting in meetings)
                        {
                            if (meeting.RescheduleCount == 3)
                                continue;

                            // Call AI service to get a new date and time
                            var payload = new RescheduleMeetingRequest
                            {
                                MeetingId = meeting.Id.ToString(),
                                Subdomain = subdomain
                            };

                            var endPoints = GlobalVariables.CustomAppSettings.AIEnpoints;
                            var response = await _apiCallService.MakeApiCallAsync<RescheduleMeetingRequest, RescheduleMeetingResponse>(endPoints.BaseUrl, endPoints.RescheduleMeeting, Method.Post, payload);

                            if (response is not null && response.Success)
                            {
                                var res = await RescheduleMeeting(meeting, response.RescheduledDatetime.RescheduledDateAndtime, subdomain);
                                if (!res)
                                    _logger.Error(subdomain, $"Failed to reschedule meeting with id {meeting.Id}");

                                _logger.Information($"ResheduleMeetingsThatDidntHappen api call response message {response?.Message}, Time: {DateTime.UtcNow}");
                            }

                            _logger.Information($"ResheduleMeetingsThatDidntHappen api call response is null, Time: {DateTime.UtcNow}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Error($"BackgroundJob:ResheduleMeetingsThatDidntHappen failed for {subdomain} tenant", ex.ToString());
                    }
                }
            }
        }
        #endregion

        #region Reschedule Meeting - Private Method
        private async Task<bool> RescheduleMeeting(CalenderMeeting meeting, DateTime newDateAndTime, string subdomain)
        {
            _logger.Information($"Reschedule meeting called by the background service, Time: {DateTime.UtcNow}");

            // Get duration in minutes
            var durationInMinutes = meeting.EndDate.Value.Subtract(meeting.StartDate).TotalMinutes;
            meeting.StartDate = newDateAndTime;
            meeting.EndDate = newDateAndTime.AddMinutes(durationInMinutes);

            var reschedulePayload = new UpdateCalenderDto
            {
                Id = meeting.Id,
                UserId = meeting.CreatedBy.ToString(),
                Name = meeting.Name,
                StartDate = meeting.StartDate,
                EndTime = meeting.EndDate.Value,
                EndDate = meeting.EndDate.Value,
                Location = meeting.Location,
                MakeSchdulePrivate = meeting.MakeSchdulePrivate,
                NotifyMe = meeting.NotifyMe,
                HasCustomFrequency = meeting.HasCustomFrequency,
                UpdatedBy = meeting.CreatedBy,
                SubDomain = subdomain,
                NotifyMeInMinutes = 10,
                Frequency = meeting.Frequency,
                AIResheduleForMeetingsThatDidntHappen = true,
                RescheduleCount = meeting.RescheduleCount + 1
            };

            // Get meeting attendees
            var attendees = await Db.UserIdMeetingIds
                .Where(x => x.CalenderId == meeting.Id.ToString()).ToListAsync();
            var internalMembers = attendees.Where(x => x.UserId != null).Select(x => x.UserId).ToList();
            var externalMembers = attendees.Where(x => x.Email != null).Select(x => x.Email).ToList();

            reschedulePayload.InvitedUsers = internalMembers;
            reschedulePayload.ExternalTeamMemberEmails = externalMembers;

            // Get CalenderService from ServiceProvider
            var scope = _serviceProvider.CreateScope();
            var calenderService = scope.ServiceProvider.GetRequiredService<ICalenderService>();

            var resheduleRes = await calenderService.UpdateMeetingOrEvents(reschedulePayload);
            return resheduleRes != null;
        }
        #endregion

        #region Background job database related operations - Private
        /// <summary>
        /// Log the job id
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="jobId"></param>
        /// <param name="context"></param>
        /// <param name="jobType"></param>
        /// <returns></returns>
        private async Task LogJobId(string eventId, string jobId, JobProDbContext context, JobType jobType = JobType.Scheduled)
        {
            await context.BackGroundJobIds.AddAsync(new BackGroundJobId
            {
                JobId = jobId,
                EventId = eventId,
                JobType = jobType
            });
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Deletes job 
        /// </summary>
        /// <param name="jobId"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        private async Task DeleteJobId(string jobId, JobProDbContext context)
        {
            var existingJobRecord = await context.BackGroundJobIds.SingleOrDefaultAsync(x => x.Id.ToString() == jobId);
            if (existingJobRecord != null)
            {
                context.BackGroundJobIds.Remove(existingJobRecord);
                context.SaveChanges();
            }
        }
        #endregion

        #region Add Uploads to Subsequent Meetings
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task AddUploadsToSubsequentMeetings(CalenderMeeting meeting, string subdomain, IEnumerable<SubsequentMeeting> subsequentMeetings)
        {
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var uploadedDocuments = await context.CalenderUploads.Where(x => x.MeetingId == meeting.Id).ToListAsync();
            if (!uploadedDocuments.Any())
                return;

            foreach (var subsequentMeeting in subsequentMeetings)
            {
                // Delete previous uploads
                var previousUploads = await context.CalenderUploads.Where(x => x.SubsequentMeetingId == subsequentMeeting.Id).ToListAsync();
                if (previousUploads != null && previousUploads.Any())
                {
                    context.CalenderUploads.RemoveRange(previousUploads);
                }

                // Add new uploads
                foreach (var document in uploadedDocuments)
                {
                    var newDocument = new CalenderUpload
                    {
                        SubsequentMeetingId = subsequentMeeting.Id,
                        FileName = document.FileName,
                    };
                    context.CalenderUploads.Add(newDocument);
                }
            }

            await context.SaveChangesAsync();
        }
        #endregion

        #region Add Members to Subsequent Meetings
        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("meeting")]
        public async Task AddMembersToSubsequentMeetings(CalenderMeeting meeting, string subdomain, IEnumerable<SubsequentMeeting> subsequentMeetings)
        {
            await using var context = new JobProDbContext(_conString, new DbContextSchema(subdomain));
            var attendees = await context.UserIdMeetingIds.Where(x => x.CalenderId == meeting.Id.ToString()).ToListAsync();
            if (attendees == null || !attendees.Any())
                return;

            var internalMembers = attendees.Where(x => x.UserId != null).Select(x => x.UserId).ToList();
            var externalMembers = attendees.Where(x => x.Email != null).Select(x => x.Email).ToList();
            foreach (var subsequentMeeting in subsequentMeetings)
            {
                // Delete previous members
                var previousMembers = await context.UserIdMeetingIds.Where(x => x.SubsequentMeetingId == subsequentMeeting.Id.ToString()).ToListAsync();
                if (previousMembers != null && previousMembers.Any())
                {
                    context.UserIdMeetingIds.RemoveRange(previousMembers);
                }

                // Add new members
                foreach (var internalMember in internalMembers)
                {
                    var userIdMeetingId = new UserIdCalenderId
                    {
                        SubsequentMeetingId = subsequentMeeting.Id.ToString(),
                        UserId = internalMember,
                        NotifyMeInMinutes = meeting.NotifyMembersIn,
                        InviteResponse = InviteResponse.Pending
                    };
                    context.UserIdMeetingIds.Add(userIdMeetingId);
                }

                foreach (var externalMember in externalMembers)
                {
                    var userIdMeetingId = new UserIdCalenderId
                    {
                        SubsequentMeetingId = subsequentMeeting.Id.ToString(),
                        Email = externalMember,
                        NotifyMeInMinutes = meeting.NotifyMembersIn,
                        InviteResponse = InviteResponse.Pending
                    };
                    context.UserIdMeetingIds.Add(userIdMeetingId);
                }
            }
        }
        #endregion
    }
}
