﻿using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.ViewModel;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class ApiCallService : IApiCallService
    {
        // Logger
        private ILogger _logger = Log.ForContext<ApiCallService>();

        public async Task<string> GetCountryRegionAsync(string countryName)
        {
            try
            {
                var baseUrl = "https://restcountries.com/v3.1/name/";
                using (var client = new RestClient(baseUrl))
                {
                    var request = new RestRequest(countryName, Method.Get);
                    request.AddHeader("Accept", "application/json");
                    request.AddHeader("Content-Type", "application/json");

                    var response = await client.ExecuteAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        var countryInfo = JsonConvert.DeserializeObject<List<CountryDetailsResponse>>(response.Content);
                        return countryInfo[0].Region ?? throw new Exception("Continent not found");
                    }

                    _logger.Error($"GetCountryRegionAsync:API call failed: {response.ErrorMessage}");
                    return default;
                }

            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetCountryRegionAsync:API call failed: {ex}");
                return default;
            }
        }

        public async Task<IpGeolocationResponse> GetCountryFromIpAsync(string ipAddress)
        {
            try
            {
                if (string.IsNullOrEmpty(ipAddress))
                {
                    _logger.Error("GetCountryFromIpAsync: IP address is null or empty");
                    return null;
                }

                // Use ip-api.com which is a free IP geolocation API
                var baseUrl = "http://ip-api.com/json/";
                using (var client = new RestClient(baseUrl))
                {
                    var request = new RestRequest(ipAddress, Method.Get);
                    request.AddHeader("Accept", "application/json");

                    var response = await client.ExecuteAsync(request);
                    if (response.IsSuccessStatusCode)
                    {
                        var geoInfo = JsonConvert.DeserializeObject<IpGeolocationResponse>(response.Content);
                        if (geoInfo.Status == "success")
                        {
                            return geoInfo;
                        }

                        _logger.Error($"GetCountryFromIpAsync: API returned error status: {geoInfo.Status}");
                        return null;
                    }

                    _logger.Error($"GetCountryFromIpAsync: API call failed: {response.ErrorMessage}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"GetCountryFromIpAsync: API call failed: {ex}");
                return null;
            }
        }

        public async Task<R> MakeApiCallAsync<P, R>(string baseUrl, string url, Method method, object body = null)
        {
            try
            {
                _logger.Information($"Making API call to {baseUrl}{url}", body);

                // Add appsettings.json file using configurationbuilder
                var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                var configuration = builder.Build();
                var key = Environment.GetEnvironmentVariable("JOBPRO_RTC_KEY") ?? configuration["RTC_API_KEY"];

                var options = new RestClientOptions(baseUrl)
                {
                    MaxTimeout = -1,
                };

                using (var client = new RestClient(options))
                {
                    var request = new RestRequest(url, method);

                    // Add header api-key
                    if (!string.IsNullOrEmpty(key))
                        request.AddHeader("api-key", key);

                    request.AddHeader("Accept", "application/json");
                    request.AddHeader("Content-Type", "application/json");

                    if (body != null)
                    {
                        var payload = JsonConvert.SerializeObject(body);
                        request.AddStringBody(payload, DataFormat.Json);
                    }

                    var response = await client.ExecuteAsync(request);
                    if (response.IsSuccessStatusCode)
                        return JsonConvert.DeserializeObject<R>(response.Content);

                    _logger.Error($"MakeApiCallAsync:API call failed: {response.ErrorMessage}");
                    return default;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"MakeApiCallAsync:API call failed: {ex}");
                return default;
            }
        }

        public async Task<R> MakeApiCallGenericAsync<P, R>(string baseUrl, string path, Method method, object body = null, Dictionary<string, string> headers = null)
        {
            try
            {
                _logger.Information($"Making API call to {baseUrl}", body);

                // Add appsettings.json file using configurationbuilder
                var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                var configuration = builder.Build();


                var options = new RestClientOptions(baseUrl)
                {
                    MaxTimeout = -1,
                };

                using (var client = new RestClient(options))
                {
                    var request = new RestRequest(path, method);
                    if (headers is not null)
                    {
                        foreach (KeyValuePair<string, string> kvp in headers)
                        {
                            request.AddHeader(kvp.Key, kvp.Value);
                        }
                    }

                    request.AddHeader("Accept", "application/json");
                    request.AddHeader("Content-Type", "application/json");

                    if (body != null)
                    {
                        var payload = JsonConvert.SerializeObject(body);
                        request.AddStringBody(payload, DataFormat.Json);
                    }

                    var response = await client.ExecuteAsync(request);
                    if (response.IsSuccessStatusCode)
                        return JsonConvert.DeserializeObject<R>(response.Content);

                    _logger.Error($"MakeApiCallAsync:API call failed: {response.ErrorMessage}");
                    return default;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"MakeApiCallAsync:API call failed: {ex}");
                return default;
            }
        }
    }
}
