﻿using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProjectManagement.Models
{
    public class TriggerSequence
    {
        [Key]
        public Guid Id { get; set; }
        public DateTime DateAndTime { get; set; }
        public TriggerAction Action { get; set; } = TriggerAction.Email;
        public Guid ProjectTriggerId { get; set; }
        public ProjectTrigger ProjectTrigger { get; set; }
        public bool Notification { get; set; }
        public bool SMS { get; set; }
        public bool Email { get; set; }
    }
}
