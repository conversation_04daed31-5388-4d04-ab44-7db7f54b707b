﻿using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class ProjectMembersVm
    {
        public List<string> TeamMembers { get; set; }

        [CanInviteExternalUser(Helpers.Enums.Applications.Joble)]
        [ValidEmailChecks]
        public List<string> ExternalMemberEmails { get; set; } = new List<string>();

        [Required(ErrorMessage = "UserId cannot be null")]
        public string UserId { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
