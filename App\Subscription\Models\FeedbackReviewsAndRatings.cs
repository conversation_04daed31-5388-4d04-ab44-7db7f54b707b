﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Subscription.Models
{
    public class FeedbackReviewsAndRatings
    {
        [Key]
        public Guid Id { get; set; } = new Guid();
        public int StarRating { get; set; }
        public string FeedBack { get; set; }
        public string Application { get; set; }
        public string UserId { get; set; }

        // Navigation Properties
        public User User { get; set; }
    }
}
