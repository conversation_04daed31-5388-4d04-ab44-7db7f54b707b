﻿using Jobid.App.Calender.Models;
using Jobid.App.Helpers.Models;
using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class BookedMeetingNotificationDto
    {
        public string BookedMeetingId { get; set; }
        public string Subdomain { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public List<string> GuestEmails { get; set; } = new List<string>();
        public ExternalMeeting ExternalMeeting { get; set; }
        public UserProfile MeetingOwner { get; set; }
        public string Subject { get; set; }
        public string BookedMeetingSubject { get; set; }
        public List<string> InternalMemberIds { get; set; } = new List<string>();
        public List<string> ExternalMemberEmails { get; set; } = new List<string>();
    }
}
