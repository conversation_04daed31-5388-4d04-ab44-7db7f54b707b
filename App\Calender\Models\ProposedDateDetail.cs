﻿using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class ProposedDateDetail
    {
        public Guid Id { get; set; }

        [ForeignKey("Meeting")]
        public Guid MeetingId { get; set; }
        public DateTime NewProposedStartDateAndTime { get; set; }
        public DateTime NewProposedEndDateAndTime { get; set; }
        public string Reason { get; set; }
        public string ProposedByEmail { get; set; }
        public DateTime PropsedOn { get; set; }
        public DateTime? AcceptedOn { get; set; }
        public ProposedNewDateStatus Status { get; set; }

        // Navigation properties
        public CalenderMeeting Meeting { get; set; }

        public ProposedDateDetail()
        {
            Id = Guid.NewGuid();
            PropsedOn = DateTime.UtcNow;
            Status = ProposedNewDateStatus.Pending;
        }
    }
}
