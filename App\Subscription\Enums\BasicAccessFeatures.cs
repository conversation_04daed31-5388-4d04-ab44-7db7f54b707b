﻿using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Subscription.Enums
{
    public class BasicAccessFeatures
    {
        public enum JobleFeatures
        {
            [Display(Name = "1 -Month Message View")]
            MessageView,

            [Display(Name = "5 Users Max Per Project")]
            UsersMaxPerProject,

            [Display(Name = "40 Minutes Max Per Meeting")]
            MinutesMaxPerMeeting,
        }
    }
}
