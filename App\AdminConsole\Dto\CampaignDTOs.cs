using Jobid.App.AdminConsole.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Dto
{
    /// <summary>
    /// DTO for creating a new campaign
    /// </summary>
    public class CreateCampaignDto
    {
        /// <summary>
        /// Reference to the contact group
        /// </summary>
        [Required]
        public Guid GroupId { get; set; }

        /// <summary>
        /// Phone number for the campaign
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Expected outcome of the campaign
        /// </summary>
        [Required]
        public CampaignExpectedOutcome ExpectedOutcome { get; set; }

        /// <summary>
        /// External meeting ID associated with this campaign
        /// </summary>
        public Guid? ExternalMeetingId { get; set; }

        /// <summary>
        /// User who is creating this campaign
        /// </summary>
        [Required]
        public string UserId { get; set; }
    }

    /// <summary>
    /// DTO for updating an existing campaign
    /// </summary>
    public class UpdateCampaignDto
    {
        /// <summary>
        /// Campaign ID to update
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// Reference to the contact group
        /// </summary>
        [Required]
        public Guid GroupId { get; set; }

        /// <summary>
        /// Phone number for the campaign
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// Expected outcome of the campaign
        /// </summary>
        [Required]
        public CampaignExpectedOutcome ExpectedOutcome { get; set; }

        /// <summary>
        /// Status of the campaign
        /// </summary>
        public CampaignStatus Status { get; set; }

        /// <summary>
        /// External meeting ID associated with this campaign
        /// </summary>
        public Guid? ExternalMeetingId { get; set; }

        /// <summary>
        /// User who is updating this campaign
        /// </summary>
        [Required]
        public string UserId { get; set; }
    }

    /// <summary>
    /// DTO for campaign response
    /// </summary>
    public class CampaignDto
    {
        public Guid Id { get; set; }
        public Guid GroupId { get; set; }
        public string PhoneNumber { get; set; }
        public CampaignExpectedOutcome ExpectedOutcome { get; set; }
        public CampaignStatus Status { get; set; }
        public string UserId { get; set; }
        public Guid? ExternalMeetingId { get; set; }
        
        /// <summary>
        /// Group information
        /// </summary>
        public ContactGroupDto Group { get; set; }

        // Audit properties
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
    }

    /// <summary>
    /// DTO for paginated campaign list
    /// </summary>
    public class PaginatedCampaignsDto
    {
        public List<CampaignDto> Campaigns { get; set; }
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    /// <summary>
    /// DTO for updating campaign status
    /// </summary>
    public class UpdateCampaignStatusDto
    {
        [Required]
        public Guid CampaignId { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        public CampaignStatus Status { get; set; }
    }

    /// <summary>
    /// DTO for campaign statistics
    /// </summary>
    public class CampaignStatsDto
    {
        public int TotalCampaigns { get; set; }
        public int ActiveCampaigns { get; set; }
        public int InactiveCampaigns { get; set; }
        public int CompletedCampaigns { get; set; }
        public int CancelledCampaigns { get; set; }
        public int BookInPersonMeetingCampaigns { get; set; }
        public int BookOnlineMeetingCampaigns { get; set; }
    }
}
