﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.SchemaTenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class CanInviteExternalUser : ValidationAttribute
    {
        private readonly Applications _app;

        public CanInviteExternalUser(Applications app)
        {
            _app = app;
        }

        protected override ValidationResult IsValid(object value, ValidationContext context)
        {
            var externalUsers = value as List<string>;
            if (externalUsers.Any())
            {
                var tenantConfig = context.GetService(typeof(ITenantConfig<JobProDbContext>)) as ITenantConfig<JobProDbContext>;
                var httpContextAccessor = context.GetService(typeof(IHttpContextAccessor)) as IHttpContextAccessor;
                var publicContext = new JobProDbContext(new DbContextSchema());
                var subdomain = tenantConfig.getSubdomainName(httpContextAccessor.HttpContext);
                var subdomainContext = tenantConfig.getRequestContext(httpContextAccessor.HttpContext);

                var companyId = publicContext.Tenants.Where(x => x.Subdomain == subdomain).Select(c => c.Id).FirstOrDefaultAsync().Result;
                var usersWithPermission = subdomainContext.AppPermissions.Where(x => x.Application == _app.ToString()).CountAsync().Result;
                var numberOfInvites = publicContext.CompanyUserInvites.Where(x => x.TenantId == companyId.ToString() && x.Application == _app)
                    .CountAsync().Result;
                var subscriptionFor = publicContext.Subscriptions.Where(x => x.TenantId == companyId && x.Application == _app).Select(c => c.SubscriptionFor).FirstOrDefaultAsync().Result;

                if (subscriptionFor == numberOfInvites + 1)
                {
                    return new ValidationResult("You can no longer invite external users to this workspace, contact your admin or proceed without inviting external members");
                }
            }

            return ValidationResult.Success;
        }
    }
}
