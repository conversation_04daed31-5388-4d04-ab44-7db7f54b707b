﻿using Hangfire;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.BackGroundJobs
{
    public class ActivityBackgroundService : IActivityBackgroundService
    {
        private readonly JobProDbContext _context;
        private readonly JobProDbContext _publicContext;
        private ILogger _logger = Log.ForContext<ActivityBackgroundService>();

        public ActivityBackgroundService(JobProDbContext context, JobProDbContext publicContext)
        {
            _context = context;
            _publicContext = publicContext;
            _publicContext = new JobProDbContext(new DbContextSchema());
            var tableExist = _publicContext.TableExists("CompanySubscriptions");
            if (tableExist)
            {
                GlobalVariables.Subdomains = _publicContext.CompanySubscriptions
                    .Include(x => x.Tenant)
                    .Where(x => x.Application == Applications.Joble && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active)
                    .Select(x => x.Tenant.Subdomain)
                    .ToList() ?? new List<string>();
            }
        }

        public async Task DeleteCompanyActivities(string tenantId, int days)
        {
            // Delete activities from the activity table that are a month old
            var activities = await _context.Activities.Where(a => a.CreatedAt < DateTime.UtcNow.AddDays(-days)).ToListAsync();
            _context.Activities.RemoveRange(activities);
            await _context.SaveChangesAsync();
        }

        [AutomaticRetry(Attempts = 3, OnAttemptsExceeded = AttemptsExceededAction.Delete)]
        [Queue("activities")]
        public async Task DeleteUserActivities(List<string> subdomains)
        {
            if (subdomains is null)
                return;

            foreach (var  subdomain in subdomains)
            {
                try
                {
                    var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                    var users = await context.UserProfiles.ToListAsync();
                    _logger.Information($"Deleting activities for {subdomain} subdomain", users);

                    foreach (var user in users)
                    {
                        switch (user.EraseAcitivity)
                        {
                            case EraseAcitivity.Now:
                                await DeleteUserActivities(context, user, 1);
                                break;
                            case EraseAcitivity.After7days:
                                await DeleteUserActivities(context, user, 7);
                                break;
                            case EraseAcitivity.After1Month:
                                await DeleteUserActivities(context, user, 30);
                                break;
                            case EraseAcitivity.After3Months:
                                await DeleteUserActivities(context, user, 90);
                                break;
                            case EraseAcitivity.After6Months:
                                await DeleteUserActivities(context, user, 180);
                                break;
                            default:
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"BackgroundJob:DeleteUserActivities failed for {subdomain} tenant", ex.ToString());
                }
            }

            return;
        }

        private async Task DeleteUserActivities(JobProDbContext context, UserProfile user, int days)
        {
            var userActivities = await context.Activities
                                            .Where(a => a.UserId == user.UserId && a.CreatedAt <= DateTime.UtcNow.AddDays(-days)).ToListAsync();
            if (userActivities.Count == 0)
                return;

            context.Activities.RemoveRange(userActivities);
            await context.SaveChangesAsync();
        }
    }
}
