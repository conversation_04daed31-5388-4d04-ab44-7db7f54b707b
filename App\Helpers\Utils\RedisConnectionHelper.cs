﻿using Microsoft.Extensions.Configuration;
using StackExchange.Redis;
using System;

namespace Jobid.App.Helpers.Utils
{
    public class RedisConnectionHelper
    {
        private static Lazy<ConnectionMultiplexer> lazyConnection;

        static RedisConnectionHelper()
        {
            lazyConnection = new Lazy<ConnectionMultiplexer>(() =>
            {
                return ConnectionMultiplexer.Connect("*************:6379");
            });
        }

        public static ConnectionMultiplexer Connection
        {
            get
            {
                return lazyConnection.Value;
            }
        }
    }
}
