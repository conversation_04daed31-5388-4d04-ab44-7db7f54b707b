{"ApplicationName": "Jobpro", "FrontendUrl": "https://pactocoin.com/", "AllowedHosts": "*", "SendGridAPIKey": "*********************************************************************", "RootDomain": "jobpro", "createAccountUrl": "http://ec2-3-90-70-68.compute-1.amazonaws.com/api/account/", "hivSecret": "yucZyB5JJuXCnJQ3D7zMGEnEbkKmDaB3btyZtw8KU3SAaEZL", "MicroservicekycResponse": "https://staging.jobpro.app/api/KYC/GetVerificationResponseForEmployee", "AllowedCorsOrigins": ["https://localhost:5000"], "AIEnpoints": {"BaseUrl": "https://ai.pactocoin.com", "RescheduleMeeting": "/api/v0/calendar/reschedule-meeting"}, "SuperAdminEnpoints": {"BaseUrl": "https://api.pactocoin.com/admin-svc", "CreateTicketExternal": "/api/v1/Ticket/external/create-ticket"}, "Endpoints": {"createCorporate": "/Identity/createCorporate", "sendEmailVerification": "/Identity/initiateEmailVerification/{0}", "createpassword": "/Identity/createPassword", "createConsumer": "/Identity/createConsumer", "sendEmailVerificationConsumers": "/Identity/initiateEmailVerificationConsumers/{0}", "verifyconsumeremail": "/Identity/verifyEmailConsumers", "verifycorporateemail": "/Identity/verifyEmailCorporate", "getToken": "/Auth/authToken", "startKyb": "/Identity/startKyb", "startKyc": "/Identity/startKyc", "startKycMobile": "/Identity/startKycMobile", "createManagedAccount": "/ManagedAccount/createAccount", "createIban": "/ManagedAccount/updateWithIBAN/", "createDebitCard": "/Card/CreateDebitCard", "stepUpToken": "/Auth/stepUpTokenChallenge", "verifyChallenge": "/Auth/verifyChallenge", "Enrol2FA": "/Auth/EnrolForTwoFA", "Verify2FA": "/Auth/verifyEnrolForTwoFA", "createAuthUser": "/Identity/CreateAuthUser", "sendEmailVerificationAuthUser": "/Identity/SendEmailVericationAuthUser/{0}", "getWeavrAccount": "/ManagedAccount/getAccount/{0}", "addBeneficiary": "/Beneficiary/AddBeneficiary", "startBenOtp": "/Beneficiary/IssueOTP", "verifyBen": "/Beneficiary/VerifyOTP", "getAllBen": "/Beneficiary/GetAllBeneficiaries", "internalTransfer": "/Transfers/CreateInternalTransfer", "sepaOutwireTransfers": "/Transfers/SepaOutWireTransfer", "getIBan": "/ManagedAccount/getIBAN/{0}", "SendOutwireChallenge": "/Transfers/CreateOutWireChallenge/{0}", "verifyoutwirechallenge": "/Transfers/VerifyOutWireChallenge", "transferToOwnAccount": "/Transfers/TransferToOwnAccount", "SendInternalTransferChallenge": "/Transfers/CreateSendChallenge/{0}", "verifyInternalTransferChallenge": "/Transfers/VerifySendChallenge", "getManageAccount": "/ManagedAccount/getAccount/{0}", "removeBeneficiary": "/Beneficiary/RemoveBeneficiary"}, "TwilioAddressSids": {"CountrySids": {"NL": "ADf0c6370324bcc99d09811cf12c4ff6f7"}}, "RTCEndpoints": {"BaseUrl": "https://webrtcapi.pactocoin.com", "CreateMeeting": "v1/meetings", "ClientUrl": "https://rtc.pactocoin.com/"}, "ProvidHivesEnv": {"Is_Live": "NO", "BaseURL": "http://**************:8088/appdevapi/api", "ClientID": "dGVzdF9Qcm92aWR1cw==", "ClientSecret": "29A492021F4B709A8D1152C3EF4D32DC5A7092723ECAC4C511781003584B48873CCBFEBDEAE89CF22ED1CB1A836213549BC6638A3B563CA7FC009BEB3BC30CF8", "X_AUTH_SIG": "BE09BEE831CF262226B426E39BD1092AF84DC63076D4174FAC78A2261F9A3D6E59744983B8326B69CDF2963FE314DFC89635CFA37A40596508DD6EAAB09402C7", "createDynamicAccountNumber": "/PiPCreateDynamicAccountNumber", "updateAccountName": "/PiPUpdateAccountName", "verifyTransaction": "/PiPverifyTransaction", "blackListAccount": "/PiPBlacklistAccount", "createReservedAccountNumber": "/PiPCreateReservedAccountNumber", "verifyTransactionBySessionID": "/PiPverifyTransaction_sessionid", "verifyTransactionBySettlemmentID": "/PiPverifyTransaction_settlementid", "accountSettlementNotification": "http://merchant_endpoint/settlement_notif", "USN": "test", "PWD": "test", "TP_BaseURL": "http://**************:8882/postingrest", "GetNIPAccount": "/GetNIPAccount", "NIPFundTransfer": "/NIPFundTransfer", "GetNIPTransactionStatus": "/GetNIPTransactionStatus", "GetNIPBanks": "/GetNIPBanks", "ProvidusFundTransfer": "/ProvidusFundTransfer", "GetProvidusTransactionStatus": "/GetProvidusTransactionStatus", "GetProvidusAccount": "/GetProvidusAccount", "NIPFundTransferMultipleDebitAccounts": "/NIPFundTransferMultipleDebitAccounts"}, "HangfireSettings": {"UnsuspendEmployee_Time": "0 */8 * * *"}, "WeavrConfigurations": {"API_KEY": "QWRtaW46UGFzc3dvcmQxMCQ="}, "interfaceEnv": "https://wvr.jobpro.app", "WebhookSignature": "UGFzc3dvcmQyMCQ=", "Grpc": {"BaseUrl": "*************:50001"}, "BridgeCard": {"BaseUrl": "https://issuecards.api.bridgecard.co/v1/issuing/sandbox/", "CardDetailsBaseUrl": "https://issuecards-api-bridgecard-co.relay.evervault.com/v1/issuing/sandbox/", "NairaCardOtpBaseUrl": "https://issuecards-api-bridgecard-co.relay.evervault.com/v1/issuing/sandbox/", "AuthToken": "at_test_04058e159ad375afd992de76520f67b0f08ace6bfcf36f8542c1cd3206cd252e048024f4739e82ee7cd31d06361ae5c8eb624f1aa3d79b2b342c94b0c95feeeab258243b5eb29ae82c01628e7af02a2731a82a2c1437d509b6ead895fed6c7700dd3ffa96de55c0fc0b1368a2f48cf4cade631989fd038ad6f00a11ae5eaaea712c48d42844721d6e2025149353e35278b863815bcf4cafdd721fba3a4ab134667197c4fec35931a759c6f968dd237f1b6cf98c07796f6699165514f75c7b9689bd83dacb9cd29639106ff9e3bef181c15cdc2cc6e1f0da92c20789b313989a801cde15375051fd1305e1e4f79e3efe6cf59ebd46f3f087dd9124492272c07fe", "SecretKey": "sk_test_VTJGc2RHVmtYMThCa2dFZUdseGhGY0src2k4M3lTTzFla3l3MEpyNHpDVC9SNGhFSE9tV2tJYmx3S08xVmR1RUovMHNKWS9MaUZXK1N3M0RVdVN6MkYzWHBIclJtZzB1MDY5YktRNXJ6ZXFac0c4c1FReDRna1B2SVA2OGhUZlRmNTVqUG9TYUkxOHBqV3ZnOGtDNUw3bWZWUkFTUXM2ZGhZaFBXWmZaTWd1dDFMY01sYnRCWGViczlCMVpBc2hGK1dhdmlYcXBlTHBwMUNXM3FQU3BtelBoK3lyRGY5R3hmdFVpOVBjYmt1Y25SbzhvQVZoKzB4dnlMZ0NoTTJ1S3FJUHF0U3prWmFoV1RzVGdSUjJleE1aS3hEUS9oSXpUY0U3MWhSY0lOL1pwd204ZWFxVnUwTWswRCtENTY2ZEZXTDcwazNTeHpXRGJiL0ticzhFKzQ3ZGlZM1RmdVRPdFNpNU5RLzJXd0N1NDMzcFFIL25nVDUrS0FTY1htamF0MWxZS2drSjZvZldReW81ZDhWamhhNVQ0NnNDU0JXeHd3UE9SYjBRNWhXSjNkY3p1OEhRWG1BdElLSlkvUXAxRG1uT29PdHBoL2ZKVnp0dndUUVNyemIwVUV3NTJCZ1VJT09mZFR5WVNuc3M9", "WebHookKey": "whsec_test_10305ce88f184253c7db3d7c6efbb26837026621295ee0e5a958e7b24b442b1f8bc521c67a8c0a6d6cd28f494cdeed8c1ae926e842743e52a7aab08c9013ba918dbabb59f75940fc046248af3908054a47f094c82c37d9e5d65daadf374a3e196a53b8f1f267c63c90225cf9288373b37dcd54c901bede877d24339b54ffd8b29b9e556ce9e6cae87c13ad160436927def136341af5005b8774c92727bce305460ddd56fc80917569103e368e3fc1053ead9fd06dea30643c9d546d2c26bc38af7d45bbae73b548d8ad6430520ad500b65dbbc2eb7d74d76869451c8d9f6587eef5ce80e01e9547c43baeb3fbcff0a0d3d4d056fa05df41ace36a89fdd3e3286", "IssuingID": "e4b51d6b-0d90-4c47-b73c-b4f032db6d35", "RegCardHolder": "cardholder/register_cardholder", "CardHolderDetails": "cardholder/get_cardholder", "DeleteCardHolder": "cardholder/delete_cardholder/", "CreateCard": "cards/create_card", "ActivatePhysicalCard": "cards/activate_physical_card", "CardDetails": "cards/get_card_details", "FundCard": "cards/fund_card_asynchronously", "FundTestWallet": "cards/fund_issuing_wallet", "UnloadCard": "cards/unload_card_asynchronously", "MockDebit": "cards/mock_debit_transaction", "CardTransactions": "cards/get_card_transactions", "CardTransactionById": "cards/get_card_transaction_by_id", "CardTransactionStatus": "cards/get_card_transaction_status", "FreezeCard": "cards/freeze_card", "UnFreezeCard": "cards/unfreeze_card", "CardHolderCards": "cards/get_all_cardholder_cards", "DeleteCard": "cards/delete_card/", "UpdateCardPin": "cards/set_3d_secure_pin", "NairaCardBalance": "/naira_cards/get_card_balance", "NairaCardOtp": "naira_cards/get_otp_message", "FundNairaCard": "naira_cards/fund_naira_card", "NairaCardTransactions": "cards/get_naira_card_transactions", "NairaFreezeCard": "naira_cards/freeze_card", "NairaUnFreezeCard": "naira_cards/unfreeze_card"}, "UserColloborationCount": "https://rtc.pactocoin.com/api/v2/work-circle/daily-message-count/{jobId}"}