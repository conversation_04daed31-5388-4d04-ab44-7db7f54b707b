﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.RegularExpressions;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class IsDomainFormatValid : ValidationAttribute
    {
        public IsDomainFormatValid() { }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var domains = value as List<string>;
            if (domains.Any())
            {
                string pattern = @"^(?!-)([a-zA-Z0-9-]{1,63}(?<!-)\.)+[a-zA-Z]{2,}$";
                Regex regex = new Regex(pattern);

                foreach (var domain in domains)
                {
                    if (regex.IsMatch(domain.ToLower()))
                    {
                        string[] publicDomains = { "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "aol.com", "icloud.com", "ymail.com", "sharklasers.com", "grr.la", "yopmail.com" };

                        if (publicDomains.Any(d => d.Equals(domain)))
                            return new ValidationResult("Domain must be a custom company email domain");

                        return ValidationResult.Success;
                    }

                    return new ValidationResult($"{domain} is not a valid domain. The format is wrong");
                }
            }

            return ValidationResult.Success;
        }
    }
}
