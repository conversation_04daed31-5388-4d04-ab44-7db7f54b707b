﻿using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.Graph.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProject.ViewModel
{
    public class ProjectReportDetailsDto
    {
        public string Id { get; set; }
        public string ProjectName { get; set; }
        public string totalTimeTracked { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<UserDto> TeamMembers { get; set; } = new List<UserDto>();
        public string Summary { get; set; }
        public string Description { get; set; }
        public List<string> ProjectFilesUrls { get; set; } = new List<string>();
        public ProjectAnalyticsDto ProjectAnalytics { get; set; }
        public List<SprintReportSummaryDto> SprintSummary { get; set; } = new List<SprintReportSummaryDto>();
    }
}
