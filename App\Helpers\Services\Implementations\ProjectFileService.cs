﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class ProjectFileService : IProjectFileService
    {

        private JobProDbContext Db;
        public ProjectFileService(JobProDbContext _db) => Db = _db;


        public async Task<ProjectFile> AddNewProjectFileAsync(ProjectFile projectFile)
        {
            Db.ProjectFile.Add(projectFile);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return projectFile; } else { return null; }
        }

        public async Task<bool> DeleteFilesAsync(Guid id)
        {
            var file = await Db.ProjectFile.Where(x => x.Id == id).FirstOrDefaultAsync();

            if (file == null)
            {
                return false;
            }
            Db.ProjectFile.Remove(file);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }
        }
    }
}
