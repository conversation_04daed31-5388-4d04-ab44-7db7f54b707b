using Microsoft.Extensions.Configuration;

namespace Jobid.App.AdminConsole.Configuration
{
    public class AwsSettings
    {
        public string Region { get; set; }
        public ChimeSettings ChimeVoiceConnector { get; set; }
        public SnsSettings SNS { get; set; }
    }

    public class ChimeSettings
    {
        public string Id { get; set; }
        public string SipMediaAppId { get; set; }
    }

    public class SnsSettings
    {
        public string WalletTopicArn { get; set; }
    }

    public class AwsConfiguration
    {
        private readonly IConfiguration _configuration;

        public AwsConfiguration(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public AwsSettings GetAwsSettings()
        {
            return _configuration.GetSection("AWS").Get<AwsSettings>();
        }
    }
}
