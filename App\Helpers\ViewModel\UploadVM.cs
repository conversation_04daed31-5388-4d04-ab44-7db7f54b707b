﻿using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Utils.Attributes;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.ViewModel
{
    public class UploadVM
    {
        [Required]
        [AllowedExtensions(new string[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx" , ".doc" })]
        public IFormFile UploadFile { get; set; }
    }
}
