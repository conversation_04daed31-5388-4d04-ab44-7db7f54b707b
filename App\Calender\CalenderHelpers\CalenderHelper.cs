﻿using Ical.Net;
using Ical.Net.CalendarComponents;
using Ical.Net.DataTypes;
using Ical.Net.Serialization;
using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;


namespace Jobid.App.Calender.CalenderHelpers
{
    public static class CalenderHelper
    {
        #region Create Calender Event
        public static MemoryStream CreateCalenderEvent(CalenderEventDto env)
        {
            var publicContext = new JobProDbContext(new DbContextSchema());
            var subdomainContext = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(env.subdomain));
            string userId = subdomainContext.UserProfiles.Where(u => u.Email == env.Email).Select(x => x.UserId).FirstOrDefault();
            RecurrencePattern recurrancePattern = GetReocurrancePattern(env);

            // Get the local time zone
            TimeZoneInfo localTimeZone = TimeZoneInfo.Local;
            var e = new CalendarEvent
            {
                Start = new CalDateTime(env.StartDateAndTime.ToLocalTime()),
                End = new CalDateTime(env.EndDateAndTime.ToLocalTime()),
                RecurrenceRules = new List<RecurrencePattern> { recurrancePattern },
                Organizer = new Organizer { CommonName = env.Organizer.FirstName + " " + env.Organizer.LastName, Value = new Uri("mailto:" + env.Organizer.Email) },
                Summary = env.Title,
                Description = "Meeting Link: " + "<a href=" + env.MeetingLink + ">" + env.MeetingLink + "</a>",
                Location = env.Location,
            };

            foreach (var attendee in env.Attendees)
            {
                String userFullName = null;
                if (!string.IsNullOrEmpty(attendee.Value))
                    userFullName = publicContext.Users.Where(u => u.Id == attendee.Value).Select(x => x.FirstName + " " + x.LastName)
                        .FirstOrDefault();
                e.Attendees.Add(new Attendee { Value = new Uri("mailto:" + attendee.Key), CommonName = userFullName is null ? attendee.Key : userFullName });
            }

            // Create a reminder before the event
            Alarm reminder = new Alarm();
            reminder.Action = AlarmAction.Display;
            reminder.Trigger = new Trigger(new TimeSpan(0, -env.NotifyMeIn, 0));
            e.Alarms.Add(reminder);

            var calendar = new Ical.Net.Calendar();
            calendar.Events.Add(e);

            var serializer = new CalendarSerializer();
            var serializedCalendar = serializer.SerializeToString(calendar);

            byte[] byteArray = Encoding.ASCII.GetBytes(serializedCalendar);
            MemoryStream stream = new MemoryStream(byteArray);

            var mailgunService = new MailgunService();
            string senderEmail = "<EMAIL>";
            //var response = mailgunService.SendEmailWithAttachment(senderEmail, "Meeting Invitation", new List<string> { env.Email }, stream).Result;

            // Add the event to the database to allow for updates and cancellations
            var iCalenderDto = new CalenderExternalIntegration
            {
                MeetingId = env.MeetingId,
                IcalenderEventId = e.Uid,
                UserId = userId,
                Email = env.Organizer.Email,
            };

            subdomainContext.CalenderExternalIntegrations.Add(iCalenderDto);
            subdomainContext.SaveChanges();

            return stream;
        }
        #endregion

        #region Get Reocurrance Pattern - Private Method
        private static RecurrencePattern GetReocurrancePattern(CalenderEventDto env)
        {
            RecurrencePattern recurrancePattern = null;

            // Add recurrance rule for normal Frequency
            if (!string.IsNullOrEmpty(env.Frequency))
            {
                Enum.TryParse<MeetingFrequency>(env.Frequency, out var frequency);
                switch (frequency)
                {
                    case MeetingFrequency.Daily:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Daily, 1) { Count = 365 };
                        break;
                    case MeetingFrequency.Weekly:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Weekly, 1) { Count = 48 };
                        break;
                    case MeetingFrequency.Monthly:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Monthly, 1) { Count = 12 };
                        break;
                    case MeetingFrequency.Yearly:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Yearly, 1) { Count = 10 };
                        break;
                    default:
                        break;
                }
            }
            else
            {
                if (env.CustomFrequencyDto is not null)
                {
                    int interval = env.CustomFrequencyDto.RepeatCount;
                    switch (env.CustomFrequencyDto.RepeatEvery)
                    {
                        case "Day":
                            recurrancePattern = new RecurrencePattern(FrequencyType.Daily, interval);
                            if (env.CustomFrequencyDto.EndStatus == EndStatus.After && env.CustomFrequencyDto.EndsAfter is not null)
                                recurrancePattern.Count = env.CustomFrequencyDto.EndsAfter.Value;

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.On && env.CustomFrequencyDto.EndsOn is not null)
                                recurrancePattern.Until = env.CustomFrequencyDto.EndsOn.Value;
                            break;
                        case "Week":
                            // Map your RepeatOn property to days of the week if RepeatOn is an integer
                            var daysOfWeek = env.CustomFrequencyDto.RepeatOn
                                .Split(',')
                                .Select(day => (DayOfWeek)Enum.Parse(typeof(DayOfWeek), day.Trim(), true));


                            recurrancePattern = new RecurrencePattern(FrequencyType.Weekly, interval)
                            {
                                ByDay = daysOfWeek.Select(day => new WeekDay(day)).ToList()
                            };

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.After && env.CustomFrequencyDto.EndsAfter is not null)
                                recurrancePattern.Count = env.CustomFrequencyDto.EndsAfter.Value;

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.On && env.CustomFrequencyDto.EndsOn is not null)
                                recurrancePattern.Until = env.CustomFrequencyDto.EndsOn.Value;
                            break;
                        case "Month":
                            // Map your RepeatOn property to the day of the month
                            var daysOfMonth = env.CustomFrequencyDto.RepeatOn.Split(",")
                                .Select(int.Parse).ToList(); // Assuming RepeatOn contains the days of the month
                            recurrancePattern = new RecurrencePattern(FrequencyType.Monthly, interval)
                            {
                                ByMonthDay = daysOfMonth
                            };

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.After && env.CustomFrequencyDto.EndsAfter is not null)
                                recurrancePattern.Count = env.CustomFrequencyDto.EndsAfter.Value;

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.On && env.CustomFrequencyDto.EndsOn is not null)
                                recurrancePattern.Until = env.CustomFrequencyDto.EndsOn.Value;
                            break;
                        case "Year":
                            var monthAndDay = env.CustomFrequencyDto.RepeatOn.Split(',').Select(int.Parse).ToArray();
                            recurrancePattern = new RecurrencePattern(FrequencyType.Yearly, interval)
                            {
                                ByMonth = new List<int> { monthAndDay[0] },
                                ByMonthDay = new List<int> { monthAndDay[1] }
                            };

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.After && env.CustomFrequencyDto.EndsAfter is not null)
                                recurrancePattern.Count = env.CustomFrequencyDto.EndsAfter.Value;

                            if (env.CustomFrequencyDto.EndStatus == EndStatus.On && env.CustomFrequencyDto.EndsOn is not null)
                                recurrancePattern.Until = env.CustomFrequencyDto.EndsOn.Value;
                            break;
                        default:
                            break;
                    }
                }
            }

            return recurrancePattern;
        }
        #endregion

        #region Update Calender Event
        public static MemoryStream UpdateCalenderEvent(CalenderEventDto env)
        {
            var publicContext = new JobProDbContext(new DbContextSchema());
            var subdomainContext = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(env.subdomain));
            string userId = subdomainContext.UserProfiles.Where(u => u.Email == env.Organizer.Email).Select(x => x.UserId).FirstOrDefault();
            var existingRecord = subdomainContext.CalenderExternalIntegrations.Where(x => x.MeetingId == env.MeetingId && (x.Email == env.Email || x.UserId == userId)).FirstOrDefault();
            if (existingRecord is null)
            {
                // Log that existingRecord is null
                return null;
            }

            //Repeat daily for 5 days
            RecurrencePattern recurrancePattern = null;
            if (env.CustomFrequencyDto is not null)
            {
                var rule = new RecurrencePattern(FrequencyType.Daily, 1) { Count = 5 };
            }

            // Add recurrance rule for normal Frequency
            if (!string.IsNullOrEmpty(env.Frequency))
            {
                Enum.TryParse<MeetingFrequency>(env.Frequency, out var frequency);
                switch (frequency)
                {
                    case MeetingFrequency.Daily:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Daily, 1) { Count = 365 };
                        break;
                    case MeetingFrequency.Weekly:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Weekly, 1) { Count = 48 };
                        break;
                    case MeetingFrequency.Monthly:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Monthly, 1) { Count = 12 };
                        break;
                    case MeetingFrequency.Yearly:
                        recurrancePattern = new RecurrencePattern(FrequencyType.Yearly, 1) { Count = 10 };
                        break;
                    default:
                        break;
                }
            }

            var calendar = new Ical.Net.Calendar();
            var targetEvent = new CalendarEvent
            {
                Start = new CalDateTime(env.StartDateAndTime),
                End = new CalDateTime(env.EndDateAndTime),
                RecurrenceRules = new List<RecurrencePattern> { recurrancePattern },
                Organizer = new Organizer { CommonName = env.Organizer.FirstName + " " + env.Organizer.LastName, Value = new Uri("mailto:" + env.Organizer.Email) },
                Summary = env.Title,
                Description = "Meeting Link: " + "<a href=" + env.MeetingLink + ">" + env.MeetingLink + "</a>",
                Location = env.Location,
                Uid = existingRecord.IcalenderEventId,
            };

            foreach (var attendee in env.Attendees)
            {
                String userFullName = null;
                if (!string.IsNullOrEmpty(attendee.Value))
                    userFullName = publicContext.Users.Where(u => u.Id == attendee.Value).Select(x => x.FirstName + " " + x.LastName)
                        .FirstOrDefault();
                targetEvent.Attendees.Add(new Attendee { Value = new Uri("mailto:" + attendee.Key), CommonName = userFullName is null ? attendee.Key : userFullName });
            }

            // clear existing alarms and add new ones
            targetEvent.Alarms.Clear();
            Alarm reminder = new Alarm();
            reminder.Action = AlarmAction.Display;
            reminder.Trigger = new Trigger(new TimeSpan(0, -env.NotifyMeIn, 0));
            targetEvent.Alarms.Add(reminder);

            calendar.Events.Add(targetEvent);

            // Update the targetEvent
            var serializer = new CalendarSerializer();
            var serializedCalendar = serializer.SerializeToString(calendar);

            byte[] byteArray = Encoding.ASCII.GetBytes(serializedCalendar);
            MemoryStream stream = new MemoryStream(byteArray);

            var mailgunService = new MailgunService();
            string senderEmail = "<EMAIL>";
            //var response = mailgunService.SendEmailWithAttachment(senderEmail, "Meeting Update", new List<string> { env.Email }, stream).Result;

            return stream;
        }
        #endregion
    }
}
