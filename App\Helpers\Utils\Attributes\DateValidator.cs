﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class DateValidator : ValidationAttribute
    {
        public DateValidator()
        {
            
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var date = value as DateTime?;
            if (date != null)
            {
                if (date < DateTime.UtcNow)
                {
                    return new ValidationResult("Date must not be in the past");
                }
            }

            return ValidationResult.Success;
        }
    }
}
