﻿namespace Jobid.App.RabbitMQ
{
    public static class RabbitMQConstants
    {
        public const string UserCreatedEvent = "user-created-event";
        public const string UserUpdatedEvent = "user-updated-event";
        public const string EmployeeCreatedEvent = "employee-created-event";
        public const string EmployeeUpdatedEvent = "employee-updated-event";

        public const string AICreateMeetingEvent = "ai-create-meeting-exchange";
        public const string AIUpdateMeetingEvent = "ai-update-meeting-exchange";
        public const string AICreateTodoEvent = "ai-create-todo-exchange";

        public const string SubscriptionEvent = "subscription-event";

        public const string TenantCreatedEvent = "tenant-created-event";
        public const string TenantUpdatedEvent = "tenant-updated-event";

        public const string PublishProductUpdateEvent = "publish-product-update-event";
        public const string ProductUpdateQueue = "product-update-queue";
        public const string ProductUpdateKey = "product-update-key";

        public const string SubscriptionKey = "subscription-key";

        // AI
        public const string AICreateMeetingQueue = "ai-create-meeting-queue";
        public const string AIUpdateMeetingQueue = "ai-update-meeting-queue";
        public const string AICreateTodoQueue = "ai-create-todo-queue";
    }
}
