﻿#region Using Statements
using AutoMapper;
using Hangfire;
using Jobid.App.ActivityLog.contract;
using Jobid.App.ActivityLog.Model;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Notification.Hubs;
using Jobid.App.Notification.Models;
using Jobid.App.Notification.ViewModel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using TodoStatus = Jobid.App.Helpers.Models.TodoStatus;
using static Jobid.App.Helpers.Utils.Extensions;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Tenant.Contract;
#endregion

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class TimeSheetService : ITimeSheetService
    {
        public JobProDbContext Db { get; }
        public JobProDbContext Dbo { get; }

        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IMapper _mapper;
        private readonly IActivityService _activityService;
        private readonly IHubContext<NotificationHub, INotificationClient> _hubContext;
        private readonly IAdminService _adminService;
        private readonly IWebHostEnvironment _environment;
        private readonly IEmailService _emailService;
        private readonly HttpContextAccessor _httpContextAccessor;
        private readonly ITenantService _tenantService;

        public TimeSheetService(JobProDbContext _db, JobProDbContext publicSchemaContext, IAWSS3Sevices aWSS3Sevices, IMapper mapper, IActivityService activityService, IHubContext<NotificationHub, INotificationClient> hubContext, IAdminService adminService, IWebHostEnvironment environment, IEmailService emailService, ITenantService tenantService)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _aWSS3Sevices = aWSS3Sevices;
            _mapper = mapper;
            _activityService = activityService;
            _hubContext = hubContext;
            _adminService = adminService;
            _environment = environment;
            _emailService = emailService;
            _tenantService = tenantService;
        }

        #region Add TimeSheet
        /// <summary>
        /// Add TimeSheet
        /// </summary>
        /// <param name="timeSheetVm"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<TimeSheet> AddTimeSheet(TimeSheetVm timeSheetVm)
        {
            //validate project and sprint selection
            if (string.IsNullOrEmpty(timeSheetVm.ProjectId) && string.IsNullOrEmpty(timeSheetVm.SprintId))
                throw new Exception("Please select either sprint or project");

            // Validate StartTime and EndTime
            if (timeSheetVm.EndTime <= timeSheetVm.StartTime)
                throw new Exception("EndTime must be greater than StartTime");

            var days = (int)(timeSheetVm.EndTime - timeSheetVm.StartTime).TotalDays;

            if (!string.IsNullOrEmpty(timeSheetVm.TodoName))
            {
                // Check if the name is already in use
                var timeSheetExists = await Db.TimeSheet
                    .Where(x => x.ProjectMgmt_ProjectId == timeSheetVm.ProjectId && x.TodoName.ToLower() == timeSheetVm.TodoName.ToLower())
                    .FirstOrDefaultAsync();
                if (timeSheetExists != null)
                    throw new RecordAlreadyExistException($"TimeSheet with name '{timeSheetVm.TodoName}' already exists");
            }
            else
            {
                timeSheetVm.TodoName = string.Empty;
            }



            ProjectMgmt_Project project = null;
            if (!string.IsNullOrEmpty(timeSheetVm.ProjectId))
            {
                project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId.ToString() == timeSheetVm.ProjectId).FirstOrDefaultAsync();
                if (project == null)
                    throw new RecordNotFoundException("Project does not exist");
            }

            SprintProject sprint = null;
            if (!string.IsNullOrEmpty(timeSheetVm.SprintId))
            {
                sprint = await Db.SprintProjects.Where(x => x.Id.ToString() == timeSheetVm.SprintId).FirstOrDefaultAsync();
                if (sprint == null)
                    throw new RecordNotFoundException("Sprint does not exist");
            }


            var timeSheet = new TimeSheet()
            {
                Summary = timeSheetVm.Summary ?? string.Empty,
                Comments = timeSheetVm.Comments ?? string.Empty,
                StartDate = timeSheetVm.StartDate,
                DateLogged = GetAdjustedDateTimeBasedOnTZNow(),
                AssignedTo = timeSheetVm.MemberId,
                Description = timeSheetVm.Description,
                StartTime = timeSheetVm.StartTime,
                EndTime = timeSheetVm.EndTime,
                Status = timeSheetVm.Status,
                ProjectMgmt_ProjectId = timeSheetVm.ProjectId,
                UserId = timeSheetVm.UserId,
                CreatedBy = timeSheetVm.UserId,
                TodoName = timeSheetVm.TodoName,
                IsBillable = timeSheetVm.IsBillable,
                LastUpdate = GetAdjustedDateTimeBasedOnTZNow(),
                SprintId = string.IsNullOrEmpty(timeSheetVm.SprintId) ? project.SprintProjectId : timeSheetVm.SprintId,
                ClientName = timeSheetVm.ClientName
            };

            if (!string.IsNullOrEmpty(timeSheetVm.Duration))
            {
                if (!timeSheetVm.TimeSpent.IsTimeInTheCorrectFormat())
                    throw new Exception($"'{timeSheetVm.TimeSpent}' is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");


                TimeSpan timeDuration;
                if (!TimeSpan.TryParse(timeSheetVm.Duration, CultureInfo.InvariantCulture, out timeDuration))
                {
                    throw new Exception($"'{timeSheetVm.Duration}' is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }
                timeSheet.Duration = timeDuration.ToString();
            }

            TimeSpan time;
            if (!string.IsNullOrEmpty(timeSheetVm.TimeSpent))
            {
                if (!TimeSpan.TryParse(timeSheetVm.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"'{timeSheetVm.TimeSpent}' is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }
                timeSheet.TimeSpent = time.ToString();
                timeSheet.ActualTimeSpent = time.ToString();
            }
            else
            {

                TimeSpan timeDurtion = timeSheetVm.EndTime - timeSheetVm.StartTime;
                string formattedDuration = $"{(int)timeDurtion.TotalHours}h {timeDurtion.Minutes}m";
                timeSheetVm.Duration = formattedDuration;

                TimeSpan timeSpent = timeSheetVm.EndTime - timeSheetVm.StartTime;
                string formattedTimeSpent = $"{(int)timeSpent.TotalDays}.{timeSpent.Hours:D2}:{timeSpent.Minutes:D2}:{timeSpent.Seconds:D2}";
                timeSheetVm.TimeSpent = formattedTimeSpent;
                timeSheet.TimeSpent = formattedTimeSpent;
                timeSheet.ActualTimeSpent = formattedTimeSpent;
            }


            var tagList = new List<ProjectTag>() { };
            if (timeSheetVm.Tags != null && timeSheetVm.Tags.Any())
            {
                var tags = timeSheetVm.Tags.Select(x => new ProjectTag()
                {
                    TagName = x

                }).ToList();

                tagList.AddRange(tags);
            }

            timeSheet.ProjectTags = tagList;
            await Db.TimeSheet.AddAsync(timeSheet);
            int result = await Db.SaveChangesAsync();
            if (result > 0)
            {
                var users = new List<User>();
                var timeUser = new List<TimeSheetMemberId>();
                if (string.IsNullOrEmpty(timeSheetVm.MemberId))
                {
                    var user = await Dbo.Users.Where(x => x.Id == timeSheetVm.MemberId).FirstOrDefaultAsync();
                    if (user != null && !users.Any(x => x.Id == user.Id))
                    {
                        var newmberid = new TimeSheetMemberId()
                        {
                            TimesheetId = timeSheet.Id.ToString(),
                            MemberId = timeSheetVm.MemberId

                        };
                        timeUser.Add(newmberid);
                    }

                    Db.TimeSheetMemberIds.AddRange(timeUser);
                    await Db.SaveChangesAsync();
                }
            }

            if (result > 0)
            {
                // Log the activity
                var description = $"Time Sheet record created by";
                var summary = $"Time Sheet record created";
                await LogActivity(timeSheetVm.UserId, description, summary, timeSheet.Id.ToString());

                return timeSheet;
            }
            else { return null; }
        }
        #endregion

        #region Úpdate TimeSheet
        /// <summary>
        /// Update TimeSheet
        /// </summary>
        /// <param name="timeSheetVm"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<bool> UpdateTimeSheet(TimeSheetVm timeSheetVm, Guid id)
        {
            // Check if the name is already in use
            var timeSheetExists = await Db.TimeSheet
                .Where(x => x.ProjectMgmt_ProjectId == timeSheetVm.ProjectId && x.TodoName.ToLower() == timeSheetVm.TodoName.ToLower())
                .FirstOrDefaultAsync();
            if (timeSheetExists != null)
                throw new RecordAlreadyExistException($"TimeSheet with name '{timeSheetVm.TodoName}' already exists");

            var sprint = await Db.SprintProjects.Where(x => x.Id.ToString() == timeSheetVm.SprintId).FirstOrDefaultAsync();
            if (sprint == null)
                throw new RecordNotFoundException("Sprint does not exist");

            var days = (int)(timeSheetVm.EndTime - timeSheetVm.StartTime).TotalDays;
            var timeSheet = await Db.TimeSheet.Where(x => x.Id == id).FirstOrDefaultAsync();
            if (timeSheet == null)
            {
                return false;
            }

            var tags = Db.ProjectTag.Where(x => x.TimeSheetId == id);
            if (tags.Any())
            {
                Db.ProjectTag.RemoveRange(tags);
                await Db.SaveChangesAsync();
            }

            timeSheet.Summary = timeSheetVm.Summary;
            timeSheet.Status = timeSheetVm.Status;
            timeSheet.StartTime = timeSheet.StartTime;
            timeSheet.EndTime = timeSheet.EndTime;
            timeSheet.Description = timeSheet.Description;
            timeSheet.Comments = timeSheet.Comments;
            timeSheet.Priority = timeSheet.Priority;
            timeSheet.TimeSpent = timeSheet.TimeSpent;
            timeSheet.ActualTimeSpent = timeSheet.TimeSpent;
            timeSheet.Duration = Utility.DateDurationCalculator(days);
            timeSheet.TodoName = timeSheetVm.TodoName;
            timeSheet.IsBillable = timeSheetVm.IsBillable;
            timeSheet.LastUpdate = GetAdjustedDateTimeBasedOnTZNow();
            timeSheet.UpdatedBy = timeSheetVm.UserId;
            timeSheet.SprintId = timeSheetVm.SprintId;

            if (timeSheetVm.Tags.Any())
            {
                timeSheet.ProjectTags = timeSheetVm.Tags.Select(x => new ProjectTag()
                {
                    TagName = x,
                }).ToList();

            }
            Db.TimeSheet.Update(timeSheet);
            int result = await Db.SaveChangesAsync();
            if (result > 0)
            {
                // Log the activity
                var description = $"Time Sheet record updated by";
                var summary = $"Time Sheet record updated";
                await LogActivity(timeSheetVm.UserId, description, summary, timeSheet.Id.ToString());

                return true;
            }
            else { return false; }
        }
        #endregion

        #region Get timesheet records
        /// <summary>
        /// Gets timesheet records
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="projectIds"></param>
        /// <param name="filters"></param>
        /// <param name="isArchived"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetTimeSheetRecords(PaginationParameters parameters, List<string> projectIds, TimeSheetFilters filters, bool isArchived = false)
        {
            var todoList = new List<TimeSheetTodoDto>();
            var teamMemberList = new List<UserDetailsForTeamSheetDto>();
            var projectsDetails = new List<AllProjectsForTimeSheetDto>();
            var allSprints = Db.SprintProjects.ToList();

            if (projectIds.Any() || filters.GetForAllProjects)
            {
                if (filters.GetForAllProjects)
                    projectIds = await Db.ProjectMgmt_Projects.Select(x => x.ProjectId.ToString()).ToListAsync();

                foreach (var projectId in projectIds)
                {
                    ProjectMgmt_Project project = null;
                    if (filters.ProjectStatus.Any())
                        project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId.ToString() == projectId && filters.ProjectStatus.Contains(x.ProjectStatus)).FirstOrDefaultAsync();
                    else
                        project = await Db.ProjectMgmt_Projects.Where(x => x.ProjectId.ToString() == projectId).FirstOrDefaultAsync();
                    if (project == null)
                    {
                        throw new RecordNotFoundException("Project not found");
                    }

                    if (!filters.TeamIds.Any() && !filters.TeamMemberIds.Any())
                    {
                        var todos = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                            .Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && (x.IsArchived == isArchived
                            || x.TodoStatus == ProjectManagementStatus.Completed.ToString()
                            || x.TodoStatus == ProjectManagementStatus.InProgress.ToString()))
                            .Select(x => new TimeSheetTodoDto()
                            {
                                Id = x.Id,
                                TodoSummary = x.TodoSummary,
                                TodoDescription = x.TodoDescription,
                                TodoId = x.TodoId,
                                TodoName = x.TodoName,
                                ProjectName = project.Name,
                                TimeLeft = x.TimeLeft,
                                ActualHours = x.TimeSpent,
                                TodoStatus = x.TodoStatus,
                                Priority = x.Priority.ToString(),
                                CreatedBy = x.CreatedBy,
                                CreatedDate = x.CreatedDate,
                                LastUpdate = x.LastUpdate,
                                DueDate = x.DueDate,
                                ExpectedHours = x.Duration,
                                EndDate = x.EndTime,
                                StartDate = x.StartDateAndTime,
                                ExistingTodoLink = x.ExistingTodoLink,
                                WillBeDueIn = x.WillBeDueIn,
                                IsBillable = x.IsBillable,
                                AmountPerHour = x.AmountPerHour,
                                LockTodo = x.LockTodo,
                                ApprovalStatus = x.ApprovalStatus,
                                RegisteredHours = x.ActualTimeSpent == "0.00:00:00" ? x.TimeSpent : x.ActualTimeSpent,
                                SprintId = x.SprintProjectId.ToString(),
                                SprintName = x.SprintProject.Name,
                                CompletionDate = x.CompletedAt,
                            })
                            .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

                        // Get todos from timesheet table
                        var todosFromTH = await Db.TimeSheet
                            .Where(x => x.ProjectMgmt_ProjectId == projectId &&
                                        x.IsArchived == isArchived &&
                                        x.Status == TimeSheetStatus.Completed)
                            .Select(x => new TimeSheetTodoDto
                            {
                                Id = x.Id,
                                TodoSummary = x.Summary,
                                TodoDescription = x.Description,
                                TodoName = x.TodoName,
                                ProjectName = project.Name,
                                ActualHours = x.TimeSpent,
                                TodoStatus = x.Status.ToString(),
                                Priority = x.Priority.HasValue ? x.Priority.Value.ToString() : string.Empty,
                                CreatedBy = x.CreatedBy,
                                CreatedDate = x.DateLogged ?? DateTime.MinValue,
                                LastUpdate = x.LastUpdate ?? DateTime.MinValue,
                                ExpectedHours = x.Duration,
                                StartDate = x.StartDate,
                                IsBillable = x.IsBillable,
                                AmountPerHour = x.AmountPerHour,
                                RegisteredHours = x.TimeSpent,
                                SprintId = x.SprintId,
                                CompletionDate = x.CompletionDate,
                                AssignedTo = Db.UserProfiles
                                    .Where(u => u.UserId == x.AssignedTo)
                                    .Select(user => new UserDto
                                    {
                                        Id = user.UserId,
                                        Email = user.Email,
                                        FirstName = user.FirstName,
                                        LastName = user.LastName
                                    }).ToList()
                            })
                            .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

                        // Get the user for each todo
                        await GetAssignedUserDetails(todosFromTH, project);
                        todoList.AddRange(todosFromTH.Items.ToList());

                        // Get the user for each todo
                        await GetAssignedUserDetails(todos, project);
                        todoList.AddRange(todos.Items.ToList());

                    }
                    else
                    {
                        if (filters.TeamIds.Any())
                        {
                            var teamMembers = await Db.TeamMembers.Where(x => filters.TeamIds.Contains(x.TeamId.ToString())).Select(x => x.UserId).ToListAsync();

                            foreach (var memberId in teamMembers)
                            {
                                var member = await GetUserDetailsForTH(projectId, project, memberId, filters.UserId);
                                teamMemberList.Add(member);
                                //member.TotalTimeSpentOnTodos = totalTime.ToString(@"dd\.hh\:mm\:ss");
                            }
                        }

                        if (filters.TeamMemberIds.Any())
                        {
                            // Get todoId for each team member
                            var todoIds = await Db.projectMgmt_TodoUsers.Where(x => filters.TeamMemberIds.Contains(x.UserId)).Select(x => x.ProjectMgmt_TodoId).ToListAsync();

                            var todos = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                            .Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && todoIds.Contains(x.Id)
                            && (x.IsArchived == isArchived
                            || x.TodoStatus == ProjectManagementStatus.Completed.ToString()
                            || x.TodoStatus == ProjectManagementStatus.InProgress.ToString()))
                            .Select(x => new TimeSheetTodoDto()
                            {
                                Id = x.Id,
                                TodoSummary = x.TodoSummary,
                                TodoDescription = x.TodoDescription,
                                TodoId = x.TodoId,
                                TodoName = x.TodoName,
                                ProjectName = project.Name,
                                TimeLeft = x.TimeLeft,
                                ActualHours = x.TimeSpent,
                                TodoStatus = x.TodoStatus,
                                Priority = x.Priority.ToString(),
                                CreatedBy = x.CreatedBy,
                                CreatedDate = x.CreatedDate,
                                LastUpdate = x.LastUpdate,
                                DueDate = x.DueDate,
                                ExpectedHours = x.Duration,
                                EndDate = x.EndTime,
                                StartDate = x.StartDateAndTime,
                                ExistingTodoLink = x.ExistingTodoLink,
                                WillBeDueIn = x.WillBeDueIn,
                                IsBillable = x.IsBillable,
                                AmountPerHour = x.AmountPerHour,
                                LockTodo = x.LockTodo,
                                ApprovalStatus = x.ApprovalStatus,
                                CompletionDate = x.CompletedAt,
                                RegisteredHours = x.ActualTimeSpent == "0.00:00:00" ? x.TimeSpent : x.ActualTimeSpent,
                                SprintName = x.SprintProject.Name,
                            })
                            .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

                            // Get todos from timesheet table
                            var todosFromTH = await Db.TimeSheet
                                .Where(x => x.ProjectMgmt_ProjectId == projectId && todoIds.Contains(x.Id) && x.IsArchived == isArchived && x.Status == TimeSheetStatus.Completed)
                                .Select(x => new TimeSheetTodoDto()
                                {
                                    Id = x.Id,
                                    TodoSummary = x.Summary,
                                    TodoDescription = x.Description,
                                    TodoName = x.TodoName,
                                    ProjectName = project.Name,
                                    ActualHours = x.TimeSpent,
                                    TodoStatus = x.Status.ToString(),
                                    Priority = x.Priority.ToString(),
                                    CreatedBy = x.CreatedBy,
                                    CreatedDate = x.DateLogged.Value,
                                    LastUpdate = x.LastUpdate.Value,
                                    ExpectedHours = x.Duration,
                                    StartDate = x.StartDate,
                                    IsBillable = x.IsBillable,
                                    AmountPerHour = x.AmountPerHour,
                                    RegisteredHours = x.TimeSpent,
                                    CompletionDate = x.CompletionDate,
                                })
                                .ToPageListAsync(parameters.PageNumber, parameters.PageSize);

                            // Get the user for each todo
                            await GetAssignedUserDetails(todosFromTH, project);
                            todoList.AddRange(todosFromTH.Items.ToList());

                            // Get the user for each todo
                            await GetAssignedUserDetails(todos, project);
                            todoList.AddRange(todos.Items.ToList());
                        }
                    }
                }

                if (filters.SprintIds.Any())
                {
                    var sprintName = await Db.SprintProjects.Where(x => filters.SprintIds.Contains(x.Id.ToString())).Select(x => x.Name).ToListAsync();
                    todoList = todoList.Where(todo => sprintName.Contains(todo.SprintName)).ToList();
                }

                // Log the activity
                await LogGetTimeSheetRecordsActivity(filters);

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Suucess",
                    Data = new
                    {
                        TodoList = todoList.AsQueryable().ToPageList(parameters.PageNumber, parameters.PageSize),
                        TeamMembers = teamMemberList,
                        ProjectDetails = projectsDetails
                    }
                };
            }
            else
            {
                Page<ProjectMgmt_Project> projects = null;
                if (!filters.ProjectStatus.Any() && !filters.TeamIds.Any() && !filters.TeamMemberIds.Any() && filters.IsBillable == null)
                    projects = await Db.ProjectMgmt_Projects.ToPageListAsync(parameters.PageNumber, parameters.PageSize);
                else if (!filters.ProjectStatus.Any() && !filters.TeamIds.Any() && !filters.TeamMemberIds.Any() && filters.IsBillable != null)
                    projects = await Db.ProjectMgmt_Projects.Where(x => x.IsBillable == filters.IsBillable).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

                if (filters.ProjectStatus.Any() && filters.IsBillable == null)
                    projects = await Db.ProjectMgmt_Projects.Where(x => filters.ProjectStatus.Contains(x.ProjectStatus)).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
                else if (filters.ProjectStatus.Any() && filters.IsBillable != null)
                    projects = await Db.ProjectMgmt_Projects.Where(x => filters.ProjectStatus.Contains(x.ProjectStatus) && x.IsBillable == filters.IsBillable).ToPageListAsync(parameters.PageNumber, parameters.PageSize);

                foreach (var project in projects.Items)
                {
                    var pro = new AllProjectsForTimeSheetDto
                    {
                        ProjectId = project.ProjectId.ToString(),
                        ProjectName = project.Name,
                        StartDate = project.StartDate,
                        EndDate = project.EndDate,
                        Status = project.ProjectStatus,
                        IsBillable = project.IsBillable,
                        AssignedTo = null,
                        ToDosCount = await Db.ProjectMgmt_Todo.CountAsync(x => x.ProjectMgmt_ProjectId == project.ProjectId),
                    };

                    // Get the total timespent on the all the todos under the project and the project value
                    var totalTime = new TimeSpan();
                    decimal projectValue = 0.0M;

                    var todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).ToListAsync();
                    var todosFromTh = await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId.ToString() && x.Status == TimeSheetStatus.Completed)
                        .Select(x => new ProjectMgmt_Todo()
                        {
                            Id = x.Id,
                            TimeSpent = x.TimeSpent,
                            IsBillable = x.IsBillable,
                        })
                        .ToListAsync();
                    todos.AddRange(todosFromTh);

                    foreach (var todo in todos)
                    {
                        if (todo.ActualTimeSpent == "0.00:00:00")
                            todo.ActualTimeSpent = null;

                        TimeSpan time;
                        if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                        {
                            throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                        }

                        totalTime += time;

                        // Get project value
                        if (project.IsBillable)
                        {
                            var amountPerMin = 0.0M;
                            switch (project.AmountFrequency)
                            {
                                case AmountFrequency.Hourly:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 60;
                                    break;
                                case AmountFrequency.Daily:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 1440;
                                    break;
                                case AmountFrequency.Weekly:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 10080;
                                    break;
                                case AmountFrequency.Monthly:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 43800;
                                    break;
                                default:
                                    break;
                            }

                            if (todo.IsBillable)
                            {
                                var todoAmount = amountPerMin * (decimal)time.TotalMinutes;
                                projectValue += todoAmount;
                            }
                        }
                    }

                    pro.ProjectValue = projectValue;
                    pro.TimeSpent = totalTime.ToString();
                    projectsDetails.Add(pro);
                };

                await LogGetTimeSheetRecordsActivity(filters);

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Suucess",
                    Data = new
                    {
                        TodoList = todoList,
                        TeamMembers = teamMemberList,
                        ProjectDetails = projectsDetails
                    }
                };
            }

            async Task LogGetTimeSheetRecordsActivity(TimeSheetFilters filters)
            {
                var description = $"Time Sheet records retrieved by";
                var summary = $"Time Sheet records retrieved";
                await LogActivity(filters.UserId, description, summary);
            }
        }
        #endregion

        #region Get timesheet records for projects
        /// <summary>
        /// Gets timesheet records for all projects
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="filters"></param>
        /// <param name="isArchived"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetTimeSheetRecordsForProjects(PaginationParameters parameters, TimeSheetFilters filters, bool isArchived = false)
        {
            var projectsDetails = new List<AllProjectsForTimeSheetDto>();

            // Apply filters to projects
            var allProjectsQuery = Db.ProjectMgmt_Projects.AsQueryable();

            // Filter by ProjectStatus
            if (filters.ProjectStatus != null && filters.ProjectStatus.Any())
            {
                allProjectsQuery = allProjectsQuery.Where(p => filters.ProjectStatus.Contains(p.ProjectStatus));
            }


            // Filter by TeamIds
            if (filters.TeamIds != null && filters.TeamIds.Any())
            {
                var teamMemberIdsFromTeams = await Db.TeamMembers
                    .Where(x => filters.TeamIds.Contains(x.TeamId.ToString())).Select(x => x.UserId).ToListAsync();

                // Add these member IDs to TeamMemberIds for further filtering
                filters.TeamMemberIds.AddRange(teamMemberIdsFromTeams);
            }

            // Filter by TeamMemberIds
            if (filters.TeamMemberIds != null && filters.TeamMemberIds.Any())
            {
                allProjectsQuery = allProjectsQuery.Where(p => p.projectMgmt_ProjectUsers.Any(u => filters.TeamMemberIds.Contains(u.UserId)));
            }

            // Filter by IsBillable
            if (filters.IsBillable.HasValue)
            {
                allProjectsQuery = allProjectsQuery.Where(p => p.IsBillable == filters.IsBillable.Value);
            }

            // Filter by SprintIds
            if (filters.SprintIds != null && filters.SprintIds.Any())
            {
                allProjectsQuery = allProjectsQuery.Where(p => filters.SprintIds.Contains(p.SprintProjectId));
            }

            // Apply pagination
            var pagedProjects = await allProjectsQuery
                .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                .Take(parameters.PageSize)
                .ToListAsync();

            foreach (var project in pagedProjects)
            {
                var pro = new AllProjectsForTimeSheetDto
                {
                    ProjectId = project.ProjectId.ToString(),
                    ProjectName = project.Name,
                    StartDate = project.StartDate,
                    EndDate = project.EndDate,
                    Status = project.ProjectStatus,
                    IsBillable = project.IsBillable,
                    Description = project.Description,
                    AssignedTo = null,
                    ToDosCount = await Db.ProjectMgmt_Todo.CountAsync(x => x.ProjectMgmt_ProjectId == project.ProjectId),
                };

                var createdBy = await Db.UserProfiles.FirstOrDefaultAsync(u => u.UserId == project.CreatedBy);
                pro.AssignedBy = createdBy != null ? $"{createdBy.FirstName} {createdBy.LastName}" : null;

                // Get the total time spent on all the todos under the project and the project value
                var totalTime = new TimeSpan();
                decimal projectValue = 0.0M;

                var todos = await Db.ProjectMgmt_Todo
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString())
                    .ToListAsync();

                var todosFromTh = await Db.TimeSheet
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId.ToString() && x.Status == TimeSheetStatus.Completed && x.IsArchived == isArchived)
                    .Select(x => new ProjectMgmt_Todo()
                    {
                        Id = x.Id,
                        TimeSpent = x.TimeSpent,
                        IsBillable = x.IsBillable,
                    })
                    .ToListAsync();

                todos.AddRange(todosFromTh);

                foreach (var todo in todos)
                {
                    if (todo.ActualTimeSpent == "0.00:00:00")
                        todo.ActualTimeSpent = null;

                    TimeSpan time;
                    if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                    {
                        throw new Exception($"{todo.ActualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                    }

                    totalTime += time;

                    // Calculate project value if it's billable
                    if (project.IsBillable)
                    {
                        var amountPerMin = 0.0M;
                        switch (project.AmountFrequency)
                        {
                            case AmountFrequency.Hourly:
                                amountPerMin = project.AmountPerSelectedFrequency.Value / 60;
                                break;
                            case AmountFrequency.Daily:
                                amountPerMin = project.AmountPerSelectedFrequency.Value / 1440;
                                break;
                            case AmountFrequency.Weekly:
                                amountPerMin = project.AmountPerSelectedFrequency.Value / 10080;
                                break;
                            case AmountFrequency.Monthly:
                                amountPerMin = project.AmountPerSelectedFrequency.Value / 43800;
                                break;
                            default:
                                break;
                        }

                        if (todo.IsBillable)
                        {
                            var todoAmount = amountPerMin * (decimal)time.TotalMinutes;
                            projectValue += todoAmount;
                        }
                    }
                }

                pro.ProjectValue = projectValue;
                pro.TimeSpent = totalTime.ToString();
                projectsDetails.Add(pro);
            }

            // Log the activity
            await LogGetTimeSheetRecordsActivity(filters);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = new
                {
                    ProjectDetails = projectsDetails
                }
            };

            async Task LogGetTimeSheetRecordsActivity(TimeSheetFilters filters)
            {
                var description = $"Time Sheet records retrieved by {filters.UserId}";
                var summary = $"Time Sheet records retrieved";
                await LogActivity(filters.UserId, description, summary);
            }
        }
        #endregion

        #region Private Method - Get Assigned User Details
        private async Task GetAssignedUserDetails(Page<TimeSheetTodoDto> todosFromTH, ProjectMgmt_Project project)
        {
            foreach (var todo in todosFromTH.Items)
            {
                // Get sprint name the todo belongs to
                todo.SprintName = await Db.SprintProjects.Where(x => x.Id.ToString() == todo.SprintId).Select(x => x.Name).FirstOrDefaultAsync();

                var todoUser = await Db.projectMgmt_TodoUsers.FirstOrDefaultAsync(x => x.ProjectMgmt_TodoId == todo.Id);
                if (todoUser == null) { continue; }

                if (todoUser.AmountPerHour == null || todoUser.AmountPerHour < 1)
                {
                    var amountPerHour = 0.0M;
                    switch (project.AmountFrequency)
                    {
                        case AmountFrequency.Daily:
                            amountPerHour = project.AmountPerSelectedFrequency.Value / 24;
                            break;
                        case AmountFrequency.Weekly:
                            amountPerHour = project.AmountPerSelectedFrequency.Value / 168;
                            break;
                        case AmountFrequency.Monthly:
                            amountPerHour = project.AmountPerSelectedFrequency.Value / 730;
                            break;
                        case AmountFrequency.Hourly:
                            amountPerHour = project.AmountPerSelectedFrequency.Value;
                            break;
                        default:
                            break;
                    }

                    TimeSpan time;
                    TimeSpan.TryParse(todo.RegisteredHours, CultureInfo.InvariantCulture, out time);
                    todo.TodoValue = amountPerHour * (decimal)time.TotalHours;
                }
                else
                {
                    var amountPerHour = 0.0M;
                    switch (project.AmountFrequency)
                    {
                        case AmountFrequency.Daily:
                            amountPerHour = todoUser.AmountPerHour.Value / 24;
                            break;
                        case AmountFrequency.Weekly:
                            amountPerHour = todoUser.AmountPerHour.Value / 168;
                            break;
                        case AmountFrequency.Monthly:
                            amountPerHour = todoUser.AmountPerHour.Value / 730;
                            break;
                        case AmountFrequency.Hourly:
                            amountPerHour = todoUser.AmountPerHour.Value;
                            break;
                        default:
                            break;
                    }

                    TimeSpan time;
                    TimeSpan.TryParse(todo.RegisteredHours, CultureInfo.InvariantCulture, out time);
                    todo.TodoValue = amountPerHour * (decimal)time.TotalHours;
                }

                var todoUsers = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
                if (todoUsers.Any())
                {
                    var userDetails = new List<UserDto>();
                    foreach (var item in todoUsers)
                    {
                        var userDetail = await Db.UserProfiles.Where(x => x.UserId == item.UserId)
                            .Select(user => new UserDto
                            {
                                Id = user.UserId,
                                Email = user.Email,
                                FirstName = user.FirstName,
                                LastName = user.LastName,
                                ProfileUrl = user.ProfilePictureUrl != null ? Utility.ConvertSignedUrlToBase64(_aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl, 30).Result) : null
                            })
                            .FirstOrDefaultAsync();
                        if (userDetail != null)
                        {
                            userDetails.Add(userDetail);
                        }
                    }

                    todo.AssignedTo = userDetails;
                }
            }
        }
        #endregion

        #region Private Method - GetUserDetailsForTH
        /// <summary>
        /// Private metthod
        /// </summary>
        /// <param name="projectId"></param>
        /// <param name="project"></param>
        /// <param name="memberId"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        private async Task<UserDetailsForTeamSheetDto> GetUserDetailsForTH(string projectId, ProjectMgmt_Project project, string memberId, string loggedInUserId)
        {
            var user = await Db.UserProfiles.Where(x => x.UserId == memberId).FirstOrDefaultAsync();
            var member = new UserDetailsForTeamSheetDto
            {
                FullName = user.FirstName + " " + user.LastName,
                Email = user.Email,
                UserId = user.UserId,
                StartDate = user.DateCreated,
                ContractEndDate = null,
            };

            // Get the total asignd todo count for the user
            //Get all todoIds under the project
            var todoIds = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId).Select(x => x.Id).ToListAsync();
            var totalAssignedTodos = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == memberId && todoIds.Contains(x.ProjectMgmt_TodoId)).ToListAsync();

            member.AssignedTodosCount = totalAssignedTodos.Count();

            todoIds = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.Id).ToListAsync();
            var totalCompletedTodos = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == memberId && todoIds.Contains(x.ProjectMgmt_TodoId)).ToListAsync();

            member.CompletedTodoCount = totalCompletedTodos.Count();

            // Get the total timespent on todos for the user and hours worked
            var totalTime = new TimeSpan();
            var todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();

            foreach (var todo in todos)
            {
                if (todo.ActualTimeSpent == "0.00:00:00")
                    todo.ActualTimeSpent = null;

                TimeSpan time;
                if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"{todo.ActualTimeSpent ?? todo.TimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTime += time;
            }

            var projectAmountPerMinute = project.AmountPerSelectedFrequency / 60;
            member.HoursWorked = (int)(totalTime.TotalMinutes / 60);
            member.TotalAmountEarned = (decimal)totalTime.TotalMinutes * projectAmountPerMinute.Value;

            // Get the total todoIds assinged to the user under the project
            var todoIdsAssignedToUser = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == memberId).Select(x => x.ProjectMgmt_TodoId).ToListAsync();
            var todosAssignedToUser = await Db.ProjectMgmt_Todo.Where(x => todoIdsAssignedToUser.Contains(x.Id) && x.ProjectMgmt_ProjectId.ToString() == projectId).ToListAsync();

            member.Todos = todosAssignedToUser;

            // Log Activity
            var canLogActivity = await _activityService.CheckIfUserHasGrantedPermission(loggedInUserId, EventCategory.TimeSheet);
            if (canLogActivity)
            {
                var currentUser = await Db.UserProfiles.Where(u => u.UserId == loggedInUserId)
                    .Select(x => x.FirstName + " " + x.LastName)
                    .FirstOrDefaultAsync();
                var activity = new ActivityDto
                {
                    Description = $"Time-Sheet user details retrieved by {currentUser}",
                    ActivitySummary = $"Time-Sheet user details retrieved",
                    EventCategory = EventCategory.Calender,
                    UserId = loggedInUserId,
                    By = currentUser,
                    Application = Applications.Joble
                };

                await _activityService.CreateLog(activity);
            }

            return member;
        }
        #endregion

        #region Calculate TimeSheet Billable and Non Billable Hours and Total Revenue by Project Id
        /// <summary>
        /// Calculate TimeSheet Billable and Non Billable Hours and Total Revenue by Project Id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> CalculateBillableandNonBillableHours(string projectId)
        {
            // Check if project exists
            var project = new ProjectMgmt_Project();
            if (projectId is not null)
            {
                project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId);
                if (project == null)
                {
                    return new GenericResponse()
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Project not found"
                    };
                }
            }

            TimeSpan totalTimeSpent = new TimeSpan();
            TimeSpan totalBillableHours = new TimeSpan();
            int totalTodosCount = 0;
            decimal totalRevenue = 0.0M;

            // Get total time spent on all the todos of the project in hours and minutes
            var todos = new List<ProjectMgmt_Todo>();
            var timeSheetTodos = new List<TimeSheet>();
            var timeSpentBillable = new List<string>();
            if (projectId != null)
            {
                todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).ToListAsync();
                timeSheetTodos = await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == projectId).ToListAsync();

                timeSpentBillable = await Db.ProjectMgmt_Todo
                .Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && x.IsBillable && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" ? x.TimeSpent : x.ActualTimeSpent)
                .ToListAsync();

                timeSpentBillable.AddRange(await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == projectId && x.IsBillable).Select(x => x.ActualTimeSpent).ToListAsync());

                totalTodosCount = await Db.ProjectMgmt_Todo
                    .CountAsync(x => x.ProjectMgmt_ProjectId.ToString() == projectId);
            }
            else
            {
                todos = await Db.ProjectMgmt_Todo.Where(x => x.TodoStatus == ProjectManagementStatus.Completed.ToString()).ToListAsync();
                timeSheetTodos = await Db.TimeSheet.Where(x => x.Status == TimeSheetStatus.Completed).ToListAsync();

                timeSpentBillable = await Db.ProjectMgmt_Todo
                .Where(x => x.IsBillable == true && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent)
                .ToListAsync();

                timeSpentBillable.AddRange(await Db.TimeSheet.Where(x => x.IsBillable == true).Select(x => x.ActualTimeSpent).ToListAsync());

                totalTodosCount = await Db.ProjectMgmt_Todo.CountAsync(todo => todo.ProjectMgmt_ProjectId != null);
            }

            foreach (var timeSheetTodo in timeSheetTodos)
            {
                if (timeSheetTodo.ActualTimeSpent == "0.00:00:00")
                    timeSheetTodo.ActualTimeSpent = null;

                TimeSpan oldTime;
                if (!TimeSpan.TryParse(timeSheetTodo.ActualTimeSpent, CultureInfo.InvariantCulture, out oldTime))
                {
                    throw new Exception($"{timeSheetTodo.TimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }
                totalTimeSpent += oldTime;
                CalculateTotalProjectValue(project, ref totalRevenue, oldTime, null, timeSheetTodo);
            }

            // For total time spent
            foreach (var todo in todos)
            {
                if (todo.ActualTimeSpent == "0.00:00:00")
                    todo.ActualTimeSpent = null;

                TimeSpan oldTime;
                if (!TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out oldTime))
                {
                    throw new Exception($"{todo.ActualTimeSpent ?? todo.TimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTimeSpent += oldTime;

                CalculateTotalProjectValue(project, ref totalRevenue, oldTime, todo, null);
            }

            // For total billable time spent
            foreach (var time in timeSpentBillable)
            {
                TimeSpan oldTimeBillable;
                if (!TimeSpan.TryParse(time, CultureInfo.InvariantCulture, out oldTimeBillable))
                {
                    throw new Exception($"{time} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }
                totalBillableHours += oldTimeBillable;
            }

            return new GenericResponse
            {
                ResponseMessage = "Success",
                ResponseCode = "200",
                Data = new
                {
                    TotalTimeSpent = totalTimeSpent.ToString(),
                    TotalBillableHours = totalBillableHours.ToString(),
                    TotalTodosCount = totalTodosCount,
                    TotalRevenue = totalRevenue
                }
            };
        }
        #endregion

        #region Calculate TimeSheet Billable and Non Billable Hours and Total Revenue using deynamic filters
        /// <summary>
        /// Calculate TimeSheet Billable and Non Billable Hours and Total Revenue using filters
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="filters"></param>
        /// <param name="isArchived"></param>
        /// <param name="projectIds"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> CalculateBillableandNonBillableHours(PaginationParameters parameters, List<string> projectIds, TimeSheetFilters filters, bool isArchived = false)
        {
            // Apply filters to projects
            var projectsQuery = Db.ProjectMgmt_Projects.AsQueryable();

            // If `GetForAllProjects` is true, ignore `projectIds`
            if (!filters.GetForAllProjects && projectIds.Any())
            {
                projectsQuery = projectsQuery.Where(p => projectIds.Contains(p.ProjectId.ToString()));
            }

            // Filter by ProjectStatus
            if (filters.ProjectStatus != null && filters.ProjectStatus.Any())
            {
                projectsQuery = projectsQuery.Where(p => filters.ProjectStatus.Contains(p.ProjectStatus));
            }

            // Filter by TeamIds

            if (filters.TeamIds != null && filters.TeamIds.Any())
            {
                var teamMemberIdsFromTeams = await Db.TeamMembers
                    .Where(x => filters.TeamIds.Contains(x.TeamId.ToString())).Select(x => x.UserId).ToListAsync();

                // Add these member IDs to TeamMemberIds for further filtering
                filters.TeamMemberIds.AddRange(teamMemberIdsFromTeams);
            }

            // Filter by TeamMemberIds
            if (filters.TeamMemberIds != null && filters.TeamMemberIds.Any())
            {
                projectsQuery = projectsQuery.Where(p => p.projectMgmt_ProjectUsers.Any(u => filters.TeamMemberIds.Contains(u.UserId)));
            }

            // Filter by IsBillable
            if (filters.IsBillable.HasValue)
            {
                projectsQuery = projectsQuery.Where(p => p.IsBillable == filters.IsBillable.Value);
            }

            // Filter by SprintIds
            if (filters.SprintIds != null && filters.SprintIds.Any())
            {
                projectsQuery = projectsQuery.Where(p => filters.SprintIds.Contains(p.SprintProjectId));
            }

            // Pagination
            var pagedProjects = await projectsQuery
                .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                .Take(parameters.PageSize)
                .ToListAsync();

            // Initialize totals
            TimeSpan totalTimeSpent = new TimeSpan();
            TimeSpan totalBillableHours = new TimeSpan();
            decimal totalRevenue = 0.0M;
            int totalTodosCount = 0;

            // Loop through the filtered projects
            foreach (var project in pagedProjects)
            {
                var todos = await Db.ProjectMgmt_Todo
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString())
                    .ToListAsync();

                var timeSheetTodos = await Db.TimeSheet
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId.ToString())
                    .ToListAsync();

                var timeSpentBillable = await Db.ProjectMgmt_Todo
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId && x.IsBillable && x.TodoStatus == ProjectManagementStatus.Completed.ToString())
                    .Select(x => x.ActualTimeSpent == "0.00:00:00" ? x.TimeSpent : x.ActualTimeSpent)
                    .ToListAsync();

                timeSpentBillable.AddRange(await Db.TimeSheet
                    .Where(x => x.ProjectMgmt_ProjectId == project.ProjectId.ToString() && x.IsBillable && x.IsArchived == isArchived)
                    .Select(x => x.ActualTimeSpent)
                    .ToListAsync());

                totalTodosCount += await Db.ProjectMgmt_Todo
                    .CountAsync(x => x.ProjectMgmt_ProjectId == project.ProjectId);

                // For each TimeSheet, calculate the time spent and revenue
                foreach (var timeSheetTodo in timeSheetTodos)
                {
                    if (timeSheetTodo.ActualTimeSpent == "0.00:00:00")
                        timeSheetTodo.ActualTimeSpent = null;

                    if (TimeSpan.TryParse(timeSheetTodo.ActualTimeSpent, CultureInfo.InvariantCulture, out var oldTime))
                    {
                        totalTimeSpent += oldTime;
                        CalculateTotalProjectValue(project, ref totalRevenue, oldTime, null, timeSheetTodo);
                    }
                }

                // For todos, calculate the time spent
                foreach (var todo in todos)
                {
                    if (todo.ActualTimeSpent == "0.00:00:00")
                        todo.ActualTimeSpent = null;

                    if (TimeSpan.TryParse(todo.ActualTimeSpent ?? todo.TimeSpent, CultureInfo.InvariantCulture, out var oldTime))
                    {
                        totalTimeSpent += oldTime;
                        CalculateTotalProjectValue(project, ref totalRevenue, oldTime, todo, null);
                    }
                }

                // For total billable time spent
                foreach (var time in timeSpentBillable)
                {
                    if (TimeSpan.TryParse(time, CultureInfo.InvariantCulture, out var oldTimeBillable))
                    {
                        totalBillableHours += oldTimeBillable;
                    }
                }
            }

            return new GenericResponse
            {
                ResponseMessage = "Success",
                ResponseCode = "200",
                Data = new
                {
                    TotalTimeSpent = totalTimeSpent.ToString(),
                    TotalBillableHours = totalBillableHours.ToString(),
                    TotalTodosCount = totalTodosCount,
                    TotalRevenue = totalRevenue
                }
            };
        }
        #endregion

        #region Private Method - Calculate Total Project Value
        private void CalculateTotalProjectValue(ProjectMgmt_Project project, ref decimal totalRevenue, TimeSpan oldTime, ProjectMgmt_Todo todo = null, TimeSheet timeSheetTodo = null)
        {
            if (timeSheetTodo != null)
            {
                if (timeSheetTodo.IsBillable)
                {
                    var amountPerHour = 0.0M;
                    switch (project.AmountFrequency)
                    {
                        case AmountFrequency.Daily:
                            amountPerHour = project.AmountPerSelectedFrequency.Value / 24;
                            break;
                        case AmountFrequency.Weekly:
                            amountPerHour = project.AmountPerSelectedFrequency.Value / 168;
                            break;
                        case AmountFrequency.Monthly:
                            amountPerHour = project.AmountPerSelectedFrequency.Value / 730;
                            break;
                        case AmountFrequency.Hourly:
                            amountPerHour = project.AmountPerSelectedFrequency.Value;
                            break;
                        default:
                            break;
                    }

                    totalRevenue += amountPerHour * (int)oldTime.TotalHours;
                }
            }
            else if (todo != null)
            {
                if (todo.IsBillable)
                {
                    // Get the user assigned to the todo
                    var todoUser = Db.projectMgmt_TodoUsers.FirstOrDefault(x => x.ProjectMgmt_TodoId == todo.Id);
                    if (todoUser is not null)
                    {
                        if (todoUser.AmountPerHour != null || todoUser.AmountPerHour > 0)
                            totalRevenue += todoUser.AmountPerHour.Value * (int)oldTime.TotalHours;
                    }
                    else
                    {
                        var amountPerHour = 0.0M;
                        switch (project.AmountFrequency)
                        {
                            case AmountFrequency.Daily:
                                amountPerHour = project.AmountPerSelectedFrequency.Value / 24;
                                break;
                            case AmountFrequency.Weekly:
                                amountPerHour = project.AmountPerSelectedFrequency.Value / 168;
                                break;
                            case AmountFrequency.Monthly:
                                amountPerHour = project.AmountPerSelectedFrequency.Value / 730;
                                break;
                            case AmountFrequency.Hourly:
                                amountPerHour = project.AmountPerSelectedFrequency.Value;
                                break;
                            default:
                                break;
                        }

                        totalRevenue += amountPerHour * (int)oldTime.TotalHours;
                    }
                }
            }
        }
        #endregion

        #region Get User Details by User Id
        /// <summary>
        /// get user details by user id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="projectId"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<UserDetailsForTeamSheetDto> GetUserDetailsById(string userId, string projectId, string loggedInUserId)
        {
            var user = await Db.Users.FirstOrDefaultAsync(x => x.Id == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            var project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId);
            if (project == null)
                throw new RecordNotFoundException("Project not found");

            return await GetUserDetailsForTH(projectId, project, userId, loggedInUserId);
        }
        #endregion

        #region Get Todo Details by Todo Id
        /// <summary>
        /// Get todo details by todo id
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="projectId"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<TimeSheetTodoDto> GetTodoDetailsById(string todoId, string loggedInUserId, string projectId = null)
        {
            ProjectMgmt_Project project = null;
            var Todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (Todo == null)
                throw new RecordNotFoundException("Todo not found");

            if (projectId is not null)
            {
                project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId);
                if (project == null)
                    throw new RecordNotFoundException("Project not found");
            }

            var todo = await Db.ProjectMgmt_Todo.Include(x => x.ProjectTags)
                           .Where(x => x.Id.ToString() == todoId)
                           .Select(x => new TimeSheetTodoDto()
                           {
                               Id = x.Id,
                               TodoSummary = x.TodoSummary,
                               TodoDescription = x.TodoDescription,
                               TodoId = x.TodoId,
                               TodoName = x.TodoName,
                               ProjectName = project == null ? "" : project.Name,
                               TimeLeft = x.TimeLeft,
                               ActualHours = x.TimeSpent,
                               TodoStatus = x.TodoStatus,
                               Priority = x.Priority.ToString(),
                               CreatedBy = x.CreatedBy,
                               CreatedDate = x.CreatedDate,
                               LastUpdate = x.LastUpdate,
                               DueDate = x.DueDate,
                               ExpectedHours = x.Duration,
                               EndDate = x.EndTime,
                               StartDate = x.StartDateAndTime,
                               ExistingTodoLink = x.ExistingTodoLink,
                               WillBeDueIn = x.WillBeDueIn,
                               IsBillable = x.IsBillable,
                               AmountPerHour = x.AmountPerHour,
                               LockTodo = x.LockTodo,
                               ApprovalStatus = x.ApprovalStatus,
                               RegisteredHours = x.ActualTimeSpent == "0.00:00:00" ? x.TimeSpent : x.ActualTimeSpent
                           })
                           .FirstOrDefaultAsync();


            var todoUsers = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id).ToListAsync();
            if (todoUsers.Any())
            {
                var userDetails = new List<UserDto>();
                foreach (var item in todoUsers)
                {
                    var userDetail = await Db.UserProfiles.Where(x => x.UserId == item.UserId)
                        .Select(user => new UserDto
                        {
                            Id = user.UserId,
                            Email = user.Email,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            ProfileUrl = user.ProfilePictureUrl != null ? Utility.ConvertSignedUrlToBase64(_aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl, 30).Result) : null
                        })
                        .FirstOrDefaultAsync();
                    if (userDetail != null)
                    {
                        userDetails.Add(userDetail);
                    }
                }

                todo.AssignedTo = userDetails;
            }

            // Get tagIds
            var tags = await Db.TagId
                .Where(x => x.TodoId == todo.Id.ToString())
                .Select(x => x.PrjectTagId).ToListAsync();

            // Get tag names using the tagIds
            var tagNames = await Db.ProjectTag
                .Where(x => tags.Contains(x.Id.ToString()))
                .Select(x => x.TagName).ToListAsync();

            todo.Tags = tagNames;

            // Get all comments for this todo
            var comments = await Db.TodoComments.Where(x => x.TodoId == todo.Id).ToListAsync();
            todo.Comments = _mapper.Map<List<TodoCommentDto>>(comments);
            foreach (var comment in todo.Comments)
            {
                var userProfile = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == comment.CommentedBy);
                if (userProfile != null)
                {
                    comment.Commenter = new UserDto
                    {
                        FirstName = userProfile.FirstName,
                        LastName = userProfile.LastName,
                        Email = userProfile.Email,
                        Id = userProfile.Id,
                        ProfileUrl = userProfile.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(userProfile.ProfilePictureUrl) : null
                    };
                }
            }

            var description = "Time Sheet record created by";
            var summary = "Time Sheet record created";
            await LogActivity(loggedInUserId, description, summary, todoId);

            return todo;
        }
        #endregion

        #region Update Todo
        /// <summary>
        /// Update todo
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UpdateTodo(string todoId, UpdateTodoForTHDto model)
        {
            TimeSheet timeSheetTodo = null;
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (todo == null)
                timeSheetTodo = await Db.TimeSheet.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);

            if (timeSheetTodo == null && todo == null)
                throw new RecordNotFoundException("Record not found");

            // Check for permissions
            await CheckPermission(todo.CreatedBy, model.UserId);

            if (timeSheetTodo != null) return true;

            todo.ApprovalStatus = model.ApprovalStatus;
            todo.IsBillable = model.IsBillable;
            if (todo.TodoStatus != TodoStatus.Completed.ToString())
                todo.LockTodo = model.LockTodo;

            Db.ProjectMgmt_Todo.Update(todo);

            // Add comment
            if (!string.IsNullOrEmpty(model.Comment))
            {
                var comment = new TodoComments
                {
                    Comment = model.Comment,
                    TodoId = todo.Id,
                    CommentedBy = model.UserId,
                };
                Db.TodoComments.Add(comment);
            }

            var result = await Db.SaveChangesAsync();
            if (result <= 0) return false;

            // Send notification to the user
            var todoUser = await Db.projectMgmt_TodoUsers.FirstOrDefaultAsync(x => x.ProjectMgmt_TodoId == todo.Id);
            var invitee = await Db.UserProfiles.Where(x => x.UserId == model.UserId)
                .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();
            var notification = new AddNotificationDto
            {
                Message = $"{model.Comment} - {invitee}",
                Event = EventCategory.Comment,
                EventId = todo.Id.ToString(),
                CreatedBy = model.UserId,
            };

            var notificationId = await AddNotification(notification);
            if (notificationId is not null)
            {
                await AddUserNotification(new List<string> { todoUser.UserId }, Guid.Parse(notificationId));
            }

            // Todo: Invoke a frontend method using SignalR
            await _hubContext.Clients.All.RecieveNotification();

            // Send mail notification if a comment was added.
            var subdomain = GlobalVariables.Subdomain;
            var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, subdomain) + $"/suite/pkg/project/todo-dashboard?type={TypeForInviteUrl.Todo}&id={todo.Id}";
            var assigneeAndAssigner = new List<string>() { todo.CreatedBy };
            var assignee = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id)
                .Select(x => x.UserId).ToListAsync();
            assigneeAndAssigner.AddRange(assignee);

            await SendCommentNotification(model.UserId, model.Comment, todo.TodoSummary, url, assigneeAndAssigner);

            // Log activity
            var description = "Time Sheet record updated by";
            var summary = "Time Sheet record updated";
            await LogActivity(model.UserId, description, summary, todoId);

            return result > 0;
        }
        #endregion

        #region Get Comments for Todo
        public async Task<List<TodoCommentDto>> GetCommentsForTodoAsync(string todoId)
        {
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (todoId is null)
                throw new RecordNotFoundException($"Todo with {todoId} does not exist");

            var comments = await Db.TodoComments.Where(x => x.TodoId.ToString() == todoId).ToListAsync();
            var commentDtos = _mapper.Map<List<TodoCommentDto>>(comments);
            foreach (var comment in commentDtos)
            {
                var userProfile = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == comment.CommentedBy);
                if (userProfile != null)
                {
                    comment.Commenter = new UserDto
                    {
                        FirstName = userProfile.FirstName,
                        LastName = userProfile.LastName,
                        Email = userProfile.Email,
                        Id = userProfile.Id,
                        ProfileUrl = userProfile.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(userProfile.ProfilePictureUrl) : null
                    };
                }
            }
            return commentDtos;
        }
        #endregion

        #region Add comment to a todo
        /// <summary>
        /// Add comment to a todo
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="com"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> AddCommentToTodo(string todoId, string com, string loggedInUserId)
        {
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == todoId);
            if (todo == null)
                throw new RecordNotFoundException("Record not found");

            // Add comment
            var comment = new TodoComments
            {
                Comment = com,
                TodoId = todo.Id,
                CommentedBy = loggedInUserId
            };
            Db.TodoComments.Add(comment);

            // Send notification to the user
            var todoUser = await Db.projectMgmt_TodoUsers.FirstOrDefaultAsync(x => x.ProjectMgmt_TodoId == todo.Id);
            if (todoUser is not null)
            {
                var invitee = await Db.UserProfiles.Where(x => x.UserId == loggedInUserId)
                    .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();
                var notification = new AddNotificationDto
                {
                    Message = $"{com} - {invitee}",
                    Event = EventCategory.Comment,
                    EventId = todo.Id.ToString(),
                    CreatedBy = loggedInUserId,
                };
                var notificationId = await AddNotification(notification);
                if (notificationId is not null)
                {
                    await AddUserNotification(new List<string> { todoUser.UserId }, Guid.Parse(notificationId));
                }
            }

            // Send mail notification if a comment was added.
            var subdomain = GlobalVariables.Subdomain;
            var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE, subdomain) + $"/suite/pkg/project/todo-dashboard?type={TypeForInviteUrl.Todo}&id={todo.Id}";
            var assigneeAndAssigner = new List<string>() { todo.CreatedBy };
            var assignee = await Db.projectMgmt_TodoUsers.Where(x => x.ProjectMgmt_TodoId == todo.Id)
                .Select(x => x.UserId).ToListAsync();
            assigneeAndAssigner.AddRange(assignee);

            await SendCommentNotification(loggedInUserId, com, todo.TodoSummary, url, assigneeAndAssigner);

            var result = await Db.SaveChangesAsync();
            return result > 0;
        }
        #endregion

        #region Edit comment
        /// <summary>
        /// Edit/Update comment
        /// </summary>
        /// <param name="commentId"></param>
        /// <param name="comment"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> EditComment(string commentId, string comment, string loggedInUserId)
        {
            var todoComment = await Db.TodoComments.FirstOrDefaultAsync(x => x.Id.ToString() == commentId);
            if (todoComment == null)
                throw new RecordNotFoundException("Record not found");

            // Check Permissions
            await CheckPermission(todoComment.CommentedBy, loggedInUserId);

            todoComment.Comment = comment;
            Db.TodoComments.Update(todoComment);

            // Send notification to the user
            var todoUser = await Db.projectMgmt_TodoUsers.FirstOrDefaultAsync(x => x.ProjectMgmt_TodoId == todoComment.TodoId);
            var invitee = await Db.UserProfiles.Where(x => x.UserId == loggedInUserId)
                .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();
            var notification = new AddNotificationDto
            {
                Message = $"{comment} - {invitee}",
                Event = EventCategory.Comment,
                EventId = todoComment.TodoId.ToString(),
                CreatedBy = loggedInUserId,
            };
            var notificationId = await AddNotification(notification);
            if (notificationId is not null)
            {
                await AddUserNotification(new List<string> { todoUser.UserId }, Guid.Parse(notificationId));
            }

            var result = await Db.SaveChangesAsync();
            return result > 0;
        }
        #endregion

        #region Lock Todos
        public async Task<bool> LockOrUnlockTodos(List<string> todoIds, string loggedInUserId)
        {
            var todos = await Db.ProjectMgmt_Todo.Where(x => todoIds.Contains(x.Id.ToString())).ToListAsync();
            if (todos == null)
                throw new RecordNotFoundException("Todos not found");

            foreach (var todo in todos)
                todo.LockTodo = true;

            Db.ProjectMgmt_Todo.UpdateRange(todos);
            var result = await Db.SaveChangesAsync();

            var description = "Time Sheet record locked by";
            var summary = "Time Sheet record locked";
            await LogActivity(loggedInUserId, description, summary, string.Join(",", todoIds));

            return result > 0;
        }
        #endregion

        #region Get Project Details by Project Id
        /// <summary>
        /// Get project details by project id
        /// </summary>
        /// <param name="projectId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <param name="loggedInUserId"></param>
        /// <exception cref="Exception"></exception>
        public async Task<AllProjectsForTimeSheetDto> GetProjectDetailsById(string projectId, string loggedInUserId)
        {
            var project = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId);
            if (project == null)
                throw new RecordNotFoundException("Project not found");

            var pro = new AllProjectsForTimeSheetDto
            {
                ProjectName = project.Name,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                Status = project.ProjectStatus,
                Description = project.Description,
                AssignedTo = null,
                CurrencySymbol = project.CurrencySymbol,
                ToDosCount = await Db.ProjectMgmt_Todo.CountAsync(x => x.ProjectMgmt_ProjectId == project.ProjectId),
            };

            // Get the total timespent on the all the todos under the project and the project value
            var totalTime = new TimeSpan();
            decimal projectValue = 0.0M;
            var todos = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();

            var todoNames = new List<string>();
            var totalbillableTime = new TimeSpan();
            foreach (var todo in todos)
            {
                todoNames.Add(todo.TodoName);

                TimeSpan time;
                if (!TimeSpan.TryParse(todo.TimeSpent, CultureInfo.InvariantCulture, out time))
                {
                    throw new Exception($"{todo.TimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                }

                totalTime += time;

                // Get project value
                if (project.IsBillable)
                {
                    var amountPerMin = 0.0M;
                    switch (project.AmountFrequency)
                    {
                        case AmountFrequency.Hourly:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 60;
                            break;
                        case AmountFrequency.Daily:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 1440;
                            break;
                        case AmountFrequency.Weekly:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 10080;
                            break;
                        case AmountFrequency.Monthly:
                            amountPerMin = project.AmountPerSelectedFrequency.Value / 43800;
                            break;
                        default:
                            break;
                    }

                    if (todo.IsBillable)
                    {
                        totalbillableTime += time;
                        var todoAmount = amountPerMin * (decimal)time.TotalMinutes;
                        projectValue += todoAmount;
                    }
                }
            }

            pro.ProjectValue = projectValue;
            pro.TimeSpent = totalTime.ToString();
            pro.Todos = todoNames;
            pro.BillableHours = (int)totalbillableTime.TotalMinutes / 60;

            // Get project tags - Todo

            // Get prject files
            pro.Files = await Db.ProjectFile.Where(x => x.ProjectMgmt_ProjectId == project.ProjectId).ToListAsync();

            var description = "Project details retrieved by";
            var summary = "Project details retrieved";
            await LogActivity(loggedInUserId, description, summary, projectId);

            return pro;
        }
        #endregion

        #region Calculate DashBoard Data
        /// <summary>
        /// Calculate Dashboard data
        /// </summary>
        /// <param name="filters"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<GenericResponse> CalculateDashboardData(TimeSheetDashboardFilters filters)
        {
            filters.StartDate = filters.StartDate == null ? new DateTime(2023, 01, 01, 9, 15, 0) : filters.StartDate.Value;
            filters.EndDate = filters.EndDate == null ? new DateTime(4000, 01, 01, 9, 15, 0) : filters.EndDate.Value;
            filters.BarChartStartDtae = filters.BarChartStartDtae == null ? new DateTime(2023, 01, 01, 9, 15, 0) : filters.BarChartStartDtae.Value;
            filters.BarChartEndDate = filters.BarChartEndDate == null ? new DateTime(4000, 01, 01, 9, 15, 0) : filters.BarChartEndDate.Value;

            // Calculate total time spent
            var projects = await Db.ProjectMgmt_Projects.Where(x => x.CreatedTime >= filters.StartDate && x.CreatedTime <= filters.EndDate).ToListAsync();

            var totalTime = new TimeSpan();
            var totalEarnings = 0.0M;
            var earningsPerPro = 0.0M;
            var timePerPro = new TimeSpan();
            var totalEarningsPerPro = new Dictionary<string, decimal>();
            var totalTimePerPro = new Dictionary<string, TimeSpan>();

            foreach (var project in projects)
            {
                var todos = await Db.ProjectMgmt_Todo.Where(x => x.CreatedDate >= filters.StartDate && x.CreatedDate <= filters.EndDate && x.ProjectMgmt_ProjectId == project.ProjectId && x.TodoStatus == ProjectManagementStatus.Completed.ToString())
                    .Select(x => new { x.Id, x.ActualTimeSpent, x.TimeSpent, x.IsBillable }).ToListAsync();

                var timeSheetTodos = await Db.TimeSheet.Where(x => x.DateLogged >= filters.StartDate && x.DateLogged <= filters.EndDate && x.ProjectMgmt_ProjectId == project.ProjectId.ToString() && x.Status == TimeSheetStatus.Completed)
                    .Select(x => new { x.Id, x.ActualTimeSpent, x.TimeSpent, x.IsBillable }).ToListAsync();

                todos.AddRange(timeSheetTodos);

                foreach (var todo in todos)
                {
                    var actualTimeSpent = todo.ActualTimeSpent ?? "0.00:00:00";
                    TimeSpan time;
                    if (!TimeSpan.TryParse(actualTimeSpent == "0.00:00:00" ? todo.TimeSpent : actualTimeSpent, CultureInfo.InvariantCulture, out time))
                    {
                        throw new Exception($"{actualTimeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                    }

                    totalTime += time;
                    timePerPro += time;

                    // Get project value
                    if (project.IsBillable)
                    {
                        // Get the user assigned to the todo
                        var todoUser = Db.projectMgmt_TodoUsers.FirstOrDefault(x => x.ProjectMgmt_TodoId == todo.Id);
                        if (todoUser is not null)
                        {
                            if (todoUser.AmountPerHour != null || todoUser.AmountPerHour > 0)
                            {
                                if (todo.IsBillable)
                                {
                                    var todoAmount = todoUser.AmountPerHour.Value * (int)time.TotalHours;
                                    totalEarnings += todoAmount;
                                    earningsPerPro += todoAmount;
                                }
                            }
                        }
                        else
                        {
                            var amountPerMin = 0.0M;
                            switch (project.AmountFrequency)
                            {
                                case AmountFrequency.Hourly:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 60;
                                    break;
                                case AmountFrequency.Daily:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 1440;
                                    break;
                                case AmountFrequency.Weekly:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 10080;
                                    break;
                                case AmountFrequency.Monthly:
                                    amountPerMin = project.AmountPerSelectedFrequency.Value / 43800;
                                    break;
                                default:
                                    break;
                            }

                            if (todo.IsBillable)
                            {
                                var todoAmount = amountPerMin * (decimal)time.TotalMinutes;
                                totalEarnings += todoAmount;
                                earningsPerPro += todoAmount;
                            }
                        }
                    }
                }

                totalEarningsPerPro.Add(project.Name, earningsPerPro);
                totalTimePerPro.Add(project.Name, timePerPro);

                earningsPerPro = 0.0M;
                timePerPro = new TimeSpan();
            }

            // Get the top project based on the total earnings from the earningsPerPro Dictionary
            var topProject = totalEarningsPerPro.OrderByDescending(x => x.Value).FirstOrDefault();

            // Calculate top performing team member base on the total time spent
            var teamMembers = await Db.UserProfiles.ToListAsync();
            var totalTimePerTeamMember = new Dictionary<string, TimeSpan>();
            var timePerTeamMember = new TimeSpan();

            foreach (var teamMember in teamMembers)
            {
                var todoIds = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == teamMember.Id).Select(x => x.ProjectMgmt_TodoId).ToListAsync();
                var timeSpentList = await Db.ProjectMgmt_Todo.Where(x => x.CreatedDate >= filters.StartDate && x.CreatedDate <= filters.EndDate && todoIds.Contains(x.Id) && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                var timeSpentListForTH = await Db.TimeSheet.Where(x => x.DateLogged >= filters.StartDate && x.DateLogged <= filters.EndDate && x.UserId == teamMember.Id && x.Status == TimeSheetStatus.Completed).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                timeSpentList.AddRange(timeSpentListForTH);

                foreach (var timeSpent in timeSpentList)
                {
                    TimeSpan time;
                    if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out time))
                    {
                        throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                    }
                    totalTime += time;
                    timePerTeamMember += time;
                }

                if (totalTimePerTeamMember.ContainsKey(teamMember.FirstName + " " + teamMember.LastName))
                {
                    timePerTeamMember = new TimeSpan();
                    continue;
                }

                totalTimePerTeamMember.Add(teamMember.FirstName + " " + teamMember.LastName, timePerTeamMember);
                timePerTeamMember = new TimeSpan();
            }

            var topTeamMember = totalTimePerTeamMember.OrderByDescending(x => x.Value).FirstOrDefault();

            var dashboardData = new TimeSheetDashboardVm
            {
                TotalTimeInMin = (int)totalTime.TotalMinutes,
                TotalEarnings = totalEarnings,
                TopProject = topProject.Key,
                TopTeamMember = topTeamMember.Key,
            };

            if (filters.ProjectIds.Any())
            {
                var totalHoursWorked = new TimeSpan();
                var projectOrTeamOrTeamMember = new List<string>();
                foreach (var projectId in filters.ProjectIds)
                {
                    // Get the project
                    ProjectMgmt_Project pro = null;
                    if (filters.ProjectStatus.Any())
                        pro = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId && filters.ProjectStatus.Contains(x.ProjectStatus));
                    else
                        pro = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId);

                    if (!filters.TeamIds.Any() && !filters.TeamMemberIds.Any())
                    {
                        // Get the total timespent on the all the todos under the project and the project value
                        var timeSpentList = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && x.CreatedDate >= filters.StartDate && x.CreatedDate <= filters.EndDate && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                        timeSpentList.AddRange(await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == projectId && x.DateLogged >= filters.StartDate && x.DateLogged <= filters.EndDate && x.Status == TimeSheetStatus.Completed).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync());

                        foreach (var timeSpent in timeSpentList)
                        {
                            TimeSpan time;
                            if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out time))
                            {
                                throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                            }

                            totalHoursWorked += time;
                        }

                        projectOrTeamOrTeamMember.Add(pro?.Name);
                    }
                    else
                    {
                        if (filters.TeamIds.Any())
                        {
                            var teamMemberIds = await Db.TeamMembers.Where(x => filters.TeamIds.Contains(x.TeamId.ToString())).Select(x => x.UserId).ToListAsync();

                            foreach (var memberId in teamMemberIds)
                            {

                                var todoIds = await Db.projectMgmt_TodoUsers.Where(x => x.UserId == memberId).Select(x => x.ProjectMgmt_TodoId).ToListAsync();
                                var timeSpentList = await Db.ProjectMgmt_Todo.Where(x => x.CreatedDate >= filters.StartDate && x.CreatedDate <= filters.EndDate && todoIds.Contains(x.Id) && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                                timeSpentList.AddRange(await Db.TimeSheet.Where(x => x.DateLogged >= filters.StartDate && x.DateLogged <= filters.EndDate && x.UserId == memberId && x.Status == TimeSheetStatus.Completed).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync());

                                foreach (var timeSpent in timeSpentList)
                                {
                                    TimeSpan time;
                                    if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out time))
                                    {
                                        throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                                    }

                                    totalHoursWorked += time;
                                }
                            }

                            projectOrTeamOrTeamMember = await Db.Teams.Where(x => filters.TeamIds.Contains(x.Id.ToString())).Select(x => x.Name).ToListAsync();
                        }

                        if (filters.TeamMemberIds.Any())
                        {
                            var todoIds = await Db.projectMgmt_TodoUsers.Where(x => filters.TeamMemberIds.Contains(x.UserId.ToString())).Select(x => x.ProjectMgmt_TodoId).ToListAsync();
                            var timeSpentList = await Db.ProjectMgmt_Todo.Where(x => x.CreatedDate >= filters.StartDate && x.CreatedDate <= filters.EndDate && todoIds.Contains(x.Id) && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                            timeSpentList.AddRange(await Db.TimeSheet.Where(x => x.DateLogged >= filters.StartDate && x.DateLogged <= filters.EndDate && filters.TeamMemberIds.Contains(x.UserId.ToString()) && x.Status == TimeSheetStatus.Completed).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync());

                            foreach (var timeSpent in timeSpentList)
                            {
                                TimeSpan time;
                                if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out time))
                                {
                                    throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                                }

                                totalHoursWorked += time;
                            }
                            projectOrTeamOrTeamMember = await Db.UserProfiles.Where(x => filters.TeamMemberIds.Contains(x.Id.ToString())).Select(x => x.FirstName + " " + x.LastName).ToListAsync();
                        }

                        if (filters.ProjectStatus.Any())
                        {
                            pro = await Db.ProjectMgmt_Projects.FirstOrDefaultAsync(x => x.ProjectId.ToString() == projectId && filters.ProjectStatus.Contains(x.ProjectStatus));

                            if (pro is null)
                                continue;

                            // Get the total timespent on the all the todos under the project and the project value
                            var timeSpentList = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId && x.CreatedDate >= filters.StartDate && x.CreatedDate <= filters.EndDate && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                            timeSpentList.AddRange(await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == projectId && x.DateLogged >= filters.StartDate && x.DateLogged <= filters.EndDate && x.Status == TimeSheetStatus.Completed).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync());

                            foreach (var timeSpent in timeSpentList)
                            {
                                TimeSpan time;
                                if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out time))
                                {
                                    throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                                }

                                totalHoursWorked += time;
                            }

                            projectOrTeamOrTeamMember.Add(pro?.Name);
                        }
                    }
                }

                //// Calculate the bar chart data
                var projectRecords = await Db.ProjectMgmt_Projects.Where(x => x.CreatedTime >= filters.BarChartStartDtae && x.CreatedTime <= filters.BarChartEndDate && filters.ProjectIds.Contains(x.ProjectId.ToString())).Select(p => new { id = p.ProjectId.ToString(), name = p.Name }).ToListAsync();
                var months = new List<string>() { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
                var monthsInt = new List<int> { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12 };

                var dict = new Dictionary<string, int>();
                var hoursWorked = new TimeSpan();
                foreach (var project in projectRecords)
                {
                    foreach (var month in monthsInt)
                    {
                        // Get the total timespent on the all the todos under the project and the project value
                        var timeSpentList = await Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == project.id && x.CompletedAt.HasValue ? x.CompletedAt.Value.Month == month : x.Id.ToString() == "" && x.TodoStatus == ProjectManagementStatus.Completed.ToString()).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync();

                        timeSpentList.AddRange(await Db.TimeSheet.Where(x => x.ProjectMgmt_ProjectId == project.id && x.DateLogged.Value.Month == month && x.Status == TimeSheetStatus.Completed).Select(x => x.ActualTimeSpent == "0.00:00:00" || x.ActualTimeSpent == null ? x.TimeSpent : x.ActualTimeSpent).ToListAsync());

                        foreach (var timeSpent in timeSpentList)
                        {
                            TimeSpan time;
                            if (!TimeSpan.TryParse(timeSpent, CultureInfo.InvariantCulture, out time))
                            {
                                throw new Exception($"{timeSpent} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
                            }

                            hoursWorked += time;
                        }

                        dict.Add(months[month - 1], (int)hoursWorked.TotalMinutes / 60);
                        hoursWorked = new TimeSpan();
                    }

                    dashboardData.BarChartData.Add(project.name, dict);
                    dict = new Dictionary<string, int>();
                }

                dashboardData.ProjectOrTeamOrTeamMember = string.Join(",", projectOrTeamOrTeamMember);
                dashboardData.TotalHoursWorked = (int)totalHoursWorked.TotalMinutes / 60;
            }
            else
            {
                dashboardData.ProjectOrTeamOrTeamMember = "All Projects";
                dashboardData.TotalHoursWorked = (int)totalTime.TotalMinutes / 60;
            }

            return new GenericResponse { ResponseCode = "200", ResponseMessage = "Dashboard data calculated successfully", Data = dashboardData };
        }
        #endregion

        #region Update recorded time of a todo
        /// <summary>
        /// Update recorded time of a todo
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="recordedTime"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        /// <param name="loggedInUserId"></param>
        /// <exception cref="Exception"></exception>
        public async Task<bool> UpdateRecordedTime(string Id, string recordedTime, string loggedInUserId)
        {
            var timeSheetTodo = new TimeSheet();
            var todo = await Db.ProjectMgmt_Todo.FirstOrDefaultAsync(x => x.Id.ToString() == Id);
            if (todo == null)
                timeSheetTodo = await Db.TimeSheet.FirstOrDefaultAsync(x => x.Id.ToString() == Id);

            if (todo == null && timeSheetTodo == null)
                throw new RecordNotFoundException("Record not found");

            var recordedTimeSpent = new TimeSpan();
            if (!TimeSpan.TryParse(recordedTime, CultureInfo.InvariantCulture, out recordedTimeSpent))
            {
                throw new Exception($"{recordedTime} is not in the correct format. Correct format eg is '4.05:30:00', where 4 equals 4 days, 05 equals 5 hours, 30 equals 30 minutes and 00 equals 00 seconds");
            }

            if (todo != null)
            {
                todo.ActualTimeSpent = recordedTimeSpent.ToString();
                Db.ProjectMgmt_Todo.Update(todo);
            }
            else
            {
                timeSheetTodo.ActualTimeSpent = recordedTimeSpent.ToString();
                Db.TimeSheet.Update(timeSheetTodo);
            }

            var res = await Db.SaveChangesAsync();

            if (res > 0)
            {
                var description = $"Todo time record updated by";
                var summary = $"Todo time record updated";
                await LogActivity(loggedInUserId, description, summary, Id);
            }

            return res > 0;
        }
        #endregion

        #region Archive todos
        /// <summary>
        /// Archive Todos
        /// </summary>
        /// <param name="todoIds"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> ArchiveTodos(List<string> todoIds, string loggedInUserId)
        {
            var todos = await Db.ProjectMgmt_Todo.Where(x => todoIds.Contains(x.Id.ToString())).ToListAsync();
            if (todos == null)
                throw new RecordNotFoundException("Todos not found");

            foreach (var todo in todos)
            {
                todo.IsArchived = true;
            }

            Db.ProjectMgmt_Todo.UpdateRange(todos);
            var res = await Db.SaveChangesAsync();

            if (res > 0)
            {
                var description = $"Todo archived by";
                var summary = $"Todo archived";
                await LogActivity(loggedInUserId, description, summary, string.Join(",", todoIds));
            }

            return res > 0;
        }
        #endregion

        #region UnArchive todos
        /// <summary>
        /// UnArchive Todos
        /// </summary>
        /// <param name="todoIds"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UnArchiveTodos(List<string> todoIds, string loggedInUserId)
        {
            var todos = await Db.ProjectMgmt_Todo.Where(x => todoIds.Contains(x.Id.ToString())).ToListAsync();
            if (todos == null)
                throw new RecordNotFoundException("Todos not found");

            foreach (var todo in todos)
                todo.IsArchived = false;

            Db.ProjectMgmt_Todo.UpdateRange(todos);
            var res = await Db.SaveChangesAsync();

            if (res > 0)
            {
                var description = $"Todo unarchived by";
                var summary = $"Todo unarchived";
                await LogActivity(loggedInUserId, description, summary, string.Join(",", todoIds));
            }

            return res > 0;
        }
        #endregion

        #region Top Recent Activities
        public async Task<List<Activity>> GetTopRecentActivities(int numberOfActivities = 15)
        {
            var activities = await Db.Activities.OrderByDescending(x => x.CreatedAt)
                .Where(x => x.EventCategory == EventCategory.TimeSheet).Take(numberOfActivities).ToListAsync();
            return activities;
        }
        #endregion

        #region Private Methods - Notification
        private async Task<string> AddNotification(AddNotificationDto model)
        {
            var notification = new Notification.Models.Notification
            {
                Message = model.Message,
                Event = model.Event,
                EventId = model.EventId,
                CreatedAt = GetAdjustedDateTimeBasedOnTZNow(),
                CreatedBy = model.CreatedBy,
            };

            await Db.Notifications.AddAsync(notification);
            return notification.Id.ToString();
        }

        private async Task<bool> AddUserNotification(List<string> userIds, Guid notificationId)
        {
            var userProfileId = await Db.UserProfiles.Where(x => userIds.Contains(x.UserId))
                .Select(x => x.Id.ToString()).ToListAsync();

            var userNotifications = new List<UserNotification>();
            foreach (var userId in userProfileId)
            {
                userNotifications.Add(new UserNotification
                {
                    UserProfileId = userId,
                    NotificationId = notificationId
                });
            }

            Db.UserNotifications.AddRange(userNotifications);
            return true;
        }
        #endregion

        #region Private Methods - TimeSheet
        /// <summary>
        /// Send comment notification to assignee and assigner using the modern todo comment alert template
        /// </summary>
        /// <param name="loggedInUserId"></param>
        /// <param name="comment"></param>
        /// <param name="todoName"></param>
        /// <param name="url"></param>
        /// <param name="assigneeAndAssigner"></param>
        /// <returns></returns>
        private async Task SendCommentNotification(string loggedInUserId, string comment, string todoName, string url, List<string> assigneeAndAssigner)
        {
            var loggedInUserName = await Db.UserProfiles.Where(u => u.UserId == loggedInUserId)
                                .Select(u => u.FirstName + " " + u.LastName).FirstOrDefaultAsync();
            var subject = $"New Comment on Task: {todoName}";
            var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/todo/todo_comment_alert.html");

            // Get todo details for project context
            var todoDetails = await Db.ProjectMgmt_Todo
                .Where(t => t.TodoSummary == todoName)
                .Select(t => new {
                    t.ProjectMgmt_ProjectId,
                    ProjectName = t.projectMgmt_Project != null ? t.projectMgmt_Project.Name : "Personal Tasks"
                })
                .FirstOrDefaultAsync();

            var projectName = todoDetails?.ProjectName ?? "Personal Tasks";

            // Take the first few words in the comment if its a long commet and add ... at the end
            if (comment.Length > 100)
                comment = comment.Substring(0, 100) + "...";

            foreach (var userId in assigneeAndAssigner)
            {
                // Skip sending email to the commenter
                if (userId == loggedInUserId) continue;

                var template = File.ReadAllText(templatePath);
                var user = await Db.UserProfiles.Where(u => u.UserId == userId).FirstOrDefaultAsync();

                if (user != null)
                {
                    var recipientName = $"{user.FirstName} {user.LastName}";

                    // Replace template placeholders with actual values
                    template = template.Replace("{recipientName}", recipientName)
                                     .Replace("{commenterName}", loggedInUserName)
                                     .Replace("{todoName}", todoName)
                                     .Replace("{projectName}", projectName)
                                     .Replace("{commentText}", comment)
                                     .Replace("{todoUrl}", url);

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, user.Email, subject));
                }
            }
        }

        /// <summary>
        /// Log activity
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="description"></param>
        /// <param name="summary"></param>
        /// <param name="eventId"></param>
        /// <returns></returns>
        private async Task LogActivity(string userId, string description, string summary, string eventId = null)
        {
            var canLogActivity = await _activityService.CheckIfUserHasGrantedPermission(userId, EventCategory.TimeSheet);
            if (canLogActivity)
            {
                var currentUser = await Db.UserProfiles.Where(u => u.UserId == userId)
                    .Select(x => x.FirstName + " " + x.LastName)
                    .FirstOrDefaultAsync();
                var activity = new ActivityDto
                {
                    Description = $"{description} {currentUser}",
                    ActivitySummary = summary,
                    EventCategory = EventCategory.TimeSheet,
                    UserId = userId,
                    By = currentUser,
                    EventId = eventId,
                    Application = Applications.Joble
                };

                await _activityService.CreateLog(activity);
            }
        }

        /// <summary>
        /// This method checks if the logged in user has permission to perform the action
        /// </summary>
        /// <param name="createdBy"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="UnauthorizedAccessException"></exception>
        private async Task CheckPermission(string createdBy, string loggedInUserId)
        {
            // Check if the logged in user is a super admin
            var userRole = await _adminService.GetUserRole(loggedInUserId);
            if (createdBy != loggedInUserId && userRole != DataSeeder.SuperAdmin)
                throw new UnauthorizedAccessException("You are not authorized to perform this action");
        }
        #endregion

        public async Task<TimeSheet> GetTimeSheetById(Guid id)
        {
            return await Db.TimeSheet.Where(x => x.Id == id).Include(x => x.ProjectTags).FirstOrDefaultAsync();
        }

        public async Task<Page<TimeSheet>> GetTimeSheets(PaginationParameters parameters)
        {
            if (parameters.StartDate != null && parameters.EndDate == null)
            {
                return await Db.TimeSheet.Where(x => x.StartTime >= parameters.StartDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }

            if (parameters.StartDate == null && parameters.EndDate != null)
            {
                return await Db.TimeSheet.Where(x => x.StartTime <= parameters.EndDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }


            if (parameters.StartDate != null && parameters.EndDate != null)
            {
                return await Db.TimeSheet.Where(x => x.StartTime >= parameters.StartDate && x.EndTime <= parameters.EndDate).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
            }

            return await Db.TimeSheet.ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }

        public async Task<Page<TimeSheet>> GetTimeSheetsByUserId(PaginationParameters parameters, string userId)
        {
            return await Db.TimeSheet.OrderByDescending(x => x.DateLogged).Where(x => x.UserId == userId).ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }

        public async Task<Page<TimeSheet>> GetTimeSheetsByProjectId(PaginationParameters parameters, string Id)
        {
            var timeSheets = Db.TimeSheet.OrderByDescending(x => x.DateLogged).Where(x => x.ProjectMgmt_ProjectId == Id);

            if (parameters.Recent != null && parameters.Recent == true)
            {
                timeSheets = timeSheets.OrderBy(x => x.DateLogged);
            }

            if (parameters.StartDate != null)
            {
                timeSheets = timeSheets.Where(x => x.StartTime > parameters.StartDate);
            }

            return await timeSheets.ToPageListAsync(parameters.PageNumber, parameters.PageSize);
        }

        #region Delete Timesheet
        public async Task<bool> DeleteTimeSheet(TimeSheet timeSheet, string loggedInUserId)
        {
            // Check for permissions
            await CheckPermission(timeSheet.CreatedBy, loggedInUserId);

            Db.TimeSheet.Remove(timeSheet);
            int result = await Db.SaveChangesAsync();
            if (result > 0) { return true; } else { return false; }

        }
        #endregion

        #region Get Timesheet report by projectId
        public async Task<TimeSheetReport> GetTimeSheetReport(string projectId)
        {
            var todoTotalHours = 0.0;
            var totalCompletedHours = 0.0;
            var allTodo = Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId);
            var todosCompleted = Db.ProjectMgmt_Todo.Where(x => x.ProjectMgmt_ProjectId.ToString() == projectId);

            foreach (var sprint in allTodo)
            {
                todoTotalHours += (sprint?.EndTime - sprint?.StartDateAndTime).GetValueOrDefault().TotalHours;
            }

            foreach (var todo in todosCompleted)
            {
                totalCompletedHours += (todo?.EndTime - todo?.StartDateAndTime).GetValueOrDefault().TotalHours;
            }

            var totalBills = totalCompletedHours * 10;

            return new TimeSheetReport()
            {
                TotalHours = todoTotalHours,
                TotalRevenue = totalBills,
                TotalTodos = allTodo.Count(),
                BillableHours = totalCompletedHours
            };
        }


        #endregion

        #region UpdateCompletionDate
        /// <summary>
        /// Updates the completion date of a specific timesheet by its ID.
        /// Throws an exception if the timesheet is not found.
        /// </summary>
        /// <param name="timeSheetId">The ID of the timesheet to update.</param>
        /// <param name="completionDate">The date to set as the completion date for the timesheet.</param>
        /// <returns>A boolean indicating if the update was successful.</returns>
        /// <exception cref="RecordNotFoundException">Thrown when the timesheet with the specified ID does not exist.</exception>
        public async Task<bool> UpdateCompletionDate(string timeSheetId, DateTime completionDate)
        {
            // Retrieve the timesheet by ID
            var timeSheet = await Db.TimeSheet.FirstOrDefaultAsync(ts => ts.Id.ToString() == timeSheetId);

            // If timesheet not found, throw an exception
            if (timeSheet == null)
            {
                throw new RecordNotFoundException("Timesheet does not exist.");
            }

            // Update the completion date
            timeSheet.CompletionDate = completionDate;
            Db.TimeSheet.Update(timeSheet);

            // Save changes and return true if successful
            return await Db.SaveChangesAsync() > 0;
        }
        #endregion
    }
}
