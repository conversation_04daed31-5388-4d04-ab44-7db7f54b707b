﻿using System;

namespace Jobid.App.Calender.Models
{
    public class BackGroundJobId
    {
        public Guid Id { get; set; } = Guid.NewGuid();

        public string JobId { get; set; }

        public string EventId { get; set; }

        public JobType JobType { get; set; }

        public bool IsCancelled { get; set; }

        public DateTime CreatedOn { get; set; } = DateTime.UtcNow;
    }

    public enum JobType
    {
        Recurring = 1,
        Scheduled
    }
}
