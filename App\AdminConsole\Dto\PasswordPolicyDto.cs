﻿namespace Jobid.App.AdminConsole.Dto
{
    public class PasswordPolicyDto
    {
        public int MinimumPasswordLength { get; set; }
        public bool RequireAtLeastOneUppercase { get; set; }
        public bool RequireAtLeastOneLowercase { get; set; }
        public bool RequireAtLeastOneNumber { get; set; }
        public bool RequireAtLeastOneSpecialCharacter { get; set; }
        public bool ProhibitUserNameAsPassword { get; set; }
        public bool RequirePasswordExpiration { get; set; }
        public int PasswordExpirationDays { get; set; }
    }
}
