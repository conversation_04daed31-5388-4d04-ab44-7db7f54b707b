@echo off
echo Installing wkhtmltopdf for Windows...

:: Check if <PERSON><PERSON> is installed
where choco >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Chocolatey is not installed. Installing...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))"
) else (
    echo Chocolatey is already installed.
)

:: Install wkhtmltopdf using Chocolatey
echo Installing wkhtmltopdf...
choco install wkhtmltopdf -y

:: Test installation
echo Testing wkhtmltopdf installation...
wkhtmltopdf --version

if %ERRORLEVEL% neq 0 (
    echo wkhtmltopdf installation may have issues. Please download and install from: https://wkhtmltopdf.org/downloads.html
    echo Make sure to add the installation directory to your PATH environment variable.
    echo After installation, create a symbolic link from the system wkhtmltox library to your project:
    echo.
    echo copy "C:\Program Files\wkhtmltopdf\bin\wkhtmltox.dll" "%~dp0DinkToPdf\libwkhtmltox.dll"
) else (
    echo wkhtmltopdf installed successfully!
    echo Creating a symbolic link from the system wkhtmltox library to your project...
    if exist "C:\Program Files\wkhtmltopdf\bin\wkhtmltox.dll" (
        copy "C:\Program Files\wkhtmltopdf\bin\wkhtmltox.dll" "%~dp0DinkToPdf\libwkhtmltox.dll"
        echo Library copied to project successfully.
    ) else (
        echo Cannot find wkhtmltox.dll in the default installation directory.
        echo Please manually copy the wkhtmltox.dll file to the DinkToPdf folder in your project.
    )
)

echo.
echo Installation complete! You may need to restart your terminal or application.
