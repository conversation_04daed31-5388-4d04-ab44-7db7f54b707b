﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ITeamSheetService
    {
        Task<Team> AddTeam(TeamDto teamDto);
        Task<bool> UpdateTeam(TeamDto teamDto, string teamId);
        Task<bool> DeleteTeam(string teamId);
        Task<TeamVm> GetTeamById(string teamId);
        Task<List<TeamVm>> GetAllTeams();
        Task<List<Team>> GetAllUserTeams(string UserId);
        Task<GenericResponse> GetTeamMembers(string teamId, GetAllTeamMembersFilter filters);
        Task<bool> AddInternalTeamMembers(string teamId, List<string> memberIds, string subdomain, string loggedInUserId);
        Task<bool> AddExternalTeamMembers(AddExternalTeamMembersToTHDto model);
        Task<bool> RemoveTeamMember(string teamId, List<string> userId);
        Task<List<CompanyUserInviteVM>> GetPendingInvitations(string tenantId, PaginationParameters paginationParameters);
        Task<GenericResponse> GetTeamComposition();
        Task<GenericResponse> InviteExternalUserToCompany(List<BulkInviteDto> invites, string subdomain, string userId, bool resendInvite, string token);
        Task<GenericResponse> ProcessBulkInvite(IFormFile file, string subdomain, string userId, string token);
        Task<GenericResponse> RevokeInvite(string invitationId);
        Task<(string, string)> GetAiDetails(string token);
    }
}
