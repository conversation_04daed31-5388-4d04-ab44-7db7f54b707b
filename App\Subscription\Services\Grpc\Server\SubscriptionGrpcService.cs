﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Grpc.Core;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Subscription.ViewModels;
using Serilog;
using static Jobid.App.Subscription.Enums.Enums;
using Status = Grpc.Core.Status;

namespace Jobid.App.Subscription.Services.Grpc.Server
{
    public class SubscriptionGrpcService : GrpcSubscriptionService.GrpcSubscriptionServiceBase
    {
        private readonly IUnitofwork _unitofwork;
        private readonly ILogger _logger = Log.ForContext<SubscriptionGrpcService>();

        public SubscriptionGrpcService(IUnitofwork unitofwork)
        {
            _unitofwork = unitofwork ?? throw new ArgumentNullException(nameof(unitofwork));
        }

        private TEnum ParseEnum<TEnum>(string value, string errorMessage) where TEnum : struct
        {
            if (!Enum.TryParse<TEnum>(value, out var enumValue))
            {
                _logger.Error(errorMessage);
                throw new RpcException(new Status(StatusCode.InvalidArgument, errorMessage));
            }
            return enumValue;
        }

        private DateTime? ParseDateTime(string value, string errorMessage)
        {
            if (!string.IsNullOrEmpty(value) && DateTime.TryParse(value, out var dateValue))
            {
                return dateValue;
            }

            _logger.Error(errorMessage);
            throw new RpcException(new Status(StatusCode.InvalidArgument, errorMessage));
        }

        public override async Task<PercentageIncrementPerPlanResponse> GetPercentageIncrementPerPlan(SubscriptionStatisticsRequest request, ServerCallContext context)
        {
            var application = ParseEnum<Applications>(request.Application, "Error parsing application");

            PaymentProviders? paymentProvider = null;
            if (string.IsNullOrEmpty(request.PaymentProvider))
                paymentProvider = ParseEnum<PaymentProviders>(request.PaymentProvider, "Error parsing paymentProvider");

            DateTime? fromDate = null;
            DateTime? toDate = null;

            if (request.PeriodFilter == TimePeriodFilter.Custom)
            {
                fromDate = ParseDateTime(request.FromDate, "Error parsing fromDate");
                toDate = ParseDateTime(request.ToDate, "Error parsing toDate");
            }

            var percentageIncrementPerPlanDtos = await _unitofwork.SubscriptionServices.GetPercentageIncrementPerPlan(application, paymentProvider.Value, request.PeriodFilter, fromDate, toDate);
            var response = new PercentageIncrementPerPlanResponse
            {
                PecentageIncrement = { percentageIncrementPerPlanDtos.Select(dto => new PercentageIncrementPerPlan
                {
                    PricingPlanId = dto.PricingPlanId.ToString(),
                    Name = dto.Name,
                    PercentageIncrement = dto.PercentageIncrement
                })}
            };

            return response;
        }

        public override async Task<PagedSubscriptionCompanyDetail> GetSubscribedCompanyDetail(SubscriptionQueryParametersRequest request, ServerCallContext context)
        {
            var enumValue = ParseEnum<Applications>(request.Application, "Error parsing application");

            DateTime? fromDate = null;
            DateTime? toDate = null;

            if (request.PeriodFilter == TimePeriodFilter.Custom)
            {
                fromDate = ParseDateTime(request.FromDate, "Error parsing fromDate");
                toDate = ParseDateTime(request.ToDate, "Error parsing toDate");
            }

            var subscriptionQueryParameters = new SubscriptionQueryParameters
            {
                Application = enumValue,
                PeriodFilter = request.PeriodFilter,
                FromDate = fromDate,
                ToDate = toDate,
                SortBy = request.SortBy,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                PlanId = request.PlanId
            };

            var pagedSubscriptionCompanyDetail = await _unitofwork.SubscriptionServices.GetSubscribedCompanyDetail(subscriptionQueryParameters);

            var response = new PagedSubscriptionCompanyDetail
            {
                Items = {
                    pagedSubscriptionCompanyDetail.Items.Select(item => new SubscriptionCompanyDetailResponse
                    {
                        CompanyName = item.CompanyName,
                        Country = item.Country,
                        Email = item.Email,
                        PlanName = item.PlanName,
                        StaffSize = item.StaffSize,
                        RegistrationDate = item.RegistrationDate.ToString("yyyy-MM-ddTHH:mm:ssZ") ?? ""
                    })
                },
                TotalSize = pagedSubscriptionCompanyDetail.TotalSize,
                PageNumber = pagedSubscriptionCompanyDetail.PageNumber,
                PageSize = pagedSubscriptionCompanyDetail.PageSize
            };

            return response;
        }

        public override async Task<SummaryStatisticsResponse> GetSummaryStatistics(SummaryStatisticsRequest request, ServerCallContext context)
        {
            var enumValue = ParseEnum<Applications>(request.Application, "Error parsing application");
            PaymentProviders? paymentProvider = null;
            if (string.IsNullOrEmpty(request.PaymentProvider))
                paymentProvider = ParseEnum<PaymentProviders>(request.PaymentProvider, "Error parsing paymentProvider");

            var statisticsDto = await _unitofwork.SubscriptionServices.GetSummaryStatistics(enumValue, paymentProvider);
            return new SummaryStatisticsResponse
            {
                TotalCompanies = statisticsDto.TotalCompanies,
                TotalRevenue = statisticsDto.TotalRevenue,
                TotalSubscriptions = statisticsDto.TotalSubscriptions
            };
        }

        public override async Task<TotalSubscriptionCountPerPlanResponse> GetTotalSubscriptionsCount(SubscriptionStatisticsRequest request, ServerCallContext context)
        {
            var enumValue = ParseEnum<Applications>(request.Application, "Error parsing application");
            PaymentProviders? paymentProvider = null;
            if (string.IsNullOrEmpty(request.PaymentProvider))
                paymentProvider = ParseEnum<PaymentProviders>(request.PaymentProvider, "Error parsing paymentProvider");

            DateTime? fromDate = null;
            DateTime? toDate = null;

            if (request.PeriodFilter == TimePeriodFilter.Custom)
            {
                fromDate = ParseDateTime(request.FromDate, "Error parsing fromDate");
                toDate = ParseDateTime(request.ToDate, "Error parsing toDate");
            }

            var totalSubscriptionCount = await _unitofwork.SubscriptionServices.GetTotalSubscriptionsCount(enumValue, paymentProvider, request.PeriodFilter, fromDate, toDate);
            var response = new TotalSubscriptionCountPerPlanResponse();
            response.TotalSubscriptionPerPlans.AddRange(totalSubscriptionCount
                .Select(item => new TotalSubscriptionCountPerPlan
                {
                    PricingPlanId = item.PricingPlanId.ToString(),
                    Name = item.Name,
                    SubscriptionCount = item.SubscriptionCount
                }));

            return response;
        }
    }
}