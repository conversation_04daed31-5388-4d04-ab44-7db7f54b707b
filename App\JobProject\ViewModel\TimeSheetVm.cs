﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.JobProjectManagement.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TimeSheetVm
    {
        public string Summary { get; set; } = string.Empty;
        public string TodoName { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Duration { get; set; }
        public TimeSheetStatus Status { get; set; }
        public DateTime StartDate { get; set; }
        public string Comments { get; set; }

        [AllowedTimeFormat]
        public string TimeSpent { get; set; }
        public string MemberId { get; set; } = string.Empty;
        public List<string> Tags { get; set; }
        public string ProjectId { get; set; }
        public bool IsBillable { get; set; }
        public decimal? AmountPerHour { get; set; }
        public string UserId { get; set; }

        [ForeignKey("SprintProject")]
        public string SprintId { get; set; }


        // Navigational properties
        public SprintProject Sprint { get; set; }

        public string ClientName { get; set; }
    }
}
