using DinkToPdf;
using iTextSharp.text.pdf;
using Microsoft.Playwright;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using PuppeteerSharp;
using PuppeteerSharp.Media;
using SkiaSharp;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Utils
{
    public static class HtmlToPdf
    {        public static Task<MemoryStream> ConvertHtmlToPdf(string html, string fileName)
        {
            try
            {
                Console.WriteLine("Initializing DinkToPdf converter...");
                var converter = new SynchronizedConverter(new DinkToPdf.PdfTools());
                Console.WriteLine("DinkToPdf converter initialized successfully");

                // Create a MemoryStream to capture the PDF output
                var memoryStream = new MemoryStream();

                var doc = new HtmlToPdfDocument
                {
                    GlobalSettings = new GlobalSettings
                    {
                        ColorMode = DinkToPdf.ColorMode.Color,
                        Orientation = Orientation.Portrait,
                        PaperSize = PaperKind.A4, // Specify paper size
                        Out = null, // Setting this to null directs output to the stream
                        DocumentTitle = Path.GetFileNameWithoutExtension(fileName)
                    }
                };

                // Add the HTML content as an object
                doc.Objects.Add(new ObjectSettings
                {
                    HtmlContent = html, // HTML content to convert
                    WebSettings = { DefaultEncoding = "utf-8" }, // Encoding
                    FooterSettings = { FontSize = 9, Right = "Page [page] of [toPage]", Line = true },
                    HeaderSettings = { FontSize = 9, Line = true }
                });

                Console.WriteLine("Converting HTML to PDF...");
                // Convert and write the output directly into the MemoryStream
                var pdfBytes = converter.Convert(doc);
                Console.WriteLine($"Conversion successful, generated PDF size: {pdfBytes.Length} bytes");
                
                memoryStream.Write(pdfBytes, 0, pdfBytes.Length);

                // Reset the position of the MemoryStream for reading
                memoryStream.Position = 0;

                try
                {
                    // Optionally, save the MemoryStream to a file for verification if a valid path is provided
                    if (!string.IsNullOrEmpty(fileName) && 
                        Directory.Exists(Path.GetDirectoryName(fileName)) && 
                        !string.IsNullOrEmpty(Path.GetExtension(fileName)))
                    {
                        File.WriteAllBytes(fileName, memoryStream.ToArray());
                        Console.WriteLine($"PDF saved to file: {fileName}");
                    }
                }
                catch (Exception fileEx)
                {
                    Console.WriteLine($"Warning: Could not save PDF to file: {fileEx.Message}");
                    // Continue even if file saving fails - we still have the memory stream
                }

                return Task.FromResult(memoryStream);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ConvertHtmlToPdf: {ex.Message}");
                Console.WriteLine($"StackTrace: {ex.StackTrace}");
                
                // Try to initialize the converter again to provide better diagnostics
                try
                {
                    DinkToPdfLibraryLoader.Init();
                    Console.WriteLine("DinkToPdf library initialized successfully on second attempt");
                }
                catch (Exception initEx)
                {
                    Console.WriteLine($"Error re-initializing DinkToPdf: {initEx.Message}");
                }
                
                // Rethrow to let the caller handle the error with proper status code
                throw new Exception($"PDF generation failed: {ex.Message}", ex);
            }
        }


        public static async Task<MemoryStream> ConvertHtmlToPdf(string htmlContent)
        {
            try
            {
                var options = new ChromeOptions();
                options.AddArgument("--headless");
                options.AddArgument("--window-size=1280x1024");

                using (var driver = new ChromeDriver(options))
                {
                    driver.Navigate().GoToUrl("data:text/html;charset=utf-8," + Uri.EscapeDataString(htmlContent));

                    // Wait until the page is fully loaded (you can also add waits for specific elements)
                    System.Threading.Thread.Sleep(2000);

                    // Capture screenshot and convert to Bitmap
                    var screenshot = ((ITakesScreenshot)driver).GetScreenshot();
                    using (var memoryStream = new MemoryStream(screenshot.AsByteArray))
                    {
                        var bitmap = SkiaSharpImageToBitmap(memoryStream); ;
                        var result = await ConvertToBase64(bitmap);
                        return result;
                    }
                }

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// Using PuppeteerSharp to convert HTML to PDF
        /// </summary>
        /// <param name="htmlContent"></param>
        /// <returns></returns>
        public static async Task<MemoryStream> ConvertHtmlToPdfUssingP(string htmlContent)
        {
            try
            {
                var browserFetcher = new BrowserFetcher();
                await browserFetcher.DownloadAsync(); // Downloads the default revision

                // Launch headless Chromium
                using var browser = await Puppeteer.LaunchAsync(new LaunchOptions
                {
                    Headless = true,
                    Args = new[] { "--no-sandbox", "--disable-setuid-sandbox" } // recommended for Linux
                });

                using var page = await browser.NewPageAsync();

                // Load your HTML content
                await page.SetContentAsync(htmlContent);
                await page.EmulateMediaTypeAsync(MediaType.Screen);

                // Generate PDF
                var pdfBuffer = await page.PdfDataAsync(new PdfOptions
                {
                    Format = PuppeteerSharp.Media.PaperFormat.A4,
                    PrintBackground = true,
                    MarginOptions = new MarginOptions { Top = "20px", Bottom = "20px" }
                });

                return new MemoryStream(pdfBuffer);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// Converts html to pdf using playright
        /// </summary>
        /// <param name="htmlContent"></param>
        /// <returns></returns>
        public static async Task<byte[]> ConvertHtmlToPdfAsyncUsingPlayRight(string htmlContent)
        {
            using var playwright = await Playwright.CreateAsync();
            await using var browser = await playwright.Chromium.LaunchAsync(new BrowserTypeLaunchOptions
            {
                Headless = true
            });

            var page = await browser.NewPageAsync();
            await page.SetContentAsync(htmlContent);

            var pdfBytes = await page.PdfAsync(new PagePdfOptions
            {
                Format = "A4"
            });

            return pdfBytes;
        }

        private static Bitmap SkiaSharpImageToBitmap(MemoryStream memoryStream)
        {
            using (var skiaImage = SKImage.FromEncodedData(memoryStream))
            {
                using (var skiaBitmap = SKBitmap.FromImage(skiaImage))
                {
                    return new Bitmap(skiaBitmap.Width, skiaBitmap.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                }
            }
        }

        public static Task<MemoryStream> ConvertToBase64(Bitmap image)
        {
            using (MemoryStream stream = new MemoryStream())
            {
                try
                {
                    // Create a new PDF document
                    using (iTextSharp.text.Document document = new iTextSharp.text.Document(new iTextSharp.text.Rectangle(image.Width, image.Height), 0, 0, 0, 0))
                    {
                        PdfWriter.GetInstance(document, stream);
                        document.Open();

                        // Convert the System.Drawing.Bitmap image to iTextSharp.text.Image
                        using (MemoryStream imageStream = new MemoryStream())
                        {
                            image.Save(imageStream, ImageFormat.Png);
                            iTextSharp.text.Image pdfImage = iTextSharp.text.Image.GetInstance(imageStream.ToArray());

                            // Optionally, set the DPI or scale the image to the document's size
                            pdfImage.ScaleToFit(document.PageSize.Width, document.PageSize.Height);
                            pdfImage.Alignment = iTextSharp.text.Element.ALIGN_CENTER;

                            // Add the image to the PDF
                            document.Add(pdfImage);
                        }
                        document.Close();
                    }

                    // Convert the PDF byte stream to a Base64 string
                    //byte[] pdfBytes = stream.ToArray();
                    //string base64String = Convert.ToBase64String(pdfBytes);
                    return Task.FromResult(stream);
                }
                catch (Exception ex)
                {
                    // Log or handle the exception as needed
                    throw new Exception("Error while converting image to PDF and Base64.", ex);
                }
            }
        }      
    }
}

