apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: backend-ingress 
  annotations:
    # kubernetes.io/ingress.class: "nginx" 
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
      #nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: "nginx"
  tls:
    - hosts:
        - jobpro.app
      secretName: jobsuite-secret
  rules:
  - host: web.jobpro.app
    http:
      paths:
      - pathType: Prefix
        path: /
        backend:
          service:
            name: backend-service  
            port:
              number: 80
