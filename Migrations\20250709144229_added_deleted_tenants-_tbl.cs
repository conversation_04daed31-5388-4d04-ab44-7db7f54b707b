﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added_deleted_tenants_tbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added_deleted_tenants_tbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DeletedTenants",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalTenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    CompanyName = table.Column<string>(type: "text", nullable: true),
                    CompanyAddress = table.Column<string>(type: "text", nullable: true),
                    WorkSpace = table.Column<string>(type: "text", nullable: true),
                    ContactNo = table.Column<string>(type: "text", nullable: true),
                    Subdomain = table.Column<string>(type: "text", nullable: true),
                    VerifiedEmailDomain = table.Column<string>(type: "text", nullable: true),
                    OriginalDateCreated = table.Column<DateTime>(type: "timestamp", nullable: false),
                    LogoUrl = table.Column<string>(type: "text", nullable: true),
                    CompanySize = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    Region = table.Column<string>(type: "text", nullable: true),
                    CompanyType = table.Column<string>(type: "text", nullable: true),
                    RegNumber = table.Column<string>(type: "text", nullable: true),
                    AdminId = table.Column<string>(type: "text", nullable: true),
                    Industry = table.Column<string>(type: "varchar(24)", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    DeletionReason = table.Column<string>(type: "text", nullable: true),
                    TotalUsers = table.Column<int>(type: "integer", nullable: false),
                    ActiveSubscriptions = table.Column<int>(type: "integer", nullable: false),
                    LastMigrationDate = table.Column<string>(type: "text", nullable: true),
                    DeletionNotes = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeletedTenants", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DeletedTenants",
                schema: _Schema);
        }
    }
}
