﻿using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System;
using Jobid.App.Helpers.Contract;
using Jobid.App.Tenant;
using Serilog;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Services.Contract;

namespace Jobid.App.Calender.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MeetingNotificationController : ControllerBase
    {
        private readonly IUnitofwork Services_Repo;
        private ILogger _logger = Log.ForContext<MeetingNotificationController>();
        private readonly ITenantSchema _tenantSchema;
        private readonly ILogService _logService;
       
        public MeetingNotificationController(IUnitofwork unitofwork, ITenantSchema tenantSchema, ILogService logService)
        {
            this.Services_Repo = unitofwork;
            _tenantSchema = tenantSchema;
            _logService = logService;
        }

        #region Send out meeting recording notification
        /// <summary>
        /// Send out meeting recording notification
        /// </summary>
        /// <param name="recordingNotificationDto"></param>
        /// <returns></returns>
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPost("MeetingRecordingNotification")]
        public async Task<IActionResult> MeetingRecordingNotification(MeetingRecordingNotificationDto recordingNotificationDto)
        {
            try
            {
                recordingNotificationDto.Subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                _logger.Information("MeetingRecordingNotification called", recordingNotificationDto);
                var result = await Services_Repo.CalenderService.MeetingRecordingNotification(recordingNotificationDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "An error occured while processing meeting recording notification try again later" });
            }
        }
        #endregion

        #region Mark meeting as happened
        /// <summary>
        /// Mark meeting as happened
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        [HttpPost("MarkMeetingAsHappened")]
        public async Task<IActionResult> MarkMeetingAsHappened(string meetingId)
        {
            try
            {
                _logger.Information("MarkMeetingAsHappened called", meetingId);

                var result = await Services_Repo.CalenderService.MarkMeetingAsHappened(meetingId);
                return result.ResponseCode == "200" ? Ok(result) : BadRequest(result);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "MarkMeetingAsHappened failed: RecordNotFoundException", meetingId);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "An error occured while marking meeting as happened try again later" });
            }
        }
        #endregion
    }
}
