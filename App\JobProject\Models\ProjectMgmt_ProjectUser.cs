﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Models
{
    public class ProjectMgmt_ProjectUser
    {
        [Key]
        public Guid Id { get; set; }
        public Guid ProjectMgmt_ProjectId { get; set; }
        public decimal? AmountPerHour { get; set; }
        public ProjectMgmt_Project projectMgmt_Project { get; set; }
        public string UserId { get; set; }
        public string Email { get; set; }
        public string CurrencySymbol { get; set; } = Currency.USD.ToString();

    }
}
