using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Microsoft.AspNetCore.Http;

namespace Jobid.App.Tenant.ViewModel
{
    public class TenantModelVM
    {
        public Guid Id { get; set; }
        public string CompanyName { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public DateTime DateCreated { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime LastUpdate { get; set; }
        public string LogoUrl { get; set; }
        public string Country { get; set; }
        public List<Plan> Plans { get; set; } = new List<Plan> { };
    }

    public class TenantDetailsVM
    {
        public Guid Id { get; set; }
        public string CompanyName { get; set; }
        public string WorkSpace { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string VerifiedEmailDomain { get; set; }
        public DateTime DateCreated { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime LastUpdate { get; set; }
        public bool isSchemaCreated { get; set; }
        public DateTime LastMigration { get; set; }
        public string AdminId { get; set; }
        public string LogoUrl { get; set; }
        public int CompanySize { get; set; }
        public string Status { get; set; }
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string CompanyType { get; set; }
        public string RegNumber { get; set; }
        public string Industry { get; set; }
        public string Website { get; set; }
    }

    public class Plan
    {
        public string PlanId { get; set; }
        public string PlanName { get; set; }
        public string Application { get; set; }
    }

    public class TenantVM
    {
        public string CompanyName { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string? LogoUrl { get; set; }
        public string CompanySize{ get; set; }
    }

    public class TenantRegistrationVM
    {
        public string CompanyName { get; set; }
        public string ContactNo { get; set; }
        public string Subdomain { get; set; }
        public string CompanySize{ get; set; }
        public string CompanyType { get; set; }

        /// <summary>
        /// This should be the country name
        /// </summary>
        [Required]
        public string Country { get; set; }
        public string CountryCode { get; set; }
        public string RegNumber { get; set; }
        public Industries Industry { get; set; }
        public Applications Application { get; set; } = Applications.Joble;

        [IsDomainFormatValid]
        public List<string> SecDomains { get; set; } = new List<string>();
    }

    public class RegisterTenantVM
    {
        [AllowedExtensions(new string[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx", ".doc" })]
        public IFormFile? Logo { get; set; }
        public bool ForEnterprizePlan { get; set; }
        public TenantRegistrationVM tenant { get; set; }
        public CompanyAdminModel companyAdmin  { get; set; }
    }

    public class CreateTenantForExistingUserVM
    {
        [AllowedExtensions(new string[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx", ".doc" })]
        public IFormFile? Logo { get; set; }
        public bool ForEnterprizePlan { get; set; } = false;
        public string CompanyEmail { get; set; }
        public string UserId { get; set; }
        public TenantRegistrationVM Tenant { get; set; }
    }

    public class TenantMigrationResultVM 
    {
        public int TotalSchemas { get; set; } = 0;
        public int MigratedSchemas { get; set; } = 0;
        public List<string> FailedSchemaMigrations { get; set; }
    }
}
