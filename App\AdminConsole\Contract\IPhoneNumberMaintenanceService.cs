using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.Helpers;

namespace Jobid.App.AdminConsole.Contract
{
    public interface IPhoneNumberMaintenanceService
    {
        /// <summary>
        /// Process monthly maintenance charges for all companies with phone numbers
        /// </summary>
        /// <param name="subdomains">List of company subdomains to process</param>
        /// <returns>Processing result</returns>
        Task ProcessMonthlyMaintenanceCharges(List<string> subdomains);

        /// <summary>
        /// Charge maintenance fee for a specific company
        /// </summary>
        /// <param name="tenantId">Company tenant ID</param>
        /// <param name="subdomain">Company subdomain</param>
        /// <returns>Charge result</returns>
        Task<GenericResponse> ChargeMaintenanceFee(Guid tenantId, string subdomain);

        /// <summary>
        /// Retry failed maintenance charges
        /// </summary>
        /// <param name="subdomains">List of company subdomains to retry</param>
        /// <returns>Retry result</returns>
        Task ProcessMaintenanceChargeRetries(List<string> subdomains);

        /// <summary>
        /// Send maintenance charge notification to company
        /// </summary>
        /// <param name="tenantId">Company tenant ID</param>
        /// <param name="notificationType">Type of notification</param>
        /// <param name="chargeAmount">Charge amount</param>
        /// <param name="phoneNumberCount">Number of phone numbers</param>
        /// <returns>Notification result</returns>
        Task<bool> SendMaintenanceChargeNotification(Guid tenantId, MaintenanceNotificationType notificationType, decimal chargeAmount, int phoneNumberCount);

        /// <summary>
        /// Get maintenance charge history for a company
        /// </summary>
        /// <param name="tenantId">Company tenant ID</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="pageNumber">Page number</param>
        /// <returns>Maintenance charge history</returns>
        Task<GenericResponse> GetMaintenanceChargeHistory(Guid tenantId, int pageSize = 10, int pageNumber = 1);

        /// <summary>
        /// Get pending maintenance charges
        /// </summary>
        /// <param name="tenantId">Optional tenant ID filter</param>
        /// <returns>Pending charges</returns>
        Task<GenericResponse> GetPendingMaintenanceCharges(Guid? tenantId = null);
    }
}
