﻿using Jobid.App.AdminConsole.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models
{
    public class CompanyDeletionRequest
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public string RequestId { get; set; }

        [Required]
        public string RequestedBy { get; set; }

        [Required]
        public string TenantId { get; set; }

        [Required]
        public string Reason { get; set; }

        public AccountDeletionStatus Status { get; set; }

        public DateTime DateRequested { get; set; }

        public DateTime? UpdatedOn { get; set; }

        public string ActionPerfomedBy { get; set; }

        public CompanyDeletionRequest()
        {
            Id = Guid.NewGuid();
            DateRequested = DateTime.UtcNow;
            Status = AccountDeletionStatus.Pending;
        }
    }
}
