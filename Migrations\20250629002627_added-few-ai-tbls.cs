﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class addedfewaitbls : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public addedfewaitbls(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsAIProcessed",
                schema: _Schema,
                table: "WikiFiles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "CompanyFilesSummaries",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    SummarizedText = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanyFilesSummaries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanyFilesSummaries_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CompanySummarizedInfos",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false),
                    SummarizedText = table.Column<string>(type: "text", nullable: true),
                    CreatedOn = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedOn = table.Column<DateTime>(type: "timestamp", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompanySummarizedInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompanySummarizedInfos_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PhoneNoToCompanyMappings",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PhoneNumber = table.Column<string>(type: "text", nullable: false),
                    TenantId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PhoneNoToCompanyMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PhoneNoToCompanyMappings_Tenants_TenantId",
                        column: x => x.TenantId,
                        principalSchema: _Schema,
                        principalTable: "Tenants",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CompanyFilesSummaries_TenantId",
                schema: _Schema,
                table: "CompanyFilesSummaries",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_CompanySummarizedInfos_TenantId",
                schema: _Schema,
                table: "CompanySummarizedInfos",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_PhoneNoToCompanyMappings_TenantId",
                schema: _Schema,
                table: "PhoneNoToCompanyMappings",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CompanyFilesSummaries",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "CompanySummarizedInfos",
                schema: _Schema);

            migrationBuilder.DropTable(
                name: "PhoneNoToCompanyMappings",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "IsAIProcessed",
                schema: _Schema,
                table: "WikiFiles");
        }
    }
}
