﻿using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.ViewModel;
using System.Collections.Generic;

namespace Jobid.App.Helpers.Utils
{
    public static class GlobalVariables
    {
        public static string ConnectionString { get; set; }
        public static List<string> Subdomains { get; set; }
        public static CustomAppSettings CustomAppSettings { get; set; }
        public static string JwtKey { get; set; }
        public static string Test { get; set; }
        public static string Environment { get; set; }
        public static string LoggedInUserId { get; set; }
        public static string Subdomain { get; set; }
        public static string TimeZone { get; set; }
        public static IRedisCacheService RedisCacheService { get; set; }
        public static Dictionary<string, List<string>> CacheKeys { get; set; } = new Dictionary<string, List<string>>();
    }
}
