using System;
using System.IO;
using System.Threading.Tasks;

namespace Jobid.App.Wiki.Services.Contract
{
    public interface IWikiFileBackgroundService
    {
        /// <summary>
        /// Process a large file upload in the background using AWS multipart upload
        /// </summary>
        /// <param name="fileId">ID of the file record in the database</param>
        /// <param name="filePath">Temporary path to the file on disk</param>
        /// <param name="awsKey">AWS S3 key for the file</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ProcessLargeFileUploadAsync(Guid fileId, string filePath, string awsKey);
        
        /// <summary>
        /// Process a large file upload directly from a stream using AWS multipart upload
        /// </summary>
        /// <param name="fileId">ID of the file record in the database</param>
        /// <param name="stream">Stream containing the file data</param>
        /// <param name="fileSize">Size of the file in bytes</param>
        /// <param name="awsKey">AWS S3 key for the file</param>
        /// <returns>True if the upload process started successfully, false otherwise</returns>
        Task<bool> ProcessLargeFileUploadStreamAsync(Guid fileId, Stream stream, long fileSize, string awsKey);
        
        /// <summary>
        /// Check the status of a background upload
        /// </summary>
        /// <param name="fileId">ID of the file record in the database</param>
        /// <returns>True if the upload is completed, false otherwise</returns>
        Task<bool> CheckUploadStatusAsync(Guid fileId);
    }
}
