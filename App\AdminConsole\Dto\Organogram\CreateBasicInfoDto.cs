﻿using System;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class CreateBasicInfoDto
    {
        public Guid UserId { get; set; }
        public DateTime? DateOfHire { get; set; }
        public DateTime? LastDayOfContract { get; set; }
        public int LengthOfContract { get; set; }
        public string Location { get; set; }
        public decimal GrossSalary { get; set; }
        public int AvailableNumberOfLeaveDays { get; set; }
    }
}
