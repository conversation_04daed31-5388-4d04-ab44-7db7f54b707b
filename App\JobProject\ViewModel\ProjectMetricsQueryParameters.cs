using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class ProjectMetricsQueryParameters
    {
        private const int DefaultPageSize = 10;
        private const int MaxPageSize = 100;
        private const int DefaultPageNumber = 1;

        private int _pageSize = DefaultPageSize;
        private int? _pageNumber;

        [Required]
        public TimePeriodFilter PeriodFilter { get; set; }
        public string SortBy { get; set; }
        public int PageNumber
        {
            get => _pageNumber ?? DefaultPageNumber;
            set => _pageNumber = value != 0 ? value : DefaultPageNumber;
        }

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = value > MaxPageSize ? MaxPageSize : value < 1 ? DefaultPageSize : value;
        }

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

    }
}