﻿using System.ComponentModel.DataAnnotations.Schema;
using System;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Jobid.App.Subscription.Models
{
    public class AdditionalLiecense
    {
        [Key]
        public Guid Id { get; set; }

        [ForeignKey("Subscription")]
        public Guid SubscriptionId { get; set; }
        public double Amount { get; set; }
        public string Status { get; set; }
        public string PaymentId { get; set; }
        public DateTime? TransactionDate { get; set; }
        public string MollieCustomerId { get; set; }
        public string StripeCustomerId { get; set; }
        public int? SubscriptionFor { get; set; }
        public string StripePriceId { get; set; }

        [Column(TypeName = "text")]
        public string UserIdsJson { get; set; }

        [NotMapped]
        public List<string> UserIds
        {
            get
            {
                return string.IsNullOrEmpty(UserIdsJson)
                    ? new List<string>()
                    : JsonConvert.DeserializeObject<List<string>>(UserIdsJson);
            }
            set
            {
                UserIdsJson = JsonConvert.SerializeObject(value);
            }
        }

        /// <summary>
        /// This is set to true when this additional license has been updated to the main subscription
        /// </summary>
        public bool Updated { get; set; }

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Subscription Subscription { get; set; }

        public AdditionalLiecense()
        {
            UserIds = new List<string>();
        }
    }
}
