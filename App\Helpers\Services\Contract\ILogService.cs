﻿using Jobid.App.Helpers.ViewModel;
using System;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface ILogService
    {
        void LogMethodCall(string MethodName, object request, object response, DateTime? requested = null);
        void InsertEvent(LogModel model);
        void LogTypeResponse<T, U>(T req, U response, string action, string message = null, DateTime? requested = null, DateTime? responseTime = null);
        void LogTypeRequest<T>(T req, string action, string message = null, DateTime? requested = null, DateTime? responseTime = null);
        void LogError(string action, string message, string eventId = null, object request = null);
    }
}