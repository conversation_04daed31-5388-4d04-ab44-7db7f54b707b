﻿using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.ViewModel;
using Newtonsoft.Json;
using System;
using WatchDog;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class LogService : ILogService
    {
        public LogService()
        {
            JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };
        }        public void LogMethodCall(string MethodName, object request, object response, DateTime? requested = null)
        {
            try
            {
                WatchDogServiceWrapper.LogWithRetry($"Request from {MethodName} Method Request: {JsonConvert.SerializeObject(request)} with Response: {JsonConvert.SerializeObject(response)}", "Requested:" + JsonConvert.SerializeObject(request), MethodName);
            }
            catch (Exception ex)
            {
                // Fallback to console/debug if WatchDog fails
                System.Diagnostics.Debug.WriteLine($"WatchDog logging failed in LogMethodCall: {ex.Message}");
            }
        }

        public void InsertEvent(LogModel model)
        {
            try
            {
                LogModel entity = new LogModel
                {
                    Action = model.Action,
                    CreatedOn = DateTime.UtcNow,
                    Message = model.Message,
                    Request = model.Request,
                    RequestTime = model.RequestTime,
                    Response = model.Response,
                    ResponseTime = model.ResponseTime,
                    LogMode = model.LogMode
                };

                if (model.RequestTime.HasValue && model.ResponseTime.HasValue)
                {
                    entity.ElapseTime = model.RequestTime.Value - model.ResponseTime.Value;
                }                WatchDogServiceWrapper.LogWithRetry(JsonConvert.SerializeObject(entity), model.Message, $"{model.Action}");
            }
            catch (Exception ex)
            {
                // Log the WatchDog failure for debugging
                System.Diagnostics.Debug.WriteLine($"WatchDog logging failed in InsertEvent: {ex.Message}");
            }
        }


        public void LogTypeResponse<T, U>(T req, U response, string action, string message = null, DateTime? requested = null, DateTime? responseTime = null)
        {
            LogModel logresp = new LogModel
            {
                CreatedOn = DateTime.UtcNow,
                Action = action,
                Request = JsonConvert.SerializeObject(req),
                Response = JsonConvert.SerializeObject(response),
                ResponseTime = responseTime,
                Message = message,
                LogMode = "Response",
                RequestTime = requested
            };
            InsertEvent(logresp);

        }

        public void LogTypeRequest<T>(T req, string action, string message = null, DateTime? requested = null, DateTime? responseTime = null)
        {
            LogModel logreq = new LogModel
            {
                CreatedOn = DateTime.UtcNow,
                Action = action,
                Request = JsonConvert.SerializeObject(req),
                Message = message,
                Response = "",
                ResponseTime = responseTime,
                LogMode = "Request",
                RequestTime = requested
            };

            InsertEvent(logreq);
        }        public void LogError(string action, string message, string eventId = null, object request = null)
        {
            try
            {
                WatchDogServiceWrapper.LogErrorWithRetry(message, eventId, action, JsonConvert.SerializeObject(request));
            }
            catch (Exception ex)
            {
                // Fallback logging for debugging
                System.Diagnostics.Debug.WriteLine($"WatchDog error logging failed in LogError: {ex.Message}");
            }
        }
    }
}