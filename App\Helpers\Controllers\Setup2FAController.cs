﻿using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Utils._Helper;
using Jobid.App.Helpers.ViewModel;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Controllers
{
    [Route("api/[controller]")]
    [Produces("Application/json")]
    public class Setup2FAController : ControllerBase
    {
        private readonly IUnitofwork _unitofwork;

        public Setup2FAController(IUnitofwork unitofwork)
        {
            _unitofwork = unitofwork;
        }

        #region Set 2FA
        /// <summary>
        /// Setup 2FA
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("setup-2fa")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> Setup2FA([Required] [FromQuery] TwoFactorSetupRequest model)
        {
            model.Subdomain = HttpContext.Request.Headers["subdomain"].ToString();
            var response = await _unitofwork.I2FAService.Setup2FA(model);
            return response.ResponseCode == "200" 
                ? Ok(response) 
                : BadRequest(response);
        }
        #endregion

        #region Enable 2FA
        /// <summary>
        /// Enable 2FA
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("enable-2fa")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> Enable2FA([FromBody] TwoFactorVerificationRequest model)
        {
            model.UserId = User.GetUserId();
            var response = await _unitofwork.I2FAService.Enable2FA(model);
            return response.ResponseCode == "200" 
                ? Ok(response) 
                : BadRequest(response);
        }
        #endregion

        #region Disable 2FA
        /// <summary>
        /// Disable 2FA
        /// </summary>
        /// <returns></returns>
        [HttpPost("disable-2fa")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        [ProducesResponseType(typeof(GenericResponse), 500)]
        public async Task<IActionResult> Disable2FA()
        {
            var userId = User.GetUserId();
            var response = await _unitofwork.I2FAService.Disable2FA(userId);
            return response.ResponseCode == "200" 
                ? Ok(response) 
                : BadRequest(response);
        }
        #endregion
    }
}
