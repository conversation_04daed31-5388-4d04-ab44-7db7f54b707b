using System;
using System.ComponentModel.DataAnnotations;
using Jobid.App.Helpers.Utils.Attributes;

namespace Jobid.App.AdminConsole.Dto
{
    /// <summary>
    /// DTO for adding billing information
    /// </summary>
    public class AddBillingInformationDto
    {
        /// <summary>
        /// First name of billing contact
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        public string FirstName { get; set; }

        /// <summary>
        /// Last name of billing contact
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        public string LastName { get; set; }

        /// <summary>
        /// Email address for billing contact - must match company domain
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        [ValidCompanyEmailDomian(ErrorMessage = "Email must use company domain")]
        public string Email { get; set; }

        /// <summary>
        /// Country for billing address
        /// </summary>
        [Required(ErrorMessage = "Country is required")]
        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string Country { get; set; }

        /// <summary>
        /// Company address for billing
        /// </summary>
        [Required(ErrorMessage = "Company address is required")]
        [StringLength(500, ErrorMessage = "Company address cannot exceed 500 characters")]
        public string CompanyAddress { get; set; }

        /// <summary>
        /// Business registration number
        /// </summary>
        [StringLength(100, ErrorMessage = "Business registration number cannot exceed 100 characters")]
        public string BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// VAT number
        /// </summary>
        [StringLength(100, ErrorMessage = "VAT number cannot exceed 100 characters")]
        public string VATNumber { get; set; }
    }

    /// <summary>
    /// DTO for updating billing information
    /// </summary>
    public class UpdateBillingInformationDto
    {
        /// <summary>
        /// First name of billing contact
        /// </summary>
        [Required(ErrorMessage = "First name is required")]
        [StringLength(100, ErrorMessage = "First name cannot exceed 100 characters")]
        public string FirstName { get; set; }

        /// <summary>
        /// Last name of billing contact
        /// </summary>
        [Required(ErrorMessage = "Last name is required")]
        [StringLength(100, ErrorMessage = "Last name cannot exceed 100 characters")]
        public string LastName { get; set; }

        /// <summary>
        /// Email address for billing contact - must match company domain
        /// </summary>
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
        [ValidCompanyEmailDomian(ErrorMessage = "Email must use company domain")]
        public string Email { get; set; }

        /// <summary>
        /// Country for billing address
        /// </summary>
        [Required(ErrorMessage = "Country is required")]
        [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
        public string Country { get; set; }

        /// <summary>
        /// Company address for billing
        /// </summary>
        [Required(ErrorMessage = "Company address is required")]
        [StringLength(500, ErrorMessage = "Company address cannot exceed 500 characters")]
        public string CompanyAddress { get; set; }

        /// <summary>
        /// Business registration number
        /// </summary>
        [StringLength(100, ErrorMessage = "Business registration number cannot exceed 100 characters")]
        public string BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// VAT number
        /// </summary>
        [StringLength(100, ErrorMessage = "VAT number cannot exceed 100 characters")]
        public string VATNumber { get; set; }
    }

    /// <summary>
    /// DTO for returning billing information
    /// </summary>
    public class BillingInformationResponseDto
    {
        /// <summary>
        /// Billing information ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// First name of billing contact
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// Last name of billing contact
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// Email address for billing contact
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Country for billing address
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// Company address for billing
        /// </summary>
        public string CompanyAddress { get; set; }

        /// <summary>
        /// Business registration number
        /// </summary>
        public string BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// VAT number
        /// </summary>
        public string VATNumber { get; set; }

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When this record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// User who created this record
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// User who last updated this record
        /// </summary>
        public string UpdatedBy { get; set; }
    }
}
