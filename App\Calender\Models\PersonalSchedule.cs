﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Calender.Models
{
    public class PersonalSchedule : BaseModel
    {
        [Key]
        public Guid Id { get; set; }
        public PersonalScheduleType ScheduleName { get; set; }
        public DateTime Date { get; set; } = DateTime.UtcNow;
        public string Day { get; set; }
        public bool Available { get; set; }
        public bool ScheduleIsPublic { get; set; } = false;
        public bool TeamMatesCanSee { get; set; } = false;
        public bool AvailableForMeetingsEmergencyHours { get; set; } = false;
        public bool AvailableForTasksEmergencyHours { get; set; } = false;
        public bool AvailableForSprintsEmergencyHours { get; set; } = false;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string TimeZone { get; set; }
        public string TimeBreakDown { get; set; }
        public string SelectedTimeSlots { get; set; }
        public Guid UserId { get; set; }
        public User User { get; set; }

        // Nullable ExternalMeetingId - only required when ScheduleName is Custom
        public string MeetingId { get; set; }
    }
}
