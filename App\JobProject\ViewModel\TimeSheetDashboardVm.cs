﻿using Microsoft.Graph.Models;
using System.Collections.Generic;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TimeSheetDashboardVm
    {
        public int TotalTimeInMin { get; set; }
        public decimal TotalEarnings { get; set; }
        public string TopProject { get; set; }
        public string TopTeamMember { get; set; }
        public int TotalHoursWorked { get; set; }
        public string ProjectOrTeamOrTeamMember { get; set; }
        public Dictionary<string, Dictionary<string, int>> BarChartData { get; set; } = new Dictionary<string, Dictionary<string, int>>();
    }
}
