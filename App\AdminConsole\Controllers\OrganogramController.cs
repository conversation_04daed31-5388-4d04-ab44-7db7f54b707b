﻿using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto.Organogram;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProjectManagement.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Controllers
{
    [Authorize]
    [Produces("Application/json")]
    [ApiController]
    [Route("api/[controller]")]
    public class OrganogramController : BaseController
    {
        private readonly IOrganogramService _organogramService;
        private readonly ILogger _logger = Log.ForContext<OrganogramController>();

        public OrganogramController(IOrganogramService organogramService)
        {
            _organogramService = organogramService;
        }

        #region Create Parent Company
        /// <summary>
        /// Creates a new parent company in the organization structure
        /// </summary>
        /// <param name="dto">The parent company details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created company details</returns>
        [HttpPost]
        [Route("CreateParentCompany")]
        public async Task<IActionResult> CreateParentCompany([FromBody] CreateParentCompanyDto dto, CancellationToken cancellationToken)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();


            if (string.IsNullOrEmpty(subdomain))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = "Subdomain is required"
                });
            }

            var result = await _organogramService.CreateParentCompanyAsync(dto, cancellationToken);

            return Ok(new ApiResponse<OrganogramCompanyResponseDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Parent company created successfully"
            });
        }
        #endregion

        #region Create Subsidiary Company
        /// <summary>
        /// Creates a new subsidiary company under a specified parent company
        /// </summary>
        /// <param name="dto">The subsidiary company details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created subsidiary company details</returns>
        [HttpPost]
        [Route("CreateSubsidiaryCompany")]
        public async Task<IActionResult> CreateSubsidiaryCompany([FromBody] CreateSubsidiaryCompanyDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();


                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.CreateSubsidiaryCompanyAsync(dto, cancellationToken);

                return Ok(new ApiResponse<OrganogramCompanyResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Subsidiary company created successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    DevResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Update Parent Company
        /// <summary>
        /// Updates an existing parent company's information
        /// </summary>
        /// <param name="dto">The updated company information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated company details</returns>
        [HttpPut]
        [Route("UpdateParentCompany")]
        public async Task<IActionResult> UpdateParentCompany([FromBody] UpdateOrganogramCompanyDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateParentCompanyAsync(dto, cancellationToken);

                return Ok(new ApiResponse<OrganogramCompanyResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Parent company updated successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Update Subsidiary Company
        /// <summary>
        /// Updates an existing subsidiary company's information
        /// </summary>
        /// <param name="dto">The updated company information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated company details</returns>
        [HttpPut]
        [Route("UpdateSubsidiaryCompany")]
        public async Task<IActionResult> UpdateSubsidiaryCompany([FromBody] UpdateOrganogramCompanyDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateSubsidiaryCompanyAsync(dto, cancellationToken);

                return Ok(new ApiResponse<OrganogramCompanyResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Subsidiary company updated successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Delete Subsidiary Company
        /// <summary>
        /// Deletes a subsidiary company if it has no associated departments or employees
        /// </summary>
        /// <param name="id">The ID of the subsidiary company to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the company was deleted, false if it wasn't found</returns>
        [HttpDelete]
        [Route("DeleteSubsidiaryCompany/{id}")]
        public async Task<IActionResult> DeleteSubsidiaryCompany(Guid id, CancellationToken cancellationToken)
        {

            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

            if (string.IsNullOrEmpty(subdomain))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = "Subdomain is required"
                });
            }

            var result = await _organogramService.DeleteSubsidiaryCompanyAsync(id, cancellationToken);

            if (!result)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = "Subsidiary company not found"
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Data = true,
                ResponseCode = "200",
                ResponseMessage = "Subsidiary company deleted successfully"
            });

        }
        #endregion

        #region Create Department
        /// <summary>
        /// Creates a new department in a company
        /// </summary>
        /// <param name="dto">The department details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created department details</returns>
        [HttpPost]
        [Route("CreateDepartment")]
        public async Task<IActionResult> CreateDepartment([FromBody] CreateDepartmentDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.CreateDepartmentAsync(dto, cancellationToken);

                return Ok(new ApiResponse<DepartmentResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Department created successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Update Department
        /// <summary>
        /// Updates an existing department
        /// </summary>
        /// <param name="dto">The updated department details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated department details</returns>
        [HttpPut]
        [Route("UpdateDepartment")]
        public async Task<IActionResult> UpdateDepartment([FromBody] UpdateDepartmentDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateDepartmentAsync(dto, cancellationToken);

                return Ok(new ApiResponse<DepartmentResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Department updated successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Delete Department
        /// <summary>
        /// Deletes a department and its associated employees
        /// </summary>
        /// <param name="id">The ID of the department to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status of the deletion operation</returns>
        [HttpDelete]
        [Route("DeleteDepartment/{id}")]
        public async Task<IActionResult> DeleteDepartment(Guid id, CancellationToken cancellationToken)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

            if (string.IsNullOrEmpty(subdomain))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = "Subdomain is required"
                });
            }

            var result = await _organogramService.DeleteDepartmentAsync(id, cancellationToken);

            if (!result)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = "Department not found"
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Data = true,
                ResponseCode = "200",
                ResponseMessage = "Department and associated employees deleted successfully"
            });
        }
        #endregion

        #region Create Position
        /// <summary>
        /// Creates a new position in a department
        /// </summary>
        /// <param name="dto">The position details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created position details</returns>
        [HttpPost]
        [Route("CreatePosition")]
        public async Task<IActionResult> CreatePosition([FromBody] CreateEmployeePositionDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.CreateEmployeePositionAsync(dto, cancellationToken);

                return Ok(new ApiResponse<EmployeePositionResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Position created successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Update Position
        /// <summary>
        /// Updates an existing position
        /// </summary>
        /// <param name="dto">The updated position details</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated position details</returns>
        [HttpPut]
        [Route("UpdatePosition")]
        public async Task<IActionResult> UpdatePosition([FromBody] UpdateEmployeePositionDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateEmployeePositionAsync(dto, cancellationToken);

                return Ok(new ApiResponse<EmployeePositionResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Position updated successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Delete Position
        /// <summary>
        /// Deletes a position and its associated employees
        /// </summary>
        /// <param name="id">The ID of the position to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status of the deletion operation</returns>
        [HttpDelete]
        [Route("DeletePosition/{id}")]
        public async Task<IActionResult> DeletePosition(Guid id, CancellationToken cancellationToken)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

            if (string.IsNullOrEmpty(subdomain))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = "Subdomain is required"
                });
            }

            var result = await _organogramService.DeleteEmployeePositionAsync(id, cancellationToken);

            if (!result)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = "Position not found"
                });
            }

            return Ok(new ApiResponse<bool>
            {
                Data = true,
                ResponseCode = "200",
                ResponseMessage = "Position and associated employees deleted successfully"
            });
        }
        #endregion

        #region Create Individual
        /// <summary>
        /// Creates a new individual in the organization with specified company, department, and position
        /// </summary>
        /// <param name="dto">The individual details</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created individual details or error response</returns>
        [HttpPost]
        [Route("CreateIndividual")]
        public async Task<IActionResult> CreateIndividual([FromBody] CreateIndividualDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.CreateIndividualAsync(dto, cancellationToken);

                return Ok(new ApiResponse<IndividualResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Individual created successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Update Individual
        /// <summary>
        /// Updates an existing individual's information in the organization
        /// </summary>
        /// <param name="dto">The updated individual information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated individual details or error response</returns>
        [HttpPut]
        [Route("UpdateIndividual")]
        public async Task<IActionResult> UpdateIndividual([FromBody] UpdateIndividualDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();


                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateIndividualAsync(dto, cancellationToken);

                return Ok(new ApiResponse<IndividualResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Individual updated successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Delete Individual
        /// <summary>
        /// Deletes an individual from the organization if they have no subordinates
        /// </summary>
        /// <param name="id">The ID of the individual to delete</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>Success status or error response</returns>
        [HttpDelete]
        [Route("DeleteIndividual/{id}")]
        public async Task<IActionResult> DeleteIndividual(Guid id, CancellationToken cancellationToken)
        {
            
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();


                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.DeleteIndividualAsync(id, cancellationToken);

                if (!result)
                {
                    return NotFound(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "404",
                        ResponseMessage = "Individual not found"
                    });
                }

                return Ok(new ApiResponse<bool>
                {
                    Data = true,
                    ResponseCode = "200",
                    ResponseMessage = "Individual deleted successfully"
                });
            
        }
        #endregion

        #region Get Organization Hierarchy
        /// <summary>
        /// Gets the complete organizational hierarchy for a given subdomain
        /// </summary>
        /// <returns>The complete organizational structure including companies, departments, positions, and individuals</returns>
        /// <response code="200">Successfully retrieved the organization hierarchy</response>
        /// <response code="400">If subdomain is missing</response>
        /// <response code="404">If parent company not found</response>
        /// <response code="500">Internal server error</response>
        [HttpGet]
        [Route("GetOrganizationHierarchy")]
        public async Task<IActionResult> GetOrganizationHierarchy(CancellationToken cancellationToken)
        {

            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

            if (string.IsNullOrEmpty(subdomain))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = "Subdomain is required"
                });
            }

            var result = await _organogramService.GetOrganizationHierarchyAsync(subdomain, cancellationToken);

            return Ok(new ApiResponse<OrganogramHierarchyDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Organization hierarchy retrieved successfully"
            });
        }
        #endregion

        #region Get Users suggestions
        /// <summary>
        /// Suggests users based on name search
        /// </summary>
        /// <param name="searchTerm">The name to search for</param>
        /// <param name="maxResults">Maximum number of results to return (default 10)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of matching users</returns>
        [HttpGet]
        [Route("UserSuggestions")]
        public async Task<IActionResult> GetUserSuggestions([FromQuery] string searchTerm, [FromQuery] int maxResults = 10, CancellationToken cancellationToken = default)
        {

                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();


                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var suggestions = await _organogramService.GetUserSuggestionsAsync(searchTerm, maxResults, cancellationToken);

                return Ok(new ApiResponse<List<UserSuggestionDto>>
                {
                    Data = suggestions,
                    ResponseCode = "200",
                    ResponseMessage = "User suggestions retrieved successfully"
                });

        }
        #endregion

        #region Basic Info Management
        /// <summary>
        /// Gets basic information for a specific user
        /// </summary>
        /// <param name="userId">The user ID to get information for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The user's basic information</returns>
        [HttpGet]
        [Route("GetBasicInfo/{userId}")]
        public async Task<IActionResult> GetBasicInfo(Guid userId, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.GetBasicInfoAsync(userId, cancellationToken);

                return Ok(new ApiResponse<BasicInfoResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Basic information retrieved successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }

        /// <summary>
        /// Creates basic information for a user
        /// </summary>
        /// <param name="dto">The basic information to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created basic information</returns>
        [HttpPost]
        [Route("AddBasicInfo")]
        public async Task<IActionResult> AddBasicInfo([FromBody] CreateBasicInfoDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.AddBasicInfoAsync(dto, cancellationToken);

                return Ok(new ApiResponse<BasicInfoResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Basic information created successfully"
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }

        /// <summary>
        /// Updates basic information for a specific user
        /// </summary>
        /// <param name="dto">The updated basic information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated basic information</returns>
        [HttpPut]
        [Route("UpdateBasicInfo")]
        public async Task<IActionResult> UpdateBasicInfo([FromBody] UpdateBasicInfoDto dto, CancellationToken cancellationToken)
        {

                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateBasicInfoAsync(dto, cancellationToken);

                return Ok(new ApiResponse<BasicInfoResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Basic information updated successfully"
                });
        }
        #endregion

        #region Emergency Contact Management
        /// <summary>
        /// Gets emergency contact information for a specific user
        /// </summary>
        /// <param name="userId">The user ID to get emergency contact for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The user's emergency contact information</returns>
        [HttpGet]
        [Route("GetEmergencyContact/{userId}")]
        public async Task<IActionResult> GetEmergencyContact(Guid userId, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.GetEmergencyContactAsync(userId, cancellationToken);

                return Ok(new ApiResponse<EmergencyContactResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Emergency contact information retrieved successfully"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }

        /// <summary>
        /// Creates emergency contact information for a user
        /// </summary>
        /// <param name="dto">The emergency contact information to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created emergency contact information</returns>
        [HttpPost]
        [Route("AddEmergencyContact")]
        public async Task<IActionResult> AddEmergencyContact([FromBody] CreateEmergencyContactDto dto, CancellationToken cancellationToken)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.AddEmergencyContactAsync(dto, cancellationToken);

                return Ok(new ApiResponse<EmergencyContactResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Emergency contact information created successfully"
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }

        /// <summary>
        /// Updates emergency contact information for a specific user
        /// </summary>
        /// <param name="dto">The updated emergency contact information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated emergency contact information</returns>
        [HttpPut]
        [Route("UpdateEmergencyContact")]
        public async Task<IActionResult> UpdateEmergencyContact([FromBody] UpdateEmergencyContactDto dto, CancellationToken cancellationToken)
        {

                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();

                if (string.IsNullOrEmpty(subdomain))
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "400",
                        ResponseMessage = "Subdomain is required"
                    });
                }

                var result = await _organogramService.UpdateEmergencyContactAsync(dto, cancellationToken);

                return Ok(new ApiResponse<EmergencyContactResponseDto>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Emergency contact information updated successfully"
                });

        }
        #endregion

        #region Register Complaint

        [HttpPost]
        [Route("RegisterComplaint")]
        public async Task<IActionResult> RegisterComplaint(RegisterComplaintDto dto, CancellationToken cancellationToken)
        {

                var subdomain = HttpContext.Request.Headers["subdomain"];
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var response = await _organogramService.RegisterComplaint(dto, subdomain, cancellationToken);
                if (response.ResponseCode == "200")
                    return Ok(response);
                return BadRequest(response);
            
        }
        #endregion

        #region Withdraw Action 
        [HttpPost]
        [Route("RegisterComplaint/{id}/{userId}")]
        public async Task<IActionResult> WithdrawAction([Required] string id, [Required] string userId)
        {

                var subdomain = HttpContext.Request.Headers["subdomain"];
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();

                var response = await _organogramService.WithdrawAction(id, userId, subdomain);
                if (response.ResponseCode == "200")
                    return Ok(response);
                return BadRequest(response);

        }
        #endregion

        #region Get All User Complaints 
        [HttpGet]
        [Route("GetAllUserComplaints/{userId}")]
        public async Task<IActionResult> GetAllUserComplaints([FromQuery] PaginationParameters parameters, [Required] string userId)
        {

                var subdomain = HttpContext.Request.Headers["subdomain"];
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var response = await _organogramService.GetAllUserComplaints(userId, parameters, subdomain);
                return Ok(new ApiResponse<Page<GetRegisteredComplaint>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Complaints fetched successfully",
                    Data = response
                });

        }
        #endregion

        #region Get All User Complaints 
        [HttpGet]
        [Route("GetAllUserComplaints/{id}/{userId}")]
        public async Task<IActionResult> GetAllUserComplaints([FromQuery] PaginationParameters parameters, [Required] string id, [Required] string userId)
        {

                var subdomain = HttpContext.Request.Headers["subdomain"];
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var response = await _organogramService.ViewUserComplaints(userId, id, subdomain);
                if (response.ResponseCode == "200")
                    return Ok(response);
                return BadRequest(response);

        }
        #endregion

    }
}