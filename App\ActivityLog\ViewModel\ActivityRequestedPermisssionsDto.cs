﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Text.Json.Serialization;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using System.Collections.Generic;

namespace Jobid.App.ActivityLog.ViewModel
{
    public class ActivityRequestedPermisssionsDto
    {
        [Required (ErrorMessage = "UserId cannot be null")]
        public string UserId { get; set; }
        public List<string> EventCategories { get; set; } = new List<string>();
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        [JsonIgnore]
        public string RequesterId { get; set; }
    }
}
