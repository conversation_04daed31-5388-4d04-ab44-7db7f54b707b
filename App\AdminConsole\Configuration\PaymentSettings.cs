using Microsoft.Extensions.Configuration;

namespace Jobid.App.AdminConsole.Configuration
{
    public class PaymentSettings
    {
        public StripeSettings Stripe { get; set; }
        public MollieSettings Mollie { get; set; }
    }

    public class StripeSettings
    {
        public string SecretKey { get; set; }
        public string PublicKey { get; set; }
        public string <PERSON>hookSecret { get; set; }
    }

    public class MollieSettings
    {
        public string ApiKey { get; set; }
    }

    public class PaymentConfiguration
    {
        private readonly IConfiguration _configuration;

        public PaymentConfiguration(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public PaymentSettings GetPaymentSettings()
        {
            return _configuration.GetSection("Payments").Get<PaymentSettings>();
        }
    }
}
