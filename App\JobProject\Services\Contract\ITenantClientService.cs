﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.JobProject.Models;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ITenantClientService
    {
        // Task<TenantClient> AddTenantClients(List<string> names);
        Task<List<TenantClient>> GetTenantClients();
    }
}
