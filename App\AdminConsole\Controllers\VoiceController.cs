using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;

namespace Jobid.App.AdminConsole.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class VoiceController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<VoiceController> _logger;

        public VoiceController(IConfiguration configuration, ILogger<VoiceController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        [HttpPost("access-token")]
        public IActionResult GenerateAccessToken([FromBody] AccessTokenRequest request)
        {
            try
            {
                var accountSid = _configuration["Twilio:AccountSid"];
                var apiKey = _configuration["Twilio:ApiKey"];
                var apiSecret = _configuration["Twilio:ApiSecret"];
                var twimlAppSid = _configuration["Twilio:TwiMLAppSid"];

                if (string.IsNullOrEmpty(accountSid) || string.IsNullOrEmpty(apiKey) || 
                    string.IsNullOrEmpty(apiSecret) || string.IsNullOrEmpty(twimlAppSid))
                {
                    return BadRequest(new { error = "Twilio configuration is incomplete" });
                }

                // Create JWT token for Twilio Voice SDK
                var token = GenerateTwilioAccessToken(
                    accountSid, 
                    apiKey, 
                    apiSecret, 
                    twimlAppSid, 
                    request.UserId
                );

                return Ok(new { token = token });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating access token for user {UserId}", request.UserId);
                return StatusCode(500, new { error = "Failed to generate access token" });
            }
        }

        [HttpPost("twiml/outbound")]
        public IActionResult HandleOutboundCall([FromForm] string To, [FromForm] string CallSid)
        {
            try
            {
                // Simple TwiML to dial the requested number
                var twiml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Dial>
        <Number>{To}</Number>
    </Dial>
</Response>";

                _logger.LogInformation("Handling outbound call to {To} with CallSid {CallSid}", To, CallSid);

                return Content(twiml, "application/xml");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling outbound call to {To}", To);
                
                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say>Sorry, there was an error processing your call.</Say>
    <Hangup/>
</Response>";

                return Content(errorTwiml, "application/xml");
            }
        }

        [HttpPost("twiml/inbound")]
        [AllowAnonymous]
        public IActionResult HandleInboundCallToWebRTC([FromForm] string From, [FromForm] string To, [FromForm] string CallSid)
        {
            try
            {
                // You can customize this logic to route calls to specific agents/users
                // For now, we'll try to route to available WebRTC clients
                
                // Get available agents (you'll need to implement this based on your user management)
                var availableAgent = GetAvailableAgent(); // This would be your custom logic
                
                var twiml = "";
                
                if (!string.IsNullOrEmpty(availableAgent))
                {
                    // Route to specific WebRTC browser client
                    twiml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say voice=""alice"">Connecting you to an agent.</Say>
    <Dial timeout=""30"" callerId=""{From}"">
        <Client>{availableAgent}</Client>
    </Dial>
    <Say voice=""alice"">Sorry, no agents are available right now. Please try again later.</Say>
    <Hangup/>
</Response>";
                }
                else
                {
                    // Fallback to traditional queue system
                    var baseUrl = _configuration["AppSettings:BaseUrl"] ?? "https://your-domain.com";
                    var holdMusicUrl = $"{baseUrl}/api/PhoneNumber/queue-wait-music";
                    var voicemailUrl = $"{baseUrl}/api/PhoneNumber/voicemail?callSid={CallSid}";

                    twiml = $@"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say voice=""alice"">Please hold while we connect you to an agent.</Say>
    <Enqueue waitUrl=""{holdMusicUrl}"" action=""{voicemailUrl}"" waitUrlMethod=""GET"">support_queue</Enqueue>
</Response>";
                }

                _logger.LogInformation("Handling inbound call to WebRTC from {From} with CallSid {CallSid}", From, CallSid);

                return Content(twiml, "application/xml");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling inbound call to WebRTC from {From}", From);
                
                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say>Sorry, there was an error processing your call.</Say>
    <Hangup/>
</Response>";

                return Content(errorTwiml, "application/xml");
            }
        }

        [HttpPost("register-for-calls")]
        public IActionResult RegisterForInboundCalls([FromBody] RegisterForCallsRequest request)
        {
            try
            {
                // TODO: Store user availability in cache/database
                // For now, just log the registration
                _logger.LogInformation("User {UserId} registered for inbound calls with status {Available}", 
                    request.UserId, request.Available);

                // In a production system, you would:
                // 1. Store user availability in Redis/Database
                // 2. Update user's call routing preferences
                // 3. Notify call distribution system

                return Ok(new { 
                    message = "Successfully registered for calls",
                    userId = request.UserId,
                    available = request.Available
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering user {UserId} for calls", request.UserId);
                return StatusCode(500, new { error = "Failed to register for calls" });
            }
        }

        private string GetAvailableAgent()
        {
            // TODO: Implement your logic to find available agents
            // This could check:
            // 1. Which users are currently online with WebRTC browsers
            // 2. Agent availability status
            // 3. Call queue assignments
            // 4. Load balancing logic
            
            // For now, return a sample agent identity
            // In production, this would query your user/agent management system
            return "agent-1"; // This should match the userId used when generating access tokens
        }

        private string GenerateTwilioAccessToken(string accountSid, string apiKey, string apiSecret, 
            string twimlAppSid, string identity)
        {
            // Create claims for the token
            var claims = new[]
            {
                new Claim("iss", apiKey),
                new Claim("sub", accountSid),
                new Claim("jti", $"{apiKey}-{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}"),
                new Claim("grants", JsonConvert.SerializeObject(new
                {
                    identity = identity,
                    voice = new
                    {
                        outgoing = new
                        {
                            application_sid = twimlAppSid
                        },
                        incoming = new { allow = true }
                    }
                }))
            };

            // Create signing key
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(apiSecret));
            var signingCredentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            // Create token
            var token = new JwtSecurityToken(
                issuer: apiKey,
                audience: accountSid,
                claims: claims,
                expires: DateTime.UtcNow.AddHours(24),
                signingCredentials: signingCredentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }
    }

    public class AccessTokenRequest
    {
        public string UserId { get; set; }
        public string DisplayName { get; set; }
    }

    public class RegisterForCallsRequest
    {
        public string UserId { get; set; }
        public bool Available { get; set; }
    }
}
