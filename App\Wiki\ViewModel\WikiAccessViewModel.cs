using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Wiki.Enums.Enums;

namespace Jobid.App.Wiki.ViewModel
{
    public class WikiAccessDto
    {
        [Required]
        public string UserId { get; set; }
        
        [Required]
        public WikiAccessStatus AccessStatus { get; set; }
    }

    public class WikiAccessBulkUpdateDto
    {
        [Required]
        public List<WikiAccessDto> AccessUpdates { get; set; }
    }

    public class WikiAccessViewModel
    {
        public Guid Id { get; set; }
        public string UserId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public WikiAccessStatus AccessStatus { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
