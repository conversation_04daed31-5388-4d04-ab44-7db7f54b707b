﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.Model
{
    public class Activity
    {
        [Key]
        public Guid Id { get; set; }
        public EventCategory EventCategory { get; set; } = EventCategory.Application;
        [Required]
        public string ActivitySummary { get; set; }
        public string Description { get; set; }
        public Applications? Application { get; set; }
        public string GenericUrl { get; set; }
        public string EventId { get; set; }
        public string By { get; set; }
        public string UserId { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigational Properties
        public ICollection<LogAttachment> LogAttachments { get; set; }

        public Activity()
        {
            Id = Guid.NewGuid();
            LogAttachments = new List<LogAttachment>();
        }
    }
}
