<div style="width: 595px; height: 842px; position: relative; background: white">
    <div style="width: 264px; height: 18px; left: 331px; top: 0px; position: absolute; background: #008CFE"></div>
    <div style="left: 32px; top: 344px; position: absolute; color: #828282; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">DATE</div>
    <div style="left: 97px; top: 344px; position: absolute; color: #828282; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">CREDIT</div>
    <div style="left: 162px; top: 344px; position: absolute; color: #828282; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">DEBIT</div>
    <div style="left: 219px; top: 344px; position: absolute; color: #828282; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">DESCRIPTION</div>
    <div style="left: 510px; top: 344px; position: absolute; color: #828282; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">BALANCE</div>
    <div style="width: 531px; height: 0px; left: 32px; top: 368px; position: absolute; border: 1.50px #008CFE solid"></div>
    <div style="left: 32px; top: 384px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 18px; display: inline-flex">
        <div style="width: 530px; height: 9px; position: relative">
            <div style="left: 0px; top: 0px; position: absolute; color: #1B1C1E; font-size: 13px; font-family: Circular Std; font-weight: 450; line-height: 18px; letter-spacing: 0.25px; word-wrap: break-word">15/2/24</div>
            <div style="left: 65px; top: 0px; position: absolute; color: #219653; font-size: 13px; font-family: Circular Std; font-weight: 500; line-height: 18px; letter-spacing: 0.25px; word-wrap: break-word">₦20,000 </div>
            <div style="left: 478px; top: 0px; position: absolute; text-align: right; color: #1B1C1E; font-size: 13px; font-family: Circular Std; font-weight: 500; line-height: 18px; letter-spacing: 0.25px; word-wrap: break-word">₦20,000 </div>
            <div style="left: 130px; top: 0px; position: absolute; color: #FF0000; font-size: 13px; font-family: Circular Std; font-weight: 500; line-height: 18px; letter-spacing: 0.25px; word-wrap: break-word">₦3,000 </div>
            <div style="left: 187px; top: 0px; position: absolute; color: #1B1C1E; font-size: 12px; font-family: Circular Std; font-weight: 450; line-height: 18px; letter-spacing: 0.25px; word-wrap: break-word">Babatunde/To SHODEINDE/0720212947</div>
        </div>
        <div style="width: 523px; height: 0px; border: 1px #E0E0E0 solid"></div>
    </div>
    <div style="left: 453px; top: 72px; position: absolute; justify-content: center; align-items: center; gap: 9px; display: inline-flex">
        <div style="width: 46px; height: 46px; padding-top: 7px; padding-bottom: 7.44px; padding-left: 13px; padding-right: 12.57px; background: #008CFE; border-radius: 999px; overflow: hidden; justify-content: center; align-items: center; display: flex">
            <div style="width: 20.43px; height: 31.56px; position: relative; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                <div style="width: 20.43px; height: 31.56px; border: 1.73px white solid"></div>
                <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 2.48px; display: inline-flex">
                    <div style="width: 12.22px; height: 9.07px; border: 1.73px white solid"></div>
                    <div style="width: 12.22px; height: 9.70px; position: relative">
                        <div style="width: 12.22px; height: 4.55px; left: 0px; top: -0px; position: absolute; border: 1.73px white solid"></div>
                        <div style="width: 2.38px; height: 3.75px; left: 2.72px; top: 4.02px; position: absolute; border: 1.73px white solid"></div>
                        <div style="width: 0.69px; height: 0.99px; left: 5.62px; top: 8.71px; position: absolute; border: 1.73px white solid"></div>
                    </div>
                </div>
            </div>
        </div>
        <div style="color: #008CFE; font-size: 14px; font-family: Ubuntu; font-weight: 500; line-height: 20px; letter-spacing: 0.25px; word-wrap: break-word">JobPays</div>
    </div>
    <div style="left: 32px; top: 175px; position: absolute; justify-content: flex-start; align-items: flex-start; gap: 82px; display: inline-flex">
        <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex">
            <div style="justify-content: center; align-items: flex-start; gap: 92px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Account Number</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">************</div>
            </div>
            <div style="width: 253px; height: 0px; border: 0.60px #E0E0E0 solid"></div>
            <div style="justify-content: center; align-items: flex-start; gap: 25px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Statement Period</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">15th Jan 23 - 20th March 24</div>
            </div>
            <div style="width: 253px; height: 0px; border: 0.60px #E0E0E0 solid"></div>
            <div style="justify-content: center; align-items: flex-start; gap: 122px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Money In</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">₦20,000,000.00</div>
            </div>
            <div style="width: 253px; height: 0px; border: 0.60px #E0E0E0 solid"></div>
            <div style="justify-content: center; align-items: flex-start; gap: 113px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Money Out</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">₦20,000,000.00</div>
            </div>
        </div>
        <div style="width: 196px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 10px; display: inline-flex">
            <div style="padding-right: 58px; justify-content: flex-start; align-items: flex-start; gap: 50px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Opening Balance</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">₦32,000.00</div>
            </div>
            <div style="width: 195px; height: 0px; border: 0.60px #E0E0E0 solid"></div>
            <div style="padding-right: 57px; justify-content: flex-start; align-items: flex-start; gap: 56px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Closing Balance</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">₦32,000.00</div>
            </div>
            <div style="width: 195px; height: 0px; border: 0.60px #E0E0E0 solid"></div>
            <div style="padding-right: 58px; justify-content: flex-start; align-items: flex-start; gap: 118px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Outstanding</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">₦0</div>
            </div>
            <div style="width: 195px; height: 0px; border: 0.60px #E0E0E0 solid"></div>
            <div style="padding-right: 58px; justify-content: flex-start; align-items: flex-start; gap: 118px; display: inline-flex">
                <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">Outstanding</div>
                <div style="text-align: right; color: #1B1C1E; font-size: 11px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">₦0</div>
            </div>
        </div>
    </div>
    <div style="left: 32px; top: 783px; position: absolute; color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; word-wrap: break-word">This is an automated transaction alert service. For <NAME_EMAIL> or call us on 98200191<br />or send an <NAME_EMAIL></div>
    <div style="left: 519px; top: 820px; position: absolute; color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 450; word-wrap: break-word">Page 1 of 12</div>
    <div style="left: 32px; top: 78px; position: absolute; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: inline-flex">
        <div style="color: black; font-size: 24px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">SHODEINDE BABATUNDE G</div>
        <div style="color: #807E7C; font-size: 11px; font-family: Circular Std; font-weight: 500; text-transform: capitalize; word-wrap: break-word">21 Thomas Jumanji Street, The Rock Avenue, Grace Estate Lekki Estate<br />Garden Lagos State</div>
    </div>
    <div style="left: 32px; top: 305px; position: absolute; color: black; font-size: 18px; font-family: Circular Std; font-weight: 700; word-wrap: break-word">Transactions</div>
</div>