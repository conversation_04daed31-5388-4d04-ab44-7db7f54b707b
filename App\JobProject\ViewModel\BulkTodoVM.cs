﻿using Jobid.App.Helpers.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.JobProject.ViewModel
{
    public class BulkTodoVM
    {
        public string SubDomain { get; set; }

        [Required]
        public string UserId { get; set; }
        public List<TodoDetails> TodoDetails { get; set; } = new List<TodoDetails> { };
    }

    public class TodoDetails
    {
        public string TodoSummary { get; set; }
        public string TodoDescription { get; set; }
        public List<string> AssignedTo { get; set; } = new List<string> { };
        public DateTime DueDate { get; set; }
        public DateTime StartDateAndTime { get; set; }
        public ProjectManagementPriority Priority { get; set; }
        public string TimeEstimate { get; set; }
        public bool CreatedByAI { get; set; }
        public ProjectManagementStatus? Status { get; set; }

        public List<string> TagIds { get; set; } = new List<string>();
    }
}
