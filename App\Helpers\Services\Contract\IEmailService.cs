﻿using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Contract
{
    public interface IEmailService
    {
        Task<bool> SendEmail(string body, string destination, string subject);
        Task<bool> SendEmail(string body, string destination, string subject, string senderEmail);
        Task<bool> SendMultipleEmail(string body, List<string> destination, string subject);
        Task<string> GetMeetingInviteMailTemplate();
        Task<string> GetMailTemplate();
        Task<string> GetJobPaysInviteMailTemplate();
        Task<string> GetJobPaysEmployeeWelcomeMailTemplate();
        Task<string> GetJobPaysWalletStatementMailTemplate();
        Task<string> GetExternalTeamMemberMailTemplate();
        Task<string> GetCrmEmailCampaign();
        Task<string> GetCrmEmailCampaignWithAttachment();
        Task<bool> SendEmailWithAttachments(string body, string destination, string subject, List<AttachmentDto> attachments = null);
    }
}
