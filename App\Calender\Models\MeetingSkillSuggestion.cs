using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace Jobid.App.Calender.Models
{
    /// <summary>
    /// Model for meeting skill suggestions
    /// </summary>
    public class MeetingSkillSuggestion
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// User ID who suggested the skills
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// Meeting ID the skills are suggested for
        /// </summary>
        public Guid MeetingId { get; set; }

        /// <summary>
        /// Comma-separated list of skills
        /// </summary>
        public string Skills { get; set; }

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Updated date
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// List of skills as a collection
        /// </summary>
        [NotMapped]
        public List<string> SkillsList
        {
            get
            {
                if (string.IsNullOrEmpty(Skills))
                    return new List<string>();
                return Skills.Split(',').ToList();
            }
        }
    }
}
