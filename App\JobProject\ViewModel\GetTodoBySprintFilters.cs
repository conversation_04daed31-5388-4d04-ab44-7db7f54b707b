﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using System;
using System.Collections.Generic;

namespace Jobid.App.JobProject.ViewModel
{
    public class GetTodoBySprintFilters
    {
        public string TodoStatus { get; set; }
        public List<string> TeamMemberIds { get; set; }
        public List<string> Tags { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public GetTodoBySprintFilters()
        {
            TeamMemberIds = new List<string>();
            Tags = new List<string>();
        }
    }
}
