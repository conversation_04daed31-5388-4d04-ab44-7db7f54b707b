using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Services;
using Serilog;
using ILogger = Serilog.ILogger;
using Jobid.App.Helpers.Contract;

namespace Jobid.App.AdminConsole.Controllers
{
    /// <summary>
    /// Unified telephony controller for all call management operations
    /// Orchestrates Twilio (PSTN), LiveKit (media), and AI service integration
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]    public class TelephonyController : ControllerBase
    {
        private readonly IUnitofwork _unitofwork;
        private ILogger _logger = Log.ForContext<PhoneNumberService>();

        public TelephonyController(
            IUnitofwork unitofwork)
        {
            _unitofwork = unitofwork;
        }

        #region Call Session Management
        
        /// <summary>
        /// Initiate an outbound call (UI -> Backend)
        /// Creates LiveKit room + <PERSON>wi<PERSON> call with media bridge
        /// </summary>        
        [HttpPost("initiate-call")]
        public async Task<IActionResult> InitiateCall([FromBody] InitiateCallSessionDto request)
        {
            // Extract subdomain from headers
            request.Subdomain = Request.Headers["subdomain"].ToString();

            var result = await _unitofwork.TelephonyService.InitiateCallAsync(request);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// Answer an incoming call (UI -> Backend)
        /// </summary>        
        [HttpPost("answer-call")]
        public async Task<IActionResult> AnswerCall([FromBody] AnswerCallDto request)
        {
            var result = await _unitofwork.TelephonyService.AnswerCallAsync(request);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// End a call session (UI -> Backend)
        /// </summary>        
        [HttpPost("end-call")]
        public async Task<IActionResult> EndCall([FromBody] EndCallDto request)
        {
            var result = await _unitofwork.TelephonyService.EndCallAsync(request);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region Participant Management
        
        /// <summary>
        /// Add participant to an ongoing call (UI -> Backend)
        /// </summary>        
        [HttpPost("add-participant")]
        public async Task<IActionResult> AddParticipant([FromBody] TelephonyAddParticipantDto request)
        {
            var result = await _unitofwork.TelephonyService.AddParticipantAsync(request);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// Remove participant from an ongoing call (UI -> Backend)
        /// </summary>        
        [HttpPost("remove-participant")]
        public async Task<IActionResult> RemoveParticipant([FromBody] TelephonyRemoveParticipantDto request)
        {
            var result = await _unitofwork.TelephonyService.RemoveParticipantAsync(request);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region Call Forwarding

        /// <summary>
        /// Forward an active call to another number/user (UI -> Backend)
        /// </summary>        
        [HttpPost("forward-call")]
        public async Task<IActionResult> ForwardCall([FromBody] ForwardCallDto request)
        {
            var result = await _unitofwork.TelephonyService.ForwardCallAsync(request);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region Media Bridge Management

        /// <summary>
        /// Get active media bridge sessions for monitoring
        /// </summary>        
        [HttpGet("bridge-sessions")]
        public async Task<IActionResult> GetActiveBridgeSessions()
        {
            var result = await _unitofwork.TelephonyService.GetActiveBridgeSessionsAsync();
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// Stop a specific media bridge session
        /// </summary>        
        [HttpPost("bridge-sessions/{sessionId}/stop")]
        public async Task<IActionResult> StopBridgeSession(string sessionId)
        {
            var result = await _unitofwork.TelephonyService.StopBridgeSessionAsync(sessionId);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region Webhook Handlers

        /// <summary>
        /// Handle Twilio call status webhooks
        /// </summary>        
        [HttpPost("webhook/twilio/call-status")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleTwilioCallStatus([FromForm] TwilioCallStatusDto request, [FromQuery] Guid callId, [FromQuery] string subdomain)
        {
            var result = await _unitofwork.TelephonyService.HandleTwilioCallStatusAsync(request, callId, subdomain);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region Room Management

        /// <summary>
        /// Get details of an active call session
        /// </summary>        
        [HttpGet("session/{sessionId}")]
        public async Task<IActionResult> GetCallSession(string sessionId)
        {
            var result = await _unitofwork.TelephonyService.GetCallSessionAsync(sessionId);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region Inbound Call Management

        /// <summary>
        /// Handle inbound call webhook from Twilio
        /// </summary>        
        [HttpPost("webhook/twilio/inbound-call")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleInboundCall([FromForm] string From, [FromForm] string To, [FromForm] string CallSid)
        {
            var twimlResponse = await _unitofwork.TelephonyService.HandleInboundCallAsync(From, To, CallSid);
            return Content(twimlResponse, "text/xml");
        }

        #endregion

        #region Call History and Analytics

        /// <summary>
        /// Get call history for a phone number
        /// </summary>        
        [HttpGet("call-history/{phoneNumberId}")]
        public async Task<IActionResult> GetCallHistory(Guid phoneNumberId, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            var result = await _unitofwork.TelephonyService.GetCallHistoryAsync(phoneNumberId, startDate, endDate);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// Get call recording
        /// </summary>        
        [HttpGet("recording/{callId}")]
        public async Task<IActionResult> GetCallRecording(string callId)
        {
            var result = await _unitofwork.TelephonyService.GetCallRecordingAsync(callId);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// Get call transcription
        /// </summary>        
        [HttpGet("transcription/{callId}")]
        public async Task<IActionResult> GetCallTranscription(string callId)
        {
            var result = await _unitofwork.TelephonyService.GetCallTranscriptionAsync(callId);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        /// <summary>
        /// Get call analytics for a phone number
        /// </summary>        
        [HttpGet("analytics/{phoneNumberId}")]
        public async Task<IActionResult> GetCallAnalytics(Guid phoneNumberId, [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            var result = await _unitofwork.TelephonyService.GetCallAnalyticsAsync(phoneNumberId, startDate, endDate);
            return StatusCode(Convert.ToInt32(result.ResponseCode), result);
        }

        #endregion

        #region TwiML Endpoints for Call Flow Management

        /// <summary>
        /// Handle dial result from Twilio webhook
        /// </summary>
        [HttpPost("webhook/twilio/dial-result")]
        [AllowAnonymous]
        public IActionResult HandleDialResult([FromForm] string DialCallStatus, [FromForm] string CallSid)
        {
            try
            {
                var twiml = _unitofwork.TelephonyService.HandleDialResult(DialCallStatus, CallSid, Request.Scheme, Request.Host.ToString());
                return Content(twiml, "text/xml");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error handling dial result for call {CallSid}", CallSid);
                var errorTwiML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Say>Technical error occurred.</Say><Hangup/></Response>";
                return Content(errorTwiML, "text/xml");
            }
        }

        /// <summary>
        /// Provide queue wait music for callers on hold
        /// </summary>
        [HttpPost("webhook/twilio/queue-wait-music")]
        [AllowAnonymous]
        public IActionResult QueueWaitMusic()
        {
            try
            {
                var twiml = _unitofwork.TelephonyService.GetQueueWaitMusic();
                return Content(twiml, "text/xml");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error providing queue wait music");
                var errorTwiML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Say>Please hold.</Say></Response>";
                return Content(errorTwiML, "text/xml");
            }
        }

        /// <summary>
        /// Handle voicemail recording from Twilio webhook
        /// </summary>
        [HttpPost("webhook/twilio/voicemail")]
        [AllowAnonymous]
        public IActionResult HandleVoicemail([FromForm] string RecordingUrl, [FromForm] string CallSid, [FromForm] string From, [FromForm] string To)
        {
            try
            {
                var twiml = _unitofwork.TelephonyService.HandleVoicemail(RecordingUrl, CallSid, From, To);
                return Content(twiml, "text/xml");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error handling voicemail for call {CallSid}", CallSid);
                var errorTwiML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Say>Message received. Goodbye.</Say><Hangup/></Response>";
                return Content(errorTwiML, "text/xml");
            }
        }

        /// <summary>
        /// Fallback endpoint for inbound calls when WebRTC agents are not available
        /// </summary>
        [HttpPost("webhook/twilio/inbound-fallback")]
        [AllowAnonymous]
        public IActionResult InboundCallFallback([FromForm] string From, [FromForm] string To, [FromForm] string CallSid)
        {
            try
            {
                var twiml = _unitofwork.TelephonyService.GetInboundCallFallback(From, To, CallSid, Request.Scheme, Request.Host.ToString());
                return Content(twiml, "text/xml");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error handling inbound call fallback for {CallSid}", CallSid);
                var errorTwiML = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say>We're experiencing technical difficulties. Please try again later.</Say>
    <Hangup/>
</Response>";
                
                return Content(errorTwiML, "text/xml");
            }
        }

        #endregion
    }
}
