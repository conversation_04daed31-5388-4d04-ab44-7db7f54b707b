﻿using Jobid.App.Helpers.Enums;
using System;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class CreateSubsidiaryCompanyDto
    {
        public string CompanyName { get; set; }
        public string Country { get; set; }
        public string FullAddress { get; set; }
        public string EmailAddress { get; set; }
        public string BranchColor { get; set; }
        public Guid ParentCompanyId { get; set; } //The id of the parent company
        public Industries Industry { get; set; }
    }
}
