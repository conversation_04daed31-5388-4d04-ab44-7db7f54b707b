using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.JobProject.ViewModel;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using static Jobid.App.Calender.ViewModel.HasMeetingHappenedDto;

namespace Jobid.App.Calender.Contracts
{
    public interface ICalenderService
    {
        Task<GenericResponse> GetUploadedDocument(string meetingId, string subsequentMeetingId);
        Task<GenericResponse> DeleteUploadedDocument(string meetingId, string fileName, string subsequentMeetingId, ReoccuringDeleteOptions? reoccuringDeleteOptions);
        Task<bool> UpdateExternalMeeting(string externalMeetingId, ExternalMeetingDto updateExternalMeeting);
        Task<CalenderMeeting> CreateMeeting(CalenderVm calenderVm);
        Task<List<CalenderMeeting>> GetCalenderById(GetUserScheduleVm model);
        Task<List<CalenderMeeting>> GetMostRecentCalenderById(GetUserScheduleVm model);
        Task<List<CalenderMeeting>> GetEventsByUserId(GetUserScheduleVm model);
        Task<GenericResponse> ProposeNewTimeForMeeting(ProposeNewDateAndTimeDto model, string host);
        Task<GenericResponse> AcceptOrRejectNewProposedDateOrTime(AcceptOrRejectNewProposedDateAndTimeDto model);
        Task<bool> RemoveMemeberFromCalenderByUserId(string userId, string calenderId, string loggedInUserId, string externalMemberEmail);
        Task<bool> AddMemberToCalender(AddMemberToMeeting request);
        Task<List<PersonalSchedule>> AddPersonalSchedule(List<PersonalScheduleDto> personalSchedule);
        Task<bool> UpdatePersonalSchedule(List<PersonalScheduleDto> personalSchedule);
        Task<GenericResponse> DeletePersonalSchedule(DeletePersonalScheduleDto model);
        Task<GenericResponse> UpdateBookedMeeting(UpdateBookedExtMeetingDto model);
        Task<List<PersonalSchedule>> GetPersonalScheduleByUserId(string userId);
        Task<GenericResponse> GetAILastHeldMeetingForReccuringMeeting(string meetingId);
        Task<List<PersonalSchedule>> GetPersonalSchedules(List<string> userIds);
        Task<bool> CancelMeeting(string meetingId, CancelMeetingVm model, string subdomain);
        Task<GenericResponse> GetExternalMeetingById(string id, string subdomain);
        Task<CalenderMeeting> GetMeetingById(string meetingId);
        Task<GenericResponse> GetUsersAvailability(GetUserAvailabilityDto model);
        Task<GenericResponse> GetMeetingByRTCId(string meetingId, bool basicDetails = true);
        Task<CalenderMeeting> GetMostRecentMeetingById(string meetingId);
        Task<ExternalMeeting> CreatExternalMeetingOrEvent(ExternalMeetingDto externalMeetingDto);
        Task<BookedExternalMeeting> BookExternalMeetingorEvent(BookExternalMeetingDto model);
        Task<CalenderMeeting> UpdateMeetingOrEvents(UpdateCalenderDto updateCalenderDto);
        Task<SubsequentMeeting> UpdateSubsequentMeeting(UpdateSubsequentMeetingDto updateCalenderDto);
        Task<GenericResponse> GetExternalMeetingOrEventByUserId(string userId);
        Task<List<BookedExternalMeeting>> GetBookedExternalMeetingOrEventByMeetingId(string meetingId);
        Task<bool> CancelExternalMeetingOrEvent(string externalMeetingId, string subdomain);
        Task<bool> ProcessMeetingResponse(MeetingResponseVm model);
        Task<GenericResponse> LockOrUnlockSelectedDate(LockOrUnlockSelectedDateDto model);
        Task<GenericResponse> GetAllMeetings(GetUsersMeetingVm model);
        Task<bool> ReScheduleBookedMeeting(string bookedExternalMeetingId, BookExternalMeetingDto model);
        Task<string> CancelBookedExternalMeeting(string meetingId, string reason, string subdoamin);
        Task<bool> LockExternalMeetingOrEvent(string meetingId, bool lockMeeting, string loggedInUserId);
        Task<GenericResponse> GetAllExternalMeetingOrEvent();
        Task<List<BookedExternalMeeting>> GetBookedExternalMeetingByUserId(string userId);
        Task<BookedExternalMeeting> GetBookedExternalMeetingById(string bookedExternalMeetingId);
        Task<GenericResponse> GetAllBookedExternalMeetingOrEvent();
        Task<List<CalenderMeeting>> SearchUserCalenderToGetMeeting(SearchUserCalenderToGetMeetingDto searchUserCalender);
        Task<string> MeetingRecordingNotification(MeetingRecordingNotificationDto recordingNotificationDto);
        Task<GenericResponse> MarkMeetingAsHappened(string meetingId);
        Task<List<string>> UploadDocument(UploadDocumentDto asset);

        // HTML to PDF conversion
        Task<GenericResponse> ConvertHtmlToPdf(ViewModel.HtmlToPdfViewModel model);

        // AI Related methods
        Task<GenericResponse> GetMeetingsThatHappened(SalesActivityParams model);
        Task<GenericResponse> GetMeetingRecordingUrl(string subDomain, string meetingId);

        // Meeting Notes
        Task<GenericResponse> DownloadMeetingNotesAsPDF(string meetingNoteId);
        Task<GenericResponse> AddOrUpdateMeetingNotes(AddMeetingNotesDto model);
        Task<GenericResponse> GetMeetingNotes(string rtcMeetingId, string userId, Guid? subSequentMeetingId);
        Task<GenericResponse> DeleteMeetingNotes(Guid meetingNotesId, CancellationToken cancellationToken = default);
        Task<GenericResponse> SendMeetingNotesAsEmail(SendMeetingNotesDto model);

        // Test methods
        Task TestRescheduleMeetingBK();
    }
}
