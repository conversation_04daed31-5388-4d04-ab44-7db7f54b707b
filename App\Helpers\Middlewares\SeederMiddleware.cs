﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using static Jobid.App.Helpers.Utils.Utility;

namespace Jobid.App.Helpers.Middlewares
{
    public static class SeederMiddleware
    {
        public static async void UseSeedData(this IApplicationBuilder app)
        {
            var subdomains = GlobalVariables.Subdomains;
            if (subdomains == null)
            {
                subdomains = new List<string>();
            }

            //_logger.LogInformation("Starting seeding on app start-up with subdomains: {Subdomains}", subdomains);

            List<string> applicationNames = new List<string>(Enum.GetNames(typeof(Applications)));
            subdomains.Add(Constants.PUBLIC_SCHEMA);

            foreach (var subdomain in subdomains)
            {
                if (subdomain == Constants.PUBLIC_SCHEMA)
                {
                    await DataSeeder.SeedData(subdomain, null, true);
                }
                else
                {
                    foreach (var application in applicationNames)
                    {
                        await DataSeeder.SeedData(subdomain, application, false);
                    }
                }
            }
        }
    }

}
