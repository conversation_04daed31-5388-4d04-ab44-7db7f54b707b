﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class addedfewmeetingrelatdtbls : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public addedfewmeetingrelatdtbls(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "SubsequentMeetingId",
                schema: _Schema,
                table: "UserIdMeetingIds",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "BookedMeetingMembers",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    BookedMeetingId = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<string>(type: "text", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    NotifyMeInMinutes = table.Column<int>(type: "integer", nullable: false),
                    InviteResponse = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BookedMeetingMembers", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BookedMeetingMembers",
                schema: _Schema);

            migrationBuilder.DropColumn(
                name: "SubsequentMeetingId",
                schema: _Schema,
                table: "UserIdMeetingIds");
        }
    }
}
