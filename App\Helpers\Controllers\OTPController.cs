﻿#region Using Statements
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.ViewModel;
using Serilog;
using Utility = Jobid.App.Helpers.Utils.Utility;
using Jobid.App.Helpers.Exceptions;
#endregion

namespace Jobid.App.Helpers.Controllers
{
    [Route("api/[controller]")]
    [Produces("Application/json")]
    [ApiController]

    public class OTPController : ControllerBase
    {
        private readonly IUnitofwork Services_Repo;
        private UserManager<User> UserManager;
        private ILogger _logger = Log.ForContext<OTPController>();

        public OTPController(IUnitofwork unitofwork, UserManager<User> _usermanager)
        {
            this.Services_Repo = unitofwork;
            this.UserManager = _usermanager;
        }

        #region Verify Token/OTP
        /// <summary>
        /// Verify Token/OTP
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("VerifyToken")]
        public async Task<IActionResult> VerifyToken([FromBody] VerifyTokenOTPVM model)
        {
            try
            {
                var result = await Services_Repo.OPTService.VerifyToken(model);
                if (result.ResponseCode == "200")
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Token Verified Successfully.",
                        ResponseCode = "200",
                        Data = result.Data
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Failed to verify token, the token passed is incorrect.",
                        ResponseCode = "500",
                        Data = false
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), "Error occured while verifying token.");
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = false
                });
            }

        }
        #endregion

        #region Generate OTP
        /// <summary>
        /// Send OTP
        /// </summary>
        /// <param name="model"></param>
        /// <param name="tokenType"></param>
        /// <param name="Length"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("GenerateToken")]
        public async Task<IActionResult> GenerateToken([FromBody] GenerateOTPVM model, [FromQuery]OTPTokenType tokenType, [FromQuery] int Length = 4)
        {
            try
            {
                var result = await Services_Repo.OPTService.GenerateToken(new NewOTPVM {
                    Identifier = model.Identifier,
                    IdentifierType=model.IdentifierType,
                    TokenType=tokenType,
                    IsWhatsAppNo = model.IsWhatsAppNo,
                    ForCheckingIfUserExists = model.ForCheckingIfUserExists,
                }, Length, tokenType);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Token Sent.",
                        ResponseCode = "201",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Failed to generate token, please try again.",
                        ResponseCode = "500",
                        Data = false
                    });
                }
            }
            catch (RecordAlreadyExistException ex)
            {
                _logger.Error(ex.Message, nameof(GenerateToken));
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message, nameof(GenerateToken));
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = false
                });
            }

        }
        #endregion
    }
}