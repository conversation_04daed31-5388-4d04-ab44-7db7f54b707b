using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.Wiki.Enums;
using Jobid.App.Wiki.Models;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.Wiki.ViewModel;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.Wiki.Enums.Enums;

namespace Jobid.App.Wiki.Services.Implementations
{
    public class WikiAccessService : IWikiAccessService
    {
        private readonly JobProDbContext _db;
        
        public WikiAccessService(JobProDbContext db)
        {
            _db = db;
        }

        #region UpdateWikiAccess
        /// <summary>
        /// Update wiki access for multiple users
        /// </summary>
        /// <param name="accessUpdates">List of user IDs and their access status</param>
        /// <returns>Success status</returns>
        public async Task<GenericResponse> UpdateWikiAccess(List<WikiAccessDto> accessUpdates)
        {
            if (accessUpdates == null || !accessUpdates.Any())
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "No access updates provided"
                };
            }

            foreach (var update in accessUpdates)
            {
                // Check if user exists
                var userExists = await _db.UserProfiles.FirstOrDefaultAsync(u => u.UserId == update.UserId);
                if (userExists == null)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "404",
                        DevResponseMessage = $"User with ID {update.UserId} does not exist",
                        ResponseMessage = "Something went wrong, please try again later"
                    };
                }

                // Check if user already has wiki access entry
                var existingAccess = await _db.Set<WikiAccess>()
                    .FirstOrDefaultAsync(w => w.UserId == update.UserId);

                if (existingAccess != null)
                {
                    // Update existing access
                    existingAccess.AccessStatus = update.AccessStatus;
                    existingAccess.UpdatedAt = DateTime.UtcNow;
                    _db.Set<WikiAccess>().Update(existingAccess);
                }
                else
                {
                    // Create new access entry
                    var newAccess = new WikiAccess
                    {
                        UserId = update.UserId,
                        AccessStatus = update.AccessStatus,
                        CreatedAt = DateTime.UtcNow
                    };
                    await _db.Set<WikiAccess>().AddAsync(newAccess);
                }

                // Log the user out if access is NoAccess
                if (update.AccessStatus == WikiAccessStatus.NoAccess)
                {
                    userExists.UserLoggedOut = true;
                    await _db.SaveChangesAsync();
                }
            }

            await _db.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Wiki access updated successfully"
            };
        }
        #endregion

        #region Get All Team Members With There Wiki Access
        /// <summary>
        /// Get all team members with their wiki access status
        /// </summary>
        /// <param name="filter">Filter by access status (All, Access, NoAccess)</param>
        /// <param name="paginationParameters">Pagination parameters</param>
        /// <returns>List of team members with their wiki access status</returns>
        public async Task<GenericResponse> GetAllTeamMembersWithWikiAccess(string filter, PaginationParameters paginationParameters)
        {
            // Get all users
            var usersQuery = _db.UserProfiles.AsQueryable();

            // Join with wiki access information
            var query = from user in usersQuery
                        join wikiAccess in _db.Set<WikiAccess>()
                            on user.UserId equals wikiAccess.UserId into wikiJoin
                        from wiki in wikiJoin.DefaultIfEmpty()
                        select new WikiAccessViewModel
                        {
                            Id = wiki != null ? wiki.Id : Guid.Empty,
                            UserId = user.UserId,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            Email = user.Email,
                            AccessStatus = wiki != null ? wiki.AccessStatus : WikiAccessStatus.NoAccess,
                            CreatedAt = wiki != null ? wiki.CreatedAt : DateTime.MinValue,
                            UpdatedAt = wiki != null ? wiki.UpdatedAt : null
                        };

            // Apply filter
            if (!string.IsNullOrEmpty(filter))
            {
                if (filter == WikiAccessFilterEnum.Access)
                {
                    query = query.Where(w => w.AccessStatus == WikiAccessStatus.Access);
                }
                else if (filter == WikiAccessFilterEnum.NoAccess)
                {
                    query = query.Where(w => w.AccessStatus == WikiAccessStatus.NoAccess);
                }
                // If filter is "All" or invalid, return all records
            }

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var pagedData = await query
                .Skip((paginationParameters.PageNumber - 1) * paginationParameters.PageSize)
                .Take(paginationParameters.PageSize)
                .ToListAsync();

            // Create pagination metadata
            var paginationMetadata = new Jobid.App.Helpers.Utils.PaginationMetadata
            {
                TotalCount = totalCount,
                PageSize = paginationParameters.PageSize,
                CurrentPage = paginationParameters.PageNumber,
                TotalPages = (int)Math.Ceiling(totalCount / (double)paginationParameters.PageSize)
            };

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Team members with wiki access retrieved successfully",
                Data = new { Items = pagedData, Pagination = paginationMetadata }
            };
        }
        #endregion

        #region Check if a user has access to wiki
        public async Task<bool> HasAccessToWiki(string userId)
        {
            if (string.IsNullOrEmpty(userId)) return false;

            // Check if the user has a wiki access entry
            var wikiAccess = await _db.WikiAccesses
                .FirstOrDefaultAsync(w => w.UserId == userId);

            // Return true if access status is Access, otherwise false
            return wikiAccess != null && wikiAccess.AccessStatus == WikiAccessStatus.Access;
        }
        #endregion
    }
}
