﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.SchemaTenant;
using Jobid.App.Tenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Security.Claims;

namespace Jobid.App.Helpers.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    public sealed class PackageSubscriptionAndPermissionAuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        private readonly Applications _allowedApp;

        public PackageSubscriptionAndPermissionAuthorizeAttribute(Applications allowedApp)
        {
            _allowedApp = allowedApp;
        }

        public PackageSubscriptionAndPermissionAuthorizeAttribute() { }

        /// <summary>
        /// JobProject Authorize Attribute
        /// </summary>
        /// <param name="context"></param>
        /// <exception cref="NotImplementedException"></exception>
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            // Skip authorization if action is decorated with [AllowAnonymous] attribute
            if (context.ActionDescriptor.EndpointMetadata
                .Any(item => item.GetType().Equals(typeof(AllowAnonymousAttribute))))
            {
                return;
            }

            // Get 'aaplication' header value from the header and allow if its 'Echo'
            var application = context.HttpContext.Request.Headers["application"].ToString();
            if (application == Applications.Echo.ToString() || application == Applications.Echo.ToString().ToLower())
            {
                return;
            }

            // Check user claims from token
            var claims = context.HttpContext.User.Claims.ToList();

            // Check if serviceId is not null
            var serviceId = claims.FirstOrDefault(x => x.Type == "serviceId")?.Value;
            if (!string.IsNullOrEmpty(serviceId))
            {
                return;
            }

            var userId = claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                context.Result = new JsonResult(new
                {
                    Status = "Failed",
                    Message = "Unauthorized User"
                })
                { StatusCode = 401 };
                return;
            }

            // Get the required services
            var tenantConfig = context.HttpContext.RequestServices.GetService(typeof(ITenantConfig<JobProDbContext>)) as ITenantConfig<JobProDbContext>;
            var tenantSchema = context.HttpContext.RequestServices.GetService(typeof(ITenantSchema)) as ITenantSchema;
            var httpContextAccessor = context.HttpContext.RequestServices.GetService(typeof(IHttpContextAccessor)) as IHttpContextAccessor;

            //var _host = tenantSchema.ExtractSubdomainFromRequest(httpContextAccessor.HttpContext);
            var DbCon = tenantConfig.getRequestContext(httpContextAccessor.HttpContext);
            var publicContext = new JobProDbContext(new DbContextSchema());
            var subdomain = tenantConfig.getSubdomainName(httpContextAccessor.HttpContext);

            var companyId = publicContext.Tenants.Where(x => x.Subdomain == subdomain).Select(c => c.Id).FirstOrDefaultAsync().Result;

            // Check if the application the company of the user is Echo and if its Echo, allow the user to continue
            var isCompanyEcho = publicContext.CompanySubscriptions
                .AnyAsync(x => x.TenantId == companyId && x.Application == Applications.Echo && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active).Result;
            if (isCompanyEcho)
            {
                return;
            }

            // First check if the company has an active subscription
            // Check if the company is still on free trial
            var company = publicContext.Tenants.FirstOrDefaultAsync(x => x.Id == companyId).Result;
            var companyPermissionId = publicContext.CompanySubscriptions
                .Where(x => x.TenantId == companyId && x.Application == _allowedApp && x.Status == Subscription.Enums.Enums.SubscriptionStatus.Active).Select(sub => sub.Id.ToString()).FirstOrDefaultAsync().Result;
            if (companyPermissionId is null && subdomain != "api")
            {
                if (company == null)
                {
                    context.Result = new JsonResult(new
                    {
                        Status = false,
                        Message = "Company not found"
                    })
                    { StatusCode = 401 };
                    return;
                }

                context.Result = new JsonResult(new
                {
                    Status = false,
                    Message = $"Company does not have an active subscription for {_allowedApp}. Please contact admin"
                })
                { StatusCode = 401 };
                return;
            }

            // Fetch logged in user app permissions
            var userPermissions = DbCon.AppPermissions
                .Where(x => x.UserId == userId && x.IsEnabled == true && x.Application == _allowedApp.ToString() 
                && x.SubscriptionStatus == Subscription.Enums.Enums.SubscriptionStatus.Active.ToString()).FirstOrDefaultAsync().Result;

            if (userPermissions == null)
            {
                // User not authorized to access JobProject
                context.Result = new JsonResult(new
                {
                    Status = false,
                    Message = $"You do not have access to {_allowedApp}. Please contact your admin"
                })
                { StatusCode = 401 };
                return;
            }

            // Check if the user is suspended
            if (userPermissions != null && userPermissions.IsSuspended)
            {
                context.Result = new JsonResult(new
                {
                    Status = false,
                    Message = $"Your account is suspended. Please contact your admin"
                })
                { StatusCode = 401 };
                return;
            }
        }
    }
}
