using Jobid.App.Helpers.Enums;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Helpers.Models
{
    [Index(nameof(Email), IsUnique = true)]
    public class User : IdentityUser
    {
        [Required]
        public string FirstName { get; set; }
        public string JobProId { get; set; }

        [Required]
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string PhoneCountryCode { get; set; }
        public DateTime Created_At { get; set; }
        public string ForgotPasswordToken { get; set; }
        public DateTime? ForgotPasswordTokenExpiry { get; set; }
        public string EmailVerificationToken { get; set; }
        public DateTime? EmailVerificationTokenExpiry { get; set; }
        public string ProfileImageUrl { get; set; }
        public string ProofOfResidence { get; set; }
        public string GovernmentId { get; set; }
        public string Gender { get; set; }
        public string Status { get; set; }
        public string DateOfBirth { get; set; }
        public string StatusComment { get; set; }
        public string InvitedBy { get; set; } 
        public DateTime Modified_At { get; set; }
        public string CV_URL { get; set; }
        public string OldReference { get; set; }
        public string NewReference { get; set; }
        public bool SendMail { get; set; }
        public bool PasswordCreatedByAdmin { get; set; }
        public string CompanyId { get; set; }
        public string UserInCompanyRole { get; set; }
        public bool IsVerified { get; set; }
        public bool IsEmailVerified { get; set; }
        public bool IsPhoneNumberVerified { get; set; }
        public string PhoneNumberVerificationToken { get; set; }
        public DateTime? PhoneNumberVerificationTokenExpiry { get; set; }
        public string Region { get; set; }
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Country { get; set; }
        public string TimeZone { get; set; }
        public string State { get; set; }
        public string? City { get; set; }
        public string? HouseNo { get; set; }
        public string JobPaysPin { get; set; }
        public string? WeavrPasscode { get; set; }
        public string? WeavrId { get; set; }
        public bool WeavrPasscodeForPublic { get; set; }
        public string BaseCurrency { get; set; }
        public UserTypes UserType { get; set; } = UserTypes.IndividualUser;
        public string UpdatedBy { get; set; }
        public Guid? ClientRoleId { get; set; }
        public ClientRole? ClientRole { get; set; }
        public string IpAddress { get; set; }
        public bool lockIpAddress { get; set; }
        public string Profession { get; set; }
        public string GoogleAuthId { get; set; }
        public string MicrosoftAuthId { get; set; }
        public string SecondaryEmail { get; set; }
        public string SecondaryPhoneNumber { get; set;}
        public string ReferralCode { get; set; }
        public IndividualUserAccountStatus IndividualUserAccountStatus { get; set; } = IndividualUserAccountStatus.Active;
        public List<UserAndRoleId> UserEmployeeAppRole { get; set; } = new List<UserAndRoleId>();

        // Not mapped fields
        [NotMapped]
        public UserCompanies UserCompany { get; set; }
    }
}