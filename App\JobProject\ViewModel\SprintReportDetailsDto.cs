﻿using Jobid.App.JobProjectManagement.ViewModel;
using System.Collections.Generic;
using System;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.JobProject.ViewModel
{
    public class SprintReportDetailsDto
    {
        public string Id { get; set; }
        public string SprintName { get; set; }
        public string Status { get; set; }
        public string Duration { get; set; }
        public string totalTimeTracked { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<UserDto> TeamMembers { get; set; } = new List<UserDto>();
        public string Summary { get; set; }
        public string Description { get; set; }
        public List<string> ProjectFilesUrls { get; set; } = new List<string>();
        public SprintAnalyticsDto SprintAnalytics { get; set; }
        public Page<ProjectMgmt_Todo> TodoSummary { get; set; }
    }
}
