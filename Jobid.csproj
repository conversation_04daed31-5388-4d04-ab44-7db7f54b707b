﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <GenerateProgramFile>false</GenerateProgramFile>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <NoWarn>1701;1702,1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <NoWarn>1701;1702,1591</NoWarn>
  </PropertyGroup>

  <PropertyGroup>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="appsettings.Development.json" />
    <Content Remove="wwwroot\EmailTemplates\free-trial-ended-email.html" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Protos\Server\authenticate.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNet.Security.OAuth.LinkedIn" Version="5.0.16" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.2" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.9.39" />
    <PackageReference Include="AWSSDK.SimpleNotificationService" Version="3.7.300.52" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.300.52" />
    <PackageReference Include="BarCode" Version="2023.6.1" />
    <PackageReference Include="ClosedXML" Version="0.102.0" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
    <PackageReference Include="DinkToPdf" Version="1.0.8" />
    <PackageReference Include="Elastic.Serilog.Sinks" Version="8.12.0" />
    <PackageReference Include="EPPlus.Core" Version="1.5.4" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.57.0" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="3.7.0" />
    <PackageReference Include="Google.Protobuf" Version="3.24.3" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.56.0" />
    <PackageReference Include="Grpc.Net.Client" Version="2.56.0" />
    <PackageReference Include="Grpc.Tools" Version="2.56.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Hangfire" Version="1.7.33" />
    <PackageReference Include="Hangfire.Dashboard.Basic.Authentication" Version="5.0.0" />
    <PackageReference Include="Hangfire.PostgreSql" Version="1.8.6" />
    <PackageReference Include="HtmlRenderer.WinForms" Version="1.5.0.6" />
    <PackageReference Include="Ical.Net" Version="4.2.0" />
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="MailKit" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNet.Mvc" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="5.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="5.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="5.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.JsonPatch" Version="7.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="5.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="5.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="5.0.11">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="5.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="5.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="5.0.11">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Graph" Version="5.0.0-rc.5" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.1" />
    <PackageReference Include="Microsoft.Playwright" Version="1.52.0" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2903.40" />
	<PackageReference Include="Mollie.Api" Version="2.3.0.1" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="NAudio.Core" Version="2.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="4.7.12" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="5.0.10" />
    <PackageReference Include="Otp.NET" Version="1.4.0" />
    <PackageReference Include="PuppeteerSharp" Version="20.0.5" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="QuestPDF" Version="2024.12.3" />
    <PackageReference Include="RabbitMQ.Client" Version="6.0.0" />
    <PackageReference Include="RestSharp" Version="110.2.0" />
    <PackageReference Include="Select.HtmlToPdf" Version="24.1.0" />
    <PackageReference Include="Selenium.WebDriver" Version="4.27.0" />
    <PackageReference Include="SendGrid" Version="9.28.1" />
    <PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.2.0" />
    <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageReference Include="Serilog.Sinks.Debug" Version="2.0.0" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="2.88.8" />
    <PackageReference Include="SlackAPI" Version="1.1.14" />
    <PackageReference Include="StackExchange.Redis" Version="2.6.116" />
    <PackageReference Include="Stripe.net" Version="42.7.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="7.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.6.2" />
    <PackageReference Include="Syncfusion.Pdf.Net.Core" Version="28.1.35" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.3.2" />    <PackageReference Include="Twilio" Version="7.2.2" />
    <PackageReference Include="Twilio.AspNet.Common" Version="8.0.2" />

    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.35.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.35.0" />
    <PackageReference Include="WatchDog.NET" Version="1.4.12" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="ZKWeb.System.Drawing" Version="4.0.1" />
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Include="wwwroot\EmailTemplates\JobPaysEmail.html" />
    <None Include="wwwroot\EmailTemplates\free-trial-ended-email.html" />
    <None Include="wwwroot\EmailTemplates\verify-domain-notification.html" />
    <None Include="wwwroot\EmailTemplates\suspend-employee.html" />
    <None Include="wwwroot\EmailTemplates\decline-meeting-email.html" />
    <None Include="wwwroot\EmailTemplates\comment.html" />
    <None Include="wwwroot\EmailTemplates\trigger-template.html" />
    <None Include="wwwroot\EmailTemplates\external-meeting-addition.html" />
    <None Include="wwwroot\EmailTemplates\free-plan-notification.html" />
    <None Include="wwwroot\EmailTemplates\maybe-meeting-email.html" />
    <None Include="wwwroot\EmailTemplates\meeting-email.html" />
    <None Include="wwwroot\EmailTemplates\meeting-response-email.html" />
    <None Include="wwwroot\EmailTemplates\external-meeting-invitation.html" />
    <None Include="wwwroot\EmailTemplates\otp.html" />
    <None Include="wwwroot\EmailTemplates\paid-plan-notofication.html" />
    <None Include="wwwroot\EmailTemplates\sprint-external-invited-email.html" />
    <None Include="wwwroot\EmailTemplates\sprint-internal-invited-email.html" />
    <None Include="wwwroot\EmailTemplates\team-invite-email.html" />
    <None Include="wwwroot\EmailTemplates\team-member-added-email.html" />
    <None Include="wwwroot\EmailTemplates\todo\todo-internal-invited-email.html" />
    <None Include="wwwroot\EmailTemplates\todo-invited-email.html" />
    <None Include="wwwroot\EmailTemplates\welcome_email_jobpays.html" />
    <None Include="wwwroot\EmailTemplates\welcome_email_admin.html" />
    <None Include="wwwroot\EmailTemplates\welcome_email.html" />
    <None Include="wwwroot\EmailTemplates\zarttech-invited-email.html" />
    <None Include="wwwroot\EmailTemplates\meeting_template.html" />
    <None Include="wwwroot\EmailTemplates\WalletStatement.html" />
	<None Include="wwwroot\EmailTemplates\verify-domain-notification.html" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\GrpcCommunicationServices.proto" GrpcServices="Client" />
    <Protobuf Include="Protos\Server\authenticate.proto" GrpcServices="Server" />
    <Protobuf Include="Protos\Server\subscription.proto" GrpcServices="Server" />
    <Protobuf Include="Protos\Server\project.proto" GrpcServices="Server" />
  </ItemGroup>

	<ItemGroup>
		<None Update="DinkToPdf\libwkhtmltox.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>