using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Utils.Attributes;
using Microsoft.EntityFrameworkCore;
using static Jobid.App.Helpers.Utils.Utility;

namespace Jobid.App.Helpers.Models
{
    [Index(propertyNames: nameof(Email), IsUnique = false)]
    public class CompanyUserInvite
    {
        [Key]
        public string Id { get; set; }

        [ValidEmailCheck]
        public string Email { get; set; }
        public DateTime DateCreated { get; set; }
        public string Status { get; set; }
        public DateTime LastUpdate { get; set; }
        public string InviteCode { get; set; }
        public string TenantId { get; set; }
        public Applications Application { get; set; }

        public CompanyUserInvite(bool deleteCache)
        {
            if (deleteCache)
            {
                // Clear the reddis cahce for client-admin key
                var redisKey = $"{GlobalVariables.Subdomain}-client-admin";
                var todoCacheKeys = new List<string>();
                if (GlobalVariables.CacheKeys.ContainsKey(redisKey))
                    todoCacheKeys = GlobalVariables.CacheKeys[redisKey];

                // Remove the key from cache
                if (todoCacheKeys.Count > 0)
                {
                    foreach (var key in todoCacheKeys)
                    {
                        var redisRes = GlobalVariables.RedisCacheService.RemoveDataAsync(key).Result;
                    }
                }
            }
        }

        public CompanyUserInvite()
        {
            
        }
    }
}
