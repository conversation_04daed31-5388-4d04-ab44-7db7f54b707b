﻿using Jobid.App.Helpers.Utils.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class ExternalMember
    {
        [CanInviteExternalUser(Helpers.Enums.Applications.Joble)]
        public List<string> Emails { get; set; } = new List<string>();

        [JsonIgnore]
        public string UserId { get; set; }

        [JsonIgnore]
        public string SprintId { get; set; }
        [JsonIgnore]
        public string SubDomain { get; set; }
    }
}
