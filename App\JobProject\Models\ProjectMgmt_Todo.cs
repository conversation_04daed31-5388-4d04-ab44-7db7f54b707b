﻿using Jobid.App.Helpers.Enums;
using Jobid.App.JobProject.Models;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Helpers.Models
{
    public class ProjectMgmt_Todo
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();
        public string TodoId { get; set; }
        public string TodoName { get; set; }
        public string TodoDescription { get; set; }
        public string TodoSummary { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdate { get; set; }
        public DateTime? DueDate { get; set; }
        public Guid? ProjectMgmt_ProjectId { get; set; }
        public ProjectMgmt_Project? projectMgmt_Project { get; set; }
        public ICollection<ProjectMgmt_TodoUser> ProjectMgmt_TodoUsers { get; set; }
        public ICollection<User> Members { get; set; }

        [Column(TypeName = "varchar(255)")]
        public ProjectManagementPriority Priority { get; set; }
        public string TodoStatus { get; set; }
        public DateTime StartDateAndTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Duration { get; set; }
        public string WillBeDueIn { get; set; }
        public string ExistingTodoLink { get; set; }
        public string TimeSpent { get; set; }
        public string TimeLeft { get; set; }
        public string ActualTimeSpent { get; set; } = "0.00:00:00";
        public bool IsBillable { get; set; }

        [Column(TypeName = "varchar(255)")]
        public ApprovalStatus ApprovalStatus { get; set; } = ApprovalStatus.Pending;
        public bool LockTodo { get; set; } = false;
        public decimal? AmountPerHour { get; set; }
        public bool IsArchived { get; set; } = false;
        public Guid? SprintProjectId { get; set; }
        public SprintProject? SprintProject { get; set; }
        public Applications Application { get; set; }
        public string ClientName { get; set; }
        public DateTime? CompletedAt { get; set; }
        public long? KpiReferenceId { get; set; }
        public long? CompanyReferenceId { get; set; }
        public long? LeadReferenceId { get; set; }
        public long? DealReferenceId { get; set; }
        public long? ContactReferenceId { get; set; }
        public bool IsMeasurable { get; set; }
        public bool CreatedByAI { get; set; }
        public bool HasCustomFrequency { get; set; }
        public string TempCreationId { get; set; }

        [NotMapped]
        public TodoCustomFrequency? TodoCustomFrequency { get; set; }

        [NotMapped]
        public UserDto AssignedTo { get; set; }
        public ICollection<ProjectTag> ProjectTags { get; set; }
        public ICollection<ProjectFile> ProjectFiles { get; set; }
        public TodoOrder? TodoOrder { get; set; }
        public ICollection<TodoTimeSequence> TodoTimeSequence { get; set; }
        public ICollection<TodoComments> TodoComments { get; set; }

    }

    public enum TodoStatus
    {
        InProgress,
        Pending,
        OverDue,
        Completed,
        Todo
    }
}
