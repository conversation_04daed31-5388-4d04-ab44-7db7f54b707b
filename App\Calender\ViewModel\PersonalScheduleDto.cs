﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.Calender.ViewModel
{
    public class PersonalScheduleDto
    {
        public string Id { get; set; }

        [Required(ErrorMessage = "Day cannot be null")]
        public string Day { get; set; }

        [Required(ErrorMessage = "ScheduleName cannot be null")]
        public PersonalScheduleType ScheduleName { get; set; } = PersonalScheduleType.Work;

        public bool Available { get; set; } = true;

        public bool ScheduleIsPublic { get; set; } = false;

        public bool TeamMatesCanSee { get; set; } = false;

        public DateTime StartTime { get; set; }

        public DateTime EndTime { get; set; }

        public bool AvailableForMeetingsEmergencyHours { get; set; } = false;

        public bool AvailableForTasksEmergencyHours { get; set; } = false;

        public bool AvailableForSprintsEmergencyHours { get; set; } = false;


        [Required(ErrorMessage = "TimeZone cannot be null")]
        public string TimeZone { get; set; }

        public Guid UserId { get; set; }

        [JsonIgnore]
        public User User { get; set; }

        // ExternalMeetingId - required when ScheduleName is Custom
        public string MeetingId { get; set; }
    }
}
