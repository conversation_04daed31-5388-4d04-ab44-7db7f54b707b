-- Migration: Add attachment file fields to Groups table
-- This adds optional file attachment support to contact groups

ALTER TABLE "Groups" 
ADD COLUMN "AttachmentFileName" VARCHAR(500) NULL,
ADD COLUMN "AttachmentAwsKey" VARCHAR(500) NULL,
ADD COLUMN "AttachmentFileSize" BIGINT NULL,
ADD COLUMN "AttachmentFileType" VARCHAR(100) NULL;

-- Add comments for documentation
COMMENT ON COLUMN "Groups"."AttachmentFileName" IS 'Original name of the uploaded attachment file';
COMMENT ON COLUMN "Groups"."AttachmentAwsKey" IS 'AWS S3 key for the attachment file';
COMMENT ON COLUMN "Groups"."AttachmentFileSize" IS 'Size of the attachment file in bytes';
COMMENT ON COLUMN "Groups"."AttachmentFileType" IS 'MIME type of the attachment file';
