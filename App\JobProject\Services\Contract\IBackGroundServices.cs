using System.Collections.Generic;
using System.Threading.Tasks;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface IBackGroundServices
    {
        void DisapperaReadNotificationsAfter24Hours(List<string> subdomains);
        void DeleteNotificationsAfterSomeDays(List<string> subdomains);
        void UpdateTodoStatusToOverDue(List<string> subdomains);
        void UpdateProjectStatusToOverDue(List<string> subdomains);
        Task UpdateTenantProjectView(List<string> subdomains);
        Task UpdateProjectMetricsViews(List<string> subdomains);
        void UpdateSprintStatusToOverDue(List<string> subdomains);
        Task UpdateActivityScoreTable(List<string> subdomains);
    }
}
