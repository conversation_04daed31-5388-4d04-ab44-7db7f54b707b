# -------------------
# Build Stage
# -------------------
FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build-env
WORKDIR /src

COPY ./Jobid.csproj ./
RUN dotnet restore

COPY . ./
RUN dotnet publish -c Release -o /publish

# -------------------
# Runtime Stage
# -------------------
FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS runtime
WORKDIR /app

# Copy published output
COPY --from=build-env /publish .

EXPOSE 80
ENTRYPOINT ["dotnet", "Jobid.dll"]