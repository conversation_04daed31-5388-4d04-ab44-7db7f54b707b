﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Google.Apis.Auth;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using System.Security.Cryptography;
using System.Net.Http;
using System.Net.Http.Headers;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Newtonsoft.Json;
using System.Text;
using System.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Http;
using System.IO;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers.Services.Contract;

namespace Jobid.App.Helpers.Utils
{
    public static class Utility
    {
        #region Constants
        public static class Constants
        {
            public const string PUBLIC_SCHEMA = "public";
            public static readonly string QA_BASE_URL_FE = "pactocoin.com";
            public static readonly string STAGING_BASE_URL_FE = "pactocoin.com";
            public static readonly string PROD_BASE_URL_FE = "jobpro.app";
            public static readonly string PROD_ENV = "PRODUCTION";
            public static readonly string QA_ENV = "QA";
            public static readonly string STAGING_ENV = "STAGING";
            public static readonly string PRODUCTION_EXCEPTION_MESSAGE = "Something went wrong, please try again later";
            public static readonly string BAD_REQUEST = "Something went wrong, one or two parameters is missing and wrong";

            public static readonly string INVITE_URL = GlobalVariables.Environment == QA_ENV ?
                "https://{0}.qa.pactocoin.com/auth/invite?invitee={1}&tenantId={2}&app={3}&email={4}&roleId={5}"
                : GlobalVariables.Environment == PROD_ENV ? "https://{0}.jobpro.app/auth/invite?invitee={1}&tenantId={2}&app={3}&email={4}&roleId={5}"
                : "https://{0}.pactocoin.com/auth/invite?invitee={1}&tenantId={2}&app={3}&email={4}&roleId={5}";

            public static readonly string FRONT_END_DASHBOARD_URL = GlobalVariables.Environment == QA_ENV
                ? "https://{0}.qa.pactocoin.com"
                : GlobalVariables.Environment == PROD_ENV ? "https://{0}.jobpro.app"
                : "https://{0}.pactocoin.com";

            public static readonly string MEETING_BASE_URL = GlobalVariables.Environment == QA_ENV
                ? "https://meet.pactocoin.com"
                : GlobalVariables.Environment == PROD_ENV ? "https://meet.jobpro.app"
                : "https://meet.pactocoin.com";

            public static readonly string MOLLIE_REDIRECT_URL = GlobalVariables.Environment == QA_ENV
                ? "https://qa.pactocoin.com/auth/check-payment/mollie"
                : GlobalVariables.Environment == PROD_ENV ? "https://jobpro.app/auth/check-payment/mollie"
                : "https://pactocoin.com/auth/check-payment/mollie";

            public static readonly string ADMIN_URL = GlobalVariables.Environment == QA_ENV
                ? "https://admin.pactocoin.com/dashboard"
                : GlobalVariables.Environment == PROD_ENV ? "https://admin.jobpro.app/dashboard"
                : "https://admin.pactocoin.com/dashboard";

            public static readonly string CLIENT_ADMIN_MOLLIE_REDIRECT_URL = GlobalVariables.Environment == QA_ENV
                ? "https://qa.pactocoin.com/auth/check-payment/mollie"
                : GlobalVariables.Environment == PROD_ENV ? "https://jobpro.app/auth/check-payment/mollie"
                : "https://pactocoin.com/auth/check-payment/mollie";

            public static readonly string BACKEND_BASE_URL = GlobalVariables.Environment == QA_ENV
                ? "https://qapi.pactocoin.com/api"
                : GlobalVariables.Environment == PROD_ENV ? "https://api.jobpro.app/api"
                : "https://api.pactocoin.com/api";

            public static readonly string FRONTEND_PAYMENT_URL = GlobalVariables.Environment == QA_ENV
                ? "https://pactocoin.com/auth/get-started"
                : GlobalVariables.Environment == PROD_ENV ? "https://sua.jobpro.app/payment-token-confirmation"
                : "https://sua.pactocoin.com/payment-token-confirmation";

            public static readonly string FRONTEND_AUTH_URL = GlobalVariables.Environment == QA_ENV
               ? "https://pactocoin.com/auth/get-started"
               : GlobalVariables.Environment == PROD_ENV ? "https://jobpro.app/auth/get-started"
               : "https://pactocoin.com/auth/get-started";

            public static readonly string BACKEND_BASE_URL_LOCAL = "https://localhost:44376/api";

            public static readonly string FRONTEND_RESET_PASSWORD = GlobalVariables.Environment == QA_ENV
                ? "https://qa.pactocoin.com/auth/forgot-password"
                : GlobalVariables.Environment == PROD_ENV ? "https://jobpro.app/auth/forgot-password"
                : "https://pactocoin.com/auth/forgot-password";

            public static readonly string BOOKING_LINK = GlobalVariables.Environment == QA_ENV
                ? "https://{0}.qajoble.pactocoin.com/booking/{1}"
                : GlobalVariables.Environment == PROD_ENV ? "https://{0}.joble.jobpro.app/booking/{1}"
                : "https://{0}.joble.pactocoin.com/booking/{1}";

            public static readonly string ACTIVITY_SHARE_URL = "/suite/pkg/log/activity";

            public static readonly string JOBLE_FE_URL = GlobalVariables.Environment == QA_ENV
                ? "https://{0}.qajoble.pactocoin.com"
                : GlobalVariables.Environment == PROD_ENV ? "https://{0}.joble.jobpro.app"
                : "https://{0}.joble.pactocoin.com";

            public static readonly string FRONT_END_DASHBOARD_URL_JOBLE = GlobalVariables.Environment == QA_ENV
                ? "https://{0}.qajoble.pactocoin.com"
                : GlobalVariables.Environment == PROD_ENV ? "https://{0}.joble.jobpro.app"
                : "https://{0}.joble.pactocoin.com";

            public static readonly string JOBID_SKILL_URL_PATH = GlobalVariables.Environment == QA_ENV
                ? "https://{0}.id.pactocoin.com/#/meeting/{1}/{2}"
                : GlobalVariables.Environment == PROD_ENV ? "https://{0}.id.jobpro.app/#/meeting/{1}/{2}"
                : "https://{0}.id.pactocoin.com/#/meeting/{1}/{2}";

            public static readonly string CHAT_SERVICE_BASEURL = GlobalVariables.Environment == QA_ENV
                ? "https://chats.pactocoin.com/api/v2/work-circle/daily-message-count/"
                : GlobalVariables.Environment == PROD_ENV ? "https://chats.jobpro.app/api/v2/work-circle/daily-message-count/"
                : "https://chats.pactocoin.com/api/v2/work-circle/daily-message-count/";

            public static readonly string AI_DETAILS_BASEURL = GlobalVariables.Environment == QA_ENV
                ? "https://ai.pactocoin.com/api/v0/britney/get-my-character"
                : GlobalVariables.Environment == PROD_ENV ? "https://ai.jobpro.app/api/v0/britney/get-my-character"
                : "https://ai.pactocoin.com/api/v0/britney/get-my-character";

            public static readonly string AI_DEFAULT_NAME = "Brittney";
            public static readonly string AI_DEFAULT_IMAGE = "https://iili.io/26YpVKg.jpg";
        }

        public static string ApplicationTitle = "Job Pro";
        public static string ApplicationFrontendURL = "http://jobpro.app";
        public static string ApplicationEmailURL = "jobpro.app";
        private static readonly RNGCryptoServiceProvider _rng = new RNGCryptoServiceProvider();
        #endregion

        #region Generate Random Numbers
        public static string DateDurationCalculator(int numberOfDays)
        {
            int days, years, weeks;
            string dateRange = "";

            years = numberOfDays / 365;
            weeks = (numberOfDays % 365) / 7;
            days = numberOfDays - ((years * 365) + (weeks * 7));

            if (years > 0)
            {
                var duration = years > 1 ? "Years" : "Year";
                dateRange += $"{years} {duration}";
            }

            if (weeks > 0)
            {
                var duration = weeks > 1 ? "Weeks" : "Week";
                dateRange += $"{weeks} {duration}";
            }

            if (days > 0)
            {
                var duration = days > 1 ? "Days" : "Day";
                dateRange += $"{days} {duration}";
            }

            return dateRange;
        }
        #endregion

        #region Convert Image to Base64
        public static string ConvertSignedUrlToBase64(string signedUrl)
        {
            try
            {
                WebClient webClient = new WebClient();
                byte[] imageData = webClient.DownloadData(signedUrl);

                string base64String = Convert.ToBase64String(imageData);
                return base64String;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        #endregion

        #region Convert Base64 to Image

        public static IFormFile ConvertBase64ToFile(Base64VM file)
        {
            // Strip off the metadata part of the base64 string, if present
            var base64Parts = file.Base64String.Split(',');
            var actualBase64String = base64Parts.Length > 1 ? base64Parts[1] : base64Parts[0];

            // Convert Base64 string to byte array
            byte[] fileBytes = Convert.FromBase64String(actualBase64String);

            // Create a memory stream from the byte array
            var memoryStream = new MemoryStream(fileBytes, true);

            // Create FormFile object from the memory stream
            var formFile = new FormFile(memoryStream, 0, memoryStream.Length, "file", file.FileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/octet-stream"
            };

            return formFile;
        }
        #endregion

        #region Send Mail
        public async static Task<bool> SendGridSendMail(string body, string destination, string subject, string emailFrom = "<EMAIL>", string fromName = "Job Pro")
        {
            try
            {
                var plainTextContent = body;
                var htmlContent = $"<strong>{plainTextContent}</strong>";
                MailgunService mailgunService = new MailgunService();
                var res = await mailgunService.SendEmail(emailFrom, subject, html: htmlContent, new List<string>() { { destination } });
                if (res.IsSuccessStatusCode)
                    return true;
                return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion

        #region Send SMS
        public static bool SendSMS(string phonumber, string body, bool isWhatsAppNo)
        {
            // Create new configuration from appsetting
            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();

            var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? config["Twilio:AccountSID"];
            var authToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN") ?? config["Twilio:AuthToken"];

            TwilioClient.Init(accountSid, authToken);
            MessageResource message = null;
            // isWhatsAppNo = false;
            if (isWhatsAppNo)
            {
                message = MessageResource.Create(
                    body: body,
                    from: new Twilio.Types.PhoneNumber("whatsapp:+***********"),
                    to: new Twilio.Types.PhoneNumber("whatsapp:" + phonumber)
                );
            }
            else
            {
                message = MessageResource.Create(
                    body: body,
                    from: new Twilio.Types.PhoneNumber("JobPro"),
                    to: new Twilio.Types.PhoneNumber(phonumber)
                );
            }

            return true;
        }

        public static (bool, MessageResource) SendSMSandWhatsapp(string phonumber, string body, bool isWhatsAppNo)
        {
            try
            {
                // Create new configuration from appsetting
                var config = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json")
                    .Build();

                var accountSid = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_ACCOUNT_SID") ?? config["Twilio:AccountSID"];
                var authToken = Environment.GetEnvironmentVariable("JOBPRO_TWILIO_AUTH_TOKEN") ?? config["Twilio:AuthToken"];

                TwilioClient.Init(accountSid, authToken);
                MessageResource message = null;
                if (isWhatsAppNo)
                {
                    message = MessageResource.Create(
                        body: body,
                        from: new Twilio.Types.PhoneNumber("whatsapp:+***********"),
                        to: new Twilio.Types.PhoneNumber("whatsapp:" + phonumber),
                        statusCallback: new Uri($"{Constants.BACKEND_BASE_URL}sms-status")

                    //from: new Twilio.Types.PhoneNumber("whatsapp:+***********"),
                    //from: new Twilio.Types.PhoneNumber("whatsapp: JobPro"),
                    //messagingServiceSid: "MG74b61db5a22433e4c901a2eaeac5d4a6",
                    //sendAt: new DateTime(2025, 02, 06, 08, 00, 00),
                    //scheduleType: "fixed"
                    );
                }
                else
                {
                    message = MessageResource.Create(
                    body: body,
                    from: new Twilio.Types.PhoneNumber("JobPro"),
                    to: new Twilio.Types.PhoneNumber(phonumber),
                    statusCallback: new Uri($"{Constants.BACKEND_BASE_URL}sms-status")
                    );
                }
                return (true, message);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.StackTrace);
            }

        }

        #endregion

        #region Get User By Google Token
        public static async Task<GoogleJsonWebSignature.Payload> GetUserByGoogleToken(string token)
        {
            try
            {
                GoogleJsonWebSignature.Payload payload = await GoogleJsonWebSignature.ValidateAsync(token);
                return payload;
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Get Microsoft User Model
        public static async Task<MicrosoftUserBasicProfile> GetMicrosoftUserModelAsync(string MicrosoftAccessToken)
        {
            // create an HttpClient instance
            var _client = new HttpClient();
            _client.DefaultRequestHeaders.Accept.Clear();
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", MicrosoftAccessToken);

            var response = await _client.GetAsync("https://graph.microsoft.com/oidc/userinfo");
            var responseContent = response.Content.ReadAsStringAsync().Result;
            var microsoftUser = JsonConvert.DeserializeObject<MicrosoftUserBasicProfile>(responseContent);
            if (string.IsNullOrEmpty(microsoftUser.Sub)) throw new Exception("Invalid Access token supplied. Please validate token is correct.");

            return microsoftUser;
        }
        #endregion

        #region Calculate Percentage
        public static int CalculatePercentage(int number, int total)
        {
            return (number / total) * 100;
        }
        #endregion

        #region Generate Ramdom Numbers
        public static string GenerateRandomNumbers(int min = 0, int max = 9, int count = 4)
        {
            if (min >= max)
            {
                throw new ArgumentException("min value must be less than max value");
            }

            int[] result = new int[count];
            byte[] randomNumber = new byte[4];

            for (int i = 0; i < count; i++)
            {
                _rng.GetBytes(randomNumber);
                int random = BitConverter.ToInt32(randomNumber, 0);
                result[i] = (int)(min + (random % (max - min)));
            }
            // return result.ToString().Replace("-", "");
            return string.Join("", result).Replace("-", "");
        }
        #endregion

        #region Generate Ramdom Id
        public static string GenerateRandomProfileId()
        {
            Random random = new Random();
            const string prefix = "JobID-";
            const string numbers = "123456789";
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            StringBuilder idBuilder = new StringBuilder(prefix);

            for (var i = 0; i < 6; i++)
                idBuilder.Append(numbers[random.Next(numbers.Length)]);

            idBuilder.Append(chars[random.Next(chars.Length)]);

            return idBuilder.ToString();
        }
        #endregion

        #region Generate Random String
        private static Random random = new Random();
        public static string RandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
        #endregion

        #region Generate Random Password
        public static string GenerateRandomPassword(int length)
        {
            // Minimum length requirement
            if (length < 10)
            {
                throw new ArgumentException("Password length should be at least 10 characters.");
            }

            const string uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
            const string numericChars = "0123456789";
            const string specialChars = "!@#$%^&*()-_=+[]{}|;:'\",.<>/?";

            // Ensure at least one of each character type
            StringBuilder passwordBuilder = new StringBuilder();
            passwordBuilder.Append(GetRandomChar(uppercaseChars));
            passwordBuilder.Append(GetRandomChar(lowercaseChars));
            passwordBuilder.Append(GetRandomChar(specialChars));

            // Generate remaining characters
            int remainingLength = length - 3; // Subtracting 3 for the already added characters
            passwordBuilder.Append(GenerateRandomString(remainingLength, uppercaseChars + lowercaseChars + numericChars + specialChars));

            // Shuffle the password characters randomly
            string shuffledPassword = new string(passwordBuilder.ToString().OrderBy(c => Guid.NewGuid()).ToArray());

            return shuffledPassword;
        }

        static char GetRandomChar(string charSet)
        {
            Random random = new Random();
            return charSet[random.Next(charSet.Length)];
        }

        static string GenerateRandomString(int length, string charSet)
        {
            Random random = new Random();
            return new string(Enumerable.Repeat(charSet, length)
              .Select(s => s[random.Next(s.Length)]).ToArray());
        }
        #endregion

        #region Add key to GlobalVariables 'CacheKeys'
        public static void AddKeyToCacheKeys(string key, string value)
        {
            if (GlobalVariables.CacheKeys.ContainsKey(key))
            {
                if (!GlobalVariables.CacheKeys[key].Contains(value))
                    GlobalVariables.CacheKeys[key].Add(value);
            }
            else
            {
                GlobalVariables.CacheKeys.Add(key, new List<string> { value });
            }
        }
        #endregion

        #region Delete all redis keys on application start up
        public static void DeleteAllRedisKeys(IServiceProvider serviceProvider)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var redisCacheService = scope.ServiceProvider.GetRequiredService<IRedisCacheService>();
                var keys = redisCacheService.GetAllKeysAsync().Result;
                if (keys == null)
                    return;

                foreach (var key in keys)
                {
                    redisCacheService.RemoveDataAsync(key);
                }
            }
        }
        #endregion
    }
}
