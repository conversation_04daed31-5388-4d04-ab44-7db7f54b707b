﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Microsoft.Graph.Models.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ISprintProjectServices
    {
        Task<AddSprintDto> AddSprint_ToProject(AddSprintDto addSprintDto, ProjectMgmt_Project project);
        Task<bool> UpdateSprintProject(SprintProjectVm sprintProjectVm, SprintProject sprintProject);
        Task<Page<SprintProject>> GetAllSprints(PaginationParameters parameters);
        Task<List<SprintProject>> GetSprintProjectById(string Id);
        Task<Page<SprintProject>> GetSprintProjectByIdPaginated(string Id, PaginationParameters parameters);
        Task<bool> RemoveSprintProject(SprintProject sprintProject, string loggedInUserId);
        Task<bool> AddExternalMemberSprintProject(ExternalMember externalMember);
        Task<bool> AddInternalMemberSprintProject(InternalMember internalMember, string projectId);
        Task<List<UserDto>> GetSprintMember(string sprintId);
        Task<SprintProject> GetSprintById(string sprintId);
        Task<TodoStatus> AddTodoStatus(AddTodoStatusVm model);
        Task<List<TodoStatus>> GetTodoStatus(string sprintId);
        Task<bool> UpdateTodoStatus(string status, string Id);
        Task<bool> DeleteTodoStatus(string Id, string loggedInUserId);
        Task<List<SprintReportSummaryDto>> GetSprintReportSummary();
        Task<SprintReportDetailsDto> GetSprintReportDetails(string sprintId);
        Task<List<SprintReportSummaryDto>> GetSprintSummaryReportByProjectId(string projectId);
        Task<bool> UpdateSprintStatus(string sprintId, SprintStatus status, string loggedInUserId);
        Task<bool> MoveTodosToDifferentOrNewSprint(List<string> todoIds, string sprintId);
        Task<bool> RemoveMemberFromSprint(List<string> memberIds, string sprintId, string loggedInUser);
    }
}
