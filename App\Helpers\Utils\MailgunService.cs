using System;
using System.IO;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Jobid.App.Helpers.ViewModel;
using System.Linq;

namespace Jobid.App.Helpers.Utils
{
    public class MailgunService
    {
        // private readonly RestClient client;
        private readonly IConfiguration _config;
        private readonly string apiKey;
        private readonly string domain;
        private string baseUrl = $"https://api.mailgun.net/v3/";

        public MailgunService()
        {
            _config = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
            apiKey = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_APIKEY") ?? _config["MAILGUN_API_KEY"];
            domain = Environment.GetEnvironmentVariable("JOBPRO_MAILGUN_DOMAIN") ?? _config["MAILGUN_DOMAIN"];
        }

        public async Task<HttpResponseMessage> SendEmail(
        string from,
        string subject,
        string html,
        List<string> to,
        List<AttachmentDto> attachments = null,
        List<string> cc = null,
        List<string> bcc = null)
        {
            using (var client = new HttpClient { BaseAddress = new Uri(baseUrl + domain) })
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic",
                    Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));

                from = from.Contains("jobpro") ? $"JobPro <{from}>" : $"Joble <{from}>";

                var formData = new MultipartFormDataContent
        {
            { new StringContent(from), "from" },
            { new StringContent(string.Join(",", to)), "to" },
            { new StringContent(subject), "subject" },
            { new StringContent(html), "html" }
        };

                if (cc != null && cc.Any())
                {
                    formData.Add(new StringContent(string.Join(",", cc)), "cc");
                }

                if (bcc != null && bcc.Any())
                {
                    formData.Add(new StringContent(string.Join(",", bcc)), "bcc");
                }

                if (attachments != null && attachments.Any())
                {
                    foreach (var attachmentDto in attachments)
                    {
                        attachmentDto.attachment.Position = 0;
                        var fileContent = new StreamContent(attachmentDto.attachment);

                        if (!string.IsNullOrWhiteSpace(attachmentDto.fileType))
                        {
                            fileContent.Headers.ContentType = new MediaTypeHeaderValue(attachmentDto.fileType);
                        }

                        fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
                        {
                            Name = "\"attachment\"",
                            FileName = $"\"{attachmentDto.fileName}\""
                        };

                        formData.Add(fileContent, "attachment", attachmentDto.fileName);
                    }
                }

                var response = await client.PostAsync($"{domain}/messages", formData);
                return response;
            }
        }

        public async Task<HttpResponseMessage> sendEmailWithAttachment(
             string from,
             string subject,
             string html,
             List<string> to,
             List<AttachmentDto> attachments = null,
             List<string> cc = null,
             List<string> bcc = null)
        {
            using (var client = new HttpClient { BaseAddress = new Uri(baseUrl + $"{domain}") })
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                    "Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));

                from = from.Contains("jobpro") ? $"JobPro <{from}>" : $"Joble <{from}>";

                var formData = new MultipartFormDataContent
                {
                    { new StringContent(from), "from" },
                    { new StringContent(string.Join(",", to)), "to" },
                    { new StringContent(subject), "subject" },
                    { new StringContent(html), "html" }
                };

                if (cc != null && cc.Any())
                    formData.Add(new StringContent(string.Join(",", cc)), "cc");

                if (bcc != null && bcc.Any())
                    formData.Add(new StringContent(string.Join(",", bcc)), "bcc");

                if (attachments != null && attachments.Any())
                {
                    foreach (var attachmentDto in attachments)
                    {
                        attachmentDto.attachment.Position = 0;
                        var fileContent = new StreamContent(attachmentDto.attachment);

                        // Set content type if specified
                        if (!string.IsNullOrWhiteSpace(attachmentDto.fileType))
                        {
                            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(attachmentDto.fileType);
                        }

                        fileContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
                        {
                            Name = "\"attachment\"",
                            FileName = $"\"{attachmentDto.fileName}\""
                        };

                        formData.Add(fileContent, "attachment", attachmentDto.fileName);
                    }
                }

                var response = await client.PostAsync($"{domain}/messages", formData);
                return response;
            }
        }

        public async Task<HttpResponseMessage> SendEmailWithAttachment(
            string from,
            string subject,
            List<string> to,
            MemoryStream attachmentStream,
            List<string> attachments = null,
            List<string> cc = null,
            List<string> bcc = null,
            string html = null)
        {
            using (var client = new HttpClient
            {
                BaseAddress = new Uri(baseUrl + $"{domain}")
            })
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));

                from = $"JobPro <{from}>";

                var content = new MultipartFormDataContent
                {
                    { new StringContent(from), "from" },
                    { new StringContent(string.Join(',', to)), "to" },
                    { new StringContent(cc != null ? string.Join(',', cc) : ""), "cc" },
                    { new StringContent(subject), "subject" },
                    { new StringContent("<p>Click on the <strong>Add To Calender</strong> link above to add this meeting to any calender app on your phone or PC</P>"), "html" }
                };

                // Add attachments if any
                if (attachments != null && attachments.Count > 0)
                {
                    foreach (var attachmentPath in attachments)
                    {
                        using (var fileStream = new FileStream(attachmentPath, FileMode.Open))
                        {
                            var fileContent = new StreamContent(fileStream);
                            content.Add(fileContent, "attachment", Path.GetFileName(attachmentPath));
                        }
                    }
                }

                // Add MemoryStream as an attachment
                if (attachmentStream != null)
                {
                    var streamContent = new StreamContent(attachmentStream);
                    content.Add(streamContent, "attachment", "calendar.ics");
                }

                var response = await client.PostAsync($"{domain}/messages", content).ConfigureAwait(false);
                return response;
            }
        }


        public async Task<HttpResponseMessage> CheckEmailStatus(string email)
        {
            using (var client = new HttpClient
            {
                BaseAddress = new Uri(baseUrl + $"{domain}")
            })
            {
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.ASCII.GetBytes($"api:{apiKey}")));
                var response = await client.GetAsync($"{domain}/events?ascending=yes&limit=25&pretty=yes&recipient={email}");
                return response;
            }
        }
    }
}