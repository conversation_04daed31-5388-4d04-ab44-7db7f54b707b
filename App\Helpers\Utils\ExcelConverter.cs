using System;
using System.Collections.Generic;
using System.IO;
using Jobid.App.Helpers.Models;
using OfficeOpenXml;

namespace Jobid.App.Helpers.Utils
{
    public class ExcelConverter
    {
        // Convert an array to Excel file and save it in a given path
        public ExcelConverterResponse ArrayToExcel(string[,] array, string name = null, string extension=".xlsx")
        {

            var memoryStream = new MemoryStream();
            if (string.IsNullOrEmpty(name))
            {
                name = Utility.GenerateRandomNumbers(count: 12);
            }

            FileInfo fileInfo = new FileInfo(name + extension);

            using (ExcelPackage package = new ExcelPackage(memoryStream))
            {
                ExcelWorksheet worksheet;
                worksheet = package.Workbook.Worksheets.Add(name);

                int rows = array.GetLength(0);
                int columns = array.GetLength(1);

                worksheet.Name = name;

                for (int i = 0; i < rows; i++)
                {
                    for (int j = 0; j < columns; j++)
                    {
                        worksheet.Cells[i + 1, j + 1].Value = array[i, j];
                    }
                }

                package.SaveAs(fileInfo);
                // package.Save();
            }
            return  new ExcelConverterResponse { FullName = fileInfo.FullName, name=name, extension=extension };
        }

        public ExcelConverterResponse ArrayToExcel(List<string> titles, List<List<string>> excelData, string name=null, string extension=".xlsx")
        {

            var memoryStream = new MemoryStream();
            if (string.IsNullOrEmpty(name)) {
                name = Utility.GenerateRandomNumbers(count:12);
            }
            
            FileInfo fileInfo = new FileInfo(name + ".xlsx");

            using (ExcelPackage package = new ExcelPackage(memoryStream))
            {
                ExcelWorksheet worksheet;
                worksheet = package.Workbook.Worksheets.Add(name);


                worksheet.Name = name;

                for (int i = 0; i < titles.Count; i++)
                {
                    worksheet.Cells[1, i+1].Value = titles[i];
                }

                for (int i = 0; i < excelData.Count; i++)
                {
                    for (int j = 0; j < titles.Count; j++) {
                        var excelRow = excelData[i];
                        worksheet.Cells[i+2, j + 1].Value = excelRow[j];
                    }
                }

                package.SaveAs(fileInfo);
            }
            return  new ExcelConverterResponse { FullName = fileInfo.FullName, name=name, extension=extension };
        }

        
        // Convert an Excel file or a CSV file to a Base64 string
        public string FileToBase64(string path, bool deleteAfterConversion=true)
        {
            byte[] bytes = File.ReadAllBytes(path);
            string Base64 = Convert.ToBase64String(bytes);
            if (deleteAfterConversion) {
                File.Delete(path);
            }
            return Base64;
        }
    }
}