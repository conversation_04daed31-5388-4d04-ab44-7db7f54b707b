using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Jobid.App.AdminConsole.Enums;

namespace Jobid.App.AdminConsole.Models.Phone
{
    public class PhoneNumber
    {
        [Key]
        public Guid Id { get; set; }
        public string Number { get; set; }
        public bool IsActive { get; set; }
        public bool IsRegistered { get; set; }
        public decimal Balance { get; set; }
        public string CountryCode { get; set; }
        public PhoneNumberType NumberType { get; set; }
        public string TwilioSid { get; set; }
        public string FriendlyName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public virtual ICollection<PhoneNumberCapability> Capabilities { get; set; }
        public virtual ICollection<PhoneNumberAssignment> Assignments { get; set; }

        public PhoneNumber()
        {
            Id = Guid.NewGuid();
            IsActive = true;
            Capabilities = new List<PhoneNumberCapability>();
            Assignments = new List<PhoneNumberAssignment>();
            CreatedAt = DateTime.UtcNow;
        }
    }
}
