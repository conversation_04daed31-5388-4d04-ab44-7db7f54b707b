#region Using statements
using AutoMapper;
using Jobid.App.ActivityLog.BackGroundJobs;
using Jobid.App.ActivityLog.contract;
using Jobid.App.ActivityLog.Repository;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Services;
using Jobid.App.AdminConsole.Hubs;
using Jobid.App.Calender.Contracts;
using Jobid.App.Calender.Services.Implementations;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProject.Services.Implemetations;
using Jobid.App.Wiki.Services.Implementations;
using Jobid.App.Notification.Contracts;
using Jobid.App.Notification.Hubs;
using Jobid.App.Notification.Repository;
using Jobid.App.RabbitMQ;
using Jobid.App.SchemaTenant;
using Jobid.App.Subscription.Configuration;
using Jobid.App.Subscription.Services.Contract;
using Jobid.App.Subscription.Services.Implementations;
using Jobid.App.Tenant;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.Repository;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Mollie.Api.Client.Abstract;
using System.Net.Http;
using System.Threading.Tasks;
#endregion

namespace Jobid.App.Helpers.Services.Implementations
{
    public class Unitofwork : IUnitofwork
    {
        #region Private Variables
        private IWebHostEnvironment GetWebHostEnvironment;
        private readonly JobProDbContext DbCon;
        private readonly JobProDbContext publicSchemaContext;
        private readonly UserStore<User> userStore;
        private readonly UserManager<User> userManager;
        private readonly ITenantSchema tenantSchema;
        private readonly IMapper mapper;
        private readonly IConfiguration _config;
        private readonly IMemoryCache _cache;
        private readonly IWebHostEnvironment env;
        private IUserServices GetUserServices;
        private IEmailService EmailService;
        private IHttpContextAccessor _httpContextAccessor;
        private ICompanyUserInvite GetCompanyUserInviteService;
        private IBackGroundService GetBackGroundService;
        private I2FAService Get2FAService;
        public IClientServices GetClientService;
        private IProjectFileService GetProjectFileService;
        private ITenantService GetTenantService;
        private IAdminService GetAdminService;
        private IContactService GetAdminContactService;
        private ICampaignService GetCampaignService;
        private IAppDashboardService GetAppDashboardService;
        private IUserCompaniesServices GetUserCompaniesService;
        private IProjectServices GetProjectService;
        private ITeamSheetService GetTeamSheetService;
        private ITodoServices GetTodoService;
        private IOTPServices GetOTPServices;
        private ITimeSheetService GetTimeSheetService;
        private ITriggerServices GetTriggerServices;
        private ISprintProjectServices GetSprintProjectServices;
        private ICalenderService GetCalenderServices;
        private IUserProfileServices GetUserProfileServices;
        private ITagService GetTagService;
        private IActivityService GetActivityService;
        private IUserAdminServices GetUserAdminServices;
        private ISubscriptionServices GetSubscriptionServices;
        private Subscription.Services.Contract.IBackGroundServices GetBackGroundServices;
        private IPaymentClient _paymentClient;
        private ICustomerClient _customerClient;
        private ISubscriptionClient _subscriptionClient;
        public IAWSS3Sevices GetAWSServices;
        private INotificationsService GetNotificationsService;
        private IHubContext<NotificationHub, INotificationClient> _hubContext;
        private readonly IHubContext<NotificationHub> _phoneHubContext;
        private IMandateClient _mandateClient;
        private IActivityBackgroundService GetActivityBackgroundService;
        private IOptions<StripeOptions> _options;
        private readonly IPublisherService _publisherService;
        private IProductUpdateService GetProductUpdateService;
        private IProductUpdateReceiverService GetProductUpdateReceiverService;
        private IActivityViewBackgroundService _activityViewBackgroundService;
        private readonly ILogService _logService;
        private readonly IRedisCacheService _redisCacheService;
        private readonly IRabbitMQConnectionService _rabbitMQConnectionService;
        private readonly IApiCallService _apiCallService;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IBackgroungService _adminConsoleBackgroungService;
        private readonly IActivityBackgroundService _activityBackgroundService;
        private readonly IBackGroundService _calenderBackGroundService;
        private IBackgroundJobService BackgroundJobTenantService;
        public JobProject.Services.Contract.IBackGroundServices GetJobleBackGroundService { get; }
        private ITenantClientService GetTenantClientService;
        private readonly IBackgroundJobService _backgroundJobService;
        private readonly IPdfService _pdfService;
        private readonly IPdfGeneratorServices _pdfGeneratorServices;        
        private Jobid.App.Wiki.Services.Contract.IWikiAccessService GetWikiAccessService;        private Jobid.App.Wiki.Services.Contract.IWikiFileService GetWikiFileService;        private IEndpointTrackerService GetEndpointTrackerService;
        private IPhoneNumberService GetPhoneNumberService;
        private IPaymentService GetPaymentService;
        private IWalletService GetWalletService;
        private ITelephonyService GetTelephonyService;
        private IPhoneNumberMaintenanceService GetPhoneNumberMaintenanceService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ITwilioConferenceService _twilioConferenceService;
        private readonly IHubContext<CallHub> _callHubContext;
        private readonly ITwilioService _twilioService;
        private ITwilioConferenceService GetTwilioConferenceService;
        #endregion

        #region Properties and contructors
        public Unitofwork(IEmailService EmailService, IHttpContextAccessor httpContextAccessor, ITenantConfig<JobProDbContext> tenantConfig, ITenantSchema tenantSchema, IMapper mapper, IConfiguration config, IMemoryCache cache, IPaymentClient paymentClient, ICustomerClient customerClient, ISubscriptionClient subscriptionClient, IWebHostEnvironment webHostEnvironment, IHubContext<NotificationHub, INotificationClient> hubContext, IMandateClient mandateClient, IAWSS3Sevices aWSS3Sevices, IOptions<StripeOptions> options, IPublisherService publisherService, JobProject.Services.Contract.IBackGroundServices backGroundService, IProductUpdateService productUpdateService, IProductUpdateReceiverService getProductUpdateReceiverService, IActivityViewBackgroundService activityViewBackgroundService, ILogService logService, IRedisCacheService redisCacheService, IRabbitMQConnectionService rabbitMQConnectionService, IApiCallService apiCallService, IServiceScopeFactory serviceScopeFactory, IBackgroungService adminConsoleBackgroungService, IActivityBackgroundService activityBackgroundService, IBackGroundService calenderBackGroundService, IBackgroundJobService backgroundJobService, IPdfService pdfService, IPdfGeneratorServices pdfGeneratorServices, IHubContext<NotificationHub> phoneHubContext, IPhoneNumberMaintenanceService phoneNumberMaintenanceService, IHttpClientFactory httpClientFactory, ITwilioConferenceService twilioConferenceService, IHubContext<CallHub> callHubContext, ITwilioService twilioService)
        {
            var _host = tenantSchema.ExtractSubdomainFromRequest(httpContextAccessor.HttpContext);
            DbCon = tenantConfig.getRequestContext(httpContextAccessor.HttpContext);
            this.EmailService = EmailService;
            _httpContextAccessor = httpContextAccessor;
            publicSchemaContext = new JobProDbContext(new DbContextSchema());
            userStore = new UserStore<User>(publicSchemaContext);
            userManager = new UserManager<User>(userStore, null, new PasswordHasher<User>(), null, null, null, null, null, null);
            this.tenantSchema = tenantSchema;
            this.mapper = mapper;
            _config = config;
            _cache = cache;
            _paymentClient = paymentClient;
            _customerClient = customerClient;
            _subscriptionClient = subscriptionClient;
            GetWebHostEnvironment = webHostEnvironment;
            _hubContext = hubContext;
            _phoneHubContext = phoneHubContext;
            _mandateClient = mandateClient;
            GetAWSServices = aWSS3Sevices;
            _options = options;
            _publisherService = publisherService;
            GetJobleBackGroundService = backGroundService;
            GetProductUpdateService = productUpdateService;
            GetProductUpdateReceiverService = getProductUpdateReceiverService;
            _activityViewBackgroundService = activityViewBackgroundService;
            _logService = logService;
            _redisCacheService = redisCacheService;
            _rabbitMQConnectionService = rabbitMQConnectionService;
            _apiCallService = apiCallService;
            _serviceScopeFactory = serviceScopeFactory;
            _adminConsoleBackgroungService = adminConsoleBackgroungService;
            _activityBackgroundService = activityBackgroundService;
            _calenderBackGroundService = calenderBackGroundService;
            _backgroundJobService = backgroundJobService;
            _pdfService = pdfService;
            _pdfGeneratorServices = pdfGeneratorServices;
            GetPhoneNumberMaintenanceService = phoneNumberMaintenanceService;
            _httpClientFactory = httpClientFactory;
            _twilioConferenceService = twilioConferenceService;
            _callHubContext = callHubContext;
            _twilioService = twilioService;
        }
        #endregion

        #region Methods
        public IActivityBackgroundService ActivityBackgroundService
        {
            get
            {
                if (GetActivityBackgroundService == null)
                    GetActivityBackgroundService = new ActivityBackgroundService(DbCon, publicSchemaContext);
                return GetActivityBackgroundService;
            }
        }

        public INotificationsService NotificationsService
        {
            get
            {
                if (GetNotificationsService == null)
                    GetNotificationsService = new NotificationsService(DbCon, GetAWSServices);
                return GetNotificationsService;
            }
        }

        public Subscription.Services.Contract.IBackGroundServices BackGroundServices
        {
            get
            {
                if (GetBackGroundServices == null)
                    GetBackGroundServices = new Subscription.Services.Implementations.BackGroundServices(publicSchemaContext, _subscriptionClient, _config);
                return GetBackGroundServices;
            }
        }

        public ISubscriptionServices SubscriptionServices
        {
            get
            {
                if (GetSubscriptionServices == null)
                    GetSubscriptionServices = new SubscriptionServices(publicSchemaContext, TenantService, _paymentClient, _customerClient, _subscriptionClient, _config, _mandateClient, mapper, DbCon, EmailService, GetWebHostEnvironment, _options, _cache, _publisherService, _backgroundJobService, _apiCallService, _pdfService);

                return GetSubscriptionServices;
            }
        }

        public IUserServices Userservice
        {
            get
            {
                if (GetUserServices == null)
                {
                    GetUserServices = new UserServices(publicSchemaContext, _httpContextAccessor, userManager, DbCon, GetAWSServices, EmailService, GetWebHostEnvironment, _config, AdminService, UserCompaniesServices);
                }

                return GetUserServices;
            }
        }

        public ICompanyUserInvite CompanyUserInviteService
        {
            get
            {

                if (GetCompanyUserInviteService == null) { GetCompanyUserInviteService = new CompanyUserInviteService(publicSchemaContext); }
                return GetCompanyUserInviteService;
            }
        }

        public I2FAService I2FAService
        {
            get
            {
                if (Get2FAService == null) { Get2FAService = new _2FAService(publicSchemaContext, DbCon, _config, OPTService, GetAWSServices); }
                return Get2FAService;
            }
        }

        public async Task CommitAsync()
        {

            await publicSchemaContext.SaveChangesAsync();

        }

        public IClientServices ClientService
        {
            get
            {
                if (GetClientService == null) { GetClientService = new ClientServices(DbCon); }
                return GetClientService;
            }
        }

        public IProjectFileService ProjectFileService
        {
            get
            {
                if (GetProjectFileService == null) { GetProjectFileService = new ProjectFileService(DbCon); }
                return GetProjectFileService;
            }
        }

        public IBackGroundService BackGroundService
        {
            get
            {
                if (GetBackGroundService == null) { GetBackGroundService = new BackGroundService(EmailService, DbCon, _config, _apiCallService, _serviceScopeFactory); }
                return GetBackGroundService;
            }
        }

        public IBackgroundJobService BackgroundJobService
        {
            get
            {
                if (BackgroundJobTenantService == null) { BackgroundJobTenantService = new BackgroundJobService(); }
                return BackgroundJobTenantService;
            }
        }

        public ITenantService TenantService
        {
            get
            {
                if (GetTenantService == null) { GetTenantService = new TenantService(publicSchemaContext, userManager, DbCon, OPTService, tenantSchema, EmailService, GetAWSServices, GetCompanyUserInviteService, _publisherService, GetJobleBackGroundService, GetWebHostEnvironment, GetProductUpdateReceiverService, _activityViewBackgroundService, _adminConsoleBackgroungService, mapper, _activityBackgroundService, _calenderBackGroundService, _backgroundJobService, _apiCallService, GetPhoneNumberMaintenanceService, GetAdminService); }
                return GetTenantService;
            }
        }

        public IUserCompaniesServices UserCompaniesServices
        {
            get
            {
                if (GetUserCompaniesService == null) { GetUserCompaniesService = new UserCompaniesService(publicSchemaContext, GetAWSServices, DbCon); }
                return GetUserCompaniesService;
            }
        }

        public IProjectServices ProjectService
        {
            get
            {
                if (GetProjectService == null) { GetProjectService = new ProjectServices(DbCon, publicSchemaContext, EmailService, TenantService, CompanyUserInviteService, GetAWSServices, GetWebHostEnvironment, AdminService); }
                return GetProjectService;
            }
        }

        public ITeamSheetService TeamSheetService
        {
            get
            {
                if (GetTeamSheetService == null) { GetTeamSheetService = new TeamSheetService(DbCon, publicSchemaContext, EmailService, TenantService, CompanyUserInviteService, GetWebHostEnvironment, GetAWSServices, _apiCallService, _config); }
                return GetTeamSheetService;
            }
        }        public IAdminService AdminService
        {
            get
            {
                if (GetAdminService == null) { GetAdminService = new AdminService(publicSchemaContext, DbCon, GetAWSServices, GetWebHostEnvironment, EmailService, _serviceScopeFactory, mapper, _publisherService, _redisCacheService, TenantService, _apiCallService, _config ); }
                return GetAdminService;
            }
        }

        public IContactService ContactService
        {
            get
            {
                if (GetAdminContactService == null) { GetAdminContactService = new ContactService(DbCon, GetAWSServices); }
                return GetAdminContactService;
            }
        }

        public ICampaignService CampaignService
        {
            get
            {
                if (GetCampaignService == null) { GetCampaignService = new CampaignService(DbCon); }
                return GetCampaignService;
            }
        }

        public IAppDashboardService AppDashboardService
        {
            get
            {
                if (GetAppDashboardService == null) { GetAppDashboardService = new AppDashboardService(publicSchemaContext, DbCon); }
                return GetAppDashboardService;
            }
        }

        public ITodoServices TodoService
        {
            get
            {
                if (GetTodoService == null) { GetTodoService = new TodoServices(DbCon, publicSchemaContext, EmailService, TenantService, CompanyUserInviteService, GetAWSServices, GetWebHostEnvironment, _hubContext, mapper, ActivityService, AdminService, _redisCacheService, _logService, _apiCallService); }
                return GetTodoService;
            }
        }

        public ITimeSheetService TimeSheetService
        {
            get
            {
                if (GetTimeSheetService == null) { GetTimeSheetService = new TimeSheetService(DbCon, publicSchemaContext, GetAWSServices, mapper, ActivityService, _hubContext, AdminService, GetWebHostEnvironment, EmailService, TenantService); }
                return GetTimeSheetService;
            }
        }

        public IOTPServices OPTService
        {
            get
            {
                if (GetOTPServices == null) { GetOTPServices = new OTPService(publicSchemaContext, EmailService, GetWebHostEnvironment); }
                return GetOTPServices;
            }
        }

        public ITriggerServices TriggerService
        {
            get
            {
                if (GetTriggerServices == null) { GetTriggerServices = new TriggerServices(DbCon, publicSchemaContext, EmailService, _hubContext); }
                return GetTriggerServices;
            }
        }

        public ISprintProjectServices SprintProjectService
        {
            get
            {
                if (GetSprintProjectServices == null) { GetSprintProjectServices = new SprintProjectServices(DbCon, EmailService, publicSchemaContext, mapper, userManager, env, TenantService, CompanyUserInviteService, GetWebHostEnvironment, GetAWSServices, AdminService); }
                return GetSprintProjectServices;
            }
        }


        public ICalenderService CalenderService
        {
            get
            {
                if (GetCalenderServices == null) { GetCalenderServices = new CalenderService(DbCon, publicSchemaContext, mapper, userManager, EmailService, BackGroundService, GetWebHostEnvironment, GetAWSServices, AdminService, _cache, _logService, _redisCacheService, _apiCallService, _pdfService, _pdfGeneratorServices, _hubContext); }
                return GetCalenderServices;
            }
        }

        public IUserProfileServices UserProfileServices
        {
            get
            {
                if (GetUserProfileServices == null) { GetUserProfileServices = new UserProfileServices(publicSchemaContext, DbCon, GetWebHostEnvironment, EmailService, GetAWSServices); }
                return GetUserProfileServices;
            }
        }

        public ITagService TagService
        {
            get
            {
                if (GetTagService == null) { GetTagService = new TagService(DbCon, publicSchemaContext); }
                return GetTagService;
            }
        }

        public ITenantClientService TenantClientService
        {
            get
            {
                if (GetTenantClientService == null) { GetTenantClientService = new TenantClientService(DbCon, publicSchemaContext); }
                return GetTenantClientService;
            }
        }


        public IActivityService ActivityService
        {
            get
            {
                if (GetActivityService == null) { GetActivityService = new ActivityService(DbCon, publicSchemaContext, mapper, AdminService, GetWebHostEnvironment, EmailService); }
                return GetActivityService;
            }
        }

        public IUserAdminServices UserAdminServices
        {
            get
            {
                if (GetUserAdminServices == null) { GetUserAdminServices = new UserAdminServices(DbCon, publicSchemaContext, mapper, GetAWSServices, _publisherService); }
                return GetUserAdminServices;
            }
        }

        public IProductUpdateReceiverService ProductUpdateReceiverService
        {
            get
            {
                if (GetProductUpdateReceiverService == null)
                    GetProductUpdateReceiverService = new ProductUpdateReceiverService(_config, GetProductUpdateService, _rabbitMQConnectionService);
                return GetProductUpdateReceiverService;
            }
        }

        public IProductUpdateService ProductUpdateService
        {
            get
            {
                if (GetProductUpdateService == null)
                    GetProductUpdateService = new ProductUpdateService();
                return GetProductUpdateService;
            }
        }

        public Jobid.App.Wiki.Services.Contract.IWikiAccessService WikiAccess
        {
            get
            {
                if (GetWikiAccessService == null) { GetWikiAccessService = new WikiAccessService(DbCon); }
                return GetWikiAccessService;
            }
        }

        public Jobid.App.Wiki.Services.Contract.IWikiFileService WikiFile
        {
            get
            {
                if (GetWikiFileService == null) 
                { 
                    var backgroundService = new WikiFileBackgroundService(GetAWSServices, DbCon, _config);
                    GetWikiFileService = new Jobid.App.Wiki.Services.Implementations.WikiFileService(DbCon, GetAWSServices, backgroundService, GetWebHostEnvironment, _config, publicSchemaContext); 
                }
                return GetWikiFileService;
            }
        } 
        
        public IEndpointTrackerService EndpointTrackerService
        {
            get
            {
                if (GetEndpointTrackerService == null) { GetEndpointTrackerService = new EndpointTrackerService(DbCon); }
                return GetEndpointTrackerService;
            }
        }        
        
        public IPhoneNumberService PhoneNumberService
        {
            get
            {
                if (GetPhoneNumberService == null) 
                { 
                    GetPhoneNumberService = new PhoneNumberService(
                        configuration: _config,
                        context: DbCon,
                        hubContext: _phoneHubContext,
                        cache: _cache,
                        publicContext: publicSchemaContext,
                        mapper: mapper,
                        wikiFileService: WikiFile,
                        callHubContext: _callHubContext,
                        twilioService: _twilioService
                    );
                }
                return GetPhoneNumberService;
            }
        }        public IPaymentService PaymentService
        {
            get
            {
                if (GetPaymentService == null)
                {
                    GetPaymentService = new PaymentService(_config, DbCon, _options, _paymentClient, publicSchemaContext);
                }
                return GetPaymentService;
            }
        }

        public IWalletService WalletService
        {
            get
            {
                if (GetWalletService == null)
                {
                    GetWalletService = new WalletService(DbCon, PaymentService);
                }
                return GetWalletService;
            }
        } 
        
        public ITelephonyService TelephonyService
        {
            get
            {
                if (GetTelephonyService == null)
                {                    
                    // Create a logger using the service scope factory
                    using var scope = _serviceScopeFactory.CreateScope();
                    var loggerFactory = scope.ServiceProvider.GetRequiredService<ILoggerFactory>();
                    var logger = loggerFactory.CreateLogger<TelephonyService>();

                    GetTelephonyService = new TelephonyService(
                        _twilioConferenceService,
                        _callHubContext,
                        logger,
                        DbCon,
                        phoneNumberService: PhoneNumberService
                    );
                }
                return GetTelephonyService;
            }
        }

        public IPhoneNumberMaintenanceService PhoneNumberMaintenanceService
        {
            get
            {
                if (GetPhoneNumberMaintenanceService == null)
                {
                    GetPhoneNumberMaintenanceService = new PhoneNumberMaintenanceService(publicSchemaContext, WalletService, EmailService, NotificationsService, _config);
                }
                return GetPhoneNumberMaintenanceService;
            }
        }

        public ITwilioConferenceService TwilioConferenceService
        {
            get
            {
                if (GetTwilioConferenceService == null)
                {   
                    GetTwilioConferenceService = _twilioConferenceService;
                }
                return GetTwilioConferenceService;
            }
        }
        #endregion
    }
}