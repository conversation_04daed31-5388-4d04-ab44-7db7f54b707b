﻿using Jobid.App.Helpers.Models;
using System.Collections.Generic;

namespace Jobid.App.Helpers.ViewModel
{
    public class UserVm
    {
        public string Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string IpAddress { get; set; }
        public bool IsIpLock { get; set; }
        public string RoleName{ get; set; }
        public string RegionName { get; set; }
    }

    public class UserMDVm
    {
        public string Id { get; set; }
        public string UserId => Id;
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string CompanyEmail { get; set; }
        public string PersonalEmail { get; set; }
        public string Username { get; set; }
        public string IpAddress { get; set; }
        public bool IsIpLock { get; set; }
        public string RoleName{ get; set; }
        public string RegionName { get; set; }
        public string Address { get; set; }
        public string Bio { get; set; }
        public string PhoneNumber { get; set; }
        public string ProfilePictureUrl { get; set; }
        public string Country { get; set; }
        public string State { get; set; }
        public string ZipCode { get; set; }
        public string TimeZone { get; set; }
        public bool IsVerified { get; set; }
        public string TenantId { get; set; }
        public string Subdomain { get; set; }
        public string Designation { get; set; }
        public string ReferralCode { get; set; }
        public string Industry { get; set; }
        public Dictionary<string, List<UserSkill>>? Skills { get; set; } = null;
    }
}
