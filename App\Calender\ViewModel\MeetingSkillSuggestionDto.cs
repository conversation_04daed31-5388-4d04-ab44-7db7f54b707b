using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.ViewModel
{
    /// <summary>
    /// DTO for user skills within a meeting suggestion
    /// </summary>
    public class UserSkillSuggestionDto
    {
        /// <summary>
        /// User ID
        /// </summary>
        [Required]
        public string UserId { get; set; }

        /// <summary>
        /// List of skills for this user
        /// </summary>
        [Required]
        public List<string> Skills { get; set; }
    }

    /// <summary>
    /// DTO for suggesting skills for a meeting
    /// </summary>
    public class MeetingSkillSuggestionDto
    {
        /// <summary>
        /// Meeting ID
        /// </summary>
        [Required]
        public Guid MeetingId { get; set; }

        /// <summary>
        /// List of user skill suggestions
        /// </summary>
        [Required]
        public List<UserSkillSuggestionDto> UserSkills { get; set; }
    }

    /// <summary>
    /// DTO for getting meeting skill suggestions
    /// </summary>
    public class GetMeetingSkillSuggestionsDto
    {
        /// <summary>
        /// Meeting ID
        /// </summary>
        [Required]
        public Guid MeetingId { get; set; }
    }

    /// <summary>
    /// Response DTO for meeting skill suggestions
    /// </summary>
    public class MeetingSkillSuggestionResponseDto
    {
        /// <summary>
        /// Suggestion ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// User ID who suggested the skills
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// Meeting ID
        /// </summary>
        public Guid MeetingId { get; set; }

        /// <summary>
        /// List of suggested skills
        /// </summary>
        public List<string> Skills { get; set; }

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Updated date
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Response DTO for grouped meeting skill suggestions
    /// </summary>
    public class GroupedMeetingSkillSuggestionResponseDto
    {
        /// <summary>
        /// Meeting ID
        /// </summary>
        public Guid MeetingId { get; set; }

        /// <summary>
        /// List of user skill suggestions
        /// </summary>
        public List<UserSkillSuggestionResponseDto> UserSkills { get; set; }
    }

    /// <summary>
    /// Response DTO for user skill suggestions
    /// </summary>
    public class UserSkillSuggestionResponseDto
    {
        /// <summary>
        /// Suggestion ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// List of skills
        /// </summary>
        public List<string> Skills { get; set; }

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Updated date
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
}
