﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Jobid.App.Helpers.ViewModel
{
    // Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
    public class Ara
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Bre
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class CapitalInfo
    {
        public List<double> Latlng { get; set; }
    }

    public class Car
    {
        public List<string> Signs { get; set; }
        public string Side { get; set; }
    }

    public class Ces
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class CoatOfArms
    {
        public string Png { get; set; }
        public string Svg { get; set; }
    }

    public class Currencies
    {
        public USD USD { get; set; }
    }

    public class Cym
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Demonyms
    {
        public Eng Eng { get; set; }
        public Fra Fra { get; set; }
    }

    public class Deu
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Eng
    {
        public string Official { get; set; }
        public string Common { get; set; }
        public string F { get; set; }
        public string M { get; set; }
    }

    public class Est
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Fin
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Flags
    {
        public string Png { get; set; }
        public string Svg { get; set; }
        public string Alt { get; set; }
    }

    public class Fra
    {
        public string Official { get; set; }
        public string Common { get; set; }
        public string F { get; set; }
        public string M { get; set; }
    }

    public class Gini
    {
        [JsonProperty("2018")]
        public double _2018 { get; set; }
    }

    public class Hrv
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Hun
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Idd
    {
        public string Root { get; set; }
        public List<string> Suffixes { get; set; }
    }

    public class Ita
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Jpn
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Kor
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Languages
    {
        public string Eng { get; set; }
    }

    public class Maps
    {
        public string GoogleMaps { get; set; }
        public string OpenStreetMaps { get; set; }
    }

    public class Name
    {
        public string Common { get; set; }
        public string Official { get; set; }
        public NativeName NativeName { get; set; }
    }

    public class NativeName
    {
        public Eng Eng { get; set; }
    }

    public class Nld
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Per
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Pol
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Por
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class PostalCode
    {
        public string Format { get; set; }
        public string Regex { get; set; }
    }

    public class CountryDetailsResponse
    {
        public Name Name { get; set; }
        public List<string> Tld { get; set; }
        public string Cca2 { get; set; }
        public string Ccn3 { get; set; }
        public string Cca3 { get; set; }
        public string Cioc { get; set; }
        public bool Independent { get; set; }
        public string Status { get; set; }
        public bool UnMember { get; set; }
        public Currencies Currencies { get; set; }
        public Idd Idd { get; set; }
        public List<string> Capital { get; set; }
        public List<string> AltSpellings { get; set; }
        public string Region { get; set; }
        public string Subregion { get; set; }
        public Languages Languages { get; set; }
        public Translations Translations { get; set; }
        public List<double> Latlng { get; set; }
        public bool Landlocked { get; set; }
        public List<string> Borders { get; set; }
        public double Area { get; set; }
        public Demonyms Demonyms { get; set; }
        public string Flag { get; set; }
        public Maps Maps { get; set; }
        public int Population { get; set; }
        public Gini Gini { get; set; }
        public string Fifa { get; set; }
        public Car Car { get; set; }
        public List<string> Timezones { get; set; }
        public List<string> Continents { get; set; }
        public Flags Flags { get; set; }
        public CoatOfArms CoatOfArms { get; set; }
        public string StartOfWeek { get; set; }
        public CapitalInfo CapitalInfo { get; set; }
        public PostalCode PostalCode { get; set; }
    }

    public class Rus
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Slk
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Spa
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Srp
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Swe
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Translations
    {
        public Ara Ara { get; set; }
        public Bre Bre { get; set; }
        public Ces Ces { get; set; }
        public Cym Cym { get; set; }
        public Deu Deu { get; set; }
        public Est Est { get; set; }
        public Fin Fin { get; set; }
        public Fra Fra { get; set; }
        public Hrv Hrv { get; set; }
        public Hun Hun { get; set; }
        public Ita Ita { get; set; }
        public Jpn Jpn { get; set; }
        public Kor Kor { get; set; }
        public Nld Nld { get; set; }
        public Per Per { get; set; }
        public Pol Pol { get; set; }
        public Por Por { get; set; }
        public Rus Rus { get; set; }
        public Slk Slk { get; set; }
        public Spa Spa { get; set; }
        public Srp Srp { get; set; }
        public Swe Swe { get; set; }
        public Tur Tur { get; set; }
        public Urd Urd { get; set; }
        public Zho Zho { get; set; }
    }

    public class Tur
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class Urd
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }

    public class USD
    {
        public string Name { get; set; }
        public string Symbol { get; set; }
    }

    public class Zho
    {
        public string Official { get; set; }
        public string Common { get; set; }
    }


}
