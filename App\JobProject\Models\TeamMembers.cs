﻿using Jobid.App.Helpers.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.JobProjectManagement.Models
{
    public class TeamMembers
    {
        public Guid Id { get; set; } = new Guid();
        public Guid TeamId { get; set; }
        public string UserId { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }

        // Navigation Properties
        public Team Team { get; set; }
    }
}
