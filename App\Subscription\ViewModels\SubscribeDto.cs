﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Jobid.App.Helpers.Enums;
using Jobid.App.Subscription.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.ViewModels
{
    public class SubscribeDto
    {
        [Required]
        public string PlanId { get; set; }
        public Currency Currency { get; set; } = Currency.EUR;
        public Applications Application { get; set; }
        public int? NumberOfUsers { get; set; }
        public PaymentMethods Method { get; set; }
        public string PayPalEmail { get; set; }
        public string PaypalBillingAgreementId { get; set; }
        public string ConsumerAccount { get; set; }
        public BillingAddressDto BillingAddress { get; set; }
        public PaymentProviders PaymentProvider { get; set; }
        public SubscriptionInterval Interval { get; set; }
        public bool FreeTrial { get; set; }
        public bool FromClientAdmin { get; set; }

        /// <summary>
        /// This is for apploication liecense
        /// </summary>
        public List<string> UserIds { get; set; }

        public Dictionary<AIAgents, List<string>> AIUserIds { get; set; }

        [Required]
        public double VAT { get; set; }
        public string DisCountCode { get; set; }
        public bool IsAISubscription { get; set; }
        public Dictionary<AIAgents, int> AIAgentDetails { get; set; }

        [JsonIgnore]
        public string UserId { get; set; }

        [JsonIgnore]
        public string Subdomain { get; set; }

        public SubscribeDto()
        {
            Interval = SubscriptionInterval.Monthly;
            UserIds = new List<string>();
            AIAgentDetails = new Dictionary<AIAgents, int>();
            IsAISubscription = false;
            AIUserIds = new Dictionary<AIAgents, List<string>>();
        }
    }
}
