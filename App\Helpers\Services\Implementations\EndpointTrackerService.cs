﻿﻿using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class EndpointTrackerService : IEndpointTrackerService
    {
        private readonly JobProDbContext _dbContext;
        private readonly JobProDbContext _publicSchemaContext;
        private readonly ILogger _logger;

        public EndpointTrackerService(JobProDbContext dbContext)
        {
            _dbContext = dbContext;
            _publicSchemaContext = new JobProDbContext(new DbContextSchema());
            _logger = Log.ForContext<EndpointTrackerService>();
        }

        /// <summary>
        /// Tracks an endpoint call
        /// </summary>
        public async Task<bool> TrackEndpointCallAsync(
            Applications application,
            ApplicationSection section,
            string controller,
            string action,
            string httpMethod)
        {
            try
            {
                var today = DateTime.UtcNow.Date;

                // Track in both contexts
                await TrackInContext(_dbContext, application, section, controller, action, httpMethod, today);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error tracking endpoint call: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Gets endpoint call counts for a specific application and time range filter
        /// </summary>
        public async Task<GenericResponse> GetEndpointCallsAsync(
            Applications application,
            TimeRangeFilter timeRange)
        {
            // Calculate date range based on the time range filter
            var (startDate, endDate) = CalculateDateRange(timeRange);

            // Get all endpoint calls for the application and date range from both contexts
            var tenantEndpointCalls = await _dbContext.EndpointCallTrackers
                .Where(e =>
                    e.Application == application &&
                    e.Date >= startDate.Date &&
                    e.Date <= endDate.Date)
                .ToListAsync();

            var publicEndpointCalls = await _publicSchemaContext.EndpointCallTrackers
                .Where(e =>
                    e.Application == application &&
                    e.Date >= startDate.Date &&
                    e.Date <= endDate.Date)
                .ToListAsync();

            // Combine the results
            var endpointCalls = tenantEndpointCalls.Concat(publicEndpointCalls).ToList();

            // Group by section
            var sectionGroups = endpointCalls
                .GroupBy(e => e.Section)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Create the response
            var response = new EndpointCallTrackingResponse
            {
                Application = application,
                TimeRange = timeRange,
                StartDate = startDate,
                EndDate = endDate,
                Sections = new List<SectionUsage>()
            };

            // Process each section
            foreach (var section in Enum.GetValues(typeof(ApplicationSection)).Cast<ApplicationSection>())
            {
                var sectionName = section.ToString();

                // Skip if no data for this section
                if (!sectionGroups.ContainsKey(sectionName) && section != ApplicationSection.Other)
                    continue;

                var sectionCalls = sectionGroups.ContainsKey(sectionName)
                    ? sectionGroups[sectionName]
                    : new List<EndpointCallTracker>();

                // Group by month and year
                var monthlyGroups = sectionCalls
                    .GroupBy(e => new { Month = e.Date.Month, Year = e.Date.Year })
                    .ToDictionary(g => g.Key, g => g.Sum(e => e.Count));

                // Create monthly usage data
                var monthlyUsage = new List<MonthlyUsage>();

                // Get all months in the year (1-12)
                // For ThisYear and LastYear, we want all 12 months
                // For other time ranges, we'll still include all 12 months of the current year
                var year = timeRange == TimeRangeFilter.LastYear ? startDate.Year : DateTime.UtcNow.Year;

                for (int month = 1; month <= 12; month++)
                {
                    var key = new { Month = month, Year = year };
                    var calls = monthlyGroups.ContainsKey(key) ? monthlyGroups[key] : 0;

                    monthlyUsage.Add(new MonthlyUsage
                    {
                        Month = month,
                        Year = year,
                        MonthName = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month),
                        Calls = calls
                    });
                }

                // Add section to response
                response.Sections.Add(new SectionUsage
                {
                    SectionName = sectionName,
                    TotalCalls = sectionCalls.Sum(e => e.Count),
                    MonthlyUsage = monthlyUsage
                });
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = response
            };
        }

        /// <summary>
        /// Calculates the date range based on the time range filter
        /// </summary>
        private (DateTime startDate, DateTime endDate) CalculateDateRange(TimeRangeFilter timeRange)
        {
            var now = DateTime.UtcNow;
            var startDate = now;
            var endDate = now;

            switch (timeRange)
            {
                case TimeRangeFilter.ThisMonth:
                    startDate = new DateTime(now.Year, now.Month, 1);
                    endDate = startDate.AddMonths(1).AddDays(-1);
                    break;

                case TimeRangeFilter.LastMonth:
                    startDate = new DateTime(now.Year, now.Month, 1).AddMonths(-1);
                    endDate = new DateTime(now.Year, now.Month, 1).AddDays(-1);
                    break;

                case TimeRangeFilter.ThreeMonthsAgo:
                    startDate = new DateTime(now.Year, now.Month, 1).AddMonths(-3);
                    endDate = startDate.AddMonths(1).AddDays(-1);
                    break;

                case TimeRangeFilter.SixMonthsAgo:
                    startDate = new DateTime(now.Year, now.Month, 1).AddMonths(-6);
                    endDate = startDate.AddMonths(1).AddDays(-1);
                    break;

                case TimeRangeFilter.ThisYear:
                    startDate = new DateTime(now.Year, 1, 1);
                    endDate = new DateTime(now.Year, 12, 31);
                    break;

                case TimeRangeFilter.LastYear:
                    startDate = new DateTime(now.Year - 1, 1, 1);
                    endDate = new DateTime(now.Year - 1, 12, 31);
                    break;
            }

            return (startDate, endDate);
        }

        /// <summary>
        /// Gets all months in the date range
        /// </summary>
        private List<DateTime> GetMonthsInRange(DateTime startDate, DateTime endDate)
        {
            var months = new List<DateTime>();
            var currentDate = new DateTime(startDate.Year, startDate.Month, 1);

            while (currentDate <= endDate)
            {
                months.Add(currentDate);
                currentDate = currentDate.AddMonths(1);
            }

            return months;
        }

        /// <summary>
        /// Helper method to track endpoint call in a specific context
        /// </summary>
        private async Task TrackInContext(
            JobProDbContext context,
            Applications application,
            ApplicationSection section,
            string controller,
            string action,
            string httpMethod,
            DateTime today)
        {
            try
            {
                // Check if there's already a record for this endpoint and date
                var existingRecord = await context.EndpointCallTrackers
                    .FirstOrDefaultAsync(e =>
                        e.Application == application &&
                        e.Section == section.ToString() &&
                        e.Controller == controller &&
                        e.Action == action &&
                        e.HttpMethod == httpMethod &&
                        e.Date.Date == today);

                if (existingRecord != null)
                {
                    // Increment the count
                    existingRecord.Count++;
                    existingRecord.UpdatedAt = DateTime.UtcNow;
                    context.EndpointCallTrackers.Update(existingRecord);
                }
                else
                {
                    // Create a new record
                    var tracker = new EndpointCallTracker
                    {
                        Application = application,
                        Section = section.ToString(),
                        Controller = controller,
                        Action = action,
                        HttpMethod = httpMethod,
                        Date = today,
                        Count = 1,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    await context.EndpointCallTrackers.AddAsync(tracker);
                }

                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error tracking endpoint call in context: {Message}", ex.Message);
                // Don't throw the exception, just log it
            }
        }

        /// <summary>
        /// Gets endpoint call counts grouped by section for a specific application and date range
        /// </summary>
        public async Task<Dictionary<string, int>> GetEndpointCallsBySectionAsync(
            Applications application,
            DateTime startDate,
            DateTime endDate)
        {
            try
            {
                // Get data from tenant context
                var tenantData = await _dbContext.EndpointCallTrackers
                    .Where(e =>
                        e.Application == application &&
                        e.Date >= startDate.Date &&
                        e.Date <= endDate.Date)
                    .GroupBy(e => e.Section)
                    .Select(g => new { Section = g.Key, Count = g.Sum(e => e.Count) })
                    .ToDictionaryAsync(x => x.Section, x => x.Count);

                // Get data from public context
                var publicData = await _publicSchemaContext.EndpointCallTrackers
                    .Where(e =>
                        e.Application == application &&
                        e.Date >= startDate.Date &&
                        e.Date <= endDate.Date)
                    .GroupBy(e => e.Section)
                    .Select(g => new { Section = g.Key, Count = g.Sum(e => e.Count) })
                    .ToDictionaryAsync(x => x.Section, x => x.Count);

                // Combine the results
                var result = new Dictionary<string, int>();

                // Add all sections from tenant data
                foreach (var item in tenantData)
                {
                    result[item.Key] = item.Value;
                }

                // Add or update sections from public data
                foreach (var item in publicData)
                {
                    if (result.ContainsKey(item.Key))
                    {
                        result[item.Key] += item.Value;
                    }
                    else
                    {
                        result[item.Key] = item.Value;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting endpoint calls by section: {Message}", ex.Message);
                return new Dictionary<string, int>();
            }
        }
    }
}
