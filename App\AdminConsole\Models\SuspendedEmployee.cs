﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Models
{
    public class SuspendedEmployee
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserProfileId { get; set; }
        public string UserId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsIndefinite { get; set; }
        public bool IsSuspended { get; set; }
        public string Message { get; set; }
        public DateTime SuspendedOn { get; set; } = DateTime.UtcNow;
        public DateTime? UnSuspendedOn { get; set; }
    }
}
