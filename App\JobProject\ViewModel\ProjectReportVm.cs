﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class ProjectReportVm
    {
        public int TotalProject { get; set; }
        public int ProjectInProgress { get; set; }
        public int TodoInProgress { get; set; }
        public int TodoCompleted { get; set; }
        public int TodoOnHoldCount { get; set; }
        public int PercentageCompleted { get; set; }
        public int PercentageActive { get; set; }
        public int ActiveProject { get; set; }
        public int inActiveProject { get; set; }


    }
}
