﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Models
{
    public class ClientRole
    {
        [Key]
        public Guid Id { get; set; }
        public string RoleName { get; set; }
        public DateTime CreatedAt { get; set; }
        public string  CreatedBy { get; set; }
        public ICollection<ClientRoleRoleModule> ClientRoleRoleModules { get; set; }
        public ICollection<User> user { get; set; }
    }
}
