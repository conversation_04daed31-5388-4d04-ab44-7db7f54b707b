#region Using Statenents
using Jobid.App.Helpers.Context;
using Jobid.App.Tenant.Contract;
using Jobid.App.Tenant.ViewModel;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel.IdentityVM;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers;
using Hangfire;
using Jobid.App.Helpers.Extensions;
using System.Text.RegularExpressions;
using Jobid.App.Subscription.ViewModels;
using static Jobid.App.Subscription.Enums.Enums;
using static Jobid.App.JobProject.Enums.Enums;
using Serilog;
using RabbitMQ.Client;
using Jobid.App.RabbitMQ;
using static Jobid.App.RabbitMQ.Records;
using Microsoft.AspNetCore.Hosting;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.ActivityLog.contract;
using WatchDog;
using AutoMapper;
using Jobid.App.ActivityLog.BackGroundJobs;
using Jobid.App.Calender.Contracts;
using Jobid.App.Subscription.Enums;
using System.Linq.Dynamic.Core;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.JobProject.Services.Contract;
using static Jobid.App.Helpers.Utils.Utility;
using System.Reflection;
using Microsoft.Extensions.Configuration;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Services.Contract;
#endregion

namespace Jobid.App.Tenant.Repository
{
    public class TenantService : ITenantService
    {
        #region Constructor and Properties
        private JobProDbContext Db;
        private JobProDbContext subdomainSchemaContext;
        private readonly int _freePlanDays;
        private readonly IUserServices _userServices;
        private readonly IBackgroundJobService _backgroundJobService;
        private readonly IApiCallService _apiCallService;
        private readonly IMapper _mapper;
        private readonly IActivityBackgroundService _activityBackgroundService;
        private readonly IBackGroundService _calenderBackGroundService;
        private readonly IBackgroungService _adminConsoleBackgroungService;
        private readonly UserManager<User> userManager;
        private readonly IOTPServices otpService;
        private readonly ITenantSchema tenantSchema;
        private readonly IEmailService _emailService;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly ICompanyUserInvite _companyUserInvite;
        private readonly IPublisherService _publisherService;
        private readonly IBackGroundServices _backGroundServices;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger _logger = Log.ForContext<TenantService>();
        private readonly IProductUpdateReceiverService _productUpdateReceiverService;
        private readonly IActivityViewBackgroundService _activityViewBackgroundService;
        private readonly IPhoneNumberMaintenanceService _phoneNumberMaintenanceService;
        private readonly IAdminService _adminService;

        public TenantService(JobProDbContext publicSchemaContext, UserManager<User> _userManager, JobProDbContext subdomainSchemaContext, IOTPServices otpService, ITenantSchema tenantSchema, IEmailService emailService, IAWSS3Sevices aWSS3Sevices, ICompanyUserInvite companyUserInvite, IPublisherService publisherService, IBackGroundServices backGroundServices, IWebHostEnvironment environment, IProductUpdateReceiverService productUpdateReceiverService, IActivityViewBackgroundService activityViewBackgroundService, IBackgroungService adminConsoleBackgroungService, IMapper mapper, IActivityBackgroundService activityBackgroundService, IBackGroundService calenderBackGroundService, IBackgroundJobService backgroundJobService, IApiCallService apiCallService, IPhoneNumberMaintenanceService phoneNumberMaintenanceService, IAdminService adminService)
        {
            this.Db = publicSchemaContext;
            userManager = _userManager;
            this.subdomainSchemaContext = subdomainSchemaContext;
            this.otpService = otpService;
            this.tenantSchema = tenantSchema;
            _emailService = emailService;
            _aWSS3Sevices = aWSS3Sevices;
            _companyUserInvite = companyUserInvite;
            _publisherService = publisherService;
            _backGroundServices = backGroundServices;
            _environment = environment;
            _productUpdateReceiverService = productUpdateReceiverService;
            _activityViewBackgroundService = activityViewBackgroundService;
            _adminConsoleBackgroungService = adminConsoleBackgroungService;
            _mapper = mapper;
            _activityBackgroundService = activityBackgroundService;
            _calenderBackGroundService = calenderBackGroundService;
            _backgroundJobService = backgroundJobService;
            _apiCallService = apiCallService;
            _phoneNumberMaintenanceService = phoneNumberMaintenanceService;
            _adminService = adminService;
        }

        public TenantService(
            JobProDbContext publicSchemaContext,
            JobProDbContext subdomainSchemaContext,
            ITenantSchema tenantSchema,
            IConfiguration configuration)
        {
            this.Db = publicSchemaContext;
            userManager = null;
            this.subdomainSchemaContext = subdomainSchemaContext;
            this.tenantSchema = tenantSchema;
            _freePlanDays = configuration.GetValue<int>("FreePlanDays");
        }
        #endregion

        #region Invite User to Tenant
        /// <summary>
        /// Invite user to tenant
        /// </summary>
        /// <param name="emails"></param>
        /// <param name="userId"></param>
        /// <param name="subDomain"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> InviteUserToTenant([ValidEmailChecks] List<string> emails, string userId, string subDomain)
        {
            // Check if an external user can be invited to the company
            var companyId = Db.Tenants.Where(x => x.Subdomain == subDomain).Select(c => c.Id).FirstOrDefaultAsync().Result;
            var usersWithPermission = subdomainSchemaContext.AppPermissions.Where(x => x.Application == Applications.Joble.ToString()).CountAsync().Result;
            var numberOfInvites = Db.CompanyUserInvites.Where(x => x.TenantId == companyId.ToString() && x.Application == Applications.Joble)
                    .CountAsync().Result;
            var subscriptionFor = Db.Subscriptions.Where(x => x.TenantId == companyId && x.Application == Applications.Joble).Select(c => c.SubscriptionFor).FirstOrDefaultAsync().Result;

            if (subscriptionFor == numberOfInvites + 1 || numberOfInvites + 1 > subscriptionFor)
            {
                throw new InvalidOperationException("External users cannot be invited at ths time, please contact your admin");
            }

            // Check if one of the supplied emails is already registered
            var existingUser = this.Db.Users.Where(x => emails.Contains(x.Email)).FirstOrDefault();
            if (existingUser != null)
                throw new RecordNotFoundException($"{existingUser.Email} is already registered");

            // Get TenantId
            var tenantId = (await GetTenantBySubdomain(subDomain)).Id;

            // Get the invitee name
            var invitee = this.Db.Users.Where(x => x.Id == userId)
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            if (invitee == null)
                throw new RecordNotFoundException("User not found");

            // Add an invite to the database
            foreach (var email in emails)
            {
                var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                {
                    Email = email,
                    Application = Applications.Joble,
                }, tenantId.ToString());
            }

            var inviteUrl = string.Format("https://{0}.jobpro.app/auth/invited-user?invitee={1}&tenantId={2}", subDomain, invitee, tenantId);

            var html = string.Format("<p>Hi ,</p><p>You have been invited by {0} to join {1}. Please click the link below to accept the invitation.</p><p><a href='{2}'>Accept Invitation</a></p><p>Thanks,</p><p>JobPro Team</p>", invitee, subDomain, inviteUrl);

            // Send email to the invitee
            string subject = $"Invitation to {subDomain}";
            var taskId = BackgroundJob.Enqueue(() => _emailService.SendMultipleEmail(html, emails, subject));

            return taskId != null;
        }
        #endregion

        #region Check if Tenant Exists
        /// <summary>
        /// Check if tenant exists
        /// </summary>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<bool> CheckIfTenantExists(string subdomain)
        {
            var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);
            return tenant != null;
        }
        #endregion

        #region Get Tenant By Id
        public async Task<TenantModelVM> GetTenantById(Guid id)
        {
            var tenantInfo = Db.Tenants.FirstOrDefault(x => x.Id == id);
            if (tenantInfo == null) throw new Exception("Tenant not found");
            return tenantInfo.Map();
        }
        #endregion

        #region Get all tenants/companies
        /// <summary>
        ///  Gets all tenants/companies
        /// </summary>
        /// <returns></returns>
        public async Task<GenericResponse> GetAllTenants()
        {
            var tenants = await Db.Tenants.AsNoTracking().ToListAsync();
            var tenantsToReturn = _mapper.Map<List<TenantDetailsVM>>(tenants);

            foreach (var tenant in tenantsToReturn)
            {
                if (!string.IsNullOrEmpty(tenant.LogoUrl))
                {
                    tenant.LogoUrl = Utility.ConvertSignedUrlToBase64(await _aWSS3Sevices.GetSignedUrlAsync(tenant.LogoUrl));
                }

                tenant.Website = "https://www." + tenant.VerifiedEmailDomain;
            }

            return new GenericResponse
            {
                Data = tenantsToReturn,
                ResponseCode = "200",
                ResponseMessage = "Companies retrieved successfully"
            };
        }
        #endregion

        #region Get Tenant By Subdomain
        public async Task<TenantModelVM> GetTenantBySubdomain(string subdomain)
        {
            var tenantInfo = Db.Tenants.AsNoTracking().FirstOrDefault(x => x.Subdomain == subdomain);
            if (tenantInfo == null)
                throw new Exception("Tenant not found");

            if (!string.IsNullOrEmpty(tenantInfo.LogoUrl))
                tenantInfo.LogoUrl = Utility.ConvertSignedUrlToBase64(await _aWSS3Sevices.GetSignedUrlAsync(tenantInfo.LogoUrl));

            return tenantInfo.Map();
        }
        #endregion

        #region Get Tenant Users
        public async Task<ApiResponse<List<UserCompaniesVM>>> GetTenantUsers(string subdomain)
        {
            var response = new ApiResponse<List<UserCompaniesVM>>();
            var tenantInfo = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);
            if (tenantInfo == null)
                tenantInfo = Db.Tenants.FirstOrDefault(x => x.Id == new Guid(subdomain));
            if (tenantInfo == null)
                throw new Exception("Company not found");
            var tenantUsers = Db.UserCompanies.Where(x => x.TenantId == tenantInfo.Id);

            response.Data = tenantUsers.Include(x => x.user).Select(x => x.Map()).ToList();
            response.ResponseCode = "200";
            return response;
        }
        #endregion

        #region Create Tenant
        /// <summary>
        /// Create Tenant
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> RegisterTenant(RegisterTenantVM model)
        {
            var response = new GenericResponse
            {
                Data = false,
                ResponseCode = "400",
                ResponseMessage = ""
            };

            // Check that the country is a string and not a guid
            if (Guid.TryParse(model.companyAdmin.Country, out Guid _))
            {
                response.ResponseCode = "400";
                response.ResponseMessage = "Invalid country, country name is required";
                return response;
            }

            model.companyAdmin.FirstName = (model.companyAdmin.FirstName ?? string.Empty).ToTitleCase();
            model.companyAdmin.LastName = (model.companyAdmin.LastName ?? string.Empty).ToTitleCase();
            if (!string.IsNullOrEmpty(model.companyAdmin.MiddleName))
                model.companyAdmin.MiddleName = model.companyAdmin.MiddleName.ToTitleCase();

            try
            {
                bool containsOnlyAlphabets = Regex.IsMatch(model.tenant.Subdomain.ToLower(), @"^[a-zA-Z]+$");
                if (!containsOnlyAlphabets)
                {
                    response.ResponseCode = "400";
                    response.ResponseMessage = "Subdomain can only contain alphabets";
                    return response;
                }

                bool isFromSocialOauth = false;
                if (!string.IsNullOrEmpty(model.companyAdmin.GoogleAuthToken)) isFromSocialOauth = true;
                if (!string.IsNullOrEmpty(model.companyAdmin.MicrosoftAccessToken)) isFromSocialOauth = true;
                var existingTenant = Db.Tenants.Where(x => x.Subdomain == model.tenant.Subdomain.prepareSubdomainName());
                if (existingTenant.Count() > 0)
                {
                    response.ResponseCode = "409";
                    response.ResponseMessage = "Subdomain already exists.";
                    return response;
                }

                // Check that the email domain does not aleady exist
                var emailDomain = model.companyAdmin.Email.Split('@')[1];
                var existingEmailDomain = Db.Tenants.Where(x => x.VerifiedEmailDomain.ToLower() == emailDomain.ToLower());
                if (existingEmailDomain.Count() > 0)
                {
                    response.ResponseCode = "409";
                    response.ResponseMessage = $"{emailDomain} - domain is not available.";
                    response.DevResponseMessage = $"{emailDomain} - domain is not available. A company with the supplied email domain already exists.";
                    return response;
                }

                if (!string.IsNullOrWhiteSpace(model.companyAdmin.PersonalEmail) && Db.Users.Where(x => x.Email == model.companyAdmin.PersonalEmail).Count() > 0)
                {
                    response.ResponseCode = "409";
                    response.ResponseMessage = "Account with the supplied personal email already exists.";
                    return response;
                }

                if (isFromSocialOauth)
                {
                    if (!string.IsNullOrEmpty(model.companyAdmin.GoogleAuthToken))
                    {
                        var GetUserByGoogleToken = await Utility.GetUserByGoogleToken(model.companyAdmin.GoogleAuthToken);
                        if (GetUserByGoogleToken == null)
                        {
                            response.ResponseMessage = "Invalid Google Authentication request, please try again.";
                            return response;
                        }
                        else if (GetUserByGoogleToken.Email != model.companyAdmin.Email)
                        {
                            response.ResponseMessage = "Invalid request, Personal email address does not match record.";
                            return response;
                        }

                    }
                    else if (!string.IsNullOrEmpty(model.companyAdmin.MicrosoftAccessToken))
                    {
                        MicrosoftUserBasicProfile microsoftUserBasic = await Utility.GetMicrosoftUserModelAsync(model.companyAdmin.MicrosoftAccessToken);
                        if (microsoftUserBasic == null)
                        {
                            response.ResponseMessage = "Invalid Mircosoft Authentication request, please try again.";
                            return response;
                        }
                        else if (microsoftUserBasic.Sub != model.companyAdmin.MicrosoftAuthId)
                        {
                            response.ResponseMessage = "Invalid request, Account mismatch while signing in, please try again.";
                            return response;
                        }
                    }
                }

                var otpIds = new List<string>();
                if (model.ForEnterprizePlan)
                    goto ContinueWithCompanyCreation;

                // verify tenant admin has verfied contact info
                var contactVerification = Db.OTP.FirstOrDefault(x => x.Identifier.ToLower() == model.companyAdmin.Email.ToLower() && x.IdentifierType == OTPIdentifierType.Email.ToString() && x.Status == "verified" && x.TokenType == OTPTokenType.TenantRegistration.ToString());
                if (contactVerification == null && !isFromSocialOauth)
                {
                    // Get the OTP info from the database
                    var otpInfo = Db.OTP.FirstOrDefault(x => x.Identifier.ToLower() == model.companyAdmin.Email.ToLower() && x.IdentifierType == OTPIdentifierType.Email.ToString());

                    if (otpInfo != null)
                        response.ResponseMessage = $"Please verify your email address for company registration.";
                    else
                        response.ResponseMessage = "Please verify your email address for company registration";

                    return response;
                }
                else
                {
                    if (contactVerification != null)
                        otpIds.Add(contactVerification.Id.ToString());
                }

                if (!string.IsNullOrEmpty(model.companyAdmin.PhoneNumber))
                {
                    contactVerification = Db.OTP.FirstOrDefault(x => x.Identifier == model.companyAdmin.PhoneNumber && x.IdentifierType == OTPIdentifierType.Phone.ToString() && x.Status == "verified" && x.TokenType == OTPTokenType.TenantRegistration.ToString());
                    if (contactVerification == null && !isFromSocialOauth)
                    {
                        // Get the OTP info from the database
                        var otpInfo = Db.OTP.FirstOrDefault(x => x.Identifier == model.companyAdmin.PhoneNumber && x.IdentifierType == OTPIdentifierType.Phone.ToString());

                        if (otpInfo != null)
                            response.ResponseMessage = $"Please verify your phone number for company registration.";
                        else
                            response.ResponseMessage = "Please verify your phone number for company registration.";

                        return response;
                    }
                    else
                    {
                        if (contactVerification != null)
                            otpIds.Add(contactVerification.Id.ToString());
                    }
                }

            ContinueWithCompanyCreation: // Label for continuing with company creation

                var tenant = model.tenant.Map();
                tenant.Subdomain = tenant.Subdomain.ToLower();
                tenant.VerifiedEmailDomain = model.companyAdmin.Email.Split('@')[1];
                tenant.DateCreated = DateTime.UtcNow;

                // Get the country region
                var countryRegion = await _apiCallService.GetCountryRegionAsync(model.tenant.Country);
                if (string.IsNullOrEmpty(countryRegion))
                {
                    response.ResponseMessage = "Invalid country name";
                    return response;
                }
                tenant.Region = countryRegion;

                // create subdomain schema
                var newSchemaCreated = await tenantSchema.NewSchema(model.tenant.Subdomain);

                if (!newSchemaCreated)
                {
                    response.ResponseMessage = "Something went wrong, please try again later";
                    response.ResponseCode = "500";
                    response.DevResponseMessage = "Creating schema failed";
                    return response;
                }
                tenant.isSchemaCreated = true;

                // create new Company Admin
                var companyAdmin = createUserModel(model.companyAdmin);
                if (companyAdmin == null)
                {
                    response.ResponseMessage = "Something went wrong, please try again later";
                    response.ResponseCode = "500";
                    return response;
                }

                // update tenant admin
                tenant.AdminId = companyAdmin.Id;

                var jobproId = Utility.RandomString(10);
                var jobproIdExist = await CheckIfJobProIdExist(jobproId);
                while (jobproIdExist)
                {
                    jobproId = Utility.RandomString(10);
                    jobproIdExist = await CheckIfJobProIdExist(jobproId);
                }
                companyAdmin.JobProId = jobproId;

                // upload logo to s3 and save
                if (model.Logo is not null)
                {
                    var fileName = model.tenant.Subdomain + "-Logo." + model.Logo.FileName;
                    tenant.LogoUrl = fileName;
                    var imageUrl = await _aWSS3Sevices.UploadFileAsync(model.Logo, fileName);
                    if (imageUrl == null)
                    {
                        _logger.Error(nameof(RegisterTenant), "Error uploading logo to s3", model);
                    }
                }

                var migrationStatus = await tenantSchema.RunMigrations(tenant.Subdomain);
                if (!migrationStatus)
                {
                    response.ResponseMessage = "Something went wrong, please try again later";
                    response.ResponseCode = "500";
                    response.DevResponseMessage = "Migration failed";
                    return response;
                }
                tenant.LastMigration = DateTime.UtcNow;
                Db.Tenants.Add(tenant);

                // Add secondary domains if there is any
                if (model.tenant.SecDomains.Any())
                {
                    foreach (var secDomain in model.tenant.SecDomains)
                    {
                        var secDomainModel = new SecDomain
                        {
                            Domain = secDomain,
                            CreatedOn = DateTime.UtcNow
                        };
                        Db.SecDomains.Add(secDomainModel);
                    }
                }

                var userCompany = AddUserToCompanyDS(companyAdmin.Id, tenant.Id, model.companyAdmin.Email);
                Db.UserCompanies.Add(userCompany);
                var userProfile = SaveNewUserProfile(companyAdmin, model.companyAdmin.Email, model.companyAdmin.Designation);
                var schemaContext = new JobProDbContext(new DbContextSchema(tenant.Subdomain));

                IdentityResult Result;
                if (string.IsNullOrEmpty(model.companyAdmin.ConfirmPassword))
                {
                    Result = await userManager.CreateAsync(companyAdmin);
                }
                else
                {
                    Result = await userManager.CreateAsync(companyAdmin, model.companyAdmin.ConfirmPassword);
                }

                if (!Result.Succeeded)
                {
                    response.ResponseMessage = "Something went wrong while trying to create the comapny, please try again later";
                    response.ResponseCode = "500";
                    return response;
                }

                // Check if NormalizedUserName and NormalizedEmail need to be updated to uppercase
                bool needsUpdate = false;
                if (companyAdmin.NormalizedUserName == companyAdmin.NormalizedUserName.ToLower())
                {
                    companyAdmin.NormalizedUserName = companyAdmin.NormalizedUserName.ToUpper();
                    needsUpdate = true;
                }

                if (companyAdmin.NormalizedEmail == companyAdmin.NormalizedEmail.ToLower())
                {
                    companyAdmin.NormalizedEmail = companyAdmin.NormalizedEmail.ToUpper();
                    needsUpdate = true;
                }

                if (needsUpdate)
                {
                    Db.Users.Update(companyAdmin);
                    var publicDbResult = await Db.SaveChangesAsync();
                }

                // Seed Data
                await DataSeeder.SeedData(model.tenant.Subdomain.ToLower(), model.tenant.Application.ToString());

                // Add user to a role
                var role = "Super Admin";
                if (!await AddUserToRole(companyAdmin.Id, role, model.tenant.Application.ToString(), schemaContext))
                {
                    // Delete company admin
                    await userManager.DeleteAsync(companyAdmin);
                    response.ResponseCode = "500";
                    response.ResponseMessage = "Something went wrong, please try again later";
                    response.DevResponseMessage = "Creating role for super admin failed";
                    return response;
                }

                // initiate a db context pointing to the subdomain to save user profile
                schemaContext.UserProfiles.Add(userProfile);
                int userProfileResult = await schemaContext.SaveChangesAsync();
                if (userProfileResult < 1)
                {
                    // delete user account
                    var user = this.Db.Users.FirstOrDefault(x => x.Id == companyAdmin.Id);
                    this.Db.Users.Remove(user);
                    await Db.SaveChangesAsync();

                    response.ResponseMessage = "Something went wrong, please try again later.";
                    response.ResponseCode = "500";
                    response.DevResponseMessage = "Creating user profile failed";
                    return response;
                }

                if (!model.ForEnterprizePlan)
                {
                    // Update the otps
                    if (otpIds.Count > 0)
                    {
                        foreach (var otpId in otpIds)
                        {
                            await this.otpService.MarkTokenAsUsed(otpId);
                        }
                    }
                }

                // Add App permission for super admin
                var appPermission = new AppPermissions(true)
                {
                    UserId = companyAdmin.Id,
                    Application = model.tenant.Application.ToString(),
                    IsEnabled = true,
                    SubscriptionStatus = SubscriptionStatus.Active.ToString(),
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    TenantId = tenant.Id.ToString()
                };

                schemaContext.AppPermissions.Add(appPermission);
                var result = await schemaContext.SaveChangesAsync();

                // Publish an 'employeecreated' event for chat service to consume
                var superAdminUser = await userManager.FindByIdAsync(companyAdmin.Id);
                var userVm = superAdminUser.MapToUserMDVm();
                userVm.TenantId = tenant.Id.ToString();
                userVm.Subdomain = tenant.Subdomain;

                var eventModel = new PublishModel
                (
                    RabbitMQConstants.UserCreatedEvent,
                    "",
                    ExchangeType.Fanout,
                    userVm
                );

                var eventRes = await _publisherService.GenericPublish(eventModel);
                if (!eventRes)
                {
                    WatchLogger.LogError("Employee onboarded but event could not be published", "Error", "TenantService", "RegisterTenant");
                    _logger.Error("Employee onboarded but event could not be published");
                }

                // Publish a 'TenantCreated' event for chat service to consume
                var tenantDtoToPublish = _mapper.Map<TenantDetailsVM>(tenant);
                eventModel = new PublishModel
                (
                    RabbitMQConstants.TenantCreatedEvent,
                    "",
                    ExchangeType.Fanout,
                    tenantDtoToPublish
                );

                eventRes = await _publisherService.GenericPublish(eventModel);
                if (!eventRes)
                {
                    WatchLogger.LogError("Tenant Created but event could not be published", "Error", "TenantService", "RegisterTenant");
                    _logger.Error("Tenant Created but event could not be published");
                }

                // Update background job for joble
                var hangFireScheduller = new HangFireJobScheduler(_backGroundServices, _productUpdateReceiverService, _activityViewBackgroundService, _adminConsoleBackgroungService, _activityBackgroundService, _calenderBackGroundService, _phoneNumberMaintenanceService);
                hangFireScheduller.ScheduleRecurringJobs();
                hangFireScheduller.ScheduleOneTimeJobs();

                // Send mail with login credentials to superadmin if the company was created by an admin
                if (model.ForEnterprizePlan)
                {
                    // Send a welcome mail with login credentials to the super admin
                    var template = Extensions.ReadTemplateFromFile("welcome_email_admin", _environment);
                    template = template.Replace("{name}", superAdminUser.FirstName).Replace("{username}", superAdminUser.Email)
                        .Replace("{password}", model.companyAdmin.Password);
                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, model.companyAdmin.Email, "Welcome to JobPro"));

                    response.Data = new CompanyCreationResponseDto
                    {
                        TenantId = tenant.Id.ToString(),
                        SuperAdminUserId = companyAdmin.Id
                    };
                }
                else
                {
                    response.Data = null;

                    // Send welcome email
                    var template = "";
                    if (model.tenant.Application == Applications.Joble)
                    {
                        template = Extensions.ReadTemplateFromFile("welcome-email-joble", _environment);
                    }
                    else
                    {
                        template = Extensions.ReadTemplateFromFile("welcome_email", _environment);
                        template = template.Replace("{name}", superAdminUser.FirstName);
                    }

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, model.companyAdmin.Email, "Welcome to JobPro"));

                    // Send free license activation email
                    BackgroundJob.Enqueue(() => SendFreeActivationEmail(
                        superAdminUser.FirstName,
                        superAdminUser.Email,
                        model.tenant.Application));

                    // Trgger a reoccuring job to send a notification to the super admin to upgrade their plan after free trial ends
                    var sendTrialEndedMailDto = new SendTrialEndedMailDto
                    {
                        Email = superAdminUser.Email,
                        Name = superAdminUser.FirstName,
                        TenantId = tenant.Id.ToString(),
                        App = model.tenant.Application
                    };

                    BackgroundJob.Schedule(() => SendFreeTrialEndNotification(sendTrialEndedMailDto), TimeSpan.FromDays(14));
                }

                response.ResponseMessage = "Company created successfully";
                response.ResponseCode = "200";
                return response;
            }
            catch (Exception ex)
            {
                _logger.Error(nameof(RegisterTenant), "Error registering tenant", ex);
                response.ResponseMessage = "Something went wrong, please try again later";
                response.DevResponseMessage = ex.ToString();
                response.ResponseCode = "500";
                return response;
            }
        }
        #endregion

        #region Register company for existing user
        /// <summary>
        ///  This creates a company for existing users
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> CreateCompanyForExistingUser(CreateTenantForExistingUserVM model)
        {
            var response = new GenericResponse
            {
                Data = false,
                ResponseCode = "400",
                ResponseMessage = ""
            };

            // Check the supplied userId is correct
            var adminDetails = await Db.Users.FirstOrDefaultAsync(x => x.Id == model.UserId);
            if (adminDetails is null)
            {
                response.ResponseCode = "400";
                response.ResponseMessage = "An error occured, please try again later";
                response.DevResponseMessage = "Invalid user ID";

                return response;
            }

            // Check that the country is a string and not a guid
            if (Guid.TryParse(model.Tenant.Country, out Guid _))
            {
                response.ResponseCode = "400";
                response.ResponseMessage = "Invalid country, country name is required";
                return response;
            }

            bool containsOnlyAlphabets = Regex.IsMatch(model.Tenant.Subdomain.ToLower(), @"^[a-zA-Z]+$");
            if (!containsOnlyAlphabets)
            {
                response.ResponseCode = "400";
                response.ResponseMessage = "Subdomain can only contain alphabets";
                return response;
            }

            var existingTenant = await Db.Tenants
                .Where(x => x.Subdomain == model.Tenant.Subdomain.prepareSubdomainName()).FirstOrDefaultAsync();
            if (existingTenant != null)
            {
                response.ResponseCode = "409";
                response.ResponseMessage = "Subdomain already exists.";
                return response;
            }

            // Check that the email domain does not aleady exist
            var emailDomain = model.CompanyEmail.Split('@')[1];
            var existingEmailDomain = await Db.Tenants
                .Where(x => x.VerifiedEmailDomain.ToLower() == emailDomain.ToLower()).FirstOrDefaultAsync();
            if (existingEmailDomain != null)
            {
                response.ResponseCode = "409";
                response.ResponseMessage = $"{emailDomain} - domain is not available.";
                response.DevResponseMessage = $"{emailDomain} - domain is not available. A company with the supplied email domain already exists.";
                return response;
            }


            var otpIds = new List<string>();
            if (model.ForEnterprizePlan)
                goto ContinueWithCompanyCreation;

            // verify tenant admin has verfied contact info
            var contactVerification = Db.OTP.FirstOrDefault(x => x.Identifier.ToLower() == model.CompanyEmail.ToLower() && x.IdentifierType == OTPIdentifierType.Email.ToString() && x.Status == "verified" && x.TokenType == OTPTokenType.TenantRegistration.ToString());
            if (contactVerification == null)
            {
                // Get the OTP info from the database
                var otpInfo = Db.OTP.FirstOrDefault(x => x.Identifier.ToLower() == model.CompanyEmail.ToLower() && x.IdentifierType == OTPIdentifierType.Email.ToString());

                if (otpInfo != null)
                    response.ResponseMessage = $"Please verify your email address for company registration.";
                else
                    response.ResponseMessage = "Please verify your email address for company registration";

                return response;
            }
            else
            {
                if (contactVerification != null)
                    otpIds.Add(contactVerification.Id.ToString());
            }

        ContinueWithCompanyCreation: // Label for continuing with company creation
            var tenant = model.Tenant.Map();
            tenant.Subdomain = tenant.Subdomain.ToLower();
            tenant.VerifiedEmailDomain = emailDomain;
            tenant.DateCreated = DateTime.UtcNow;

            // Get the country region
            var countryRegion = await _apiCallService.GetCountryRegionAsync(model.Tenant.Country);
            if (string.IsNullOrEmpty(countryRegion))
            {
                response.ResponseMessage = "Invalid country name";
                return response;
            }
            tenant.Region = countryRegion;

            // create subdomain schema
            var newSchemaCreated = await tenantSchema.NewSchema(model.Tenant.Subdomain);

            if (!newSchemaCreated)
            {
                response.ResponseMessage = "Something went wrong, please try again later";
                response.ResponseCode = "500";
                response.DevResponseMessage = "Creating schema failed";
                return response;
            }

            tenant.isSchemaCreated = true;
            tenant.AdminId = model.UserId;

            // upload logo to s3 and save
            if (model.Logo is not null)
            {
                var fileName = model.Tenant.Subdomain + "-Logo." + model.Logo.FileName;
                tenant.LogoUrl = fileName;
                var imageUrl = await _aWSS3Sevices.UploadFileAsync(model.Logo, fileName);
                if (imageUrl == null)
                {
                    _logger.Error(nameof(CreateCompanyForExistingUser), "Error uploading logo to s3", model);
                }
            }

            var migrationStatus = await tenantSchema.RunMigrations(tenant.Subdomain);
            if (!migrationStatus)
            {
                response.ResponseMessage = "Something went wrong, please try again later";
                response.ResponseCode = "500";
                response.DevResponseMessage = "Migration failed";
                return response;
            }
            tenant.LastMigration = DateTime.UtcNow;
            Db.Tenants.Add(tenant);

            // Add secondary domains if there is any
            if (model.Tenant.SecDomains.Any())
            {
                foreach (var secDomain in model.Tenant.SecDomains)
                {
                    var secDomainModel = new SecDomain
                    {
                        Domain = secDomain,
                        CreatedOn = DateTime.UtcNow
                    };
                    Db.SecDomains.Add(secDomainModel);
                }
            }

            var publicDbResut = await Db.SaveChangesAsync();
            if (publicDbResut < 1)
            {
                response.ResponseMessage = "Something went wrong, please try again later";
                response.ResponseCode = "500";
                response.DevResponseMessage = "Creating company failed";
                return response;
            }

            var userCompany = AddUserToCompanyDS(model.UserId, tenant.Id, model.CompanyEmail);
            Db.UserCompanies.Add(userCompany);

            // Create a profile for the admin on the new company sb schema
            var userProfile = SaveNewUserProfile(adminDetails, model.CompanyEmail);
            var schemaContext = new JobProDbContext(new DbContextSchema(tenant.Subdomain));

            // Seed Data
            await DataSeeder.SeedData(model.Tenant.Subdomain.ToLower(), model.Tenant.Application.ToString());

            // Add user to a role
            var role = "Super Admin";
            if (!await AddUserToRole(model.UserId, role, model.Tenant.Application.ToString(), schemaContext))
            {
                response.ResponseCode = "500";
                response.ResponseMessage = "Something went wrong, please try again later";
                response.DevResponseMessage = "Creating role for super admin failed";
                return response;
            }

            // initiate a db context pointing to the subdomain to save user profile
            schemaContext.UserProfiles.Add(userProfile);
            int userProfileResult = await schemaContext.SaveChangesAsync();
            if (userProfileResult < 1)
            {
                // delete tenant account
                var tenantToDelete = Db.Tenants.FirstOrDefault(x => x.Id == tenant.Id);
                Db.Tenants.Remove(tenantToDelete);
                await Db.SaveChangesAsync();

                response.ResponseMessage = "Something went wrong, please try again later.";
                response.ResponseCode = "500";
                response.DevResponseMessage = "Creating user profile failed";
                return response;
            }

            if (!model.ForEnterprizePlan)
            {
                // Update the otps
                if (otpIds.Count > 0)
                {
                    foreach (var otpId in otpIds)
                    {
                        await this.otpService.MarkTokenAsUsed(otpId);
                    }
                }
            }

            // Publish an 'employeecreated' event for chat service to consume
            var superAdminUser = await userManager.FindByIdAsync(model.UserId);
            var userVm = superAdminUser.MapToUserMDVm();
            userVm.TenantId = tenant.Id.ToString();
            userVm.Subdomain = tenant.Subdomain;

            var eventModel = new PublishModel
            (
                RabbitMQConstants.UserCreatedEvent,
                "",
                ExchangeType.Fanout,
                userVm
            );

            var eventRes = await _publisherService.GenericPublish(eventModel);
            if (!eventRes)
            {
                WatchLogger.LogError("Employee onboarded but event could not be published", "Error", "TenantService", "RegisterTenant");
                _logger.Error("Employee onboarded but event could not be published");
            }

            // Publish a 'TenantCreated' event for chat service to consume
            var tenantDtoToPublish = _mapper.Map<TenantDetailsVM>(tenant);
            eventModel = new PublishModel
            (
                RabbitMQConstants.TenantCreatedEvent,
                "",
                ExchangeType.Fanout,
                tenantDtoToPublish
            );

            eventRes = await _publisherService.GenericPublish(eventModel);
            if (!eventRes)
            {
                WatchLogger.LogError("Tenant Created but event could not be published", "Error", "TenantService", "RegisterTenant");
                _logger.Error("Tenant Created but event could not be published");
            }

            // Update background job for joble
            var hangFireScheduller = new HangFireJobScheduler(_backGroundServices, _productUpdateReceiverService, _activityViewBackgroundService, _adminConsoleBackgroungService, _activityBackgroundService, _calenderBackGroundService, _phoneNumberMaintenanceService);
            hangFireScheduller.ScheduleRecurringJobs();
            hangFireScheduller.ScheduleOneTimeJobs();

            response.Data = new CompanyCreationResponseDto
            {
                TenantId = tenant.Id.ToString(),
                SuperAdminUserId = model.UserId
            };

            // Send welcome email
            var template = "";
            if (model.Tenant.Application == Applications.Joble)
            {
                template = Extensions.ReadTemplateFromFile("welcome-email-joble", _environment);
            }
            else
            {
                template = Extensions.ReadTemplateFromFile("welcome_email", _environment);
                template = template.Replace("{name}", superAdminUser.FirstName);
            }

            BackgroundJob.Enqueue(() => _emailService.SendEmail(template, model.CompanyEmail, "Welcome to JobPro"));

            // Send free license activation email
            BackgroundJob.Enqueue(() => SendFreeActivationEmail(
                superAdminUser.FirstName,
                superAdminUser.Email,
                model.Tenant.Application));

            // Trgger a reoccuring job to send a notification to the super admin to upgrade their plan after free trial ends
            var sendTrialEndedMailDto = new SendTrialEndedMailDto
            {
                Email = superAdminUser.Email,
                Name = superAdminUser.FirstName,
                TenantId = tenant.Id.ToString(),
                App = model.Tenant.Application
            };

            BackgroundJob.Schedule(() => SendFreeTrialEndNotification(sendTrialEndedMailDto), TimeSpan.FromDays(14));

            response.ResponseMessage = "Company created successfully";
            response.ResponseCode = "200";
            return response;
        }
        #endregion

        #region Update Tenant Details
        public async Task<GenericResponse> UpdateTenantDetails(UpdateTenantDto model)
        {
            var response = new GenericResponse();
            try
            {
                var tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Id == model.Id);
                if (tenant == null)
                {
                    response.ResponseMessage = "Tenant not found";
                    response.ResponseCode = "404";
                    return response;
                }

                tenant = model.MapToTenant(tenant);
                tenant.LastUpdate = DateTime.UtcNow;

                if (model.logo != null)
                {
                    var fileName = tenant.Subdomain + "-Logo." + model.logo.FileName;
                    tenant.LogoUrl = fileName;
                    var imageUrl = await _aWSS3Sevices.UploadFileAsync(model.logo, fileName);
                    if (imageUrl == null)
                    {
                        _logger.Error(nameof(UpdateTenantDetails), "Error uploading logo to s3", model);
                    }

                    tenant.LogoUrl = fileName;
                }

                Db.Tenants.Update(tenant);
                var result = await Db.SaveChangesAsync();

                if (result > 0)
                {
                    response.ResponseMessage = "Tenant updated successfully";
                    response.ResponseCode = "200";
                    response.Data = tenant;
                    return response;
                }
                else
                {
                    response.ResponseMessage = "Tenant update failed";
                    response.ResponseCode = "500";
                    response.Data = false;
                    return response;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(nameof(UpdateTenantDetails), "Error getting tenant details", ex);
                response.ResponseMessage = "Something went wrong, please try again later";
                response.ResponseCode = "500";
                return response;
            }
        }
        #endregion

        #region Add user to a company
        /// <summary>
        /// This method is used to add user to a company but first cheack if that the user does not exist on that company
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="tenantId"></param>
        /// <param name="companyEmail"></param>
        /// <returns></returns>
        public async Task<UserCompanies> AddUserToCompany(string userId, Guid tenantId, string companyEmail)
        {
            // First check if the user already exists on the usercompanies table
            var userExists = await Db.UserCompanies.FirstOrDefaultAsync(x => x.UserId == userId && x.TenantId == tenantId);
            if (userExists != null)
                return userExists;

            // create relationship between user and company
            var userCompany = new UserCompanies
            {
                Active = true,
                DateCreated = DateTime.UtcNow,
                TenantId = tenantId,
                UserId = userId,
                Email = companyEmail,
                StartDate = DateTime.UtcNow
            };

            await Db.UserCompanies.AddAsync(userCompany);
            var res = await Db.SaveChangesAsync();

            return res > 0 ? userCompany : null;
        }
        #endregion

        #region Check if user already exists on the tenant
        /// <summary>
        /// Check if user already exists on the tenant
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<bool> UserExisitsOnCompanySchema(string userId, string email = null)
        {
            var user = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (email is not null)
            {
                user = await subdomainSchemaContext.UserProfiles.FirstOrDefaultAsync(x => x.Email.ToLower() == email.ToLower());
            }

            return user != null ? true : false;
        }
        #endregion

        #region Add user to app permission
        /// <summary>
        /// Add user to app permission
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="app"></param>
        /// <param name="tenantId"></param>
        /// <param name="agent"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        public async Task<bool> AddUserToAppPermission(string userId, string app, string loggedInUserId, string tenantId = null, AIAgents? agent = null)
        {
            var subscription = new Subscription.Models.Subscription();

            // Get userprofile count
            var userProfileCount = await subdomainSchemaContext.UserProfiles.CountAsync();

            // Check if the user already have permission for the app under the tenant
            var company = await Db.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == tenantId);
            var hasExistingAppPermission = false;
            AppPermissions appPermission;
            SubscriptionStatus subscriptionStatus;
            var usersWithActiveSubCount = 0;
            bool? liencensFinished = null;
            if (agent == null)
                appPermission = await subdomainSchemaContext.AppPermissions
                    .Where(a => a.UserId == userId && a.TenantId == tenantId && a.Application == app).FirstOrDefaultAsync();
            else
                appPermission = await subdomainSchemaContext.AppPermissions
                     .Where(a => a.UserId == userId && a.TenantId == tenantId && a.Application == app && a.Agent == agent).FirstOrDefaultAsync();

            if (appPermission is null)
            {
                appPermission = new AppPermissions(true)
                {
                    IsEnabled = false,
                    TenantId = tenantId,
                    UserId = userId,
                    Application = app,
                    SubscriptionStatus = SubscriptionStatus.Inactive.ToString(),
                    Agent = agent
                };
            }
            else
                hasExistingAppPermission = true;

            // Perse the app to Applications enum
            Enum.TryParse(app, out Applications application);

            if (tenantId != null)
                subscription = await Db.Subscriptions.FirstOrDefaultAsync(x => x.TenantId.ToString() == tenantId && x.Application == application);

            if (subscription != null)
            {
                // Get comapany subscription status
                if (agent is null)
                    subscriptionStatus = await Db.CompanySubscriptions
                        .Where(x => x.TenantId.ToString() == tenantId && x.Application == application).Select(s => s.Status).FirstOrDefaultAsync();
                else
                    subscriptionStatus = await Db.CompanySubscriptions
                        .Where(x => x.TenantId.ToString() == tenantId && x.Application == application && x.AIAgent == agent).Select(s => s.Status).FirstOrDefaultAsync();

                if (subscriptionStatus == SubscriptionStatus.Active)
                {
                    liencensFinished = true;

                    // Get users under the comapny that have active subscription for the same appplication
                    if (agent is null)
                        usersWithActiveSubCount = await subdomainSchemaContext.AppPermissions
                            .CountAsync(x => x.Application == app && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.IsEnabled == true && x.TenantId == tenantId);
                    else
                        usersWithActiveSubCount = await subdomainSchemaContext.AppPermissions
                           .CountAsync(x => x.Application == app && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.IsEnabled == true && x.TenantId == tenantId && x.Agent == agent);

                    if (agent == null)
                    {
                        // Get the plan name
                        var planName = await Db.PricingPlans.Where(x => x.Id == subscription.PricingPlanId)
                            .Select(p => p.Name).FirstOrDefaultAsync();
                        if (planName != PricingPlans.Enterprise.ToString() && subscription.SubscriptionFor > usersWithActiveSubCount)
                        {
                            liencensFinished = false;
                            appPermission.IsEnabled = true;
                            appPermission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                        }

                        if (planName == PricingPlans.Enterprise.ToString() && subscription.SubscriptionFor == 0)
                        {
                            liencensFinished = false;
                            appPermission.IsEnabled = true;
                            appPermission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                        }

                        if (planName == PricingPlans.Enterprise.ToString() && subscription.SubscriptionFor != 0 && subscription.SubscriptionFor > usersWithActiveSubCount)
                        {
                            liencensFinished = false;
                            appPermission.IsEnabled = true;
                            appPermission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                        }
                    }
                    else
                    {
                        var agentDetails = await Db.AISubscriptionDetails.Where(x => x.Agent == agent && x.SubscriptionId == subscription.Id).FirstOrDefaultAsync();
                        if (agentDetails != null)
                        {
                            if (agentDetails.NoOfUserSubscribedFor > usersWithActiveSubCount)
                            {
                                liencensFinished = false;
                                appPermission.IsEnabled = true;
                                appPermission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
                            }
                        }
                    }
                }
                else
                    throw new InvalidOperationException("You do not have an active subscription");
            }
            else
            {
                //if (DateTime.UtcNow > company.DateCreated.AddDays(_freePlanDays))
                //    throw new InvalidOperationException("You do not have an active subscription");

                if (userProfileCount > 3)
                    throw new InvalidOperationException("You do not have an active subscription. Please subscribe to a plan to be able to add more users");

                appPermission.IsEnabled = true;
                appPermission.SubscriptionStatus = SubscriptionStatus.Active.ToString();
            }

            if (hasExistingAppPermission)
            {
                appPermission.UpdatedAt = DateTime.UtcNow;
                appPermission.UpdatedBy = loggedInUserId;
                subdomainSchemaContext.AppPermissions.Update(appPermission);
            }
            else
            {
                await subdomainSchemaContext.AppPermissions.AddAsync(appPermission);
            }

            var res = await subdomainSchemaContext.SaveChangesAsync();

            if (liencensFinished.HasValue && liencensFinished.Value)
                throw new InvalidOperationException("You do not have any available liecense. Purchase more liecense to be able to give more users Full Access");

            return res > 0;
        }
        #endregion

        #region Downgrade user to basic access
        /// <summary>
        /// Downgrade user to basic access from full access
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> DowngradeUserToBasicAccess(DowngradeUserToBasicAccessDto model)
        {
            AppPermissions appPermission;
            if (model.Agent == null)
                appPermission = await subdomainSchemaContext.AppPermissions
                    .Where(a => a.UserId == model.UserId && a.TenantId == model.TenantId && a.Application == model.App && a.IsEnabled && a.SubscriptionStatus == SubscriptionStatus.Active.ToString()).FirstOrDefaultAsync();
            else
                appPermission = await subdomainSchemaContext.AppPermissions
                     .Where(a => a.UserId == model.UserId && a.TenantId == model.TenantId && a.Application == model.App && a.Agent == model.Agent && a.IsEnabled && a.SubscriptionStatus == SubscriptionStatus.Active.ToString()).FirstOrDefaultAsync();

            if (appPermission is null)
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "User does not have full access priviledge",
                    Data = false
                };

            appPermission.UpdatedBy = model.LoggedInUserId;
            appPermission.UpdatedAt = DateTime.UtcNow;
            appPermission.IsEnabled = false;
            appPermission.SubscriptionStatus = SubscriptionStatus.Inactive.ToString();

            subdomainSchemaContext.AppPermissions.Update(appPermission);
            var res = await subdomainSchemaContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = res > 0 ? "200" : "500",
                ResponseMessage = res > 0 ? "User downgraded successfully" : "Failed to downgrade user",
                Data = res > 0 ? true : false
            };
        }
        #endregion

        #region Revoke User Access
        /// <summary>
        /// This method is used to revoke user access to an application
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<GenericResponse> RevokeUserAccess(RevokeUserAccessDto model)
        {
            AppPermissions appPermission;
            if (model.Agent == null)
                appPermission = await subdomainSchemaContext.AppPermissions
                    .Where(a => a.UserId == model.UserId && a.TenantId == model.TenantId && a.Application == model.App).FirstOrDefaultAsync();
            else
                appPermission = await subdomainSchemaContext.AppPermissions
                     .Where(a => a.UserId == model.UserId && a.TenantId == model.TenantId && a.Application == model.App && a.Agent == model.Agent).FirstOrDefaultAsync();

            if (appPermission is null)
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "User does not have access",
                    Data = false
                };

            subdomainSchemaContext.AppPermissions.Remove(appPermission);
            var res = await subdomainSchemaContext.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = res > 0 ? "200" : "500",
                ResponseMessage = res > 0 ? "User access revoked successfully" : "Failed to revoke user access",
                Data = res > 0 ? true : false
            };
        }
        #endregion

        #region Remove a user from workspace
        /// <summary>
        /// Remove a user from workspace
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<GenericResponse> DeleteUserFromCompany(string userId, string subdomain)
        {
            using var transaction = await Db.Database.BeginTransactionAsync();
            try
            {
                var tenant = await Db.Tenants
                    .FirstOrDefaultAsync(x => x.Subdomain == subdomain);

                if (tenant == null)
                    throw new InvalidOperationException("Tenant not found");

                var userCompany = await Db.UserCompanies
                    .FirstOrDefaultAsync(x => x.UserId == userId && x.TenantId == tenant.Id);

                if (userCompany != null)
                    Db.UserCompanies.Remove(userCompany);

                var permissionsToDelete = await subdomainSchemaContext.AppPermissions
                    .Where(x => x.UserId == userId && x.TenantId == tenant.Id.ToString())
                    .ToListAsync();

                if (permissionsToDelete.Any())
                    subdomainSchemaContext.AppPermissions.RemoveRange(permissionsToDelete);

                // Delete user profile
                var userProfile = await subdomainSchemaContext.UserProfiles
                    .FirstOrDefaultAsync(x => x.UserId == userId);

                if (userProfile != null)
                    subdomainSchemaContext.UserProfiles.Remove(userProfile);

                // Save changes to both contexts in one go
                await Task.WhenAll(
                    Db.SaveChangesAsync(),
                    subdomainSchemaContext.SaveChangesAsync()
                );

                await transaction.CommitAsync();
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "User removed successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                return new GenericResponse
                {
                    ResponseCode = "500",
                    DevResponseMessage = $"Failed to remove user: {ex.Message}",
                    ResponseMessage = "Something went wrong, please try again later",
                };
            }
        }
        #endregion

        #region Get user permitted apps
        /// <summary>
        /// This method is maily used by mobile app to get the apps that a user has permission to access
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetUserPermittedApps(string userId, string subdomain)
        {
            var response = new GenericResponse();
            if (subdomain != "api")
            {
                var userApps = await subdomainSchemaContext.AppPermissions
                    .Where(x => x.UserId == userId && x.IsEnabled && x.SubscriptionStatus == SubscriptionStatus.Active.ToString())
                    .Select(x => x.Application).ToListAsync();

                response.Data = userApps;
            }
            else
            {
                var userApps = await Db.AppPermissions
                    .Where(x => x.UserId == userId && x.IsEnabled && x.SubscriptionStatus == SubscriptionStatus.Active.ToString())
                    .Select(x => x.Application).ToListAsync();

                response.Data = userApps;
            }

            response.ResponseCode = "200";
            return response;
        }
        #endregion

        #region Get user app permission
        /// <summary>
        /// Get user app permission
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="application"></param>
        /// <param name="tenantId"></param>
        /// <param name="agent"></param>
        /// <returns></returns>
        public async Task<AppPermissions> GetUserAppPermission(string userId, string application, string tenantId = null, AIAgents? agent = null)
        {
            var permission = await subdomainSchemaContext.AppPermissions
                .Where(x => x.UserId == userId && x.Application == application && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString())
                .FirstOrDefaultAsync();

            if (tenantId != null)
            {
                if (agent == null)
                {
                    permission = await subdomainSchemaContext.AppPermissions
                        .Where(x => x.UserId == userId && x.Application == application && x.TenantId == tenantId.ToString() && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString())
                        .FirstOrDefaultAsync();
                }
                else
                {
                    permission = await subdomainSchemaContext.AppPermissions
                        .Where(x => x.UserId == userId && x.Application == application && x.TenantId == tenantId.ToString() && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.Agent == agent)
                        .FirstOrDefaultAsync();
                }
            }

            return permission;
        }
        #endregion

        #region Get user app permissions
        /// <summary>
        /// Get user app permissions
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="context"></param>
        /// <param name="tenantId"></param>
        /// <param name="getFavorites"></param>
        /// <returns></returns>
        public async Task<List<AppPermissions>> GetUserAppPermissions(string userId, bool getFavorites, string tenantId = null, JobProDbContext context = null)
        {
            if (context != null)
                if (getFavorites)
                    return await context.AppPermissions
                        .Where(x => x.UserId == userId && x.TenantId == tenantId && x.IsEnabled == true && x.MakeFavorite == getFavorites).ToListAsync();
                else
                    return await context.AppPermissions
                        .Where(x => x.UserId == userId && x.TenantId == tenantId && x.IsEnabled == true).ToListAsync();

            if (tenantId is null)
                if (getFavorites)
                    return await Db.AppPermissions
                        .Where(x => x.UserId == userId && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.MakeFavorite == getFavorites).ToListAsync();
                else
                    return await Db.AppPermissions.Where(x => x.UserId == userId && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString()).ToListAsync();

            if (getFavorites)
                return await subdomainSchemaContext.AppPermissions.Where(x => x.UserId == userId && x.TenantId == tenantId && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.MakeFavorite == getFavorites).ToListAsync();
            else
                return await subdomainSchemaContext.AppPermissions.Where(x => x.UserId == userId && x.TenantId == tenantId && x.IsEnabled == true && x.SubscriptionStatus == SubscriptionStatus.Active.ToString()).ToListAsync();
        }
        #endregion

        #region Get UserCompanies Model
        private UserCompanies AddUserToCompanyDS(string userId, Guid tenantId, string companyEmail)
        {
            // create relationship between user and company
            var userCompany = new UserCompanies
            {
                Active = true,
                DateCreated = DateTime.UtcNow,
                TenantId = tenantId,
                UserId = userId,
                StartDate = DateTime.UtcNow,
                Email = companyEmail
            };

            return userCompany;
        }
        #endregion

        #region Get User Tenant
        public async Task<TenantModelVM> GetUserTenant(string userId)
        {
            // create relationship between user and company
            var userCompany = await Db.UserCompanies.FirstOrDefaultAsync(x => x.UserId == userId);
            if (userCompany == null)
            {
                return null;
            }
            return Db.Tenants.FirstOrDefault(x => x.Id == userCompany.TenantId).Map();
        }
        #endregion

        #region Create User
        private User createUserModel(CompanyAdminModel Model)
        {
            User NewUser = new User
            {
                Email = Model.PersonalEmail,
                UserName = Model.PersonalEmail,
                FirstName = Model.FirstName,
                LastName = Model.LastName,
                MiddleName = Model.MiddleName,
                Created_At = DateTime.UtcNow,
                PhoneNumber = Model.PhoneNumber,
                PhoneCountryCode = Model.PhoneCountryCode,
                Country = Model.Country,
                CountryCode = Model.CountryCode,
                State = Model.State,
                IpAddress = Model.IpAddress,
                ZipCode = Model.ZipCode,
                DateOfBirth = Model.DateOfBirth,
                BaseCurrency = Model.BaseCurrency,
                Address = Model.Address,
                Status = "Active",
                StatusComment = null,
                InvitedBy = null,
                CV_URL = null,
                OldReference = null,
                NewReference = null,
                PasswordCreatedByAdmin = false,
                ClientRoleId = Db.ClientRoles.FirstOrDefault(x => x.RoleName.ToLower() == "administrator")?.Id,
                Region = "HQ",
                Id = Guid.NewGuid().ToString(),
                GoogleAuthId = Model.GoogleAuthId,
                MicrosoftAuthId = Model.MicrosoftAuthId,
                UserType = UserTypes.CompanyAdmin
            };

            return NewUser;
        }
        #endregion

        #region Create User Profile
        public UserProfile SaveNewUserProfile(User user, string companyEmail, string designation = null)
        {
            UserProfile userProfile = new UserProfile()
            {
                DateCreated = DateTime.UtcNow,
                Email = companyEmail,
                FirstName = user.FirstName,
                LastName = user.LastName,
                UserId = user.Id,
                Id = Guid.NewGuid().ToString(),
                Designation = designation,
                EventCategory = EventCategory.All.ToString(),
                PhoneNumber = user.PhoneNumber,
                Country = user.Country,
                CountryCode = user.CountryCode,
                MiddleName = user.MiddleName,
                Gender = user.Gender,
            };

            return userProfile;
        }
        #endregion

        #region Run migrations for all tenants
        /// <summary>
        /// This method runs migrations for all tenants and the public schema
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task<TenantMigrationResultVM> MigrateTenants()
        {

            var response = new TenantMigrationResultVM();
            var tenantSchemaInstance = new TenantSchema();
            var tableExist = Db.TableExists("Tenants");
            var tenants = tableExist ? Db.Tenants.Where(x => x.isSchemaCreated).Select(x => x.Subdomain).ToList() : new List<string>();
            response.FailedSchemaMigrations = new List<string>();

            // run for public schema
            if (!(await tenantSchemaInstance.RunMigrations(Constants.PUBLIC_SCHEMA)))
                throw new Exception("Something went wrong while running migrations on the public schema");

            for (int i = 0; i < tenants.Count(); i++)
            {
                var currentSchema = tenants[i];
                response.TotalSchemas++;
                try
                {
                    var result = await tenantSchemaInstance.RunMigrations(currentSchema);
                    if (result) response.MigratedSchemas++;
                    else
                    {
                        response.FailedSchemaMigrations.Add(currentSchema);
                    }
                }
                catch (Exception ex)
                {
                    response.FailedSchemaMigrations.Add(currentSchema);
                    Console.WriteLine(ex);
                    continue;
                }
            }

            return response;
        }
        #endregion

        #region Send Free Trial End Notification
        public async Task<bool> SendFreeTrialEndNotification(SendTrialEndedMailDto model)
        {
            // First check if the company has already subscribed to a plan for the application
            var company = await this.Db.Tenants.FirstOrDefaultAsync(x => x.Id.ToString() == model.TenantId);
            if (company == null)
                return false;

            var subscription = await this.Db.CompanySubscriptions
                .FirstOrDefaultAsync(x => x.TenantId == company.Id && x.Application == Applications.Joble);
            if (subscription != null)
                return false;

            // Get billing information if it exists
            // Note: The model already contains billing information details if they exist
            // Those were passed from the SubscriptionServices class

            var template = Extensions.ReadTemplateFromFile("free-trial-ended-email", _environment);
            template = template.Replace("{name}", model.Name).Replace("{payment-url}", Constants.FRONTEND_PAYMENT_URL);

            // Send mail
            await _emailService.SendEmail(template, model.Email, "JobPro Subscription Expired: Action Required");
            return true;
        }
        #endregion

        #region Delete Tenant
        public async Task<ApiResponse<bool>> DeleteTenant(string subdomain, string subdomainFromHeader, string deletedBy = null, string deletionReason = null)
        {
            var response = new ApiResponse<bool>
            {
                Data = false,
                ResponseCode = "400",
                ResponseMessage = ""
            };

            // Validate inputs
            if (string.IsNullOrEmpty(subdomain))
            {
                response.DevResponseMessage = "Subdomain is required.";
                response.ResponseMessage = "Bad request. Please contact support.";
                return response;
            }

            if (Db == null)
            {
                response.DevResponseMessage = "Database context is not initialized.";
                response.ResponseMessage = "Something went wrong, please try again later.";
                response.ResponseCode = "500";
                return response;
            }

            if (subdomainFromHeader == "api")
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = "You cannot delete the public schema",
                    ResponseMessage = "Bad request. Please contact support."
                };

            // Use execution strategy to handle transactions properly with NpgsqlRetryingExecutionStrategy
            var strategy = Db.Database.CreateExecutionStrategy();
            return await strategy.ExecuteAsync(async () =>
            {
                try
                {
                    // Get tenant information
                    Model.Tenant tenant = await Db.Tenants.FirstOrDefaultAsync(x => x.Subdomain == subdomain);
                    if (tenant == null)
                    {
                        response.DevResponseMessage = "Company not found.";
                        response.ResponseMessage = "Something went wrong, please try again later.";
                        return response;
                    }

                    var tenantId = tenant.Id;

                    // Get additional metadata for the deleted tenant record
                    var totalUsers = await Db.UserCompanies.CountAsync(x => x.TenantId == tenantId);
                    var activeSubscriptions = await Db.CompanySubscriptions.CountAsync(x => x.TenantId == tenantId && x.Status == SubscriptionStatus.Active);

                    // Create deleted tenant record before deletion using AutoMapper
                    var deletedTenant = _mapper.Map<Model.DeletedTenant>(tenant);
                    deletedTenant.Id = Guid.NewGuid();
                    deletedTenant.DeletedAt = DateTime.UtcNow;
                    deletedTenant.DeletedBy = deletedBy ?? "System";
                    deletedTenant.DeletionReason = deletionReason ?? "Manual deletion via DeleteTenant method";
                    deletedTenant.TotalUsers = totalUsers;
                    deletedTenant.ActiveSubscriptions = activeSubscriptions;
                    deletedTenant.LastMigrationDate = tenant.LastMigration.ToString("yyyy-MM-dd HH:mm:ss");
                    deletedTenant.DeletionNotes = $"Tenant deleted from subdomain: {subdomain}";

                    // Save deleted tenant record
                    await Db.DeletedTenants.AddAsync(deletedTenant);

                    // Delete all records from public schema related to this tenant

                    // Delete subscriptions and related records
                    var subscriptions = await Db.Subscriptions.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (subscriptions.Any())
                    {
                        Db.Subscriptions.RemoveRange(subscriptions);
                    }

                    // Delete subscription history
                    var subscriptionHistory = await Db.SubscriptionHistory.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (subscriptionHistory.Any())
                    {
                        Db.SubscriptionHistory.RemoveRange(subscriptionHistory);
                    }

                    // Delete user companies
                    var userCompanies = await Db.UserCompanies.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (userCompanies.Any())
                    {
                        Db.UserCompanies.RemoveRange(userCompanies);
                    }

                    // Delete company subscriptions
                    var companySubscriptions = await Db.CompanySubscriptions.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (companySubscriptions.Any())
                    {
                        Db.CompanySubscriptions.RemoveRange(companySubscriptions);
                    }

                    // Delete wallet transaction to company mappings
                    var walletTransactions = await Db.WalletTranToCompanyMappings.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (walletTransactions.Any())
                    {
                        Db.WalletTranToCompanyMappings.RemoveRange(walletTransactions);
                    }

                    // Delete phone number to company mappings
                    var phoneNoMappings = await Db.PhoneNoToCompanyMappings.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (phoneNoMappings.Any())
                    {
                        Db.PhoneNoToCompanyMappings.RemoveRange(phoneNoMappings);
                    }

                    // Delete enterprise subscriptions
                    var enterpriseSubscriptions = await Db.EnterpriseSubscriptions.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (enterpriseSubscriptions.Any())
                    {
                        Db.EnterpriseSubscriptions.RemoveRange(enterpriseSubscriptions);
                    }

                    // Delete enterprise subscription payments
                    var enterprisePayments = await Db.EnterprizeSubscriptionPayments.Where(x => x.TenantId == tenantId).ToListAsync();
                    if (enterprisePayments.Any())
                    {
                        Db.EnterprizeSubscriptionPayments.RemoveRange(enterprisePayments);
                    }

                    // Remove RefreshTokens
                    if (!string.IsNullOrEmpty(tenant.AdminId))
                    {
                        var refreshTokens = await Db.RefreshTokens.Where(x => x.UserId == tenant.AdminId).ToListAsync();
                        if (refreshTokens.Any())
                        {
                            Db.RefreshTokens.RemoveRange(refreshTokens);
                        }

                        // Remove tenant admin user
                        var admin = await Db.Users.FirstOrDefaultAsync(x => x.Id == tenant.AdminId);
                        if (admin != null)
                        {
                            Db.Users.Remove(admin);
                        }
                    }

                    // Remove tenant
                    Db.Tenants.Remove(tenant);

                    // Save changes to public schema
                    var publicSchemaResult = await Db.SaveChangesAsync();

                    // Drop the tenant schema and all its tables
                    try
                    {
                        var schemaName = subdomain.prepareSubdomainName();
                        var dropSchemaSql = $"DROP SCHEMA IF EXISTS \"{schemaName}\" CASCADE;";
                        await Db.Database.ExecuteSqlRawAsync(dropSchemaSql);

                        _logger.Information($"Schema {schemaName} dropped successfully");
                    }
                    catch (Exception schemaEx)
                    {
                        _logger.Warning(schemaEx, $"Failed to drop schema {subdomain}, but continuing with tenant deletion");
                    }

                    // Log all the user's in the comapny out
                    if (_adminService != null)
                    {
                        var loggedOutUsers = await _adminService.LogAllUsersOut();
                        if (loggedOutUsers.ResponseCode != "200")
                            _logger?.Warning($"Failed to log out all users for tenant {subdomain}. Response: {loggedOutUsers.ResponseMessage}");
                    }
                    else
                    {
                        _logger?.Warning($"Admin service is not available for tenant {subdomain}. Skipping user logout.");
                    }

                    response.Data = publicSchemaResult > 0;
                    response.ResponseMessage = publicSchemaResult > 0
                        ? "Company and all related data removed successfully"
                        : "Failed to remove company information";
                    response.ResponseCode = publicSchemaResult > 0 ? "200" : "500";

                    _logger?.Information($"Tenant {subdomain} and all related data deleted successfully");
                    return response;
                }
                catch (Exception ex)
                {
                    _logger?.Error(ex, $"Error deleting tenant {subdomain}");
                    response.ResponseMessage = "Something went wrong while deleting company";
                    response.ResponseCode = "500";
                    response.DevResponseMessage = ex.Message;
                    return response;
                }
            });
        }
        #endregion

        #region Add role to a user - private method
        private async Task<bool> AddUserToRole(string userId, string role, string appName, JobProDbContext context)
        {
            var roleID = await context.EmployeeRoles.Where(r => r.RoleName == role && r.PackageName == appName)
                .Select(x => x.Id).FirstOrDefaultAsync();
            if (roleID is not null)
            {
                // Check if the user has already been assigned to that role
                if (await context.UserAndRoleIds.AnyAsync(x => x.UserProId == userId && x.RoleId == roleID))
                {
                    return true;
                }

                await context.AddAsync(new UserAndRoleId
                {
                    UserProId = userId,
                    RoleId = roleID
                });

                return await context.SaveChangesAsync() > 0;
            }

            return false;
        }
        #endregion

        #region Get Deleted Tenants
        /// <summary>
        /// Gets all deleted/trashed tenants
        /// </summary>
        /// <returns></returns>
        public async Task<GenericResponse> GetDeletedTenants()
        {
            var response = new GenericResponse();
            try
            {
                var deletedTenants = await Db.DeletedTenants.OrderByDescending(x => x.DeletedAt).ToListAsync();
                var deletedTenantsVM = _mapper.Map<List<DeletedTenantVM>>(deletedTenants);

                // Process logo URLs if they exist
                foreach (var tenant in deletedTenantsVM)
                {
                    if (!string.IsNullOrEmpty(tenant.LogoUrl))
                    {
                        try
                        {
                            tenant.LogoUrl = Utility.ConvertSignedUrlToBase64(await _aWSS3Sevices.GetSignedUrlAsync(tenant.LogoUrl));
                        }
                        catch (Exception ex)
                        {
                            _logger?.Warning($"Failed to get signed URL for logo: {tenant.LogoUrl}. Error: {ex.Message}");
                            tenant.LogoUrl = null; // Clear the logo URL if it fails
                        }
                    }
                }

                response.Data = deletedTenantsVM;
                response.ResponseCode = "200";
                response.ResponseMessage = "Deleted companies retrieved successfully";
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "Error retrieving deleted tenants");
                response.ResponseCode = "500";
                response.ResponseMessage = "Something went wrong while retrieving deleted companies";
                response.DevResponseMessage = ex.Message;
            }

            return response;
        }
        #endregion

        #region Get top performing country
        public async Task<GenericResponse> TopPerformingCountries()
        {
            var response = new GenericResponse();
            var top6Countries = await Db.Subscriptions
                .Include(s => s.Tenant)
                .GroupBy(t => t.Tenant.Country)
                .OrderByDescending(group => group.Count())
                .Take(6)
                .Select(group => new { Country = group.Key, TenantCount = group.Count() })
                .ToListAsync();

            response.ResponseMessage = "Top 6 countries fetched successfully";
            response.ResponseCode = "200";
            response.Data = top6Countries;
            return response;

        }
        #endregion

        #region Get top performing country
        public async Task<GenericResponse> GetAllCountriesPercentage()
        {
            var response = new GenericResponse();
            var allCountries = await Db.Subscriptions
                .Include(s => s.Tenant)
                .GroupBy(t => t.Tenant.Country)
                .Select(group => new { Country = group.Key, TenantCount = group.Count() })
                .ToListAsync();

            var totalTenants = allCountries.Sum(country => country.TenantCount);
            var result = allCountries.Select(country => new
            {
                country.Country,
                Percentage = (double)country.TenantCount / totalTenants * 100
            }).ToList();

            response.ResponseMessage = "Countries percentage fetched successfully";
            response.ResponseCode = "200";
            response.Data = result;
            return response;
        }
        #endregion

        #region Migrate From SQL Server to Postgres
        public async Task<GenericResponse> MigrateFromSQLServerToPostgres(MigrateFromSqlToPostgresDto model)
        {
            var response = new GenericResponse();
            await using var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(model.Subdomain));

            // Get all subdomain
            var subdomains = await Db.Tenants.Select(x => x.Subdomain).ToListAsync();
            subdomains.Add("public");

            // Check if the subdomain exists
            if (!subdomains.Contains(model.Subdomain))
            {
                response.ResponseMessage = "Subdomain not found";
                response.ResponseCode = "404";
                return response;
            }

            // Check if the table exists
            if (!context.TableExists(model.TableName))
            {
                response.ResponseMessage = "Table not found";
                response.ResponseCode = "404";
                return response;
            }

            // Add records to the table, check if the record already exists
            foreach (var record in model.Records)
            {
                var recordDict = ObjectToDictionary(record);
                var recordExists = await context.Users.FindAsync(recordDict["Id"]);
                if (recordExists != null)
                {
                    context.Entry(recordExists).CurrentValues.SetValues(record);
                }
                else
                {
                    await context.AddAsync(record);
                }
            }

            response.ResponseMessage = "Success";
            response.ResponseCode = "200";
            return response;
        }

        public static Dictionary<string, object> ObjectToDictionary(object obj)
        {
            if (obj == null)
                return null;

            Dictionary<string, object> dictionary = new Dictionary<string, object>();

            // Get all properties of the object
            // Get all properties of the object (public and private)
            PropertyInfo[] properties = obj.GetType().GetProperties(
                BindingFlags.Public | BindingFlags.Instance | BindingFlags.NonPublic);

            foreach (PropertyInfo property in properties)
            {
                object value = property.GetValue(obj);
                dictionary.Add(property.Name, value);
            }

            return dictionary;
        }
        #endregion

        private async Task<bool> CheckIfJobProIdExist(string jobproId)
        {
            var user = await this.Db.Users.FirstOrDefaultAsync(x => x.JobProId.ToLower() == jobproId.ToLower());
            return user != null;
        }

        /// <summary>
        /// Sends a free license activation email to the user
        /// </summary>
        /// <param name="userName">First name of the user</param>
        /// <param name="userEmail">Email address of the user</param>
        /// <param name="application">Application name</param>
        public async Task SendFreeActivationEmail(string userName, string userEmail, Applications application)
        {
            // Create parameters dictionary
            var parameters = new Dictionary<string, string>
                {
                    { "[User Name]", userName },
                    { "[no_users]", "5" }, // Default to 5 free users
                    { "100 GB storage", $"Full access to {application} features" },
                    { "Team collaboration tools", $"Premium {application} support" }
                };

            // Get and update the template
            var template = Extensions.UpdateTemplateWithParams("subscription/free_trial_activation", _environment, parameters);
            if (string.IsNullOrEmpty(template))
            {
                _logger.Error("Free trial activation template not found");
                return;
            }

            // Send the email
            string subject = $"Welcome to Your Free {application} License!";
            await _emailService.SendEmail(template, userEmail, subject);
        }
    }
}
