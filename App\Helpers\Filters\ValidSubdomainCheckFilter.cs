﻿using Jobid.App.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Filters
{
    public class ValidSubdomainCheckFilter : IActionFilter
    {
        public void OnActionExecuted(ActionExecutedContext context)
        {
            // Not Implemented
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            TenantSchema tenantSchema = new TenantSchema();
            var origin = context.HttpContext.Request.Headers["subdomain"].ToString();
            if (string.IsNullOrEmpty(origin))
            {
                context.Result = new BadRequestObjectResult(new
                {
                    message = "Subdomain cannot be null",
                    status = false
                });
                return;
            }

            tenantSchema._schema = origin;
            var schemaExists = tenantSchema.DoesCurrentSubdomainExist().Result;
            Console.WriteLine($"origin {origin}");

            if (!schemaExists)
            {
                // Return a 404 Not Found response.
                context.Result = new BadRequestObjectResult(new
                {
                    message = "Invalid subdomain",
                    status = false
                });
                return;
            }
        }
    }
}
