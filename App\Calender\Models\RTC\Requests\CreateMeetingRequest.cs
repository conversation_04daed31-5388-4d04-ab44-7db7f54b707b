﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Jobid.App.Calender.Models.RTC.Requests
{
    public class CreateMeetingRequest
    {
        [Json<PERSON>roperty("hostJobAuthId")]
        public string HostJobAuthId { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("isPrivate")]
        public bool IsPrivate { get; set; }

        [Json<PERSON>roperty("coHostJobAuthIds")]
        public List<string> CoHostJobAuthIds { get; set; }

        [JsonProperty("guestJobAuthIds")]
        public List<string> GuestJobAuthIds { get; set; }

        [Json<PERSON>roperty("externalGuestEmails")]
        public List<string> ExternalGuestEmails { get; set; }

        [JsonProperty("tenantId")]
        public string TenantId { get; set; }
    }
   
}
