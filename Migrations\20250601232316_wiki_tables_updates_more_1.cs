﻿using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Jobid.Migrations
{
    public partial class wiki_tables_updates_more_1 : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public wiki_tables_updates_more_1(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TextContent",
                schema: _Schema,
                table: "WikiFiles",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TextContent",
                schema: _Schema,
                table: "WikiFiles");
        }
    }
}
