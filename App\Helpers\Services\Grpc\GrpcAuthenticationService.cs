﻿using Grpc.Core;
using Serilog;
using System.Threading.Tasks;
using Jobid.App.Helpers.Contract;

namespace Jobid.App.Helpers.Services.Grpc
{
    public class GrpcAuthenticationService : GrpcAuthService.GrpcAuthServiceBase
    {
        private readonly ILogger _logger = Log.ForContext<GrpcAuthenticationService>();
        private readonly IUserServices _userServices;

        public GrpcAuthenticationService(IUserServices userServices)
        {
            _userServices = userServices;
        }

        public override async Task<GrpcAuthenticationResponse> Authenticate(GrpcAuthenticateRequest request, ServerCallContext context)
        {
            var response = await _userServices.InternalServiceLogin(request);
            var responseToReturn = response.Data as GrpcAuthenticationResponse;

            return responseToReturn;
        }
    }
}
