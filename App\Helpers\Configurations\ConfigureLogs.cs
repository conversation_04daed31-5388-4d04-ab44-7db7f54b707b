﻿using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using Serilog.Exceptions;
using Serilog.Sinks.Elasticsearch;
using System.Reflection;

namespace Jobid.App.Helpers.Configurations
{
    public class ConfigureLogs
    {
        public static void ConfigureSerilog()
        {
            string cloudId, username, password, uri = string.Empty;
            IConfigurationRoot configuration = null;
            // Get application environment
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            cloudId = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_CLOUD_ID") ?? configuration["ElasticConfiguration:CloudId"];
            username = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_USERNAME") ?? configuration["ElasticConfiguration:Username"];
            password = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_PASSWORD") ?? configuration["ElasticConfiguration:Password"];
            uri = Environment.GetEnvironmentVariable("JOBPRO_ELASTIC_URI") ?? configuration["ElasticConfiguration:Uri"];

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .Enrich.FromLogContext()
                .Enrich.WithExceptionDetails()
                .WriteTo.Debug()
                .WriteTo.Console()
                .WriteTo.File("Logs/log.txt", rollingInterval: RollingInterval.Hour)
                .WriteTo.Elasticsearch(ConfigureElasticSearch(uri, environment, username, password))
                .Enrich.WithProperty("Environment", environment)
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
        }

        private static ElasticsearchSinkOptions ConfigureElasticSearch(string uriStr, string environment, string username, string password)
        {
            var uri = new Uri(uriStr);
            var options = new ElasticsearchSinkOptions(uri)
            {
                AutoRegisterTemplate = true,
                ModifyConnectionSettings = x => x.BasicAuthentication(username, password),
                IndexFormat = $"{Assembly.GetExecutingAssembly().GetName().Name.ToLower().Replace(".", "-")}-{environment?.ToLower().Replace(".", "-")}-{DateTime.UtcNow:yyyy-MM}",
                NumberOfShards = 2,
                NumberOfReplicas = 1,
            };

            return options;
        }
    }
}
