﻿using Jobid.App.JobProject.Models;
using System;

namespace Jobid.App.JobProject.ViewModel
{
    public class TodoCustomFrequencyDto
    {
        public string RepeatEvery { get; set; }
        public int? RepeatCount { get; set; }
        public string RepeatOn { get; set; }
        public TodoEndStatus? EndStatus { get; set; }
        public DateTime? EndsOn { get; set; }
        public int? EndsAfter { get; set; }
        public Guid? ProjectMgmt_TodoId { get; set; }
    }
}
