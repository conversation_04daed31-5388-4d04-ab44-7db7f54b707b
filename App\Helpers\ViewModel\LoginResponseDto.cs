﻿using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Elastic.CommonSchema;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using System.Collections.Generic;

namespace Jobid.App.Helpers.ViewModel
{
    public class LoginResponseDto
    {
        public UserDetails? User { get; set; }
        public string Id { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public UserTypes UserType { get; set; }
        public object? AccessToken { get; set; }
        public object? TempToken { get; set; }
        public string RefreshToken { get; set; }
        public string Role { get; set; }
        public List<string> userPermissions { get; set; }
        public RoleDtoVm? newRole { get; set; }
        public UserCompaniesVM? userCompany { get; set; }
        public UserProfile? userProfile { get; set; }
        public List<string> userPermittedApp { get; set; }
        public string LoggedInWithEmail { get; set; }
        public Localization? localization { get; set; }
        public TwoFactorSetting? CompanyTwoFactorSettings { get; set; }
        public string Message { get; set; }
        public bool TwoFaOptionsUpdated { get; set; }
        public bool HasAccessToWiki { get; set; }
    }
}
