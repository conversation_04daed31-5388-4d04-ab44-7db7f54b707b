﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added_new_filds_to_group : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added_new_filds_to_group(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Language",
                schema: _Schema,
                table: "Groups",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PreferredVoiceAI",
                schema: _Schema,
                table: "Groups",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<Guid>(
                name: "ExternalMeetingId",
                schema: _Schema,
                table: "Campaigns",
                type: "uuid",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Language",
                schema: _Schema,
                table: "Groups");

            migrationBuilder.DropColumn(
                name: "PreferredVoiceAI",
                schema: _Schema,
                table: "Groups");

            migrationBuilder.DropColumn(
                name: "ExternalMeetingId",
                schema: _Schema,
                table: "Campaigns");
        }
    }
}
