﻿using Jobid.App.Helpers.Enums;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class CreateParentCompanyDto
    {
        [Required]
        public string CompanyName { get; set; }
        [Required]
        public string Country { get; set; }
        public string FullAddress { get; set; }
        [EmailAddress]
        [Required]
        public string EmailAddress { get; set; }
        [Required]
        public string BranchColor { get; set; }
        public Industries Industry { get; set; }
    }

}
