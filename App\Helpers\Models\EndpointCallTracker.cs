﻿﻿using Jobid.App.Helpers.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Helpers.Models
{
    public class EndpointCallTracker
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// The application that the endpoint belongs to
        /// </summary>
        public Applications Application { get; set; }

        /// <summary>
        /// The section of the application (Calendar, Todo, Project, Chat)
        /// </summary>
        public string Section { get; set; }

        /// <summary>
        /// The controller name
        /// </summary>
        public string Controller { get; set; }

        /// <summary>
        /// The action/endpoint name
        /// </summary>
        public string Action { get; set; }

        /// <summary>
        /// The HTTP method (GET, POST, PUT, DELETE, etc.)
        /// </summary>
        public string HttpMethod { get; set; }

        /// <summary>
        /// The date of the call (without time)
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// The number of calls made on this date
        /// </summary>
        public int Count { get; set; } = 1;

        /// <summary>
        /// When the record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
