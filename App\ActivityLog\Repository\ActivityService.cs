﻿using AutoMapper;
using Hangfire;
using Jobid.App.ActivityLog.contract;
using Jobid.App.ActivityLog.Model;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.Repository
{
    public class ActivityService : IActivityService
    {
        public ActivityService(JobProDbContext _db, JobProDbContext publicSchemaContext,
            IMapper mapper, IAdminService adminService, IWebHostEnvironment environment, IEmailService emailService)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _mapper = mapper;
            _adminService = adminService;
            _environment = environment;
            _emailService = emailService;
        }

        public JobProDbContext Db { get; set; }
        public JobProDbContext Dbo { get; set; }
        private readonly IMapper _mapper;
        private readonly IAdminService _adminService;
        private readonly IWebHostEnvironment _environment;
        private readonly IEmailService _emailService;

        #region Create Log
        /// <summary>
        /// Create Log
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> CreateLog(ActivityDto model)
        {
            // Check if its a bulk todo request and create a context using the subdomain
            if (model.IsBulkTodoUpload && model.subdomain is not null)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(model.subdomain));
                Db = context;
            }

            var log = new Activity()
            {
                Description = model.Description,
                ActivitySummary = model.ActivitySummary,
                EventId = model.EventId,
                EventCategory = model.EventCategory,
                UserId = model.UserId,
                By = model.By,
                Application = model.Application,
                CreatedAt = DateTime.UtcNow,
                GenericUrl = model.GenericUrl
            };

            Db.Activities.Add(log);

            // If there are attachments, add them to the database
            if (model.LogAttachments != null && model.LogAttachments.Any())
            {
                var attachments = _mapper.Map<List<LogAttachment>>(model.LogAttachments);
                foreach (var attachment in attachments)
                {
                    attachment.ActivityId = log.Id;
                    Db.LogAttachments.Add(attachment);
                }
            }

            var result = await Db.SaveChangesAsync();
            return result > 0;
        }
        #endregion

        #region Get Activities with filters
        /// <summary>
        /// Get activities with filters
        /// </summary>
        /// <param name="filters"> filter params</param>
        /// <returns></returns>
        public async Task<GenericResponse> GetActivitiesWithFilters(AcitivityLogFilters filters)
        {
            // Check that the difference between the start and end date is not more than 24 hours
            //if (filters.EndDate.Subtract(filters.StartDate).TotalHours > 24)
            //    return new GenericResponse { Data = false, ResponseMessage = "The difference between the start and end date cannot be more than 24 hours", ResponseCode = "400" };

            var activitiesFor = "";
            if (filters.ShareId is not null)
            {
                // Get the share activity params from the database
                var parameters = await Db.ShareActivityParams.FirstOrDefaultAsync(x => x.ShareId == filters.ShareId);
                if (parameters == null)
                    throw new RecordNotFoundException($"No record found for the {filters.ShareId}");

                filters.StartDate = parameters.StartDate;
                filters.EndDate = parameters.EndDate;
                filters.AnyOneCanView = parameters.AnyOneCanView;
                filters.MemberIds.Add(parameters.MemberId);
                filters.EventCategory = parameters.EventCategory.Split('-').ToList();

                activitiesFor = await Db.UserProfiles.Where(x => x.UserId == parameters.MemberId)
                    .Select(x => x.FirstName + " " + x.LastName).FirstOrDefaultAsync();
            }
            else
            {
                if (filters.StartDate.Year == 1 || filters.EndDate.Year == 1)
                    throw new DirtyFormException("StartDate or EndDate is not a valid date");
            }

            if (filters.EventCategory.Contains(EventCategory.All.ToString()))
                filters.EventCategory = Enum.GetNames(typeof(EventCategory)).ToList();

            var filter = filters.EventCategory.Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();

            var response = new Dictionary<string, List<Activity>>();
            Expression<Func<Activity, bool>> expression = null;
            var activities = new List<Activity>();

            if (!filters.EventCategory.Contains(EventCategory.All.ToString()) && !filters.MemberIds.Any())
                expression = x => filter.Contains(x.EventCategory) && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate;
            else if (filters.MemberIds.Any())
            {
                // Check if the user has granted access to the logged in user
                foreach (var id in filters.MemberIds)
                {
                    if (id != filters.UserId)
                    {
                        if (!filters.AnyOneCanView)
                        {
                            var permissionReq = await Db.ActivityRequestedPermisssions.Where(x => x.RequesterId == filters.UserId && x.UserId == id && x.Status == ApprovalStatus.Approved).ToListAsync();

                            foreach (var permission in permissionReq)
                            {
                                var acts = new List<Activity>();
                                var permissionFilter = permission.EventCategories.Split('-').Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();
                                if (permissionFilter.Contains(EventCategory.All))
                                    acts = await Db.Activities
                                        .Where(x => x.UserId == id && x.CreatedAt >= permission.FromDate && x.CreatedAt <= permission.ToDate).ToListAsync();
                                else
                                    acts = await Db.Activities
                                        .Where(x => x.UserId == id && x.CreatedAt >= permission.FromDate && x.CreatedAt <= permission.ToDate && permissionFilter.Contains(x.EventCategory)).ToListAsync();

                                activities.AddRange(acts);
                            }
                        }
                        else
                        {
                            var anyOneCanViewFilter = new List<string>();
                            if (filters.EventCategory.Contains(EventCategory.All.ToString()))
                                anyOneCanViewFilter = Enum.GetNames(typeof(EventCategory)).ToList();

                            var listOfEventCategories = anyOneCanViewFilter.Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();

                            var acts = await Db.Activities.Where(x => x.UserId == id && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate && listOfEventCategories.Contains(x.EventCategory)).ToListAsync();

                            activities.AddRange(acts);
                        }
                    }
                    else
                    {
                        var acts = await Db.Activities.Where(x => x.UserId == id && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate && filter.Contains(x.EventCategory)).ToListAsync();

                        activities.AddRange(acts);
                    }
                }
                goto FilterByHours;
            }
            else if (!string.IsNullOrEmpty(filters.SearchParam))
            {
                if (!filters.EventCategory.Contains(EventCategory.All.ToString()) && !filters.MemberIds.Any())
                    expression = x => filter.Contains(x.EventCategory) && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate && (x.ActivitySummary.ToLower().Contains(filters.SearchParam.ToLower()) || x.By.ToLower().Contains(filters.SearchParam.ToLower()));
                else if (filters.MemberIds.Any())
                {
                    foreach (var id in filters.MemberIds)
                    {
                        if (id != filters.UserId)
                        {
                            if (!filters.AnyOneCanView)
                            {
                                var permissionReq = await Db.ActivityRequestedPermisssions.Where(x => x.RequesterId == filters.UserId && x.UserId == id && x.Status == ApprovalStatus.Approved).ToListAsync();

                                foreach (var permission in permissionReq)
                                {
                                    var anyOneCanViewFilter = new List<string>();
                                    var permissionFilter = permission.EventCategories.Split('-').Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();
                                    if (permissionFilter.Contains(EventCategory.All))
                                        anyOneCanViewFilter = Enum.GetNames(typeof(EventCategory)).ToList();
                                    permissionFilter = anyOneCanViewFilter.Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();

                                    var acts = await Db.Activities.Where(x => x.UserId == id && x.CreatedAt >= permission.FromDate && x.CreatedAt <= permission.ToDate && permissionFilter.Contains(x.EventCategory) && (x.ActivitySummary.ToLower().Contains(filters.SearchParam.ToLower()) || x.By.ToLower().Contains(filters.SearchParam.ToLower()))).ToListAsync();

                                    activities.AddRange(acts);
                                }
                            }
                            else
                            {
                                var anyOneCanViewFilter = new List<string>();
                                if (filters.EventCategory.Contains(EventCategory.All.ToString()))
                                    anyOneCanViewFilter = Enum.GetNames(typeof(EventCategory)).ToList();

                                var listOfEventCategories = anyOneCanViewFilter.Select(x => (EventCategory)Enum.Parse(typeof(EventCategory), x)).ToList();

                                var acts = await Db.Activities.Where(x => x.UserId == id && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate && listOfEventCategories.Contains(x.EventCategory) && (x.ActivitySummary.ToLower().Contains(filters.SearchParam.ToLower()) || x.By.ToLower().Contains(filters.SearchParam.ToLower()))).ToListAsync();
                            }

                        }
                        else
                        {
                            var acts = await Db.Activities.Where(x => x.UserId == id && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate && filter.Contains(x.EventCategory) && (x.ActivitySummary.ToLower().Contains(filters.SearchParam.ToLower()) || x.By.ToLower().Contains(filters.SearchParam.ToLower()))).ToListAsync();

                            activities.AddRange(acts);
                        }
                    }
                    goto FilterByHours;
                }
                else
                    expression = x => x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate && (x.ActivitySummary.ToLower().Contains(filters.SearchParam.ToLower()) || x.By.ToLower().Contains(filters.SearchParam.ToLower()));
            }
            else
            {
                // Check if the user is an admin or super admin
                var isAdminOrSuperAdmin = await UserAnAdminOrSuperAdmin(filters.UserId);
                if (isAdminOrSuperAdmin)
                    expression = x => x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate;
                else
                    expression = x => x.UserId == filters.UserId && x.CreatedAt >= filters.StartDate && x.CreatedAt <= filters.EndDate;
            }

            // Check if the logged in user is an admin or superadmin

            activities = await Db.Activities
                .OrderByDescending(x => x.CreatedAt)
                .Where(expression).ToListAsync();

        // A list of string of all the hours from 8am to 8pm
        FilterByHours:
            var hours = new List<string> { "8:00 AM", "9:00 AM", "10:00 AM", "11:00 AM", "12:00 PM",
                           "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM", "5:00 PM",
                           "6:00 PM", "7:00 PM", "8:00 PM" };

            var activitiesPerHour = new List<Activity>();
            foreach (var hour in hours)
            {
                var activity = activities.Where(x => x.CreatedAt.ToLocalTime().TimeOfDay >= DateTime.Parse(hour).ToLocalTime().TimeOfDay && x.CreatedAt.ToLocalTime().TimeOfDay <= DateTime.Parse(hour).AddHours(1).ToLocalTime().TimeOfDay).ToList();
                activitiesPerHour.AddRange(activity);
                response.Add(hour, activity);
            }

            return new GenericResponse()
            {
                Data = new
                {
                    Activities = response,
                    ActivitiesFor = activitiesFor == "" ? "Own" : activitiesFor
                },
                ResponseMessage = "Activities retrieved successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Activity Settings
        /// <summary>
        /// Activity Settings
        /// </summary>
        /// <param name="activitySettingDto"></param>
        /// <returns></returns>
        public async Task<bool> SetActivitySettings(ActivitySettingsDto activitySettingDto)
        {
            if (!string.IsNullOrEmpty(activitySettingDto.TeamId))
            {
                // Get all users in the team
                var teamMembers = await Db.TeamMembers
                    .Where(x => x.TeamId.ToString() == activitySettingDto.TeamId).ToListAsync();

                // Get all users in the team
                activitySettingDto.UserIds.AddRange(teamMembers?.Select(x => x.UserId).ToList());
            }

            if (activitySettingDto.TurnOnOrOffForAllUsers.HasValue)
            {
                var allUsers = await Db.UserProfiles.ToListAsync();

                // Add the userIds t the payload user id
                activitySettingDto.UserIds.AddRange(allUsers.Select(x => x.UserId).ToList());
            }

            if (activitySettingDto.UserIds.Count == 0)
                throw new DirtyFormException("User Id is requried");

            var activitySetttings = new List<ActivitySettings>();
            foreach (var userId in activitySettingDto.UserIds)
            {
                // Get user
                var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
                if (user == null)
                    throw new RecordNotFoundException("User not found");

                if (activitySettingDto.TurnOnOrOffForAllUsers.HasValue)
                {
                    user.LogActivity = activitySettingDto.TurnOnOrOffForAllUsers.Value;
                    user.EraseAcitivity = activitySettingDto.EraseAcitivity;
                    if (activitySettingDto.Categories.Count > 0)
                        user.EventCategory = activitySettingDto.Categories.Count > 1 ? string.Join("-", activitySettingDto.Categories) : activitySettingDto.Categories[0];

                    activitySetttings.Add(new ActivitySettings
                    {
                        UserId = userId,
                        LogActivity = activitySettingDto.TurnOnOrOffForAllUsers.Value,
                        TurnOnOrOffForAllUsers = activitySettingDto.TurnOnOrOffForAllUsers,
                        EraseAcitivity = activitySettingDto.EraseAcitivity,
                    });
                }
                else
                {
                    // If the user is the only one in the list, update the user
                    user.LogActivity = activitySettingDto.LogActivity ?? user.LogActivity;
                    user.EraseAcitivity = activitySettingDto.EraseAcitivity;

                    if (activitySettingDto.Categories.Count > 0)
                        user.EventCategory = activitySettingDto.Categories.Count > 1 ? string.Join("-", activitySettingDto.Categories) : activitySettingDto.Categories[0];

                    activitySetttings.Add(new ActivitySettings
                    {
                        UserId = userId,
                        LogActivity = activitySettingDto.LogActivity ?? user.LogActivity,
                        EraseAcitivity = activitySettingDto.EraseAcitivity,
                    });
                }

                Db.UserProfiles.Update(user);
                Db.ActivitySettings.AddRange(activitySetttings);
            }

            var res = await Db.SaveChangesAsync();
            return res > 0;
        }
        #endregion

        #region Get Activity Settings
        public async Task<GenericResponse> GetActivitySettings(string userId)
        {
            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (user == null)
                throw new RecordNotFoundException("User not found");

            var activitySetting = new ActivitySettingsDto
            {
                LogActivity = user.LogActivity,
                EraseAcitivity = user.EraseAcitivity,
                Categories = user.EventCategory.Split('-').ToList()
            };

            return new GenericResponse
            {
                Data = activitySetting,
                ResponseMessage = "Activity settings retrieved successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Get Company Activity Settings
        public async Task<GenericResponse> GetCompanyActivitySettings(string subdomain)
        {
            // Get the company activity settings
            var companySettings = await Dbo.ActivitySettings.FirstOrDefaultAsync(x => x.TurnOnOrOffForAllUsers != null);
            if (companySettings == null)
                throw new RecordNotFoundException("Company activity settings not found");

            return new GenericResponse
            {
                Data = new
                {
                    companySettings.TurnOnOrOffForAllUsers.Value,
                    companySettings.EraseAcitivity,
                },
                ResponseMessage = "Company activity settings retrieved successfully",
                ResponseCode = "200"
            };
        }
        #endregion

        #region Request For Permission
        /// <summary>
        /// Activity Permission Request
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> RequestForPermissions(List<ActivityRequestedPermisssionsDto> model)
        {
            var activityPermissionReq = _mapper.Map<List<ActivityRequestedPermisssions>>(model);

            Db.ActivityRequestedPermisssions.AddRange(activityPermissionReq);
            var result = await Db.SaveChangesAsync();
            return result > 0;
        }
        #endregion

        #region Request for permisiions for team members
        /// <summary>
        /// Request for permisiions for team members
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<bool> RequestForPermissionsForTeamMembers(ActivityRequestedPermisssionsForTeamMembersDto model)
        {
            var activityPermissionReqs = new List<ActivityRequestedPermisssions>();
            foreach (var teamId in model.TeamIds)
            {
                // Get team membersIds for each team
                var teamMembers = await Db.TeamMembers.Where(x => x.TeamId.ToString() == teamId).ToListAsync();
                foreach (var member in teamMembers)
                {
                    var activityPermissionReq = new ActivityRequestedPermisssions
                    {
                        UserId = member.UserId,
                        RequesterId = model.RequesterId,
                        FromDate = model.FromDate,
                        ToDate = model.ToDate,
                        CreatedAt = DateTime.UtcNow
                    };

                    activityPermissionReqs.Add(activityPermissionReq);
                }
            }

            Db.ActivityRequestedPermisssions.AddRange(activityPermissionReqs);
            var result = await Db.SaveChangesAsync();
            return result > 0;
        }
        #endregion

        #region Get Requested Permissions Per User
        /// <summary>
        /// Get Requested Permissions Per User
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<ActivityRequestedPermisssions>> GetRequestedPermissionPerUser(string userId)
        {
            return await Db.ActivityRequestedPermisssions.Where(x => x.UserId == userId)
                .OrderByDescending(x => x.CreatedAt).ToListAsync();
        }
        #endregion

        #region Grant or Reject Permissions Requests
        /// <summary>
        /// Grant Or Reject Activity Permissions Request
        /// </summary>
        /// <param name="requestId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public async Task<bool> GrantOrRejectActivityPermissionsRequest(string requestId, ApprovalStatus status)
        {
            var request = await Db.ActivityRequestedPermisssions.FirstOrDefaultAsync(x => x.Id.ToString() == requestId);
            if (request == null)
                return false;

            request.Status = status;
            Db.ActivityRequestedPermisssions.Update(request);
            var result = await Db.SaveChangesAsync();

            return result > 0;
        }
        #endregion

        #region Get activity permission requests by a user
        /// <summary>
        /// Get activity permission requests by a user
        /// </summary>
        /// <param name="requesterId"></param>
        /// <returns></returns>
        public async Task<List<ActivityRequestedPermisssions>> GetPermissionRequestsByUser(string requesterId)
        {
            return await Db.ActivityRequestedPermisssions.Where(x => x.RequesterId == requesterId)
                .OrderByDescending(x => x.CreatedAt).ToListAsync();
        }
        #endregion

        #region Get users that granted access to the logged in user
        public async Task<List<UserDto>> GetUsersThatGrantedAccessToLoggedInUser(string userId)
        {
            var permissionReqs = await Db.ActivityRequestedPermisssions.Where(x => x.RequesterId == userId && x.Status == ApprovalStatus.Approved)
                .OrderByDescending(x => x.CreatedAt).ToListAsync();

            var users = new List<UserDto>();
            foreach (var req in permissionReqs)
            {
                var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == req.UserId);
                if (user != null)
                {
                    var userDto = new UserDto
                    {
                        Id = user.UserId,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Email = user.Email
                    };
                    users.Add(userDto);
                }
            }

            return users;
        }
        #endregion

        #region Get all Activity
        public async Task<Page<Activity>> GetActivities(PaginationParameters paginationParameters)
        {
            return await Db.Activities.OrderByDescending(x => x.CreatedAt).ToPageListAsync(paginationParameters.PageNumber, paginationParameters.PageSize);
        }
        #endregion

        #region Share activity
        public async Task<GenericResponse> ShareActivity(ShareActivityDto model, string loggedInUserId)
        {
            var activityPermissionReqs = new List<ActivityRequestedPermisssions>();

            foreach (var userId in model.UserIds)
            {
                var activityPermissionReq = new ActivityRequestedPermisssions
                {
                    Status = ApprovalStatus.Approved,
                    RequesterId = userId,
                    UserId = loggedInUserId,
                    FromDate = model.StartDate,
                    ToDate = model.EndDate,
                    CreatedAt = DateTime.UtcNow,
                    EventCategories = model.EventCategories.Any() ? string.Join("-", model.EventCategories) : EventCategory.All.ToString(),
                };

                activityPermissionReqs.Add(activityPermissionReq);
            }

            await Db.ActivityRequestedPermisssions.AddRangeAsync(activityPermissionReqs);

            // Add the params to the database
            var shareId = GenerateRandomString();
            var activityParams = new ShareActivityParams
            {
                ShareId = shareId,
                MemberId = loggedInUserId,
                StartDate = model.StartDate,
                EndDate = model.EndDate,
                EventCategory = model.EventCategories.Any() ? string.Join("-", model.EventCategories) : EventCategory.All.ToString(),
                AnyOneCanView = model.AnyOneCanView
            };
            await Db.ShareActivityParams.AddAsync(activityParams);
            var res = await Db.SaveChangesAsync();

            var url = "";
            if (res > 0)
            {
                url = String.Format(Utility.Constants.FRONT_END_DASHBOARD_URL_JOBLE + Utility.Constants.ACTIVITY_SHARE_URL + "?shareId=" + shareId, Db.Schema);

                // Todo: Send the link via email to the users
                var loggedInUserName = await Db.UserProfiles.Where(u => u.UserId == loggedInUserId)
                    .Select(u => u.FirstName + " " + u.LastName).FirstOrDefaultAsync();
                var subject = $"Activity Logs: {loggedInUserName}";
                var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/share-activity.html");

                foreach (var userId in model.UserIds)
                {
                    var template = File.ReadAllText(templatePath);
                    var user = await Db.UserProfiles.Where(u => u.UserId == userId).FirstOrDefaultAsync();
                    template = template.Replace("{name}", user.FirstName).Replace("{owner}", loggedInUserName)
                        .Replace("{fromDate}", model.StartDate.ToShortDateString()).Replace("{toDate}", model.EndDate.ToShortDateString()).Replace("{url}", url);

                    BackgroundJob.Enqueue(() => _emailService.SendEmail(template, user.Email, subject));
                }
            }

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Link generated successfully",
                Data = url
            };
        }
        #endregion

        #region All Tenants Activities
        public async Task<GenericResponse> GetMonthlyActivityCounts(Applications applications)
        {
            var monthPairs = await GetMonthPairsAsync(applications);
            var result = monthPairs.Select(mp => new MonthlyActivityCount
            {
                MonthNumber = mp.CurrentMonthCount.MonthNumber,
                YearNumber = mp.CurrentMonthCount.YearNumber,
                MonthName = DateTimeFormatInfo.CurrentInfo.GetMonthName(mp.CurrentMonthCount.MonthNumber),
                Count = mp.CurrentMonthCount.Count,
                PercentageIncrement = mp.PreviousMonthCount != null && mp.PreviousMonthCount.Count != 0 ? (decimal)(mp.CurrentMonthCount.Count - mp.PreviousMonthCount.Count) / mp.PreviousMonthCount.Count * 100 : 0
            })
            .OrderByDescending(mp => mp.MonthNumber);
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Monthly activities fetched successfully",
                Data = result
            };
        }
        #endregion

        #region Tenant Activities
        public async Task<GenericResponse> GetTenantsCounts(ActivityQueryParameters activityQueryParameters)
        {
            var query = from activity in Dbo.ActivityViews
                        join tenant in Dbo.Tenants on activity.SubDomain equals tenant.Subdomain
                        select new { Activity = activity, Tenant = tenant };

            if (activityQueryParameters.Application != null)
                query = query.Where(av => av.Activity.Application == activityQueryParameters.Application);

            if (activityQueryParameters.FromDate != null)
                query = query.Where(av => av.Activity.CreatedAt >= activityQueryParameters.FromDate);

            if (activityQueryParameters.ToDate != null)
                query = query.Where(av => av.Activity.CreatedAt <= activityQueryParameters.ToDate);

            if (!string.IsNullOrEmpty(activityQueryParameters.CompanyName))
                query = query.Where(av => av.Tenant.CompanyName.Contains(activityQueryParameters.CompanyName));

            var result = await query
                .GroupBy(av => new { av.Tenant.Subdomain, av.Tenant.CompanyName })
                .Select(g => new ActivityCountPerTenant
                {
                    SubDomain = g.Key.Subdomain,
                    CompanyName = g.Key.CompanyName,
                    ActivityCount = g.Count()
                })
                .ToPageListAsync(activityQueryParameters.PageNumber, activityQueryParameters.PageSize);

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Tenants activities count fetched successfully",
                Data = result
            };
        }
        #endregion

        #region Private Methods
        private async Task<IQueryable<MonthlyCountPair>> GetMonthPairsAsync(Applications applications)
        {
            var monthlyCounts = await GetMonthCountsQuery(applications, -6).ToListAsync();
            var monthPairs = from currentMonthCount in monthlyCounts
                             join previousMonthCount in monthlyCounts on new { currentMonthCount.MonthNumber, currentMonthCount.YearNumber } equals new { MonthNumber = previousMonthCount.MonthNumber + 1, previousMonthCount.YearNumber } into gj
                             from previousMonthCount in gj.DefaultIfEmpty()
                             select new MonthlyCountPair
                             {
                                 CurrentMonthCount = currentMonthCount,
                                 PreviousMonthCount = previousMonthCount
                             };
            return monthPairs.AsQueryable();
        }

        private IQueryable<MonthlyActivityCount> GetMonthCountsQuery(Applications applications, int count)
        {
            return Dbo.ActivityViews
                .Where(a => a.Application == applications && a.CreatedAt >= DateTime.UtcNow.AddMonths(count))
                .GroupBy(a => new { a.CreatedAt.Month, a.CreatedAt.Year })
                .Select(g => new MonthlyActivityCount
                {
                    MonthNumber = g.Key.Month,
                    YearNumber = g.Key.Year,
                    Count = g.Count()
                });
        }
        #endregion

        #region Check if the user has granted permission for his/her activity to be logged
        public async Task<bool> CheckIfUserHasGrantedPermission(string userId, EventCategory eventCategory, string subdomain = null, bool isBulkTodoUpload = false)
        {
            // Check if its a bulk todo request and create a context usong the subdomain
            if (isBulkTodoUpload && subdomain is not null)
            {
                var context = new JobProDbContext(GlobalVariables.ConnectionString, new DbContextSchema(subdomain));
                Db = context;
            }

            var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
            if (!user.LogActivity)
                return false;

            var categories = user.EventCategory;
            if (categories.Contains(EventCategory.All.ToString()) || categories.Contains(eventCategory.ToString()))
                return true;

            return false;
        }
        #endregion

        #region Private Method

        /// <summary>
        /// This method checks if the logged in user has permission to perform the action
        /// </summary>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        private async Task<bool> UserAnAdminOrSuperAdmin(string loggedInUserId)
        {
            // Check if the logged in user is a super admin or an admin
            var userRole = await _adminService.GetUserRole(loggedInUserId);
            if (userRole != DataSeeder.SuperAdmin && userRole != DataSeeder.Admin)
                return false;

            return true;
        }

        /// <summary>
        /// This method generates a random string
        /// </summary>
        /// <returns></returns>
        private string GenerateRandomString()
        {
            const string chars = "abcdefghijklmnopqrstuvwxyz";
            StringBuilder sb = new StringBuilder();
            Random random = new Random();

            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 4; j++)
                    sb.Append(chars[random.Next(chars.Length)]);
                if (i < 2)
                    sb.Append('-');
            }

            return sb.ToString();
        }
        #endregion
    }
}
