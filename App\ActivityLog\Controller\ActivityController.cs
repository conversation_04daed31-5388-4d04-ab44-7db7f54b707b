﻿using Jobid.App.ActivityLog.Model;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.ActivityLog.Controller
{
    [Route("api/[controller]")]
    [ApiController]
    public class ActivityController : ControllerBase
    {
        private string ClaimId => User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        private string ClaimName => User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;
        protected Guid? CurrentUserId => ClaimId is null ? null : Guid.Parse(ClaimId);
        protected string CurrentUser => ClaimName is null ? null : ClaimName;

        private readonly IUnitofwork Services_Repo;
        private readonly ILogger _logger = Log.ForContext<ActivityController>();

        public ActivityController(IUnitofwork unitofwork)
        {
            this.Services_Repo = unitofwork;
        }

        #region Create Log
        /// <summary>
        /// Create Log
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost("CreateLog")]
        public async Task<IActionResult> CreateLog(ActivityDto model)
        {
            var result = await this.Services_Repo.ActivityService.CreateLog(model);
            if (result)
            {
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            else
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Failed",
                    ResponseCode = "400",
                    Data = result
                });
            }
        }
        #endregion

        #region Get All Activities
        /// <summary>
        /// Get all activities
        /// </summary>
        /// <param name="paginationParameters"></param>
        /// <returns></returns>
        [HttpGet("GetAllAcitivities")]
        public async Task<IActionResult> GetAllAcitivities([FromQuery] PaginationParameters paginationParameters)
        {
            var loggedInUser = CurrentUserId.ToString();
            var result = await this.Services_Repo.ActivityService.GetActivities(paginationParameters);
            if (result.Items.Count() > 0)
            {
                var description = $"All activities retrieved by {CurrentUser}";
                var summary = "All activities retrieved";
                await LogActivity(description, summary, null);

                return Ok(new ApiResponse<Page<Activity>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            else
            {
                return BadRequest(new ApiResponse<Page<Activity>>
                {
                    ResponseMessage = "Activity Not Found",
                    ResponseCode = "404",
                    Data = null
                });
            }
        }
        #endregion

        #region Get All Tenants Activities Statistics
        /// <summary>
        /// Get all tenants activities
        /// </summary>
        /// <param name="applications"></param>
        /// <returns></returns>
        [HttpGet("Tenant/GetAllActivities")]
        public async Task<IActionResult> GetAllTenantsAcitivities([FromQuery] Applications applications)
        {
            var result = await this.Services_Repo.ActivityService.GetMonthlyActivityCounts(applications);
            return Ok(result);
        }
        #endregion

        #region Get All Tenants Activities
        /// <summary>
        /// Get all tenants activities
        /// </summary>
        /// <param name="activityQueryParameters"></param>
        /// <returns></returns>
        [HttpGet("Tenant/GetActivities")]
        public async Task<IActionResult> GetTenantsCounts([FromQuery] ActivityQueryParameters activityQueryParameters)
        {
            var result = await this.Services_Repo.ActivityService.GetTenantsCounts(activityQueryParameters);
            return Ok(result);
        }
        #endregion

        #region Get Activities with filters
        /// <summary>
        /// Get activities with filters
        /// </summary>
        /// <param name="filters"></param>
        /// <returns></returns>
        //[Authorize]
        [HttpGet("GetActivitiesWithFilters")]
        public async Task<IActionResult> GetActivitiesWithFilters([FromQuery] AcitivityLogFilters filters)
        {
            try
            {
                filters.UserId = CurrentUserId.ToString();
                var result = await this.Services_Repo.ActivityService.GetActivitiesWithFilters(filters);
                if (result.Data != null)
                {
                    var description = $"Viewed log activities by {CurrentUser}";
                    var summary = $"Viewed log activities";
                    await LogActivity(description, summary, null);

                    return Ok(result);
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Activity Not Found",
                        ResponseCode = "404",
                        Data = false
                    });
                }
            }
            catch (DirtyFormException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "500",
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Activity Seetings
        /// <summary>
        /// Activity Seetings - used to set activity settings and turn log activity on and off
        /// </summary>
        /// <param name="activitySettingsDto"></param>
        /// <returns></returns>
        //[Authorize]
        [HttpPut]
        [Route("ActivitySettings")]
        public async Task<IActionResult> ActivitySettings(ActivitySettingsDto activitySettingsDto)
        {
            try
            {
                var result = await this.Services_Repo.ActivityService.SetActivitySettings(activitySettingsDto);
                if (result)
                {
                    var description = $"Activity settings updated.";
                    var summary = $"Activity settings updated";
                    await LogActivity(description, summary, null);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Successful",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Activity settings not updated",
                        ResponseCode = "400",
                        Data = false
                    });
                }
            }
            catch (DirtyFormException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Activity Settings
        /// <summary>
        /// Get Activity Settings
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetActivitySettings")]
        public async Task<IActionResult> GetActivitySettings()
        {
            try
            {
                var userId = CurrentUserId.ToString();
                var result = await this.Services_Repo.ActivityService.GetActivitySettings(userId);
                if (result != null)
                {
                    var description = $"Activity settings retrieved by {CurrentUser}";
                    var summary = $"Activity settings retrieved";
                    await LogActivity(description, summary, null);
                }

                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Company Activity Settings
        /// <summary>
        /// Get Company Activity Settings
        /// </summary>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [HttpGet("Company/GetActivitySettings")]
        public async Task<IActionResult> GetCompanyActivitySettings([Required] string subdomain)
        {
            var result = await this.Services_Repo.ActivityService.GetCompanyActivitySettings(subdomain);
            if (result != null)
            {
                var description = $"Company activity settings retrieved by {CurrentUser}";
                var summary = $"Company activity settings retrieved";
                await LogActivity(description, summary, null);
            }

            return Ok(result);
        }
        #endregion

        #region Activity Permission Request
        /// <summary>
        /// Activity Permission Request
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize, HttpPost]
        [Route("ActivityPermissionRequest")]
        public async Task<IActionResult> ActivityPermissionRequest(List<ActivityRequestedPermisssionsDto> model)
        {
            foreach (var item in model)
                item.RequesterId = CurrentUserId.ToString();

            var result = await this.Services_Repo.ActivityService.RequestForPermissions(model);
            if (result)
            {
                var description = $"Activity permission requested by {CurrentUser}";
                var summary = $"Activity permission requested";
                await LogActivity($"{description}", summary, null);

                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            else
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Activity permission request process failed",
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Activity Permissions Request For Teams
        /// <summary>
        /// Activity Permissions Request For Teams
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ActivityPermissionsRequestForTeams")]
        public async Task<IActionResult> ActivityPermissionsRequestForTeams([FromBody] ActivityRequestedPermisssionsForTeamMembersDto model)
        {
            model.RequesterId = CurrentUserId.ToString();
            var result = await this.Services_Repo.ActivityService.RequestForPermissionsForTeamMembers(model);
            if (result)
            {
                var description = $"Activity permission per team retrived by {CurrentUser}";
                var summary = $"Got activity permission per team retrived";
                await LogActivity(description, summary, String.Join(",", model.TeamIds));

                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = true
                });
            }
            else
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Activity permission per team not found",
                    ResponseCode = "404",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Requested Activity Permissions Per User
        /// <summary>
        /// Get Requested Activity Permissions Per User
        /// </summary>
        /// <returns></returns>
        [Authorize, HttpGet]
        [Route("GetRequestedActivityPermissionsPerUser")]
        public async Task<IActionResult> GetRequestedActivityPermissionsPerUser()
        {
            Log.Information("GetRequestedActivityPermissionsPerUser action called");
            var result = await this.Services_Repo.ActivityService.GetRequestedPermissionPerUser(CurrentUserId.ToString());
            if (result != null)
            {
                var description = $"Activity requested permissions for a user retrieved by {CurrentUser}";
                var summary = $"Requested activity permissions per user retrieved";
                await LogActivity(description, summary, null);
            }
            return Ok(new ApiResponse<List<ActivityRequestedPermisssions>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Grant or Reject Activity Permissions Requests
        /// <summary>
        /// Grant or Reject Activity Permissions Requests
        /// </summary>
        /// <param name="requestId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("GrantOrRejectActivityPermissionsRequests")]
        public async Task<IActionResult> GrantOrRejectActivityPermissionsRequests(string requestId, ApprovalStatus status)
        {
            Log.Information("GrantOrRejectActivityPermissionsRequests action called");
            var result = await this.Services_Repo.ActivityService.GrantOrRejectActivityPermissionsRequest(requestId, status);
            if (result)
            {
                var description = $"Activity permission granted or rejected by {CurrentUser}";
                var summary = $"Activity permission granted or rejected";
                var activityModel = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Activity,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                    EventId = requestId
                };

                await Services_Repo.ActivityService.CreateLog(activityModel);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            else
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Activity permission grant or reject process failed",
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Activity Permission requested by a user
        /// <summary>
        /// Get Activity Permission requested by a user
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetActivityPermissionRequestedByUser")]
        public async Task<IActionResult> GetActivityPermissionRequestedByUser()
        {
            Log.Information("GetActivityPermissionRequestedByUser action called");
            var result = await this.Services_Repo.ActivityService.GetPermissionRequestsByUser(CurrentUserId.ToString());
            if (result != null)
            {
                var description = $"Activity Retrived";
                var summary = $"Got activity permission requested by user";
                var activityModel = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Activity,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                };
                await Services_Repo.ActivityService.CreateLog(activityModel);
            }
            return Ok(new ApiResponse<List<ActivityRequestedPermisssions>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Get user that granted permission to the logged in user
        /// <summary>
        /// This get all the users that have grated access to the logged in user to view their activities
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserThatGrantedPermissionToLoggedInUser")]
        public async Task<IActionResult> GetUserThatGrantedPermissionToLoggedInUser()
        {
            Log.Information("GetUserThatGrantedPermissionToLoggedInUser action called");
            var result = await this.Services_Repo.ActivityService.GetUsersThatGrantedAccessToLoggedInUser(CurrentUserId.ToString());
            if (result != null)
            {
                var description = $"Activity Retrived";
                var summary = $"Got user that granted permission to logged in user";
                var activityModel = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Activity,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                };
                await Services_Repo.ActivityService.CreateLog(activityModel);
            }
            return Ok(new ApiResponse<bool>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = true
            });
        }
        #endregion

        #region Share Activity
        /// <summary>
        /// Share activity with other users
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ShareActivity")]
        public async Task<IActionResult> ShareActivity([FromBody] ShareActivityDto model)
        {
            try
            {
                Log.Information("ShareActivity action called");
                var loggedInUser = CurrentUserId.ToString();
                var result = await this.Services_Repo.ActivityService.ShareActivity(model, loggedInUser);
                if (result.Data is not null)
                {
                    var description = $"Activity shared by {CurrentUser}";
                    var summary = $"Activity shared";
                    await LogActivity(description, summary);

                    return Ok(result);
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Activity sharing process failed",
                        ResponseCode = "400",
                        Data = false
                    });
                }
            }
            catch (DirtyFormException ex)
            {
                Log.Error("An error occured" + ex.Message);
                return BadRequest(new ApiResponse<bool> { ResponseMessage = ex.Message, ResponseCode = "400", Data = false });
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error("An error occured" + ex.Message);
                return BadRequest(new ApiResponse<bool> { DevResponseMessage = ex.Message, ResponseMessage = "Something went wrong, please try again later", ResponseCode = "500", Data = false });
            }
        }
        #endregion

        #region Private Methods
        private async Task LogActivity(string description, string summary, string eventId = null)
        {
            var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString(), EventCategory.Activity);
            if (canLogActivity)
            {
                var activityModel = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Activity,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                    EventId = eventId
                };

                await Services_Repo.ActivityService.CreateLog(activityModel);
            }
        }
        #endregion
    }
}
