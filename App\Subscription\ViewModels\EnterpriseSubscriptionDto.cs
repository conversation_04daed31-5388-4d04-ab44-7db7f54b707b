﻿using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils.Attributes;
using Jobid.App.Helpers.ViewModel;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.Subscription.ViewModels
{
    public class EnterpriseSubscriptionDto
    {
        public EnterPriseOptionsDto EnterPriseOptions { get; set; }

        /// <summary>
        /// Admin and Comapany Details
        /// <!-- This is the user that created the subscription and the company details -->-->
        /// </summary>
        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        public string MiddleName { get; set; }

        [Required]
        [StringLength(50, ErrorMessage = "First name cannot be longer than 50 characters.")]
        public string CompanyName { get; set; }

        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        [ValidCompanyEmailDomian]
        [StringLength(50, ErrorMessage = "Email cannot be longer than 50 characters.")]
        public string Email { get; set; }

        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        [IsPersonalEmail]
        public string PersonalEmail { get; set; }

        [Required]
        [DataType(DataType.PhoneNumber)]
        public string PhoneNumber { get; set; }

        [Required]
        [StringLength(50, ErrorMessage = "First name cannot be longer than 50 characters.")]
        public string Domain { get; set; }

        [Required]
        public string CompanyType { get; set; }

        [Required]
        public string Country { get; set; }

        public Base64VM? Logo { get; set; }

        public string Designation { get; set; }

        public Industries Industry { get; set; }

        [IsDomainFormatValid]
        public List<string> SecDomains { get; set; } = new List<string>();
    }

    public class EnterpriseSubscriptionForExistingUserDto
    {
        public EnterPriseOptionsDto EnterPriseOptions { get; set; }

        [Required]
        public string UserId { get; set; }

        [Required]
        [StringLength(50, ErrorMessage = "First name cannot be longer than 50 characters.")]
        public string CompanyName { get; set; }

        [Required]
        [StringLength(50, ErrorMessage = "First name cannot be longer than 50 characters.")]
        public string Domain { get; set; }

        [Required]
        public string CompanyType { get; set; }

        [Required]
        public string Country { get; set; }

        public Base64VM? Logo { get; set; }

        public string Designation { get; set; }

        public Industries Industry { get; set; }

        [Required]
        [DataType(DataType.EmailAddress)]
        [ValidEmailCheck]
        [ValidCompanyEmailDomian]
        [StringLength(50, ErrorMessage = "Email cannot be longer than 50 characters.")]
        public string Email { get; set; }

        [IsDomainFormatValid]
        public List<string> SecDomains { get; set; } = new List<string>();
    }

    public class EnterPriseOptionsDto
    {
        /// <summary>
        /// Subscription etails
        /// </summary>
        public Applications Application { get; set; }
        public PaymentProviders Provider { get; set; }

        [Required]
        public string PlanId { get; set; }
        public string SubscriptionId { get; set; }

        [Required]
        public int ProjectLimit { get; set; }

        [Required]
        public int InternalCommunicationHistoryLimit { get; set; }
        public int DataRetentionPeriodInMonth { get; set; }
        public bool TimeSheetManagement { get; set; }

        [Required]
        public int ActivityLogHistoryLimit { get; set; }

        [Required]
        public int CalenderLimit { get; set; }

        [Required]
        public int StorageLimit { get; set; }

        [Required]
        public int UsersLimit { get; set; }

        public bool AiAssistants { get; set; }

        [Required]
        public double AmountPaid { get; set; }

        public string PaymentId { get; set; }

        public DateTime? TransactionDate { get; set; }

        public DateTime? ActivatedOn { get; set; }

        public DateTime ExpiresOn { get; set; }

        public Currency Currency { get; set; } = Currency.USD;

        public string TransactionCode { get; set; }

        public string TenantId { get; set; }

        public bool CreateWithOutPayment { get; set; }

        [Required]
        public string LoggedInUserId { get; set; }
    }
}
