﻿using Jobid.App.Helpers.Models;
using System;
using System.Collections.Generic;

namespace Jobid.App.Calender.ViewModel
{
    public class CalenderEventDto
    {
        public string MeetingId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string Location { get; set; }
        public DateTime StartDateAndTime { get; set; }
        public DateTime EndDateAndTime { get; set; }

        // Emails and name of other invited attendees. Key is the email and value is the name of the user
        public Dictionary<string, string> Attendees { get; set; } = new Dictionary<string, string>();

        // Email of the invitee
        public string Email { get; set; }
        public UserProfile Organizer { get; set; }
        public int NotifyMeIn { get; set; }
        public string subdomain { get; set; }
        public string MeetingLink { get; set; }
        public string Frequency { get; set; }
        public CustomFrequencyDto? CustomFrequencyDto { get; set; } = null;
    }
}
