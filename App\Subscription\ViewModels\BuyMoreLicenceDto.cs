﻿using Jobid.App.Helpers.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Jobid.App.Subscription.ViewModels
{
    public class BuyMoreLicenceDto
    {
        public int NumberOfUsers { get; set; }

        [JsonIgnore]
        public string Subdomain { get; set; }

        public Applications App { get; set; }

        public bool ApplyAtTheEndOfCurrentSub { get; set; }

        public List<string> UserIds { get; set; } = new List<string>();

        public bool FirstCall { get; set; } = true;

        [JsonIgnore]
        public string LoggedInUserId { get; set; }
    }
}
