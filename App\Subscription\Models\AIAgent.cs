﻿using Jobid.App.Helpers.Enums;
using System;

namespace Jobid.App.Subscription.Models
{
    public class AIAgent
    {
        public Guid Id { get; set; }
        public string Agent { get; set; }
        public double AmountPerMonthPerUser { get; set; }
        public double AmountPerYearPerUser { get; set; }
        public string UpdatedBy { get; set; }
        public Currency Currency { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }

        public AIAgent()
        {
            Id = Guid.NewGuid();
            CreatedOn = DateTime.UtcNow;
            AmountPerYearPerUser = 0.0;
            AmountPerMonthPerUser = 0.0;
            Currency = Currency.EUR;
        }
    }
}
