using Jobid.App.Helpers.Utils;
using static Jobid.App.Helpers.Utils.Utility;
namespace Jobid.App.Tenant.SchemaTenant.SchemaContext
{
    public interface IDbContextSchema
    {
        string Schema { get; }
    }

    public class DbContextSchema : IDbContextSchema
    {
        public string Schema { get; }
        public DbContextSchema(string schema = Constants.PUBLIC_SCHEMA) // schema=Constants.PUBLIC_SCHEMA
        {
            Schema = schema.prepareSubdomainName();
        }
    }
}