﻿using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Filters;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.Utils._Helper;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;

namespace Jobid.App.JobProjectManagement.Controllers
{
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    [EndpointTrackerFilter(Applications.Joble, ApplicationSection.Todo)]
    public class TodoController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private readonly ITenantSchema _tenantSchema;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private ILogger _logger = Log.ForContext<TodoController>();

        public TodoController(IUnitofwork unitofwork, ITenantSchema tenantSchema, IAWSS3Sevices aWSS3Sevices)
        {
            this.Services_Repo = unitofwork;
            _tenantSchema = tenantSchema;
            _aWSS3Sevices = aWSS3Sevices;
        }

        #region Create Todo
        /// <summary>
        /// Create a todo for a project
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("AddProjectMgmt_Todo")]
        public async Task<IActionResult> CreateTodo([FromForm] CreateTodoDto model)
        {
            try
            {
                Log.Information("TodoController: Post: AddProjectMgmt_Todo");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.SubDomain = subdomain;

                ProjectMgmt_Project Project = null;
                if (model.SprintId is null && model.ProjectId is not null)
                {
                    Project = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(new Guid(model.ProjectId));
                    if (Project == null)
                    {
                        return NotFound(new ApiResponse<List<ProjectMgmt_Todo>>
                        {
                            Data = null,
                            ResponseCode = "400",
                            ResponseMessage = "Project not found",
                        });
                    }
                }

                var result = await Services_Repo.TodoService.AddProjectMgmt_Todo(model, Project);
                if (result != null)
                {
                    return Ok(new ApiResponse<List<ProjectMgmt_Todo>>
                    {
                        ResponseCode = "200",
                        ResponseMessage = " Todo created successfully.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<List<ProjectMgmt_Todo>>
                    {
                        Data = null,
                        ResponseCode = "500",
                        ResponseMessage = "Todo failed to be created, please try again.",
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.ToString(), nameof(CreateTodo));
                return BadRequest(new ApiResponse<List<ProjectMgmt_Todo>> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE });
            }
            catch (DirtyFormException ex)
            {
                Log.Error(ex.ToString(), nameof(CreateTodo));
                return BadRequest(new ApiResponse<List<ProjectMgmt_Todo>> { Data = null, ResponseCode = "400", ResponseMessage = ex.Message, });
            }
            catch (FileUploadException ex)
            {
                Log.Error(ex.ToString(), nameof(CreateTodo));
                return BadRequest(new ApiResponse<List<ProjectMgmt_Todo>> { Data = null, ResponseCode = "400", ResponseMessage = ex.Message, });
            }
        }
        #endregion

        #region Add Bulk Todo
        /// <summary>
        /// Create a todo for a project
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("AddBulkTodo")]
        public async Task<IActionResult> AddBulkTodo(BulkTodoVM model)
        {
            try
            {
                Log.Information("TodoController: Post: AddBulkTodo");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.SubDomain = subdomain;

                var result = await Services_Repo.TodoService.AddBulkTodo(model);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = " Todos created successfully.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "500",
                        ResponseMessage = "Todos failed to be created, please try again.",
                    });
                }
            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex.ToString(), nameof(AddBulkTodo));

                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Uploads todo file to AWS
        /// <summary>
        /// Uploades todo files to AWS
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="uploadedFile"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="FileUploadException"></exception>
        [HttpPost("UploadTodoFile/{todoId}")]
        public async Task<IActionResult> UploadTodoFile(string todoId, IFormFile uploadedFile)
        {
            if (todoId == null) { throw new ArgumentNullException("Id cannot be null"); }

            var allowedExtensions = new string[] { ".jpg", ".jpeg", ".png", ".pdf", ".docx", ".doc" };
            var extension = Path.GetExtension(uploadedFile.FileName);

            if (!allowedExtensions.Contains(extension))
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = "File extension not allowed",
                    Data = false
                });
            }

            try
            {
                using var memoryStream = new MemoryStream();
                await uploadedFile.CopyToAsync(memoryStream);
                Guid guid = Guid.NewGuid();
                var fileTrimmed = uploadedFile.FileName.Replace(" ", "");
                var fileName = guid.ToString()
                                        .Replace('-', '0')
                                        .Replace('_', '0')
                                        .ToUpper() + "-" + fileTrimmed;

                var imageUrl = (await _aWSS3Sevices.UploadFileAsync(uploadedFile, fileName));

                await Services_Repo.ProjectFileService.AddNewProjectFileAsync(new ProjectFile()
                {
                    FileName = fileName,
                    ProjectMgmt_TodoId = new Guid(todoId)
                });

                if (string.IsNullOrEmpty(imageUrl))
                {
                    throw new FileUploadException("File upload was not successful");
                }

                return Ok(new ApiResponse<bool>
                {
                    ResponseCode = "200",
                    ResponseMessage = "File uploaded successfully",
                    Data = true
                });
            }
            catch (FileUploadException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
        }
        #endregion

        #region Gets todo files
        /// <summary>
        /// Gets a todo file
        /// </summary>
        /// <param name="todoId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetTodoFile/{todoId}")]
        public async Task<ApiResponse<List<TodoFilesDto>>> GetProjectfilebyId(string todoId)
        {
            var result = await this.Services_Repo.TodoService.GetTodoUplaodedFiles(todoId);
            return new ApiResponse<List<TodoFilesDto>>
            {
                ResponseMessage = "Successful",
                ResponseCode = "200",
                Data = result
            };
        }
        #endregion

        #region Get users assigned to a todo by todo id
        /// <summary>
        /// Gets todo user by todoId
        /// </summary>
        /// <param name="todoId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetTodoUsers/{todoId}")]
        public async Task<IActionResult> GetTodoUsers(string todoId)
        {
            try
            {
                var result = await this.Services_Repo.TodoService.GetTodoUsersByTodoId(todoId);
                return Ok(new ApiResponse<List<UserDto>>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return NotFound(new ApiResponse<List<UserDto>>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "404",
                    Data = null
                });
            }
        }
        #endregion

        #region Delete Todo
        /// <summary>
        /// Deletes a Todo
        /// </summary>
        /// <param name="todoId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpDelete]
        [Route("DeleteTodo/{todoId}")]
        public async Task<IActionResult> DeleteTodo(string todoId)
        {
            if (todoId == null) { return BadRequest(new { Msg = " Todo Id Is Required !" }); }
            try
            {
                var result = await this.Services_Repo.TodoService.DeleteProjectMgmt_Todo(todoId, CurrentUserId.ToString());
                if (result == false) { return BadRequest(new { Mgs = " Todo Not Deleted !" }); }
                return Ok(new { Message = "Todo Deleted Successfully", Status = "200", Data = true });
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "403",
                    Data = false
                });
            }
        }
        #endregion

        #region Update Todo Order or Status
        /// <summary>
        /// Updates Todo Order or Status
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("UpdateTodoOrderOrStatus")]
        public async Task<IActionResult> UpdateTodoOrderOrStatus([FromBody] List<UpdateTodoOrderDto> model)
        {
            if (model == null)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = " Todo Id Is Required !",
                    ResponseCode = "400",
                    Data = false
                });
            }

            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
            var result = await this.Services_Repo.TodoService.UpdateTodoStatusAndOrder(model, CurrentUserId.ToString(), subdomain);
            return Ok(new ApiResponse<bool>
            {
                ResponseMessage = "Todo Updated Successfully",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Update Todo Status
        /// <summary>
        /// Updates Todo Status
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("UpdateTodoStatus/{todoId}")]
        public async Task<IActionResult> UpdateTodoStatus(string todoId, [FromQuery] ProjectManagementStatus status)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await this.Services_Repo.TodoService.UpdateTodoStatus(todoId, status, CurrentUserId.ToString(), subdomain);
                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Todo Updated Successfully",
                    ResponseCode = "200",
                    Data = true
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex.ToString(), nameof(UpdateTodoStatus));

                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Assign external or internal user to todo
        /// <summary>
        /// Assign external or internal user to todo
        /// </summary>
        /// <param name="todoId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("AssignUserToTodo/{todoId}")]
        public async Task<IActionResult> AssignUserToTodo(string todoId, [FromBody] AssignUserToTodoDto model)
        {
            try
            {
                var loggedInUser = CurrentUserId.ToString();
                model.LoggedInUserId = loggedInUser;

                var subdomain = this.HttpContext.Request.Headers["subdomain"].ToString();
                model.Subdomain = subdomain;

                var result = await this.Services_Repo.TodoService.AssignUserToTodo(todoId, model);

                if (result == false)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "User Could Not Be Assigned To Todo",
                        ResponseCode = "400",
                        Data = false
                    });
                }

                return Ok(new ApiResponse<bool>
                {
                    ResponseMessage = "Todo assigned successfully",
                    ResponseCode = "200",
                    Data = true
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = ex.Message,
                    ResponseCode = "400",
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    DevResponseMessage = ex.Message,
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "400",
                    Data = false
                });
            }
        }
        #endregion

        #region Deletes a todo file
        /// <summary>
        /// Deletes a todo file
        /// </summary>
        /// <param name="todoId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpDelete]
        [Route("DeleteTodoFile/{todoId}")]
        public async Task<ApiResponse<ProjectFile>> DeleteProjectfilebyId(string todoId)
        {
            Guid newid = new Guid(todoId);
            var result = await this.Services_Repo.ProjectFileService.DeleteFilesAsync(newid);
            if (result)
            {
                return new ApiResponse<ProjectFile>
                {
                    ResponseMessage = "Successful",
                    ResponseCode = "200"
                };
            }
            else
            {
                return new ApiResponse<ProjectFile>
                {
                    ResponseMessage = "Todo File Not Found",
                    ResponseCode = "404",
                    Data = null
                };
            }
        }
        #endregion

        #region Get todo by todoId
        /// <summary>
        /// Gets a single todo using todoId
        /// </summary>
        /// <param name="TodoId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetProjectMgmt_Todo/{TodoId}")]
        public async Task<ApiResponse<ProjectMgmt_Todo>> GetTodoById(Guid TodoId)
        {
            var Project = await Services_Repo.TodoService.GetProjectMgmt_Todo(TodoId);
            if (Project == null)
            {
                return new ApiResponse<ProjectMgmt_Todo>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Todo not found",
                };
            }
            else
            {
                return new ApiResponse<ProjectMgmt_Todo>
                {
                    Data = Project,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get todos by userId
        /// <summary>
        /// Gets all todos by userId
        /// </summary>
        /// <param name="UserId"></param>
        /// <param name="statusParam"></param>
        ///  /// <param name="meMode"></param>
        /// <param name="strictStartDate"></param>
        /// <param name="createdByAI"></param>
        /// <param name="app"></param>
        /// <param name="parameters"></param>
        /// <param name="todoFilterForMobile"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetProjectMgmt_TodoByUserId/{UserId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodosByUserId(string UserId, Applications? app, [FromQuery] PaginationParameters parameters, string statusParam = null, bool meMode = false, bool strictStartDate = false, bool createdByAI = false, TodoFilterForMobile? todoFilterForMobile = null, string searchParam = null)
        {
            try
            {
                var todos = await Services_Repo.TodoService.GetTodoSByUserId(UserId, app, parameters, statusParam, meMode, strictStartDate, createdByAI, todoFilterForMobile, searchParam);

                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = todos,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Todo Weekly Analytics
        /// <summary>
        /// Get User Todo Analytics
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("weekly_analytics/{UserId}")]
        public async Task<ApiResponse<WeeklyActivityAnalytics>> GetUserTodoWeeklyAnalytics(string UserId)
        {
            try
            {
                var subdomain = this.HttpContext.Request.Headers["subdomain"].ToString();
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                var responseBody = await Services_Repo.TodoService.GetUserWeeklyTodoAnalytics(UserId, subdomain, token);

                return new ApiResponse<WeeklyActivityAnalytics>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<WeeklyActivityAnalytics>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Time Suggestions For Todo
        /// <summary>
        /// Suggests available time for todo creation
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPost]
        [Route("TimeSuggestionsForTodo")]
        public async Task<IActionResult> TimeSuggestionsForTodo([FromBody] SuggestTimeForTodoCreationDto model)
        {
            var responseBody = await Services_Repo.TodoService.SuggestTimeForTodoCreation(model);
            return responseBody.ResponseCode == "200" ? Ok(responseBody) : BadRequest(responseBody);
        }
        #endregion

        #region Get all todos
        /// <summary>
        /// Gets all todos
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="statusParam"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetAllProjectMgmt_Todo")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetAll([FromQuery] PaginationParameters parameters, Applications? app, string statusParam = null)
        {
            var Projects = await Services_Repo.TodoService.GetAllProjectMgmt_Todo(parameters, app, statusParam);
            if (Projects.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "200",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = Projects,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get all todos without project or sprint
        /// <summary>
        /// Get all todos without project or sprint
        /// </summary>
        /// <param name="parameters"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetAllTodoWithOutProjectOrSprint")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetAllTodoWithOutProjectOrSprint([FromQuery] PaginationParameters parameters,
            Applications? app)
        {
            var Projects = await Services_Repo.TodoService.GetAllTodoWithoutSprintOrProject(parameters, app);

            return new ApiResponse<Page<ProjectMgmt_Todo>>
            {
                Data = Projects,
                ResponseCode = "200",
                ResponseMessage = "Todos found",
            };
        }
        #endregion

        #region Get Todo Status using sprintId
        /// <summary>
        /// Gets Todo Status using sprintId
        /// </summary>
        /// <param name="sprintId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetTodoStatusBySprintId/{sprintId}")]
        public async Task<ApiResponse<List<TodoStatus>>> GetTodoStatusBySprintId(Guid sprintId)
        {
            var todoStatus = await Services_Repo.TodoService.GetTodoStatusBySprintId(sprintId);
            return new ApiResponse<List<TodoStatus>>
            {
                Data = todoStatus,
                ResponseCode = "200",
                ResponseMessage = "Todo Status found",
            };
        }
        #endregion

        #region Get all todos by projectId
        /// <summary>
        /// Get all todos by projectId
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetAllProjectMgmt_Todo/{projectId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAll([FromQuery] PaginationParameters parameters, Guid projectId)
        {
            var Projects = await Services_Repo.TodoService.GetAllProjectMgmt_TodobyprojectId(parameters, projectId);
            if (Projects.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = Projects,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get todo by sprintId
        /// <summary>
        /// Get todos by sprint Id
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="sprintId"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetAllProjectMgmt_TodoBySprint/{sprintId}")]
        public async Task<IActionResult> GetTodoAllBySprintId([FromQuery] PaginationParameters parameters, Guid sprintId, [FromQuery] GetTodoBySprintFilters filters)
        {
            var Projects = await Services_Repo.TodoService.GetAllProjectMgmt_TodobysprintId(parameters, sprintId, CurrentUserId.ToString(), filters);
            if (Projects.Items.Length < 1)
            {
                return BadRequest(new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                });
            }
            else
            {
                return Ok(new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = Projects,
                    ResponseCode = "200",
                    ResponseMessage = "Todos found",
                });
            }
        }
        #endregion

        #region Reschedule Todo
        /// <summary>
        /// Reschedule Todo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("RescheduleTodo")]
        public async Task<ApiResponse<bool>> RescheduleTodoAsync([FromBody] ReScheduleTodoDto model)
        {
            try
            {
                Log.Information("TodoController: Put: Reschedule todo");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.Subdomain = subdomain;
                model.UserId = User.GetUserId();
                if (model.EndTime <= model.StartDateAndTime || (int)(model.EndTime - model.StartDateAndTime).TotalHours > 24)
                {
                    return new ApiResponse<bool>
                    {
                        ResponseMessage = (int)(model.EndTime - model.StartDateAndTime).TotalHours > 24 ? "Time allocation cannot be more than 24 hours" : "Invalid Date Range. Start time cannot be greater than End time",
                        ResponseCode = "400",
                        Data = false
                    };
                }

                var result = await Services_Repo.TodoService.RescheduleTodo(model);
                if (result)
                {
                    return new ApiResponse<bool>
                    {
                        Data = true,
                        ResponseCode = "200",
                        ResponseMessage = "Todo rescheduled successfully",
                    };
                }
                else
                {
                    return new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "500",
                        ResponseMessage = "Something went wrong while rescheduling todo, please try again",
                    };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                };
            }
            catch (RecordNotFoundException ex)
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Update Todo
        /// <summary>
        /// Updates Todo
        /// </summary>
        /// <param name="model"></param>
        /// <param name="TodoId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("UpdateProjectMgmt_Todo/{TodoId}")]
        public async Task<ApiResponse<bool>> UpdateTodo([FromForm] TodoUpdateVm model, Guid TodoId)
        {
            try
            {
                Log.Information("TodoController: Post: AddProjectMgmt_Todo");
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.SubDomain = subdomain;

                if (model.UploadFile.Any())
                {
                    var allowedExtensions = new string[] { ".jpg", ".jpeg", ".png", ".pdf" };
                    foreach (var file in model.UploadFile)
                    {
                        var extension = Path.GetExtension(file.FileName);

                        if (!allowedExtensions.Contains(extension))
                        {
                            return new ApiResponse<bool>
                            {
                                ResponseCode = "400",
                                ResponseMessage = "File extension not allowed",
                                Data = false
                            };
                        }
                    }
                }

                //if (model.EndTime <= model.StartDateAndTime || (int)(model.EndTime - model.StartDateAndTime).TotalHours > 24)
                //{
                //    return new ApiResponse<bool>
                //    {
                //        ResponseMessage = (int)(model.EndTime - model.StartDateAndTime).TotalHours > 24 ? "Time allocation cannot be more than 24 hours" : "Invalid Date Range. Start time cannot be greater than End time",
                //        ResponseCode = "400",
                //        Data = false
                //    };
                //}

                ProjectMgmt_Project Project = null;
                if (!string.IsNullOrEmpty(model.SprintId))
                {
                    Project = await Services_Repo.ProjectService.GetProjectMgmt_SprintId(new Guid(model.SprintId));
                    if (Project == null)
                    {
                        return new ApiResponse<bool>
                        {
                            Data = false,
                            ResponseCode = "400",
                            ResponseMessage = "Project not found",
                        };
                    }
                }

                if (string.IsNullOrEmpty(model.SprintId) && !string.IsNullOrEmpty(model.ProjectId))
                {
                    Project = await Services_Repo.ProjectService.GetProjectMgmt_ProjectId(new Guid(model.ProjectId));
                    if (Project == null)
                    {
                        return new ApiResponse<bool>
                        {
                            Data = false,
                            ResponseCode = "400",
                            ResponseMessage = "Project not found",
                        };
                    }
                }

                model.LoggedInUserId = CurrentUserId.ToString();
                var result = await Services_Repo.TodoService.UpdateProjectTodo(model, TodoId, Project);
                if (result)
                {
                    return new ApiResponse<bool>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Todo updated successfully",
                    };
                }
                else
                {
                    return new ApiResponse<bool>
                    {
                        Data = result,
                        ResponseCode = "500",
                        ResponseMessage = "Todo update failed, please try again.",
                    };
                }
            }
            catch (RecordNotFoundException ex)
            {
                return new ApiResponse<bool> { Data = false, ResponseCode = "404", ResponseMessage = ex.Message, };
            }
            catch (InvalidOperationException ex)
            {
                return new ApiResponse<bool> { Data = false, ResponseCode = "400", ResponseMessage = ex.Message, };
            }
            catch (UnauthorizedAccessException ex)
            {
                return new ApiResponse<bool> { Data = false, ResponseCode = "401", ResponseMessage = ex.Message, };
            }
            catch (FileUploadException ex)
            {
                return new ApiResponse<bool> { Data = false, ResponseCode = "500", ResponseMessage = ex.Message, };
            }
            catch (DirtyFormException ex)
            {
                return new ApiResponse<bool> { Data = false, ResponseCode = "400", ResponseMessage = ex.Message, };
            }
        }
        #endregion

        #region Move todo to sprint or project
        /// <summary>
        /// Move todo to sprint or project
        /// </summary>
        /// <param name="TodoId"></param>
        /// <param name="sprintId"></param>
        /// <param name="projectId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPatch]
        [Route("MoveTodo/{TodoId}")]
        public async Task<IActionResult> MoveTodoToSprintOrProject(Guid TodoId, string sprintId, string projectId)
        {
            try
            {
                Log.Information("TodoController: Patch: MoveTodoToSprintOrProject");
                var res = await this.Services_Repo.TodoService.MoveTodoToProjectOrSprint(TodoId, CurrentUserId.ToString(), sprintId, projectId);
                return Ok(new ApiResponse<bool>
                {
                    Data = res,
                    ResponseCode = "200",
                    ResponseMessage = "Todo moved successfully",
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                });
            }
            catch (RecordNotFoundException ex)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Update todo count down and status(optional)
        /// <summary>
        /// Update todo count down
        /// </summary>
        /// <param name="TodoId"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpPut]
        [Route("UpdateTodoCountDown/{TodoId}")]
        public async Task<IActionResult> UpdateTodoCountDown(Guid TodoId, string time, string status = null)
        {
            try
            {
                Log.Information("TodoController: Put: UpdateTodoCountDown");
                var res = await this.Services_Repo.TodoService.UpdateTodoCountDownAndTimeSpent(TodoId, time, CurrentUserId.ToString(), status);
                return Ok(new ApiResponse<bool>
                {
                    Data = res,
                    ResponseCode = "200",
                    ResponseMessage = "Todo count down updated successfully",
                });
            }
            catch (RecordNotFoundException ex)
            {
                return NotFound(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Get todos under a project by name parameter
        /// <summary>
        /// Get todos under a project by name parameter
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="projectId"></param>
        /// <param name="todoName"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_edit_delete_todos)]
        [HttpGet]
        [Route("GetAllProjectMgmt_Todo/search/{projectId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAllByName([FromQuery] PaginationParameters parameters, Guid projectId, [FromQuery] string todoName)
        {
            var Projects = await Services_Repo.TodoService.GetAllProjectMgmt_TodobyTodoName(parameters, projectId, todoName);
            if (Projects.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = Projects,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get all todos by kpi referenceId
        /// <summary>
        /// This endpoint returns all todos created from KPI
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="kpiId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllKpiTodo/{kpiId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAllByKpiReferenceId([FromQuery] PaginationParameters parameters, long kpiId)
        {
            var todosKpi = await Services_Repo.TodoService.GetAllTodoByKpiReferenceId(parameters, kpiId);
            if (todosKpi.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = todosKpi,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get all todos by company referenceId
        /// <summary>
        /// Get all todo's by company id
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="companyId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllCompanyTodo/{companyId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAllByCompanyReferenceId([FromQuery] PaginationParameters parameters, long companyId)
        {
            var companyTodos = await Services_Repo.TodoService.GetAllTodoByCompanyReferenceId(parameters, companyId);
            if (companyTodos.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = companyTodos,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get all todos by Lead referenceId
        /// <summary>
        /// This endpoint returns all todos created from Lead
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="LeadId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllLeadTodo/{LeadId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAllByLeadReferenceId([FromQuery] PaginationParameters parameters, long LeadId)
        {
            var todosKpi = await Services_Repo.TodoService.GetAllTodoByLeadReferenceId(parameters, LeadId);
            if (todosKpi.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = todosKpi,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get all todos by Deal referenceId
        /// <summary>
        /// Get all todo's by Deal id
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="DealId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllDealTodo/{DealId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAllByDealReferenceId([FromQuery] PaginationParameters parameters, long DealId)
        {
            var companyTodos = await Services_Repo.TodoService.GetAllTodoByDealReferenceId(parameters, DealId);
            if (companyTodos.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = companyTodos,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get all todos by Contact referenceId
        /// <summary>
        /// Get all todo's by Contact id
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="ContactId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllContactTodo/{ContactId}")]
        public async Task<ApiResponse<Page<ProjectMgmt_Todo>>> GetTodoAllByContactReferenceId([FromQuery] PaginationParameters parameters, long ContactId)
        {
            var companyTodos = await Services_Repo.TodoService.GetAllTodoByContactReferenceId(parameters, ContactId);
            if (companyTodos.Items.Length < 1)
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "No Todo found",
                };
            }
            else
            {
                return new ApiResponse<Page<ProjectMgmt_Todo>>
                {
                    Data = companyTodos,
                    ResponseCode = "200",
                    ResponseMessage = "Todo found",
                };
            }
        }
        #endregion

        #region Get User Weekly Company Activity Score
        /// <summary>
        /// Get User Weekly Company ActivityScore
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserWeeklyCompanyActivityScore/{UserId}")]
        public async Task<ApiResponse<List<UserWeeklyActivityScore>>> GetUserWeeklyCompanyActivityScore(string UserId, DateTime? StartDate, DateTime? EndDate)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                var responseBody = await Services_Repo.TodoService.GetUserWeeklyCompanyActivityScore(subdomain, UserId, StartDate, EndDate);

                return new ApiResponse<List<UserWeeklyActivityScore>>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<List<UserWeeklyActivityScore>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Weekly Industry Activity Score
        /// <summary>
        /// Get User Weekly Industry ActivityScore
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserWeeklyIndustryActivityScore/{UserId}")]
        public async Task<ApiResponse<List<CompanyWeeklyAnalytics>>> GetUserWeeklyIndustryActivityScore(string UserId, DateTime? StartDate, DateTime? EndDate)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                var responseBody = await Services_Repo.TodoService.GetUserWeeklyIndustryActivityScore(subdomain, UserId, StartDate, EndDate);

                return new ApiResponse<List<CompanyWeeklyAnalytics>>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<List<CompanyWeeklyAnalytics>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Weekly Industry Activity Score
        /// <summary>
        /// Get User Weekly Global ActivityScore
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserWeeklyGlobalActivityScore/{UserId}")]
        public async Task<ApiResponse<List<CompanyWeeklyAnalytics>>> GetUserWeeklyGlobalActivityScore(string UserId, DateTime? StartDate, DateTime? EndDate)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                var responseBody = await Services_Repo.TodoService.GetUserWeeklyGlobalActivityScore(subdomain, UserId, StartDate, EndDate);

                return new ApiResponse<List<CompanyWeeklyAnalytics>>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<List<CompanyWeeklyAnalytics>>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Todo Completion Rate Analytics
        /// <summary>
        /// Get User Weekly Global ActivityScore
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserCompletedTodoAnalytics/{UserId}")]
        public async Task<ApiResponse<TodoAnalyticsVM>> GetUserCompletedTodoAnalytics(string UserId)
        {
            try
            {
                var responseBody = await Services_Repo.TodoService.GetUserTodoCompletedAnalyticsVM(UserId);

                return new ApiResponse<TodoAnalyticsVM>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<TodoAnalyticsVM>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Todo Speed Stamina Analytics
        /// <summary>
        /// Get User Weekly Global ActivityScore
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserTodoSpeedStaminaAnalytics/{UserId}")]
        public async Task<ApiResponse<TodoSpeedStaminaVM>> GetUserTodoSpeedStaminaAnalytics(string UserId)
        {
            try
            {
                var responseBody = await Services_Repo.TodoService.GetTodoSpeedStamina(UserId);

                return new ApiResponse<TodoSpeedStaminaVM>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<TodoSpeedStaminaVM>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Monthly Todo Completed by week
        /// <summary>
        /// Get User Weekly Global ActivityScore
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserMonthlyCompletedTodoByWeek/{UserId}")]
        public async Task<ApiResponse<MonthlyTodoCompletedByWeek>> GetUserMonthlyTodoCompletedByWeek(string UserId)
        {
            try
            {
                var responseBody = await Services_Repo.TodoService.GetMonthlyTodoCompletedByWeek(UserId);

                return new ApiResponse<MonthlyTodoCompletedByWeek>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<MonthlyTodoCompletedByWeek>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion

        #region Get User Todo Analytics by status
        /// <summary>
        /// Get User Weekly Global ActivityScore
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserTodoAnalyticsByStatus/{UserId}")]
        public async Task<ApiResponse<TodoStatusAnalyticsDictionary>> GetUserTodoAnalyticsByStatus(string userId, [FromQuery] DateTime startDate, [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var responseBody = await Services_Repo.TodoService.GetUserTodoAnalyticsByStatus(userId, startDate, endDate);

                return new ApiResponse<TodoStatusAnalyticsDictionary>
                {
                    Data = responseBody,
                    ResponseCode = "200",
                    ResponseMessage = "Successful",
                };
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return new ApiResponse<TodoStatusAnalyticsDictionary>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                };
            }
        }
        #endregion
    }
}
