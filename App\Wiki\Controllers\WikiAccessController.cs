using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Wiki.Services.Contract;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Wiki.Enums;
using Jobid.App.Wiki.ViewModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;

namespace Jobid.App.Wiki.Controllers
{
    public class WikiAccessController : BaseController
    {
        private readonly IUnitofwork _services;
        private readonly ILogger _logger = Log.ForContext<WikiAccessController>();

        public WikiAccessController(IUnitofwork services)
        {
            _services = services;
        }

        /// <summary>
        /// Update wiki access for multiple users
        /// </summary>
        /// <param name="model">List of user IDs and their access status</param>
        /// <returns>Success status</returns>
        [Authorize]
        [HttpPost]
        [Route("UpdateWikiAccess")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        public async Task<IActionResult> UpdateWikiAccess([FromBody] WikiAccessBulkUpdateDto model)
        {
            try
            {
                var result = await _services.WikiAccess.UpdateWikiAccess(model.AccessUpdates);
                return Ok(await ConvertDateTimeToLocalDateTime(result));
            }
            catch (System.Exception ex)
            {
                _logger.Error(ex, "Error updating wiki access");
                return StatusCode(500, new GenericResponse { ResponseCode = "500", ResponseMessage = "An error occurred while updating wiki access" });
            }
        }

        /// <summary>
        /// Get all team members with their wiki access status
        /// </summary>
        /// <param name="filter">Filter by access status (All, Access, NoAccess)</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>List of team members with their wiki access status</returns>
        [Authorize]
        [HttpGet]
        [Route("GetTeamMembersWithWikiAccess")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        public async Task<IActionResult> GetTeamMembersWithWikiAccess(
            [FromQuery] string filter = WikiAccessFilterEnum.All,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var paginationParameters = new PaginationParameters
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                var result = await _services.WikiAccess.GetAllTeamMembersWithWikiAccess(filter, paginationParameters);
                return Ok(await ConvertDateTimeToLocalDateTime(result));
            }
            catch (System.Exception ex)
            {
                _logger.Error(ex, "Error retrieving team members with wiki access");
                return StatusCode(500, new GenericResponse { ResponseCode = "500", ResponseMessage = "An error occurred while retrieving team members with wiki access" });
            }
        }
    }
}
