using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Jobid.App.Helpers.Enums;

namespace Jobid.App.Helpers.ViewModel
{
    public class OTPVM
    {
        public string Id { get; set; }
        // phone or email identifier
        public string Identifier { get; set; }
        public string IdentifierType { get; set; }
        public DateTime DateCreated { get; set; }
        public string Status { get; set; }
        public DateTime LastUpdate { get; set; }
        public string Token { get; set; }
    }

    public class GenerateOTPVM
    {
        public string Identifier { get; set; }
        public OTPIdentifierType IdentifierType { get; set; }
        public bool IsWhatsAppNo { get; set; }
        public bool ForCheckingIfUserExists { get; set; }
    }

    public class NewOTPVM
    {
        public string Identifier { get; set; }
        public bool IsWhatsAppNo { get; set; } = false;
        public OTPIdentifierType IdentifierType { get; set; }
        public OTPTokenType TokenType { get; set; }
        public bool ForCheckingIfUserExists { get; set; }
    }
    

    

    public class VerifyTokenOTPVM
    {
        public string Identifier { get; set; }
        public OTPIdentifierType IdentifierType { get; set; }
        public string Token { get; set; }
    }
}
