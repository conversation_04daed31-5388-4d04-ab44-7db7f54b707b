﻿using Microsoft.Extensions.Hosting;
using System.Threading;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

public class ApplicationShutdownService : IHostedService
{
    private readonly IServiceProvider _services;

    public ApplicationShutdownService(IServiceProvider services)
    {
        _services = services;
    }

    Task IHostedService.StartAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    Task IHostedService.StopAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Dispose of any IDisposable services gracefully
            var disposableServices = _services.GetServices<IDisposable>()
                .ToList();

            foreach (var service in disposableServices)
            {
                service.Dispose();
            }

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error disposing services: {ex.Message}");
            return Task.FromException(ex);
        }
    }
}