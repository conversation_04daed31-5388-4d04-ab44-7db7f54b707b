﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Jobid.App.Helpers.Utils.Attributes
{
    public class ValidEmailCheck : ValidationAttribute
    {
        public ValidEmailCheck() { }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var email = value as string;
            if (email != null)
            {
                string pattern = @"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$";
                Regex regex = new Regex(pattern);

                // Check if the email matches the pattern
                if (regex.IsMatch(email))
                {
                    return ValidationResult.Success;
                }
                else
                {
                    return new ValidationResult("Email is not valid");
                }
            }

            return ValidationResult.Success;
        }
    }
}
