﻿using Jobid.App.AdminConsole.Enums;
using System.ComponentModel.DataAnnotations;
using System;

namespace Jobid.App.Helpers.Models
{
    public class AccountDeletionRequest
    {
        [Key]
        public Guid Id { get; set; }

        [Required]
        public string RequestId { get; set; }

        [Required]
        public string UserId { get; set; }

        public string Reason { get; set; }

        public AccountDeletionStatus Status { get; set; }

        public DateTime DateRequested { get; set; }

        public DateTime? UpdatedOn { get; set; }

        public string ActionPerfomedBy { get; set; }

        public AccountDeletionRequest()
        {
            Id = Guid.NewGuid();
            DateRequested = DateTime.UtcNow;
            Status = AccountDeletionStatus.Pending;
        }
    }
}
