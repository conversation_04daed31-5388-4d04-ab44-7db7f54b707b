﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class added_app_usage_tbl : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public added_app_usage_tbl(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EndpointCallTrackers",
                schema: _Schema,
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Application = table.Column<int>(type: "integer", nullable: false),
                    Section = table.Column<string>(type: "text", nullable: true),
                    Controller = table.Column<string>(type: "text", nullable: true),
                    Action = table.Column<string>(type: "text", nullable: true),
                    HttpMethod = table.Column<string>(type: "text", nullable: true),
                    Date = table.Column<DateTime>(type: "timestamp", nullable: false),
                    Count = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EndpointCallTrackers", x => x.Id);
                });

            //migrationBuilder.DropForeignKey(
            //    name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId",
            //    schema: _Schema, 
            //    table: "ExternalMeeting");

            //migrationBuilder.AddForeignKey(
            //    name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId",
            //    schema: _Schema, 
            //    table: "ExternalMeeting",
            //    column: "PersonalScheduleId",
            //    principalSchema: _Schema,
            //    principalTable: "PersonalSchedule",
            //    principalColumn: "Id",
            //    onDelete: ReferentialAction.NoAction);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EndpointCallTrackers",
                schema: _Schema);

            migrationBuilder.DropForeignKey(
                name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId",
                schema: _Schema,
                table: "ExternalMeeting");

            migrationBuilder.AddForeignKey(
                name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId",
                schema: _Schema,
                table: "ExternalMeeting",
                column: "PersonalScheduleId",
                principalSchema: _Schema,
                principalTable: "PersonalSchedule",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
