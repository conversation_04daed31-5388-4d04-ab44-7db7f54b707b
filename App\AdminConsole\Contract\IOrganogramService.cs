﻿using System.Threading.Tasks;
using System.Threading;
using System;
using Jobid.App.AdminConsole.Dto.Organogram;
using System.Collections.Generic;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;

namespace Jobid.App.AdminConsole.Contract
{
    /// <summary>
    /// Interface for managing organizational structure including parent and subsidiary companies
    /// </summary>
    public interface IOrganogramService
    {
        /// <summary>
        /// Creates a new parent company in the organization structure
        /// </summary>
        /// <param name="dto">The data for creating the parent company</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created company details</returns>
        Task<OrganogramCompanyResponseDto> CreateParentCompanyAsync(CreateParentCompanyDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new subsidiary company under a specified parent company
        /// </summary>
        /// <param name="dto">The data for creating the subsidiary company</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created company details</returns>
        Task<OrganogramCompanyResponseDto> CreateSubsidiaryCompanyAsync(CreateSubsidiaryCompanyDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing parent company's information
        /// </summary>
        /// <param name="dto">The updated company information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated company details</returns>
        Task<OrganogramCompanyResponseDto> UpdateParentCompanyAsync(UpdateOrganogramCompanyDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing subsidiary company's information
        /// </summary>
        /// <param name="dto">The updated company information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated company details</returns>
        Task<OrganogramCompanyResponseDto> UpdateSubsidiaryCompanyAsync(UpdateOrganogramCompanyDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a subsidiary company if it has no associated departments or employees
        /// </summary>
        /// <param name="id">The ID of the subsidiary company to delete</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>True if the company was deleted, false if it wasn't found</returns>
        Task<bool> DeleteSubsidiaryCompanyAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new department in a company
        /// </summary>
        /// <param name="dto">The data for creating the department</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The created department details</returns>
        Task<DepartmentResponseDto> CreateDepartmentAsync(CreateDepartmentDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing department's information
        /// </summary>
        /// <param name="dto">The updated department information</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>The updated department details</returns>
        Task<DepartmentResponseDto> UpdateDepartmentAsync(UpdateDepartmentDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a department if it has no associated employees or sub-departments
        /// </summary>
        /// <param name="id">The ID of the department to delete</param>
        /// <param name="cancellationToken">Cancellation token for async operations</param>
        /// <returns>True if the department was deleted, false if it wasn't found</returns>
        Task<bool> DeleteDepartmentAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new employee position in a department
        /// </summary>
        Task<EmployeePositionResponseDto> CreateEmployeePositionAsync(CreateEmployeePositionDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing employee position
        /// </summary>
        Task<EmployeePositionResponseDto> UpdateEmployeePositionAsync(UpdateEmployeePositionDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes an employee position if it's not assigned to any employees
        /// </summary>
        Task<bool> DeleteEmployeePositionAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new individual in the organization
        /// </summary>
        Task<IndividualResponseDto> CreateIndividualAsync(CreateIndividualDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing individual's information
        /// </summary>
        Task<IndividualResponseDto> UpdateIndividualAsync(UpdateIndividualDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes an individual from the organization
        /// </summary>
        Task<bool> DeleteIndividualAsync(Guid id, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the complete organizational hierarchy for a given subdomain
        /// </summary>
        /// <param name="subdomain">The subdomain to get the hierarchy for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The complete organizational hierarchy</returns>
        Task<OrganogramHierarchyDto> GetOrganizationHierarchyAsync(string subdomain, CancellationToken cancellationToken = default);


        /// <summary>
        /// Retrieves a list of user suggestions based on a search term matching first name, middle name, or last name
        /// </summary>
        /// <param name="searchTerm">The search term to match against user names. Can be part of first name, middle name, or last name</param>
        /// <param name="subdomain">The subdomain to restrict the search to</param>
        /// <param name="maxResults">Maximum number of suggestions to return (default is 10)</param>
        /// <param name="cancellationToken">A cancellation token that can be used to cancel the operation</param>
        /// <returns>A list of user suggestions containing user details</returns>
        Task<List<UserSuggestionDto>> GetUserSuggestionsAsync(string searchTerm, int maxResults = 10, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets basic information for a specific user
        /// </summary>
        /// <param name="userId">The user ID to get basic info for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The user's basic information</returns>
        Task<BasicInfoResponseDto> GetBasicInfoAsync(Guid userId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates basic information for a specific user
        /// </summary>
        /// <param name="dto">The updated basic information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated basic information</returns>
        Task<BasicInfoResponseDto> UpdateBasicInfoAsync(UpdateBasicInfoDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets emergency contact information for a specific user
        /// </summary>
        /// <param name="userId">The user ID to get emergency contact info for</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The user's emergency contact information</returns>
        Task<EmergencyContactResponseDto> GetEmergencyContactAsync(Guid userId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates emergency contact information for a specific user
        /// </summary>
        /// <param name="dto">The updated emergency contact information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated emergency contact information</returns>
        Task<EmergencyContactResponseDto> UpdateEmergencyContactAsync(UpdateEmergencyContactDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates basic information for a user
        /// </summary>
        /// <param name="dto">The basic information to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created basic information</returns>
        Task<BasicInfoResponseDto> AddBasicInfoAsync(CreateBasicInfoDto dto, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates emergency contact information for a user
        /// </summary>
        /// <param name="dto">The emergency contact information to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created emergency contact information</returns>
        Task<EmergencyContactResponseDto> AddEmergencyContactAsync(CreateEmergencyContactDto dto, CancellationToken cancellationToken = default);
        Task<GenericResponse> RegisterComplaint(RegisterComplaintDto dto, string subDomain, CancellationToken cancellationToken = default);
        Task<ApiResponse<GetRegisteredComplaint>> ViewUserComplaints(string userId,string id, string subDomain);
        Task<GenericResponse> WithdrawAction(string userId, string id, string subDomain);
        Task<Page<GetRegisteredComplaint>> GetAllUserComplaints(string userId, PaginationParameters parameters, string subDomain);

    }
}