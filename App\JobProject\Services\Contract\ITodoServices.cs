﻿using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;
using TodoStatus = Jobid.App.JobProjectManagement.Models.TodoStatus;

namespace Jobid.App.JobProject.Services.Contract
{
    public interface ITodoServices
    {
        Task<List<ProjectMgmt_Todo>> AddProjectMgmt_Todo(CreateTodoDto model, ProjectMgmt_Project project = null);
        Task<bool> AddBulkTodo(BulkTodoVM model);
        Task<ProjectMgmt_Todo> GetProjectMgmt_Todo(Guid id);
        Task<WeeklyActivityAnalytics> GetUserWeeklyTodoAnalytics(string userId, string subdomain, string token);
        Task<Page<ProjectMgmt_Todo>> GetTodoSByUserId(string userId, Applications? app, PaginationParameters parameters, string statusParam = null, bool meMode = false, bool strictStartDate = false, bool createdByAI = false, TodoFilterForMobile? todoFilterForMobile = null, string serachParam = null);
        Task<List<TodoFilesDto>> GetTodoUplaodedFiles(string todoId);
        Task<Page<ProjectMgmt_Todo>> GetAllTodoWithoutSprintOrProject(PaginationParameters parameters, Applications? app);
        Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_Todo(PaginationParameters parameters, Applications? app, string statusParam = null);
        Task<bool> UpdateProjectTodo(TodoUpdateVm model, Guid tagId, ProjectMgmt_Project project = null);
        Task<bool> DeleteProjectMgmt_Todo(string id, string loggedInUserId);
        Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_TodobyprojectId(PaginationParameters parameters, Guid projectId);
        Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_TodobysprintId(PaginationParameters parameters, Guid sprintId, string userId, GetTodoBySprintFilters filters = null);
        Task<Page<ProjectMgmt_Todo>> GetAllProjectMgmt_TodobyTodoName(PaginationParameters parameters, Guid projectId, string todoName);
        Task<bool> UpdateTodoStatusAndOrder(List<UpdateTodoOrderDto> model, string loggedInUserId, string subdomain);
        Task<List<TodoStatus>> GetTodoStatusBySprintId(Guid sprintId);
        Task<GenericResponse> SuggestTimeForTodoCreation(SuggestTimeForTodoCreationDto model);
        Task<bool> MoveTodoToProjectOrSprint(Guid todoId, string userId, string sprintId = null, string projectId = null);
        Task<bool> UpdateTodoCountDownAndTimeSpent(Guid todoId, string time, string loggedInUserId, string status);
        Task<bool> AssignUserToTodo(string todoId, AssignUserToTodoDto model);
        Task<Page<ProjectMgmt_Todo>> GetAllTodoByKpiReferenceId(PaginationParameters parameters, long kpiId);
        Task<Page<ProjectMgmt_Todo>> GetAllTodoByCompanyReferenceId(PaginationParameters parameters, long companyId);
        Task<Page<ProjectMgmt_Todo>> GetAllTodoByLeadReferenceId(PaginationParameters parameters, long LeadId);
        Task<Page<ProjectMgmt_Todo>> GetAllTodoByDealReferenceId(PaginationParameters parameters, long DealId);
        Task<Page<ProjectMgmt_Todo>> GetAllTodoByContactReferenceId(PaginationParameters parameters, long ContactId);
        Task<bool> RescheduleTodo(ReScheduleTodoDto model);
        Task<List<UserDto>> GetTodoUsersByTodoId(string todoId);
        Task<ApiResponse<bool>> UpdateTodoStatus(string todoId, ProjectManagementStatus status, string loggedInUserId, string subdoamin);
        Task<List<CompanyWeeklyAnalytics>> GetUserWeeklyGlobalActivityScore(string subdomain, string userId, DateTime? startDateRange, DateTime? endDateRange);
        Task<List<CompanyWeeklyAnalytics>> GetUserWeeklyIndustryActivityScore(string subdomain, string userId, DateTime? startDateRange, DateTime? endDateRange);
        Task<List<UserWeeklyActivityScore>> GetUserWeeklyCompanyActivityScore(string subdomain, string userId, DateTime? startDateRange, DateTime? endDateRange);
        Task<TodoAnalyticsVM> GetUserTodoCompletedAnalyticsVM(string userId);
        Task<TodoSpeedStaminaVM> GetTodoSpeedStamina(string userId);
        Task<MonthlyTodoCompletedByWeek> GetMonthlyTodoCompletedByWeek(string userId);
        Task<TodoStatusAnalyticsDictionary> GetUserTodoAnalyticsByStatus(string userId, DateTime dateTime, DateTime? endDate = null);
        Task<List<TodoOptimalTimeResponse>> GetUserTodoStartOptimalTime(List<TodoOptimalTimeCheck> model);
    }
}
