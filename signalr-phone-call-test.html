<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SignalR Phone Call Events Test</title>
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1,
      h2 {
        color: #333;
      }
      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: bold;
      }
      .connected {
        background-color: #d4edda;
        color: #155724;
      }
      .disconnected {
        background-color: #f8d7da;
        color: #721c24;
      }
      .event-log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        margin: 10px 0;
      }
      .event-item {
        margin: 5px 0;
        padding: 5px;
        border-radius: 3px;
      }
      .event-received {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
      }
      .event-sent {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
      }
      .event-error {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
      }
      .controls {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 20px 0;
      }
      button {
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
      }
      .btn-primary {
        background-color: #007bff;
        color: white;
      }
      .btn-primary:hover {
        background-color: #0056b3;
      }
      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }
      .btn-secondary:hover {
        background-color: #545b62;
      }
      .btn-success {
        background-color: #28a745;
        color: white;
      }
      .btn-success:hover {
        background-color: #1e7e34;
      }
      .btn-danger {
        background-color: #dc3545;
        color: white;
      }
      .btn-danger:hover {
        background-color: #c82333;
      }
      .btn-warning {
        background-color: #ffc107;
        color: black;
      }
      .btn-warning:hover {
        background-color: #e0a800;
      }
      input,
      select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin: 5px;
      }
      .form-group {
        margin: 10px 0;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        margin: 20px 0;
      }
      .stat-item {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        text-align: center;
      }
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
      }
      .stat-label {
        font-size: 12px;
        color: #666;
      }
      .config-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔊 SignalR Phone Call Events Test</h1>

      <div class="config-section">
        <h3>Configuration</h3>
        <div class="form-group">
          <label for="serverUrl">Server URL:</label>
          <input
            type="text"
            id="serverUrl"
            value="https://localhost:44376/callHub"
            style="width: 300px"
          />
        </div>
        <div class="form-group">
          <label for="phoneNumberId">Phone Number ID:</label>
          <input
            type="text"
            id="phoneNumberId"
            value="615b5135-7782-4921-8d57-4e61e200c233"
            placeholder="Enter phone number ID"
          />
        </div>
        <div class="form-group">
          <label for="userId">User ID:</label>
          <input
            type="text"
            id="userId"
            value="cfd54e01-21d0-44d5-877f-4d2092f92876"
            placeholder="Enter user ID"
          />
        </div>
        <div class="form-group">
          <label for="jwtToken">JWT Token (for authentication):</label>
          <textarea
            id="jwtToken"
            placeholder="Enter JWT token for authentication (optional)"
            style="
              width: 100%;
              height: 80px;
              font-family: monospace;
              font-size: 10px;
            "
          >
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QgmApSyJXAmhZKr_-RB21EeE5k6wSEmiYQRPTj6QImo</textarea
          >
        </div>
      </div>

      <div id="connectionStatus" class="status disconnected">Disconnected</div>

      <div class="stats">
        <div class="stat-item">
          <div class="stat-value" id="eventsReceived">0</div>
          <div class="stat-label">Events Received</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="eventsSent">0</div>
          <div class="stat-label">Events Sent</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="errors">0</div>
          <div class="stat-label">Errors</div>
        </div>
      </div>

      <div class="controls">
        <button id="connectBtn" class="btn-primary">Connect</button>
        <button id="disconnectBtn" class="btn-secondary">Disconnect</button>
        <button id="associateUserBtn" class="btn-primary">
          Associate User Connection
        </button>
        <button id="testUserConnectionBtn" class="btn-warning">
          Test User Connection
        </button>
        <button id="clearLogBtn" class="btn-warning">Clear Log</button>
        <button id="exportLogBtn" class="btn-secondary">Export Log</button>
      </div>
    </div>

    <div class="container">
      <h2>🔗 User Connection Association</h2>
      <div class="config-section">
        <h4>Understanding User Connection Association:</h4>
        <p>
          For SignalR to send events to specific users via
          <code>Clients.User(userId).SendAsync()</code>, it needs to know which
          connection belongs to which user. This is handled by:
        </p>
        <ul>
          <li>
            <strong>IUserIdProvider:</strong> Maps connections to user IDs
            (configured in Startup.cs)
          </li>
          <li>
            <strong>JWT Claims (Recommended):</strong> Automatic user ID
            extraction from authentication tokens (sub, userId, NameIdentifier
            claims)
          </li>
          <li>
            <strong>AssociateUserConnection:</strong> Hub method to manually
            associate connection with user ID (fallback for anonymous
            connections)
          </li>
        </ul>

        <h4>Authentication Methods:</h4>
        <ol>
          <li>
            <strong>JWT Token (Recommended):</strong> Enter a valid JWT token
            above. The token should contain user claims like 'sub' or 'userId'
            matching your User ID.
          </li>
          <li>
            <strong>Manual Association:</strong> Leave JWT token blank and use
            the "Associate User Connection" button after connecting.
          </li>
        </ol>

        <h4>Testing Steps:</h4>
        <ol>
          <li>
            Enter a JWT token (hardcoded test token provided) OR leave blank for
            manual association
          </li>
          <li>Click "Connect" to establish SignalR connection</li>
          <li>
            If no JWT token: Click "Associate User Connection" to link your
            connection with your User ID
          </li>
          <li>Click "Test User Connection" to verify the association</li>
          <li>
            Use "Test: Initiate Call via API" to trigger real user-targeted
            events
          </li>
        </ol>
      </div>
    </div>

    <div class="container">
      <h2>SignalR Hub Methods</h2>
      <div class="controls">
        <button id="joinGroupBtn" class="btn-success">Join Call Group</button>
        <button id="leaveGroupBtn" class="btn-danger">Leave Call Group</button>
        <button id="updateStatusBtn" class="btn-warning">
          Update Call Status
        </button>
      </div>

      <div class="form-group">
        <label for="callStatus">Call Status:</label>
        <select id="callStatus">
          <option value="ringing">Ringing</option>
          <option value="connected">Connected</option>
          <option value="ended">Ended</option>
          <option value="failed">Failed</option>
          <option value="busy">Busy</option>
          <option value="no-answer">No Answer</option>
        </select>
      </div>

      <div class="form-group">
        <label for="callId">Call ID:</label>
        <input
          type="text"
          id="callId"
          value="test-call-123"
          placeholder="Enter call ID"
        />
      </div>
    </div>

    <div class="container">
      <h2>📡 User-Targeted Events Testing</h2>
      <p>
        <strong
          >Testing `Clients.User(userId).SendAsync()` functionality:</strong
        >
      </p>
      <div class="config-section">
        <h4>How User-Targeted Events Work:</h4>
        <ul>
          <li>
            <strong>Group Events:</strong>
            <code>Clients.Group(phoneNumberId).SendAsync()</code> - Sends to all
            users in a group
          </li>
          <li>
            <strong>User-Specific Events:</strong>
            <code>Clients.User(userId).SendAsync()</code> - Sends only to
            specific user
          </li>
          <li>
            <strong>Broadcast Events:</strong>
            <code>Clients.All.SendAsync()</code> - Sends to all connected
            clients
          </li>
        </ul>

        <h4>Test Scenarios:</h4>
        <ol>
          <li>
            <strong>Connect</strong> with your User ID:
            <code>cfd54e01-21d0-44d5-877f-4d2092f92876</code>
          </li>
          <li>
            <strong>Initiate Call</strong> - This triggers:
            <code
              >await
              _callHubContext.Clients.User(request.UserId).SendAsync("CallSessionCreated",
              callSession);</code
            >
          </li>
          <li><strong>Watch Event Log</strong> for user-specific events</li>
        </ol>
      </div>

      <div class="form-group">
        <label for="targetUserId">Target User ID for Testing:</label>
        <input
          type="text"
          id="targetUserId"
          value="cfd54e01-21d0-44d5-877f-4d2092f92876"
          placeholder="User ID to send events to"
        />
        <button id="testUserEvent" class="btn-primary">
          Test Send Event to User
        </button>
      </div>
    </div>

    <div class="container">
      <h2>Test Real Server Events</h2>
      <p>
        <strong>Instructions to test CallSessionCreated:</strong>
      </p>
      <ol>
        <li>
          Make sure your JobSuite backend is running on https://localhost:44376
        </li>
        <li>Click "Connect" to establish SignalR connection</li>
        <li>
          Click "Join Call Group" to subscribe to events for your phone number
        </li>
        <li>
          Use your backend API to initiate a call (this will trigger
          CallSessionCreated)
        </li>
        <li>Watch the event log below for real-time events</li>
      </ol>

      <div class="form-group">
        <label for="testPhoneNumber">Test Phone Number (to call):</label>
        <input
          type="text"
          id="testPhoneNumber"
          value="+2347062746869"
          placeholder="Enter phone number to call"
        />
      </div>

      <div class="controls">
        <button id="testInitiateCall" class="btn-primary">
          Test: Initiate Call via API
        </button>
        <button id="testAnswerCall" class="btn-success">
          Test: Answer Call via API
        </button>
        <button id="testEndCall" class="btn-danger">
          Test: End Call via API
        </button>
      </div>
    </div>

    <div class="container">
      <h2>Simulate Client Events (Local Testing)</h2>
      <p>These buttons simulate events locally for testing the UI:</p>
      <div class="controls">
        <button id="simulateIncomingCall" class="btn-primary">
          Simulate Incoming Call
        </button>
        <button id="simulateCallSessionCreated" class="btn-primary">
          Simulate Call Session Created
        </button>
        <button id="simulateCallStatusUpdate" class="btn-primary">
          Simulate Call Status Update
        </button>
        <button id="simulateParticipantJoined" class="btn-success">
          Simulate Participant Joined
        </button>
        <button id="simulateParticipantLeft" class="btn-danger">
          Simulate Participant Left
        </button>
        <button id="simulateCallEnded" class="btn-danger">
          Simulate Call Ended
        </button>
      </div>
    </div>

    <div class="container">
      <h2>Event Log</h2>
      <div id="eventLog" class="event-log"></div>
    </div>

    <script>
      let connection;
      let eventsReceived = 0;
      let eventsSent = 0;
      let errors = 0;

      // DOM elements
      const connectBtn = document.getElementById("connectBtn");
      const disconnectBtn = document.getElementById("disconnectBtn");
      const associateUserBtn = document.getElementById("associateUserBtn");
      const testUserConnectionBtn = document.getElementById(
        "testUserConnectionBtn"
      );
      const clearLogBtn = document.getElementById("clearLogBtn");
      const exportLogBtn = document.getElementById("exportLogBtn");
      const joinGroupBtn = document.getElementById("joinGroupBtn");
      const leaveGroupBtn = document.getElementById("leaveGroupBtn");
      const updateStatusBtn = document.getElementById("updateStatusBtn");
      const connectionStatus = document.getElementById("connectionStatus");
      const eventLog = document.getElementById("eventLog");
      const serverUrl = document.getElementById("serverUrl");
      const phoneNumberId = document.getElementById("phoneNumberId");
      const userId = document.getElementById("userId");
      const jwtToken = document.getElementById("jwtToken");
      const callStatus = document.getElementById("callStatus");
      const callId = document.getElementById("callId");

      // Simulation buttons
      const simulateIncomingCall = document.getElementById(
        "simulateIncomingCall"
      );
      const simulateCallSessionCreated = document.getElementById(
        "simulateCallSessionCreated"
      );
      const simulateCallStatusUpdate = document.getElementById(
        "simulateCallStatusUpdate"
      );
      const simulateParticipantJoined = document.getElementById(
        "simulateParticipantJoined"
      );
      const simulateParticipantLeft = document.getElementById(
        "simulateParticipantLeft"
      );
      const simulateCallEnded = document.getElementById("simulateCallEnded");

      // API test buttons
      const testInitiateCall = document.getElementById("testInitiateCall");
      const testAnswerCall = document.getElementById("testAnswerCall");
      const testEndCall = document.getElementById("testEndCall");
      const testPhoneNumber = document.getElementById("testPhoneNumber");
      const testUserEvent = document.getElementById("testUserEvent");
      const targetUserId = document.getElementById("targetUserId");

      // Utility functions
      function updateStats() {
        document.getElementById("eventsReceived").textContent = eventsReceived;
        document.getElementById("eventsSent").textContent = eventsSent;
        document.getElementById("errors").textContent = errors;
      }

      function logEvent(message, type = "info") {
        const timestamp = new Date().toISOString();
        const eventItem = document.createElement("div");
        eventItem.className = `event-item event-${type}`;
        eventItem.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        eventLog.appendChild(eventItem);
        eventLog.scrollTop = eventLog.scrollHeight;

        if (type === "received") eventsReceived++;
        else if (type === "sent") eventsSent++;
        else if (type === "error") errors++;

        updateStats();
      }

      function updateConnectionStatus(connected) {
        connectionStatus.textContent = connected ? "Connected" : "Disconnected";
        connectionStatus.className = `status ${
          connected ? "connected" : "disconnected"
        }`;
      }

      // SignalR connection setup
      async function connect() {
        try {
          // Request notification permission
          if (
            Notification.permission !== "granted" &&
            Notification.permission !== "denied"
          ) {
            await Notification.requestPermission();
          }

          connection = new signalR.HubConnectionBuilder()
            .withUrl(serverUrl.value, {
              accessTokenFactory: () => {
                const token = jwtToken.value.trim();
                if (token) {
                  logEvent(
                    `🔐 Using JWT token for authentication: ${token.substring(
                      0,
                      50
                    )}...`,
                    "info"
                  );
                  return token;
                }
                logEvent(
                  "⚠️ No JWT token provided - using anonymous connection",
                  "info"
                );
                return null;
              },
            })
            .withAutomaticReconnect()
            .build();

          // Connection events
          connection.onreconnecting((error) => {
            logEvent(
              `Connection lost due to error: ${error}. Reconnecting...`,
              "error"
            );
            updateConnectionStatus(false);
          });

          connection.onreconnected((connectionId) => {
            logEvent(
              `Connection reestablished. Connected with connectionId: ${connectionId}`,
              "info"
            );
            updateConnectionStatus(true);
          });

          connection.onclose((error) => {
            logEvent(`Connection closed due to error: ${error}`, "error");
            updateConnectionStatus(false);
          });

          // Register all SignalR event handlers for phone calls
          setupCallEventHandlers();

          await connection.start();
          logEvent("✅ Connected to SignalR CallHub successfully!", "info");
          logEvent(`📡 Connection ID: ${connection.connectionId}`, "info");
          logEvent(`👤 User ID: ${userId.value}`, "info");

          const currentToken = jwtToken.value.trim();
          if (currentToken) {
            logEvent(`🔐 Connected with JWT authentication`, "info");
            logEvent(
              `🎯 Token contains user claims for automatic user ID resolution`,
              "info"
            );
          } else {
            logEvent(
              `⚠️ Connected without JWT token - using manual user association`,
              "info"
            );
          }

          updateConnectionStatus(true);

          // Test by calling the association method again
          await associateUserConnection();

          // After connection, associate this connection with the user ID for user-targeted events
          if (currentToken) {
            logEvent(
              "🔐 JWT token provided - user should be automatically associated via claims",
              "info"
            );
            logEvent(
              "🎯 Testing if automatic user association from JWT works...",
              "info"
            );
            // With JWT, the CustomUserIdProvider should automatically extract user ID from claims
          } else {
            logEvent(
              "📝 No JWT token - will use manual user association",
              "info"
            );
            // Manual association for anonymous connections
            try {
              await connection.invoke("AssociateUserConnection", userId.value);
              logEvent(
                `✅ Successfully associated connection with user: ${userId.value}`,
                "info"
              );
              logEvent(
                `🎯 This connection can now receive events sent via Clients.User("${userId.value}")`,
                "info"
              );
            } catch (error) {
              logEvent(
                `❌ Failed to associate user connection: ${error}`,
                "error"
              );
              logEvent(
                `⚠️ User-targeted events may not work without proper association`,
                "error"
              );
            }
          }

          connectBtn.disabled = true;
          disconnectBtn.disabled = false;
          associateUserBtn.disabled = false;
          testUserConnectionBtn.disabled = false;
          joinGroupBtn.disabled = false;
          leaveGroupBtn.disabled = false;
          updateStatusBtn.disabled = false;
        } catch (error) {
          logEvent(`❌ Connection failed: ${error}`, "error");
          updateConnectionStatus(false);
        }
      }

      async function disconnect() {
        try {
          await connection.stop();
          logEvent("Disconnected from SignalR Hub", "info");
          updateConnectionStatus(false);

          connectBtn.disabled = false;
          disconnectBtn.disabled = true;
          associateUserBtn.disabled = true;
          testUserConnectionBtn.disabled = true;
          joinGroupBtn.disabled = true;
          leaveGroupBtn.disabled = true;
          updateStatusBtn.disabled = true;
        } catch (error) {
          logEvent(`Disconnection failed: ${error}`, "error");
        }
      }

      async function associateUserConnection() {
        try {
          if (
            !connection ||
            connection.state !== signalR.HubConnectionState.Connected
          ) {
            logEvent("❌ Must be connected to SignalR first", "error");
            return;
          }

          if (!userId.value) {
            logEvent("❌ User ID is required for association", "error");
            return;
          }

          await connection.invoke("AssociateUserConnection", userId.value);
          logEvent(
            `✅ Successfully associated connection with user: ${userId.value}`,
            "sent"
          );
          logEvent(
            `🎯 Connection ${connection.connectionId} is now linked to user ${userId.value}`,
            "info"
          );
          logEvent(
            `📡 Server can now send events via: Clients.User("${userId.value}").SendAsync(...)`,
            "info"
          );
        } catch (error) {
          logEvent(`❌ Failed to associate user connection: ${error}`, "error");
        }
      }

      async function testUserConnection() {
        try {
          if (
            !connection ||
            connection.state !== signalR.HubConnectionState.Connected
          ) {
            logEvent("❌ Must be connected to SignalR first", "error");
            return;
          }

          logEvent("🧪 Testing user connection association...", "info");
          logEvent(
            `📱 Current Connection ID: ${connection.connectionId}`,
            "info"
          );
          logEvent(`👤 Current User ID: ${userId.value}`, "info");

          // Test by calling the association method again
          await associateUserConnection();

          logEvent("✅ User connection test completed", "info");
          logEvent("💡 To verify user-targeted events work:", "info");
          logEvent("   1. Make sure you're connected and associated", "info");
          logEvent(
            "   2. Use the 'Test: Initiate Call via API' button",
            "info"
          );
          logEvent(
            "   3. Watch for CallSessionCreated event in the log",
            "info"
          );
        } catch (error) {
          logEvent(`❌ User connection test failed: ${error}`, "error");
        }
      }

      function setupCallEventHandlers() {
        // Core Call Events
        connection.on("IncomingCall", (data) => {
          logEvent(
            `📞 RECEIVED: Incoming Call - ${JSON.stringify(data)}`,
            "received"
          );
          // Show browser notification if supported
          if (Notification.permission === "granted") {
            new Notification("Incoming Call", {
              body: `From: ${data.fromNumber || "Unknown"}`,
              icon: "📞",
            });
          }
        });

        connection.on("IncomingWebRTCCall", (data) => {
          logEvent(
            `🌐 RECEIVED: Incoming WebRTC Call - ${JSON.stringify(data)}`,
            "received"
          );
          // Show browser notification if supported
          if (Notification.permission === "granted") {
            new Notification("Incoming WebRTC Call", {
              body: `From: ${data.From || data.fromNumber || "Unknown"}\nTo: ${
                data.To || data.toNumber || "Unknown"
              }\nAgent: ${data.AgentId || data.agentId || "N/A"}\nCall Type: ${
                data.CallType || data.callType || "WebRTC"
              }`,
              icon: "🌐",
            });
          }
          // Highlight event in log for easier testing
          logEvent(
            `✨ SUCCESS: IncomingWebRTCCall event received. CallId: ${
              data.CallId || data.callId
            }, AgentId: ${data.AgentId || data.agentId}`,
            "info"
          );
        });

        connection.on("CallSessionCreated", (data) => {
          logEvent(
            `📞 RECEIVED: Call Session Created - ${JSON.stringify(data)}`,
            "received"
          );
          // Highlight this event as it's what we're testing
          if (data.sessionId) {
            logEvent(
              `✨ SUCCESS: CallSessionCreated event received with session ID: ${data.sessionId}`,
              "info"
            );
          }
        });

        connection.on("CallStatusUpdate", (data) => {
          logEvent(
            `📊 RECEIVED: Call Status Update - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallStatusUpdated", (data) => {
          logEvent(
            `📊 RECEIVED: Call Status Updated - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallEnded", (data) => {
          logEvent(
            `📞 RECEIVED: Call Ended - ${JSON.stringify(data)}`,
            "received"
          );
        });

        // Call State Events
        connection.on("CallRinging", (data) => {
          logEvent(
            `📞 RECEIVED: Call Ringing - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallConnected", (data) => {
          logEvent(
            `📞 RECEIVED: Call Connected - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallBusy", (data) => {
          logEvent(
            `📞 RECEIVED: Call Busy - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallFailed", (data) => {
          logEvent(
            `📞 RECEIVED: Call Failed - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallNoAnswer", (data) => {
          logEvent(
            `📞 RECEIVED: Call No Answer - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallStatusChanged", (data) => {
          logEvent(
            `📊 RECEIVED: Call Status Changed - ${JSON.stringify(data)}`,
            "received"
          );
        });

        // Participant Events
        connection.on("ParticipantJoined", (data) => {
          logEvent(
            `👤 RECEIVED: Participant Joined - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("ParticipantLeft", (data) => {
          logEvent(
            `👤 RECEIVED: Participant Left - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("ParticipantAdded", (data) => {
          logEvent(
            `👤 RECEIVED: Participant Added - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("ParticipantRemoved", (data) => {
          logEvent(
            `👤 RECEIVED: Participant Removed - ${JSON.stringify(data)}`,
            "received"
          );
        });

        // LiveKit Events
        connection.on("LiveKitParticipantAdded", (data) => {
          logEvent(
            `🎥 RECEIVED: LiveKit Participant Added - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("LiveKitParticipantRemoved", (data) => {
          logEvent(
            `🎥 RECEIVED: LiveKit Participant Removed - ${JSON.stringify(
              data
            )}`,
            "received"
          );
        });

        connection.on("LiveKitParticipantConnected", (data) => {
          logEvent(
            `🎥 RECEIVED: LiveKit Participant Connected - ${JSON.stringify(
              data
            )}`,
            "received"
          );
        });

        connection.on("LiveKitParticipantDisconnected", (data) => {
          logEvent(
            `🎥 RECEIVED: LiveKit Participant Disconnected - ${JSON.stringify(
              data
            )}`,
            "received"
          );
        });

        connection.on("CallBridgedToLiveKit", (data) => {
          logEvent(
            `🌉 RECEIVED: Call Bridged to LiveKit - ${JSON.stringify(data)}`,
            "received"
          );
        });

        // Queue and WebRTC Events
        connection.on("WebRTCCallIncoming", (data) => {
          logEvent(
            `📞 RECEIVED: WebRTC Call Incoming - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("WebRTCCallStatus", (data) => {
          logEvent(
            `📊 RECEIVED: WebRTC Call Status - ${JSON.stringify(data)}`,
            "received"
          );
        });

        connection.on("CallQueueUpdate", (data) => {
          logEvent(
            `📋 RECEIVED: Call Queue Update - ${JSON.stringify(data)}`,
            "received"
          );
        });

        // Generic error handler
        connection.on("error", (error) => {
          logEvent(`❌ SignalR Error: ${error}`, "error");
        });
      }

      // Hub method calls
      async function joinCallGroup() {
        try {
          await connection.invoke("JoinCallGroup", phoneNumberId.value);
          logEvent(`📞 Joined call group: ${phoneNumberId.value}`, "sent");
        } catch (error) {
          logEvent(`Failed to join call group: ${error}`, "error");
        }
      }

      async function leaveCallGroup() {
        try {
          await connection.invoke("LeaveCallGroup", phoneNumberId.value);
          logEvent(`📞 Left call group: ${phoneNumberId.value}`, "sent");
        } catch (error) {
          logEvent(`Failed to leave call group: ${error}`, "error");
        }
      }

      async function updateCallStatus() {
        try {
          await connection.invoke(
            "UpdateCallStatus",
            phoneNumberId.value,
            callStatus.value,
            callId.value
          );
          logEvent(
            `📊 Updated call status: ${callStatus.value} for call ${callId.value}`,
            "sent"
          );
        } catch (error) {
          logEvent(`Failed to update call status: ${error}`, "error");
        }
      }

      // Simulation functions
      function simulateEvent(eventName, data) {
        logEvent(
          `🎬 Simulating event: ${eventName} with data: ${JSON.stringify(
            data
          )}`,
          "sent"
        );
        // Directly call registered handlers for the event
        if (
          connection &&
          connection.handlers &&
          connection.handlers[eventName]
        ) {
          connection.handlers[eventName].forEach((handler) => {
            try {
              handler(data);
            } catch (e) {
              logEvent(`Error in event handler: ${e}`, "error");
            }
          });
        }
      }

      // API Testing functions
      async function testInitiateCallAPI() {
        try {
          logEvent("🔄 Testing API: Initiating call...", "info");

          // Log the API request
          logEvent(
            `📡 API Request: Initiating call with user ID ${userId.value} and phone number ID ${phoneNumberId.value}`,
            "info"
          );
          if (!userId.value || !phoneNumberId.value) {
            logEvent(
              "❌ User ID or Phone Number ID is missing. Please fill in the fields.",
              "error"
            );
            return;
          }

          const headers = {
            "Content-Type": "application/json",
            Accept: "application/json",
            subdomain: "localhost",
          };

          // Add JWT token to API request headers if available
          const token = jwtToken.value.trim();
          if (token) {
            headers.Authorization = `Bearer ${token}`;
            logEvent(`🔐 Including JWT token in API request headers`, "info");
          }

          const response = await fetch(
            "https://localhost:44376/api/telephony/initiate-call",
            {
              method: "POST",
              headers: headers,
              body: JSON.stringify({
                userId: userId.value,
                fromNumberId: phoneNumberId.value,
                toNumber: testPhoneNumber.value,
                userDisplayName: "Test User",
                maxParticipants: 10,
                enableRecording: true,
              }),
            }
          );

          const result = await response.json();

          if (response.ok) {
            logEvent(
              `✅ API Success: Call initiated - ${JSON.stringify(result)}`,
              "info"
            );
            logEvent(
              `🎯 This should trigger CallSessionCreated event for user: ${userId.value}`,
              "info"
            );
            logEvent(
              `📡 Server will send: _callHubContext.Clients.User("${userId.value}").SendAsync("CallSessionCreated", data)`,
              "info"
            );
            logEvent(
              `⏳ Watch for CallSessionCreated event in the log below...`,
              "info"
            );
          } else {
            logEvent(
              `❌ API Error: ${response.status} - ${JSON.stringify(result)}`,
              "error"
            );
          }
        } catch (error) {
          logEvent(`❌ API Request failed: ${error.message}`, "error");
        }
      }

      async function testAnswerCallAPI() {
        try {
          logEvent("🔄 Testing API: Answering call...", "info");

          const response = await fetch(
            "https://localhost:44376/api/telephony/answer-call",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              body: JSON.stringify({
                userId: userId.value,
                callSid: "CA123456789", // You'll need to use actual call SID from previous call
                liveKitRoomName: "call-room-123",
                userDisplayName: "Test User",
              }),
            }
          );

          const result = await response.json();

          if (response.ok) {
            logEvent(
              `✅ API Success: Call answered - ${JSON.stringify(result)}`,
              "info"
            );
          } else {
            logEvent(
              `❌ API Error: ${response.status} - ${JSON.stringify(result)}`,
              "error"
            );
          }
        } catch (error) {
          logEvent(`❌ API Request failed: ${error.message}`, "error");
        }
      }

      async function testEndCallAPI() {
        try {
          logEvent("🔄 Testing API: Ending call...", "info");

          const response = await fetch(
            "https://localhost:44376/api/telephony/end-call",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              body: JSON.stringify({
                userId: userId.value,
                sessionId: "session-123",
                callSid: "CA123456789",
                reason: "Test call termination",
              }),
            }
          );

          const result = await response.json();

          if (response.ok) {
            logEvent(
              `✅ API Success: Call ended - ${JSON.stringify(result)}`,
              "info"
            );
          } else {
            logEvent(
              `❌ API Error: ${response.status} - ${JSON.stringify(result)}`,
              "error"
            );
          }
        } catch (error) {
          logEvent(`❌ API Request failed: ${error.message}`, "error");
        }
      }

      // Test user-targeted event
      async function testUserTargetedEvent() {
        try {
          if (
            !connection ||
            connection.state !== signalR.HubConnectionState.Connected
          ) {
            logEvent("❌ Must be connected to SignalR first", "error");
            return;
          }

          logEvent(
            `🎯 Testing user-targeted event to user: ${targetUserId.value}`,
            "info"
          );
          logEvent(
            `📱 Current Connection ID: ${connection.connectionId}`,
            "info"
          );
          logEvent(`👤 Current User ID: ${userId.value}`, "info");
          logEvent(`🎯 Target User ID: ${targetUserId.value}`, "info");

          // First, ensure user association is set up
          await associateUserConnection();

          // This would simulate what happens when the backend sends:
          // await _callHubContext.Clients.User(targetUserId.value).SendAsync("CallSessionCreated", callSession);

          const testData = {
            sessionId: `user-session-${Date.now()}`,
            callId: `user-call-${Date.now()}`,
            roomName: `user-room-${Date.now()}`,
            participants: [
              {
                userId: targetUserId.value,
                displayName: "Test User (User-Targeted)",
              },
            ],
            status: "initiating",
            targetedToUser: targetUserId.value,
            timestamp: new Date().toISOString(),
          };

          // In a real scenario, this would be sent from the server to the specific user
          logEvent(
            `📡 Server would send: _callHubContext.Clients.User("${targetUserId.value}").SendAsync("CallSessionCreated", data)`,
            "info"
          );
          logEvent(`📞 Event data: ${JSON.stringify(testData)}`, "info");

          // For demonstration, we'll trigger the event locally
          if (targetUserId.value === userId.value) {
            logEvent(
              `✅ You are the target user - event would be received!`,
              "info"
            );
            logEvent(
              `🎯 Since you've associated your connection with user ID ${userId.value}, you should receive this event`,
              "info"
            );
            // Simulate receiving the event
            if (connection.handlers && connection.handlers.CallSessionCreated) {
              connection.handlers.CallSessionCreated.forEach((handler) => {
                try {
                  handler(testData);
                } catch (e) {
                  logEvent(`Error in event handler: ${e}`, "error");
                }
              });
            }
          } else {
            logEvent(
              `⚠️ You are NOT the target user - event would NOT be received by this connection`,
              "info"
            );
            logEvent(
              `💡 To test user-targeted events, make sure Target User ID matches your User ID`,
              "info"
            );
          }
        } catch (error) {
          logEvent(
            `❌ User-targeted event test failed: ${error.message}`,
            "error"
          );
        }
      }

      // API Testing functions
      async function testInitiateCallAPI() {
        try {
          logEvent("🔄 Testing API: Initiating call...", "info");

          const payload = {
            userId: userId.value,
            fromNumberId: phoneNumberId.value,
            toNumber: testPhoneNumber.value,
            userDisplayName: "Test User",
            maxParticipants: 10,
            enableRecording: false,
          };
          logEvent(
            `📡 API Request: Initiating call with payload: ${JSON.stringify(
              payload
            )}`,
            "info"
          );
          const response = await fetch(
            "https://localhost:44376/api/telephony/initiate-call",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
                subdomain: "localhost", // Adjust as needed for your API
              },
              body: payload ? JSON.stringify(payload) : null, // Ensure body is null if no payload
            }
          );

          const result = await response.json();

          if (response.ok) {
            logEvent(
              `✅ API Success: Call initiated - ${JSON.stringify(result)}`,
              "info"
            );
          } else {
            logEvent(
              `❌ API Error: ${response.status} - ${JSON.stringify(result)}`,
              "error"
            );
          }
        } catch (error) {
          logEvent(`❌ API Request failed: ${error.message}`, "error");
        }
      }

      async function testAnswerCallAPI() {
        try {
          logEvent("🔄 Testing API: Answering call...", "info");

          const response = await fetch(
            "https://localhost:44376/api/telephony/answer-call",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              body: JSON.stringify({
                userId: userId.value,
                callSid: "CA123456789", // You'll need to use actual call SID from previous call
                liveKitRoomName: "call-room-123",
                userDisplayName: "Test User",
              }),
            }
          );

          const result = await response.json();

          if (response.ok) {
            logEvent(
              `✅ API Success: Call answered - ${JSON.stringify(result)}`,
              "info"
            );
          } else {
            logEvent(
              `❌ API Error: ${response.status} - ${JSON.stringify(result)}`,
              "error"
            );
          }
        } catch (error) {
          logEvent(`❌ API Request failed: ${error.message}`, "error");
        }
      }

      async function testEndCallAPI() {
        try {
          logEvent("🔄 Testing API: Ending call...", "info");

          const response = await fetch(
            "https://localhost:44376/api/telephony/end-call",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
              },
              body: JSON.stringify({
                userId: userId.value,
                sessionId: "session-123",
                callSid: "CA123456789",
                reason: "Test call termination",
              }),
            }
          );

          const result = await response.json();

          if (response.ok) {
            logEvent(
              `✅ API Success: Call ended - ${JSON.stringify(result)}`,
              "info"
            );
          } else {
            logEvent(
              `❌ API Error: ${response.status} - ${JSON.stringify(result)}`,
              "error"
            );
          }
        } catch (error) {
          logEvent(`❌ API Request failed: ${error.message}`, "error");
        }
      }

      // Event listeners
      connectBtn.addEventListener("click", connect);
      disconnectBtn.addEventListener("click", disconnect);
      associateUserBtn.addEventListener("click", associateUserConnection);
      testUserConnectionBtn.addEventListener("click", testUserConnection);
      joinGroupBtn.addEventListener("click", joinCallGroup);
      leaveGroupBtn.addEventListener("click", leaveCallGroup);
      updateStatusBtn.addEventListener("click", updateCallStatus);

      // API test event listeners
      testInitiateCall.addEventListener("click", testInitiateCallAPI);
      testAnswerCall.addEventListener("click", testAnswerCallAPI);
      testEndCall.addEventListener("click", testEndCallAPI);
      testUserEvent.addEventListener("click", testUserTargetedEvent);

      clearLogBtn.addEventListener("click", () => {
        eventLog.innerHTML = "";
        eventsReceived = 0;
        eventsSent = 0;
        errors = 0;
        updateStats();
      });

      exportLogBtn.addEventListener("click", () => {
        const logContent = eventLog.innerText;
        const blob = new Blob([logContent], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `signalr-call-events-${new Date()
          .toISOString()
          .replace(/[:.]/g, "-")}.txt`;
        a.click();
        URL.revokeObjectURL(url);
      });

      // Simulation event listeners
      simulateIncomingCall.addEventListener("click", () => {
        // Simulate classic PSTN incoming call
        simulateEvent("IncomingCall", {
          callId: callId.value,
          fromNumber: "+1234567890",
          toNumber: "+0987654321",
          callSid: "CA123456789",
          timestamp: new Date().toISOString(),
        });

        // Simulate WebRTC incoming call event
        simulateEvent("IncomingWebRTCCall", {
          CallId: callId.value,
          CallSid: "CA987654321",
          From: "+1234567890",
          To: "+0987654321",
          AgentId: userId.value,
          CallType: "WebRTC",
          IsAssignedAgent: true,
          Timestamp: new Date().toISOString(),
        });
      });

      simulateCallSessionCreated.addEventListener("click", () => {
        simulateEvent("CallSessionCreated", {
          sessionId: "session-123",
          callId: callId.value,
          roomName: "call-room-123",
          participants: [{ userId: userId.value, displayName: "Test User" }],
          status: "initiating",
        });
      });

      simulateCallStatusUpdate.addEventListener("click", () => {
        simulateEvent("CallStatusUpdate", {
          callId: callId.value,
          status: callStatus.value,
          timestamp: new Date().toISOString(),
        });
      });

      simulateParticipantJoined.addEventListener("click", () => {
        simulateEvent("ParticipantJoined", {
          userId: userId.value,
          displayName: "Test User",
          joinedAt: new Date().toISOString(),
        });
      });

      simulateParticipantLeft.addEventListener("click", () => {
        simulateEvent("ParticipantLeft", {
          userId: userId.value,
          displayName: "Test User",
          leftAt: new Date().toISOString(),
        });
      });

      simulateCallEnded.addEventListener("click", () => {
        simulateEvent("CallEnded", {
          sessionId: "session-123",
          endedBy: userId.value,
          endedAt: new Date().toISOString(),
          reason: "Normal call termination",
        });
      });

      // Initial setup
      disconnectBtn.disabled = true;
      associateUserBtn.disabled = true;
      testUserConnectionBtn.disabled = true;
      joinGroupBtn.disabled = true;
      leaveGroupBtn.disabled = true;
      updateStatusBtn.disabled = true;
      updateStats();

      // Auto-connect on page load (optional)
      // connect();
    </script>
  </body>
</html>
