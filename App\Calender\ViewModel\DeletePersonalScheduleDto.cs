﻿﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Jobid.App.Calender.ViewModel
{
    public class DeletePersonalScheduleDto
    {
        // PersonalScheduleId is not required when DeleteExpiredSchedules is true
        public string PersonalScheduleId { get; set; }

        [Required(ErrorMessage = "User ID is required")]
        public string UserId { get; set; }

        // Flag to indicate whether to delete all expired schedules
        public bool DeleteExpiredSchedules { get; set; } = false;
    }
}
