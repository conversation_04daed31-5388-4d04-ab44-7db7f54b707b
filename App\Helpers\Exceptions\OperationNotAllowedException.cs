﻿using System;
using System.Runtime.Serialization;

namespace Jobid.App.Helpers.Exceptions
{
    [Serializable]
    public class OperationNotAllowedException : Exception
    {
        public OperationNotAllowedException()
        {
        }

        public OperationNotAllowedException(string message) : base(message)
        {
        }

        public OperationNotAllowedException(string message, Exception innerException) : base(message, innerException)
        {
        }

        protected OperationNotAllowedException(SerializationInfo info, StreamingContext context) : base(info, context)
        {
        }
    }
}
