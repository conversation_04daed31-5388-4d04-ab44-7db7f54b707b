﻿using Hangfire.Dashboard;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Filters
{
    public class HangFireAuthorizationFilter : IDashboardAsyncAuthorizationFilter
    {
        public async Task<bool> AuthorizeAsync(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();
            var user = httpContext.User;
            if (user.Identity.IsAuthenticated)
            {
                return await Task.FromResult(true);
            }

            return await Task.FromResult(false);

            return true;
        }
    }
}
