﻿using Jobid.App.Helpers.Enums;
using System.Collections.Generic;
using System;

namespace Jobid.App.JobProjectManagement.ViewModel
{
    public class TimeSheetDashboardFilters
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? BarChartStartDtae { get; set; }
        public DateTime? BarChartEndDate { get; set; }
        public List<ProjectStatus> ProjectStatus { get; set; } = null;
        public List<string> TeamIds { get; set; } = new List<string>();
        public List<string> TeamMemberIds { get; set; } = new List<string>();
        public List<string> ProjectIds { get; set; }
    }
}
