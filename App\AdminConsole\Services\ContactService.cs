using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Services.Contract;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Globalization;
using CsvHelper;
using OfficeOpenXml;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Enums;

namespace Jobid.App.AdminConsole.Services
{
    /// <summary>
    /// Service for Contact operations
    /// </summary>
    public class ContactService : IContactService
    {
        private readonly JobProDbContext _context;
        private readonly IAWSS3Sevices _awsS3Service;

        public ContactService(JobProDbContext context, IAWSS3Sevices awsS3Service)
        {
            _context = context;
            _awsS3Service = awsS3Service;
        }

        #region Create Contact
        /// <summary>
        /// Create a new contact
        /// </summary>
        public async Task<ApiResponse<UserContactDto>> CreateContactAsync(CreateUserContactDto contactDto)
        {
            var contact = new UserContact
            {
                Name = contactDto.Name,
                Email = contactDto.Email,
                PhoneNumber = contactDto.PhoneNumber,
                Industry = contactDto.Industry,
                UserId = contactDto.UserId,
                CreatedBy = contactDto.UserId,
                UpdatedBy = contactDto.UserId
            };

            _context.UserContacts.Add(contact);
            await _context.SaveChangesAsync();

            var contactDto_result = MapToContactDto(contact);

            return new ApiResponse<UserContactDto>
            {
                Data = contactDto_result,
                ResponseCode = "200",
                ResponseMessage = "Contact created successfully"
            };
        }
        #endregion

        #region Create Multiple Contacts
        /// <summary>
        /// Create multiple contacts
        /// </summary>
        public async Task<ApiResponse<List<UserContactDto>>> CreateContactsAsync(List<CreateUserContactDto> contacts)
        {
            var contactEntities = contacts.Select(dto => new UserContact
            {
                Name = dto.Name,
                Email = dto.Email,
                PhoneNumber = dto.PhoneNumber,
                Industry = dto.Industry,
                UserId = dto.UserId,
                CreatedBy = dto.UserId,
                UpdatedBy = dto.UserId
            }).ToList();

            _context.UserContacts.AddRange(contactEntities);
            await _context.SaveChangesAsync();

            var contactDtos = contactEntities.Select(MapToContactDto).ToList();

            return new ApiResponse<List<UserContactDto>>
            {
                Data = contactDtos,
                ResponseCode = "200",
                ResponseMessage = $"{contacts.Count} contacts created successfully"
            };
        }
        #endregion

        #region Get Contacts - User
        /// <summary>
        /// Get contacts for a specific user
        /// </summary>
        public async Task<ApiResponse<List<UserContactDto>>> GetUserContactsAsync(string userId)
        {
            var contacts = await _context.UserContacts
                .Where(c => c.UserId == userId && !c.IsDeleted)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();

            var contactDtos = contacts.Select(MapToContactDto).ToList();

            return new ApiResponse<List<UserContactDto>>
            {
                Data = contactDtos,
                ResponseCode = "200",
                ResponseMessage = $"{contacts.Count} contacts retrieved successfully"
            };
        }
        #endregion

        #region Check User Contacts Uploaded
        /// <summary>
        /// Check if user has uploaded contacts
        /// </summary>
        public async Task<ApiResponse<bool>> HasUserUploadedContactsAsync(string userId)
        {
            var hasContacts = await _context.UserContacts
                .AnyAsync(c => c.UserId == userId && !c.IsDeleted);

            return new ApiResponse<bool>
            {
                Data = hasContacts,
                ResponseCode = "200",
                ResponseMessage = hasContacts ? "User has contacts" : "User has no contacts"
            };
        }
        #endregion

        #region Update Contact
        /// <summary>
        /// Update an existing contact
        /// </summary>
        public async Task<ApiResponse<UserContactDto>> UpdateContactAsync(UpdateUserContactDto contactDto)
        {
            var contact = await _context.UserContacts
                .FirstOrDefaultAsync(c => c.Id == contactDto.Id && c.UserId == contactDto.UserId && !c.IsDeleted);

            if (contact == null)
            {
                return new ApiResponse<UserContactDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact not found"
                };
            }

            contact.Name = contactDto.Name;
            contact.Email = contactDto.Email;
            contact.PhoneNumber = contactDto.PhoneNumber;
            contact.Industry = contactDto.Industry;
            contact.UpdatedBy = contactDto.UserId;
            contact.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = MapToContactDto(contact);

            return new ApiResponse<UserContactDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Contact updated successfully"
            };
        }
        #endregion

        #region Delete Contact
        /// <summary>
        /// Delete a contact
        /// </summary>
        public async Task<ApiResponse<bool>> DeleteContactAsync(Guid contactId, string userId)
        {
            var contact = await _context.UserContacts
                .FirstOrDefaultAsync(c => c.Id == contactId && c.UserId == userId && !c.IsDeleted);

            if (contact == null)
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = "Contact not found"
                };
            }

            contact.IsDeleted = true;
            contact.UpdatedBy = userId;
            contact.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return new ApiResponse<bool>
            {
                Data = true,
                ResponseCode = "200",
                ResponseMessage = "Contact deleted successfully"
            };
        }
        #endregion

        #region Get All Contacts with Pagination
        /// <summary>
        /// Get all contacts with pagination
        /// </summary>
        public async Task<ApiResponse<PaginatedContactsDto>> GetAllContactsAsync(int pageNumber, int pageSize)
        {
            return await GetAllContactsAsync(pageNumber, pageSize, null);
        }

        /// <summary>
        /// Get all contacts with pagination and user filter
        /// </summary>
        public async Task<ApiResponse<PaginatedContactsDto>> GetAllContactsAsync(int pageNumber, int pageSize, List<string> userIds)
        {
            var query = _context.UserContacts.Where(c => !c.IsDeleted);

            // Apply user filter if provided
            if (userIds != null && userIds.Any())
            {
                query = query.Where(c => userIds.Contains(c.UserId));
            }

            var totalCount = await query.CountAsync();

            var contacts = await query
                .OrderByDescending(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var contactDtos = contacts.Select(MapToContactDto).ToList();
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var result = new PaginatedContactsDto
            {
                Contacts = contactDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = totalPages
            };

            return new ApiResponse<PaginatedContactsDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{contacts.Count} contacts retrieved successfully"
            };
        }
        #endregion

        #region Get Contact by ID
        /// <summary>
        /// Get contact by ID
        /// </summary>
        public async Task<ApiResponse<UserContactDto>> GetContactByIdAsync(Guid contactId)
        {
            var contact = await _context.UserContacts
                .FirstOrDefaultAsync(c => c.Id == contactId && !c.IsDeleted);

            if (contact == null)
            {
                return new ApiResponse<UserContactDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact not found"
                };
            }

            var contactDto = MapToContactDto(contact);

            return new ApiResponse<UserContactDto>
            {
                Data = contactDto,
                ResponseCode = "200",
                ResponseMessage = "Contact retrieved successfully"
            };
        }
        #endregion

        #region Upload Contacts
        /// <summary>
        /// Upload contacts from CSV or Excel file
        /// </summary>
        public async Task<ApiResponse<ContactUploadResponseDto>> UploadContactsAsync(IFormFile file, string userId)
        {
            var errors = new List<string>();
            var contacts = new List<CreateUserContactDto>();

            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

            if (fileExtension == ".csv")
            {
                contacts = await ParseCsvFile(file, errors);
            }
            else if (fileExtension == ".xlsx" || fileExtension == ".xls")
            {
                contacts = await ParseExcelFile(file, errors);
            }
            else
            {
                return new ApiResponse<ContactUploadResponseDto>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "Unsupported file format. Please upload CSV or Excel files only."
                };
            }

            // Set userId for all parsed contacts
            foreach (var contactDto in contacts)
            {
                contactDto.UserId = userId;
            }

            var successfulUploads = 0;
            var failedUploads = 0;

            foreach (var contactDto in contacts)
            {
                var contact = new UserContact
                {
                    Name = contactDto.Name,
                    Email = contactDto.Email,
                    PhoneNumber = contactDto.PhoneNumber,
                    Industry = contactDto.Industry,
                    UserId = contactDto.UserId,
                    CreatedBy = contactDto.UserId,
                    UpdatedBy = contactDto.UserId
                };

                _context.UserContacts.Add(contact);
                successfulUploads++;
            }

            if (successfulUploads > 0)
            {
                await _context.SaveChangesAsync();
            }

            var response = new ContactUploadResponseDto
            {
                TotalProcessed = contacts.Count,
                SuccessfulUploads = successfulUploads,
                FailedUploads = failedUploads,
                Errors = errors
            };

            return new ApiResponse<ContactUploadResponseDto>
            {
                Data = response,
                ResponseCode = "200",
                ResponseMessage = $"Upload completed. {successfulUploads} contacts uploaded successfully, {failedUploads} failed."
            };
        }
        #endregion

        // Group Implementation

        #region Contact Group Operations
        /// <summary>
        /// Create a new contact group
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> CreateContactGroupAsync(CreateContactGroupDto groupDto)
        {
            // Validate that all contact IDs exist and belong to the user
            var validContactIds = await _context.UserContacts
                .Where(c => groupDto.ContactIds.Contains(c.Id) && !c.IsDeleted)
                .Select(c => c.Id)
                .ToListAsync();

            if (validContactIds.Count != groupDto.ContactIds.Count)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "One or more contact IDs are invalid or do not belong to the user"
                };
            }

            var group = new Group
            {
                Name = groupDto.Name,
                SalePitch = groupDto.SalePitch,
                Language = groupDto.Language,
                PreferredVoiceAI = groupDto.PreferredVoiceAI,
                UserId = groupDto.UserId,
                CreatedBy = groupDto.UserId,
                UpdatedBy = groupDto.UserId
            };            // Handle optional file upload
            if (groupDto.AttachmentFile != null)
            {
                var uploadSuccess = await UploadFileToGroupAsync(group, groupDto.AttachmentFile, groupDto.UserId);
                if (!uploadSuccess)
                {
                    return new ApiResponse<ContactGroupDto>
                    {
                        Data = null,
                        ResponseCode = "500",
                        ResponseMessage = "Failed to upload attachment file"
                    };
                }
            }

            _context.Groups.Add(group);
            await _context.SaveChangesAsync();

            // Add group contacts
            var groupContacts = groupDto.ContactIds.Select(contactId => new GroupContact
            {
                GroupId = group.Id,
                ContactId = contactId,
                CreatedBy = groupDto.UserId
            }).ToList();

            _context.GroupContacts.AddRange(groupContacts);
            await _context.SaveChangesAsync();

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Contact group created successfully"
            };
        }

        /// <summary>
        /// Update an existing contact group
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> UpdateContactGroupAsync(UpdateContactGroupDto groupDto)
        {
            var group = await _context.Groups
                .FirstOrDefaultAsync(c => c.Id == groupDto.Id && c.UserId == groupDto.UserId && !c.IsDeleted);
            if (group == null)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found"
                };
            }

            // Validate that all contact IDs exist and belong to the user
            var validContactIds = await _context.UserContacts
                .Where(c => groupDto.ContactIds.Contains(c.Id) && c.UserId == groupDto.UserId && !c.IsDeleted)
                .Select(c => c.Id)
                .ToListAsync();

            if (validContactIds.Count != groupDto.ContactIds.Count)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "One or more contact IDs are invalid or do not belong to the user"
                };
            }

            group.Name = groupDto.Name;
            group.SalePitch = groupDto.SalePitch;
            group.Language = groupDto.Language;
            group.PreferredVoiceAI = groupDto.PreferredVoiceAI;
            group.Status = groupDto.Status;
            group.UpdatedBy = groupDto.UserId;
            group.UpdatedAt = DateTime.UtcNow;

            // Remove existing group contacts
            var existingGroupContacts = await _context.GroupContacts
                .Where(cc => cc.GroupId == group.Id)
                .ToListAsync();
            _context.GroupContacts.RemoveRange(existingGroupContacts);

            // Add new group contacts
            var newGroupContacts = groupDto.ContactIds.Select(contactId => new GroupContact
            {
                GroupId = group.Id,
                ContactId = contactId,
                CreatedBy = groupDto.UserId
            }).ToList();

            _context.GroupContacts.AddRange(newGroupContacts);
            await _context.SaveChangesAsync();

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Contact group updated successfully"
            };
        }

        /// <summary>
        /// Delete a contact group
        /// </summary>
        public async Task<ApiResponse<bool>> DeleteContactGroupAsync(Guid groupId, string userId)
        {
            var group = await _context.Groups
                .FirstOrDefaultAsync(c => c.Id == groupId && c.UserId == userId && !c.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found"
                };
            }

            group.IsDeleted = true;
            group.UpdatedBy = userId;
            group.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return new ApiResponse<bool>
            {
                Data = true,
                ResponseCode = "200",
                ResponseMessage = "Contact group deleted successfully"
            };
        }

        /// <summary>
        /// Get contact group by ID
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> GetContactGroupByIdAsync(Guid groupId)
        {
            var group = await _context.Groups
                .FirstOrDefaultAsync(c => c.Id == groupId && !c.IsDeleted);

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Contact group retrieved successfully"
            };
        }

        /// <summary>
        /// Get contact groups for a specific user with pagination
        /// </summary>
        public async Task<ApiResponse<PaginatedContactGroupsDto>> GetUserContactGroupsAsync(string userId, int pageNumber, int pageSize)
        {
            var totalCount = await _context.Groups
                .CountAsync(c => c.UserId == userId && !c.IsDeleted);

            var groups = await _context.Groups
                .Where(c => c.UserId == userId && !c.IsDeleted)
                .OrderByDescending(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var groupDtos = new List<ContactGroupDto>();
            foreach (var group in groups)
            {
                groupDtos.Add(await MapToContactGroupDto(group));
            }

            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var result = new PaginatedContactGroupsDto
            {
                Groups = groupDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = totalPages
            };

            return new ApiResponse<PaginatedContactGroupsDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{groups.Count} contact groups retrieved successfully"
            };
        }

        /// <summary>
        /// Get all contact groups with pagination
        /// </summary>
        public async Task<ApiResponse<PaginatedContactGroupsDto>> GetAllContactGroupsAsync(int pageNumber, int pageSize)
        {
            var totalCount = await _context.Groups
                .CountAsync(c => !c.IsDeleted);

            var groups = await _context.Groups
                .Where(c => !c.IsDeleted)
                .OrderByDescending(c => c.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var groupDtos = new List<ContactGroupDto>();
            foreach (var group in groups)
            {
                groupDtos.Add(await MapToContactGroupDto(group));
            }

            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var result = new PaginatedContactGroupsDto
            {
                Groups = groupDtos,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = totalPages
            };

            return new ApiResponse<PaginatedContactGroupsDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{groups.Count} contact groups retrieved successfully"
            };
        }

        /// <summary>
        /// Add contacts to a contact group
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> AddContactsToContactGroupAsync(UpdateContactGroupContactsDto updateDto)
        {
            var group = await _context.Groups
                .FirstOrDefaultAsync(c => c.Id == updateDto.GroupId && c.UserId == updateDto.UserId && !c.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found"
                };
            }

            // Validate that all contact IDs exist and belong to the user
            var validContactIds = await _context.UserContacts
                .Where(c => updateDto.ContactIds.Contains(c.Id) && c.UserId == updateDto.UserId && !c.IsDeleted)
                .Select(c => c.Id)
                .ToListAsync();

            if (validContactIds.Count != updateDto.ContactIds.Count)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "One or more contact IDs are invalid or do not belong to the user"
                };
            }

            // Get existing group contact IDs
            var existingContactIds = await _context.GroupContacts
                .Where(cc => cc.GroupId == updateDto.GroupId)
                .Select(cc => cc.ContactId)
                .ToListAsync();

            // Add new contact IDs that are not already in the group
            var newContactIds = updateDto.ContactIds.Except(existingContactIds).ToList();

            var newGroupContacts = newContactIds.Select(contactId => new GroupContact
            {
                GroupId = updateDto.GroupId,
                ContactId = contactId,
                CreatedBy = updateDto.UserId
            }).ToList();

            _context.GroupContacts.AddRange(newGroupContacts);

            group.UpdatedBy = updateDto.UserId;
            group.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{newContactIds.Count} contacts added to group successfully"
            };
        }

        /// <summary>
        /// Remove contacts from a contact group
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> RemoveContactsFromContactGroupAsync(UpdateContactGroupContactsDto updateDto)
        {
            var group = await _context.Groups
                .FirstOrDefaultAsync(c => c.Id == updateDto.GroupId && c.UserId == updateDto.UserId && !c.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found"
                };
            }

            // Remove group contacts
            var groupContactsToRemove = await _context.GroupContacts
                .Where(cc => cc.GroupId == updateDto.GroupId && updateDto.ContactIds.Contains(cc.ContactId))
                .ToListAsync();

            _context.GroupContacts.RemoveRange(groupContactsToRemove);

            group.UpdatedBy = updateDto.UserId;
            group.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = $"{groupContactsToRemove.Count} contacts removed from group successfully"
            };
        }

        /// <summary>
        /// Update contact group status
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> UpdateContactGroupStatusAsync(UpdateContactGroupStatusDto statusDto)
        {
            var group = await _context.Groups
                .FirstOrDefaultAsync(c => c.Id == statusDto.GroupId && c.UserId == statusDto.UserId && !c.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found"
                };
            }

            group.Status = statusDto.Status;
            group.UpdatedBy = statusDto.UserId;
            group.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Contact group status updated successfully"
            };
        }

        /// <summary>
        /// Get contact group statistics for a user
        /// </summary>
        public async Task<ApiResponse<ContactGroupStatsDto>> GetContactGroupStatsAsync(string userId)
        {
            var totalGroups = await _context.Groups
                .CountAsync(c => c.UserId == userId && !c.IsDeleted);

            var activeGroups = await _context.Groups
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == ContactGroupStatus.Active);

            var draftGroups = await _context.Groups
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == ContactGroupStatus.Draft);

            var completedGroups = await _context.Groups
                .CountAsync(c => c.UserId == userId && !c.IsDeleted && c.Status == ContactGroupStatus.Completed);

            var totalContacts = await _context.GroupContacts
                .Where(gc => _context.Groups.Any(g => g.Id == gc.GroupId && g.UserId == userId && !g.IsDeleted))
                .Select(gc => gc.ContactId)
                .Distinct()
                .CountAsync();

            var stats = new ContactGroupStatsDto
            {
                TotalGroups = totalGroups,
                ActiveGroups = activeGroups,
                DraftGroups = draftGroups,
                CompletedGroups = completedGroups,
                TotalContacts = totalContacts
            };

            return new ApiResponse<ContactGroupStatsDto>
            {
                Data = stats,
                ResponseCode = "200",
                ResponseMessage = "Contact group statistics retrieved successfully"            };
        }

        #region Private Helper Methods
        /// <summary>
        /// Map ContactGroup entity to ContactGroupDto
        /// </summary>
        private async Task<ContactGroupDto> MapToContactGroupDto(Group group)
        {
            var groupContacts = await _context.GroupContacts
                .Where(cc => cc.GroupId == group.Id)
                .Include(cc => cc.Contact)
                .ToListAsync();            var contactIds = groupContacts.Select(cc => cc.ContactId).ToList();
            var contacts = groupContacts.Select(cc => MapToContactDto(cc.Contact)).ToList();

            // Generate presigned URL for attachment if it exists
            string attachmentFileUrl = null;
            if (!string.IsNullOrEmpty(group.AttachmentAwsKey))
            {
                try
                {
                    attachmentFileUrl = await _awsS3Service.GetPresignedUrlForKeyAsync(group.AttachmentAwsKey, 60); // 1 hour expiry
                }
                catch (Exception)
                {
                    // If there's an error getting the presigned URL, we'll just leave it null
                    attachmentFileUrl = null;
                }
            }

            return new ContactGroupDto
            {
                Id = group.Id,
                Name = group.Name,
                SalePitch = group.SalePitch,
                Language = group.Language,
                PreferredVoiceAI = group.PreferredVoiceAI,
                ContactIds = contactIds,
                Contacts = contacts,
                Status = group.Status,
                UserId = group.UserId,
                CreatedAt = group.CreatedAt,
                UpdatedAt = group.UpdatedAt,
                CreatedBy = group.CreatedBy,
                UpdatedBy = group.UpdatedBy,
                AttachmentFileName = group.AttachmentFileName,
                AttachmentFileUrl = attachmentFileUrl,
                AttachmentFileSize = group.AttachmentFileSize,
                AttachmentFileType = group.AttachmentFileType
            };
        }

        /// <summary>
        /// Map UserContact entity to UserContactDto
        /// </summary>
        private UserContactDto MapToContactDto(UserContact contact)
        {
            return new UserContactDto
            {
                Id = contact.Id,
                Name = contact.Name,
                Email = contact.Email,
                PhoneNumber = contact.PhoneNumber,
                Industry = contact.Industry,
                UserId = contact.UserId,
                CreatedAt = contact.CreatedAt,
                UpdatedAt = contact.UpdatedAt,
                CreatedBy = contact.CreatedBy,
                UpdatedBy = contact.UpdatedBy
            };
        }

        /// <summary>
        /// Parse CSV file and extract contacts
        /// </summary>
        private async Task<List<CreateUserContactDto>> ParseCsvFile(IFormFile file, List<string> errors)
        {
            var contacts = new List<CreateUserContactDto>();

            using var reader = new StreamReader(file.OpenReadStream());
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);

            try
            {
                var records = csv.GetRecords<dynamic>().ToList();

                foreach (var record in records)
                {
                    var recordDict = (IDictionary<string, object>)record;

                    if (recordDict.TryGetValue("Name", out var name) &&
                        recordDict.TryGetValue("Email", out var email))
                    {
                        contacts.Add(new CreateUserContactDto
                        {
                            Name = name?.ToString(),
                            Email = email?.ToString(),
                            PhoneNumber = recordDict.TryGetValue("PhoneNumber", out var phone) ? phone?.ToString() : null,
                            Industry = recordDict.TryGetValue("Industry", out var industry) ? industry?.ToString() : null
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Error parsing CSV file: {ex.Message}");
            }

            return contacts;
        }

        /// <summary>
        /// Parse Excel file and extract contacts
        /// </summary>
        private async Task<List<CreateUserContactDto>> ParseExcelFile(IFormFile file, List<string> errors)
        {
            var contacts = new List<CreateUserContactDto>();

            try
            {
                using var stream = file.OpenReadStream();
                using var package = new ExcelPackage(stream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet != null)
                {
                    for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                    {
                        var name = worksheet.Cells[row, 1].Value?.ToString();
                        var email = worksheet.Cells[row, 2].Value?.ToString();
                        var phoneNumber = worksheet.Cells[row, 3].Value?.ToString();
                        var industry = worksheet.Cells[row, 4].Value?.ToString();

                        if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(email))
                        {
                            contacts.Add(new CreateUserContactDto
                            {
                                Name = name,
                                Email = email,
                                PhoneNumber = phoneNumber,
                                Industry = industry
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errors.Add($"Error parsing Excel file: {ex.Message}");
            }

            return contacts;
        }

        #endregion

        /// <summary>
        /// Upload a file to an existing contact group
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> UploadContactGroupFileAsync(UploadContactGroupFileDto uploadDto)
        {
            // Find the group and verify ownership
            var group = await _context.Groups
                .FirstOrDefaultAsync(g => g.Id == uploadDto.GroupId && g.UserId == uploadDto.UserId && !g.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found or access denied"
                };
            }

            var uploadSuccess = await UploadFileToGroupAsync(group, uploadDto.AttachmentFile, uploadDto.UserId);
            if (!uploadSuccess)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = "Failed to upload file"
                };
            }

            await _context.SaveChangesAsync();
            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "File uploaded successfully"
            };
        }

        /// <summary>
        /// Delete a file from an existing contact group
        /// </summary>
        public async Task<ApiResponse<ContactGroupDto>> DeleteContactGroupFileAsync(DeleteContactGroupFileDto deleteDto)
        {
            // Find the group and verify ownership
            var group = await _context.Groups
                .FirstOrDefaultAsync(g => g.Id == deleteDto.GroupId && g.UserId == deleteDto.UserId && !g.IsDeleted);

            if (group == null)
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = "Contact group not found or access denied"
                };
            }

            // Check if group has a file to delete
            if (string.IsNullOrEmpty(group.AttachmentAwsKey))
            {
                return new ApiResponse<ContactGroupDto>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = "No file found to delete"
                };
            }            // Delete file from S3
            await _awsS3Service.DeleteFileAsync(group.AttachmentAwsKey);

            // Clear file information from the group
            group.AttachmentFileName = null;
            group.AttachmentAwsKey = null;
            group.AttachmentFileSize = null;
            group.AttachmentFileType = null;
            group.UpdatedBy = deleteDto.UserId;
            group.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            var result = await MapToContactGroupDto(group);

            return new ApiResponse<ContactGroupDto>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "File deleted successfully"
            };
        }

        /// <summary>
        /// Upload file to AWS S3 and update group with file information
        /// </summary>
        /// <param name="group">The group to update</param>
        /// <param name="file">The file to upload</param>
        /// <param name="userId">User ID for audit trail</param>
        /// <returns>True if successful, false otherwise</returns>
        private async Task<bool> UploadFileToGroupAsync(Group group, IFormFile file, string userId)
        {
            // If there's an existing file, delete it from S3 first
            if (!string.IsNullOrEmpty(group.AttachmentAwsKey))
            {
                await _awsS3Service.DeleteFileAsync(group.AttachmentAwsKey);
            }

            // Generate a unique filename for AWS S3
            var fileExtension = Path.GetExtension(file.FileName);
            var uniqueFileName = $"contact-groups/{Guid.NewGuid()}{fileExtension}";
            
            // Upload new file to AWS S3
            var uploadResult = await _awsS3Service.UploadFileAsync(file, uniqueFileName);
            
            if (!string.IsNullOrEmpty(uploadResult))
            {
                // Update file information in the group
                group.AttachmentFileName = file.FileName;
                group.AttachmentAwsKey = uniqueFileName;
                group.AttachmentFileSize = file.Length;
                group.AttachmentFileType = file.ContentType;
                group.UpdatedBy = userId;
                group.UpdatedAt = DateTime.UtcNow;
                
                return true;
            }
              return false;
        }
        #endregion
    }
}
