﻿using Jobid.App.Helpers.Enums;
using System;

namespace Jobid.App.Helpers.ViewModel
{
    public class UserDetails
    {
        public string JobProId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string MiddleName { get; set; }
        public string UserName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string PhoneCountryCode { get; set; }
        public DateTime Created_At { get; set; }
        public string ProfileImageUrl { get; set; }
        public string ProofOfResidence { get; set; }
        public string GovernmentId { get; set; }
        public string Gender { get; set; }
        public string Status { get; set; }
        public string DateOfBirth { get; set; }
        public string StatusComment { get; set; }
        public string InvitedBy { get; set; }
        public DateTime Modified_At { get; set; }
        public string CompanyId { get; set; }
        public string Region { get; set; }
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public string CountryCode { get; set; }
        public string Country { get; set; }
        public string TimeZone { get; set; }
        public string State { get; set; }
        public string JobPaysPin { get; set; }
        public bool IsMobileLoginPinCreated { get; set; }
        public string? WeavrPasscode { get; set; }
        public string? WeavrId { get; set; }
        public bool WeavrPasscodeForPublic { get; set; }
        public string BaseCurrency { get; set; }
        public UserTypes UserType { get; set; } = UserTypes.IndividualUser;
        public string UpdatedBy { get; set; }
        public Guid? ClientRoleId { get; set; }
        public string Profession { get; set; }
    }
}
