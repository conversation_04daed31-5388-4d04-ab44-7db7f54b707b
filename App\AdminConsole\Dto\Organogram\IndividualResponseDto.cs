﻿using System;

namespace Jobid.App.AdminConsole.Dto.Organogram
{
    public class IndividualResponseDto
    {
        public Guid Id { get; set; }
        public Guid DepartmentId { get; set; }
        public Guid CompanyId { get; set; }
        public Guid PositionId { get; set; }
        public long Index { get; set; }
        public string EmailAddress { get; set; }
        public long BelongsTo { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? UpdatedOn { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public string Name { get; set; }
        public string Title { get; set; }
        public string BranchColor { get; set; }
        public bool IsHeadOfDepartment { get; set; }
    }
}
