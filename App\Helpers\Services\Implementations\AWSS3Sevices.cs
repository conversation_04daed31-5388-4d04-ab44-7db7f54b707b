using Amazon;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Model;
using Jobid.App.Helpers.Configurations;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Services.Contract;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class AWSS3Sevices : IAWSS3Sevices
    {
        private readonly AmazonS3Client _s3Client;
        private readonly AWSConfigOptions _options;

        public AWSS3Sevices(IConfiguration config)
        {
            _options = config.GetSection("AWSConfigOptions").Get<AWSConfigOptions>();
            _options.BucketName = Environment.GetEnvironmentVariable("AWS_BUCKET_NAME") ?? _options.BucketName;
            _options.AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY") ?? _options.AccessKey;
            _options.SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_KEY") ?? _options.SecretKey;
            _options.Region = Environment.GetEnvironmentVariable("AWS_REGION") ?? _options.Region;

            var awsCredentials = new BasicAWSCredentials(_options.AccessKey, _options.SecretKey);
            _s3Client = new AmazonS3Client(awsCredentials, RegionEndpoint.GetBySystemName(_options.Region));
        }

        #region Upload file
        /// <summary>
        /// This method is used to upload a file to the AWS S3 Bucket. It takes the file and the file name as input.
        /// </summary>
        /// <param name="fileToUpload"></param>
        /// <param name="fileNameToSave"></param>
        /// <returns></returns>
        public async Task<string> UploadFileAsync(IFormFile fileToUpload, [Required] string fileNameToSave)
        {
            // Check if fileToUpload is null
            if (fileToUpload == null)
            {
                throw new FileUploadException($"The file {nameof(fileToUpload)} to upload cannot be null.");
            }

            // Check if file size is greater than 0
            if (fileToUpload.Length <= 0)
            {
                throw new FileUploadException($"The file size must be greater than 0.");
            }

            // Note: We no longer check for 5MB limit here as we have UploadLargeFileAsync for large files

            try
            {
                using (var memoryStream = new MemoryStream())
                {
                    await fileToUpload.CopyToAsync(memoryStream);
                    memoryStream.Position = 0; // Reset the stream position to the beginning

                    var putRequest = new PutObjectRequest
                    {
                        BucketName = _options.BucketName,
                        Key = fileNameToSave,
                        InputStream = memoryStream,
                        ContentType = fileToUpload.ContentType
                    };

                    var response = await _s3Client.PutObjectAsync(putRequest);

                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                    {
                        // Return the S3 file URL
                        return $"https://{_options.BucketName}.s3.{_options.Region}.amazonaws.com/{fileNameToSave}";
                    }

                    throw new Exception("Failed to upload file to S3.");
                }
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Delete file
        /// <summary>
        /// This method is used to delete a file from the AWS S3 Bucket. It takes the file name as input.
        /// </summary>
        /// <param name="fileNameToDelete"></param>
        /// <returns></returns>
        public async Task<bool> DeleteFileAsync(string fileNameToDelete)
        {
            try
            {
                var deleteRequest = new DeleteObjectRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToDelete
                };

                var response = _s3Client.DeleteObjectAsync(deleteRequest).Result;

                if (response.HttpStatusCode == System.Net.HttpStatusCode.NoContent)
                {
                    return await Task.FromResult(true);
                }

                return await Task.FromResult(false);
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Download file and convert to base64
        /// <summary>
        /// This method is used to download a file from the AWS S3 Bucket and convert it to a base64 string. It takes the file name as input.
        /// </summary>
        /// <param name="fileNameToDownload"></param>
        /// <returns></returns>
        public Task<string> DownloadFileAndConvertToBase64(string fileNameToDownload)
        {
            try
            {
                var getRequest = new GetObjectRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToDownload
                };

                using (var response = _s3Client.GetObjectAsync(getRequest).Result)
                {
                    using (var responseStream = response.ResponseStream)
                    {
                        using (var memoryStream = new MemoryStream())
                        {
                            responseStream.CopyTo(memoryStream);
                            memoryStream.Position = 0; // Reset the stream position to the beginning

                            return Task.FromResult(Convert.ToBase64String(memoryStream.ToArray()));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new OperationFailedException(ex.Message);
            }
        }
        #endregion

        #region Get signed URL
        /// <summary>
        /// This method is used to get a signed URL for a file in the AWS S3 Bucket. It takes the file name as input.
        /// </summary>
        /// <param name="fileNameToRead"></param>
        /// <param name="timeOutInMinutes"></param>
        /// <returns></returns>
        public Task<string> GetSignedUrlAsync(string fileNameToRead, int timeOutInMinutes = 30)
        {
            try
            {
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToRead,
                    Expires = DateTime.UtcNow.AddMinutes(timeOutInMinutes)
                };

                return Task.FromResult(_s3Client.GetPreSignedURL(request));
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Upload file with size limit
        /// <summary>
        /// This method is used to upload a file to the AWS S3 Bucket with a file size limit.
        /// It takes the file, the file name, the file content type, and the file size limit as input.
        /// </summary>
        /// <param name="fileToUpload"></param>
        /// <param name="fileNameToSave"></param>
        /// <param name="fileSizeLimit"></param>
        /// <returns></returns>
        public Task<string> UploadFileWithLimitAsync(IFormFile fileToUpload, string fileNameToSave, int fileSizeLimit = 5)
        {
            try
            {
                if (fileToUpload.Length > fileSizeLimit * 1024 * 1024)
                {
                    throw new FileUploadException($"File size limit exceeded. File size should be less than {fileSizeLimit}MB.");
                }

                return UploadFileAsync(fileToUpload, fileNameToSave);
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Upload large file using multipart upload
        /// <summary>
        /// This method is used to upload a large file to the AWS S3 Bucket using multipart upload.
        /// It automatically uses multipart upload if the file size exceeds the specified threshold (default 50MB).
        /// </summary>
        /// <param name="fileToUpload">The file to upload</param>
        /// <param name="fileNameToSave">The name to save the file as in S3</param>
        /// <param name="fileSizeThresholdInMB">The threshold in MB above which multipart upload will be used (default 50MB)</param>
        /// <returns>The URL of the uploaded file</returns>
        public async Task<string> UploadLargeFileAsync(IFormFile fileToUpload, string fileNameToSave, int fileSizeThresholdInMB = 50)
        {
            // Check if fileToUpload is null
            if (fileToUpload == null)
            {
                throw new FileUploadException($"The file {nameof(fileToUpload)} to upload cannot be null.");
            }

            // Check if file size is greater than 0
            if (fileToUpload.Length <= 0)
            {
                throw new FileUploadException($"The file size must be greater than 0.");
            }

            // If file size is less than the threshold, use the regular upload method
            if (fileToUpload.Length <= fileSizeThresholdInMB * 1024 * 1024)
            {
                return await UploadFileAsync(fileToUpload, fileNameToSave);
            }

            // For large files, use multipart upload
            string uploadId = null;
            try
            {
                // 1. Initiate multipart upload
                var initiateRequest = new InitiateMultipartUploadRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToSave,
                    ContentType = fileToUpload.ContentType
                };

                var initiateResponse = await _s3Client.InitiateMultipartUploadAsync(initiateRequest);
                uploadId = initiateResponse.UploadId;

                // Define part size (5MB is the minimum for multipart uploads except the last part)
                const int partSize = 5 * 1024 * 1024; // 5MB
                var partETags = new List<PartETag>();
                long filePosition = 0;
                int partNumber = 1;

                using (var fileStream = fileToUpload.OpenReadStream())
                {
                    while (filePosition < fileToUpload.Length)
                    {
                        // Calculate part size for the current part
                        var currentPartSize = Math.Min(partSize, fileToUpload.Length - filePosition);
                        byte[] buffer = new byte[currentPartSize];

                        // Read data for the current part
                        await fileStream.ReadAsync(buffer, 0, (int)currentPartSize);

                        using (var partStream = new MemoryStream(buffer))
                        {
                            // Upload the part
                            var uploadPartRequest = new UploadPartRequest
                            {
                                BucketName = _options.BucketName,
                                Key = fileNameToSave,
                                UploadId = uploadId,
                                PartNumber = partNumber,
                                PartSize = currentPartSize,
                                InputStream = partStream
                            };

                            var uploadPartResponse = await _s3Client.UploadPartAsync(uploadPartRequest);
                            partETags.Add(new PartETag(partNumber, uploadPartResponse.ETag));
                        }

                        // Move to the next part
                        filePosition += currentPartSize;
                        partNumber++;
                    }
                }

                // 3. Complete multipart upload
                var completeRequest = new CompleteMultipartUploadRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToSave,
                    UploadId = uploadId,
                    PartETags = partETags
                };

                var completeResponse = await _s3Client.CompleteMultipartUploadAsync(completeRequest);

                if (completeResponse.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    // Return the S3 file URL
                    return $"https://{_options.BucketName}.s3.{_options.Region}.amazonaws.com/{fileNameToSave}";
                }

                throw new Exception("Failed to complete multipart upload to S3.");
            }
            catch (Exception ex)
            {
                // If an error occurs, we should abort the multipart upload to clean up any partial uploads
                if (!string.IsNullOrEmpty(uploadId))
                {
                    var abortRequest = new AbortMultipartUploadRequest
                    {
                        BucketName = _options.BucketName,
                        Key = fileNameToSave,
                        UploadId = uploadId
                    };

                    await _s3Client.AbortMultipartUploadAsync(abortRequest);
                }

                throw new FileUploadException($"Failed to upload file using multipart upload: {ex.Message}", ex);
            }
        }
        #endregion

        #region Wiki File Upload Methods
        /// <summary>
        /// Uploads a file from a stream to S3
        /// </summary>
        /// <param name="fileStream">The file stream</param>
        /// <param name="fileNameToSave">The name to save the file as in S3</param>
        /// <param name="contentType">The content type of the file</param>
        /// <returns>The URL of the uploaded file</returns>
        public async Task<string> UploadFileFromStreamAsync(Stream fileStream, string fileNameToSave, string contentType)
        {
            try
            {
                var putRequest = new PutObjectRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToSave,
                    InputStream = fileStream,
                    ContentType = contentType
                };

                var response = await _s3Client.PutObjectAsync(putRequest);

                if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    // Return the S3 file URL
                    return $"https://{_options.BucketName}.s3.{_options.Region}.amazonaws.com/{fileNameToSave}";
                }

                throw new Exception("Failed to upload file to S3.");
            }
            catch (Exception ex)
            {
                throw new FileUploadException($"Failed to upload file from stream: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Initiates a multipart upload to S3
        /// </summary>
        /// <param name="fileNameToSave">The name to save the file as in S3</param>
        /// <param name="contentType">The content type of the file</param>
        /// <returns>A tuple containing the upload ID and key</returns>
        public async Task<(string uploadId, string key)> InitiateMultipartUploadAsync(string fileNameToSave, string contentType)
        {
            try
            {
                var initiateRequest = new InitiateMultipartUploadRequest
                {
                    BucketName = _options.BucketName,
                    Key = fileNameToSave,
                    ContentType = contentType
                };

                var initiateResponse = await _s3Client.InitiateMultipartUploadAsync(initiateRequest);
                return (initiateResponse.UploadId, fileNameToSave);
            }
            catch (Exception ex)
            {
                throw new FileUploadException($"Failed to initiate multipart upload: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Uploads a part of a multipart upload to S3
        /// </summary>
        /// <param name="key">The key of the file</param>
        /// <param name="uploadId">The upload ID</param>
        /// <param name="partNumber">The part number</param>
        /// <param name="partStream">The stream containing the part data</param>
        /// <param name="partSize">The size of the part</param>
        /// <returns>The ETag of the uploaded part</returns>
        public async Task<string> UploadPartAsync(string key, string uploadId, int partNumber, Stream partStream, long partSize)
        {
            try
            {
                var uploadPartRequest = new UploadPartRequest
                {
                    BucketName = _options.BucketName,
                    Key = key,
                    UploadId = uploadId,
                    PartNumber = partNumber,
                    PartSize = partSize,
                    InputStream = partStream
                };

                var uploadPartResponse = await _s3Client.UploadPartAsync(uploadPartRequest);
                return uploadPartResponse.ETag;
            }
            catch (Exception ex)
            {
                throw new FileUploadException($"Failed to upload part {partNumber}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Completes a multipart upload to S3
        /// </summary>
        /// <param name="key">The key of the file</param>
        /// <param name="uploadId">The upload ID</param>
        /// <param name="partETags">A dictionary mapping part numbers to ETags</param>
        /// <returns>True if the upload was completed successfully</returns>
        public async Task<bool> CompleteMultipartUploadAsync(string key, string uploadId, Dictionary<int, string> partETags)
        {
            try
            {
                var partETagList = new List<PartETag>();
                foreach (var kvp in partETags)
                {
                    partETagList.Add(new PartETag(kvp.Key, kvp.Value));
                }

                var completeRequest = new CompleteMultipartUploadRequest
                {
                    BucketName = _options.BucketName,
                    Key = key,
                    UploadId = uploadId,
                    PartETags = partETagList
                };

                var completeResponse = await _s3Client.CompleteMultipartUploadAsync(completeRequest);
                return completeResponse.HttpStatusCode == System.Net.HttpStatusCode.OK;
            }
            catch (Exception ex)
            {
                throw new FileUploadException($"Failed to complete multipart upload: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Aborts a multipart upload to S3
        /// </summary>
        /// <param name="key">The key of the file</param>
        /// <param name="uploadId">The upload ID</param>
        /// <returns>True if the upload was aborted successfully</returns>
        public async Task<bool> AbortMultipartUploadAsync(string key, string uploadId)
        {
            try
            {
                var abortRequest = new AbortMultipartUploadRequest
                {
                    BucketName = _options.BucketName,
                    Key = key,
                    UploadId = uploadId
                };

                var abortResponse = await _s3Client.AbortMultipartUploadAsync(abortRequest);
                return abortResponse.HttpStatusCode == System.Net.HttpStatusCode.NoContent;
            }
            catch (Exception ex)
            {
                throw new FileUploadException($"Failed to abort multipart upload: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets a presigned URL for a file in S3
        /// </summary>
        /// <param name="key">The key of the file</param>
        /// <param name="expiryTimeInMinutes">The expiry time of the URL in minutes</param>
        /// <returns>The presigned URL</returns>
        public Task<string> GetPresignedUrlForKeyAsync(string key, int expiryTimeInMinutes = 60)
        {
            return GetSignedUrlAsync(key, expiryTimeInMinutes);
        }
        #endregion
    }
}
