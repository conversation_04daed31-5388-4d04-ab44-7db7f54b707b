﻿using Newtonsoft.Json;
using System;

namespace Jobid.App.Calender.Models.AI.Responses
{
    public class RescheduledDatetime
    {
        [JsonProperty("rescheduled_datetime")]
        public DateTime RescheduledDateAndtime { get; set; }
    }

    public class RescheduleMeetingResponse
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("error")]
        public object Error { get; set; }

        [JsonProperty("rescheduled_datetime")]
        public RescheduledDatetime RescheduledDatetime { get; set; }
    }
}
