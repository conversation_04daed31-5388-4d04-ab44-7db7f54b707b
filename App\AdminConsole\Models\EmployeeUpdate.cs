﻿using Jobid.App.Helpers.Enums;
using System.ComponentModel.DataAnnotations;
using Twilio.TwiML.Voice;

namespace Jobid.App.AdminConsole.Models
{
    public class EmployeeUpdate
    {
        [Required]
        public string UserId { get; set; }
        [RegularExpression(@"^[0-9]*$", ErrorMessage = "PhoneNumber accepts only alphabets")]
        public string PhoneNumber { get; set; }
        [Required, RegularExpression(@"^[A-Za-z]+$", ErrorMessage = "FirstName accepts only alphabets")]
        public string FirstName { get; set; }
        [Required, RegularExpression(@"^[A-Za-z]+$", ErrorMessage = "LastName accepts only alphabets")]
        public string LastName { get; set; }
        [Required]
        public string RoleId { get; set; }
        [Required]
        public Applications Package { get; set; }
    }
}
