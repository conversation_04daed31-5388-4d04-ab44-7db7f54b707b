using Jobid.App.AdminConsole.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models
{    
    /// <summary>
    /// Contact Group model for marketing groups
    /// </summary>
    public class Group
    {        
        public Group()
        {
            Id = Guid.NewGuid();
            Name = string.Empty;
            SalePitch = string.Empty;
            Language = "English"; // Default language
            PreferredVoiceAI = PreferredVoiceAI.Jude; // Default voice AI
            GroupContacts = new List<GroupContact>();
            CreatedBy = string.Empty;
            UpdatedBy = string.Empty;
        }

        [Key]
        public Guid Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }        
          [Required]
        [StringLength(1000)]
        public string SalePitch { get; set; }

        /// <summary>
        /// Language for the group communications
        /// </summary>
        [StringLength(100)]
        public string Language { get; set; }

        /// <summary>
        /// Preferred voice AI for the group
        /// </summary>
        public PreferredVoiceAI PreferredVoiceAI { get; set; }

        /// <summary>
        /// Optional attachment file name
        /// </summary>
        [StringLength(500)]
        public string AttachmentFileName { get; set; }

        /// <summary>
        /// AWS S3 key for the attachment file
        /// </summary>
        [StringLength(500)]
        public string AttachmentAwsKey { get; set; }

        /// <summary>
        /// Size of the attachment file in bytes
        /// </summary>
        public long? AttachmentFileSize { get; set; }

        /// <summary>
        /// MIME type of the attachment file
        /// </summary>
        [StringLength(100)]
        public string AttachmentFileType { get; set; }

        /// <summary>
        /// Navigation property to group contacts
        /// </summary>
        public virtual ICollection<GroupContact> GroupContacts { get; set; }

        /// <summary>
        /// Status of the group
        /// </summary>
        public ContactGroupStatus Status { get; set; } = ContactGroupStatus.Draft;

        /// <summary>
        /// User who owns this group
        /// </summary>
        [Required]
        public string UserId { get; set; }

        /// <summary>
        /// Soft delete flag
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        #region Audit Properties

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When this record was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created this record
        /// </summary>
        [StringLength(450)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// User who last updated this record
        /// </summary>
        [StringLength(450)]
        public string UpdatedBy { get; set; }

        #endregion
    }
}
