using Jobid.App.JobProjectManagement.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.Wiki.Models
{
    public class WikiFileDepartmentAccess
    {
        [Key]
        public Guid Id { get; set; }
        
        [Required]
        public Guid WikiFileId { get; set; }
        
        [Required]
        public Guid DepartmentId { get; set; }
        
        [Required]
        public DateTime CreatedAt { get; set; }
        
        public DateTime? RemovedAt { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // Navigation properties
        [ForeignKey("WikiFileId")]
        public virtual WikiContent WikiFile { get; set; }

        [ForeignKey("DepartmentId")]
        public virtual Team Team { get; set; }
    }
}
