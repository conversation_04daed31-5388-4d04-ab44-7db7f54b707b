using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Jobid.App.AdminConsole.Models.Wallet;
using Jobid.App.AdminConsole.Enums;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers;
using System.Linq;

namespace Jobid.App.AdminConsole.Services
{    public class WalletService : IWalletService
    {
        private readonly JobProDbContext _context;
        private readonly IPaymentService _paymentService;

        public WalletService(JobProDbContext context, IPaymentService paymentService)
        {
            _context = context;
            _paymentService = paymentService;
        }

        public async Task<CompanyWallet> GetWalletAsync()
        {
            var wallet = await _context.CompanyWallets
                .FirstOrDefaultAsync();

            if (wallet == null)
            {
                wallet = new CompanyWallet
                {
                    Id = Guid.NewGuid(),
                    Balance = 0,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.CompanyWallets.Add(wallet);
                await _context.SaveChangesAsync();
            }

            return wallet;
        }        

        public async Task<WalletTransaction> DeductFundsAsync(decimal amount, string description)
        {
            var wallet = await GetWalletAsync();

            if (wallet.Balance < amount)
                throw new Exception("Insufficient funds");

            var transaction = new WalletTransaction
            {
                Id = Guid.NewGuid(),
                WalletId = wallet.Id,
                Amount = amount,
                Type = TransactionType.Debit,
                PaymentMethod = PaymentMethod.None, // Default value
                TransactionReference = Guid.NewGuid().ToString(),
                Description = description,
                Status = TransactionStatus.Completed,
                CreatedAt = DateTime.UtcNow
            }; wallet.Balance -= amount;
            wallet.UpdatedAt = DateTime.UtcNow;

            _context.WalletTransactions.Add(transaction);
            await _context.SaveChangesAsync();

            return transaction;
        }

        public async Task<bool> ProcessCallChargesAsync(Guid phoneNumberId, decimal duration)
        {
            var phoneNumber = await _context.PhoneNumbers
                .FirstOrDefaultAsync(p => p.Id == phoneNumberId);

            if (phoneNumber == null)
                return false;

            const decimal COST_PER_MINUTE = 0.015m;
            var cost = Math.Ceiling(duration) * COST_PER_MINUTE;

            try
            {
                await DeductFundsAsync(cost, $"Call charges for {duration} minutes");
                return true;
            }
            catch
            {
                return false;
            }
        }

        // Interface implementations
        public async Task<GenericResponse> GetWalletBalance()
        {
            var wallet = await GetWalletAsync();
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Wallet balance retrieved successfully",
                DevResponseMessage = "Success",
                Data = new { Balance = wallet.Balance, WalletId = wallet.Id }
            };
        }

        public async Task<GenericResponse> CreateWallet()
        {
            var wallet = await GetWalletAsync();
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Wallet created/retrieved successfully",
                DevResponseMessage = "Success",
                Data = new { WalletId = wallet.Id, Balance = wallet.Balance }
            };
        }

        public async Task<GenericResponse> GetWallet()
        {
            return await GetWalletBalance();
        }       
        
        public async Task<GenericResponse> FundWallet(FundWalletDto model)
        {
            // Validate input
            if (model.Amount <= 0)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Amount must be greater than zero",
                    Data = null
                };
            }            
            
            var paymentResponse = await _paymentService.InitiateWalletFundingAsync(
                 model.Amount,
                 model.Currency,
                 model.Provider,
                 model.Subdomain
             );

            return paymentResponse;
        }

        public async Task<GenericResponse> GetTransactions(DateTime? startDate = null, DateTime? endDate = null)
        {
            var wallet = await GetWalletAsync();
            var query = _context.WalletTransactions.Where(t => t.WalletId == wallet.Id);

            if (startDate.HasValue)
                query = query.Where(t => t.CreatedAt >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(t => t.CreatedAt <= endDate.Value);

            var transactions = await query.OrderByDescending(t => t.CreatedAt).ToListAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Transactions retrieved successfully",
                DevResponseMessage = "Success",
                Data = transactions
            };
        }

        public async Task<GenericResponse> GetTransaction(Guid transactionId)
        {
            var transaction = await _context.WalletTransactions.FirstOrDefaultAsync(t => t.Id == transactionId);
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Transaction retrieved successfully",
                DevResponseMessage = "Success",
                Data = transaction
            };
        }

        public async Task<GenericResponse> GetPhoneNumberBalance(Guid phoneNumberId)
        {
            var phoneNumber = await _context.PhoneNumbers.FirstOrDefaultAsync(p => p.Id == phoneNumberId);
            if (phoneNumber == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "404",
                    ResponseMessage = "Phone number not found",
                    DevResponseMessage = "No phone number found with the specified ID",
                    Data = null
                };
            }

            // For now, returning a placeholder response
            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Phone number balance retrieved",
                DevResponseMessage = "Success",
                Data = new { PhoneNumberId = phoneNumberId, Balance = 0.00m }
            };
        }
    }
}
