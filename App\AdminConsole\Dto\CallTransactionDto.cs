﻿using Jobid.App.AdminConsole.Enums;
using System.ComponentModel.DataAnnotations;
using System;

namespace Jobid.App.AdminConsole.Dto
{
    public class CallTransactionDto
    {
        public string CompanyId { get; set; }
        public Actions Action { get; set; } = Actions.NoAction;

        [Required]
        public string FromNumber { get; set; }

        [Required]
        public string ToNumber { get; set; }

        [Required]
        public string Transcription { get; set; }
        public string CustomerName { get; set; }
        public CallDirection CallDirection { get; set; } = CallDirection.Inbound;

        // In seconds
        [Required]
        public double CallDuration { get; set; }

        // Meeting details
        public string CustomerEmail { get; set; }
        public string EmployeeName { get; set; }
        public DateTime? MeetingDate { get; set; }
        public TimeSpan? MeetingTime { get; set; }
        public double? DurationInMinutes { get; set; }

        // Action Status
        public ActionStatus ActionStatus { get; set; } = ActionStatus.NoActionTaken;
    }
}
