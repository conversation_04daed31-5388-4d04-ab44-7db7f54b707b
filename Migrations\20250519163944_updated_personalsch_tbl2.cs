﻿using System;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Jobid.Migrations
{
    public partial class updated_personalsch_tbl2 : Migration
    {
        private readonly IDbContextSchema _schema;
        private string _Schema;
        public updated_personalsch_tbl2(IDbContextSchema schema)
        {
            _schema = schema ?? throw new ArgumentNullException(nameof(schema));
            _Schema = _schema.Schema;
        }
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ExternalMeetingId",
                schema: _Schema,
                table: "PersonalSchedule",
                newName: "ExtMeetId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId",
                schema: _Schema,
                table: "ExternalMeeting");

            migrationBuilder.DropIndex(
                name: "IX_ExternalMeeting_PersonalScheduleId",
                schema: _Schema,
                table: "ExternalMeeting");

            migrationBuilder.RenameColumn(
                name: "ExtMeetId",
                schema: _Schema,
                table: "PersonalSchedule",
                newName: "ExternalMeetingId");

            migrationBuilder.AddColumn<Guid>(
                name: "PersonalScheduleId1",
                schema: _Schema,
                table: "ExternalMeeting",
                type: "uuid",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ExternalMeeting_PersonalScheduleId1",
                schema: _Schema,
                table: "ExternalMeeting",
                column: "PersonalScheduleId1");

            migrationBuilder.AddForeignKey(
                name: "FK_ExternalMeeting_PersonalSchedule_PersonalScheduleId1",
                schema: _Schema,
                table: "ExternalMeeting",
                column: "PersonalScheduleId1",
                principalSchema: _Schema,
                principalTable: "PersonalSchedule",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
