using Jobid.App.AdminConsole.Enums;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Jobid.App.AdminConsole.Models.Wallet
{
    public class WalletTransaction
    {
        [Key]
        public Guid Id { get; set; }
        public Guid WalletId { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        public TransactionType Type { get; set; }
        public PaymentMethod PaymentMethod { get; set; }
        public string TransactionReference { get; set; }
        public string Description { get; set; }
        public string PaymentId { get; set; }
        public TransactionStatus Status { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation property to CompanyWallet
        public virtual CompanyWallet Wallet { get; set; }

        public WalletTransaction()
        {
            Id = Guid.NewGuid();
            CreatedAt = DateTime.UtcNow;
            Status = TransactionStatus.Pending; // Default status
        }
    }
}
