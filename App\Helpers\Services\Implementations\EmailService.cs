﻿using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace Jobid.App.Helpers.Services.Implementations
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _config;
        private readonly IWebHostEnvironment _environment;
        private readonly IAWSS3Sevices _aWSS3Sevices;

        public EmailService(IConfiguration config, IWebHostEnvironment environment, IAWSS3Sevices aWSS3Sevices)
        {
            _config = config;
            _environment = environment;
            _aWSS3Sevices = aWSS3Sevices;
        }

        public async Task<bool> SendEmail(string body, string destination, string subject)
        {
            try
            {
                string senderEmail = subject.ToLower().Contains("waitlist") ? "<EMAIL>" : "<EMAIL>";
                var htmlContent = $"<strong>{body}</strong>";
                MailgunService mailgunService = new MailgunService();

                // Create the sender display name and email address
                var res = await mailgunService.SendEmail(senderEmail, subject, html: htmlContent, new List<string>() { destination });
                if (res.IsSuccessStatusCode)
                    return true;

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendEmail(string body, string destination, string subject, string senderEmail)
        {
            try
            {
                var htmlContent = $"<strong>{body}</strong>";
                MailgunService mailgunService = new MailgunService();

                // Create the sender display name and email address
                var res = await mailgunService.SendEmail(senderEmail, subject, html: htmlContent, new List<string>() { destination });
                if (res.IsSuccessStatusCode)
                    return true;

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendEmailWithAttachments(string body, string destination, string subject, List<AttachmentDto> attachments = null)
        {
            try
            {
                string senderEmail = subject.ToLower().Contains("waitlist") ? "<EMAIL>" : "<EMAIL>";
                var htmlContent = $"<strong>{body}</strong>";
                MailgunService mailgunService = new MailgunService();

                // Create the sender display name and email address
                var res = await mailgunService.sendEmailWithAttachment(senderEmail, subject, html: htmlContent, new List<string>() { destination }, attachments);
                if (res.IsSuccessStatusCode)
                    return true;

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> SendMultipleEmail(string body, List<string> destination, string subject)
        {
            try
            {
                var htmlContent = $"<strong>{body}</strong>";
                await Utility.SendGridSendMail(htmlContent, string.Join(",", destination), subject);

                return true;
            }
            catch
            {
                return false;
            }
        }


        public async Task<string> GetMailTemplate()
        {
            var fileName = "6F2757590F936046D308F93097E98A578A18-zarttech-invited-email.html";
            var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(fileName);

            using var client = new HttpClient();
            var content = await client.GetStringAsync(signedUrl);
            //   return File.ReadAllText(htmlPath);
            return content;
        }

        public async Task<string> GetMeetingInviteMailTemplate()
        {
            var fileName = "B715F6A90F11604C6A08D1D00549BE93BC8D-meeting-email.html";
            var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(fileName);

            using var client = new HttpClient();
            var content = await client.GetStringAsync(signedUrl);
            return content;
        }

        public async Task<string> GetJobPaysInviteMailTemplate()
        {
            using var client = new HttpClient();
            var content = Path.Combine(_environment.WebRootPath, @"EmailTemplates/JobPaysEmail.html");
            var body = await File.ReadAllTextAsync(content);
            return body;
        }

        public async Task<string> GetJobPaysEmployeeWelcomeMailTemplate()
        {
            using var client = new HttpClient();
            var content = Path.Combine(_environment.WebRootPath, @"EmailTemplates/welcome_email_jobpays.html");
            var body = await File.ReadAllTextAsync(content);
            return body;
        }

        public async Task<string> GetJobPaysWalletStatementMailTemplate()
        {
            using var client = new HttpClient();
            var content = Path.Combine(_environment.WebRootPath, @"EmailTemplates/WalletStatement.html");
            var body = await File.ReadAllTextAsync(content);
            return body;
        }

        public async Task<string> GetExternalTeamMemberMailTemplate()
        {
            var fileName = "93E2FB69038B604B1309BD60831EED4CA6E8-team-invite-email.html";
            var signedUrl = await _aWSS3Sevices.GetSignedUrlAsync(fileName);

            using var client = new HttpClient();
            var content = await client.GetStringAsync(signedUrl);

            return content;
        }

        public async Task<string> GetCrmEmailCampaign()
        {
            using var client = new HttpClient();
            var contentPath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/CRM-EmailTemplate.html");
            return await File.ReadAllTextAsync(contentPath);
        }

        public async Task<string> GetCrmEmailCampaignWithAttachment()
        {
            using var client = new HttpClient();
            var contentPath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/CRM-EmailTemplate-With-Attachment.html");
            return await File.ReadAllTextAsync(contentPath);
        }
    }
}
