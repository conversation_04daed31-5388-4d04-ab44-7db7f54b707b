using System;
using System.Threading.Tasks;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.Helpers;

namespace Jobid.App.AdminConsole.Contract
{
    /// <summary>
    /// Service for managing telephony operations including call sessions, participant management, and call forwarding
    /// Orchestrates Twilio (PSTN), LiveKit (media), and AI service integration
    /// </summary>
    public interface ITelephonyService
    {
        #region Call Session Management
        
        /// <summary>
        /// Initiate an outbound call session
        /// Creates LiveKit room + Twilio call with media bridge
        /// </summary>
        Task<GenericResponse> InitiateCallAsync(InitiateCallSessionDto request);

        /// <summary>
        /// Answer an incoming call
        /// </summary>
        Task<GenericResponse> AnswerCallAsync(AnswerCallDto request);

        /// <summary>
        /// End a call session
        /// </summary>
        Task<GenericResponse> EndCallAsync(EndCallDto request);

        /// <summary>
        /// Get details of an active call session
        /// </summary>
        Task<GenericResponse> GetCallSessionAsync(string sessionId);

        #endregion

        #region Participant Management
        
        /// <summary>
        /// Add participant to an ongoing call
        /// </summary>
        Task<GenericResponse> AddParticipantAsync(TelephonyAddParticipantDto request);

        /// <summary>
        /// Remove participant from an ongoing call
        /// </summary>
        Task<GenericResponse> RemoveParticipantAsync(TelephonyRemoveParticipantDto request);

        #endregion

        #region Call Forwarding
        
        /// <summary>
        /// Forward an active call to another number/user
        /// </summary>
        Task<GenericResponse> ForwardCallAsync(ForwardCallDto request);

        #endregion

        #region Media Bridge Management
        
        /// <summary>
        /// Get active media bridge sessions for monitoring
        /// </summary>
        Task<GenericResponse> GetActiveBridgeSessionsAsync();

        /// <summary>
        /// Stop a specific media bridge session
        /// </summary>
        Task<GenericResponse> StopBridgeSessionAsync(string sessionId);

        #endregion

        #region Webhook Handlers
        
        /// <summary>
        /// Handle Twilio call status webhooks
        /// </summary>
        Task<GenericResponse> HandleTwilioCallStatusAsync(TwilioCallStatusDto request, Guid callId, string subdomain);

        /// <summary>
        /// Handle inbound call webhook from Twilio
        /// </summary>
        Task<string> HandleInboundCallAsync(string from, string to, string callSid);

        #endregion

        #region Call History and Analytics
        
        /// <summary>
        /// Get call history for a phone number
        /// </summary>
        Task<GenericResponse> GetCallHistoryAsync(Guid phoneNumberId, DateTime? startDate, DateTime? endDate);

        /// <summary>
        /// Get call recording
        /// </summary>
        Task<GenericResponse> GetCallRecordingAsync(string callId);

        /// <summary>
        /// Get call transcription
        /// </summary>
        Task<GenericResponse> GetCallTranscriptionAsync(string callId);

        /// <summary>
        /// Get call analytics for a phone number
        /// </summary>
        Task<GenericResponse> GetCallAnalyticsAsync(Guid phoneNumberId, DateTime? startDate, DateTime? endDate);

        #endregion

        #region TwiML Endpoints
        
        /// <summary>
        /// Handle dial result from Twilio webhook
        /// </summary>
        string HandleDialResult(string dialCallStatus, string callSid, string requestScheme, string requestHost);

        /// <summary>
        /// Provide queue wait music for callers on hold
        /// </summary>
        string GetQueueWaitMusic();

        /// <summary>
        /// Handle voicemail recording from Twilio webhook
        /// </summary>
        string HandleVoicemail(string recordingUrl, string callSid, string from, string to);

        /// <summary>
        /// Fallback endpoint for inbound calls when WebRTC agents are not available
        /// </summary>
        string GetInboundCallFallback(string from, string to, string callSid, string requestScheme, string requestHost);

        #endregion
    }
}
