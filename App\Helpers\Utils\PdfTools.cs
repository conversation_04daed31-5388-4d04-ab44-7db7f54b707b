using DinkToPdf;
using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;

namespace Jobid.App.Helpers.Utils
{
    public class CustomPdfTools : IDisposable
    {
        private readonly CustomAssemblyLoadContext _context;
        private readonly string _architectureFolder;
        private bool _disposed = false;

        public CustomPdfTools()
        {
            _context = new CustomAssemblyLoadContext();
            _architectureFolder = GetArchitectureFolder();
            LoadNativeLibrary();
        }

        private string GetArchitectureFolder()
        {
            return RuntimeInformation.ProcessArchitecture switch
            {
                Architecture.X86 => "x86",
                Architecture.X64 => "x64",
                Architecture.Arm => "arm",
                Architecture.Arm64 => "arm64",
                _ => throw new PlatformNotSupportedException("Architecture not supported")
            };
        }

        private void LoadNativeLibrary()
        {
            try
            {
                // Try to load from the architecture-specific folder first
                var assemblyDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var libPath = Path.Combine(assemblyDir, "DinkToPdf", "libwkhtmltox.dll");
                
                if (!File.Exists(libPath))
                {
                    // Fallback to root directory if not found in the architecture folder
                    libPath = Path.Combine(assemblyDir, "libwkhtmltox.dll");
                    
                    if (!File.Exists(libPath))
                    {
                        // Try one more location - the project's DinkToPdf folder
                        var projectDir = Directory.GetCurrentDirectory();
                        libPath = Path.Combine(projectDir, "DinkToPdf", "libwkhtmltox.dll");
                        
                        if (!File.Exists(libPath))
                        {
                            throw new FileNotFoundException($"Could not find libwkhtmltox.dll in any of the expected locations.");
                        }
                    }
                }
                
                _context.LoadUnmanagedLibrary(libPath);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading native DinkToPdf library: {ex.Message}", ex);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }

        ~CustomPdfTools()
        {
            Dispose(false);
        }
    }
}
