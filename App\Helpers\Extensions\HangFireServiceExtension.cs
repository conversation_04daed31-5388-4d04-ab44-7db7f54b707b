﻿using Hangfire;
using Hangfire.PostgreSql;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;

namespace Jobid.App.Helpers.Extensions
{
    /// <summary>
    /// Hangfire service setup
    /// </summary>
    public static class HangFireServiceExtension
    {        /// <summary>
        /// Add hangfire service configuration set up
        /// </summary>
        /// <param name="services"></param>
        /// <param name="conn"></param>
        public static void AddHangfireConfigurations(this IServiceCollection services, string conn)
        {            services.AddHangfire(config => config
                            .UseSimpleAssemblyNameTypeSerializer()
                            .UseRecommendedSerializerSettings()
                            .UseSerializerSettings(new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore })
                            .UsePostgreSqlStorage(conn, new PostgreSqlStorageOptions
                            {
                                PrepareSchemaIfNecessary = true,
                                SchemaName = "hangfire",
                                QueuePollInterval = TimeSpan.FromSeconds(15), // Increased to further reduce contention
                                JobExpirationCheckInterval = TimeSpan.FromHours(1),
                                InvisibilityTimeout = TimeSpan.FromMinutes(30),
                                DistributedLockTimeout = TimeSpan.FromMinutes(5), // Reduced further to release locks even faster
                                UseNativeDatabaseTransactions = true, // Enable native transactions for better isolation
                                TransactionSynchronisationTimeout = TimeSpan.FromMinutes(1) // Shorter transaction timeout
                            }));            // Configure one server with fewer workers to reduce connection pool pressure
            services.AddHangfireServer(options =>
            {
                options.WorkerCount = 1; // Reduced worker count to minimize concurrent connections
                options.Queues = new[] { "admin", "meeting" };
                options.ServerName = "AdminServer";
                options.ServerTimeout = TimeSpan.FromMinutes(5);
                options.ShutdownTimeout = TimeSpan.FromMinutes(1);
                options.CancellationCheckInterval = TimeSpan.FromSeconds(5);
            });

            services.AddHangfireServer(options =>
            {
                options.WorkerCount = 1; // Reduced worker count to minimize concurrent connections
                options.Queues = new[] { "project", "sprint" };
                options.ServerName = "ProjectServer";
                options.ServerTimeout = TimeSpan.FromMinutes(5);
                options.ShutdownTimeout = TimeSpan.FromMinutes(1);
                options.CancellationCheckInterval = TimeSpan.FromSeconds(5);
            });

            services.AddHangfireServer(options =>
            {
                options.WorkerCount = 2;
                options.Queues = new[] { "todo", "subscription" };
            });

            services.AddHangfireServer(options =>
            {
                options.WorkerCount = 2;
                options.Queues = new[] { "notification", "general" };
            });

            services.AddHangfireServer(options =>
            {
                options.WorkerCount = 2;
                options.Queues = new[] { "default" };
            });

            // Adds Identity and password policy set up
            services.AddIdentity<User, IdentityRole>(options =>
            {
                options.Password.RequiredLength = 10;
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(10);
            })
            .AddEntityFrameworkStores<JobProDbContext>()
            .AddTokenProvider<DataProtectorTokenProvider<User>>(TokenOptions.DefaultProvider);
        }
    }
}
